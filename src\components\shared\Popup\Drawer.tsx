import * as React from 'react';
import CloseIcon from '@mui/icons-material/Close';
import Drawer from '@mui/material/Drawer';
import { Box, Typography, Stack, IconButton } from '@mui/material';

type DrawerProps = {
  onClose?: () => void;
  state?: boolean;
  DrawerContent?: React.ReactNode;
  Title?: String;
  closeIconDisable?: boolean;
  fontWeight?: number | string;
  fontSize?: number | string;
  minWidth?: number | string;
};
export default function TemporaryDrawer({
  onClose,
  fontWeight,
  fontSize,
  state,
  DrawerContent,
  Title,
  closeIconDisable,
  minWidth,
}: DrawerProps) {
  return (
    <div>
      <Drawer anchor="right" open={state}>
        <Box sx={{ minWidth: minWidth ?? 400, height: '100%' }} role="presentation">
          <Box sx={{ px: 4 }}>
            <Stack pt={3} direction="row" display="flex" alignItems="center" justifyContent="space-between">
              <Typography variant="h6" fontSize={fontSize ?? 20} fontWeight={fontWeight ?? 600}>
                {Title}
              </Typography>
              {closeIconDisable === true ? (
                ''
              ) : (
                <IconButton
                  aria-label="close"
                  onClick={onClose}
                  sx={{
                    p: 1,
                    color: (theme) => theme.palette.grey[500],
                    '&:hover': {},
                  }}
                >
                  <CloseIcon />
                </IconButton>
              )}
            </Stack>
            {DrawerContent}
          </Box>
        </Box>
      </Drawer>
    </div>
  );
}
