/* eslint-disable jsx-a11y/alt-text */
import React, { useState, useEffect, useMemo, useCallback } from 'react';
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  TextField,
  Button,
  Typography,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Card,
  Chip,
  Slider,
  Checkbox,
  FormControlLabel,
  Alert,
  AlertTitle,
  FormControl,
} from '@mui/material';
import styled from 'styled-components';
// No need to import DAY_SELECT as we're defining it dynamically based on includeSaturday
import { useAppSelector } from '@/hooks/useAppSelector';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import {
  getCTSFilterListStatus,
  getCTSDetailsListData,
  getCTSDetailsListStatus,
  getCTSYearList,
  getCTSClassList,
  getCTSSubjectList,
  getCTSStaffList,
} from '@/config/storeSelectors';
import { fetchCTSFilter, fetchCTSList } from '@/store/StaffMangement/StaffMangement/staffMangement.thunks';
import Popup from '@/components/shared/Popup/Popup';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import { ErrorMessage } from '@/components/shared/Popup/ErrorMessage';
import typography from '@/theme/typography';
import useAuth from '@/hooks/useAuth';

const TimetableGeneratorRoot = styled.div`
  padding: 1rem;
  .card-table-container {
    overflow-x: auto;
    margin-top: 1rem;
    padding: 1rem;
  }
  .subject-hours {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
  }
  .subject-name {
    min-width: 25%;
  }
  .hours-slider {
    flex-grow: 1;
    margin: 0 16px;
  }
  .hours-input {
    width: 60px;
  }  .timetable-grid {
    margin-top: 24px;
  }
  .period-cell {
    min-width: 120px;
    text-align: center;
    padding: 8px;
  }
  .subject-chip {
    width: 100%;
    justify-content: center;
    min-height: 60px;
  }
`;

interface SubjectWithStaff {
  subjectId: number;
  subjectName: string;
  staffId: number;
  staffName: string;
  color: string;
}

interface SubjectHours {
  subjectId: number;
  subjectName: string;
  staffId: number;
  staffName: string;
  hours: number;
  color: string;
}

interface TimetableEntry {
  day: string;
  period: number;
  subjectId: number;
  subjectName: string;
  staffId: number;
  staffName: string;
  color: string;
}

interface StaffConflict {
  day: string;
  period: number;
  staffId: number;
  staffName: string;
  classes: string[];
}

function TimetableGenerator() {
  const { user } = useAuth();
  const adminId = user?.accountId;
  const dispatch = useAppDispatch();

  // State for selections
  const [selectedYear, setSelectedYear] = useState<number | null>(null);
  const [selectedClass, setSelectedClass] = useState<number | null>(null);
  const [selectedSubjects, setSelectedSubjects] = useState<SubjectWithStaff[]>([]);
  const [subjectHours, setSubjectHours] = useState<SubjectHours[]>([]);
  const [periodsPerDay, setPeriodsPerDay] = useState<number>(8);
  const [includeSaturday, setIncludeSaturday] = useState<boolean>(true);

  // State for generated timetable
  const [generatedTimetable, setGeneratedTimetable] = useState<TimetableEntry[]>([]);
  const [staffConflicts, setStaffConflicts] = useState<StaffConflict[]>([]);
  const [totalHoursAllocated, setTotalHoursAllocated] = useState<number>(0);
  const [totalSlotsAvailable, setTotalSlotsAvailable] = useState<number>(0);

  // State for UI
  const [popup, setPopup] = useState(false);
  const [errorPopup, setErrorPopup] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [editMode, setEditMode] = useState(false);
  const [editedTimetable, setEditedTimetable] = useState<TimetableEntry[]>([]);

  // Days of the week
  const daysOfWeek = useMemo(() => {
    return includeSaturday ? ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'] : ['Mon', 'Tue', 'Wed', 'Thu', 'Fri'];
  }, [includeSaturday]);

  // Get data from Redux store
  const yearList = useAppSelector(getCTSYearList);
  const classList = useAppSelector(getCTSClassList);
  const subjectList = useAppSelector(getCTSSubjectList);
  const staffList = useAppSelector(getCTSStaffList);
  const ctsDetailsData = useAppSelector(getCTSDetailsListData);
  const ctsFilterStatus = useAppSelector(getCTSFilterListStatus);
  const ctsDetailsStatus = useAppSelector(getCTSDetailsListStatus);

  // Fetch CTS filter data when component mounts
  useEffect(() => {
    if (adminId) {
      dispatch(fetchCTSFilter(adminId));
    }
  }, [dispatch, adminId]);

  // Update total slots available when periods or days change
  useEffect(() => {
    const totalSlots = periodsPerDay * daysOfWeek.length;
    setTotalSlotsAvailable(totalSlots);
  }, [periodsPerDay, daysOfWeek]);

  // Update total hours allocated when subject hours change
  useEffect(() => {
    const totalHours = subjectHours.reduce((sum, item) => sum + item.hours, 0);
    setTotalHoursAllocated(totalHours);
  }, [subjectHours]);

  // Initialize subject hours when selected subjects change
  useEffect(() => {
    // Expanded light color palette with pastel colors
    const lightColors = [
      '#FFFFF0', // Light Yellow
      '#E3F8FF', // Light Blue
      '#E8F8EC', // Light Green
      '#F5E6FA', // Light Purple
      '#FFF8E1', // Light Amber
      '#FDE7EF', // Light Pink
      '#E6FAF8', // Light Teal
      '#F9FBE7', // Light Lime
      '#FFEDE5', // Light Orange
      '#F3F0FA', // Light Lavender
      '#F3FDEB', // Light Mint
      '#FFF3E0', // Light Peach
      '#FAF9F6', // Light Beige
      '#E6FCFB', // Light Cyan
      '#FFFDF6', // Light Cream
      '#F8F3FA', // Light Lilac
      '#F6FBE6', // Light Olive
      '#FFEDEE', // Light Rose
      '#F6FBF1', // Light Moss
      '#F4F9FD', // Light Sky Blue
      '#F8F9F9', // Light Silver
    ];

    // Shuffle colors to assign unique ones
    const shuffledColors = [...lightColors].sort(() => Math.random() - 0.5);

    const initialSubjectHours = selectedSubjects.map((subject, idx) => ({
      subjectId: subject.subjectId,
      subjectName: subject.subjectName,
      staffId: subject.staffId,
      staffName: subject.staffName,
      color: shuffledColors[idx % shuffledColors.length],
      hours: 1, // Default to 1 hour per subject
    }));
    setSubjectHours(initialSubjectHours);
  }, [selectedSubjects]);

  const handleSubjectHoursChange = (subjectId: number, hours: number) => {
    setSubjectHours((prevHours) => prevHours.map((item) => (item.subjectId === subjectId ? { ...item, hours } : item)));
  };

  // Fetch CTS details when class and year are selected
  const fetchCTSDetails = useCallback(() => {
    if (adminId && selectedYear && selectedClass) {
      const request = {
        pageNumber: 1,
        pageSize: 100,
        filters: {
          adminId,
          academicId: selectedYear,
          classId: selectedClass,
          staffId: -1,
          subjectId: -1,
        },
      };
      dispatch(fetchCTSList(request));
    }
  }, [adminId, selectedYear, selectedClass, dispatch]);

  // Check for staff conflicts in the timetable
  const checkStaffConflicts = (timetable: TimetableEntry[]): StaffConflict[] => {
    // 1. Initialize an empty array to collect conflicts and a mapping object for staff assignments.
    const conflicts: StaffConflict[] = [];
    const staffAssignments: Record<string, Record<number, string[]>> = {};

    // 2. Iterate over each timetable entry:
    //    - Skip entries with no staffId.
    //    - For each (day, period), group staff assignments.
    //    - For each staff, collect the class name they are assigned to at that slot.
    timetable.forEach((entry) => {
      if (!entry.staffId) return;

      const key = `${entry.day}-${entry.period}`;
      if (!staffAssignments[key]) {
        staffAssignments[key] = {};
      }

      if (!staffAssignments[key][entry.staffId]) {
        staffAssignments[key][entry.staffId] = [];
      }

      if (selectedClass) {
        const className = classList.find((c) => c.classId === selectedClass)?.className || '';
        staffAssignments[key][entry.staffId].push(className);
      }
    });

    // 3. After grouping, check each (day, period, staff) combination:
    //   - If a staff is assigned to more than one class at the same time, record a conflict.
    Object.entries(staffAssignments).forEach(([key, staffs]) => {
      const [day, periodStr] = key.split('-');
      const period = parseInt(periodStr, 10);

      Object.entries(staffs).forEach(([staffIdStr, classes]) => {
        const staffId = parseInt(staffIdStr, 10);
        if (classes.length > 1) {
          const staffName = staffList.find((s) => s.staffId === staffId)?.staffName || '';
          conflicts.push({
            day,
            period,
            staffId,
            staffName,
            classes,
          });
        }
      });
    });

    // 4. Return the array of detected conflicts.
    return conflicts;
  };

  // Prevent same subject in adjacent periods.
  const preventAdjacentSubjects = (subjects: SubjectHours[]): SubjectHours[] => {
    // Create a pool of subjects based on their hours
    const subjectPool: SubjectHours[] = [];
    subjects.forEach((item) => {
      for (let i = 0; i < item.hours; i += 1) {
        subjectPool.push({ ...item });
      }
    });

    // Shuffle the subject pool for initial randomness
    const shuffledSubjects = [...subjectPool].sort(() => Math.random() - 0.5);

    // Reorder to avoid adjacent same subjects
    const reorderedSubjects: SubjectHours[] = [];
    let lastSubjectId: number | null = null;

    // First pass - try to avoid adjacency
    while (shuffledSubjects.length > 0) {
      // Store the current value of lastSubjectId to avoid closure issues
      const currentLastSubjectId = lastSubjectId;
      // Find a subject different from the last one
      const nonAdjacentIndex = shuffledSubjects.findIndex((s) => s.subjectId !== currentLastSubjectId);

      if (nonAdjacentIndex !== -1 || shuffledSubjects.length === 1) {
        // We found a non-adjacent subject or only one subject left
        const index = nonAdjacentIndex !== -1 ? nonAdjacentIndex : 0;
        const subject = shuffledSubjects[index];
        reorderedSubjects.push(subject);
        lastSubjectId = subject.subjectId;
        shuffledSubjects.splice(index, 1);
      } else {
        // If we can't avoid adjacency, just add the first subject
        reorderedSubjects.push(shuffledSubjects[0]);
        lastSubjectId = shuffledSubjects[0].subjectId;
        shuffledSubjects.splice(0, 1);
      }
    }

    return reorderedSubjects;
  };

  const generateTimetable = () => {
    if (!selectedYear || !selectedClass || selectedSubjects.length === 0) {
      setErrorMessage('Please select year, class, and at least one subject');
      setErrorPopup(true);
      return;
    }

    if (totalHoursAllocated !== totalSlotsAvailable) {
      setErrorMessage(
        `Total subject hours (${totalHoursAllocated}) must equal available slots (${totalSlotsAvailable}). Please adjust the hours.`
      );
      setErrorPopup(true);
      return;
    }

    // Get subjects with their allocated hours and prevent adjacent repetition
    const orderedSubjects = preventAdjacentSubjects(subjectHours);

    // Generate timetable
    const timetable: TimetableEntry[] = [];
    let subjectIndex = 0;

    daysOfWeek.forEach((day) => {
      for (let period = 1; period <= periodsPerDay; period += 1) {
        if (subjectIndex < orderedSubjects.length) {
          // Here call api to check the conflict
          // If conflict found then change the subject
          // If no conflict found then add the subject
          const subject = orderedSubjects[subjectIndex];
          timetable.push({
            day,
            period,
            subjectId: subject.subjectId,
            subjectName: subject.subjectName,
            staffId: subject.staffId,
            staffName: subject.staffName,
            color: subject.color,
          });
          subjectIndex += 1;
        } else {
          // If we run out of subjects, add empty periods
          timetable.push({
            day,
            period,
            subjectId: 0,
            subjectName: '',
            staffId: 0,
            staffName: '',
            color: '#ffffff',
          });
        }
      }
    });

    // Check for staff conflicts
    const conflicts = checkStaffConflicts(timetable);
    setStaffConflicts(conflicts);

    if (conflicts.length > 0) {
      setErrorMessage(
        `Found ${conflicts.length} staff conflicts. Some teachers are assigned to multiple classes at the same time.`
      );
      setErrorPopup(true);
    }

    setGeneratedTimetable(timetable);
    setPopup(true);
  };

  const handleClickClose = () => setPopup(false);

  // Handle error popup close
  const handleErrorClose = () => setErrorPopup(false);

  // Toggle edit mode
  const toggleEditMode = () => {
    if (!editMode) {
      // When entering edit mode, initialize editedTimetable with a deep copy of generatedTimetable
      setEditedTimetable([...generatedTimetable]);
    }
    setEditMode(!editMode);
  };

  const handleCellEdit = (
    day: string,
    period: number,
    subjectId: number,
    subjectName: string,
    staffId: number,
    staffName: string
  ) => {
    setEditedTimetable((prev) => {
      const newTimetable = [...prev];
      const entryIndex = newTimetable.findIndex((item) => item.day === day && item.period === period);

      // Find the color for the subject-staff combination
      // Try to get color from subjectHours first, then from selectedSubjects
      const color =
        subjectHours.find((s) => s.subjectId === subjectId && s.staffId === staffId)?.color ||
        subjectHours.find((s) => s.subjectId === subjectId)?.color ||
        selectedSubjects.find((s) => s.subjectId === subjectId && s.staffId === staffId)?.color ||
        selectedSubjects.find((s) => s.subjectId === subjectId)?.color ||
        '#ffffff';

      if (entryIndex !== -1) {
        // Update existing entry
        newTimetable[entryIndex] = {
          ...newTimetable[entryIndex],
          subjectId,
          subjectName,
          staffId,
          staffName,
          color,
        };
      } else {
        // Add new entry
        newTimetable.push({
          day,
          period,
          subjectId,
          subjectName,
          staffId,
          staffName,
          color,
        });
      }

      return newTimetable;
    });
  };

  // Save edited timetable
  const saveEditedTimetable = () => {
    // Ensure each entry has the correct color after editing
    const updatedEditedTimetable = editedTimetable.map((entry) => {
      const color =
        subjectHours.find((s) => s.subjectId === entry.subjectId && s.staffId === entry.staffId)?.color ||
        subjectHours.find((s) => s.subjectId === entry.subjectId)?.color ||
        selectedSubjects.find((s) => s.subjectId === entry.subjectId && s.staffId === entry.staffId)?.color ||
        selectedSubjects.find((s) => s.subjectId === entry.subjectId)?.color ||
        '#ffffff';
      return { ...entry, color };
    });

    // Check for staff conflicts in the edited timetable
    const conflicts = checkStaffConflicts(updatedEditedTimetable);
    setStaffConflicts(conflicts);

    // Update the generated timetable with the edited version
    setGeneratedTimetable(updatedEditedTimetable);

    // Exit edit mode
    setEditMode(false);

    // Show success message
    setPopup(true);
  };

  // Effect to fetch CTS details when class and year change
  useEffect(() => {
    if (selectedYear && selectedClass && adminId) {
      fetchCTSDetails();
    }
  }, [selectedYear, selectedClass, adminId, fetchCTSDetails]);

  return (
    <Page title="Timetable Generator">
      <TimetableGeneratorRoot>
        <Card elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Typography variant="h6" fontSize={17}>
            Automated Timetable Generator
          </Typography>
          <Divider />
          <Grid container spacing={3} pt={2}>
            <Grid item lg={4} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Academic Year
              </Typography>
              <Autocomplete
                options={yearList || []}
                getOptionLabel={(option) => option.academicTime}
                value={yearList?.find((y) => y.academicId === selectedYear) || null}
                onChange={(e, v) => setSelectedYear(v ? v.academicId : null)}
                renderInput={(params) => <TextField {...params} placeholder="Select Year" />}
                isOptionEqualToValue={(option, value) => option.academicId === value.academicId}
              />
            </Grid>
            <Grid item lg={4} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Class
              </Typography>
              <Autocomplete
                options={classList || []}
                getOptionLabel={(option) => option.className}
                value={classList?.find((c) => c.classId === selectedClass) || null}
                onChange={(e, v) => setSelectedClass(v ? v.classId : null)}
                renderInput={(params) => <TextField {...params} placeholder="Select Class" />}
                isOptionEqualToValue={(option, value) => option.classId === value.classId}
              />
            </Grid>
            <Grid item lg={4} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Subjects with Staff
              </Typography>
              <Autocomplete
                multiple
                options={
                  subjectList
                    ?.map((subject) => {
                      const staffForSubject = staffList?.find((staff) =>
                        ctsDetailsData?.some(
                          (detail) =>
                            detail.subjectId === subject.subjectId &&
                            detail.staffId === staff.staffId &&
                            detail.classId === selectedClass &&
                            detail.academicId === selectedYear
                        )
                      );

                      return {
                        subjectId: subject.subjectId,
                        subjectName: subject.subjectName,
                        staffId: staffForSubject?.staffId || 0,
                        staffName: staffForSubject?.staffName || 'No Staff Assigned',
                        color: selectedSubjects.find((s) => s.subjectId === subject.subjectId)?.color,
                      };
                    })
                    .filter((item) => item.staffId !== 0) || []
                }
                getOptionLabel={(option) => `${option.subjectName} (${option.staffName})`}
                value={selectedSubjects}
                onChange={(_, v) =>
                  setSelectedSubjects(
                    v.map((item) => ({
                      ...item,
                      color: selectedSubjects?.find((s) => s.subjectId === item.subjectId)?.color || '#ffffff',
                    }))
                  )
                }
                renderInput={(params) => <TextField {...params} placeholder="Select Subjects" />}
                isOptionEqualToValue={(option, value) =>
                  option.subjectId === value.subjectId && option.staffId === value.staffId
                }
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" fontSize={14} gutterBottom>
                Periods Per Day
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <TextField
                  sx={{ width: '80px', mr: 2 }}
                  type="number"
                  value={periodsPerDay}
                  onChange={(e) => setPeriodsPerDay(parseInt(e.target.value, 10) || 1)}
                  InputProps={{ inputProps: { min: 1, max: 10 } }}
                  size="small"
                />
                <Slider
                  value={periodsPerDay}
                  onChange={(_, v) => setPeriodsPerDay(v as number)}
                  step={1}
                  marks
                  min={1}
                  max={10}
                  valueLabelDisplay="auto"
                  sx={{ flexGrow: 1, maxWidth: 300 }}
                />
              </Box>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={<Checkbox checked={includeSaturday} onChange={(e) => setIncludeSaturday(e.target.checked)} />}
                label="Include Saturday"
              />
            </Grid>
          </Grid>

          {/* Display total hours and slots */}
          {totalHoursAllocated > 0 && (
            <Box mt={3}>
              <Alert severity={totalHoursAllocated === totalSlotsAvailable ? 'success' : 'warning'} sx={{ mb: 2 }}>
                <AlertTitle>
                  {totalHoursAllocated === totalSlotsAvailable ? 'Hours Balanced' : 'Hours Imbalance'}
                </AlertTitle>
                Total subject hours: <strong>{totalHoursAllocated}</strong>
                <br />
                Available slots: <strong>{totalSlotsAvailable}</strong>
                <br />
                {totalHoursAllocated !== totalSlotsAvailable &&
                  `Please adjust subject hours to match available slots (${totalSlotsAvailable - totalHoursAllocated} ${
                    totalHoursAllocated < totalSlotsAvailable ? 'more' : 'less'
                  } needed)`}
              </Alert>
            </Box>
          )}

          {selectedSubjects.length > 0 && (
            <Box mt={4}>
              <Typography variant="h6" fontSize={16} gutterBottom>
                Select number of periods for Each Subject per week
              </Typography>
              <Divider sx={{ mb: 2 }} />
              {subjectHours.map((item) => (
                <Box key={`${item.subjectId}-${item.staffId}`} className="subject-hours">
                  <Typography className="subject-name">
                    {item.subjectName} <small>({item.staffName})</small>
                  </Typography>
                  <TextField
                    className="hours-input"
                    type="number"
                    value={item.hours}
                    onChange={(e) => handleSubjectHoursChange(item.subjectId, parseInt(e.target.value, 10) || 1)}
                    InputProps={{ inputProps: { min: 1, max: 10 } }}
                    size="small"
                  />
                  <Slider
                    className="hours-slider"
                    value={item.hours}
                    onChange={(_, v) => handleSubjectHoursChange(item.subjectId, v as number)}
                    step={1}
                    marks
                    min={1}
                    max={10}
                    valueLabelDisplay="auto"
                  />
                </Box>
              ))}
            </Box>
          )}

          <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, mt: 4 }}>
            <Button
              variant="contained"
              color="primary"
              onClick={generateTimetable}
              sx={{ fontFamily: typography.fontFamily }}
            >
              Generate Timetable
            </Button>
          </Box>
        </Card>

        {generatedTimetable.length > 0 && (
          <Card elevation={1} sx={{ mt: 4, px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="h6" fontSize={17}>
                Generated Timetable
              </Typography>
              <Box>
                {editMode ? (
                  <>
                    <Button
                      variant="contained"
                      color="success"
                      onClick={saveEditedTimetable}
                      sx={{ mr: 1, fontFamily: typography.fontFamily }}
                    >
                      Save Changes
                    </Button>
                    <Button
                      variant="outlined"
                      color="error"
                      onClick={() => setEditMode(false)}
                      sx={{ fontFamily: typography.fontFamily }}
                    >
                      Cancel
                    </Button>
                  </>
                ) : (
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={toggleEditMode}
                    sx={{ fontFamily: typography.fontFamily }}
                  >
                    Edit Timetable
                  </Button>
                )}
              </Box>
            </Box>
            <Divider />

            {/* Display staff conflicts if any */}
            {staffConflicts.length > 0 && (
              <Alert severity="error" sx={{ mt: 2, mb: 2 }}>
                <AlertTitle>Staff Conflicts Detected</AlertTitle>
                <Typography variant="body2">
                  The following staff members are assigned to multiple classes at the same time:
                </Typography>
                <ul>
                  {staffConflicts.map((conflict) => (
                    <li key={`conflict-${conflict.day}-${conflict.period}-${conflict.staffId}`}>
                      {conflict.staffName} on {conflict.day}, Period {conflict.period} - Classes:{' '}
                      {conflict.classes.join(', ')}
                    </li>
                  ))}
                </ul>
              </Alert>
            )}

            {editMode && (
              <Alert severity="info" sx={{ mt: 2, mb: 2 }}>
                <AlertTitle>Edit Mode</AlertTitle>
                <Typography variant="body2">
                  Click on any cell to edit the subject and staff assignment. Click &quot;Save Changes&quot; when done.
                </Typography>
              </Alert>
            )}

            <TableContainer className="timetable-grid" sx={{ overflowX: 'auto' }}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Day / Period</TableCell>
                    {Array.from({ length: periodsPerDay }, (_, i) => (
                      <TableCell key={i} align="center">
                        Period {i + 1}
                      </TableCell>
                    ))}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {daysOfWeek.map((day) => (
                    <TableRow key={day}>
                      <TableCell component="th" scope="row">
                        {day}
                      </TableCell>
                      {Array.from({ length: periodsPerDay }, (_, periodIndex) => {
                        const period = periodIndex + 1;
                        const timetableToUse = editMode ? editedTimetable : generatedTimetable;
                        const entry = timetableToUse.find((item) => item.day === day && item.period === period);

                        return (
                          <TableCell key={periodIndex} align="center">
                            {editMode ? (
                              <FormControl fullWidth size="small" sx={{ minHeight: 60, p: 0 }}>
                                <Autocomplete
                                  size="small"
                                  options={selectedSubjects}
                                  getOptionLabel={(option) => `${option.subjectName} (${option.staffName})`}
                                  value={
                                    entry
                                      ? selectedSubjects.find(
                                          (s) => s.subjectId === entry.subjectId && s.staffId === entry.staffId
                                        ) || null
                                      : null
                                  }
                                  onChange={(event, newValue) => {
                                    if (newValue) {
                                      handleCellEdit(
                                        day,
                                        period,
                                        newValue.subjectId,
                                        newValue.subjectName,
                                        newValue.staffId,
                                        newValue.staffName
                                      );
                                    } else {
                                      // Handle clearing the cell
                                      handleCellEdit(day, period, 0, '', 0, '');
                                    }
                                  }}
                                  renderInput={(params) => (
                                    <TextField {...params} placeholder="Select Subject" size="small" />
                                  )}
                                  sx={{
                                    minHeight: 60,
                                    '& .MuiInputBase-root': {
                                      minHeight: 60,
                                      height: 60,
                                      p: 0,
                                      alignItems: 'center',
                                      display: 'flex',
                                    },
                                    '& .MuiAutocomplete-input': {
                                      p: 0,
                                      m: 0,
                                      height: 'auto',
                                      lineHeight: 1.2,
                                      whiteSpace: 'normal',
                                      overflow: 'visible',
                                      textOverflow: 'clip',
                                      display: 'block',
                                    },
                                  }}
                                />
                              </FormControl>
                            ) : (
                              entry &&
                              entry.subjectName && (
                                <Chip
                                  label={
                                    <Box>
                                      <Typography variant="body2" fontWeight="bold">
                                        {entry.subjectName}
                                      </Typography>
                                      <Typography variant="caption" display="block">
                                        {entry.staffName}
                                      </Typography>
                                    </Box>
                                  }
                                  sx={{
                                    backgroundColor: entry.color,
                                    '& .MuiChip-label': {
                                      display: 'flex',
                                      flexDirection: 'column',
                                      alignItems: 'center',
                                      justifyContent: 'center',
                                      height: '100%',
                                    },
                                  }}
                                  variant="filled"
                                  className="subject-chip"
                                />
                              )
                            )}
                          </TableCell>
                        );
                      })}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Card>
        )}

        <Popup
          size="xs"
          state={popup}
          onClose={handleClickClose}
          popupContent={<SuccessMessage message="Timetable Generated Successfully" />}
        />

        <Popup
          size="sm"
          state={errorPopup}
          onClose={handleErrorClose}
          popupContent={<ErrorMessage message={errorMessage} />}
        />
      </TimetableGeneratorRoot>
    </Page>
  );
}

export default TimetableGenerator;
