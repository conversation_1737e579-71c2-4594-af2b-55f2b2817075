import { useState, useMemo, useCallback } from 'react';
import { ListMenuItem } from '@/types/Layout';
import {
  SearchableMenuItem,
  createSearchableMenuItems,
  filterMenuItems,
  debounce,
} from '@/utils/searchUtils';

export interface UseMenuSearchProps {
  menuData: ListMenuItem[];
  debounceDelay?: number;
}

export interface UseMenuSearchReturn {
  searchQuery: string;
  searchResults: SearchableMenuItem[];
  isSearching: boolean;
  hasResults: boolean;
  setSearchQuery: (query: string) => void;
  clearSearch: () => void;
  handleSearchChange: (query: string) => void;
}

export function useMenuSearch({
  menuData,
  debounceDelay = 300,
}: UseMenuSearchProps): UseMenuSearchReturn {
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedQuery, setDebouncedQuery] = useState('');

  // Create searchable menu items from the main menu data
  const searchableItems = useMemo(() => {
    return createSearchableMenuItems(menuData);
  }, [menuData]);

  // Debounced search function
  const debouncedSearch = useMemo(
    () =>
      debounce((query: string) => {
        setDebouncedQuery(query);
      }, debounceDelay),
    [debounceDelay]
  );

  // Filter search results based on debounced query
  const searchResults = useMemo(() => {
    return filterMenuItems(searchableItems, debouncedQuery);
  }, [searchableItems, debouncedQuery]);

  // Handle search input change
  const handleSearchChange = useCallback(
    (query: string) => {
      setSearchQuery(query);
      debouncedSearch(query);
    },
    [debouncedSearch]
  );

  // Clear search
  const clearSearch = useCallback(() => {
    setSearchQuery('');
    setDebouncedQuery('');
  }, []);

  const isSearching = searchQuery.trim().length > 0;
  const hasResults = searchResults.length > 0;

  return {
    searchQuery,
    searchResults,
    isSearching,
    hasResults,
    setSearchQuery,
    clearSearch,
    handleSearchChange,
  };
}
