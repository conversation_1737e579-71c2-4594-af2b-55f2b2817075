/* eslint-disable prettier/prettier */
import React from 'react';
import { Link, useLocation, useOutletContext } from 'react-router-dom';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';
import styled from 'styled-components';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import BookFineSetting from '@/features/LibraryManagement/BookFineSetting';
import EnrollBooks from '@/features/LibraryManagement/EnrollBooks';
import BookList from '@/features/LibraryManagement/BookList/BookList';
import IssueList from '@/features/LibraryManagement/IssueList/IssueList';
import CategoryManager from '@/features/LibraryManagement/CategoryManager';
import LocationManager from '@/features/LibraryManagement/LocationManager';
import Page from '@/components/shared/Page';
import AboutUs from '@/features/GeneralSettings/AboutUs';
import ContactUs from '@/features/GeneralSettings/ContactUs';
import LandingImages from '@/features/GeneralSettings/LandingImages';
import AuthorizeKeywords from '@/features/GeneralSettings/AuthorizeKeywords';
import { useDispatch } from 'react-redux';
import { flushStore } from '@/store/flush.slice';
import { OutletContextType } from '@/types/Common';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const LibraryManagementRoot = styled.div`
  min-height: calc(100vh - ${TOP_BAR_HEIGHT});

  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};

  .tab {
    background-color: ${(props) => (props.theme.themeMode === 'light' ? '#fff' : props.theme.palette.grey[800])};
    /* @media screen and (min-width: 1414px) {
      width: 100%;
    }
    @media screen and (max-width: 720px) {
    } */
    width: 100%;
  }
  .Mui-selected {
    color: ${(props) => props.theme.palette.primary.main};
  }
`;

function TabPanel({ children, value, index }: TabPanelProps) {
  return (
    <Box
      sx={{ paddingTop: '3rem' }}
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
    >
      {value === index && <Box>{children}</Box>}
    </Box>
  );
}
function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`,
  };
}

function LibraryManagement() {
  const { topBarHeight } = useOutletContext<OutletContextType>();
  const location = useLocation();
  const [value, setValue] = React.useState(0);

  const dispatch = useDispatch();

  React.useEffect(() => {
    dispatch(flushStore());
    console.log('Flush Store');
  }, [dispatch, value]);

  React.useEffect(() => {
    const currentPath = location.pathname;
    const routePaths = [
      '/general-settings/about-us',
      '/general-settings/contact-us',
      '/general-settings/landing-images-list',
      '/general-settings/authorise-keywords',
    ];
    const index = routePaths.indexOf(currentPath);
    if (index !== -1) {
      setValue(index);
    }
  }, [location]);

  return (
    <Page title="">
      <LibraryManagementRoot>
        <Tabs
          className="tab"
          variant="scrollable"
          scrollButtons
          allowScrollButtonsMobile
          value={value}
          aria-label="basic tabs"
          sx={{
            ml: '0px',
            borderBottom: 1,
            borderColor: 'divider',
            position: 'fixed',
             top: `${topBarHeight}px`,
            zIndex: 1,
          }}
        >
          <Tab {...a11yProps(0)} label="About Us" component={Link} to="/general-settings/about-us" />
          <Tab {...a11yProps(1)} label="Contact Us" component={Link} to="/general-settings/contact-us" />
          <Tab {...a11yProps(2)} label="Landing Images" component={Link} to="/general-settings/landing-images-list" />
          <Tab {...a11yProps(3)} label="Authorise Keywords" component={Link} to="/general-settings/authorise-keywords" />
        </Tabs>

        <TabPanel value={value} index={0}>
          <AboutUs />
        </TabPanel>
        <TabPanel value={value} index={1}>
          <ContactUs />
        </TabPanel>
        <TabPanel value={value} index={2}>
          <LandingImages />
        </TabPanel>
        <TabPanel value={value} index={3}>
          <AuthorizeKeywords />
        </TabPanel>
      </LibraryManagementRoot>
    </Page>
  );
}

export default LibraryManagement;
