import React from 'react';
import { Box, Button, FormControl, MenuItem, Select, Stack, TextField, Typography } from '@mui/material';
import { FEE_TYPE_ID_OPTIONS, FEE_TYPE_OPTIONS } from '@/config/Selection';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { ErrorIcon } from '@/theme/overrides/CustomIcons';
import LoadingButton from '@mui/lab/LoadingButton';
import DatePickers from '@/components/shared/Selections/DatePicker';
import dayjs from 'dayjs';
import { CreateBasicFeeSettingTitleDataType } from '@/types/ManageFee';
import useAuth from '@/hooks/useAuth';
import { getManageFeeSubmitting } from '@/config/storeSelectors';
import { useAppSelector } from '@/hooks/useAppSelector';
import styled from 'styled-components';

export type CreateEditBasicFeeTitleFormProps = {
  onCancel: (mode: 'create' | 'edit') => void;
  onSave: (values: CreateBasicFeeSettingTitleDataType, mode: 'create' | 'edit') => void;
  basicFeeDetails: CreateBasicFeeSettingTitleDataType;
};

const CreateFormRoot = styled.div`
  .MuiStack-root {
    width: 100%;
  }
`;

const CreateEditBasicFeeTitleValidationSchema = Yup.object({
  feeTitle: Yup.string().required('Please enter Fee Title'),
  feeType: Yup.number().oneOf([1, 2, 3], 'Please select Fee Type'),
  startDate: Yup.string().required('Please enter Start Date'),
  endDate: Yup.string().required('Please enter End Date'),
  feeTypeId: Yup.number().oneOf([1, 2], 'Please select Type'),
});

export const CreateEditBasicFeeTitleForm = ({
  onCancel,
  onSave,
  basicFeeDetails,
}: CreateEditBasicFeeTitleFormProps) => {
  const mode = basicFeeDetails.feeId === 0 ? 'create' : 'edit';
  const { user } = useAuth();
  const adminId: number = user ? user.accountId : 0;
  const isSubmitting = useAppSelector(getManageFeeSubmitting);

  const {
    values: { feeTitle, feeType, startDate, endDate, feeTypeId },
    handleChange,
    handleBlur,
    handleSubmit,
    touched,
    errors,
    setFieldValue,
  } = useFormik<CreateBasicFeeSettingTitleDataType>({
    initialValues: {
      adminId,
      feeId: basicFeeDetails.feeId,
      feeTitle: basicFeeDetails.feeTitle,
      feeType: basicFeeDetails.feeType,
      startDate: basicFeeDetails.startDate,
      endDate: basicFeeDetails.endDate,
      feeTypeId: basicFeeDetails.feeTypeId,
    },
    validationSchema: CreateEditBasicFeeTitleValidationSchema,
    onSubmit: async (values) => {
      onSave(values, mode);
    },
    validateOnBlur: false,
    enableReinitialize: true,
  });

  return (
    <CreateFormRoot>
      <form noValidate onSubmit={handleSubmit}>
        <Stack
          sx={{
            height: 'calc(100vh - 150px)',
            overflow: 'auto',
            '&::-webkit-scrollbar': {
              width: 0,
            },
          }}
          direction="column"
        >
          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Fee Title
            </Typography>
            <TextField
              placeholder="Enter fee title"
              name="feeTitle"
              value={feeTitle}
              onBlur={handleBlur}
              onChange={handleChange}
              error={touched.feeTitle && !!errors.feeTitle}
              helperText={errors.feeTitle}
              disabled={isSubmitting}
              InputProps={{
                endAdornment: touched.feeTitle && !!errors.feeTitle && <ErrorIcon color="error" />,
              }}
            />
          </FormControl>

          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Fee Type
            </Typography>
            <Select
              name="feeType"
              value={feeType}
              onChange={handleChange}
              error={touched.feeType && !!errors.feeType}
              disabled={isSubmitting}
            >
              <MenuItem sx={{ display: 'none' }} value={0}>
                Select Fee Type
              </MenuItem>
              {FEE_TYPE_OPTIONS.map((opt) => (
                <MenuItem key={opt.id} value={opt.id}>
                  {opt.name}
                </MenuItem>
              ))}
            </Select>
            {touched.feeType && !!errors.feeType && (
              <Typography color="red" fontSize="12px" ml={2.5} mt={1} variant="subtitle1">
                {errors.feeType}
              </Typography>
            )}
          </FormControl>
          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Select Type
            </Typography>
            <Select
              name="feeTypeId"
              value={feeTypeId}
              onChange={handleChange}
              error={touched.feeTypeId && !!errors.feeTypeId}
              disabled={isSubmitting}
            >
              <MenuItem sx={{ display: 'none' }} value={0}>
                Select Type
              </MenuItem>
              {FEE_TYPE_ID_OPTIONS.map((opt) => (
                <MenuItem key={opt.id} value={opt.id}>
                  {opt.name}
                </MenuItem>
              ))}
            </Select>
            {touched.feeTypeId && !!errors.feeTypeId && (
              <Typography color="red" fontSize="12px" ml={2.5} mt={1} variant="subtitle1">
                {errors.feeTypeId}
              </Typography>
            )}
          </FormControl>
          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Start Date
            </Typography>
            <DatePickers
              disabled={isSubmitting}
              name="startDate"
              value={dayjs(startDate)}
              onChange={(e) => {
                const formattedDate = e ? e.format('DD/MM/YYYY') : '';
                setFieldValue('startDate', formattedDate);
                console.log('date::::', formattedDate);
              }}
            />
            {touched.startDate && !!errors.startDate && (
              <Typography color="red" fontSize="12px" ml={2.5} mt={1} variant="subtitle1">
                {errors.startDate}
              </Typography>
            )}
          </FormControl>
          <FormControl fullWidth>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              End Date
            </Typography>
            <DatePickers
              disabled={isSubmitting}
              name="endDate"
              value={dayjs(endDate)}
              onChange={(e) => {
                const formattedDate = e ? e.format('DD/MM/YYYY') : '';
                setFieldValue('endDate', formattedDate);
                console.log('date::::', formattedDate);
              }}
            />
            {touched.endDate && !!errors.endDate && (
              <Typography color="red" fontSize="12px" ml={2.5} mt={1} variant="subtitle1">
                {errors.endDate}
              </Typography>
            )}
          </FormControl>
        </Stack>
        <Box sx={{ justifyContent: 'center', pt: 3, position: 'relative' }}>
          <Stack spacing={2} direction="row" sx={{}}>
            <Button
              disabled={isSubmitting}
              onClick={() => onCancel(mode)}
              fullWidth
              variant="contained"
              color="secondary"
              type="button"
            >
              Cancel
            </Button>
            <LoadingButton
              loading={isSubmitting}
              fullWidth
              variant="contained"
              color="primary"
              type="submit"
              loadingPosition="start"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Saving...' : 'Save'}
            </LoadingButton>
          </Stack>
        </Box>
      </form>
    </CreateFormRoot>
  );
};
