import api from '@/api';
import { ClassListInfo } from '@/types/AcademicManagement';
import {
  DashboardAttendanceType,
  DashboardBdayType,
  DashboardEventsType,
  DashboardFeeChartType,
  DashboardStatsType,
  DashboardTimeTableType,
  DashboardVideosType,
  YearDataType,
} from '@/types/Dashboard';
import { createAsyncThunk } from '@reduxjs/toolkit';

export const fetchDashboardStats = createAsyncThunk<DashboardStatsType[], number | undefined, { rejectValue: string }>(
  'dashboard/stats',
  async (adminId, { rejectWithValue }) => {
    try {
      const response = await api.Dashboard.GetDashboardStats(adminId);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in fetching Dashboard Status');
    }
  }
);
export const fetchDashboardEvents = createAsyncThunk<
  DashboardEventsType[],
  number | undefined,
  { rejectValue: string }
>('dashboard/events', async (adminId, { rejectWithValue }) => {
  try {
    const response = await api.Dashboard.GetDashboardEvents(adminId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching Dashboard Events');
  }
});
export const fetchDashboardBday = createAsyncThunk<DashboardBdayType[], number | undefined, { rejectValue: string }>(
  'dashboard/Bday',
  async (adminId, { rejectWithValue }) => {
    try {
      const response = await api.Dashboard.GetDashboardBday(adminId);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in fetching Dashboard Bday List');
    }
  }
);
export const fetchDashboardFeeChart = createAsyncThunk<
  DashboardFeeChartType[],
  { adminId: number | undefined; academicId: number | undefined; classId: number | undefined },
  { rejectValue: string }
>('dashboard/FeeChart', async ({ adminId, academicId, classId }, { rejectWithValue }) => {
  try {
    const response = await api.Dashboard.GetDashboardFeeChart(adminId, academicId, classId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching Dashboard FeeChart List');
  }
});
export const fetchDashboardTimeTable = createAsyncThunk<
  DashboardTimeTableType[],
  { adminId: number | undefined; classId: number | undefined },
  { rejectValue: string }
>('dashboard/Timetable', async ({ adminId, classId }, { rejectWithValue }) => {
  try {
    const response = await api.Dashboard.GetDashboardTimetable(adminId, classId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching Dashboard TimeTable');
  }
});
export const fetchDashboardAttendance = createAsyncThunk<
  DashboardAttendanceType[],
  { adminId: number | undefined; classId: number | undefined },
  { rejectValue: string }
>('dashboard/Attendance', async ({ adminId, classId }, { rejectWithValue }) => {
  try {
    const response = await api.Dashboard.GetDashboardAttendance(adminId, classId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching Dashboard Attendance');
  }
});
export const fetchDashboardVideos = createAsyncThunk<
  DashboardVideosType[],
  { adminId: number | undefined; classId: number | undefined },
  { rejectValue: string }
>('dashboard/Videos', async ({ adminId, classId }, { rejectWithValue }) => {
  try {
    const response = await api.Dashboard.GetDashboardVideos(adminId, classId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching Dashboard Videos');
  }
});

export const fetchClassList = createAsyncThunk<ClassListInfo[], number | undefined, { rejectValue: string }>(
  'dashboard/ClassList',
  async (adminId, { rejectWithValue }) => {
    try {
      const response = await api.Dashboard.GetClassList(adminId);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in fetching Dashboard Class List');
    }
  }
);

export const fetchYearList = createAsyncThunk<YearDataType[], number | undefined, { rejectValue: string }>(
  'dashboard/YearList',
  async (adminId, { rejectWithValue }) => {
    try {
      const response = await api.Dashboard.GetYearList(adminId);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in fetching Dashboard Year List');
    }
  }
);
