import React, { useCallback, useMemo, useRef, useState, useEffect } from 'react';
import Page from '@/components/shared/Page';
import {
  Avatar,
  Box,
  Button,
  Divider,
  FormControl,
  Card,
  Stack,
  Collapse,
  Grid,
  IconButton,
  TextField,
  Typography,
  Select,
  MenuItem,
  Paper,
  useTheme,
  Table,
  TableBody,
  TableRow,
  TableCell,
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import { breakPointsMaxwidth } from '@/config/breakpoints';
import { useReactToPrint } from 'react-to-print';
import styled from 'styled-components';
import useSettings from '@/hooks/useSettings';
import { EXAM_SELECT_OPTIONS } from '@/config/Selection';
import TabButton from '@/components/shared/TabButton';
import logo from '@/assets/SchoolLogos/HolyLogo.jpeg';
import user from '@/assets/user.jpg';
import img0 from '@/assets/holyProgressCardImg/img.png';
import img1 from '@/assets/holyProgressCardImg/img1.png';
import img2 from '@/assets/holyProgressCardImg/img2.png';
import img3 from '@/assets/holyProgressCardImg/img3.png';
import img4 from '@/assets/holyProgressCardImg/img4.png';
import img5 from '@/assets/holyProgressCardImg/img5.png';
import img6 from '@/assets/holyProgressCardImg/img6.png';
import img7 from '@/assets/holyProgressCardImg/img7.png';
import img8 from '@/assets/holyProgressCardImg/img8.png';
import img9 from '@/assets/holyProgressCardImg/img9.png';
import img10 from '@/assets/holyProgressCardImg/img10.png';
import img11 from '@/assets/holyProgressCardImg/img11.png';
import img12 from '@/assets/holyProgressCardImg/img12.png';
import img13 from '@/assets/holyProgressCardImg/img13.png';
import img14 from '@/assets/holyProgressCardImg/img14.png';
import img15 from '@/assets/holyProgressCardImg/img15.png';
import img16 from '@/assets/holyProgressCardImg/img16.png';
import img17 from '@/assets/holyProgressCardImg/img17.png';
import img18 from '@/assets/holyProgressCardImg/img18.png';
import img19 from '@/assets/holyProgressCardImg/img19.png';
import img20 from '@/assets/holyProgressCardImg/img20.png';
import img21 from '@/assets/holyProgressCardImg/img21.png';
import img22 from '@/assets/holyProgressCardImg/img22.png';
import img23 from '@/assets/holyProgressCardImg/img23.png';
import img24 from '@/assets/holyProgressCardImg/img24.png';
import img25 from '@/assets/holyProgressCardImg/img25.png';
import img26 from '@/assets/holyProgressCardImg/img26.png';
import img27 from '@/assets/holyProgressCardImg/img27.png';
import img28 from '@/assets/holyProgressCardImg/img28.png';
import img29 from '@/assets/holyProgressCardImg/img29.png';
import debounce from 'lodash/debounce';

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  border: '1px solid black',
  height: '24px',
  fontSize: 11,
  fontWeight: 600,
  textAlign: 'center', // Aligns text to the center
  '&.table-cell': {
    // Additional styles for the "table-cell" class if needed
  },
}));

const healthStatusData = [
  { label: '', term1: 'TERM 1', term2: 'TERM 2' },
  { label: 'HEIGHT', term1: '105 cm', term2: '108 cm' },
  { label: 'WEIGHT', term1: '16 kg', term2: '16 kg' },
];

const largeMotorSkillsData = [
  { label: '', term1: 'TERM 1', term2: 'TERM 2' },
  { label: 'WALKING IN STRAIGHT LINE', term1: 'Always', term2: 'Always' },
  { label: 'WALKING ON ZIG-ZAG LINE', term1: 'Sometime', term2: 'Always' },
  { label: 'RUNNING', term1: 'Always', term2: 'Always' },
  { label: 'JUMPING', term1: 'Always', term2: 'Always' },
  { label: 'KICKING', term1: 'Always', term2: 'Always' },
  { label: 'THROWING', term1: 'Always', term2: 'Always' },
  { label: 'CATCHING', term1: 'Always', term2: 'Always' },
  { label: 'BALANCING', term1: 'Sometime', term2: 'Sometime' },
  { label: 'HOPPING', term1: 'Always', term2: 'Always' },
  { label: 'DANCING', term1: 'Always', term2: 'Always' },
];

const fineMotorSkillsData = [
  { label: '', term1: 'TERM 1', term2: 'TERM 2' },
  { label: 'PASSING THE PARCEL', term1: 'Always', term2: 'Always' },
  { label: 'PICKING OBJECT', term1: 'Sometime', term2: 'Always' },
  { label: 'ZIPPING', term1: 'Always', term2: 'Always' },
  { label: 'BUTTONING', term1: 'Sometime', term2: 'Sometime' },
  { label: 'BUCKLING', term1: 'Sometime', term2: 'Sometime' },
  { label: 'MOULDING CLAY', term1: 'Sometime', term2: 'Sometime' },
  { label: 'PAPER FOLDING', term1: 'Sometime', term2: 'Sometime' },
  { label: 'LACING', term1: 'Sometime', term2: 'Sometime' },
  { label: 'BEADING', term1: 'Always', term2: 'Always' },
  { label: 'COMPLETES PUZZLES', term1: 'Need Practice', term2: 'Need Practice' },
  { label: 'TEARING & PASTING', term1: 'Sometime', term2: 'Always' },
  { label: 'HOLDING', term1: 'Sometime', term2: 'Always' },
  { label: 'TURNING THE PAGES', term1: 'Always', term2: 'Always' },
  { label: 'PINCER GRIP', term1: 'Sometime', term2: 'Always' },
];

const personalityDevelopmentData = [
  { label: 'SHARES THINGS', term1: 'Always', term2: 'Always' },
  { label: 'COMES NEATLY DRESSED', term1: 'Always', term2: 'Always' },
  { label: 'FOLLOWS PERSONAL HYGIENE', term1: 'Always', term2: 'Always' },
  { label: 'REGULAR TO SCHOOL', term1: 'Always', term2: 'Always' },
  { label: 'COMES ON TIME', term1: 'Need Practice', term2: 'Sometime' },
  { label: 'RESPECTS TEACHER', term1: 'Always', term2: 'Always' },
  { label: 'RESPECTS PEER GROUP', term1: 'Always', term2: 'Always' },
  { label: 'COMPLETES TASK ON TIME', term1: 'Need Practice', term2: 'Sometime' },
  { label: 'WAITS FOR TURN', term1: 'Sometime', term2: 'Sometime' },
  { label: 'PARTICIPATES IN ACTIVITY', term1: 'Always', term2: 'Always' },
  { label: 'WORKS INDEPENDENTLY', term1: 'Sometime', term2: 'Sometime' },
];

const literacySkillsData = [
  { label: 'LISTENING TO RHYME', term1: 'Always', term2: 'Always' },
  { label: 'LISTENING TO STORIES', term1: 'Sometime', term2: 'Always' },
  { label: 'RECOGNISES LETTERS', term1: 'Need Practice', term2: 'Sometime' },
  { label: 'IDENTIFIES PHONIC SOUND', term1: 'Need Practice', term2: 'Sometime' },
  { label: 'RECITES RHYME', term1: 'Sometime', term2: 'Sometime' },
  { label: 'RECITES PHONIC SONG WITH ACTION', term1: 'Sometime', term2: 'Sometime' },
  { label: 'READS WORDS WITH PHONIC SOUND', term1: 'Always', term2: 'Sometime' },
  { label: 'RECOGNISES SIGHT WORDS', term1: 'Need Practice', term2: 'Sometime' },
  { label: 'CONVERSE IN ENGLISH', term1: 'Need Practice', term2: 'Sometime' },
  { label: 'NARRATES STORY', term1: 'Sometime', term2: 'Sometime' },
  { label: 'TRACE/WRITE LETTERS OR SENTENCES', term1: 'Sometime', term2: 'Sometime' },
  { label: 'NEATNESS IN WRITING', term1: 'Need Practice', term2: 'Sometime' },
  { label: 'WRITES WITH CORRECT FORMATION', term1: 'Need Practice', term2: 'Need Practice' },
];

const numeracySkillsData = [
  { label: 'RECOGNISES NUMBERS', term1: 'Sometime', term2: 'Sometime' },
  { label: 'COUNTS THE OBJECTS', term1: 'Sometime', term2: 'Always' },
  { label: 'SEQUENCING THE NUMBERS', term1: 'Sometime', term2: 'Sometime' },
  { label: 'AWARE OF MATHEMATICAL CONCEPT', term1: 'Always', term2: 'Always' },
  { label: 'TRACE/WRITE NUMBERS', term1: 'Sometime', term2: 'Sometime' },
  { label: 'WRITES WITH CORRECT FORMATION', term1: 'Sometime', term2: 'Sometime' },
  { label: 'NEATNESS IN WRITING', term1: 'Need Practice', term2: 'Sometime' },
  { label: 'EXPLORE WAYS TO SOLVE PROBLEM', term1: 'Always', term2: 'Always' },
];

const generalAwarenessSkillsData = [
  { label: 'IDENTIFIES THINGS IN ENVIRONMENT', term1: 'Always', term2: 'Always' },
  { label: 'SORTS AND CLASSIFIES OBJECT', term1: 'Always', term2: 'Always' },
  { label: 'OBSERVES WITH INTEREST DURING EXPERIMENTATION', term1: 'Always', term2: 'Always' },
  { label: 'INVOLVES IN EXPERIMENTAL LEARNING', term1: 'Sometime', term2: 'Sometime' },
  { label: 'UNDERSTANDING OF LIFE SKILLS', term1: 'Always', term2: 'Always' },
];

const musicAndMovementsData = [
  { label: 'SHOWS INTEREST IN LISTENING TO SONGS', term1: 'Always', term2: 'Always' },
  { label: 'REMEMBERS LYRICS OF THE SONG', term1: 'Need Practice', term2: 'Sometime' },
  { label: 'SING SONGS WITH RHYTHM', term1: 'Sometime', term2: 'Sometime' },
  { label: 'SHOWS INTEREST IN DANCE', term1: 'Sometime', term2: 'Sometime' },
];

const artAndCraftData = [
  { label: 'SHOWS INTEREST IN ART', term1: 'Always', term2: 'Always' },
  { label: 'ENJOYS COLOURING', term1: 'Always', term2: 'Always' },
  { label: 'SHOWS IMAGINATION', term1: 'Sometime', term2: 'Sometime' },
  { label: 'SHOWS CREATIVITY', term1: 'Sometime', term2: 'Sometime' },
  { label: 'NEATNESS IN COLOURING', term1: 'Need Practice', term2: 'Sometime' },
];

const academicAssessmentData = [
  {
    subject: 'English',
    skills: [
      { skill: 'Oral', marks: 25, term1: 10, term2: 11 },
      { skill: 'Written', marks: 50, term1: 23, term2: 25 },
      { skill: 'TOTAL', marks: 75, term1: 33, term2: 35.5 },
    ],
  },
  {
    subject: 'Maths',
    skills: [
      { skill: 'Oral', marks: 25, term1: 10, term2: 11 },
      { skill: 'Written', marks: 50, term1: 23, term2: 25 },
      { skill: 'TOTAL', marks: 75, term1: 33, term2: 35.5 },
    ],
  },
  {
    subject: 'E V S',
    skills: [
      { skill: 'Oral', marks: 25, term1: 10, term2: 11 },
      { skill: 'Written', marks: 50, term1: 23, term2: 25 },
      { skill: 'TOTAL', marks: 75, term1: 33, term2: 35.5 },
    ],
  },
  {
    subject: 'Hindi',
    skills: [
      { skill: 'Oral', marks: 25, term1: 10, term2: 11 },
      { skill: 'Written', marks: 50, term1: 23, term2: 25 },
      { skill: 'TOTAL', marks: 75, term1: 33, term2: 35.5 },
    ],
  },
  {
    subject: ' ',
    skills: [{ skill: 'GRAND TOTAL', marks: 250, term1: 250, term2: 250 }],
  },
  {
    subject: 'Colouring',
    skills: [{ skill: 'Grade', marks: ' ', term1: 'C+', term2: 'C+' }],
  },
  {
    subject: 'Craft',
    skills: [{ skill: 'Grade', marks: ' ', term1: 'C+', term2: 'C+' }],
  },
];

const attendanceData = [{ label: 'ATTENDANCE', term1: '82 / 94', term2: '82 / 92' }];

const gradationData = [
  { range: '91% & ABOVE', grade: 'A+', proficiency: 'EXCELLENT' },
  { range: '75-90%', grade: 'A', proficiency: 'VERY GOOD' },
  { range: '60-74%', grade: 'B+', proficiency: 'GOOD' },
  { range: '46-59%', grade: 'B', proficiency: 'AVERAGE' },
  { range: 'BELOW 45%', grade: 'C', proficiency: 'NEEDS IMPROVEMENT' },
];

const termResultData = [
  { label: 'PERCENTAGE', value: '60.3%' },
  { label: 'GRADE', value: '60.3%' },
  { label: 'RANK', value: '2' },
];

const HolyRoot = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    min-height: calc(100vh - 100px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      height: calc(100% - 100px);
      flex-grow: 1;
      .certificate {
        /* border: 2px solid ${(props) => props.theme.palette.primary.main}; */
        /* padding: 0px 20px; */
        /* border-radius: 10px; */
      }
      .main-card-container {
        /* flex-grow: 1;*/
        /* width: 100%;*/
        /* height: 100%;*/
        /* display: flex;*/
        /* flex-direction: column;*/
        /* border: 1px solid ${(props) => props.theme.palette.secondary.lighter};*/
        /* border-radius: 6px;*/
        /* overflow: hidden;*/
        .card-top {
          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.primary.lighter : props.theme.palette.grey[900]};
        }

        .card-table-container {
          flex-grow: 1;
          width: 100%;
          height: 100%;
          display: flex;
          flex-direction: column;
          /* border: 1px solid ${(props) => props.theme.palette.secondary.lighter}; */
          overflow: hidden;
          border-radius: 0px;

          .MuiTableContainer-root {
            height: 100%;
          }
          .MuiTable-root {
          }

          .MuiTablePagination-root {
            flex-grow: 1;
            flex-shrink: 0;
          }
        }
      }
      .avg_circle_icon {
        font-size: 10px;
        color: ${(props) => props.theme.palette.primary.light};
        margin-right: 3px;
      }
      @media ${breakPointsMaxwidth.xl} {
        .MuiTableCell-root {
          font-size: 11px;
        }
        .MuiTableCell-root .MuiTypography-root {
          font-size: 11px;
        }
      }
      @media screen and (max-width: 1217px) {
        .MuiFormControl-root {
          width: 200px;
        }
        .select_box {
          width: 200px;
        }
      }
      @media screen and (max-width: 1160px) {
        .MuiTableContainer-root {
          /* width: 900px; */
        }
        .card-table-container {
          overflow: auto;
        }
      }
    }
    .card_top {
      display: flex;
      justify-content: space-between;
      flex-direction: row;
    }
    .title_searchbar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex: 1;
    }
    .progress_report_tab {
      display: flex;
      align-items: center;
      flex-direction: row;
    }
    @media screen and (max-width: 1160px) {
      .card_top {
        display: flex;
        flex-direction: column;
      }
      .progress_report_tab {
        display: flex;
        justify-content: end;
        padding-bottom: 10px;
        overflow-x: auto;
        ::-webkit-scrollbar {
          height: 10px;
        }
        ::-webkit-scrollbar-thumb {
          background-color: ${(props) => props.theme.palette.grey[400]};
          border-radius: 20px;
        }
      }
    }
    @media screen and (max-width: 998px) {
      .progress_report_tab {
        justify-content: start;
      }
    }
    @media screen and (max-width: 768px) {
      .progress_report_tab {
        ::-webkit-scrollbar {
          height: 0px;
        }
      }
    }
  }
`;

export type StudentClassWiseProps = {
  onClickPromotionList: () => void;
  onClickClassWise: () => void;
  onClickTopper: () => void;
  onClickGradeWise: () => void;
};

function Holy({ onClickPromotionList, onClickClassWise, onClickTopper, onClickGradeWise }: StudentClassWiseProps) {
  const [divCount, setDivCount] = useState(1);
  const [showFilter, setShowFilter] = useState(false);
  const theme = useTheme();
  const { themeMode } = useSettings();

  const isPrinting = useRef(false);

  const handleBeforePrint = () => {
    isPrinting.current = true;
    document.querySelectorAll('img').forEach((img) => {
      if (img.loading === 'lazy') {
        img.loading = 'eager'; // Force eager loading
      }
    });
  };

  const handleAfterPrint = () => {
    isPrinting.current = false;
  };

  const preloadImages = (imageUrls: string[]) => {
    imageUrls.forEach((url) => {
      const img = new Image();
      img.src = url;
    });
  };

  const renderTableRows = (data, images, width) => {
    return data.map((row, index) => (
      <TableRow key={index}>
        {index === 0 && (
          <StyledTableCell rowSpan={data.length} align="center">
            <Stack gap={2} alignItems="center">
              {images.map((imgSrc, imgIndex) => (
                <img key={imgIndex} width={width} src={imgSrc} alt="" loading={isPrinting.current ? 'eager' : 'lazy'} />
              ))}
            </Stack>
          </StyledTableCell>
        )}
        <StyledTableCell>{row.label}</StyledTableCell>
        <StyledTableCell>{row.term1}</StyledTableCell>
        <StyledTableCell>{row.term2}</StyledTableCell>
        {index === 0 && (
          <StyledTableCell rowSpan={data.length} align="center">
            <Stack gap={2} alignItems="center">
              {images.map((imgSrc, imgIndex) => (
                <img key={imgIndex} width={width} src={imgSrc} alt="" loading={isPrinting.current ? 'eager' : 'lazy'} />
              ))}
            </Stack>
          </StyledTableCell>
        )}
      </TableRow>
    ));
  };

  const debouncedSetDivCount = useCallback((value: number) => debounce(() => setDivCount(value), 300)(), [setDivCount]);

  const reportRef = useRef<HTMLDivElement | null>(null);
  const handlePrint = useReactToPrint({
    content: () => reportRef.current,
    onBeforePrint: handleBeforePrint,
    onAfterPrint: handleAfterPrint,
    documentTitle: `Student_Wise_Progress_Report_Holy_${new Date()
      .toLocaleString('en-GB')
      .replace(/\//g, '-')
      .replace(/:/g, '.')
      .replace(/, /g, '_')}`,
    pageStyle: `
      @page {
        size: A4;
      }
      @media print {
        body {
          -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
        }
        .MuiTableCell-root {
          border: 1px solid ${theme.palette.secondary.main} !important;
          height: 5px !important;
        }
        .MuiTableContainer-root {
          height: 100%;
        }
      }
    `,
  });

  const memoizedImageUrls = useMemo(
    () => [
      img0,
      img1,
      img2,
      img3,
      img4,
      img5,
      img6,
      img7,
      img8,
      img9,
      img10,
      img11,
      img12,
      img13,
      img14,
      img15,
      img16,
      img17,
      img18,
      img19,
      img20,
      img21,
      img22,
      img23,
      img24,
      img25,
      img26,
      img27,
      img28,
      img29,
    ],
    []
  );

  useEffect(() => {
    preloadImages(memoizedImageUrls);
  }, [memoizedImageUrls]);

  const handleDivCountChange = (e) => {
    const value = Number(e.target.value);
    debouncedSetDivCount(value);
  };

  return (
    <Page title="Student Wise">
      <HolyRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <div className="card_top ">
            <div className="title_searchbar">
              <Typography variant="h6" fontSize={17}>
                Student Individual
              </Typography>

              <Box sx={{ flexShrink: 0 }}>
                <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                  <SearchIcon />
                </IconButton>
              </Box>
            </div>
            <div className="progress_report_tab">
              <div style={{ flexShrink: 0 }}>
                <TabButton title="Student Individual" variant="contained" />
                <TabButton title="Student Promotion List" variant="outlined" onClick={onClickPromotionList} />
                <TabButton title="Class Wise" variant="outlined" onClick={onClickClassWise} />
                <TabButton title="Topper" variant="outlined" onClick={onClickTopper} />
                <TabButton title="Grade Wise" variant="outlined" onClick={onClickGradeWise} />
              </div>
            </div>
          </div>
          <Divider sx={{ py: 1 }} />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={3} alignItems="end">
                  <Grid item sm="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', xl: 240 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Year
                      </Typography>
                      <TextField name="year" sx={{ minWidth: { xs: '100%', xl: 240 } }} />
                    </FormControl>
                  </Grid>
                  <Grid item sm="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', xl: 240 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <TextField name="className" sx={{ minWidth: { xs: '100%', xl: 240 } }} />
                    </FormControl>
                  </Grid>
                  <Grid item sm="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', xl: 240 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Exam
                      </Typography>
                      <Select
                        sx={{ minWidth: { xs: '100%', xl: 240 } }}
                        className="select_box"
                        labelId="classStatusFilter"
                        id="classStatusFilterSelect"
                      >
                        <MenuItem value={-1}>All</MenuItem>
                        {EXAM_SELECT_OPTIONS.map((opt) => (
                          <MenuItem key={opt.id} value={opt.id}>
                            {opt.exam}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item sm="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', xl: 240 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Student
                      </Typography>
                      <TextField name="className" sx={{ minWidth: { xs: '100%', xl: 240 } }} />
                    </FormControl>
                  </Grid>
                  <Grid item sm="auto" xs={12}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button type="reset" variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, my: 3 }}>
              <Stack spacing={2} direction="row">
                <Box>
                  <Button
                    variant="contained"
                    disabled={divCount === 0}
                    onClick={() => setDivCount(divCount - 1)}
                    sx={{ ml: 2 }}
                  >
                    -
                  </Button>
                  <TextField type="number" value={divCount} onChange={handleDivCountChange} sx={{ mx: 2 }} />
                  <Button variant="contained" onClick={() => setDivCount(divCount + 1)}>
                    +
                  </Button>
                  <Button variant="contained" color="primary" onClick={handlePrint} sx={{ ml: 2 }}>
                    Print
                  </Button>
                </Box>
              </Stack>
            </Box>

            <Box display="flex" justifyContent="center" alignItems="center">
              <Box ref={reportRef} sx={{ WebkitPrintColorAdjust: 'exact' }} className="main-card-container">
                {Array.from({ length: divCount }).map((_, pageIndex) => (
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      '@media print': {
                        padding: 0,
                      },
                    }}
                    key={pageIndex}
                  >
                    {/* =============== Page No 1 =============== */}
                    <Paper
                      style={{
                        pageBreakAfter: 'always',
                        width: '207mm',
                        height: '297mm',
                        paddingInline: '10px',
                        backgroundColor: '#ffff99',
                        border: '2px dashed black',
                      }}
                    >
                      <Stack mt={5} direction="row" alignItems="center" gap={2} justifyContent="center">
                        <img src={logo} width={100} alt="" loading={isPrinting.current ? 'eager' : 'lazy'} />
                        <Typography variant="h4" color="error" fontWeight="bold">
                          <i> ANGELS&apos; PARADISE</i>
                        </Typography>
                      </Stack>
                      <Stack direction="column" alignItems="center" gap={2} justifyContent="center">
                        <Typography color="black" variant="h6" fontStyle="italic">
                          REPORT CARD
                        </Typography>
                        <Typography color="black" variant="h6" fontStyle="italic">
                          (ACADEMIC YEAR 2024-2025)
                        </Typography>
                      </Stack>
                      <Grid container justifyContent="center" style={{ margin: '10px 0 30px 0 ' }}>
                        <img
                          style={{ borderRadius: 0, width: '3.5cm', height: '4.5cm', border: '2px solid black' }}
                          alt="Student Avatar"
                          src={user}
                          loading={isPrinting.current ? 'eager' : 'lazy'}
                        />
                      </Grid>
                      {[
                        ['NAME', 'AADIRAJ SAGAR MORE'],
                        ['ROLL NO.', '1'],
                        ["FATHER'S NAME", 'SAGAR'],
                        ["MOTHER'S NAME", 'ASHWINI'],
                        ['CLASS', 'JRKG'],
                        ['SECTION', 'A'],
                        ['DATE OF BIRTH', '07/Apr/2020'],
                        ['NAME OF THE CLASS TEACHER', 'JANHAVI SHRIPURN JOSHI'],
                      ].map(([label, value]) => (
                        <Stack direction="row" alignItems="center" gap={0} justifyContent="space-around" pl={5}>
                          <Stack width="40%" textAlign="start" mb={4}>
                            <Typography color="black" variant="subtitle2" fontSize={13}>
                              {label}
                            </Typography>
                          </Stack>
                          <Stack width="60%" textAlign="start" mb={4}>
                            <Typography color="black" variant="subtitle2" fontSize={13}>
                              :&nbsp;{value}
                            </Typography>
                          </Stack>
                        </Stack>
                      ))}
                      <Stack mt={0} direction="row" justifyContent="space-between" alignItems="center">
                        <img
                          width={150}
                          src={memoizedImageUrls[0]}
                          alt="Decoration 1"
                          loading={isPrinting.current ? 'eager' : 'lazy'}
                        />
                        <img
                          width={190}
                          src={memoizedImageUrls[1]}
                          alt="Decoration 2"
                          loading={isPrinting.current ? 'eager' : 'lazy'}
                        />
                      </Stack>
                    </Paper>
                    {/* Page Break - Forces Next Content to Print on a New A4 Page */}
                    {/* =============== Page No 2 =============== */}
                    <Paper
                      style={{
                        pageBreakAfter: 'always',
                        width: '207mm',
                        height: '297mm',
                        padding: '10px',
                        backgroundColor: '#ffff99',
                        border: '2px dashed black',
                        margin: 'auto',
                      }}
                    >
                      {' '}
                    </Paper>
                    {/* =============== Page No 3 =============== */}
                    <Paper
                      style={{
                        pageBreakAfter: 'always',
                        width: '207mm',
                        height: '297mm',
                        padding: '10px',
                        backgroundColor: '#ffff99',
                        border: '2px dashed black',
                        margin: 'auto',
                      }}
                    >
                      <Box border={1} borderColor="black">
                        <Stack direction="row" py={1} px={1} gap={1}>
                          <Typography color="black" variant="subtitle2" fontSize={12}>
                            NAME:
                          </Typography>
                          <Typography color="black" variant="subtitle2" fontSize={12} borderBottom={1} width={250}>
                            {' '}
                          </Typography>
                          <Typography color="black" variant="subtitle2" fontSize={12}>
                            CLASS:
                          </Typography>
                          <Typography color="black" variant="subtitle2" fontSize={12} borderBottom={1} width={110}>
                            {' '}
                          </Typography>
                          <Typography color="black" variant="subtitle2" fontSize={12}>
                            ROL NO:
                          </Typography>
                          <Typography color="black" variant="subtitle2" fontSize={12} borderBottom={1} width={110}>
                            {' '}
                          </Typography>
                        </Stack>
                        <Table>
                          <TableBody>
                            {/* HEALTH STATUS */}
                            <TableRow>
                              <StyledTableCell colSpan={5} align="center" style={{ fontWeight: 600 }}>
                                HEALTH STATUS
                              </StyledTableCell>
                            </TableRow>
                            {renderTableRows(healthStatusData, [memoizedImageUrls[2], memoizedImageUrls[3]], 60)}

                            {/* LARGE MOTOR SKILLS */}
                            <TableRow>
                              <StyledTableCell colSpan={5} align="center" style={{ fontWeight: 600 }}>
                                LARGE MOTOR SKILLS
                              </StyledTableCell>
                            </TableRow>
                            {renderTableRows(largeMotorSkillsData, [memoizedImageUrls[4], memoizedImageUrls[5]], 80)}
                            {/* FINE MOTOR SKILLS */}
                            <TableRow>
                              <StyledTableCell colSpan={5} align="center" style={{ fontWeight: 600 }}>
                                FINE MOTOR SKILLS
                              </StyledTableCell>
                            </TableRow>
                            {renderTableRows(fineMotorSkillsData, [memoizedImageUrls[8], memoizedImageUrls[9]], 100)}
                          </TableBody>
                        </Table>
                      </Box>
                    </Paper>
                    {/* =============== Page No 4 =============== */}

                    <Paper
                      style={{
                        pageBreakAfter: 'always',
                        width: '207mm',
                        height: '297mm',
                        padding: '10px',
                        backgroundColor: '#ffff99',
                        border: '2px dashed black',
                        margin: 'auto',
                      }}
                    >
                      <Box border={1} borderColor="black">
                        <Stack direction="row" py={1} px={1} gap={1}>
                          <Typography color="black" variant="subtitle2" fontSize={12}>
                            NAME:
                          </Typography>
                          <Typography color="black" variant="subtitle2" fontSize={12} borderBottom={1} width={250}>
                            {' '}
                          </Typography>
                          <Typography color="black" variant="subtitle2" fontSize={12}>
                            CLASS:
                          </Typography>
                          <Typography color="black" variant="subtitle2" fontSize={12} borderBottom={1} width={110}>
                            {' '}
                          </Typography>
                          <Typography color="black" variant="subtitle2" fontSize={12}>
                            ROL NO:
                          </Typography>
                          <Typography color="black" variant="subtitle2" fontSize={12} borderBottom={1} width={110}>
                            {' '}
                          </Typography>
                        </Stack>
                        <Table>
                          <TableBody>
                            {/* PERSONALITY DEVELOPMENT */}
                            <TableRow>
                              <StyledTableCell colSpan={5} align="center" style={{ fontWeight: 600 }}>
                                PERSONALITY DEVELOPMENT
                              </StyledTableCell>
                            </TableRow>
                            {renderTableRows(
                              personalityDevelopmentData,
                              [memoizedImageUrls[12], memoizedImageUrls[13]],
                              120
                            )}

                            {/* LITERACY SKILLS */}
                            <TableRow>
                              <StyledTableCell colSpan={5} align="center" style={{ fontWeight: 600 }}>
                                LITERACY SKILLS
                              </StyledTableCell>
                            </TableRow>
                            {renderTableRows(literacySkillsData, [memoizedImageUrls[16], memoizedImageUrls[17]], 120)}

                            {/* NUMERACY SKILLS */}
                            <TableRow>
                              <StyledTableCell colSpan={5} align="center" style={{ fontWeight: 600 }}>
                                NUMERACY SKILLS
                              </StyledTableCell>
                            </TableRow>
                            {renderTableRows(numeracySkillsData, [memoizedImageUrls[20], memoizedImageUrls[21]], 120)}
                          </TableBody>
                        </Table>
                      </Box>
                    </Paper>
                    {/* Page No 5 */}
                    <Paper
                      style={{
                        pageBreakAfter: 'always',
                        width: '207mm',
                        height: '297mm',
                        padding: '10px',
                        backgroundColor: '#ffff99',
                        border: '2px dashed black',
                        margin: 'auto',
                      }}
                    >
                      <Box border={1} borderColor="black">
                        <Stack direction="row" py={1} px={1} gap={1}>
                          <Typography color="black" variant="subtitle2" fontSize={12}>
                            NAME:
                          </Typography>
                          <Typography color="black" variant="subtitle2" fontSize={12} borderBottom={1} width={250}>
                            {' '}
                          </Typography>
                          <Typography color="black" variant="subtitle2" fontSize={12}>
                            CLASS:
                          </Typography>
                          <Typography color="black" variant="subtitle2" fontSize={12} borderBottom={1} width={110}>
                            {' '}
                          </Typography>
                          <Typography color="black" variant="subtitle2" fontSize={12}>
                            ROL NO:
                          </Typography>
                          <Typography color="black" variant="subtitle2" fontSize={12} borderBottom={1} width={110}>
                            {' '}
                          </Typography>
                        </Stack>
                        <Table>
                          <TableBody>
                            {/* GENERAL AWARENESS SKILLS */}
                            <TableRow>
                              <StyledTableCell colSpan={5} align="center" style={{ fontWeight: 600 }}>
                                GENERAL AWARENESS SKILLS
                              </StyledTableCell>
                            </TableRow>
                            {renderTableRows(generalAwarenessSkillsData, [memoizedImageUrls[24]], 120)}

                            {/* MUSIC & MOVEMENTS */}
                            <TableRow>
                              <StyledTableCell colSpan={5} align="center" style={{ fontWeight: 600 }}>
                                MUSIC & MOVEMENTS
                              </StyledTableCell>
                            </TableRow>
                            {renderTableRows(musicAndMovementsData, [memoizedImageUrls[26]], 120)}

                            {/* ART & CRAFT */}
                            <TableRow>
                              <StyledTableCell colSpan={5} align="center" style={{ fontWeight: 600 }}>
                                ART & CRAFT
                              </StyledTableCell>
                            </TableRow>
                            {renderTableRows(artAndCraftData, [memoizedImageUrls[28]], 120)}

                            {/* ACADEMIC ASSESSMENT */}
                            <TableRow>
                              <StyledTableCell colSpan={5} align="center" style={{ fontWeight: 600 }}>
                                ACADEMIC ASSESSMENT
                              </StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>Subject</StyledTableCell>
                              <StyledTableCell>Skill</StyledTableCell>
                              <StyledTableCell>Marks</StyledTableCell>
                              <StyledTableCell>TERM 1</StyledTableCell>
                              <StyledTableCell>TERM 2</StyledTableCell>
                            </TableRow>
                            {academicAssessmentData.map((subject, subjectIndex) =>
                              subject.skills.map((skill, skillIndex) => (
                                <TableRow key={`${subjectIndex}-${skillIndex}`}>
                                  {skillIndex === 0 && (
                                    <StyledTableCell rowSpan={subject.skills.length} align="center">
                                      {subject.subject}
                                    </StyledTableCell>
                                  )}
                                  <StyledTableCell>{skill.skill}</StyledTableCell>
                                  <StyledTableCell>{skill.marks}</StyledTableCell>
                                  <StyledTableCell>{skill.term1}</StyledTableCell>
                                  <StyledTableCell>{skill.term2}</StyledTableCell>
                                </TableRow>
                              ))
                            )}

                            {/* ATTENDANCE */}
                            <TableRow>
                              <StyledTableCell colSpan={5} align="center" style={{ fontWeight: 600 }}>
                                ATTENDANCE
                              </StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell colSpan={2}> </StyledTableCell>
                              <StyledTableCell>TERM 1</StyledTableCell>
                              <StyledTableCell>TERM 2</StyledTableCell>
                              <StyledTableCell> </StyledTableCell>
                            </TableRow>
                            {attendanceData.map((row, index) => (
                              <TableRow key={index}>
                                <StyledTableCell colSpan={2} align="center">
                                  {row.label}
                                </StyledTableCell>
                                <StyledTableCell>{row.term1}</StyledTableCell>
                                <StyledTableCell>{row.term2}</StyledTableCell>
                                <StyledTableCell> </StyledTableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </Box>
                    </Paper>
                    {/* Page No 6 */}
                    <Paper
                      style={{
                        pageBreakAfter: 'always',
                        width: '207mm',
                        height: '297mm',
                        padding: '10px',
                        backgroundColor: '#ffff99',
                        border: '2px dashed black',
                        margin: 'auto',
                      }}
                    >
                      <Box border={1} borderColor="black">
                        <Stack direction="row" py={1} px={1} gap={1}>
                          <Typography color="black" variant="subtitle2" fontSize={12}>
                            NAME:
                          </Typography>
                          <Typography color="black" variant="subtitle2" fontSize={12} borderBottom={1} width={250}>
                            {' '}
                          </Typography>
                          <Typography color="black" variant="subtitle2" fontSize={12}>
                            CLASS:
                          </Typography>
                          <Typography color="black" variant="subtitle2" fontSize={12} borderBottom={1} width={110}>
                            {' '}
                          </Typography>
                          <Typography color="black" variant="subtitle2" fontSize={12}>
                            ROL NO:
                          </Typography>
                          <Typography color="black" variant="subtitle2" fontSize={12} borderBottom={1} width={110}>
                            {' '}
                          </Typography>
                        </Stack>
                        <Table>
                          <TableBody>
                            {/* GRADATION */}
                            <TableRow>
                              <StyledTableCell colSpan={3} align="center" style={{ fontWeight: 600 }}>
                                GRADATION
                              </StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>RANGE</StyledTableCell>
                              <StyledTableCell>GRADE</StyledTableCell>
                              <StyledTableCell>PROFICIENCY LEVEL</StyledTableCell>
                            </TableRow>
                            {gradationData.map((row, index) => (
                              <TableRow key={index}>
                                <StyledTableCell>{row.range}</StyledTableCell>
                                <StyledTableCell>{row.grade}</StyledTableCell>
                                <StyledTableCell>{row.proficiency}</StyledTableCell>
                              </TableRow>
                            ))}

                            {/* TERM RESULT */}
                            <TableRow>
                              <StyledTableCell colSpan={3} align="center" style={{ fontWeight: 600 }}>
                                TERM RESULT
                              </StyledTableCell>
                            </TableRow>
                            {termResultData.map((row) => (
                              <TableRow>
                                <StyledTableCell colSpan={2} align="center" style={{ fontWeight: 600 }}>
                                  {row.label}
                                </StyledTableCell>
                                <StyledTableCell>{row.value}</StyledTableCell>
                              </TableRow>
                            ))}

                            {/* REMARKS */}
                            <TableRow>
                              <StyledTableCell colSpan={3} align="center" style={{ fontWeight: 600 }}>
                                REMARKS
                              </StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell colSpan={3} align="center" style={{ fontWeight: 600 }}>
                                Good
                              </StyledTableCell>
                            </TableRow>

                            {/* RESULT */}
                            <TableRow>
                              <StyledTableCell colSpan={3} align="center" style={{ fontWeight: 600 }}>
                                RESULT
                              </StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell colSpan={3} align="center" style={{ fontWeight: 600 }}>
                                PASSED TO / PROMOTED TO CLASS : SRKG - C
                              </StyledTableCell>
                            </TableRow>
                          </TableBody>
                        </Table>
                        <Typography color="black" p={1} variant="subtitle2" textAlign="start" fontSize={12}>
                          ISSUE DATE:
                        </Typography>
                        <Stack px={1} mt={20} direction="row" justifyContent="space-between">
                          <Typography color="black" variant="subtitle2" fontSize={12}>
                            CLASS TEACHER
                          </Typography>
                          <Typography color="black" variant="subtitle2" fontSize={12}>
                            PRINCIPAL
                          </Typography>
                          <Typography color="black" variant="subtitle2" fontSize={12}>
                            HEADMISTRESS
                          </Typography>
                          <Typography color="black" variant="subtitle2" fontSize={12}>
                            PARENT
                          </Typography>
                        </Stack>
                      </Box>
                    </Paper>
                  </Box>
                ))}
              </Box>
            </Box>
          </div>
        </Card>
      </HolyRoot>
    </Page>
  );
}

export default Holy;
