/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/alt-text */
import React, { FormEvent, useCallback, useEffect, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  Collapse,
  Tooltip,
  FormControl,
  Select,
  MenuItem,
  Chip,
  SelectChangeEvent,
  FormControlLabel,
  Radio,
  RadioGroup,
} from '@mui/material';
import styled from 'styled-components';
import SearchIcon from '@mui/icons-material/Search';
import { IoIosArrowUp } from 'react-icons/io';
import ErrorIcon from '@/assets/ManageFee/ErrorIcon.json';
import Success from '@/assets/ManageFee/Success.json';
import deleteBin from '@/assets/ManageFee/deleteBin.json';
import deleteSuccess from '@/assets/ManageFee/deleteSuccess.json';
import errorIcon from '@/assets/ManageFee/Error.json';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import DeleteIcon from '@mui/icons-material/Delete';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import NoData from '@/assets/no-datas.png';
import { MdAdd } from 'react-icons/md';
import useAuth from '@/hooks/useAuth';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import { useAppSelector } from '@/hooks/useAppSelector';
import { getManageFeeSubmitting, getTermFeeListData, getTermFeeListStatus, getYearData } from '@/config/storeSelectors';
import { CreateTermFeeSettingTitleDataType, DeleteTermFeeListType, GetTermFeeListType } from '@/types/ManageFee';
import { createTermFeeSettingTitle, deleteTermFeeList, fetchTermFeeList } from '@/store/ManageFee/manageFee.thunks';
import SaveIcon from '@mui/icons-material/Save';

import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { FEE_TYPE_ID_OPTIONS } from '@/config/Selection';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import { ErrorMessage } from '@/components/shared/Popup/ErrorMessage';
import { fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import dayjs, { Dayjs } from 'dayjs';
import DatePickers from '@/components/shared/Selections/DatePicker';
import LoadingButton from '@mui/lab/LoadingButton';
import CreateMultipleTermTitleForm from './CreateMultipleTermTitleForm';
import { TermFeeListCreateEditForm } from './TermFeeListCreateEditForm';
import { useTheme } from '@mui/material';
import useSettings from '@/hooks/useSettings';
import DTVirtuoso from '@/components/shared/RND/DataTableVirtuoso';

const ListRoot = styled.div`
  padding: 1rem;
  @media screen and (max-width: 576px) {
    padding: 0.5rem;
  }
  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 576px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 50px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid ${(props) => props.theme.palette.grey[200]};
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

const DefaultTermFeeInfo: CreateTermFeeSettingTitleDataType = {
  adminId: 0,
  termId: 0,
  termTitle: '',
  startDate: '',
  endDate: '',
  feeTypeId: 0,
};

function TermFeeList() {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const { user } = useAuth();
  const dispatch = useAppDispatch();
  const { confirm } = useConfirm();
  const [showFilter, setShowFilter] = useState(true);
  const [drawerOpen, setDrawerOpen] = React.useState<boolean>(false);
  const adminId: number = user ? user.accountId : 0;
  const YearData = useAppSelector(getYearData);
  const termFeeListData = useAppSelector(getTermFeeListData);
  const termFeeListStatus = useAppSelector(getTermFeeListStatus);
  const isSubmitting = useAppSelector(getManageFeeSubmitting);

  const [selectedTermFeeDetail, setSelectedTermFeeDetail] =
    useState<CreateTermFeeSettingTitleDataType>(DefaultTermFeeInfo);
  const defaultYear = YearData[0]?.accademicId || 0;
  const [academicYearFilter, setAcademicYearFilter] = useState(defaultYear);
  const [feeTypeFilter, setFeeTypeFilter] = useState(0);
  const [termTitleFilter, setTermTitleFilter] = useState('');
  const [termFeeData, setTermFeeData] = useState<CreateTermFeeSettingTitleDataType[]>([]);
  // const [statusFilter, setStatusFilter] = useState(-1);
  const [showCreateMultiple, setShowCreateMultiple] = React.useState<'list' | 'multiple'>('list');

  const [selectedRows, setSelectedRows] = useState<CreateTermFeeSettingTitleDataType[]>([]);
  const [editedRows, setEditedRows] = useState<CreateTermFeeSettingTitleDataType[]>([]);

  const [currentPage, setCurrentPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const currentTermFeeListRequest = useMemo(
    () => ({
      adminId,
      accademicId: academicYearFilter,
      termTitle: termTitleFilter,
      feeTypeId: feeTypeFilter,
    }),
    [termTitleFilter, academicYearFilter, adminId, feeTypeFilter]
  );
  const loadTermFeeList = useCallback(
    async (request: GetTermFeeListType) => {
      try {
        const response = await dispatch(fetchTermFeeList(request));
        // Handle the response if needed
        console.log('response::::----', response);
      } catch (error) {
        // Log the error or handle it appropriately
        console.error('Failed to load term fee list:', error);
      }
    },
    [dispatch]
  );

  useEffect(() => {
    if (termFeeListStatus === 'idle') {
      loadTermFeeList(currentTermFeeListRequest);
      dispatch(fetchYearList(adminId));
    }
    setTermFeeData(termFeeListData);
    // console.log('datass::', cuseEffectlassListData);
    console.log('selectedRows::', selectedRows);
  }, [loadTermFeeList, termFeeListStatus, selectedRows, currentTermFeeListRequest, adminId, dispatch, termFeeListData]);

  // const handleStatusChange = (e: SelectChangeEvent) => {
  //   const statusVal = parseInt(e.target.value, 10);
  //   setStatusFilter(statusVal);
  //   loadTermFeeList({
  //     ...currentTermFeeListRequest,
  //     classStatus: statusVal,
  //   });
  // };

  const handleYearChange = (e: SelectChangeEvent) => {
    setAcademicYearFilter(parseInt(YearData.filter((item) => item.accademicId === e.target.value)[0].accademicId, 10));
    loadTermFeeList({ ...currentTermFeeListRequest, accademicId: parseInt(e.target.value, 10) });
  };

  const handleFeeTypeChange = (e: SelectChangeEvent) => {
    const feeTypeId = parseInt(e.target.value, 10);
    setFeeTypeFilter(feeTypeId);
    loadTermFeeList({ ...currentTermFeeListRequest, feeTypeId: parseInt(e.target.value, 10) });
  };

  const handleReset = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      setAcademicYearFilter(defaultYear);
      setFeeTypeFilter(0);
      setTermTitleFilter('');
      loadTermFeeList({
        adminId,
        accademicId: academicYearFilter,
        termTitle: termTitleFilter,
        feeTypeId: feeTypeFilter,
      });
    },
    [loadTermFeeList, adminId, defaultYear, academicYearFilter, termTitleFilter, feeTypeFilter]
  );

  const toggleDrawerOpen = () => {
    setDrawerOpen(true);
  };

  const toggleDrawerClose = () => setDrawerOpen(false);

  const handleSaveOrEdit = useCallback(
    async (values: CreateTermFeeSettingTitleDataType, mode: 'create' | 'edit') => {
      try {
        console.log('values::::----', values);
        if (mode === 'create') {
          console.log('values::::----', values);
          const response = await dispatch(createTermFeeSettingTitle(values)).unwrap();
          console.log('response::::----', response);
          if (response.id > 0) {
            toggleDrawerClose();
            loadTermFeeList({ ...currentTermFeeListRequest });
            const successMessage = (
              <SuccessMessage icon="" loop={false} jsonIcon={Success} message="Term title create successfully" />
            );
            await confirm(successMessage, '', { okLabel: 'Ok', showOnlyOk: true });
          } else if (response.id === 0) {
            toggleDrawerClose();
            const errorMessage = <ErrorMessage icon="" jsonIcon={ErrorIcon} message="Term title already created" />;
            await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
            toggleDrawerOpen();
          } else {
            const errorMessage = <ErrorMessage icon="" jsonIcon={ErrorIcon} message="Term title create failed" />;
            await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
          }
        } else {
          const { startDate, endDate } = values;

          const isDateFormatted = (date: string) => {
            const dateRegex = /^\d{2}\/\d{2}\/\d{4}$/;
            return dateRegex.test(date);
          };

          // Function to format dates
          const formatDate = (date: Dayjs | string | number | null): string | null => {
            if (date === null) return null;
            if (dayjs.isDayjs(date)) return date.format('DD/MM/YYYY');
            if (typeof date === 'string' && isDateFormatted(date)) return date; // Already formatted
            if (typeof date === 'string' || typeof date === 'number') return dayjs(date).format('DD/MM/YYYY');
            return null; // Fallback for any unexpected types
          };

          // Convert startDate and endDate to DD/MM/YYYY format
          const formattedStartDate = formatDate(startDate);
          const formattedEndDate = formatDate(endDate);
          const updateReq = {
            ...values,
            startDate: formattedStartDate,
            endDate: formattedEndDate,
            dbResult: 'string',
            updatedId: 0,
          };
          console.log('updateReq::::----', updateReq);
          const response = await dispatch(createTermFeeSettingTitle(updateReq)).unwrap();
          console.log('values::::----', values);

          if (response) {
            setDrawerOpen(false);
            const successMessage = <SuccessMessage message="Term Title updated successfully" />;
            await confirm(successMessage, 'Term Title Updated', { okLabel: 'Ok', showOnlyOk: true });

            loadTermFeeList({ ...currentTermFeeListRequest });
          } else {
            const errorMessage = <ErrorMessage loop={false} jsonIcon={ErrorIcon} message="Term Title updated failed" />;
            await confirm(errorMessage, 'Term Title Update', { okLabel: 'Ok', showOnlyOk: true });
          }
        }
      } catch {
        const errorMessage = (
          <ErrorMessage loop={false} jsonIcon={ErrorIcon} message="Something went wrong please try again" />
        );
        await confirm(errorMessage, 'Term Title Update', { okLabel: 'Ok', showOnlyOk: true });
      }
    },
    [dispatch, confirm, loadTermFeeList, currentTermFeeListRequest]
  );

  const handleUpdateAll = useCallback(async () => {
    try {
      const promises = editedRows.map(async (row) => {
        const { startDate, endDate } = row;
        // Convert startDate and endDate to DD/MM/YYYY format
        // const formattedStartDate = startDate ? new Date(startDate).toLocaleDateString('en-GB') : null;
        // const formattedEndDate = endDate ? new Date(endDate).toLocaleDateString('en-GB') : null;
        // Function to format dates
        const isDateFormatted = (date: string) => {
          const dateRegex = /^\d{2}\/\d{2}\/\d{4}$/;
          return dateRegex.test(date);
        };

        // Function to format dates
        const formatDate = (date: Dayjs | string | number | null): string | null => {
          if (date === null) return null;
          if (dayjs.isDayjs(date)) return date.format('DD/MM/YYYY');
          if (typeof date === 'string' && isDateFormatted(date)) return date; // Already formatted
          if (typeof date === 'string' || typeof date === 'number') return dayjs(date).format('DD/MM/YYYY');
          return null; // Fallback for any unexpected types
        };

        // Convert startDate and endDate to DD/MM/YYYY format
        const formattedStartDate = formatDate(startDate);
        const formattedEndDate = formatDate(endDate);
        const updateReq = {
          ...row,
          startDate: formattedStartDate,
          endDate: formattedEndDate,
          dbResult: 'string',
          updatedId: 0,
        };
        console.log('updateReq::::----', updateReq);
        const response = await dispatch(createTermFeeSettingTitle(updateReq)).unwrap();
        return response;
      });

      const responses = await Promise.all(promises);

      // Check if all updates were successful
      const isSuccess = responses.every((response) => response);

      if (isSuccess) {
        setSelectedRows([]);
        loadTermFeeList({ ...currentTermFeeListRequest });
        const successMessage = (
          <SuccessMessage loop={false} jsonIcon={Success} message="Term Titles updated successfully" />
        );
        await confirm(successMessage, 'Term Titles Updated', { okLabel: 'Ok', showOnlyOk: true });
      } else {
        const errorMessage = <ErrorMessage loop={false} jsonIcon={ErrorIcon} message="Term Titles updated failed" />;
        await confirm(errorMessage, 'Term Titles Update', { okLabel: 'Ok', showOnlyOk: true });
      }
    } catch (error) {
      const errorMessage = (
        <ErrorMessage loop={false} jsonIcon={ErrorIcon} message="Something went wrong please try again " />
      );
      await confirm(errorMessage, 'Term Titles Update', { okLabel: 'Ok', showOnlyOk: true });
      // Handle error
      console.error('Error updating term titles:', error);
    }
  }, [dispatch, confirm, loadTermFeeList, currentTermFeeListRequest, editedRows]);

  const handleAddTermFeeList = () => {
    setSelectedTermFeeDetail(DefaultTermFeeInfo);
    setDrawerOpen(true);
  };

  const handleEditTermFeeList = useCallback((termObj: CreateTermFeeSettingTitleDataType) => {
    setSelectedTermFeeDetail(termObj);
    setDrawerOpen(true);
  }, []);

  const handleDeleteTermFeeList = useCallback(
    async (termObj: CreateTermFeeSettingTitleDataType) => {
      const { termId } = termObj;
      const deleteConfirmMessage = (
        <DeleteMessage
          jsonIcon={deleteBin}
          message={
            <div>
              Are you sure you want to delete the Term Fee Row <br />
              &quot;{termObj.termTitle}&quot; ?
            </div>
          }
        />
      );
      if (await confirm(deleteConfirmMessage, 'Delete Row?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
        const sendReq = [{ adminId, accademicId: academicYearFilter, termId, dbResult: 'string', deletedId: 0 }];

        console.log('sendReq', sendReq);

        const deleteResponse = await dispatch(deleteTermFeeList(sendReq));
        console.log('deleteResponse::::----', deleteResponse);
        if (deleteResponse && Array.isArray(deleteResponse.payload)) {
          const results: DeleteTermFeeListType[] = deleteResponse.payload;
          const errorMessages = results.find((result) => result.dbResult === 'Failed');
          const successMessages = results.find((result) => result.dbResult === 'Success');
          // Reload only the specific message template with the deleted messageId

          if (!errorMessages) {
            const deleteSuccessMessage = (
              <SuccessMessage loop={false} jsonIcon={deleteSuccess} message="Delete successfully" />
            );
            await confirm(deleteSuccessMessage, 'Success', { okLabel: 'Ok', showOnlyOk: true });
          } else if (!successMessages) {
            const deleteErrorMessage = <DeleteMessage jsonIcon={errorIcon} message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          } else {
            const deleteErrorMessage = <DeleteMessage jsonIcon={errorIcon} message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          }
          // loadTermFeeList({ ...currentTermFeeListRequest });

          // setSnackBar(true);
          setTermFeeData((prevDetails) => prevDetails.filter((item) => item.termId !== termId));
        }
      }
    },
    [dispatch, academicYearFilter, adminId, confirm]
  );

  const handleDeleteMultiple = useCallback(async () => {
    const sendConfirmMessage = (
      <DeleteMessage jsonIcon={deleteBin} message={<div>Are you sure you want to delete all ?</div>} />
    );
    if (await confirm(sendConfirmMessage, 'Delete Term Fee?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
      // const deleteResponse = await dispatch(deleteBasicFeeList(sendReq));
      const sendRequests: DeleteTermFeeListType[] = await Promise.all(
        selectedRows?.map(async (row) => {
          const sendReq = {
            adminId,
            accademicId: academicYearFilter,
            termId: row.termId,
            dbResult: '',
            deletedId: 0,
          };
          return sendReq;
        }) || []
      );
      const deleteResponse = await dispatch(deleteTermFeeList(sendRequests));

      console.log('deleteResponse', deleteResponse);
      if (deleteResponse && Array.isArray(deleteResponse.payload)) {
        const results: DeleteTermFeeListType[] = deleteResponse.payload;
        const errorMessages = results.find((result) => result.dbResult === 'Failed');
        const successMessages = results.find((result) => result.dbResult === 'Success');

        if (!errorMessages) {
          loadTermFeeList({ ...currentTermFeeListRequest });
          const deleteSuccessMessage = (
            <SuccessMessage loop={false} jsonIcon={deleteSuccess} message="Delete All Successfully" />
          );
          await confirm(deleteSuccessMessage, 'Success', { okLabel: 'Ok', showOnlyOk: true });
        } else if (!successMessages) {
          const deleteErrorMessage = <DeleteMessage loop={false} jsonIcon={errorIcon} message="Delete Failed" />;
          await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
        } else {
          const deleteErrorMessage = (
            <DeleteMessage loop={false} jsonIcon={errorIcon} message="Something went wrong please try again" />
          );
          await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
        }
        setSelectedRows([]);
        setTermFeeData((prevDetails) =>
          prevDetails.filter((item: CreateTermFeeSettingTitleDataType) => !selectedRows.includes(item.termId))
        );
      }
    }
  }, [confirm, dispatch, selectedRows, adminId, academicYearFilter, loadTermFeeList, currentTermFeeListRequest]);

  // Pagination handlers
  const handlePageChange = useCallback(
    (event: any, newPage: any) => {
      setCurrentPage(newPage); // Update current page
    },
    [setCurrentPage]
  );

  const handleChangeRowsPerPage = useCallback(
    (event: any) => {
      setRowsPerPage(parseInt(event.target.value, 10)); // Update rows per page
      // Reset to first page when changing rows per page
    },
    [setRowsPerPage]
  );

  const paginatedData = termFeeData.slice(currentPage * rowsPerPage, currentPage * rowsPerPage + rowsPerPage);

  const pageProps = useMemo(
    () => ({
      rowsPerPageOptions: [10, 20, 30, 50, 100, 200, 300, 500],
      pageNumber: currentPage,
      pageSize: rowsPerPage,
      totalRecords: termFeeData.length,
      onPageChange: handlePageChange,
      onRowsPerPageChange: handleChangeRowsPerPage,
    }),
    [handleChangeRowsPerPage, handlePageChange, currentPage, rowsPerPage, termFeeData]
  );

  const termFeeListColumns: DataTableColumn<CreateTermFeeSettingTitleDataType>[] = useMemo(
    () => [
      {
        name: 'termId',
        headerLabel: 'Sl No',
        renderCell: (row) => {
          return (
            <Typography minWidth={50} fontSize={13} variant="subtitle1">
              {row.termId}
            </Typography>
          );
        },
        sortable: true,
      },
      {
        name: 'termTitle',
        headerLabel: 'Term Title',
        renderCell: (row) => {
          const isSelected = selectedRows.some((selectedRow) => selectedRow.termId === row.termId);
          return !isSelected ? (
            <Typography minWidth={200} fontSize={13} variant="subtitle1">
              {row.termTitle}
            </Typography>
          ) : (
            <TextField
              defaultValue={row.termTitle}
              inputProps={{
                style: {
                  // padding: '4px 10px',
                  fontSize: '13px',
                  // width: 110,
                },
              }}
              onChange={(e) => {
                const updatedRows = selectedRows.map((item) => {
                  if (item.termId === row.termId) {
                    return { ...item, termTitle: e.target.value };
                  }
                  return item;
                });

                setEditedRows((prev) => {
                  const exists = prev.find((item) => item.termId === row.termId);
                  if (exists) {
                    // Update the existing item
                    return prev.map((item) =>
                      item.termId === row.termId ? { ...item, termTitle: e.target.value } : item
                    );
                  }
                  // Add new edited item
                  return [...prev, { ...row, termTitle: e.target.value }];
                });
              }}
            />
          );
        },
      },
      {
        name: 'startDate',
        headerLabel: 'Start Date',
        renderCell: (row) => {
          const isSelected = selectedRows.some((selectedRow) => selectedRow.termId === row.termId);
          const formattedDate = dayjs(row.startDate, 'YYYY/MM/DD').format('DD/MM/YYYY');
          const startDateValue = dayjs(row.startDate, 'YYYY/MM/DD');
          return !isSelected ? (
            <Typography minWidth={150} fontSize={13} variant="subtitle1">
              {formattedDate}
            </Typography>
          ) : (
            <DatePickers
              name="startDate"
              value={startDateValue}
              onChange={(date) => {
                if (date) {
                  const updatedRows = selectedRows.map((item) => {
                    if (item.termId === row.termId) {
                      return { ...item, startDate: date.format('DD/MM/YYYY') };
                    }
                    return item;
                  });
                  setEditedRows((prev) => {
                    const exists = prev.find((item) => item.termId === row.termId);
                    if (exists) {
                      // Update the existing item
                      return prev.map((item) =>
                        item.termId === row.termId ? { ...item, startDate: date.format('DD/MM/YYYY') } : item
                      );
                    }
                    // Add new edited item
                    return [...prev, { ...row, startDate: date.format('DD/MM/YYYY') }];
                  });
                }
              }}
            />
          );
        },
      },
      {
        name: 'endDate',
        headerLabel: 'End Date',
        renderCell: (row) => {
          const isSelected = selectedRows.some((selectedRow) => selectedRow.termId === row.termId);
          const formattedDate = dayjs(row.endDate, 'YYYY/MM/DD').format('DD/MM/YYYY');
          const endDateValue = dayjs(row.endDate, 'YYYY/MM/DD');
          return !isSelected ? (
            <Typography minWidth={150} fontSize={13} variant="subtitle1">
              {formattedDate}
            </Typography>
          ) : (
            <DatePickers
              name="endDate"
              value={endDateValue}
              onChange={(date) => {
                if (date) {
                  const updatedRows = selectedRows.map((item) => {
                    if (item.termId === row.termId) {
                      return { ...item, endDate: date.format('DD/MM/YYYY') };
                    }
                    return item;
                  });
                  setEditedRows((prev) => {
                    const exists = prev.find((item) => item.termId === row.termId);
                    if (exists) {
                      // Update the existing item
                      return prev.map((item) =>
                        item.termId === row.termId ? { ...item, endDate: date.format('DD/MM/YYYY') } : item
                      );
                    }
                    // Add new edited item
                    return [...prev, { ...row, endDate: date.format('DD/MM/YYYY') }];
                  });
                }
              }}
            />
          );
        },
      },
      {
        name: 'feeTypeId',
        headerLabel: 'Type',
        sortable: true,
        renderCell: (row) => {
          const isSelected = selectedRows.some((selectedRow) => selectedRow.termId === row.termId);
          return !isSelected ? (
            <Typography minWidth={150} variant="subtitle1" fontSize={13}>
              {row.feeTypeId === 1 ? 'Accademic Fee' : row.feeTypeId === 2 ? 'Bus Fee' : ''}
            </Typography>
          ) : (
            <Select
              name="feeTypeId"
              defaultValue={row.feeTypeId}
              onChange={(e) => {
                const updatedRows = selectedRows.map((item) => {
                  if (item.termId === row.termId) {
                    return { ...item, feeTypeId: e.target.value };
                  }
                  return item;
                });
                setEditedRows((prev) => {
                  const exists = prev.find((item) => item.termId === row.termId);
                  if (exists) {
                    // Update the existing item
                    return prev.map((item) =>
                      item.termId === row.termId ? { ...item, feeTypeId: e.target.value } : item
                    );
                  }
                  // Add new edited item
                  return [...prev, { ...row, feeTypeId: e.target.value }];
                });
              }}
              sx={{ minWidth: 125 }}
            >
              <MenuItem sx={{ display: 'none' }} value={0}>
                Select type
              </MenuItem>
              {FEE_TYPE_ID_OPTIONS.map((opt) => (
                <MenuItem key={opt.id} value={opt.id}>
                  {opt.name}
                </MenuItem>
              ))}
            </Select>
          );
        },
      },
      {
        name: 'status',
        headerLabel: 'Status',
        sortable: true,
        renderCell: (row) => {
          const isSelected = selectedRows.some((selectedRow) => selectedRow.termId === row.termId);
          return !isSelected ? (
            <Chip
              size="small"
              label={row.status === 1 ? 'Active' : 'Inactive'}
              variant="filled"
              color={row.status === 1 ? 'success' : 'error'}
            />
          ) : (
            <RadioGroup
              row
              aria-labelledby="demo-row-radio-buttons-group-label"
              name="row-radio-buttons-group"
              value={selectedRows.find((item) => item.termId === row.termId)?.status ?? row.status}
              onChange={(e) => {
                const updatedRows = selectedRows.map((item) => {
                  if (item.termId === row.termId) {
                    return { ...item, status: Number(e.target.value) };
                  }
                  return item;
                });
                setEditedRows((prev) => {
                  const exists = prev.find((item) => item.termId === row.termId);
                  if (exists) {
                    // Update the existing item
                    return prev.map((item) =>
                      item.termId === row.termId ? { ...item, status: Number(e.target.value) } : item
                    );
                  }
                  // Add new edited item
                  return [...prev, { ...row, status: Number(e.target.value) }];
                });
              }}
            >
              <FormControlLabel control={<Radio size="small" value={1} />} label="Active" />
              <FormControlLabel control={<Radio size="small" value={0} />} label="Inactive" />
            </RadioGroup>
          );
        },
      },
      {
        name: 'actions',
        headerLabel: 'Actions',
        renderCell: (row) => {
          return (
            <Stack minWidth={80} direction="row" gap={1}>
              <IconButton size="small" onClick={() => handleEditTermFeeList(row)} sx={{ padding: 0.5 }}>
                <ModeEditIcon />
              </IconButton>
              <IconButton size="small" color="error" sx={{ padding: 0.5 }} onClick={() => handleDeleteTermFeeList(row)}>
                <DeleteIcon />
              </IconButton>
            </Stack>
          );
        },
      },
    ],
    [handleEditTermFeeList, selectedRows, handleDeleteTermFeeList]
  );
  const getRowKey = useCallback((row: any) => row.termId, []);

  return showCreateMultiple === 'multiple' ? (
    <CreateMultipleTermTitleForm onBackClick={() => setShowCreateMultiple('list')} />
  ) : (
    <Page title="List">
      <ListRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Box display="flex" pb={0.5} justifyContent="space-between">
            <Typography variant="h6" fontSize={17}>
              Term Fee List
            </Typography>
            <Stack direction="row" alignItems="center">
              {selectedRows.length > 0 && (
                <Tooltip title="Delete All">
                  <IconButton
                    sx={{ visibility: { xs: 'hidden', sm: 'visible' }, mr: 1 }}
                    aria-label="delete"
                    color="error"
                    onClick={handleDeleteMultiple}
                  >
                    <DeleteIcon />
                  </IconButton>
                </Tooltip>
              )}
              <Stack direction="row" alignItems="center" gap={1}>
                <Button sx={{ borderRadius: '20px' }} variant="outlined" size="small" onClick={handleAddTermFeeList}>
                  <MdAdd size="20px" /> Create
                </Button>
                <Button
                  sx={{ borderRadius: '20px' }}
                  variant="outlined"
                  size="small"
                  onClick={() => setShowCreateMultiple('multiple')}
                >
                  <MdAdd size="20px" /> Create Multiple
                </Button>
              </Stack>

              {showFilter === false ? (
                <Tooltip title="Search">
                  <IconButton
                    aria-label="delete"
                    color="primary"
                    sx={{ ml: 1 }}
                    onClick={() => setShowFilter((x) => !x)}
                  >
                    <SearchIcon />
                  </IconButton>
                </Tooltip>
              ) : (
                <IconButton aria-label="delete" color="primary" sx={{ ml: 1 }} onClick={() => setShowFilter((x) => !x)}>
                  <IoIosArrowUp />
                </IconButton>
              )}
            </Stack>
          </Box>

          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate onReset={handleReset}>
                <Grid pb={2} pt={1} container spacing={3} alignItems="end">
                  <Grid item xxl={2} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Select
                        labelId="academicYearFilter"
                        id="academicYearFilterSelect"
                        value={academicYearFilter.toString()}
                        onChange={handleYearChange}
                        placeholder="Select Year"
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Year
                        </MenuItem>
                        {YearData.map((opt) => (
                          <MenuItem key={opt.accademicId} value={opt.accademicId}>
                            {opt.accademicTime}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Select Type
                      </Typography>
                      <Select
                        labelId="feeTypeFilter"
                        id="feeTypeFilterSelect"
                        value={feeTypeFilter.toString()}
                        onChange={handleFeeTypeChange}
                        placeholder="Select Year"
                      >
                        <MenuItem sx={{ display: 'none' }} value={0}>
                          Select Type
                        </MenuItem>
                        {FEE_TYPE_ID_OPTIONS.map((opt) => (
                          <MenuItem key={opt.id} value={opt.id}>
                            {opt.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Term Title
                      </Typography>
                      <TextField
                        placeholder="Enter"
                        name="termTitleFilter"
                        value={termTitleFilter}
                        variant="outlined"
                        onChange={(e) => {
                          setTermTitleFilter(e.target.value);
                          loadTermFeeList({
                            ...currentTermFeeListRequest,
                            termTitle: e.target.value,
                          });
                        }}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto">
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button type="reset" variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>
            {feeTypeFilter !== 0 && (
              <Box mt={!showFilter ? 2 : 0} display="flex" justifyContent="end">
                <LoadingButton
                  loading={isSubmitting}
                  onClick={handleUpdateAll}
                  disabled={selectedRows.length === 0}
                  color="warning"
                  startIcon={<SaveIcon fontSize="small" />}
                  size="small"
                  variant="contained"
                  sx={{ py: 0.2, px: 1, fontSize: 12 }}
                >
                  Update All
                  {/* {switchToUpdateButton ? 'Update All ' : 'Save All'} */}
                </LoadingButton>
              </Box>
            )}
            {feeTypeFilter !== 0 ? (
              <Paper className="card-table-container" sx={{ marginTop: '12px' }}>
                <DTVirtuoso
                  tableStyles={{ minWidth: { xs: '700px', lg: '1200px', xl: '100% ' } }}
                  showHorizontalScroll
                  ShowCheckBox
                  setSelectedRows={setSelectedRows}
                  selectedRows={selectedRows}
                  RowSelected
                  columns={termFeeListColumns}
                  data={paginatedData}
                  getRowKey={getRowKey}
                  fetchStatus={termFeeListStatus}
                  PaginationProps={pageProps}
                  allowPagination
                />
              </Paper>
            ) : (
              <Paper
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  border: 1,
                  borderColor: theme.palette.grey[300],
                  width: '100%',
                  height: { xs: 'calc(100vh - 400px)', sm: 'calc(100vh - 400px)', lg: 'calc(100vh - 300px)' },
                }}
              >
                <Stack direction="column" alignItems="center">
                  <img src={NoData} width="150px" alt="" />
                  <Typography variant="subtitle2" mt={2} color="GrayText">
                    No data found !
                  </Typography>
                </Stack>
              </Paper>
            )}
          </div>
        </Card>
      </ListRoot>
      <TemporaryDrawer
        onClose={toggleDrawerClose}
        state={drawerOpen}
        Title={selectedTermFeeDetail === DefaultTermFeeInfo ? 'Create Term Title' : 'Edit Term Title'}
        DrawerContent={
          <TermFeeListCreateEditForm
            termFeeDetails={selectedTermFeeDetail}
            onSave={handleSaveOrEdit}
            onCancel={toggleDrawerClose}
          />
        }
      />
    </Page>
  );
}

export default TermFeeList;
