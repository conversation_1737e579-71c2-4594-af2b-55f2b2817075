import Lottie from 'lottie-react';
import { MessagesTypeProps } from '@/types/Common';
import { Box, Stack, Typography } from '@mui/material';

export const ErrorMessage = ({ message, icon, jsonIcon, loop }: MessagesTypeProps) => {
  return (
    <Stack sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
      <Box
        sx={{
          // backgroundColor: '#F4F6F8',
          width: 80,
          height: 80,
          borderRadius: '50%',
          display: 'flex',
          justifyContent: 'center',
        }}
      >
        {icon ? (
          <img style={{ borderRadius: '10%' }} src={icon} alt={icon} />
        ) : (
          <Lottie animationData={jsonIcon} loop={loop} style={{ width: '200px' }} />
        )}
      </Box>
      <Typography fontWeight={550} textAlign="center" color="secondary" pt={2}>
        {message}
      </Typography>
    </Stack>
  );
};
