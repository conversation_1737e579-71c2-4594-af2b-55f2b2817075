// /* eslint-disable no-nested-ternary */
// /* eslint-disable jsx-a11y/alt-text */
// import React, { ChangeEvent, useCallback, useMemo, useState } from 'react';
// import Page from '@/components/shared/Page';
// import {
//   Box,
//   Divider,
//   Grid,
//   Paper,
//   Stack,
//   Button,
//   Typography,
//   Card,
//   Tooltip,
//   IconButton,
//   Collapse,
//   FormControl,
//   SelectChangeEvent,
//   MenuItem,
//   Select,
//   useTheme,
//   Table,
//   TableRow,
//   TableBody,
// } from '@mui/material';
// import styled from 'styled-components';
// import SearchIcon from '@mui/icons-material/Search';
// import Lottie from 'lottie-react';
// import successIcon from '@/assets/MessageIcons/success.json';
// import { MdAdd } from 'react-icons/md';
// import ErrorIcon from '@/assets/ManageFee/ErrorIcon.json';
// import Success from '@/assets/ManageFee/Success.json';
// import deleteBin from '@/assets/ManageFee/deleteBin.json';
// import deleteSuccess from '@/assets/ManageFee/deleteSuccess.json';
// import errorIcon from '@/assets/ManageFee/Error.json';
// import DeleteIcon from '@mui/icons-material/Delete';
// import ModeEditIcon from '@mui/icons-material/ModeEdit';
// import { ROLL_OPTIONS } from '@/config/Selection';
// import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
// import typography from '@/theme/typography';
// import useSettings from '@/hooks/useSettings';
// import { useAppDispatch } from '@/hooks/useAppDispatch';
// import { useConfirm } from '@/components/shared/Popup/Confirmation';
// import useAuth from '@/hooks/useAuth';
// import {
//   CTSAllocationMappedInfo,
//   CTSAllocationMappedResponseInfo,
//   CTSAllocationMappingFilter,
//   CTSAllocationTeacherWiseMappingFilter,
// } from '@/types/StaffManagement';
// import {
//   AddCTSAllocationMap,
//   fetchCTSFilter,
//   fetchCTSMapping,
//   fetchCTSTeacherWiseMapiing,
// } from '@/store/StaffMangement/StaffMangement/staffMangement.thunks';
// import {
//   getCTSClassList,
//   getCTSDetailsListPageInfo,
//   getCTSFilterListStatus,
//   getCTSTeacherWiseMappingListData,
//   getCTSTeacherWiseMappingListStatus,
//   getCTSSortColumn,
//   getCTSSortDirection,
//   getCTSStaffList,
//   getCTSStaffListData,
//   getCTSTeacherWiseClassListData,
//   getCTSTeacherWiseSubjectListData,
//   getCTSYearList,
//   getStaffSubmitting,
// } from '@/config/storeSelectors';
// import { useAppSelector } from '@/hooks/useAppSelector';
// import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
// import { ErrorMessage } from '@/components/shared/Popup/ErrorMessage';
// import LoadingButton from '@mui/lab/LoadingButton';
// import { TextField } from '@mui/material';
// import { Autocomplete } from '@mui/material';
// import { Checkbox } from '@mui/material';
// import BackButton from '@/components/shared/BackButton';
// import { useNavigate } from 'react-router-dom';
// import { TableContainer } from '@mui/material';
// import { TableHead } from '@mui/material';
// import { TableCell } from '@mui/material';

// const CtsAllocationTeacherWiseRoot = styled.div`
//   background-color: ${(props) =>
//     props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
//   padding: 1rem;

//   .Card {
//     display: flex;
//     flex-direction: column;
//     height: calc(100vh - 160px);
//     @media screen and (max-width: 576px) {
//       height: 100%;
//     }

//     .card-main-body {
//       display: flex;
//       flex-direction: column;
//       max-height: calc(100% - 100px);
//       flex-grow: 1;

//       .card-table-container {
//         flex-grow: 1;
//         width: 100%;
//         height: 100%;
//         display: flex;
//         flex-direction: column;
//         border: 1px solid ${(props) => props.theme.palette.grey[200]};
//         overflow: hidden;

//         .MuiTableContainer-root {
//           height: 100%;
//         }

//         .MuiTablePagination-root {
//           flex-grow: 1;
//           flex-shrink: 0;
//         }
//         /* .MuiTableCell-root:first-child {
//           padding: 5px;
//         } */
//       }
//     }
//   }
// `;

// type CtsPropsTypes = {
//   onBackClick?: () => void;
// };

// function CtsAllocationTeacherWise({ onBackClick }: CtsPropsTypes) {
//   const theme = useTheme();
//   const { themeMode } = useSettings();
//   const isLight = themeMode === 'light';
//   const dispatch = useAppDispatch();
//   const { confirm } = useConfirm();
//   const { user } = useAuth();
//   const adminId: number = user ? user.accountId : 0;

//   const [showFilter, setShowFilter] = useState(true);
//   const [academicYearFilter, setAcademicYearFilter] = useState(0);
//   const [staffFilter, setStaffFilter] = useState(0);
//   const [selectedRows, setSelectedRows] = useState<Record<number, boolean>>({});
//   const [individualLoadingMap, setIndividualLoadingMap] = useState<Record<string, boolean>>({});
//   const sortColumn = useAppSelector(getCTSSortColumn);
//   const sortDirection = useAppSelector(getCTSSortDirection);
//   const paginationInfo = useAppSelector(getCTSDetailsListPageInfo);
//   const CTSYearList = useAppSelector(getCTSYearList);
//   const CTSStaffListData = useAppSelector(getCTSStaffListData);
//   const CTSStaffList = useAppSelector(getCTSStaffList);
//   const CTSFilterListStatus = useAppSelector(getCTSFilterListStatus);
//   const CTSTeacherWiseMappingListStatus = useAppSelector(getCTSTeacherWiseMappingListStatus);
//   const CTSTeacherWiseMappingListData = useAppSelector(getCTSTeacherWiseMappingListData);
//   const CTSTeacherWiseSubjectListData = useAppSelector(getCTSTeacherWiseSubjectListData);
//   const CTSTeacherWiseClassListData = useAppSelector(getCTSTeacherWiseClassListData);
//   // const CTSStaffListData = useAppSelector(getCTSStaffListData);
//   const isSubmitting = useAppSelector(getStaffSubmitting);
//   const [dbStatus, setDbStatus] = useState<'Success' | 'Exist' | 'Failed' | ''>('');
//   // const [editedRows, setEditedRows] = useState<CTSAllocationMappedInfo[]>([]);
//   const [rowBackgroundColors, setRowBackgroundColors] = useState<Record<number, string>>({});
//   const [editTableRow, setEditTableRow] = useState<Record<number, boolean>>({});
//   const [rowSelect, setRowSelect] = useState<Record<number, boolean>>({});
//   const [editedRows, setEditedRows] = useState<CTSAllocationMappedInfo[]>([]);
//   const [isAllSelected, setIsAllSelected] = useState<boolean>(false);
//   const navigate = useNavigate();

//   const { pagenumber, pagesize, totalrecords } = paginationInfo;

//   const currentCTSTeacherWiseListRequest: CTSAllocationTeacherWiseMappingFilter = React.useMemo(
//     () => ({
//       adminId,
//       academicId: academicYearFilter,
//       staffId: staffFilter,
//     }),
//     [adminId, academicYearFilter, staffFilter]
//   );

//   const loadCTSTeacherWiseList = React.useCallback(
//     async (request: CTSAllocationTeacherWiseMappingFilter) => {
//       try {
//         const data = await dispatch(fetchCTSTeacherWiseMapiing(request));
//         console.log('CTSStaffListData', CTSStaffListData);
//       } catch (error) {
//         console.error('Error loading list:', error);
//       }
//     },
//     [dispatch, CTSStaffListData]
//   );

//   React.useEffect(() => {
//     // if (YearStatus === 'idle') {
//     //   setAcademicYearFilter(defualtYear);
//     // }
//     if (CTSTeacherWiseMappingListStatus === 'idle') {
//       loadCTSTeacherWiseList(currentCTSTeacherWiseListRequest);
//     }
//     if (CTSFilterListStatus === 'idle') {
//       const CtsFilerDatas = dispatch(fetchCTSFilter(adminId));
//       console.log('CtsFilerDatas', CtsFilerDatas);
//     }
//   }, [
//     dispatch,
//     adminId,
//     CTSFilterListStatus,
//     CTSTeacherWiseMappingListStatus,
//     loadCTSTeacherWiseList,
//     currentCTSTeacherWiseListRequest,
//   ]);
//   const [mappedLists, setMappedLists] = useState<CTSAllocationMappedInfo[]>([
//     {
//       subjectId: 0,
//       subjectName: '',
//       cteacherId: 0,
//       staffId: 0,
//       staffRole: 0,
//     },
//   ]);
//   const handleAddRow = () => {
//     const newRow: CTSAllocationMappedInfo = {
//       subjectId: 0,
//       subjectName: '',
//       cteacherId: 0,
//       staffId: 0,
//       staffRole: 0,
//     };
//     setMappedLists([...mappedLists, newRow]);
//     setRowBackgroundColors((prev) => ({
//       ...prev,
//       [newRow.subjectId]: isLight ? '#f0fdf4' : theme.palette.grey[900],
//     }));
//     setIndividualSaveButtonEnabled((prev) => ({ ...prev, [newRow.subjectId]: true }));
//     setIndividualRemoveRowButton((prev) => ({ ...prev, [newRow.subjectId]: true }));
//     setEditTableRow((prev) => ({ ...prev, [newRow.subjectId]: true }));
//   };

//   const handleYearChange = (e: SelectChangeEvent) => {
//     const selectedAcademicId = e.target.value;
//     setAcademicYearFilter(parseInt(selectedAcademicId, 10));

//     loadCTSTeacherWiseList({
//       ...currentCTSTeacherWiseListRequest,
//       academicId: parseInt(selectedAcademicId, 10),
//     });
//   };

//   const handleStaffChange = (e: SelectChangeEvent) => {
//     const selectedStaff = parseInt(e.target.value, 10);
//     if (selectedStaff) {
//       setStaffFilter(selectedStaff);
//     }
//     loadCTSTeacherWiseList({
//       ...currentCTSTeacherWiseListRequest,
//       staffId: selectedStaff || -1,
//     });
//   };

//   const handleReset = React.useCallback(
//     (e: any) => {
//       e.preventDefault();
//       setAcademicYearFilter(0);
//       setStaffFilter(0);
//       // setEditedRows([]);
//       loadCTSTeacherWiseList({
//         adminId,
//         academicId: 0,
//         staffId: 0,
//       });
//     },
//     [loadCTSTeacherWiseList, adminId]
//   );

//   // const handleIndividualMapCTS = useCallback(
//   //   async (row: CTSAllocationMappedInfo) => {
//   //     try {
//   //       // Set loading state for the current subject
//   //       setIndividualLoadingMap((prevMap) => ({ ...prevMap, [row.subjectId]: true }));

//   //       // Map over selected rows and create promises
//   //       const promises = editedRows.map(async (r) => {
//   //         const req = [
//   //           {
//   //             ...row,
//   //             staffId: r.staffId,
//   //             staffRole: r.staffRole,
//   //             adminId,
//   //             academicId: academicYearFilter,
//   //             classId: staffFilter,
//   //             dbResult: 'string',
//   //             id: 0,
//   //           },
//   //         ];

//   //         console.log('Request:', req);

//   //         try {
//   //           const response = await dispatch(AddCTSAllocationMap(req)).unwrap();
//   //           console.log('Response:', response);

//   //           // Process each object in the response
//   //           await Promise.all(
//   //             response.map(async (resObj: CTSAllocationMappedResponseInfo) => {
//   //               console.log('Processing dbResult:', resObj.dbResult);
//   //               setDbStatus((prevMap) => ({ ...prevMap, [row.subjectId]: resObj.dbResult }));

//   //               if (resObj.dbResult === 'Success') {
//   //                 loadCTSTeacherWiseList({ ...currentCTSTeacherWiseListRequest });
//   //               }
//   //             })
//   //           );

//   //           return response;
//   //         } catch (err) {
//   //           console.error('Error processing request:', req, err);
//   //           return [{ dbResult: 'Failed' }]; // Return a fallback response
//   //         }
//   //       });

//   //       // Wait for all promises to complete
//   //       const allResponses = await Promise.all(promises);
//   //       setEditedRows([]);
//   //       console.log('All Responses:', allResponses.flat());
//   //     } catch (error) {
//   //       const errorMessage = (
//   //         <ErrorMessage loop={false} jsonIcon={ErrorIcon} message="Something went wrong, please try again" />
//   //       );
//   //       await confirm(errorMessage, 'CTS Map', { okLabel: 'Ok', showOnlyOk: true });
//   //       console.error('Error mapping CTS:', error);
//   //     } finally {
//   //       // Reset statuses
//   //       setTimeout(() => {
//   //         setDbStatus((prevMap) => ({ ...prevMap, [row.subjectId]: '' }));
//   //       }, 5000);
//   //       setIndividualLoadingMap((prevMap) => ({ ...prevMap, [row.subjectId]: false }));
//   //     }
//   //   },
//   //   [
//   //     dispatch,
//   //     academicYearFilter,
//   //     confirm,
//   //     loadCTSTeacherWiseList,
//   //     adminId,
//   //     currentCTSTeacherWiseListRequest,
//   //     staffFilter,
//   //     editedRows,
//   //   ]
//   // );
//   const getRowKey = useCallback((row: CTSAllocationMappedInfo) => row.subjectId, []);

//   const SelectedColumns: DataTableColumn<CTSAllocationMappedInfo>[] = useMemo(
//     () => [
//       {
//         name: 'subjectId',
//         headerLabel: 'Sl.No',
//         dataKey: 'subjectId',
//       },
//       {
//         name: 'subjectName',
//         headerLabel: 'Subject',
//         dataKey: 'subjectName',
//       },
//       {
//         name: 'staff',
//         headerLabel: 'Teacher',
//         renderCell: (row) => {
//           const currentStaff = CTSStaffListData.find((staff) => staff.staffId === row.staffId);
//           const staffName = currentStaff?.staffName;

//           return <Typography variant="subtitle2">{staffName}</Typography>;
//         },
//       },
//       {
//         name: 'staffRole',
//         headerLabel: 'Role',
//         width: '200px',
//         renderCell: (row) => {
//           const currentStaff = ROLL_OPTIONS.find((staff) => staff.id === row.staffRole);
//           const staffName = currentStaff?.name;

//           return <Typography variant="subtitle2">{staffName}</Typography>;
//         },
//       },
//     ],
//     [CTSStaffListData]
//   );

//   // const handleMapCTS = useCallback(async () => {
//   //   try {
//   //     const ConfirmMessage = (
//   //       <SuccessMessage
//   //         message={
//   //           <Card sx={{ my: 1, boxShadow: 0, width: 500, border: 1, borderColor: theme.palette.grey[300] }}>
//   //             <DataTable columns={SelectedColumns} data={editedRows} getRowKey={getRowKey} fetchStatus="success" />
//   //           </Card>
//   //         }
//   //       />
//   //     );
//   //     if (await confirm(ConfirmMessage, 'Map Rows?', { okLabel: 'Confirm', cancelLabel: 'Cancel' })) {
//   //       const promises = editedRows.map(async (row) => {
//   //         // Convert startDate and endDate to DD/MM/YYYY format
//   //         const req = [
//   //           {
//   //             ...row,
//   //             adminId,
//   //             academicId: academicYearFilter,
//   //             classId: staffFilter,
//   //             dbResult: 'string',
//   //             id: 0,
//   //           },
//   //         ];
//   //         console.log('updateReq::::----', req);
//   //         const response = await dispatch(AddCTSAllocationMap(req)).unwrap();
//   //         console.log('response::::----', response);

//   //         return response;
//   //       });

//   //       const responses = await Promise.all(promises);

//   //       // Check if all updates were successful
//   //       const isSuccess = responses.every((response) => response);
//   //       const isExist = responses.every((response) => response.dbResult === 'Exist');
//   //       // const isSuccess = responses.every((response) => response.dbResult === 'Success');
//   //       // const isExist = responses.every((response) => response.dbResult === 'Exist');
//   //       console.log('isSuccess::::----', isSuccess);
//   //       console.log('promises::::----', promises);

//   //       if (isSuccess) {
//   //         setEditedRows([]);
//   //         loadCTSTeacherWiseList({ ...currentCTSTeacherWiseListRequest });
//   //         const successMessage = <SuccessMessage loop={false} jsonIcon={Success} message="CTS Map successfully" />;
//   //         await confirm(successMessage, 'CTS Map', { okLabel: 'Ok', showOnlyOk: true });
//   //       } else if (isExist) {
//   //         const errorMessage = <ErrorMessage loop={false} jsonIcon={ErrorIcon} message="CTS already created" />;
//   //         await confirm(errorMessage, 'CTS Map', { okLabel: 'Ok', showOnlyOk: true });
//   //       } else {
//   //         const errorMessage = <ErrorMessage loop={false} jsonIcon={ErrorIcon} message="CTS mapped failed" />;
//   //         await confirm(errorMessage, 'CTS Map', { okLabel: 'Ok', showOnlyOk: true });
//   //       }
//   //     }
//   //   } catch (error) {
//   //     const errorMessage = (
//   //       <ErrorMessage loop={false} jsonIcon={ErrorIcon} message="Something went wrong please try again " />
//   //     );
//   //     await confirm(errorMessage, 'CTS Map', { okLabel: 'Ok', showOnlyOk: true });
//   //     // Handle error
//   //     console.error('Error mapping cts :', error);
//   //   }
//   // }, [
//   //   dispatch,
//   //   academicYearFilter,
//   //   confirm,
//   //   loadCTSTeacherWiseList,
//   //   adminId,
//   //   currentCTSTeacherWiseListRequest,
//   //   staffFilter,
//   //   editedRows,
//   //   SelectedColumns,
//   //   getRowKey,
//   //   theme,
//   // ]);

//   const handleEditCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>, subjectId: number) => {
//     const { checked } = event.target;

//     if (checked) {
//       setRowSelect((prev) => ({ ...prev, [subjectId]: true }));
//       console.log('rowSelect::::----', rowSelect);
//     } else {
//       setRowSelect((prev) => ({ ...prev, [subjectId]: false }));
//     }
//   };

//   const handleSelectAll = (event: ChangeEvent<HTMLInputElement>) => {
//     const isChecked = event.target.checked;
//     setIsAllSelected(isChecked);
//     setRowSelect(mappedLists.reduce((acc, item) => ({ ...acc, [item.subjectId]: isChecked }), {}));
//   };

//   // const CTSAllocationMappingColumns: DataTableColumn<CTSAllocationMappedInfo>[] = useMemo(
//   //   () => [
//   //     {
//   //       name: 'checkBox',
//   //       renderHeader: () => {
//   //         return (
//   //           <Checkbox
//   //             disabled={isSubmitting}
//   //             color="primary"
//   //             onChange={handleRowAllClick}
//   //             indeterminate={
//   //               CTSTeacherWiseMappingListData.length > 0 &&
//   //               Object.values(selectedRows).some((checked) => checked) &&
//   //               !isAllSelected
//   //             }
//   //             // indeterminate={
//   //             //   Object.keys(selectedRows).length > 0 && Object.keys(selectedRows).length < qucikUpdateStudentListData.length
//   //             // }
//   //             checked={isAllSelected}
//   //           />
//   //         );
//   //       },
//   //       renderCell: (row) => {
//   //         const isRowSelected = selectedRows[row.subjectId] || false;
//   //         return (
//   //           <Checkbox
//   //             disabled={isSubmitting}
//   //             color="primary"
//   //             checked={isRowSelected}
//   //             onChange={(e) => {
//   //               handleRowClick(e, row);
//   //             }}
//   //             inputProps={{ 'aria-labelledby': `checkbox-${row}` }}
//   //           />
//   //         );
//   //       },
//   //     },
//   //     {
//   //       name: 'subjectId',
//   //       headerLabel: 'Sl.No',
//   //       dataKey: 'subjectId',
//   //     },
//   //     {
//   //       name: 'subjectName',
//   //       headerLabel: 'Subject',
//   //       dataKey: 'subjectName',
//   //       sortable: true,
//   //     },
//   //     {
//   //       name: 'staff',
//   //       headerLabel: 'Teacher',
//   //       width: '200px',
//   //       renderCell: (row) => {
//   //         const editedRow = editedRows.find((r) => r.subjectId === row.subjectId);
//   //         // const isSelected = Boolean(selectedRow);
//   //         const isSelected = selectedRows[row.subjectId] || false;
//   //         const isSelectedStaffId = editedRow?.staffId ?? -1;
//   //         const currentStaff = CTSStaffListData.find((staff) => staff.staffId === row.staffId);
//   //         const staffName = currentStaff?.staffName;

//   //         if (!isSelected && staffName) {
//   //           return <Typography variant="subtitle2">{staffName}</Typography>;
//   //         }

//   //         return (
//   //           <Autocomplete
//   //             value={isSelected ? CTSStaffListData.find((opt) => opt.staffId === isSelectedStaffId) || null : null}
//   //             options={CTSStaffListData}
//   //             getOptionLabel={(option) => option.staffName || ''}
//   //             isOptionEqualToValue={(option, value) => option.staffId === value.staffId}
//   //             disabled={!isSelected}
//   //             onChange={(event, newValue) => {
//   //               const selectedStaffId = newValue ? newValue.staffId : -1;
//   //               setEditedRows((prevRows) =>
//   //                 prevRows.map((item) =>
//   //                   item.subjectId === row.subjectId ? { ...item, staffId: selectedStaffId } : item
//   //                 )
//   //               );
//   //             }}
//   //             renderInput={(params) => (
//   //               <TextField
//   //                 {...params}
//   //                 placeholder="Select Teacher"
//   //                 sx={{
//   //                   '& .MuiInputBase-root': {
//   //                     padding: '6px', // Adjust padding for the input field
//   //                     backgroundColor: !isSelected
//   //                       ? isLight
//   //                         ? theme.palette.grey[100]
//   //                         : theme.palette.grey[500]
//   //                       : 'transparent',
//   //                   },
//   //                   '& .MuiAutocomplete-endAdornment': {
//   //                     right: '4px', // Adjust the position of the dropdown icon
//   //                   },
//   //                 }}
//   //               />
//   //             )}
//   //             ListboxProps={{ style: { maxHeight: '250px' } }} // Adjust dropdown height
//   //           />
//   //         );
//   //       },
//   //     },
//   //     {
//   //       name: 'staffRole',
//   //       headerLabel: 'Role',
//   //       width: '200px',
//   //       renderCell: (row) => {
//   //         const editedRow = editedRows.find((r) => r.subjectId === row.subjectId);
//   //         // const isSelected = Boolean(selectedRow);
//   //         const isSelected = selectedRows[row.subjectId] || false;
//   //         const isSelectedStaffRole = editedRow?.staffRole ?? -1;
//   //         const currentStaff = ROLL_OPTIONS.find((staff) => staff.id === row.staffRole);
//   //         const staffName = currentStaff?.name;

//   //         // Check if staffRole id 1 exists in CTSTeacherWiseMappingListData
//   //         const isRoleOnePresent = CTSTeacherWiseMappingListData.some((data) => data.staffRole === 1);

//   //         // Modify ROLL_OPTIONS to include disabled property
//   //         const optionsWithDisabled = ROLL_OPTIONS.map((option) => ({
//   //           ...option,
//   //           disabled: option.id === 1 && isRoleOnePresent,
//   //         }));

//   //         if (!isSelected && staffName) {
//   //           return <Typography variant="subtitle2">{staffName}</Typography>;
//   //         }

//   //         return (
//   //           <Autocomplete
//   //             value={isSelected ? ROLL_OPTIONS.find((opt) => opt.id === isSelectedStaffRole) || null : null}
//   //             options={optionsWithDisabled}
//   //             getOptionLabel={(option) => option.name || ''}
//   //             isOptionEqualToValue={(option, value) => option.id === value.id}
//   //             disabled={!isSelected}
//   //             onChange={(event, newValue) => {
//   //               const selected = newValue ? newValue.id : -1;
//   //               setEditedRows((prevRows) =>
//   //                 prevRows.map((item) =>
//   //                   item.subjectId === row.subjectId ? { ...item, staffRole: selected } : item
//   //                 )
//   //               );
//   //             }}
//   //             renderInput={(params) => (
//   //               <TextField
//   //                 {...params}
//   //                 placeholder="Select Role"
//   //                 sx={{
//   //                   '& .MuiInputBase-root': {
//   //                     padding: '6px', // Adjust padding for the input field
//   //                     backgroundColor: !isSelected
//   //                       ? isLight
//   //                         ? theme.palette.grey[100]
//   //                         : theme.palette.grey[500]
//   //                       : 'transparent', // Change background when disabled
//   //                   },
//   //                   '& .MuiAutocomplete-endAdornment': {
//   //                     right: '4px', // Adjust the position of the dropdown icon
//   //                   },
//   //                 }}
//   //               />
//   //             )}
//   //             ListboxProps={{
//   //               style: { maxHeight: '250px' }, // Adjust dropdown height
//   //             }}
//   //             renderOption={(props, option) => (
//   //               <li {...props} aria-disabled={option.disabled} style={{ opacity: option.disabled ? 0.5 : 1 }}>
//   //                 {option.name}
//   //               </li>
//   //             )}
//   //           />
//   //         );
//   //       },
//   //     },

//   //     {
//   //       name: 'actions',
//   //       headerLabel: 'Actions',
//   //       renderCell: (row) => {
//   //         const disabledDelete = row.cteacherId > 0;
//   //         // const editedRow = editedRows.find((r) => r.subjectId === row.subjectId);
//   //         // const isSelected = Boolean(selectedRow);
//   //         const isSelected = selectedRows[row.subjectId] || false;
//   //         return (
//   //           <Stack direction="row" gap={1}>
//   //             <IconButton disabled={!disabledDelete} size="small" color="error" sx={{ padding: 0.5 }}>
//   //               <DeleteIcon />
//   //             </IconButton>
//   //             {dbStatus[row.subjectId] === 'Success' ? (
//   //               <Stack>
//   //                 <Lottie animationData={successIcon} loop={false} style={{ width: '35px' }} />
//   //               </Stack>
//   //             ) : dbStatus[row.subjectId] === 'Exist' ? (
//   //               <Stack>
//   //                 <Lottie animationData={errorIcon} loop={false} style={{ width: '30px' }} />
//   //               </Stack>
//   //             ) : dbStatus[row.subjectId] === 'Failed' ? (
//   //               <Stack>
//   //                 <Lottie animationData={errorIcon} loop={false} style={{ width: '30px' }} />
//   //               </Stack>
//   //             ) : (
//   //               <LoadingButton
//   //                 disabled={!isSelected}
//   //                 loading={individualLoadingMap[row.subjectId]}
//   //                 variant="contained"
//   //                 size="small"
//   //                 sx={{ p: 0.5 }}
//   //                 onClick={() => handleIndividualMapCTS(row)}
//   //               >
//   //                 {individualLoadingMap[row.subjectId] ? '' : 'Map'}
//   //               </LoadingButton>
//   //             )}
//   //           </Stack>
//   //         );
//   //       },
//   //     },
//   //   ],
//   //   [
//   //     selectedRows,
//   //     dbStatus,
//   //     CTSStaffListData,
//   //     isLight,
//   //     handleIndividualMapCTS,
//   //     CTSTeacherWiseMappingListData,
//   //     individualLoadingMap,
//   //     editedRows,
//   //     theme,
//   //     handleRowAllClick,
//   //     isAllSelected,
//   //     isSubmitting,
//   //   ]
//   // );

//   return (
//     <Page title="CTS Allocation ClassWise">
//       <CtsAllocationTeacherWiseRoot>
//         <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
//           <Stack pb={1} direction="row" justifyContent="space-between" alignItems="center" flexWrap="wrap">
//             <Stack direction="row" alignItems="center" justifyContent="space-between" flex={1} whiteSpace="nowrap">
//               <BackButton
//                 onBackClick={() => {
//                   onBackClick();
//                   navigate('/staff-management/allocation-list');
//                 }}
//               />
//               <Typography variant="h6" fontSize={17} width="100%">
//                 CTS Allocation Teacher Wise
//               </Typography>
//               <Tooltip title="Search">
//                 <IconButton aria-label="" color="primary" sx={{ mr: 1 }} onClick={() => setShowFilter((x) => !x)}>
//                   <SearchIcon />
//                 </IconButton>
//               </Tooltip>
//             </Stack>
//           </Stack>
//           <Stack>
//             <Divider />
//           </Stack>
//           <div className="card-main-body">
//             <Collapse in={showFilter}>
//               <form noValidate onReset={handleReset}>
//                 <Grid pb={5} pt={1} container spacing={2}>
//                   <Grid item xxl={2} xl={2.5} lg={2.5} md={4} sm={4} xs={12}>
//                     <FormControl fullWidth>
//                       <Typography variant="subtitle1" fontSize={12} color="GrayText">
//                         Academic Year
//                       </Typography>
//                       <Select
//                         labelId="academicYearFilter"
//                         id="academicYearFilterSelect"
//                         value={academicYearFilter?.toString()}
//                         onChange={handleYearChange}
//                         placeholder="Select Year"
//                       >
//                         <MenuItem value={0}>Select</MenuItem>
//                         {CTSYearList.map((opt) => (
//                           <MenuItem key={opt.academicId} value={opt.academicId}>
//                             {opt.academicTime}
//                           </MenuItem>
//                         ))}
//                       </Select>
//                     </FormControl>
//                   </Grid>
//                   <Grid item xxl={2} xl={2.5} lg={2.5} md={4} sm={4} xs={12}>
//                     <FormControl fullWidth>
//                       <Typography variant="subtitle1" fontSize={12} color="GrayText">
//                         Select Teacher
//                       </Typography>
//                       <Select
//                         labelId="staffFilter"
//                         id="staffFilterSelect"
//                         value={staffFilter?.toString()}
//                         onChange={handleStaffChange}
//                         placeholder="Select Class"
//                         MenuProps={{
//                           PaperProps: {
//                             style: {
//                               maxHeight: '250px', // Adjust the value to your desired height
//                             },
//                           },
//                         }}
//                       >
//                         <MenuItem value={0}>Select</MenuItem>
//                         {CTSStaffList.map((opt) => (
//                           <MenuItem key={opt.staffId} value={opt.staffId}>
//                             {opt.staffName}
//                           </MenuItem>
//                         ))}
//                       </Select>
//                     </FormControl>
//                   </Grid>
//                   <Grid item lg={1} xs={12}>
//                     <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
//                       <Button type="reset" variant="contained" color="secondary" fullWidth>
//                         Reset
//                       </Button>
//                     </Stack>
//                   </Grid>
//                 </Grid>
//               </form>
//             </Collapse>
//             <Box display="flex" sx={{ justifyContent: 'end', py: 2 }}>
//               <Button
//                 size="small"
//                 type="button"
//                 color="secondary"
//                 variant="contained"
//                 // startIcon={<AddIcon />}
//                 onClick={handleAddRow}
//                 sx={{ py: 0, px: 1 }}
//               >
//                 Add Row
//               </Button>
//             </Box>
//             <Paper className="card-table-container">
//               <TableContainer
//                 sx={{
//                   '&::-webkit-scrollbar': {
//                     height: '15px',
//                   },
//                 }}
//               >
//                 <Table
//                   sx={{
//                     minWidth: {
//                       xs: '1200px',
//                     },
//                   }}
//                   stickyHeader
//                   aria-label="simple table"
//                 >
//                   <TableHead>
//                     <TableRow>
//                       <TableCell>
//                         <Checkbox
//                           aria-label="All row select"
//                           indeterminate={
//                             mappedLists.length > 0 &&
//                             Object.values(rowSelect).some((checked) => checked) &&
//                             !isAllSelected
//                           }
//                           disabled={mappedLists.length === 1 || mappedLists.length === 0}
//                           checked={isAllSelected}
//                           color="primary"
//                           onChange={handleSelectAll} // Add this function to handle select all
//                         />
//                       </TableCell>
//                       <TableCell>Class</TableCell>
//                       <TableCell>Subject</TableCell>
//                       {/* <TableCell>Fee Allocation2</TableCell> */}
//                       <TableCell>Status</TableCell>
//                       <TableCell>Actions</TableCell>
//                     </TableRow>
//                   </TableHead>
//                   <TableBody>
//                     {mappedLists?.map((row: CTSAllocationMappedInfo) => {
//                       const scholarshipExists = mappedLists.some((f) => f.subjectId !== row.subjectId);
//                       const editedRow = editedRows.find((r) => r.subjectId === row.subjectId);
//                       // const isSelected = Boolean(selectedRow);
//                       const isSelected = selectedRows[row.subjectId] || false;
//                       const isSelectedStaffRole = editedRow?.staffRole ?? -1;
//                       const currentStaff = ROLL_OPTIONS.find((staff) => staff.id === row.staffRole);
//                       const staffName = currentStaff?.name;

//                       // Check if staffRole id 1 exists in CTSTeacherWiseMappingListData
//                       const isRoleOnePresent = CTSTeacherWiseMappingListData.some((data) => data.staffRole === 1);

//                       // Modify ROLL_OPTIONS to include disabled property
//                       const optionsWithDisabled = ROLL_OPTIONS.map((option) => ({
//                         ...option,
//                         disabled: option.id === 1 && isRoleOnePresent,
//                       }));
//                       return (
//                         <TableRow
//                           // sx={{ backgroundColor: rowBackgroundColors[row.subjectId] }}
//                           hover={!rowBackgroundColors[row.subjectId]}
//                           key={row.subjectId}
//                         >
//                           <TableCell>
//                             <Checkbox
//                               disabled={editTableRow[row.subjectId]}
//                               color="primary"
//                               checked={rowSelect[row.subjectId] || false}
//                               onChange={(event) => handleEditCheckboxChange(event, row.subjectId)}
//                             />
//                           </TableCell>
//                           <TableCell>
//                             {!editTableRow[row.subjectId] && !rowSelect[row.subjectId] ? (
//                               staffName
//                             ) : (
//                               <Autocomplete
//                                 value={
//                                   isSelected ? ROLL_OPTIONS.find((opt) => opt.id === isSelectedStaffRole) || null : null
//                                 }
//                                 options={optionsWithDisabled}
//                                 getOptionLabel={(option) => option.name || ''}
//                                 isOptionEqualToValue={(option, value) => option.id === value.id}
//                                 disabled={isSelected}
//                                 onChange={(event, newValue) => {
//                                   const selectedStaffRole = newValue ? newValue.id : -1;
//                                   setEditedRows((prevRows) =>
//                                     prevRows.map((item) =>
//                                       item.subjectId === row.subjectId
//                                         ? { ...item, staffRole: selectedStaffRole }
//                                         : item
//                                     )
//                                   );
//                                 }}
//                                 renderInput={(params) => (
//                                   <TextField
//                                     {...params}
//                                     placeholder="Select Role"
//                                     sx={{
//                                       '& .MuiInputBase-root': {
//                                         padding: '6px', // Adjust padding for the input field
//                                         backgroundColor: !isSelected
//                                           ? isLight
//                                             ? theme.palette.grey[100]
//                                             : theme.palette.grey[500]
//                                           : 'transparent', // Change background when disabled
//                                       },
//                                       '& .MuiAutocomplete-endAdornment': {
//                                         right: '4px', // Adjust the position of the dropdown icon
//                                       },
//                                     }}
//                                   />
//                                 )}
//                                 ListboxProps={{
//                                   style: { maxHeight: '250px' }, // Adjust dropdown height
//                                 }}
//                                 renderOption={(props, option) => (
//                                   <li
//                                     {...props}
//                                     aria-disabled={option.disabled}
//                                     style={{ opacity: option.disabled ? 0.5 : 1 }}
//                                   >
//                                     {option.name}
//                                   </li>
//                                 )}
//                               />
//                             )}
//                           </TableCell>

//                           {/* ================================ */}
//                           {/* ================================ */}
//                           {/* <TableCell>
//                             <Stack direction="row" gap={1}>
//                               <LoadingButton
//                                 loading={individualSaveLoading[row.subjectId]}
//                                 disabled={!rowSelect[row.subjectId] && !individualSaveButtonEnabled[row.subjectId]}
//                                 variant="contained"
//                                 size="small"
//                                 color={!rowSelect[row.subjectId] ? 'success' : 'warning'}
//                                 sx={{ py: 0.5, fontSize: '10px' }}
//                                 onClick={() => handleSave(row)}
//                               >
//                                 {rowSelect[row.subjectId]
//                                   ? 'Update'
//                                   : !individualSaveButtonEnabled[row.subjectId]
//                                   ? 'Saved'
//                                   : 'Save'}
//                               </LoadingButton>
//                               <Button
//                                 variant="contained"
//                                 size="small"
//                                 color="error"
//                                 sx={{ py: 0.5, fontSize: '10px' }}
//                                 onClick={() =>
//                                   individualRemoveRowButton[row.subjectId] ? handleDelete(row) : handleDeleteRow(row)
//                                 }
//                               >
//                                 {editTableRow[row.subjectId] && !rowSelect[row.subjectId] ? 'Remove' : 'Delete'}
//                               </Button>
//                             </Stack>
//                           </TableCell> */}
//                         </TableRow>
//                       );
//                     })}
//                   </TableBody>
//                 </Table>
//               </TableContainer>
//             </Paper>
//           </div>
//           <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 3 }}>
//             <Stack spacing={2} direction="row">
//               <Button
//                 disabled={CTSTeacherWiseMappingListData.length === 0}
//                 variant="contained"
//                 color="secondary"
//                 sx={{ fontFamily: typography.fontFamily }}
//               >
//                 Cancel
//               </Button>
//               <LoadingButton
//                 sx={{ width: 100 }}
//                 // loading={isSubmitting}
//                 // onClick={handleMapCTS}
//                 variant="contained"
//                 color="primary"
//                 // disabled={editedRows.length === 0}
//               >
//                 Map All
//               </LoadingButton>
//             </Stack>
//           </Box>
//         </Card>
//       </CtsAllocationTeacherWiseRoot>
//     </Page>
//   );
// }

// export default CtsAllocationTeacherWise;

/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/alt-text */
import React, { ChangeEvent, FormEvent, useCallback, useEffect, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Box,
  Select,
  Divider,
  Grid,
  Paper,
  Stack,
  Button,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Typography,
  Card,
  SelectChangeEvent,
  FormControl,
  IconButton,
  Checkbox,
  Tooltip,
  Collapse,
  Alert,
  Snackbar,
  MenuItem,
} from '@mui/material';
import styled, { useTheme } from 'styled-components';
import NoData from '@/assets/no-datas.png';
import AddIcon from '@mui/icons-material/Add';
import successIcon from '@/assets/MessageIcons/success.json';
import errorIcon from '@/assets/ManageFee/Error.json';
import ErrorIcon from '@/assets/ManageFee/ErrorIcon.json';
import Success from '@/assets/ManageFee/Success.json';
import deleteBin from '@/assets/ManageFee/deleteBin.json';
import deleteSuccess from '@/assets/ManageFee/deleteSuccess.json';
import EditIcon from '@mui/icons-material/Edit';
import LoadingButton from '@mui/lab/LoadingButton';
import useSettings from '@/hooks/useSettings';
import useAuth from '@/hooks/useAuth';
import { DeleteScholarshipType, ScholarshipDetailsType } from '@/types/ManageFee';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useAppSelector } from '@/hooks/useAppSelector';
import {
  getCTSClassList,
  getCTSFilterListStatus,
  getCTSStaffList,
  getCTSStaffListData,
  getCTSTeacherWiseClassListData,
  getCTSTeacherWiseMappingListData,
  getCTSTeacherWiseMappingListError,
  getCTSTeacherWiseMappingListStatus,
  getCTSTeacherWiseSubjectListData,
  getCTSYearList,
  getYearData,
} from '@/config/storeSelectors';
import DeleteIcon from '@mui/icons-material/Delete';
import SearchIcon from '@mui/icons-material/Search';
import { IoIosArrowUp } from 'react-icons/io';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import { ErrorMessage } from '@/components/shared/Popup/ErrorMessage';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import Lottie from 'lottie-react';
import {
  CTSAllocationMappedInfo,
  CTSAllocationMappedResponseInfo,
  CTSAllocationTeacherWiseMappingFilter,
  DeleteCTSAllocationRequest,
} from '@/types/StaffManagement';
import {
  AddCTSAllocationMap,
  deleteCTS,
  fetchCTSFilter,
  fetchCTSTeacherWiseMapiing,
} from '@/store/StaffMangement/StaffMangement/staffMangement.thunks';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import BackButton from '@/components/shared/BackButton';
import { useNavigate } from 'react-router-dom';
import { ROLL_OPTIONS } from '@/config/Selection';

const CtsAllocationTeacherWiseRoot = styled.div`
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 576px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 100px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid ${(props) => props.theme.palette.grey[200]};
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
        /* .MuiTableCell-root:first-child {
          padding: 5px;
        } */
      }
    }
  }
`;

export type ScholarshipAllocationType = {
  id?: number;
  feeId: number;
  termId: number;
  type: number;
  value: string;
  dbResult: string;
  subjectId: number;
  scholarshipMapId: number;
};

// export type ScholarshipDetailsType = {
//   id: number;
//   title: string;
//   type: number;
//   description: string;
//   adminId: number;
//   dbResult: string;
//   subjectId: number;
// };

type CtsPropsTypes = {
  onBackClick?: () => void;
};

export default function CtsAllocationTeacherWise({ onBackClick }: CtsPropsTypes) {
  const theme = useTheme();
  const isLight = useSettings().themeMode === 'light';
  const { user } = useAuth();
  const adminId: number = user ? user.accountId : 0;
  const dispatch = useAppDispatch();
  const YearData = useAppSelector(getYearData);
  const { confirm } = useConfirm();
  const [popup, setPopup] = useState({ id: 0, open: false });
  const [popup2, setPopup2] = useState({ id: 0, row: {}, open: false });

  const [snackBar, setSnackBar] = useState<boolean>(false);
  const [showFilter, setShowFilter] = useState(true);
  const [individualSaveButtonEnabled, setIndividualSaveButtonEnabled] = useState<{ [key: string]: number }>({});
  const [individualRemoveRowButton, setIndividualRemoveRowButton] = useState<{ [key: string]: number }>({});
  const [rowBackgroundColors, setRowBackgroundColors] = useState<Record<number, string>>({});
  const [editTableRow, setEditTableRow] = useState<Record<number, boolean>>({});
  const [rowSelect, setRowSelect] = useState<Record<number, boolean>>({});
  const [id, setId] = useState();
  const [scholarshipValues, setScholarshipValues] = useState<ScholarshipAllocationType[]>([
    { id: 0, feeId: -1, termId: -1, type: 1, value: '', dbResult: '', subjectId: 0, scholarshipMapId: 0 },
  ]);
  const [isAllSelected, setIsAllSelected] = useState<boolean>(false);
  const [editedRows, setEditedRows] = useState<CTSAllocationMappedInfo[]>([]);
  const CTSClassList = useAppSelector(getCTSClassList);
  const CTSTeacherWiseMappingListStatus = useAppSelector(getCTSTeacherWiseMappingListStatus);
  const CTSTeacherWiseMappingListError = useAppSelector(getCTSTeacherWiseMappingListError);
  const CTSTeacherWiseMappingListData = useAppSelector(getCTSTeacherWiseMappingListData);
  const CTSTeacherWiseSubjectListData = useAppSelector(getCTSTeacherWiseSubjectListData);
  const CTSTeacherWiseClassListData = useAppSelector(getCTSTeacherWiseClassListData);
  const [staffFilter, setStaffFilter] = useState(0);
  const [selectedRows, setSelectedRows] = useState<Record<number, boolean>>({});
  const [individualLoadingMap, setIndividualLoadingMap] = useState<Record<string, boolean>>({});
  const CTSYearList = useAppSelector(getCTSYearList);
  const CTSStaffListData = useAppSelector(getCTSStaffListData);
  const CTSStaffList = useAppSelector(getCTSStaffList);
  const CTSFilterListStatus = useAppSelector(getCTSFilterListStatus);
  const defaultYear = CTSYearList.length > 0 ? CTSYearList[CTSYearList.length - 1] : null; // Get the last object.
  const [academicYearFilter, setAcademicYearFilter] = useState(defaultYear ? defaultYear.academicId : 0);
  const [dbStatus, setDbStatus] = useState<'Success' | 'Exist' | 'Failed' | ''>('');
  const navigate = useNavigate();

  const handleClose = () =>
    setPopup((row) => {
      return { ...row, open: false };
    });
  const [scholarshipValuesArray, setScholarshipValuesArray] = useState<ScholarshipAllocationType[]>([]);

  // const [scholarshipValues2, setScholarshipValues2] = useState<any[]>([]);
  const [scholarshipValues2, setScholarshipValues2] = useState<ScholarshipAllocationType[]>([
    { id: 0, feeId: -1, termId: -1, type: 1, value: '', dbResult: '', subjectId: 0, scholarshipMapId: 0 },
  ]);
  const handleClose2 = () => {
    setPopup2((prev) => {
      return { ...prev, open: false };
    });
  };
  // const handleOpenPopup = (row, subjectId) => {
  //   setPopup2(true);
  //   setScholarshipValues2(row);
  //   setId(subjectId);
  //   console.log('row::::----', row);
  // };
  const handleSave2 = (updatedValues: any) => {
    setScholarshipValuesArray(updatedValues);
    console.log('scholarshipValuesArray:::-', scholarshipValuesArray);
  };

  useEffect(() => {
    // Load scholarshipValuesArray from local storage on component mount
    const storedScholarshipValuesArray = localStorage.getItem('scholarshipValuesArray');
    if (storedScholarshipValuesArray) {
      setScholarshipValuesArray(JSON.parse(storedScholarshipValuesArray));
    }
    console.log('storedScholarshipValuesArray:::-', storedScholarshipValuesArray);
  }, []);
  const [mappedLists, setMappedLists] = useState<CTSAllocationMappedInfo[]>([
    {
      id: 0,
      subjectId: 0,
      subjectName: '',
      cteacherId: 0,
      staffId: 0,
      staffRole: -1,
      classId: 0,
    },
  ]);
  const handleAddRow = () => {
    const newRow: CTSAllocationMappedInfo = {
      id: mappedLists.length + 1,
      subjectId: 0,
      subjectName: '',
      cteacherId: 0,
      staffId: 0,
      staffRole: -1,
      classId: 0,
    };
    setMappedLists([...mappedLists, newRow]);
    setRowBackgroundColors((prev) => ({
      ...prev,
      [newRow.subjectId]: isLight ? '#f0fdf4' : theme.palette.grey[900],
    }));
    setIndividualSaveButtonEnabled((prev) => ({ ...prev, [newRow.cteacherId]: true }));
    setIndividualRemoveRowButton((prev) => ({ ...prev, [newRow.cteacherId]: true }));
    setEditTableRow((prev) => ({ ...prev, [newRow.cteacherId]: true }));
  };

  const currentCTSTeacherWiseListRequest: CTSAllocationTeacherWiseMappingFilter = React.useMemo(
    () => ({
      adminId,
      academicId: academicYearFilter,
      staffId: staffFilter,
    }),
    [adminId, academicYearFilter, staffFilter]
  );

  const loadCTSTeacherWiseList = React.useCallback(
    async (request: CTSAllocationTeacherWiseMappingFilter) => {
      try {
        const data = await dispatch(fetchCTSTeacherWiseMapiing(request)).unwrap();
        if (data) {
          const mappedL = data.mappedList ? data.mappedList.map((item: CTSAllocationMappedInfo) => ({ ...item })) : [];
          setMappedLists(mappedL);
          console.log('data::::----', data);
        }
        console.log('mappedLists', mappedLists);
      } catch (error) {
        console.error('Error loading list:', error);
      }
    },
    [dispatch, mappedLists]
  );

  React.useEffect(() => {
    // if (YearStatus === 'idle') {
    //   setAcademicYearFilter(defualtYear);
    // }
    if (CTSTeacherWiseMappingListStatus === 'idle') {
      loadCTSTeacherWiseList(currentCTSTeacherWiseListRequest);
    }
    if (CTSFilterListStatus === 'idle') {
      const CtsFilerDatas = dispatch(fetchCTSFilter(adminId));
      console.log('CtsFilerDatas', CtsFilerDatas);
    }
  }, [
    dispatch,
    adminId,
    CTSFilterListStatus,
    CTSTeacherWiseMappingListStatus,
    loadCTSTeacherWiseList,
    currentCTSTeacherWiseListRequest,
  ]);

  const handleYearChange = (e: SelectChangeEvent) => {
    setRowSelect({});
    setEditTableRow({});
    setIsAllSelected(false);
    const selectedAcademicId = e.target.value;
    setAcademicYearFilter(parseInt(selectedAcademicId, 10));

    loadCTSTeacherWiseList({
      ...currentCTSTeacherWiseListRequest,
      academicId: parseInt(selectedAcademicId, 10),
    });
  };

  const handleStaffChange = (e: SelectChangeEvent) => {
    setRowSelect({});
    setEditTableRow({});
    setIsAllSelected(false);
    const selectedStaff = parseInt(e.target.value, 10);
    if (selectedStaff) {
      setStaffFilter(selectedStaff);
    }
    loadCTSTeacherWiseList({
      ...currentCTSTeacherWiseListRequest,
      staffId: selectedStaff || -1,
    });
  };

  const handleIndividualMapCTS = useCallback(
    async (row: CTSAllocationMappedInfo) => {
      try {
        // Set loading state for the current subject
        setIndividualLoadingMap((prevMap) => ({ ...prevMap, [row.cteacherId]: true }));

        const req = [
          {
            ...row,
            staffId: staffFilter,
            staffRole: row.staffRole,
            adminId,
            academicId: academicYearFilter,
            classId: row.classId,
            dbResult: 'string',
            id: 0,
          },
        ];

        console.log('Request:', req);

        try {
          const response = await dispatch(AddCTSAllocationMap(req)).unwrap();
          console.log('Response:', response);

          if (!Array.isArray(response) || response.length === 0) {
            console.error('Invalid response:', response);
            throw new Error('Invalid response from server');
          }

          // Process each object in the response
          await Promise.all(
            response.map(async (resObj: CTSAllocationMappedResponseInfo) => {
              console.log('Processing dbResult:', resObj.dbResult);
              setDbStatus((prevMap) => ({ ...prevMap, [row.cteacherId]: resObj.dbResult }));

              if (resObj.dbResult === 'Success') {
                await loadCTSTeacherWiseList({ ...currentCTSTeacherWiseListRequest });
              }
            })
          );

          // Return the processed response
          return response;
        } catch (err) {
          console.error('Error processing request:', req, err);
          setDbStatus((prevMap) => ({ ...prevMap, [row.cteacherId]: 'Failed' }));
          return [{ dbResult: 'Failed' }]; // Return fallback response
        }
      } catch (error) {
        const errorMessage = (
          <ErrorMessage loop={false} jsonIcon={ErrorIcon} message="Something went wrong, please try again" />
        );
        await confirm(errorMessage, 'CTS Map', { okLabel: 'Ok', showOnlyOk: true });
        console.error('Error mapping CTS:', error);
        return null; // Return null for failed execution
      } finally {
        // Reset statuses
        setTimeout(() => {
          setDbStatus((prevMap) => ({ ...prevMap, [row.cteacherId]: '' }));
        }, 5000);
        setIndividualLoadingMap((prevMap) => ({ ...prevMap, [row.cteacherId]: false }));
      }
    },
    [
      dispatch,
      academicYearFilter,
      confirm,
      loadCTSTeacherWiseList,
      adminId,
      currentCTSTeacherWiseListRequest,
      staffFilter,
    ]
  );

  const handleDeleteCtsList = useCallback(
    async (ctsObj: CTSAllocationMappedInfo) => {
      const { cteacherId } = ctsObj;
      const deleteConfirmMessage = (
        <DeleteMessage jsonIcon={deleteBin} message={<div>Are you sure you want to delete the CTS Row ?</div>} />
      );
      if (await confirm(deleteConfirmMessage, 'Delete Row?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
        const sendReq = [{ adminId, cteacherId, dbResult: 'string', id: 0 }];

        console.log('sendReq', sendReq);

        const deleteResponse = await dispatch(deleteCTS(sendReq));
        console.log('deleteResponse::::----', deleteResponse);
        if (deleteResponse && Array.isArray(deleteResponse.payload)) {
          const results: DeleteCTSAllocationRequest[] = deleteResponse.payload;
          const errorMessages = results.find((result) => result.dbResult === 'Failed');
          const successMessages = results.find((result) => result.dbResult === 'Success');
          // Reload only the specific message template with the deleted messageId

          if (!errorMessages) {
            const deleteSuccessMessage = (
              <SuccessMessage loop={false} jsonIcon={deleteSuccess} message="Delete successfully" />
            );
            await confirm(deleteSuccessMessage, 'Success', { okLabel: 'Ok', showOnlyOk: true });
          } else if (!successMessages) {
            const deleteErrorMessage = <DeleteMessage jsonIcon={errorIcon} message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          } else {
            const deleteErrorMessage = <DeleteMessage jsonIcon={errorIcon} message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          }
          loadCTSTeacherWiseList({ ...currentCTSTeacherWiseListRequest });

          // setSnackBar(true);
          // setBasicFeeData((prevDetails) => prevDetails.filter((item) => item.feeId !== feeId));
        }
      }
    },
    [dispatch, adminId, confirm, loadCTSTeacherWiseList, currentCTSTeacherWiseListRequest]
  );

  const handleDeleteMultiple = useCallback(async () => {
    const sendConfirmMessage = (
      <DeleteMessage jsonIcon={deleteBin} message={<div>Are you sure you want to delete all ?</div>} />
    );
    if (await confirm(sendConfirmMessage, 'Delete Basic Fee?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
      const sendRequests: DeleteCTSAllocationRequest[] = await Promise.all(
        editedRows?.map(async (row) => {
          const sendReq = {
            adminId,
            cteacherId: row.cteacherId,
            dbResult: '',
            id: 0,
          };
          return sendReq;
        }) || []
      );
      const deleteResponse = await dispatch(deleteCTS(sendRequests));

      console.log('deleteResponse', deleteResponse);
      if (deleteResponse && Array.isArray(deleteResponse.payload)) {
        const results: DeleteCTSAllocationRequest[] = deleteResponse.payload;
        const errorMessages = results.find((result) => result.dbResult === 'Failed');
        const successMessages = results.find((result) => result.dbResult === 'Success');

        if (!errorMessages) {
          const deleteSuccessMessage = (
            <SuccessMessage loop={false} jsonIcon={deleteSuccess} message="Delete All Successfully" />
          );
          await confirm(deleteSuccessMessage, 'Success', { okLabel: 'Ok', showOnlyOk: true });
        } else if (!successMessages) {
          const deleteErrorMessage = <DeleteMessage loop={false} jsonIcon={errorIcon} message="Delete Failed" />;
          await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
        } else {
          const deleteErrorMessage = (
            <DeleteMessage loop={false} jsonIcon={errorIcon} message="Something went wrong please try again" />
          );
          await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
        }
        loadCTSTeacherWiseList({ ...currentCTSTeacherWiseListRequest });
        setEditedRows([]);
        setRowSelect({});
        setEditTableRow({});
        setIsAllSelected(false);
        // setBasicFeeData((prevDetails) =>
        //   prevDetails.filter((item: CreateBasicFeeSettingTitleDataType) => !selectedRows.includes(item.feeId))
        // );
      }
    }
  }, [confirm, dispatch, editedRows, adminId, loadCTSTeacherWiseList, currentCTSTeacherWiseListRequest]);

  const getRowKey = useCallback((row: CTSAllocationMappedInfo) => row.subjectId, []);

  const SelectedColumns: DataTableColumn<CTSAllocationMappedInfo>[] = useMemo(
    () => [
      {
        name: 'subjectId',
        headerLabel: 'Sl.No',
        dataKey: 'subjectId',
      },
      {
        name: 'subjectName',
        headerLabel: 'Subject',
        dataKey: 'subjectName',
      },
      {
        name: 'staff',
        headerLabel: 'Teacher',
        renderCell: (row) => {
          const currentStaff = CTSStaffListData.find((staff) => staff.staffId === row.staffId);
          const staffName = currentStaff?.staffName;

          return <Typography variant="subtitle2">{staffName}</Typography>;
        },
      },
      {
        name: 'staffRole',
        headerLabel: 'Role',
        width: '200px',
        renderCell: (row) => {
          const currentStaff = ROLL_OPTIONS.find((staff) => staff.id === row.staffRole);
          const staffName = currentStaff?.name;

          return <Typography variant="subtitle2">{staffName}</Typography>;
        },
      },
    ],
    [CTSStaffListData]
  );

  const handleMapCTS = useCallback(async () => {
    try {
      const ConfirmMessage = (
        <SuccessMessage
          message={
            <Card sx={{ my: 1, boxShadow: 0, width: 500, border: 1, borderColor: theme.palette.grey[300] }}>
              <DataTable columns={SelectedColumns} data={editedRows} getRowKey={getRowKey} fetchStatus="success" />
            </Card>
          }
        />
      );
      if (await confirm(ConfirmMessage, 'Map Rows?', { okLabel: 'Confirm', cancelLabel: 'Cancel' })) {
        const promises = editedRows.map(async (row) => {
          // Convert startDate and endDate to DD/MM/YYYY format
          const req = [
            {
              ...row,
              adminId,
              academicId: academicYearFilter,
              classId: staffFilter,
              dbResult: 'string',
              id: 0,
            },
          ];
          console.log('updateReq::::----', req);
          const response = await dispatch(AddCTSAllocationMap(req)).unwrap();
          console.log('response::::----', response);

          return response;
        });

        const responses = await Promise.all(promises);

        // Check if all updates were successful
        const isSuccess = responses.every((response) => response);
        const isExist = responses.every((response) => response.dbResult === 'Exist');
        // const isSuccess = responses.every((response) => response.dbResult === 'Success');
        // const isExist = responses.every((response) => response.dbResult === 'Exist');
        console.log('isSuccess::::----', isSuccess);
        console.log('promises::::----', promises);

        if (isSuccess) {
          setEditedRows([]);
          loadCTSTeacherWiseList({ ...currentCTSTeacherWiseListRequest });
          const successMessage = <SuccessMessage loop={false} jsonIcon={Success} message="CTS Map successfully" />;
          await confirm(successMessage, 'CTS Map', { okLabel: 'Ok', showOnlyOk: true });
        } else if (isExist) {
          const errorMessage = <ErrorMessage loop={false} jsonIcon={ErrorIcon} message="CTS already created" />;
          await confirm(errorMessage, 'CTS Map', { okLabel: 'Ok', showOnlyOk: true });
        } else {
          const errorMessage = <ErrorMessage loop={false} jsonIcon={ErrorIcon} message="CTS mapped failed" />;
          await confirm(errorMessage, 'CTS Map', { okLabel: 'Ok', showOnlyOk: true });
        }
      }
    } catch (error) {
      const errorMessage = (
        <ErrorMessage loop={false} jsonIcon={ErrorIcon} message="Something went wrong please try again " />
      );
      await confirm(errorMessage, 'CTS Map', { okLabel: 'Ok', showOnlyOk: true });
      // Handle error
      console.error('Error mapping cts :', error);
    }
  }, [
    dispatch,
    academicYearFilter,
    confirm,
    loadCTSTeacherWiseList,
    adminId,
    currentCTSTeacherWiseListRequest,
    staffFilter,
    editedRows,
    SelectedColumns,
    getRowKey,
    theme,
  ]);

  const handleDelete = (row: CTSAllocationMappedInfo) => {
    setMappedLists((prev) => prev.filter((val) => !(val.id === row.id)));
    setScholarshipValues((prev) => prev.filter((val) => !(val.id === row.id)));
  };

  const handleReset = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      setAcademicYearFilter(defaultYear ? defaultYear.academicId : 0);
      setStaffFilter(0);
      setRowSelect({});
      setEditTableRow({});
      setIsAllSelected(false);
      loadCTSTeacherWiseList({
        adminId,
        academicId: defaultYear ? defaultYear.academicId : 0,
        staffId: 0,
      });
      // (currentCTSTeacherWiseListRequest);
    },
    [loadCTSTeacherWiseList, adminId, defaultYear]
  );

  const handleEditCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>, row: CTSAllocationMappedInfo) => {
    const { cteacherId } = row;
    const { checked } = event.target;

    if (checked) {
      setRowSelect((prev) => ({ ...prev, [cteacherId]: true }));
      console.log('rowSelect::::----', rowSelect);
    } else {
      setRowSelect((prev) => ({ ...prev, [cteacherId]: false }));
    }
    setEditedRows((prev) => {
      if (checked) {
        return prev.some((item) => item.cteacherId === row.cteacherId) ? prev : [...prev, row];
      }
      return prev.filter((item) => item.cteacherId !== row.cteacherId);
    });
  };

  const handleSelectAll = (event: ChangeEvent<HTMLInputElement>) => {
    const isChecked = event.target.checked;
    setIsAllSelected(isChecked);
    setRowSelect(mappedLists.reduce((acc, item) => ({ ...acc, [item.cteacherId]: isChecked }), {}));
    const newEditedRows = isChecked ? mappedLists : [];
    setEditedRows(newEditedRows);
  };

  const action = (
    <Button color="secondary" size="small">
      UNDO
    </Button>
  );

  const sortedYearList = [...CTSYearList].sort((a, b) => b.academicId - a.academicId);
  return (
    <Page title=" CTS Allocaction Teacher Wise">
      <CtsAllocationTeacherWiseRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack
            direction="row"
            alignItems="start"
            justifyContent="space-between"
            flex={{ xs: 1, sm: 0 }}
            flexWrap="nowrap"
          >
            <Stack direction="row" alignItems="center">
              <BackButton
                onBackClick={() => {
                  onBackClick();
                  navigate('/staff-management/allocation-list');
                }}
              />
              <Typography variant="h6" fontSize={17}>
                CTS Allocaction Teacher Wise
              </Typography>
            </Stack>
            <Box pb={1} sx={{ flexShrink: 0 }}>
              {mappedLists.length > 0 && Object.values(rowSelect).some((checked) => checked) && (
                <Tooltip title="Delete All">
                  <IconButton
                    sx={{ visibility: { xs: 'hidden', sm: 'visible' }, mr: 1 }}
                    aria-label="delete"
                    color="error"
                    onClick={handleDeleteMultiple}
                  >
                    <DeleteIcon />
                  </IconButton>
                </Tooltip>
              )}

              {showFilter === false ? (
                <Tooltip title="Search">
                  <IconButton
                    aria-label="delete"
                    color="primary"
                    sx={{ mr: { xs: 0, sm: 1 } }}
                    onClick={() => setShowFilter((x) => !x)}
                  >
                    <SearchIcon />
                  </IconButton>
                </Tooltip>
              ) : (
                <IconButton
                  aria-label="delete"
                  color="primary"
                  sx={{ mr: { xs: 0, sm: 1 } }}
                  onClick={() => setShowFilter((x) => !x)}
                >
                  <IoIosArrowUp />
                </IconButton>
              )}
            </Box>
          </Stack>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate onReset={handleReset}>
                <Grid pb={1} container spacing={2}>
                  <Grid item xxl={2} xl={2.5} lg={2.5} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Select
                        labelId="academicYearFilter"
                        id="academicYearFilterSelect"
                        value={academicYearFilter?.toString()}
                        onChange={handleYearChange}
                        placeholder="Select Year"
                      >
                        <MenuItem sx={{ display: 'none' }} value={0}>
                          Select
                        </MenuItem>
                        {sortedYearList.map((opt) => (
                          <MenuItem key={opt.academicId} value={opt.academicId}>
                            {opt.academicTime}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={2.5} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Select Teacher
                      </Typography>
                      <Select
                        labelId="staffFilter"
                        id="staffFilterSelect"
                        value={staffFilter?.toString()}
                        onChange={handleStaffChange}
                        placeholder="Select Class"
                        MenuProps={{
                          PaperProps: {
                            style: {
                              maxHeight: '250px', // Adjust the value to your desired height
                            },
                          },
                        }}
                      >
                        <MenuItem value={0}>Select</MenuItem>
                        {CTSStaffList.map((opt) => (
                          <MenuItem key={opt.staffId} value={opt.staffId}>
                            {opt.staffName}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item sm={1} xs={12}>
                    <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, sm: 3.79 } }}>
                      <Button type="reset" variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>
            {mappedLists.length !== 0 && (
              <Box display="flex" gap={2} sx={{ justifyContent: 'end', py: 2 }}>
                {/* <LoadingButton
                  sx={{ width: 100 }}
                  size="small"
                  // loading={isSubmitting}
                  onClick={handleMapCTS}
                  variant="contained"
                  color="primary"
                  // disabled={editedRows.length === 0}
                >
                  Map All
                </LoadingButton> */}
                <Button
                  size="small"
                  type="button"
                  color="secondary"
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={handleAddRow}
                  sx={{ py: 0, px: 1 }}
                >
                  Add Row
                </Button>
              </Box>
            )}
            {mappedLists.length !== 0 ? (
              <Paper
                className="card-table-container"
                sx={{
                  border: '1px solid #e8e8e9',
                  width: '100%',
                  overflow: 'auto',
                  '&::-webkit-scrollbar': {
                    width: 0,
                  },
                }}
              >
                <TableContainer
                  sx={{
                    '&::-webkit-scrollbar': {
                      height: '15px',
                    },
                  }}
                >
                  <Table
                    sx={{
                      minWidth: {
                        xs: '1200px',
                      },
                    }}
                    stickyHeader
                    aria-label="simple table"
                  >
                    <TableHead>
                      <TableRow>
                        <TableCell>
                          <Checkbox
                            aria-label="All row select"
                            indeterminate={
                              mappedLists.length > 0 &&
                              Object.values(rowSelect).some((checked) => checked) &&
                              !isAllSelected
                            }
                            disabled={mappedLists.length === 1 || mappedLists.length === 0}
                            checked={isAllSelected}
                            color="primary"
                            onChange={handleSelectAll} // Add this function to handle select all
                          />
                        </TableCell>
                        <TableCell>Class</TableCell>
                        <TableCell>Subject</TableCell>
                        <TableCell>Staff Role</TableCell>
                        <TableCell>Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {mappedLists?.map((row: CTSAllocationMappedInfo) => {
                        const scholarshipExists = mappedLists.some((f) => f.subjectId !== row.subjectId);
                        const editedRow = editedRows.find((r) => r.subjectId === row.subjectId);
                        // const isSelected = Boolean(selectedRow);
                        // const isSelected = selectedRows[row.subjectId] || false;

                        const currentClass = CTSTeacherWiseClassListData.find((c) => c.classId === row.classId);
                        const className = currentClass?.className;

                        const isSelectedSubject = editedRow?.subjectId ?? -1;
                        const currentSubject = CTSTeacherWiseSubjectListData.find((c) => c.subjectId === row.subjectId);
                        const subjectName = currentSubject?.subjectName;

                        const isSelectedRoll = editedRow?.staffRole ?? -1;
                        const currentRoll = ROLL_OPTIONS.find((c) => c.id === row.staffRole);
                        const staffRole = currentRoll?.name;

                        console.log('editedRow', editedRows);

                        return (
                          <TableRow
                            // sx={{ backgroundColor: rowBackgroundColors[row.subjectId] }}
                            hover={!rowBackgroundColors[row.cteacherId]}
                            key={row.cteacherId}
                          >
                            <TableCell>
                              <Checkbox
                                disabled={editTableRow[row.cteacherId]}
                                color="primary"
                                checked={rowSelect[row.cteacherId] || false}
                                onChange={(event) => handleEditCheckboxChange(event, row)}
                              />
                            </TableCell>
                            <TableCell>
                              {!editTableRow[row.cteacherId] && !rowSelect[row.cteacherId] ? (
                                className
                              ) : (
                                <Select
                                  fullWidth
                                  value={row.classId}
                                  onChange={(e) => {
                                    const newValue = parseInt(e.target.value as string, 10);
                                    setMappedLists((prevRows) =>
                                      prevRows.map((item) => {
                                        if (
                                          row.cteacherId === 0 ? row.id === item.id : row.cteacherId === item.cteacherId
                                        ) {
                                          return { ...item, classId: newValue };
                                        }
                                        return item;
                                      })
                                    );
                                  }}
                                  sx={{
                                    height: '30.5px',
                                    // width: 130,
                                    color: rowSelect[row.cteacherId] ? theme.palette.warning.main : '',
                                  }}
                                  size="small"
                                >
                                  <MenuItem sx={{ display: 'none' }} value={0}>
                                    Select Class
                                  </MenuItem>
                                  {CTSTeacherWiseClassListData.map((opt) => (
                                    <MenuItem key={opt.classId} value={opt.classId}>
                                      {opt.className}
                                    </MenuItem>
                                  ))}
                                </Select>
                              )}
                            </TableCell>
                            <TableCell>
                              {!editTableRow[row.cteacherId] && !rowSelect[row.cteacherId] ? (
                                subjectName
                              ) : (
                                <Select
                                  fullWidth
                                  value={row.subjectId}
                                  onChange={(e) => {
                                    const newValue = parseInt(e.target.value as string, 10);
                                    setMappedLists((prevRows) =>
                                      prevRows.map((item) => {
                                        if (
                                          row.cteacherId === 0 ? row.id === item.id : row.cteacherId === item.cteacherId
                                        ) {
                                          return { ...item, subjectId: newValue };
                                        }
                                        return item;
                                      })
                                    );
                                  }}
                                  sx={{
                                    height: '30.5px',
                                    // width: 130,
                                    color: rowSelect[row.cteacherId] ? theme.palette.warning.main : '',
                                  }}
                                  size="small"
                                >
                                  <MenuItem sx={{ display: 'none' }} value={0}>
                                    Select Subject
                                  </MenuItem>
                                  {CTSTeacherWiseSubjectListData.map((opt) => (
                                    <MenuItem key={opt.subjectId} value={opt.subjectId}>
                                      {opt.subjectName}
                                    </MenuItem>
                                  ))}
                                </Select>
                              )}
                            </TableCell>
                            <TableCell>
                              {!editTableRow[row.cteacherId] && !rowSelect[row.cteacherId] ? (
                                staffRole
                              ) : (
                                <Select
                                  fullWidth
                                  value={row.staffRole}
                                  onChange={(e) => {
                                    const newValue = parseInt(e.target.value as string, 10);
                                    setMappedLists((prevRows) =>
                                      prevRows.map((item) => {
                                        if (
                                          row.cteacherId === 0 ? row.id === item.id : row.cteacherId === item.cteacherId
                                        ) {
                                          return { ...item, staffRole: newValue };
                                        }
                                        return item;
                                      })
                                    );
                                  }}
                                  sx={{
                                    height: '30.5px',
                                    // width: 130,
                                    color: rowSelect[row.cteacherId] ? theme.palette.warning.main : '',
                                  }}
                                  size="small"
                                >
                                  <MenuItem sx={{ display: 'none' }} value={-1}>
                                    Select Role
                                  </MenuItem>
                                  {ROLL_OPTIONS.map((opt) => (
                                    <MenuItem key={opt.id} value={opt.id}>
                                      {opt.name}
                                    </MenuItem>
                                  ))}
                                </Select>
                              )}
                            </TableCell>

                            <TableCell>
                              <Stack direction="row" gap={1}>
                                {dbStatus[row.cteacherId] === 'Success' ? (
                                  <Stack alignItems="center" width={64}>
                                    <Lottie animationData={successIcon} loop={false} style={{ width: '27px' }} />
                                  </Stack>
                                ) : dbStatus[row.cteacherId] === 'Exist' ? (
                                  <Tooltip title="Already Exist">
                                    <Stack alignItems="center" width={64}>
                                      <Lottie animationData={errorIcon} loop={false} style={{ width: '27px' }} />
                                    </Stack>
                                  </Tooltip>
                                ) : dbStatus[row.cteacherId] === 'Failed' ? (
                                  <Tooltip title={CTSTeacherWiseMappingListError}>
                                    <Stack alignItems="center" width={64}>
                                      <Lottie animationData={errorIcon} loop={false} style={{ width: '27px' }} />
                                    </Stack>
                                  </Tooltip>
                                ) : (
                                  <LoadingButton
                                    loading={individualLoadingMap[row.cteacherId]}
                                    disabled={
                                      !rowSelect[row.cteacherId] && !individualSaveButtonEnabled[row.cteacherId]
                                    }
                                    variant="contained"
                                    size="small"
                                    color={!rowSelect[row.cteacherId] ? 'success' : 'warning'}
                                    sx={{ py: 0.5, fontSize: '10px' }}
                                    onClick={() => handleIndividualMapCTS(row)}
                                  >
                                    {rowSelect[row.cteacherId]
                                      ? 'Update'
                                      : !individualSaveButtonEnabled[row.cteacherId]
                                      ? 'Mapped'
                                      : 'Map'}
                                  </LoadingButton>
                                )}
                                <Button
                                  variant="contained"
                                  size="small"
                                  color="error"
                                  sx={{ py: 0.5, fontSize: '10px' }}
                                  onClick={() =>
                                    individualRemoveRowButton[row.cteacherId]
                                      ? handleDelete(row)
                                      : handleDeleteCtsList(row)
                                  }
                                >
                                  {editTableRow[row.cteacherId] && !rowSelect[row.cteacherId] ? 'Remove' : 'Delete'}
                                </Button>
                              </Stack>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Paper>
            ) : (
              <Box
                display="flex"
                alignItems="center"
                justifyContent="center"
                width="100%"
                height={{ xs: 'calc(100vh - 400px)', sm: 'calc(100vh - 400px)', lg: 'calc(100vh - 400px)' }}
              >
                <Stack direction="column" alignItems="center">
                  <img src={NoData} width="150px" alt="" />
                  <Typography variant="subtitle2" mt={2} color="GrayText">
                    No data found !
                  </Typography>
                </Stack>
              </Box>
            )}
          </div>
        </Card>
      </CtsAllocationTeacherWiseRoot>
      <Snackbar
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        open={snackBar}
        autoHideDuration={3000}
        onClose={() => setSnackBar(false)}
        // message="Receipt number already exist"
      >
        <Alert onClose={() => setSnackBar(false)} severity="success" variant="filled" sx={{ width: '100%' }}>
          Row Deleted
        </Alert>
      </Snackbar>
    </Page>
  );
}
