/* eslint-disable import/no-extraneous-dependencies */
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import alias from '@rollup/plugin-alias';
import { resolve } from 'path';
import { VitePWA } from 'vite-plugin-pwa';
// import mkcert from 'vite-plugin-mkcert';
// import checker from 'vite-plugin-checker';

const projectRootDir = resolve(__dirname);

// const httpsOptions = {
//   key: resolve(projectRootDir, 'localhost+1-key.pem'),
//   cert: resolve(projectRootDir, 'localhost+1.pem'),
// };

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    // mkcert({ keyFileName: httpsOptions.key, certFileName: httpsOptions.cert }),
    react(),
    VitePWA({
      manifest: {
        name: 'passdaily',
        short_name: 'Passdaily',
        // description: '',
        icons: [
          {
            src: '/logo-small.svg',
            sizes: '192x192',
            type: 'image/png',
            purpose: 'any',
          },
          {
            src: '/logo-small.svg',
            sizes: '512x512',
            type: 'image/png',
            purpose: 'maskable',
          },
        ],
      },
    }),
    alias({
      entries: [
        {
          find: '@',
          replacement: resolve(projectRootDir, 'src'),
        },
      ],
    }),
    // checker({
    //   typescript: true,
    // }),
  ],
  server: {
    open: true,
    port: 8080,
    // https: true,
    // hmr: {
    //   host: 'localhost',
    //   port: 8081,
    //   // protocol: 'wss',
    // },
  },
});
