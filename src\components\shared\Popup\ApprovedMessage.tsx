import { Box, Stack, Typography } from '@mui/material';
import Approved from '@/assets/attendance/Successfully.svg';
import NotApproved from '@/assets/attendance/notApprove.svg';

export const ApprovedPopup = ({ approved }: any) => {
  return (
    <Stack sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
      <Box
        sx={{
          backgroundColor: '#F4F6F8',
          p: 2,
          borderRadius: '50%',
          height: 100,
          width: 100,
          display: 'flex',
          justifyContent: 'center',
        }}
      >
        {approved && approved === 'Approved' ? <img src={Approved} alt="" /> : <img src={NotApproved} alt="" />}
      </Box>
      {approved && approved === 'Approved' ? (
        <Typography gutterBottom fontWeight={550} textAlign="center" color="secondary" pt={2}>
          Approved the Leave
        </Typography>
      ) : (
        <Typography gutterBottom fontWeight={550} textAlign="center" color="secondary" pt={2}>
          Not Approved the Leave
        </Typography>
      )}
    </Stack>
  );
};
