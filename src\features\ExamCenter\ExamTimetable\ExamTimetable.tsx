/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  Collapse,
  FormControl,
  useTheme,
} from '@mui/material';
import styled from 'styled-components';
import deleteBin from '@/assets/ManageFee/deleteBin.json';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import { MdAdd } from 'react-icons/md';
import DeleteIcon from '@mui/icons-material/Delete';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import SearchIcon from '@mui/icons-material/Search';
import { ExamTimetableInfo } from '@/types/ExamCenter';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import Popup from '@/components/shared/Popup/Popup';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import { Create } from '../ExamManage/Create';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { deleteTermFeeList } from '@/store/ManageFee/manageFee.thunks';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';

const ManageExamRoot = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      height: calc(100% - 40px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;
const dummyExamData: ExamTimetableInfo[] = [
  {
    examId: 1,
    accademicYear: '2023-2024',
    className: 'VII-A',
    examName: 'Malayalam',
    examSubject: 'Malayalam',
    examDate: '12-07-2023',
    examTime: '10:00AM to 12:30PM',
    status: 1,
  },
  {
    examId: 2,
    accademicYear: '2023-2024',
    className: 'VII-A',
    examName: 'English',
    examSubject: 'English',
    examDate: '12-07-2023',
    examTime: '10:00AM to 12:30PM',
    status: 1,
  },
  {
    examId: 3,
    accademicYear: '2023-2024',
    className: 'VII-A',
    examName: 'Hindi',
    examSubject: 'Malayalam',
    examDate: '12-07-2023',
    examTime: '10:00AM to 12:30PM',
    status: 1,
  },
  {
    examId: 0,
    accademicYear: '2023-2024',
    className: 'VII-A',
    examName: 'Malayalam',
    examSubject: 'Malayalam',
    examDate: '12-07-2023',
    examTime: '10:00AM to 12:30PM',
    status: 1,
  },

  {
    examId: 0,
    accademicYear: '2023-2024',
    className: 'VII-A',
    examName: 'Malayalam',
    examSubject: 'Malayalam',
    examDate: '12-07-2023',
    examTime: '10:00AM to 12:30PM',
    status: 1,
  },

  {
    examId: 0,
    accademicYear: '2023-2024',
    className: 'VII-A',
    examName: 'Malayalam',
    examSubject: 'Malayalam',
    examDate: '12-07-2023',
    examTime: '10:00AM to 12:30PM',
    status: 1,
  },
];

const status = ['Publish', 'Unpublish'];

function ExamTimetable() {
  const theme = useTheme();
  const { confirm } = useConfirm();
  const dispatch = useAppDispatch();
  const [drawerOpen, setDrawerOpen] = React.useState<boolean>(false);
  const [Delete, setDelete] = React.useState(false);
  const [EditData, setEditData] = React.useState();
  const [showFilter, setShowFilter] = useState(true);

  const handleClickDelete = () => setDelete(true);
  const handleClickCloseDelete = () => setDelete(false);
  const update = (data: any) => {
    setEditData(data);
  };
  const toggleDrawerOpen = () => {
    setDrawerOpen(true);
  };

  const handleClickOpenPopup = () => {
    setDrawerOpen(false);
  };

  const toggleDrawerClose = useCallback(() => setDrawerOpen(false), []);

  const getRowKey = useCallback((row: ExamTimetableInfo) => row.examId, []);
  const handleDeleteTermFeeList = useCallback(
    async (termObj: any) => {
      const { termId } = termObj;
      const deleteConfirmMessage = (
        <DeleteMessage
          jsonIcon={deleteBin}
          message={
            <div>
              Are you sure you want to delete the Row <br />
              &quot;{termObj.termTitle}&quot; ?
            </div>
          }
        />
      );
      if (await confirm(deleteConfirmMessage, 'Delete Row?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
        const sendReq = [{ dbResult: 'string', deletedId: 0 }];

        console.log('sendReq', sendReq);

        const deleteResponse = await dispatch(deleteTermFeeList(sendReq));
        console.log('deleteResponse::::----', deleteResponse);
        if (deleteResponse && Array.isArray(deleteResponse.payload)) {
          const results: any[] = deleteResponse.payload;
          const errorMessages = results.find((result) => result.dbResult === 'Failed');
          const successMessages = results.find((result) => result.dbResult === 'Success');
          // Reload only the specific message template with the deleted messageId

          if (!errorMessages) {
            const deleteSuccessMessage = <SuccessMessage loop={false} message="Delete successfully" />;
            await confirm(deleteSuccessMessage, 'Success', { okLabel: 'Ok', showOnlyOk: true });
          } else if (!successMessages) {
            const deleteErrorMessage = <DeleteMessage message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          } else {
            const deleteErrorMessage = <DeleteMessage message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          }
          // loadTermFeeList({ ...currentTermFeeListRequest });

          // setSnackBar(true);
          // setTermFeeData((prevDetails) => prevDetails.filter((item) => item.termId !== termId));
        }
      }
    },
    [confirm]
  );

  const examListColumns: DataTableColumn<ExamTimetableInfo>[] = [
    {
      name: 'examId',
      headerLabel: 'Exam Id',
      dataKey: 'examId',
      sortable: true,
    },
    {
      name: 'className',
      dataKey: 'className',
      headerLabel: 'Class Name',
      sortable: true,
    },
    {
      name: 'examSubject',
      dataKey: 'examSubject',
      headerLabel: 'Subject',
    },
    {
      name: 'examDate',
      dataKey: 'examDate',
      headerLabel: 'Date',
    },
    {
      name: 'examTime',
      dataKey: 'examTime',
      headerLabel: 'Time',
    },
    {
      name: 'actions',
      headerLabel: 'Actions',
      renderCell: (row) => {
        return (
          <Stack direction="row" gap={1}>
            <IconButton onClick={toggleDrawerOpen} size="small" sx={{ padding: 0.5 }}>
              <ModeEditIcon />
            </IconButton>
            <IconButton onClick={handleDeleteTermFeeList} size="small" sx={{ padding: 0.5 }}>
              <DeleteIcon />
            </IconButton>
          </Stack>
        );
      },
    },
  ];

  return (
    <Page title="List">
      <ManageExamRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17}>
              Manage Exam
            </Typography>
            <Box pb={1} sx={{ flexShrink: 0 }}>
              <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                <SearchIcon />
              </IconButton>
              <Button sx={{ borderRadius: '20px' }} variant="outlined" size="small">
                <MdAdd size="20px" /> Create
              </Button>
            </Box>
          </Stack>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={3} alignItems="end">
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 300 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Exam Name
                      </Typography>
                      <TextField fullWidth placeholder="Enter Name" />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 300 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Status
                      </Typography>
                      <Autocomplete
                        options={status}
                        renderInput={(params) => <TextField {...params} placeholder="Select Status" />}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item lg="auto" xs={12} sm={6}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Paper className="card-table-container" sx={{ marginTop: '12px' }}>
              <DataTable columns={examListColumns} data={dummyExamData} getRowKey={getRowKey} fetchStatus="success" />
            </Paper>
          </div>
        </Card>
      </ManageExamRoot>

      <TemporaryDrawer onClose={toggleDrawerClose} Title="Add Timetable" state={drawerOpen} DrawerContent="" />
      <Popup
        size="xs"
        state={Delete}
        onClose={handleClickCloseDelete}
        popupContent={<DeleteMessage message="Are you sure want to delete?" />}
      />
      <TemporaryDrawer
        onClose={toggleDrawerClose}
        state={drawerOpen}
        Title="Create Exam Details"
        DrawerContent={<Create onClose={toggleDrawerClose} />}
      />
    </Page>
  );
}

export default ExamTimetable;
