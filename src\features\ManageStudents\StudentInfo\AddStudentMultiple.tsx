import { useEffect, useCallback, useRef, useState, FormEvent } from 'react';
import { GENDER_SELECT, STATUS_OPTIONS } from '@/config/Selection';
import { StudentCreateRow, StudentListInfo } from '@/types/StudentManagement';
import {
  Stack,
  Typography,
  Button,
  Table,
  TableHead,
  TableRow,
  Paper,
  TableBody,
  Grid,
  TableContainer,
  TextField,
  MenuItem,
  Select,
  IconButton,
  Card,
  Tooltip,
  Alert,
  Avatar,
  useTheme,
} from '@mui/material';
import { FormikErrors, useFormik } from 'formik';
import { MdArrowBack } from 'react-icons/md';
import styled from 'styled-components';
import { v4 as uuidv4 } from 'uuid';
import CloseIcon from '@mui/icons-material/Close';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import errorFailedIcon from '@/assets/ManageFee/ErrorIcon.json';
import Success from '@/assets/ManageFee/Success.json';
import Page from '@/components/shared/Page';
import ErrorIcon from '@mui/icons-material/Error';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { addNewClassMulti } from '@/store/Academics/ClassManagement/classManagement.thunks';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import * as Yup from 'yup';
import api from '@/api';
import BackButton from '@/components/shared/BackButton';
import useSettings from '@/hooks/useSettings';
import { keyframes } from '@emotion/react';
import dayjs from 'dayjs';
import DatePickers from '@/components/shared/Selections/DatePicker';
import { useAppSelector } from '@/hooks/useAppSelector';
import { getClassData, getStudentSubmitting, getYearData } from '@/config/storeSelectors';
import useAuth from '@/hooks/useAuth';
import { ClassListInfo } from '@/types/AcademicManagement';
import { addNewMultipleStudent } from '@/store/Students/studentManagement.thunks';
import { ErrorMessage } from '@/components/shared/Popup/ErrorMessage';
import ProfileImage from '@/components/shared/ProfileImage';

export type AddStudentMultipleProps = {
  onBackClick: () => void;
  studentDetail?: StudentListInfo;
};

export type AddStudentMultipleState = {
  students: StudentCreateRow[];
};

const AddStudentMultipleRoot = styled.div`
  padding: 1rem;

  .Card {
    height: calc(100vh - 160px);
    display: flex;
    flex-direction: column;

    form {
      height: calc(100% - 40px);
      display: flex;
      flex-direction: column;

      .form-container {
        flex-grow: 1;
        height: calc(100% - 84px);

        .MuiTableContainer-root {
          height: 100%;
          overflow: auto;
          overflow-x: hidden;
        }
      }

      .button-container {
        border-top: 1px solid #ddd;
      }
    }
    .icon-wrapper {
      position: absolute;
      bottom: 0px;
      right: 0px;
    }
    .date-feild {
      .MuiStack-root {
        width: 100%;
      }
    }
    @media screen and (max-width: 380px) {
      form {
        height: calc(100% - 70px);
      }
      .student_info_header {
        flex-wrap: wrap;
      }
    }
  }
`;

const rotateAnimation = keyframes`
  0% {
    transform: rotate(360deg);
  }
  100% {
    transform: rotate(0deg);
  }
`;
function AddStudentMultiple({ onBackClick, studentDetail }: AddStudentMultipleProps) {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const dispatch = useAppDispatch();
  const { confirm } = useConfirm();
  const { user } = useAuth();
  const adminId: number = user ? user.accountId : 0;
  const YearData = useAppSelector(getYearData);
  const [error, setError] = useState<string | null>(null);
  const scrollContainerRef = useRef<any>(null);
  const textBoxRefs = useRef<(HTMLInputElement | null)[]>([]);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [isAnimating, setIsAnimating] = useState(false);
  const [isSolid, setIsSolid] = useState(false);

  const ClassData = useAppSelector(getClassData);
  const isSubmitting = useAppSelector(getStudentSubmitting);

  const formValidationSchema = Yup.object({
    students: Yup.array().of(
      Yup.object({
        admissionNumber: Yup.string()
          .required('Please enter admission number')
          .test('exists', 'Admission number already used', async (val) => {
            try {
              if (val) {
                const existsResponse = await api.StudentManagement.StudentAdmNumberExists(val);
                return !existsResponse.data;
              }
              return true;
            } catch {
              return true;
            }
          }),
        studentName: Yup.string()
          .required('Student name is required')
          .min(2, 'Name must be at least 2 characters long'),
        classId: Yup.string()
          .oneOf(
            ClassData.map((item) => item.classId.toString()),
            'Please select a valid class'
          )
          .required('Please select a class'),
        academicId: Yup.string()
          .oneOf(
            YearData.map((item) => item.accademicId.toString()),
            'Please select a valid academic year'
          )
          .required('Please select an academic year'),
        studentGender: Yup.string().oneOf(['0', '1'], 'Please select a gender').required(),
        studentDOB: Yup.string().required('Please select the date of birth'),
        admissionDate: Yup.string().required('Please select the admission date'),
        studentBloodGroup: Yup.string().oneOf(
          ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'],
          'Please select a valid blood group'
        ),
        studentFatherName: Yup.string().required('Please enter father name'),
        studentFatherNumber: Yup.string()
          .required('Please enter father phone number')
          .matches(/^\d{10}$/, 'Phone number must be exactly 10 digits'),
      })
    ),
  });

  const AllClassOption = {
    classId: 0,
    className: 'Select Class',
    classDescription: 'string',
    classStatus: 1,
  };

  const classDataWithAllClass = [AllClassOption, ...ClassData];

  const initialRowKey = uuidv4();

  const defaultRow: StudentCreateRow = {
    rowKey: initialRowKey,
    // studentId: studentDetail.studentId,
    academicId: YearData[0]?.accademicId,
    classId: studentDetail.classId,
    studentName: studentDetail.studentName,
    admissionNumber: studentDetail.admissionNumber,
    admissionDate: studentDetail.admissionDate,
    studentGender: studentDetail.studentGender,
    studentDOB: studentDetail.studentDOB,
    studentLastStudied: studentDetail.studentLastStudied,
    studentBloodGroup: studentDetail.studentBloodGroup,
    studentBirthPlace: studentDetail.studentBirthPlace,
    studentNationality: studentDetail.studentNationality,
    studentMotherTongue: studentDetail.studentMotherTongue,
    studentReligion: studentDetail.studentReligion,
    studentCaste: studentDetail.studentCaste,
    studentPAddress: studentDetail.studentPAddress,
    studentCAddress: studentDetail.studentCAddress,
    studentFatherName: studentDetail.studentFatherName,
    studentFatherQualification: studentDetail.studentFatherQualification,
    studentFatherOccupation: studentDetail.studentFatherOccupation,
    studentFatherNumber: studentDetail.studentFatherNumber,
    studentEmailId: studentDetail.studentEmailId,
    studentMotherName: studentDetail.studentMotherName,
    studentMotherQualification: studentDetail.studentMotherQualification,
    studentMotherOccupation: studentDetail.studentMotherQualification,
    studentMotherNumber: studentDetail.studentMotherNumber,
    studentImage: studentDetail.studentImage,
    createdBy: adminId,
  };

  const { values, handleChange, handleBlur, handleSubmit, setFieldValue, touched, errors, resetForm } =
    useFormik<AddStudentMultipleState>({
      initialValues: {
        students: [defaultRow],
      },
      onSubmit: async (data) => {
        try {
          const response = await dispatch(addNewMultipleStudent(data.students)).unwrap();
          // const response = { inserted: 1 };

          if (response.inserted) {
            const successMessage = (
              <SuccessMessage
                loop={false}
                jsonIcon={Success}
                message={`${data.students.length} Students created successfully`}
              />
            );
            await confirm(successMessage, 'Students Created', { okLabel: 'Ok', showOnlyOk: true });
            resetForm();
            setFieldValue('students', []);
            if (response.inserted) {
              setError(null);
              onBackClick();
            } else {
              setError('Something went wrong in creating students');
            }
          } else {
            const errorMessage = (
              <ErrorMessage loop={false} jsonIcon={errorFailedIcon} message="Staff created failed" />
            );
            await confirm(errorMessage, 'Staff Create', { okLabel: 'Ok', showOnlyOk: true });
          }
        } catch {
          setError('Something went wrong in creating students');
          const errorMessage = <ErrorMessage loop={false} jsonIcon={errorFailedIcon} message="Staff created failed" />;
          await confirm(errorMessage, 'Staff Create', { okLabel: 'Ok', showOnlyOk: true });
        }
      },
      validateOnBlur: false,
      validationSchema: formValidationSchema,
      validate: (studentVals) => {
        const errorObj: any = {};
        studentVals.students.forEach(async (studentRow, rowIndex, arr) => {
          if (
            arr.some(
              (x, i) =>
                studentRow.admissionNumber !== '' && x.admissionNumber === studentRow.admissionNumber && i !== rowIndex
            )
          ) {
            if (!errorObj.students) {
              errorObj.students = [];
            }
            errorObj.students[rowIndex] = {};
            errorObj.students[rowIndex].admissionNumber = 'Duplicate adm no.';
          }
        });
        return errorObj;
      },
    });

  useEffect(() => {
    textBoxRefs.current[values.students.length - 1]?.focus();
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollTop = scrollContainerRef.current.scrollHeight;
    }
  }, [values.students.length]);

  const handleAddNewRow = () => {
    const newRow = {
      studentId: 0,
      academicId: YearData[0]?.accademicId,
      classId: 0,
      studentName: '',
      admissionNumber: '',
      admissionDate: '',
      studentGender: -2,
      studentDOB: '',
      studentLastStudied: '',
      studentBloodGroup: '',
      studentBirthPlace: '',
      studentNationality: '',
      studentMotherTongue: '',
      studentReligion: '',
      studentCaste: '',
      studentPAddress: '',
      studentCAddress: '',
      studentFatherName: '',
      studentFatherQualification: '',
      studentFatherOccupation: '',
      studentFatherNumber: '',
      studentEmailId: '',
      studentMotherName: '',
      studentMotherQualification: '',
      studentMotherOccupation: '',
      studentMotherNumber: '',
      studentImage: '',
      createdBy: adminId,
      rowKey: uuidv4(), // Ensure each row has a unique key
    };
    setFieldValue('students', [...values.students, newRow]);
  };

  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>, rowIndex: number) => {
    if (event.target.files && event.target.files.length > 0) {
      setIsAnimating(true); // Start the animation
      const file = event.target.files[0];
      const imageUrl = URL.createObjectURL(file);
      setSelectedImage(imageUrl);
      setFieldValue(`students[${rowIndex}].studentImage`, imageUrl);
      // Clear input value to allow re-uploading the same file
      event.target.value = '';
    }
  };

  const fileInputRefs = useRef<Array<HTMLInputElement | null>>([]);

  const setFileInputRef = useCallback((el: HTMLInputElement | null, rowIndex: number) => {
    fileInputRefs.current[rowIndex] = el;
  }, []);

  const handleAvatarClick = (rowIndex: number) => {
    fileInputRefs.current[rowIndex]?.click();
  };
  // const handleAvatarClick = () => {
  //   document.getElementById('imageInput')?.click();
  // };

  const handleRemoveImage = (rowIndex: number) => {
    setSelectedImage('');
    setFieldValue(`staff[${rowIndex}].staffImage`, '');
    setIsAnimating(false); // Stop the animation when image is removed
  };

  const handleRowDelete = (rowkey: string) => {
    const updatedRows = values.students.filter((x) => x.rowKey !== rowkey);
    setFieldValue('students', updatedRows);
  };

  const hasAdmNumberFieldError = (rowIndex: number) => {
    if (touched.students && touched.students.length > 0 && errors.students && errors.students.length > 0) {
      return (
        !!touched.students[rowIndex]?.admissionNumber &&
        !!(errors.students as FormikErrors<StudentCreateRow>[])[rowIndex]?.admissionNumber
      );
    }
    return false;
  };
  const getAdmNumberFieldError = (rowIndex: number) => {
    return (errors.students as FormikErrors<StudentCreateRow>[])[rowIndex]?.admissionNumber;
  };
  const hasStudentNameFieldError = (rowIndex: number) => {
    if (touched.students && touched.students.length > 0 && errors.students && errors.students.length > 0) {
      return (
        !!touched.students[rowIndex]?.studentName &&
        !!(errors.students as FormikErrors<StudentCreateRow>[])[rowIndex]?.studentName
      );
    }
    return false;
  };
  const getStudentNameFieldError = (rowIndex: number) => {
    return (errors.students as FormikErrors<StudentCreateRow>[])[rowIndex]?.studentName;
  };

  const hasAccademicYearFieldError = (rowIndex: number) => {
    if (touched.students && touched.students.length > 0 && errors.students && errors.students.length > 0) {
      return (
        !!touched.students[rowIndex]?.academicId &&
        !!(errors.students as FormikErrors<StudentCreateRow>[])[rowIndex]?.academicId
      );
    }
    return false;
  };
  const getAccademicYearFieldError = (rowIndex: number) => {
    return (errors.students as FormikErrors<StudentCreateRow>[])[rowIndex]?.academicId;
  };

  const hasClassFieldError = (rowIndex: number) => {
    if (touched.students && touched.students.length > 0 && errors.students && errors.students.length > 0) {
      return (
        !!touched.students[rowIndex]?.classId &&
        !!(errors.students as FormikErrors<StudentCreateRow>[])[rowIndex]?.classId
      );
    }
    return false;
  };
  const getClassFieldError = (rowIndex: number) => {
    return (errors.students as FormikErrors<StudentCreateRow>[])[rowIndex]?.classId;
  };

  const hasStudentGenderFieldError = (rowIndex: number) => {
    if (touched.students && touched.students.length > 0 && errors.students && errors.students.length > 0) {
      return (
        !!touched.students[rowIndex]?.studentGender &&
        !!(errors.students as FormikErrors<StudentCreateRow>[])[rowIndex]?.studentGender
      );
    }
    return false;
  };
  const getStudentGenderFieldError = (rowIndex: number) => {
    return (errors.students as FormikErrors<StudentCreateRow>[])[rowIndex]?.studentGender;
  };

  const hasStudentFatherNameFieldError = (rowIndex: number) => {
    if (touched.students && touched.students.length > 0 && errors.students && errors.students.length > 0) {
      return (
        !!touched.students[rowIndex]?.studentFatherName &&
        !!(errors.students as FormikErrors<StudentCreateRow>[])[rowIndex]?.studentFatherName
      );
    }
    return false;
  };
  const getStudentFatherNameFieldError = (rowIndex: number) => {
    return (errors.students as FormikErrors<StudentCreateRow>[])[rowIndex]?.studentFatherName;
  };

  const hasStudentFatherNumberFieldError = (rowIndex: number) => {
    if (touched.students && touched.students.length > 0 && errors.students && errors.students.length > 0) {
      return (
        !!touched.students[rowIndex]?.studentFatherNumber &&
        !!(errors.students as FormikErrors<StudentCreateRow>[])[rowIndex]?.studentFatherNumber
      );
    }
    return false;
  };
  const getStudentFatherNumberFieldError = (rowIndex: number) => {
    return (errors.students as FormikErrors<StudentCreateRow>[])[rowIndex]?.studentFatherNumber;
  };

  const handleReset = async () => {
    if (await confirm('Are you sure you want to reset form?', 'Reset?')) {
      resetForm();
      setFieldValue('students', [defaultRow]);
    }
  };

  const handleResetForm = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    handleReset();
  };

  return (
    <Page title="Create Multiple Classes">
      <AddStudentMultipleRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack
            mb={2}
            direction="row"
            alignItems="center"
            justifyContent="space-between"
            className="student_info_header"
            spacing={2}
          >
            <BackButton onBackClick={onBackClick} title="Information" />
            <Stack direction="row" justifyContent="end" width="100%">
              <Button
                type="button"
                variant="contained"
                size="small"
                sx={{ borderRadius: 10 }}
                startIcon={<AddIcon />}
                onClick={handleAddNewRow}
              >
                Add New Row
              </Button>
            </Stack>
          </Stack>
          <form noValidate onSubmit={handleSubmit} onReset={handleResetForm}>
            <div className="form-container">
              {!!error && (
                <Alert color="error" sx={{ marginBottom: '10px' }}>
                  {error}
                </Alert>
              )}

              <TableContainer component={Paper} ref={scrollContainerRef}>
                {values.students.map((studentRow, rowIndex) => (
                  <Card
                    key={studentRow.rowKey}
                    sx={{
                      border: 1,
                      mb: 2,
                      p: 3,
                      boxShadow: 0,
                      bgcolor: isLight ? theme.palette.common.white : theme.palette.grey[900],
                      borderColor: isLight ? theme.palette.grey[300] : theme.palette.grey[900],
                    }}
                  >
                    <Grid
                      container
                      columnSpacing={2}
                      rowSpacing={1}
                      sx={rowIndex === values.students.length - 1 ? { '& td': { border: 0 } } : null}
                    >
                      <Grid item xs={4} sm={6} md={6} lg={4} xl={1} textAlign="center">
                        <ProfileImage
                          width="65px"
                          height="65px"
                          selectedImage={studentRow.studentImage}
                          setFieldValue={setFieldValue}
                        />
                        {/* <IconButton
                          color="primary"
                          sx={{
                            width: 'fit-content',
                            p: 0,
                            position: 'relative',
                          }}
                        >
                          <Avatar
                            sx={{
                              backgroundColor: isLight ? theme.palette.common.white : theme.palette.grey[900],
                              color: theme.palette.primary.main,
                              cursor: 'pointer',
                              width: '55px',
                              height: '55px',
                              position: 'relative',
                              '&::before': {
                                content: '""',
                                position: 'absolute',
                                top: '0px',
                                left: '0px',
                                right: '0px',
                                bottom: '0px',
                                border: `2px ${theme.palette.primary.main}`,
                                // borderStyle: selectedImage && isSolid ? 'solid' : 'dashed',
                                borderStyle: isAnimating ? 'solid' : 'dashed',
                                borderRadius: '50%',
                                animation: isAnimating ? `${rotateAnimation} .5s linear` : 'none',
                              },
                            }}
                            alt="Student Avatar"
                            src={studentRow.studentImage}
                            onClick={() => handleAvatarClick(rowIndex)} // Trigger file input on click
                          />
                          {studentRow.studentImage ? (
                            <Tooltip title="Remove Image">
                              <CloseIcon
                                className="icon-wrapper"
                                sx={{
                                  fontSize: '15px',
                                  cursor: 'pointer',
                                  bgcolor: theme.palette.error.main,
                                  color: theme.palette.common.white,
                                  borderRadius: '50px',
                                }}
                                onClick={() => handleRemoveImage(rowIndex)} // Handle image removal
                              />
                            </Tooltip>
                          ) : (
                            <AddIcon
                              className="icon-wrapper"
                              sx={{
                                fontSize: '15px',
                                cursor: 'pointer',
                                bgcolor: theme.palette.primary.main,
                                color: theme.palette.common.white,
                                borderRadius: '50px',
                              }}
                              onClick={() => handleAvatarClick(rowIndex)} // Trigger file input on click
                            />
                          )}
                          <input
                            id="imageInput"
                            type="file"
                            accept="image/*"
                            style={{ display: 'none' }}
                            ref={(el) => setFileInputRef(el, rowIndex)}
                            name={`students[${rowIndex}].studentImage`}
                            onChange={(e) => handleImageChange(e, rowIndex)}
                          />
                        </IconButton> */}
                      </Grid>
                      <Grid item xs={8} sm={6} md={6} lg={4} xl={3} xxl={2}>
                        <Typography variant="subtitle2" fontSize={12}>
                          Adm No
                        </Typography>
                        <TextField
                          name={`students[${rowIndex}].admissionNumber`}
                          value={studentRow.admissionNumber}
                          variant="outlined"
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Enter adm no."
                          fullWidth
                          error={hasAdmNumberFieldError(rowIndex)}
                          inputRef={(el) => {
                            textBoxRefs.current[rowIndex] = el;
                          }}
                          InputProps={{
                            endAdornment: hasAdmNumberFieldError(rowIndex) && (
                              <Tooltip title={getAdmNumberFieldError(rowIndex)} arrow>
                                <ErrorIcon color="error" />
                              </Tooltip>
                            ),
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6} md={6} lg={4} xl={3} xxl={3}>
                        <Typography variant="subtitle2" fontSize={12}>
                          Student Name
                        </Typography>
                        <TextField
                          name={`students[${rowIndex}].studentName`}
                          value={studentRow.studentName}
                          variant="outlined"
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Enter student name"
                          fullWidth
                          error={hasStudentNameFieldError(rowIndex)}
                          inputRef={(el) => {
                            textBoxRefs.current[rowIndex] = el;
                          }}
                          InputProps={{
                            endAdornment: hasStudentNameFieldError(rowIndex) && (
                              <Tooltip title={getStudentNameFieldError(rowIndex)} arrow>
                                <ErrorIcon color="error" />
                              </Tooltip>
                            ),
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6} md={6} lg={4} xl={2.5} xxl={2} className="date-feild">
                        <Typography variant="subtitle2" fontSize={12}>
                          Date of Birth
                        </Typography>
                        <DatePickers
                          // width="340px "
                          name={`students[${rowIndex}].studentDOB`}
                          value={dayjs(studentRow.studentDOB, 'DD/MM/YYYY')}
                          onChange={(e) => {
                            const formattedDate = e ? e.format('DD/MM/YYYY') : '';
                            setFieldValue(`students[${rowIndex}].studentDOB`, formattedDate);
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6} md={6} lg={4} xl={2.5} xxl={2} className="date-feild">
                        <Typography variant="subtitle2" fontSize={12}>
                          Admission Date
                        </Typography>
                        <DatePickers
                          // width="340px "
                          name={`students[${rowIndex}].admissionDate`}
                          value={dayjs(studentRow.admissionDate, 'DD/MM/YYYY')}
                          onChange={(e) => {
                            const formattedDate = e ? e.format('DD/MM/YYYY') : '';
                            setFieldValue(`students[${rowIndex}].admissionDate`, formattedDate);
                          }}
                        />
                      </Grid>

                      <Grid item xs={12} sm={6} md={6} lg={4} xl={2} xxl={2}>
                        <Typography variant="subtitle2" fontSize={12}>
                          Academic Year
                        </Typography>
                        <Select
                          // disabled={isSubmitting}
                          fullWidth
                          name={`students[${rowIndex}].academicId`}
                          value={studentRow.academicId}
                          onChange={handleChange}
                          placeholder="Select Year"
                          error={hasAccademicYearFieldError(rowIndex)}
                          endAdornment={
                            hasAccademicYearFieldError(rowIndex) && (
                              <Tooltip title={getAccademicYearFieldError(rowIndex)} arrow>
                                <ErrorIcon color="error" style={{ marginLeft: 8 }} />
                              </Tooltip>
                            )
                          }
                        >
                          <MenuItem value={0} sx={{ display: 'none' }}>
                            Select Year
                          </MenuItem>
                          {YearData.map((opt) => (
                            <MenuItem key={opt.accademicId} value={opt.accademicId}>
                              {opt.accademicTime}
                            </MenuItem>
                          ))}
                        </Select>
                      </Grid>

                      <Grid item xs={12} sm={6} md={6} lg={4} xl={2} xxl={1.5}>
                        <Typography variant="subtitle2" fontSize={12}>
                          Class
                        </Typography>
                        <Select
                          fullWidth
                          name={`students[${rowIndex}].classId`}
                          value={studentRow.classId}
                          onChange={handleChange}
                          placeholder="Select Class"
                          error={hasClassFieldError(rowIndex)}
                          endAdornment={
                            hasClassFieldError(rowIndex) && (
                              <Tooltip title={getClassFieldError(rowIndex)} arrow>
                                <ErrorIcon color="error" style={{ marginLeft: 8 }} />
                              </Tooltip>
                            )
                          }
                        >
                          {classDataWithAllClass?.map((item: ClassListInfo) => (
                            <MenuItem sx={{ fontSize: '13px' }} key={item.classId} value={item.classId}>
                              {item.className}
                            </MenuItem>
                          ))}
                        </Select>
                      </Grid>

                      <Grid item xs={12} sm={6} md={6} lg={4} xl={1.5}>
                        <Typography variant="subtitle2" fontSize={12}>
                          Gender
                        </Typography>
                        <Select
                          fullWidth
                          name={`students[${rowIndex}].studentGender`}
                          value={studentRow.studentGender}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          error={hasStudentGenderFieldError(rowIndex)}
                          endAdornment={
                            hasStudentGenderFieldError(rowIndex) && (
                              <Tooltip title={getStudentGenderFieldError(rowIndex)} arrow>
                                <ErrorIcon color="error" style={{ marginLeft: 8 }} />
                              </Tooltip>
                            )
                          }
                        >
                          <MenuItem value={-2}>Select Gender</MenuItem>
                          {GENDER_SELECT.map((opt) => (
                            <MenuItem key={opt.id} value={opt.id}>
                              {opt.gender}
                            </MenuItem>
                          ))}
                        </Select>
                      </Grid>
                      <Grid item xs={12} sm={6} md={6} lg={4} xl={2.5} xxl={3}>
                        <Typography variant="subtitle2" fontSize={12}>
                          Birth Place
                        </Typography>
                        <TextField
                          name={`students[${rowIndex}].studentBirthPlace`}
                          value={studentRow.studentBirthPlace}
                          variant="outlined"
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Enter birth place"
                          fullWidth
                          inputRef={(el) => {
                            textBoxRefs.current[rowIndex] = el;
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6} md={6} lg={4} xl={1.5} xxl={2}>
                        <Typography variant="subtitle2" fontSize={12}>
                          Blood Group
                        </Typography>
                        <TextField
                          name={`students[${rowIndex}].studentBloodGroup`}
                          value={studentRow.studentBloodGroup}
                          variant="outlined"
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Enter blood group"
                          fullWidth
                          inputRef={(el) => {
                            textBoxRefs.current[rowIndex] = el;
                          }}
                        />
                      </Grid>

                      <Grid item xs={12} sm={6} md={6} lg={4} xl={2.5} xxl={2}>
                        <Typography variant="subtitle2" fontSize={12}>
                          Nationality
                        </Typography>
                        <TextField
                          name={`students[${rowIndex}].studentNationality`}
                          value={studentRow.studentNationality}
                          variant="outlined"
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Enter nationality"
                          fullWidth
                          inputRef={(el) => {
                            textBoxRefs.current[rowIndex] = el;
                          }}
                        />
                      </Grid>

                      <Grid item xs={12} sm={6} md={6} lg={4} xl={2}>
                        <Typography variant="subtitle2" fontSize={12}>
                          Mother Tongue
                        </Typography>
                        <TextField
                          name={`students[${rowIndex}].studentMotherTongue`}
                          value={studentRow.studentMotherTongue}
                          variant="outlined"
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Enter  mother tongue"
                          fullWidth
                          inputRef={(el) => {
                            textBoxRefs.current[rowIndex] = el;
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6} md={6} lg={4} xl={1.5}>
                        <Typography variant="subtitle2" fontSize={12}>
                          Religion
                        </Typography>
                        <TextField
                          name={`students[${rowIndex}].studentReligion`}
                          value={studentRow.studentReligion}
                          variant="outlined"
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Contact Name"
                          fullWidth
                          inputRef={(el) => {
                            textBoxRefs.current[rowIndex] = el;
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6} md={6} lg={4} xl={1.5} xxl={1.5}>
                        <Typography variant="subtitle2" fontSize={12}>
                          Caste
                        </Typography>
                        <TextField
                          name={`students[${rowIndex}].studentCaste`}
                          value={studentRow.studentCaste}
                          variant="outlined"
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Enter caste"
                          fullWidth
                          inputRef={(el) => {
                            textBoxRefs.current[rowIndex] = el;
                          }}
                        />
                      </Grid>

                      <Grid item xs={12} sm={6} md={6} lg={4} xl={2} xxl={3}>
                        <Typography variant="subtitle2" fontSize={12}>
                          Father Name
                        </Typography>
                        <TextField
                          name={`students[${rowIndex}].studentFatherName`}
                          value={studentRow.studentFatherName}
                          variant="outlined"
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Enter father name"
                          fullWidth
                          error={hasStudentFatherNameFieldError(rowIndex)}
                          inputRef={(el) => {
                            textBoxRefs.current[rowIndex] = el;
                          }}
                          InputProps={{
                            endAdornment: hasStudentFatherNameFieldError(rowIndex) && (
                              <Tooltip title={getStudentFatherNameFieldError(rowIndex)} arrow>
                                <ErrorIcon color="error" />
                              </Tooltip>
                            ),
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6} md={6} lg={4} xl={2} xxl={2}>
                        <Typography variant="subtitle2" fontSize={12}>
                          Father Number
                        </Typography>
                        <TextField
                          name={`students[${rowIndex}].studentFatherNumber`}
                          value={studentRow.studentFatherNumber}
                          variant="outlined"
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Enter father number"
                          fullWidth
                          error={hasStudentFatherNumberFieldError(rowIndex)}
                          inputRef={(el) => {
                            textBoxRefs.current[rowIndex] = el;
                          }}
                          InputProps={{
                            endAdornment: hasStudentFatherNumberFieldError(rowIndex) && (
                              <Tooltip title={getStudentFatherNumberFieldError(rowIndex)} arrow>
                                <ErrorIcon color="error" />
                              </Tooltip>
                            ),
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6} md={6} lg={4} xl={2}>
                        <Typography variant="subtitle2" fontSize={12}>
                          Father Qualification
                        </Typography>
                        <TextField
                          name={`students[${rowIndex}].studentFatherQualification`}
                          value={studentRow.studentFatherQualification}
                          variant="outlined"
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Enter father qualification"
                          fullWidth
                          inputRef={(el) => {
                            textBoxRefs.current[rowIndex] = el;
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6} md={6} lg={4} xl={2}>
                        <Typography variant="subtitle2" fontSize={12}>
                          Father Occupation
                        </Typography>
                        <TextField
                          name={`students[${rowIndex}].studentFatherOccupation`}
                          value={studentRow.studentFatherOccupation}
                          variant="outlined"
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Enter father occupation"
                          fullWidth
                          inputRef={(el) => {
                            textBoxRefs.current[rowIndex] = el;
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6} md={6} lg={4} xl={2} xxl={3}>
                        <Typography variant="subtitle2" fontSize={12}>
                          Last Studied
                        </Typography>
                        <TextField
                          name={`students[${rowIndex}].studentLastStudied`}
                          value={studentRow.studentLastStudied}
                          variant="outlined"
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Enter last studied"
                          fullWidth
                          inputRef={(el) => {
                            textBoxRefs.current[rowIndex] = el;
                          }}
                        />
                      </Grid>

                      <Grid item xs={12} sm={6} md={6} lg={4} xl={3}>
                        <Typography variant="subtitle2" fontSize={12}>
                          Mother Name
                        </Typography>
                        <TextField
                          name={`students[${rowIndex}].studentMotherName`}
                          value={studentRow.studentMotherName}
                          variant="outlined"
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Enter mother name"
                          fullWidth
                          inputRef={(el) => {
                            textBoxRefs.current[rowIndex] = el;
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6} md={6} lg={4} xl={2}>
                        <Typography variant="subtitle2" fontSize={12}>
                          Mother Number
                        </Typography>
                        <TextField
                          name={`students[${rowIndex}].studentMotherNumber`}
                          value={studentRow.studentMotherNumber}
                          variant="outlined"
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Enter mother number"
                          fullWidth
                          inputRef={(el) => {
                            textBoxRefs.current[rowIndex] = el;
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6} md={6} lg={4} xl={2}>
                        <Typography variant="subtitle2" fontSize={12}>
                          Mother Qualification
                        </Typography>
                        <TextField
                          name={`students[${rowIndex}].studentMotherQualification`}
                          value={studentRow.studentMotherQualification}
                          variant="outlined"
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Enter mother qualification"
                          fullWidth
                          inputRef={(el) => {
                            textBoxRefs.current[rowIndex] = el;
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6} md={6} lg={4} xl={2}>
                        <Typography variant="subtitle2" fontSize={12}>
                          Mother Occupation
                        </Typography>
                        <TextField
                          name={`students[${rowIndex}].studentMotherOccupation`}
                          value={studentRow.studentMotherOccupation}
                          variant="outlined"
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Enter mother occupation"
                          fullWidth
                          inputRef={(el) => {
                            textBoxRefs.current[rowIndex] = el;
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6} md={6} lg={4} xl={2} xxl={3}>
                        <Typography variant="subtitle2" fontSize={12}>
                          Email Id
                        </Typography>
                        <TextField
                          name={`students[${rowIndex}].studentEmailId`}
                          value={studentRow.studentEmailId}
                          variant="outlined"
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Enter email id"
                          fullWidth
                          inputRef={(el) => {
                            textBoxRefs.current[rowIndex] = el;
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6} md={6} lg={4} xl={2} xxl={5}>
                        <Typography variant="subtitle2" fontSize={12}>
                          Permanent Address
                        </Typography>
                        <TextField
                          name={`students[${rowIndex}].studentPAddress`}
                          value={studentRow.studentPAddress}
                          variant="outlined"
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Enter permanent address"
                          fullWidth
                          inputRef={(el) => {
                            textBoxRefs.current[rowIndex] = el;
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6} md={6} lg={4} xl={2} xxl={4}>
                        <Typography variant="subtitle2" fontSize={12}>
                          Current Address
                        </Typography>
                        <TextField
                          name={`students[${rowIndex}].studentCAddress`}
                          value={studentRow.studentCAddress}
                          variant="outlined"
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Enter current address"
                          fullWidth
                          inputRef={(el) => {
                            textBoxRefs.current[rowIndex] = el;
                          }}
                        />
                      </Grid>
                    </Grid>
                    <Stack position="absolute" right={3} top={3}>
                      <IconButton
                        aria-label="Delete Row"
                        color="error"
                        size="small"
                        onClick={() => handleRowDelete(studentRow.rowKey)}
                        disabled={values.students.length === 1}
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </Stack>
                  </Card>
                ))}
              </TableContainer>
            </div>
            <Stack className="button-container" direction="row" justifyContent="end" gap={1} pt={1}>
              <Button type="button" color="secondary" variant="contained" onClick={handleReset}>
                Reset
              </Button>
              <Button color="primary" variant="contained" type="submit">
                Save
              </Button>
            </Stack>
          </form>
        </Card>
      </AddStudentMultipleRoot>
    </Page>
  );
}

export default AddStudentMultiple;
