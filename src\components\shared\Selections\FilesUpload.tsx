/* eslint-disable no-nested-ternary */
import * as React from 'react';
import Button from '@mui/material/Button';
import { Box, useTheme, Stack, Typography } from '@mui/material';
import useSettings from '@/hooks/useSettings';
import { TbCloudUpload } from 'react-icons/tb';

type UploadPropsTypes = {
  onChange?: (e: any) => void;
  setUploaded?: File[];
  accept: string;
  name: string;
  multiple?: boolean;
  disabled?: boolean;
  title?: string;
  height?: string | number;
  error?: boolean | undefined;
  helperText?: string | boolean | undefined;
  backgroundColorDisabled?: boolean;
};

export default function FilesUpload({
  onChange,
  setUploaded,
  accept,
  name,
  multiple,
  disabled,
  title,
  height,
  error,
  helperText,
  backgroundColorDisabled,
}: UploadPropsTypes) {
  const theme = useTheme();
  const isLight = useSettings().themeMode === 'light';
  // const [files, setFiles] = React.useState([]);

  // const handleFileChange = (event: React.ChangeEvent<HTMLInputElement> | React.DragEvent<HTMLDivElement>) => {
  //   let uploadFiles;

  //   if ('dataTransfer' in event) {
  //     event.preventDefault();
  //     uploadFiles = event.dataTransfer.files;
  //   } else {
  //     uploadFiles = event.target.files;
  //   }
  //   if (uploadFiles && uploadFiles.length > 0) {
  //     const selectedFiles = Array.from(event.target.files);
  //     setUploaded((prevFiles) => [...prevFiles, ...selectedFiles]);
  //   }
  //   onChange(event);
  // };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    onChange(event);
  };
  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    onChange(event);
  };

  return (
    <Box>
      <Box
        sx={{
          height,
          border: `2px dashed ${theme.palette.grey[300]}`,
          borderRadius: 1,
          backgroundColor:
            !backgroundColorDisabled && !disabled
              ? isLight
                ? theme.palette.primary.lighter
                : theme.palette.grey[900]
              : theme.palette.grey[100],
        }}
      >
        <div
          style={{
            width: '100%',
            height: '100%',
            display: 'flex',
            justifyContent: 'center',
            flexDirection: 'column',
          }}
          onDragOver={handleDragOver}
          onDrop={handleDrop}
        >
          <Button
            fullWidth
            sx={{
              '&.MuiButton-root:hover': {
                backgroundColor: 'transparent',
              },
            }}
            color="primary"
            aria-label="upload files"
            component="label"
            disableTouchRipple
          >
            <input
              type="file"
              disabled={disabled}
              hidden
              multiple={multiple}
              accept={accept}
              onChange={onChange}
              name={name}
            />
            <Stack alignItems="center">
              <TbCloudUpload fontSize={50} />
              <Typography variant="h6" fontSize={12}>
                Choose Files
              </Typography>
              <Typography variant="h6" fontSize={13} color="secondary">
                or drag and drop
              </Typography>
            </Stack>
          </Button>
          {/* <Stack mx={5}>{showProgress && <PrgressUploadImage />}</Stack> */}
          <Typography align="center" color="secondary" fontSize={12} px={5}>
            {title}
          </Typography>
        </div>
      </Box>
      <Typography variant="subtitle1" color="error" fontSize={12} mt={1} ml={2.5}>
        {/* {touched.videoFile && errors.videoFile} */}
      </Typography>
      {/* <Typography variant="subtitle1" color="error" fontSize={12} ml={2.5}>
    {touched.imageFile && !!errors.imageFile}
    {errors.imageFile}
  </Typography> */}
      {error && (
        <Typography variant="subtitle1" color="error" fontSize={12} mt={1} ml={2.5}>
          {error}
        </Typography>
      )}
      {helperText && (
        <Typography variant="subtitle1" color="error" fontSize={12} mt={1} ml={2.5}>
          {helperText}
        </Typography>
      )}
    </Box>
  );
}
