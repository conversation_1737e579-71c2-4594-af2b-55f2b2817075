/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Typography,
  Card,
  Avatar,
  IconButton,
  Collapse,
  Select,
  MenuItem,
  FormControl,
  useTheme,
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import { IoIosArrowUp } from 'react-icons/io';
import { BsFiletypeXls } from 'react-icons/bs';
import styled from 'styled-components';
import { CLASS_SELECT, STATUS_OPTIONS, YEAR_SELECT } from '@/config/Selection';
import { StudentInfoData } from '@/config/StudentDetails';
import DateSelect from '@/components/shared/Selections/DateSelect';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import DatePickers from '@/components/shared/Selections/DatePicker';
import { Tooltip } from '@mui/material';

const Report = [
  {
    slNo: 1,
    'adm.No': 'ADM001',
    classDiv: '10A',
    admissionFee: 5000,
    studentName: 'John Doe',
    formFee: 200,
    lateFeeTermI: 50,
    lateFeeTermII: 75,
    lateFeeTermIII: 100,
  },
  {
    slNo: 2,
    'adm.No': 'ADM002',
    classDiv: '9B',
    admissionFee: 4500,
    studentName: 'Jane Smith',
    formFee: 180,
    lateFeeTermI: 40,
    lateFeeTermII: 60,
    lateFeeTermIII: 90,
  },
  {
    slNo: 3,
    'adm.No': 'ADM003',
    classDiv: '11C',
    admissionFee: 6000,
    studentName: 'Alice Johnson',
    formFee: 220,
    lateFeeTermI: 55,
    lateFeeTermII: 80,
    lateFeeTermIII: 110,
  },
  {
    slNo: 4,
    'adm.No': 'ADM004',
    classDiv: '8D',
    admissionFee: 4200,
    studentName: 'Michael Williams',
    formFee: 160,
    lateFeeTermI: 35,
    lateFeeTermII: 55,
    lateFeeTermIII: 80,
  },
  {
    slNo: 5,
    'adm.No': 'ADM005',
    classDiv: '12E',
    admissionFee: 7000,
    studentName: 'Emily Brown',
    formFee: 250,
    lateFeeTermI: 60,
    lateFeeTermII: 90,
    lateFeeTermIII: 120,
  },
  {
    slNo: 6,
    'adm.No': 'ADM006',
    classDiv: '7F',
    admissionFee: 3800,
    studentName: 'David Lee',
    formFee: 140,
    lateFeeTermI: 30,
    lateFeeTermII: 45,
    lateFeeTermIII: 70,
  },
  {
    slNo: 7,
    'adm.No': 'ADM007',
    classDiv: '9G',
    admissionFee: 4500,
    studentName: 'Sophia Taylor',
    formFee: 180,
    lateFeeTermI: 40,
    lateFeeTermII: 60,
    lateFeeTermIII: 90,
  },
  {
    slNo: 8,
    'adm.No': 'ADM008',
    classDiv: '10H',
    admissionFee: 5000,
    studentName: 'William Anderson',
    formFee: 200,
    lateFeeTermI: 50,
    lateFeeTermII: 75,
    lateFeeTermIII: 100,
  },
  {
    slNo: 9,
    'adm.No': 'ADM009',
    classDiv: '11I',
    admissionFee: 6000,
    studentName: 'Olivia Martinez',
    formFee: 220,
    lateFeeTermI: 55,
    lateFeeTermII: 80,
    lateFeeTermIII: 110,
  },
  {
    slNo: 10,
    'adm.No': 'ADM010',
    classDiv: '8J',
    admissionFee: 4200,
    studentName: 'Daniel Garcia',
    formFee: 160,
    lateFeeTermI: 35,
    lateFeeTermII: 55,
    lateFeeTermIII: 80,
  },
];


const OptionalFeeReportRoot = styled.div`
  padding: 1rem;
  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    /* @media screen and (max-width: 996px) {
      height: 100%;
    } */

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 50px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

const array = [
  {
    slNo: 1,
    feeTitle: 'Tution Fee',
    total: '10000',
    paid: '6800',
    pending: '5700',
    discount: '0.00',
    scholarship: '0.00',
    balance: '5700',
  },
  {
    slNo: 2,
    feeTitle: 'Tution Fee',
    total: '10000',
    paid: '6800',
    pending: '5700',
    discount: '0.00',
    scholarship: '0.00',
    balance: '5700',
  },
  {
    slNo: 3,
    feeTitle: 'Tution Fee',
    total: '10000',
    paid: '6800',
    pending: '5700',
    discount: '0.00',
    scholarship: '0.00',
    balance: '5700',
  },
  {
    slNo: 4,
    feeTitle: 'Tution Fee',
    total: '10000',
    paid: '6800',
    pending: '5700',
    discount: '0.00',
    scholarship: '0.00',
    balance: '5700',
  },
  {
    slNo: 5,
    feeTitle: 'Tution Fee',
    total: '10000',
    paid: '6800',
    pending: '5700',
    discount: '0.00',
    scholarship: '0.00',
    balance: '5700',
  },
];

export default function OptionalFeeReport() {
  const theme = useTheme();
  const [showFilter, setShowFilter] = useState(false);
  const OptionalFeeReportListColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'slNo',
        headerLabel: 'Sl.No',
        dataKey: 'slNo',
        sortable: true,
      },
      {
        name: 'studentName',
        headerLabel: 'Student Name',
        dataKey: 'studentName',
      },
      {
        name: 'adm.No',
        headerLabel: 'Adm.No',
        dataKey: 'adm.No',
      },
      {
        name: 'classDiv',
        dataKey: 'classDiv',
        headerLabel: 'Class-Div',
      },
      {
        name: 'admissionFee',
        headerLabel: 'Admission Fee',
        dataKey: 'admissionFee',
      },
      {
        name: 'formFee',
        dataKey: 'formFee',
        headerLabel: 'Form Fee',
      },
      {
        name: 'lateFeeTermI',
        dataKey: 'lateFeeTermI',
        headerLabel: 'Late Fee Term I',
      },
      {
        name: 'lateFeeTermII',
        headerLabel: 'Late Fee Term II',
        dataKey: 'lateFeeTermII',
      },
      {
        name: 'lateFeeTermIII',
        headerLabel: 'Late Fee Term III',
        dataKey: 'lateFeeTermIII',
      },
    ],
    []
  );

  const getRowKey = useCallback((row: any) => row.classId, []);
  return (
    <Page title="Fees Collection">
      <OptionalFeeReportRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between" mb={2} alignItems="center">
            <Typography variant="h6" fontSize={17}>
              Optional Fee Report
            </Typography>
            <Stack direction="row" alignItems="center" gap={1}>
              <Button startIcon={<BsFiletypeXls />} variant="contained" color="warning">
                Download
              </Button>
              {showFilter === false ? (
                <Tooltip title="Search">
                  <IconButton aria-label="delete" color="primary" onClick={() => setShowFilter((x) => !x)}>
                    <SearchIcon />
                  </IconButton>
                </Tooltip>
              ) : (
                <IconButton aria-label="delete" color="primary" onClick={() => setShowFilter((x) => !x)}>
                  <IoIosArrowUp />
                </IconButton>
              )}
            </Stack>
          </Stack>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={4} container spacing={2} alignItems="end">
                  <Grid item lg={2} md={6} sm={6} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Select labelId="classStatusFilter" id="classStatusFilterSelect" value="">
                        <MenuItem value={-1}>All</MenuItem>
                        {STATUS_OPTIONS.map((opt) => (
                          <MenuItem key={opt.id} value={opt.id}>
                            {opt.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item lg={2} md={6} sm={6} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Standard
                      </Typography>
                      <Select labelId="classStatusFilter" id="classStatusFilterSelect" value="">
                        <MenuItem value={-1}>All</MenuItem>
                        {STATUS_OPTIONS.map((opt) => (
                          <MenuItem key={opt.id} value={opt.id}>
                            {opt.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item lg={2} md={6} sm={6} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Division
                      </Typography>
                      <Select labelId="classStatusFilter" id="classStatusFilterSelect" value="">
                        <MenuItem value={-1}>All</MenuItem>
                        {STATUS_OPTIONS.map((opt) => (
                          <MenuItem key={opt.id} value={opt.id}>
                            {opt.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item lg={2} md={6} sm={6} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Optional Fee
                      </Typography>
                      <Select labelId="classStatusFilter" id="classStatusFilterSelect" value="">
                        <MenuItem value={-1}>All</MenuItem>
                        {STATUS_OPTIONS.map((opt) => (
                          <MenuItem key={opt.id} value={opt.id}>
                            {opt.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item lg={2} md={6} sm={6} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Month
                      </Typography>
                      <TextField id="" label="" />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto">
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      {/* <Button type="submit" variant="contained" color="primary" fullWidth>
                        Search
                      </Button> */}
                      <Button type="reset" variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>
            <Paper className="card-table-container" sx={{ marginTop: '12px' }}>
              <DataTable
                tableStyles={{ width: { xs: '900px', xxl: '100%' } }}
                columns={OptionalFeeReportListColumns}
                data={Report}
                getRowKey={getRowKey}
                fetchStatus="success"
              />
            </Paper>
          </div>
        </Card>
      </OptionalFeeReportRoot>
      {/* <TemporaryDrawer
        onClose={toggleDrawerClose}
        state={drawerOpen}
        DrawerContent={<EditList onClose={toggleDrawerClose} open={handleClickOpen} />}
      /> */}
      {/* <Popup size="md" state={popup} onClose={handleClickClose} popupContent={<EditedTable />} />
      <Popup
        size="xs"
        state={Delete}
        onClose={handleClickCloseDelete}
        popupContent={<DeleteMessage message="Are you sure want to delete?" />}
      /> */}
    </Page>
  );
}
