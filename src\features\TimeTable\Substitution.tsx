/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Card,
  IconButton,
} from '@mui/material';
import styled from 'styled-components';
import { CLASS_SELECT, YEAR_SELECT } from '@/config/Selection';
import typography from '@/theme/typography';
import Popup from '@/components/shared/Popup/Popup';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import { STAFF_DATA, TIME_TABLE_DATA } from '@/config/TableData';
import DeleteIcon from '@mui/icons-material/Delete';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';

const SubstitutionRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  h6 {
    font-weight: 600;
  }
  .MuiTextField-root:hover .MuiInputBase-input {
    cursor: text;
  }
  /* .Card {
    min-height: calc(100vh - 500px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }
  } */
`;

function Substitution() {
  const [popup, setPopup] = React.useState(false);

  const handleClickOpen = () => setPopup(true);
  const handleClickClose = () => setPopup(false);

  const [popup2, setPopup2] = React.useState(false);

  const handleClickOpen2 = () => setPopup2(true);
  const handleClickClose2 = () => setPopup2(false);

  const [deletepopup, setDeletepopup] = React.useState(false);

  const handleDeleteOpen = () => setDeletepopup(true);
  const handleDeleteClose = () => setDeletepopup(false);

  const Subsitute = STAFF_DATA.reverse().map((row) => row.name);
  const Timetableclass = TIME_TABLE_DATA.map((row) => row.class);

  return (
    <Page title="Substitution List">
      <SubstitutionRoot>
        <Card elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Typography variant="h6" fontSize={17}>
            Staff Substitution
          </Typography>
          <Divider />
          <Grid pb={4} pt={2} container spacing={3}>
            <Grid item lg={6} xs={12}>
              <Grid pt={2} container spacing={3}>
                <Grid item xs={12}>
                  <Typography variant="h6" fontSize={14}>
                    Absent Staff
                  </Typography>
                  <Autocomplete
                    options={YEAR_SELECT}
                    renderInput={(params) => <TextField {...params} placeholder="Select" />}
                  />
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="h6" fontSize={14}>
                    Class
                  </Typography>
                  <Autocomplete
                    options={CLASS_SELECT}
                    renderInput={(params) => <TextField {...params} placeholder="Select" />}
                  />
                </Grid>
              </Grid>
            </Grid>
            <Grid item lg={6} xs={12}>
              <Grid pt={{ lg: 2, xs: 0 }} pb={4} container spacing={3}>
                <Grid item xs={12}>
                  <Typography variant="h6" fontSize={14}>
                    Period
                  </Typography>
                  <Autocomplete
                    options={YEAR_SELECT}
                    renderInput={(params) => <TextField {...params} placeholder="Select" />}
                  />
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="h6" fontSize={14}>
                    Available Staff
                  </Typography>
                  <Autocomplete
                    options={CLASS_SELECT}
                    renderInput={(params) => <TextField {...params} placeholder="Select" />}
                  />
                </Grid>
              </Grid>
            </Grid>
          </Grid>
          <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' } }}>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="secondary" sx={{ fontFamily: typography.fontFamily }}>
                Cancel
              </Button>
              <Button onClick={handleClickOpen} variant="contained" color="primary">
                Submit
              </Button>
            </Stack>
          </Box>
        </Card>

        <Card className="Card" elevation={5} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 }, mt: 3 }}>
          <Typography variant="h6" fontSize={17}>
            Substituted List
          </Typography>
          <Divider />
          <Grid pb={4} container spacing={3} pt={2}>
            <Grid item lg={3} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Select Year
              </Typography>
              <Autocomplete
                options={YEAR_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select" />}
              />
            </Grid>
            <Grid item lg={3} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Select Class
              </Typography>
              <Autocomplete
                options={CLASS_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select" />}
              />
            </Grid>
            <Grid item lg={3} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Select Period
              </Typography>
              <Autocomplete
                options={['1', '2', '3', '4', '5', '6', '7', '8']}
                renderInput={(params) => <TextField {...params} placeholder="Select" />}
              />
            </Grid>
            <Grid item lg={3} xs={12}>
              <Stack spacing={2} sx={{ pt: { xs: 0, md: 3.79 } }} direction="row">
                <Button onClick={() => {}} variant="contained" color="secondary" fullWidth>
                  Reset
                </Button>
                <Button variant="contained" color="primary" fullWidth>
                  Search
                </Button>
              </Stack>
            </Grid>
          </Grid>
          <Paper
            sx={{
              border: `1px solid #e8e8e9`,
              width: '100%',
              height: '100%',
              overflow: 'auto',
              '&::-webkit-scrollbar': {
                height: 0,
              },
            }}
          >
            <TableContainer
              sx={{
                width: { xs: '650px', md: '100%' },
              }}
            >
              <Table stickyHeader aria-labelledby="tableTitle">
                <TableHead>
                  <TableCell>Sl No.</TableCell>
                  <TableCell>Absent Staff</TableCell>
                  <TableCell>Sustitute Staff</TableCell>
                  <TableCell>Class</TableCell>
                  <TableCell>Period</TableCell>
                  <TableCell>Date</TableCell>
                  <TableCell>By</TableCell>
                  <TableCell>Action</TableCell>
                </TableHead>
                <TableBody>
                  {STAFF_DATA.reverse().map((row, index) => {
                    return (
                      <TableRow>
                        <TableCell sx={{ fontWeight: 600 }} width="10%">
                          {index + 1}
                        </TableCell>
                        <TableCell sx={{ fontWeight: 600 }}>{row.name}</TableCell>
                        <TableCell sx={{ fontWeight: 600 }}>{Subsitute[index]}</TableCell>
                        <TableCell sx={{ fontWeight: 600 }}>{Timetableclass[index]}</TableCell>
                        <TableCell sx={{ fontWeight: 600 }}>{index + 1}</TableCell>
                        <TableCell sx={{ fontWeight: 600 }}>02/06/23</TableCell>
                        <TableCell sx={{ fontWeight: 600 }}>Passdaily Admin</TableCell>
                        <TableCell width="20%">
                          <Stack direction="row" gap={1} maxHeight={0.8}>
                            <IconButton size="small" onClick={handleDeleteOpen}>
                              <DeleteIcon />
                            </IconButton>
                            <Button variant="outlined" size="small" onClick={handleClickOpen2}>
                              Send Reminder
                            </Button>
                          </Stack>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        </Card>
        <Popup
          size="xs"
          state={popup}
          onClose={handleClickClose}
          popupContent={<SuccessMessage message="Substitute Added Successfully" />}
        />
        <Popup
          size="xs"
          state={popup2}
          onClose={handleClickClose2}
          popupContent={<SuccessMessage message="Reminder Sent Successfully" />}
        />
        <Popup
          size="xs"
          state={deletepopup}
          onClose={handleDeleteClose}
          popupContent={<DeleteMessage message="Are you sure want to delete?" />}
        />
      </SubstitutionRoot>
    </Page>
  );
}

export default Substitution;
