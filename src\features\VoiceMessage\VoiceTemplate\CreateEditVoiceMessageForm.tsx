/* eslint-disable no-else-return */
import React, { useState } from 'react';
import {
  Box,
  Button,
  FormControl,
  MenuItem,
  Select,
  Stack,
  TextField,
  IconButton,
  Typography,
  useTheme,
} from '@mui/material';
import { STATUS_OPTIONS } from '@/config/Selection';

import { useFormik } from 'formik';
import * as Yup from 'yup';
import AudioPlayer from 'react-h5-audio-player';
import CloseIcon from '@mui/icons-material/Close';
import { ErrorIcon } from '@/theme/overrides/CustomIcons';
import LoadingButton from '@mui/lab/LoadingButton';
import { VoiceCreateRequest } from '@/types/VoiceMessage';
import FilesUpload from '@/components/shared/Selections/FilesUpload';
import useSettings from '@/hooks/useSettings';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { fileUpload } from '@/store/VoiceMessage/voiceMessage.thunk';

export type CreateEditNotificationFormProps = {
  onSave: (values: VoiceCreateRequest, mode: 'create' | 'edit') => void;
  onCancel: (mode: 'create' | 'edit') => void;
  voiceMessageDetails: VoiceCreateRequest;
  isSubmitting: boolean;
  adminId: number | undefined;
  form?: string;
};

export const CreateEditNotificationForm = ({
  form,
  voiceMessageDetails,
  onCancel,
  isSubmitting,
  onSave,
  adminId,
}: CreateEditNotificationFormProps) => {
  const mode = voiceMessageDetails.voiceId === 0 ? 'create' : 'edit';
  const [uploaded, setUploaded] = useState<File[]>([]);
  const theme = useTheme();
  const isLight = useSettings().themeMode === 'light';
  const dispatch = useAppDispatch();

  const CreateEditVoiceMessageValidationSchema = Yup.object({
    voiceTitle: Yup.string().required('Please enter Title'),
    voiceStatus: Yup.number().oneOf([0, 1], 'Please select status'),
    voiceFile: Yup.mixed()
      .test('fileCount', 'Please upload only one file', (value) => !value || uploaded.length <= 1)
      .test(
        'fileSize',
        'File size is too large (maximum size is 5 MB)',
        (value) => !value || value.size <= 5 * 1024 * 1024 // 5 MB
      )
      .required('Please upload a voice file'),
  });

  const {
    values: { voiceTitle, voiceStatus },
    handleChange,
    handleBlur,
    handleSubmit,
    setFieldValue,
    touched,
    errors,
  } = useFormik<VoiceCreateRequest>({
    initialValues: {
      voiceId: voiceMessageDetails.voiceId,
      voiceTitle: voiceMessageDetails.voiceTitle,
      voiceFile: voiceMessageDetails.voiceFile,
      // createdDate: voiceMessageDetails.createdDate,
      templateId: voiceMessageDetails.templateId,
      voiceStatus: voiceMessageDetails.voiceStatus,
      adminId,
    },
    validationSchema: CreateEditVoiceMessageValidationSchema,
    onSubmit: async (values) => {
      let uploadedFilesDetails = null;
      if (uploaded.length !== 0) {
        const formData = new FormData();
        uploaded.forEach((file) => {
          formData.append('files', file);
        });

        const uploadResponse = await dispatch(
          fileUpload({
            files: formData,
          })
        ).unwrap();
        uploadedFilesDetails = uploadResponse.voiceFile;
      }
      const rest = { ...values, voiceFile: uploadedFilesDetails };
      console.log('uploadedFilesDetails', uploadedFilesDetails);
      console.log('rest::::', rest);
      onSave(rest, mode);
    },
    validateOnBlur: false,
  });
  const handleRemoveFile = (index: number) => {
    setUploaded((prevFiles) => {
      const newFiles = [...prevFiles];
      newFiles.splice(index, 1);
      return newFiles;
    });
  };
  return (
    <Box mt={3}>
      <form noValidate onSubmit={handleSubmit}>
        <Stack
          sx={{
            height: form === 'card' ? '100%' : 'calc(100vh - 150px)',
            overflow: 'auto',
            '&::-webkit-scrollbar': {
              width: 0,
            },
          }}
          direction="column"
        >
          <FormControl>
            <Typography variant="subtitle1" fontSize={12}>
              Voice Mail
            </Typography>
            <TextField
              placeholder="Enter title"
              name="voiceTitle"
              value={voiceTitle}
              onBlur={handleBlur}
              onChange={handleChange}
              error={touched.voiceTitle && !!errors.voiceTitle}
              helperText={errors.voiceTitle}
              disabled={isSubmitting}
              InputProps={{
                endAdornment: touched.voiceTitle && !!errors.voiceTitle && <ErrorIcon color="error" />,
              }}
            />
          </FormControl>
          <FormControl fullWidth sx={{ minWidth: { xs: '100%', sm: 180 } }}>
            <Typography mt={2} variant="subtitle1" fontSize={12} color="GrayText">
              Status
            </Typography>
            <Select
              disabled={isSubmitting}
              name="voiceStatus"
              value={voiceStatus}
              onBlur={handleBlur}
              onChange={handleChange}
            >
              {STATUS_OPTIONS.map((opt) => (
                <MenuItem key={opt.id} value={opt.id}>
                  {opt.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          <Box gap={5} mt={2}>
            <Stack width={{ xs: '100%', lg: '100%' }}>
              <FormControl fullWidth>
                <Typography variant="subtitle1" fontSize={12}>
                  Upload Files
                </Typography>
                <FilesUpload
                  height={150}
                  disabled={isSubmitting}
                  name="voiceFile"
                  accept=".mp3,audio/*"
                  multiple={false}
                  setUploaded={setUploaded}
                  onChange={(event) => {
                    setFieldValue('voiceFile', event.currentTarget.files?.[0]);
                  }}
                  error={touched.voiceFile && !!errors.voiceFile}
                  helperText={touched.voiceFile && errors.voiceFile}
                />
              </FormControl>
              {/* {uploaded.length === 0 && <Typography variant="subtitle2">Please select voice file </Typography>} */}
            </Stack>

            <Box mt={2} display="flex" flexWrap="wrap" gap={1}>
              {uploaded.map((file, index: number) => (
                <Box
                  // key={uuidv4()}
                  sx={{
                    position: 'relative',
                    height: 100,
                    width: '100%',
                    px: 0.09,
                  }}
                >
                  <IconButton
                    size="small"
                    sx={{
                      position: 'absolute',
                      right: 5,
                      top: 5,
                      zIndex: 1,
                      p: 0,
                      border: `1px solid ${theme.palette.grey[300]}`,
                      backgroundColor: theme.palette.common.white,
                      '&:hover': {
                        backgroundColor: theme.palette.error.lighter,
                      },
                    }}
                    onClick={() => handleRemoveFile(index)}
                  >
                    <CloseIcon fontSize="small" sx={{ '&:hover': { color: 'red' } }} />
                  </IconButton>

                  <AudioPlayer
                    autoPlay
                    disabled={isSubmitting}
                    muted
                    style={{
                      borderRadius: 10,
                      backgroundColor: isLight ? theme.palette.grey[300] : theme.palette.grey[800],
                    }}
                    src={URL.createObjectURL(file)}
                    onPlay={(e) => console.log('onPlay')}
                    // other props here
                  />
                </Box>
              ))}
            </Box>
          </Box>
        </Stack>
        <Box sx={{ justifyContent: 'center', pt: 3, position: 'relative' }}>
          <Stack spacing={2} direction="row" sx={{}}>
            <Button onClick={() => onCancel(mode)} fullWidth variant="contained" color="secondary" type="button">
              Cancel
            </Button>
            <LoadingButton
              loading={isSubmitting}
              fullWidth
              variant="contained"
              color="primary"
              type="submit"
              loadingPosition="start"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Saving...' : 'Save'}
            </LoadingButton>
          </Stack>
        </Box>
      </form>
    </Box>
  );
};
