/* eslint-disable react/self-closing-comp */
import styled from 'styled-components';
import Typography from '@mui/material/Typography';
import typography from '@/theme/typography';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import AccessAlarmsOutlinedIcon from '@mui/icons-material/AccessAlarmsOutlined';
import { Swiper, SwiperSlide } from 'swiper/react';
import KeyboardArrowLeftIcon from '@mui/icons-material/KeyboardArrowLeft';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import { Card, CardContent, CardMedia, Stack, Skeleton, Box, IconButton } from '@mui/material';
import 'swiper/css';
import 'swiper/css/effect-coverflow';
import 'swiper/css/pagination';
import 'swiper/css/navigation';
import { Pagination, Navigation } from 'swiper';
import { BsFillPlayCircleFill } from 'react-icons/bs';
import { getdashboardEventsListStatus, getdashboardEventsListData } from '@/config/storeSelectors';
import { useAppSelector } from '@/hooks/useAppSelector';
import { fetchDashboardEvents } from '@/store/Dashboard/dashboard.thunks';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import React, { useEffect } from 'react';
import Videoplayer from '@/components/Dashboard/Videoplayer';
import LoadingButton from '@mui/lab/LoadingButton';
import { BiRefresh } from 'react-icons/bi';
import Popup from '@/components/shared/Popup/Popup';
import PopupVideoPlayer from '@/components/shared/PopupVideoPlayer';
import { useSchool } from '@/contexts/SchoolContext';

const EventsRoot = styled.div`
  padding: 1;
  .swiper_container {
    position: relative;
    padding: 1rem;
  }
  .swiper-slide {
    position: relative;
    /* padding: 1rem 0rem; */
  }
  .swiper-button-next,
  .swiper-button-prev {
    position: absolute;
    top: var(--swiper-navigation-top-offset, 50%);
    margin-top: calc(0px - (var(--swiper-navigation-size) / 2));
    z-index: 10;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .events-card {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[800]};
    border: 1px solid
      ${(props) => (props.theme.themeMode === 'light' ? props.theme.palette.grey[300] : props.theme.palette.grey[900])};
  }
  .swiper-button-prev,
  .swiper-button-next {
    height: 30px;
    width: 30px;
    color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.primary.main : props.theme.palette.primary.main};
  }
`;

interface EventsProps {
  title?: string;
  adminId?: number;
}
function Events({ title, adminId }: EventsProps) {
  const dispatch = useAppDispatch();
  const dashboardEventsListStatus = useAppSelector(getdashboardEventsListStatus);
  const dashboardEventsListData = useAppSelector(getdashboardEventsListData);
  const [videoPopup, setVideoPopup] = React.useState(false);
  const [videoFile, setVideoFile] = React.useState();
  const { selectedSchool } = useSchool();

  const handlePlayVideo = (eventLinkFile: any) => {
    setVideoPopup(true);
    setVideoFile(eventLinkFile);
    // console.log('videoFile::::----', videoFile);
  };

  useEffect(() => {
    if (dashboardEventsListStatus === 'idle') {
      dispatch(fetchDashboardEvents(adminId));
    }
  }, [dispatch, dashboardEventsListStatus, adminId]);
  return (
    <EventsRoot>
      <Stack direction="row" justifyContent="space-between" px={1}>
        <Typography
          variant="h6"
          fontSize={20}
          sx={{ fontFamily: typography.fontFamily, paddingLeft: '1rem', paddingTop: '1rem' }}
        >
          {title}
        </Typography>
        {dashboardEventsListStatus === 'loading' ? (
          <LoadingButton size="small" loading />
        ) : (
          <IconButton sx={{ height: 1, mt: 2 }} onClick={() => dispatch(fetchDashboardEvents(adminId))}>
            <BiRefresh fontSize="inherit" />
          </IconButton>
        )}
      </Stack>
      <Swiper
        spaceBetween={16}
        effect="coverflow"
        grabCursor
        breakpoints={{
          299: {
            slidesPerView: 1.1,
          },
          499: {
            slidesPerView: 2,
          },
          999: {
            slidesPerView: 2,
          },
          1599: {
            slidesPerView: 3,
          },
        }}
        coverflowEffect={{
          rotate: 0,
          depth: 0,
          stretch: 0,
        }}
        pagination={{ el: '.swiper-pagination', clickable: true }}
        navigation={{
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev',
          // clickable: true,
        }}
        modules={[Pagination, Navigation]}
        className="swiper_container "
      >
        {dashboardEventsListStatus === 'loading'
          ? [1, 2, 3].map((event) => (
              <SwiperSlide key={event} className="swiper-slide ">
                <Card sx={{ boxShadow: '0' }} className="events-card">
                  <Skeleton sx={{ height: 170 }} animation="wave" variant="rectangular" />
                  <CardContent>
                    <Skeleton animation="wave" height={10} style={{ mb: 10, mt: 10 }} />
                    <Skeleton animation="wave" height={10} width="80%" />
                    <Stack direction="row" display="flex" alignItems="center" justifyContent="space-between">
                      <Box display="flex" alignItems="center">
                        <Skeleton animation="wave" height={10} width="80%" />
                      </Box>
                      <Box display="flex" alignItems="center">
                        <Skeleton animation="wave" height={10} width="80%" />
                      </Box>
                    </Stack>
                  </CardContent>
                </Card>
              </SwiperSlide>
            ))
          : dashboardEventsListData?.map((event) => (
              <SwiperSlide className="swiper-slide " key={event.eventId}>
                <Card sx={{ boxShadow: '0' }} className="events-card">
                  <CardMedia
                    component="img"
                    alt="Events"
                    height="160"
                    image={`${selectedSchool?.eventThumbnail}${event.eventThumbNail}`}
                    // image={`http://demo.passdaily.in/EventFile/${event.eventThumbNail}`}
                    // image={`http://holy.passdaily.in/EventFile/${event.eventThumbNail}`}
                    // image={`http://carmel.passdaily.in/EventFile/${event.eventThumbNail}`}
                    // image={`http://therese.passdaily.in/EventFile/${event.eventThumbNail}`}
                    // image={`http://stm.passdaily.in/EventFile/${event.eventThumbNail}`}
                    // image={`http://nirmala.passdaily.in/EventFile/${event.eventThumbNail}`}
                    sx={{ position: 'relative', cursor: event.eventLinkFile && 'pointer' }}
                    onClick={() => event.eventLinkFile && handlePlayVideo(event.eventLinkFile)}
                  />
                  {event.eventLinkFile && (
                    <IconButton
                      onClick={() => event.eventLinkFile && handlePlayVideo(event.eventLinkFile)}
                      sx={{
                        position: 'absolute',
                        top: '35%',
                        left: '50%',
                        transform: 'translate(-50%, -50%)',
                        color: 'white',
                        fontSize: '3rem', // Adjust icon size as needed
                      }}
                      aria-label="play"
                    >
                      <BsFillPlayCircleFill />
                    </IconButton>
                  )}
                  {/* /<Box height={160}>
                      <Videoplayer
                      //     url={
                      //       event.eventType === 2
                      //         ? `http://demo.passdaily.in/EventFile/${event.eventLinkFile}`
                      //         : `https://www.youtube.com/watch?${event.eventLinkFile}`
                      //     }
                      //     thumbnail={`http://demo.passdaily.in/EventFile/${event.eventThumbNail}`}
                      //   />
                      // </Box> */}
                  {/* <img src={`http://demo.passdaily.in/EventFile/${event.eventThumbNail}`} alt="thumbnail" /> */}
                  <CardContent>
                    <Box>
                      <Typography
                        sx={{
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                          overflow: 'hidden',
                        }}
                        variant="h6"
                        fontSize={14}
                      >
                        {event.eventTitle.toUpperCase()}
                      </Typography>
                      <Typography
                        sx={{
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                          overflow: 'hidden',
                        }}
                        variant="body2"
                        fontWeight={typography.fontWeightMedium}
                        marginBottom={1}
                        fontSize="10px"
                      >
                        {(event.eventDescription ? event.eventDescription : event.eventTitle)
                          .split(' ')
                          .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
                          .join(' ')}
                      </Typography>
                    </Box>
                    <Stack direction="row" display="flex" alignItems="center" justifyContent="space-between">
                      <Box display="flex" alignItems="center">
                        <CalendarTodayIcon color="secondary" sx={{ fontSize: '10px', marginRight: '5px' }} />
                        <Typography
                          sx={{ fontFamily: 'Poppins medium', fontWeight: '500', fontSize: '10px', color: 'gray' }}
                        >
                          {event.eventDate.split('T')[0]}
                        </Typography>
                      </Box>
                      <Box display="flex" alignItems="center">
                        <AccessAlarmsOutlinedIcon color="secondary" sx={{ fontSize: '11px', marginRight: '5px' }} />
                        <Typography
                          sx={{ fontFamily: 'Poppins medium', fontWeight: '500', fontSize: '10px', color: 'gray' }}
                        >
                          {event.eventDate.split('T')[1]}
                        </Typography>
                      </Box>
                    </Stack>
                  </CardContent>
                </Card>
              </SwiperSlide>
            ))}
        <div className="slider-controler d-none d-sm-flex">
          <div>
            <KeyboardArrowLeftIcon className="swiper-button-prev  slider-arrow border-0 card shadow rounded-circle  " />
          </div>
          <div>
            <KeyboardArrowRightIcon className="swiper-button-next slider-arrow border-0 card shadow rounded-circle " />
          </div>
        </div>
      </Swiper>
      <Popup
        title="Events"
        size="xs"
        state={videoPopup}
        onClose={() => setVideoPopup(false)}
        popupContent={<PopupVideoPlayer videoFile={videoFile} />}
      />
    </EventsRoot>
  );
}

export default Events;
