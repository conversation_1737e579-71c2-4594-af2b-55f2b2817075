/* eslint-disable jsx-a11y/alt-text */
import React, { useEffect, useState } from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Autocomplete,
  Avatar,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Typography,
  Card,
  IconButton,
  Select,
  Collapse,
  Tooltip,
  FormControl,
  MenuItem,
} from '@mui/material';
import styled from 'styled-components';
import deleteBin from '@/assets/ManageFee/deleteBin.json';
import { CLASS_SELECT, GENDER_SELECT, YEAR_SELECT } from '@/config/Selection';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import DeleteIcon from '@mui/icons-material/Delete';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import Popup from '@/components/shared/Popup/Popup';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import EditStudent from '@/features/ManageStudents/StudentReAllocation/Edit';
import { StudentInfoData } from '@/config/StudentDetails';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import { MdAdd } from 'react-icons/md';
import SearchIcon from '@mui/icons-material/Search';
import { IoIosArrowUp } from 'react-icons/io';
import { getClassData, getYearData, getYearStatus } from '@/config/storeSelectors';
import { SelectChangeEvent } from '@mui/material';
import { useAppSelector } from '@/hooks/useAppSelector';
import { ClassListInfo } from '@/types/AcademicManagement';
import { fetchClassList, fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import useAuth from '@/hooks/useAuth';
import { useAppDispatch } from '@/hooks/useAppDispatch';

const StudentClassReAllocationRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  h6 {
    font-weight: 600;
  }
  .MuiTextField-root:hover .MuiInputBase-input {
    cursor: text;
  }
`;
const AllClassOption = {
  classId: -1,
  className: 'All Class',
  classDescription: 'string',
  classStatus: 1,
};
function StudentClassReAllocation() {
  const { user } = useAuth();
  const dispatch = useAppDispatch();
  const adminId: number = user ? user.accountId : 0;
  const [Delete, setDelete] = React.useState(false);
  const [showFilter, setShowFilter] = useState(true);
  const YearStatus = useAppSelector(getYearStatus);
  const YearData = useAppSelector(getYearData);
  const ClassData = useAppSelector(getClassData);
  const classDataWithAllClass = [AllClassOption, ...ClassData];
  const defualtYear = YearData[0]?.accademicId || 0;
  const [academicYearFilter, setAcademicYearFilter] = useState(defualtYear);
  const [classFilter, setClassFilter] = useState(classDataWithAllClass[0]);
  const { classId, className } = classFilter || {};
  const [studentGenderFilter, setStudentGenderFilter] = useState(-1);

  const handleClickDelete = () => setDelete(true);
  const handleClickCloseDelete = () => setDelete(false);
  const [popup, setPopup] = React.useState(false);

  const handleClickOpen = () => setPopup(true);
  const handleClickClose = () => setPopup(false);

  const [students, setStudents] = React.useState(StudentInfoData);
  const [open, setOpen] = React.useState(false);
  const [selectedStudent, setSelectedStudent] = React.useState(null);

  const handleOpen = (student: any) => {
    setSelectedStudent(student);
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleUpdate = (id: number, updatedData: any) => {
    const updatedStudents = students.map((student) => (student.id === id ? { ...student, ...updatedData } : student));
    setStudents(updatedStudents);
    setOpen(false);
  };
  const handleYearChange = (e: SelectChangeEvent) => {
    setAcademicYearFilter(parseInt(YearData.filter((item) => item.accademicId === e.target.value)[0].accademicId, 10));
    // loadTermFeeList({ ...currentTermFeeListRequest, accademicId: parseInt(e.target.value, 10) });
  };
  const handleClassChange = (e: SelectChangeEvent) => {
    const selectedClass = classDataWithAllClass.find((item) => item.className === e.target.value);
    if (selectedClass) {
      setClassFilter(selectedClass);
    }
    // loadStudentList({
    //   ...currentStudentListRequest,
    //   pageNumber: 1,
    //   filters: { ...currentStudentListRequest.filters, classId: selectedClass ? selectedClass.classId : 0 },
    // });
  };

  const handleGenderChange = (e: SelectChangeEvent) => {
    setStudentGenderFilter(parseInt(GENDER_SELECT.filter((item) => item.id === e.target.value)[0].id, 10));
    // loadStudentList({
    //   ...currentStudentListRequest,
    //   filters: { ...currentStudentListRequest.filters, studentGender: parseInt(e.target.value, 10) },
    // });
  };

  useEffect(() => {
    if (YearStatus === 'idle') {
      setAcademicYearFilter(defualtYear);
      dispatch(fetchYearList(adminId));
      dispatch(fetchClassList(adminId));
    }
  }, [defualtYear, YearStatus, adminId, dispatch]);
  return (
    <Page title="Student Class Re-allocation">
      <StudentClassReAllocationRoot>
        <Card elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Box display="flex" pb={0.5} justifyContent="space-between">
            <Typography variant="h6" fontSize={17}>
              Student Class Re-allocation
            </Typography>
            <Stack direction="row" alignItems="center">
              <Button sx={{ borderRadius: '20px' }} variant="outlined" size="small">
                <MdAdd size="20px" /> Create
              </Button>

              {showFilter === false ? (
                <Tooltip title="Search">
                  <IconButton
                    aria-label="delete"
                    color="primary"
                    sx={{ ml: 1 }}
                    onClick={() => setShowFilter((x) => !x)}
                  >
                    <SearchIcon />
                  </IconButton>
                </Tooltip>
              ) : (
                <IconButton aria-label="delete" color="primary" sx={{ ml: 1 }} onClick={() => setShowFilter((x) => !x)}>
                  <IoIosArrowUp />
                </IconButton>
              )}
            </Stack>
          </Box>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={3} alignItems="end">
                  <Grid item xxl={2} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Select
                        labelId="academicYearFilter"
                        id="academicYearFilterSelect"
                        value={academicYearFilter.toString()}
                        onChange={handleYearChange}
                        placeholder="Select Year"
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Year
                        </MenuItem>
                        {YearData.map((opt) => (
                          <MenuItem key={opt.accademicId} value={opt.accademicId}>
                            {opt.accademicTime}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Select
                        labelId="classFilter"
                        id="classFilterSelect"
                        value={className}
                        onChange={handleClassChange}
                        placeholder="Select"
                        MenuProps={{
                          PaperProps: {
                            style: {
                              maxHeight: '250px', // Adjust the value to your desired height
                            },
                          },
                        }}
                      >
                        {classDataWithAllClass?.map((item: ClassListInfo) => (
                          <MenuItem key={item.classId} value={item.className}>
                            {item.className}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Gender
                      </Typography>
                      <Select
                        labelId="genderFilterSelect"
                        id="genderFilterSelect"
                        value={studentGenderFilter.toString()}
                        onChange={handleGenderChange}
                        placeholder="Select"
                      >
                        {GENDER_SELECT.map((item) => (
                          <MenuItem key={item.id} value={item.id}>
                            {item.gender}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item lg={2.4} xs={12}>
                    <Typography variant="h6" fontSize={14}>
                      Roll No.
                    </Typography>
                    <TextField fullWidth placeholder="Enter No." />
                  </Grid>
                  <Grid item lg="auto">
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button type="reset" variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>
            <Box>
              <Paper
                sx={{
                  border: `1px solid #e8e8e9`,
                  width: '100%',
                  overflow: 'auto',
                  '&::-webkit-scrollbar': {
                    width: 0,
                  },
                }}
              >
                <TableContainer
                  sx={{
                    maxHeight: 410,
                    width: { xs: '700px', md: '100%' },
                    '&::-webkit-scrollbar': {
                      width: 0,
                    },
                  }}
                >
                  <Table stickyHeader aria-label="simple table">
                    <TableHead>
                      <TableRow>
                        <TableCell>Current Roll No.</TableCell>
                        <TableCell>Student Name</TableCell>
                        <TableCell>Class</TableCell>
                        <TableCell>Assign New Roll No.</TableCell>
                        <TableCell>Action</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {students.map((r, index) => (
                        <TableRow hover key={r.rollNo}>
                          <TableCell>{r.rollNo}</TableCell>
                          <TableCell>
                            <Stack direction="row">
                              <Avatar src={r.image} sx={{ mr: 2 }} />
                              <Typography pt={0.7} fontSize={15}>
                                {r.name}
                              </Typography>
                            </Stack>
                          </TableCell>
                          <TableCell>VIII-A</TableCell>
                          <TableCell>
                            <TextField defaultValue={index + 1} type="number" />
                          </TableCell>
                          <TableCell>
                            <Stack direction="row" gap={1}>
                              <IconButton onClick={() => handleOpen(r)} size="small">
                                <ModeEditIcon />
                              </IconButton>
                              <IconButton onClick={handleClickDelete} color="error" size="small">
                                <DeleteIcon />
                              </IconButton>
                            </Stack>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Paper>
            </Box>
          </div>
          <Box
            display="flex"
            sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: { xs: 5, md: 3 } }}
          >
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="secondary">
                Cancel
              </Button>
              <Button onClick={handleClickOpen} variant="contained" color="primary">
                Allocate Students
              </Button>
            </Stack>
          </Box>
        </Card>
        <Popup
          size="xs"
          state={popup}
          onClose={handleClickClose}
          popupContent={<SuccessMessage message="Updated Successfully" />}
        />
        <Popup
          size="xs"
          state={Delete}
          onClose={handleClickCloseDelete}
          popupContent={<DeleteMessage jsonIcon={deleteBin} message="Are you sure want to delete?" />}
        />
        <TemporaryDrawer
          state={open}
          onClose={handleClose}
          DrawerContent={<EditStudent student={selectedStudent} onUpdate={handleUpdate} onCancel={handleClose} />}
        />
      </StudentClassReAllocationRoot>
    </Page>
  );
}

export default StudentClassReAllocation;
