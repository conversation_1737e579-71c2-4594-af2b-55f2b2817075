import React, { useState, useEffect, useReducer, useMemo, useCallback } from 'react';
import { Box, Grid, Button } from '@mui/material';
import dayjs, { Dayjs } from 'dayjs';
import CalendarContext, { CalendarContextType, EventType, LabelType } from '@/contexts/CalendarContext';
import { MdAdd } from 'react-icons/md';
import { getMonth } from '@/utils/Calendar';
import Popup from '@/components/shared/Popup/Popup';
import Month from './Month';
import CalendarHeader from './CalendarHeader';
import EventModal from './EventModal';
import Sidebar from './Sidebar';
import useSettings from '@/hooks/useSettings';

function savedEventsReducer(state: EventType[], { type, payload }: { type: string; payload?: any }): EventType[] {
  switch (type) {
    case 'push':
      return [...state, payload];
    case 'update':
      return state.map((evt) => (evt.id === payload.id ? payload : evt));
    case 'delete':
      return state.filter((evt) => evt.id !== payload.id);
    default:
      throw new Error();
  }
}

function initEvents(): EventType[] {
  const storageEvents = localStorage.getItem('savedEvents');
  const parsedEvents = storageEvents ? JSON.parse(storageEvents) : [];
  return parsedEvents;
}

export default function CalendarComponent(): JSX.Element {
  const isLight = useSettings().themeMode === 'light';
  const [monthIndex, setMonthIndex] = useState<number>(dayjs().month());
  const [smallCalendarMonth, setSmallCalendarMonth] = useState<number | null>(null);
  const [daySelected, setDaySelected] = useState<Dayjs>(dayjs());
  const [showEventModal, setShowEventModal] = useState<boolean>(false);
  const [selectedEvent, setSelectedEvent] = useState<EventType | null>(null);
  const [labels, setLabels] = useState<LabelType[] | any[]>([]);
  const [savedEvents, dispatchCalEvent] = useReducer<
    React.Reducer<EventType[], { type: string; payload?: any }>,
    EventType[]
  >(savedEventsReducer, [], initEvents);

  const [currenMonth, setCurrentMonth] = useState(getMonth());

  useEffect(() => {
    setCurrentMonth(getMonth(monthIndex));
  }, [monthIndex]);

  const filteredEvents = useMemo(() => {
    return savedEvents.filter((evt) =>
      labels
        .filter((lbl) => lbl.checked)
        .map((lbl) => lbl.label)
        .includes(evt.label)
    );
  }, [savedEvents, labels]);

  useEffect(() => {
    localStorage.setItem('savedEvents', JSON.stringify(savedEvents));
  }, [savedEvents]);

  useEffect(() => {
    setLabels((prevLabels) => {
      const uniqueLabels = [...new Set(savedEvents.map((evt) => `${evt.label}-${evt.type}`))];

      return uniqueLabels.map((uniqueLabel) => {
        const [label, type] = uniqueLabel.split('-');
        const currentLabel = prevLabels.find((lbl) => lbl.label === label && lbl.type === type);

        return {
          label,
          type,
          checked: currentLabel ? currentLabel.checked : true,
        };
      });
    });
  }, [savedEvents]);

  useEffect(() => {
    if (smallCalendarMonth !== null) {
      setMonthIndex(smallCalendarMonth);
    }
  }, [smallCalendarMonth]);

  useEffect(() => {
    if (!showEventModal) {
      setSelectedEvent(null);
    }
  }, [showEventModal]);

  const updateLabel = useCallback(
    (label: LabelType): void => {
      setLabels((prevLabels) => prevLabels.map((lbl) => (lbl.label === label.label ? label : lbl)));
    },
    [setLabels]
  );

  const contextValue: CalendarContextType | null = useMemo(() => {
    return {
      monthIndex,
      setMonthIndex,
      smallCalendarMonth,
      setSmallCalendarMonth,
      daySelected,
      setDaySelected,
      showEventModal,
      setShowEventModal,
      dispatchCalEvent,
      selectedEvent,
      setSelectedEvent,
      savedEvents,
      setLabels,
      labels,
      updateLabel,
      filteredEvents,
    };
  }, [
    monthIndex,
    setMonthIndex,
    smallCalendarMonth,
    setSmallCalendarMonth,
    daySelected,
    setDaySelected,
    showEventModal,
    setShowEventModal,
    dispatchCalEvent,
    selectedEvent,
    setSelectedEvent,
    savedEvents,
    setLabels,
    labels,
    updateLabel,
    filteredEvents,
  ]);

  const handleEventClick = (evt: EventType | null) => {
    setSelectedEvent(evt);
    setShowEventModal(true);
  };
  return (
    <CalendarContext.Provider value={contextValue}>
      <Box sx={{ display: 'flex', flexDirection: 'column', flex: 1, width: '100%' }}>
        <Popup
          size="xs"
          title={selectedEvent ? 'Edit Event Details' : 'Add Event Details'}
          state={showEventModal}
          onClose={() => setShowEventModal(false)}
          popupContent={<EventModal />}
        />
        <Grid container sx={{ display: 'flex', flex: 1 }}>
          <Grid item xs={12} md={9.5}>
            <CalendarHeader />
          </Grid>
          <Grid item xs={12} md={2.5} sx={{ display: 'flex', justifyContent: 'end' }}>
            {/* {daySelected.format('DD-MM-YY') !== dayjs().format('DD-MM-YY') ? ( */}
            <Button
              size="small"
              variant="outlined"
              color="primary"
              sx={{
                border: '1px solid',
                height: '35px',
                fontWeight: 'bold',
                mt: 2,
                display: { xs: 'none', lg: 'flex' },
              }}
              onClick={() => handleEventClick(selectedEvent)}
            >
              <MdAdd size={20} style={{ marginBottom: '3px' }} /> Add Event
            </Button>
            {/* ) : null} */}
          </Grid>
          <Grid item xs={12} md={9.5} height="100%" sx={{ display: 'flex' }}>
            <Month month={currenMonth} />
          </Grid>
          <Grid item xs={12} md={2.5} height="100%">
            <Sidebar />
          </Grid>
        </Grid>
      </Box>
    </CalendarContext.Provider>
  );
}
