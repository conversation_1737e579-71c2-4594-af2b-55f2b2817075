/* eslint-disable jsx-a11y/alt-text */
import { useCallback, useMemo } from 'react';
import { Box, Grid, Card, useTheme, Chip, Paper } from '@mui/material';
import styled from 'styled-components';
import useSettings from '@/hooks/useSettings';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { ObjectiveExamReportType } from '@/types/Reports';

const ObjectiveExamReportData = [
  {
    id: 1,
    subject: 'Vacation',
    exam: 'Online Test',
    totalQuestion: 10,
    answeredQuestion: 6,
    correctAnswer: 4,
  },
  {
    id: 2,
    subject: 'Vacation',
    exam: 'Online Test',
    totalQuestion: 10,
    answeredQuestion: 6,
    correctAnswer: 4,
  },
  {
    id: 3,
    subject: 'Vacation',
    exam: 'Online Test',
    totalQuestion: 10,
    answeredQuestion: 6,
    correctAnswer: 4,
  },

  {
    id: 4,
    subject: 'Vacation',
    exam: 'Online Test',
    totalQuestion: 10,
    answeredQuestion: 6,
    correctAnswer: 4,
  },
  {
    id: 5,
    subject: 'Vacation',
    exam: 'Online Test',
    totalQuestion: 10,
    answeredQuestion: 6,
    correctAnswer: 4,
  },
  {
    id: 6,
    subject: 'Vacation',
    exam: 'Online Test',
    totalQuestion: 10,
    answeredQuestion: 6,
    correctAnswer: 4,
  },
  {
    id: 7,
    subject: 'Vacation',
    exam: 'Online Test',
    totalQuestion: 10,
    answeredQuestion: 6,
    correctAnswer: 4,
  },
];

const ObjectiveExamReportRoot = styled.div`
  .ReportCard {
    display: flex;
    flex-direction: column;
    height: 350px;
    background-color: ${(props) => (props.theme.themeMode === 'light' ? '#f6fbf7' : props.theme.palette.grey[900])};
    /* border: 1px solid ${(props) => props.theme.palette.grey[300]}; */

    .card-main-body {
      display: flex;
      flex-direction: column;
      min-height: calc(100% - 5px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }
    }
  }
`;

function ObjectiveExamReport() {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';

  const tableStyles = {
    '& th:nth-of-type(n+3):nth-of-type(-n+5), & td:nth-of-type(n+3):nth-of-type(-n+5)': {
      textAlign: 'center',
    },
    backgroundColor: isLight ? '#f6fbf7' : theme.palette.grey[900],
  };

  const getRowKeyObjectiveExamReport = useCallback((row: ObjectiveExamReportType) => row.id, []);

  const ObjectiveExamReportColumns: DataTableColumn<ObjectiveExamReportType>[] = useMemo(
    () => [
      {
        name: 'subject',
        dataKey: 'subject',
        headerLabel: 'Subject',
      },
      {
        name: 'exam',
        dataKey: 'exam',
        headerLabel: 'Exam',
      },
      {
        name: 'totalQuestion',
        dataKey: 'totalQuestion',
        headerLabel: 'Total Question',
      },
      {
        name: 'answeredQuestion',
        dataKey: 'answeredQuestion',
        headerLabel: 'Answered Question',
      },
      {
        name: 'correctAnswer',
        dataKey: 'correctAnswer',
        headerLabel: 'Correct Answer',
      },
    ],
    []
  );

  return (
    <ObjectiveExamReportRoot>
      <Grid container spacing={3}>
        <Grid item xl={12} lg={12} xs={12}>
          <Card className="ReportCard" sx={{ mt: 3, boxShadow: 0, px: { xs: 3, md: 2 }, py: { xs: 2, md: 2 } }}>
            <div className="card-main-body">
              <Box>
                <Chip
                  label="Objective Exam"
                  variant="filled"
                  sx={{
                    height: 25,
                    background: isLight ? theme.palette.common.black : theme.palette.common.white,
                    color: isLight ? theme.palette.common.white : theme.palette.common.black,
                  }}
                />
              </Box>
              <Paper className="card-table-container">
                <DataTable
                  columns={ObjectiveExamReportColumns}
                  data={ObjectiveExamReportData}
                  getRowKey={getRowKeyObjectiveExamReport}
                  fetchStatus="success"
                  tableStyles={tableStyles}
                />
              </Paper>
            </div>
          </Card>
        </Grid>
      </Grid>
    </ObjectiveExamReportRoot>
  );
}

export default ObjectiveExamReport;
