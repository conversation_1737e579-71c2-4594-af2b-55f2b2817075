/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  Collapse,
  FormControl,
} from '@mui/material';
import styled from 'styled-components';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import SearchIcon from '@mui/icons-material/Search';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import DeleteIcon from '@mui/icons-material/Delete';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import deleteBin from '@/assets/ManageFee/deleteBin.json';
import { MdAdd } from 'react-icons/md';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import { deleteTermFeeList } from '@/store/ManageFee/manageFee.thunks';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import { useAppDispatch } from '@/hooks/useAppDispatch';

const ParentLoginRoot = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      height: calc(100% - 40px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;
const getStudentNamesAsString = (students) => {
  return students.map((student) => student.name).join(', ');
};

export const data = [
  {
    SlNo: '1',
    GuardianNames: 'Ansar',
    GuardianNumber: '9746980992',
    Students: [
      { id: 1, name: 'MANHA' },
      { id: 2, name: 'HAASINI M' },
      { id: 3, name: 'AAMIRA AL FATHIMA F' },
      { id: 4, name: 'RAYAN K' },
      { id: 5, name: 'MUHAMMED HAMDAN' },
    ],
    Class: 'LKG-A, I-A, V-A, VIII-A, TESTCLASSI',
    UserId: '9746980992',
    Password: '696451',
    CreatedDate: '24 Aug 2023 12:06 PM',
  },
  {
    SlNo: '2',
    GuardianNames: 'ANWAR',
    GuardianNumber: '7558966668',
    Students: [
      { id: 1, name: 'AZRAA J' },
      { id: 2, name: 'NOUFA FATHIMA N A' },
      { id: 3, name: 'FARHAT ZARA SHEIKH' },
    ],
    Class: 'II-A, VI-A, IX-A',
    UserId: '7558966668',
    Password: '325994',
    CreatedDate: '23 Aug 2023 04:23 PM',
  },
  {
    SlNo: '3',
    GuardianNames: 'RUBY, PARENT',
    GuardianNumber: '9995436405',
    Students: [
      { id: 1, name: 'ARSHIYA HANANA A' },
      { id: 2, name: 'FATHIMA' },
      { id: 3, name: 'ANUGRAHITHA' },
      { id: 4, name: 'MOHAMMED YUSUF M' },
      { id: 5, name: 'ATHIRA' },
    ],
    Class: 'I-A, V-B, VIII-A, VIII-B, X-A',
    UserId: '9995436405',
    Password: '',
    CreatedDate: '23 Aug 2023 11:21 AM',
  },
  {
    SlNo: '4',
    GuardianNames: 'FAVAS, FAVAS',
    GuardianNumber: '9946824752',
    Students: [
      { id: 1, name: 'AFNAN AHMED A' },
      { id: 2, name: 'MOHAMMED ISHAAN' },
    ],
    Class: 'UKG-A, II-A',
    UserId: '9946824752',
    Password: '486722',
    CreatedDate: '22 Aug 2023 02:09 PM',
  },
  {
    SlNo: '5',
    GuardianNames: 'PARENT',
    GuardianNumber: '9061168913',
    Students: [
      { id: 1, name: 'ADVIKAV' },
      { id: 2, name: 'SACHIN S' },
      { id: 3, name: 'PRANAVS' },
    ],
    Class: 'LKG-A, VII-A, XI-COM',
    UserId: '9061168913',
    Password: '642654',
    CreatedDate: '22 Aug 2023 01:03 PM',
  },
  {
    SlNo: '6',
    GuardianNames: 'PARENT',
    GuardianNumber: '7092171154',
    Students: [
      { id: 1, name: 'AADIDEVS' },
      { id: 2, name: 'DHIYAR' },
      { id: 3, name: 'ROHAN' },
    ],
    Class: 'V-A, VIII-A, X-A',
    UserId: '7092171154',
    Password: '878884',
    CreatedDate: '22 Aug 2023 12:24 PM',
  },
];
data.forEach((item) => {
  item.concatenatedStudents = getStudentNamesAsString(item.Students);
});
function ParentLogin() {
  const { confirm } = useConfirm();
  const dispatch = useAppDispatch();
  const [drawerOpen, setDrawerOpen] = React.useState<boolean>(false);
  const [showFilter, setShowFilter] = useState(false);

  const handleOpen = () => {
    setDrawerOpen(true);
  };

  const toggleDrawerClose = useCallback(() => setDrawerOpen(false), []);

  const getRowKey = useCallback((row: any) => row.examId, []);
  const handleDeleteTermFeeList = useCallback(
    async (termObj: any) => {
      const { termId } = termObj;
      const deleteConfirmMessage = (
        <DeleteMessage
          jsonIcon={deleteBin}
          message={
            <div>
              Are you sure you want to delete the Row <br />
              &quot;{termObj.termTitle}&quot; ?
            </div>
          }
        />
      );
      if (await confirm(deleteConfirmMessage, 'Delete Row?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
        const sendReq = [{ dbResult: 'string', deletedId: 0 }];

        console.log('sendReq', sendReq);

        const deleteResponse = await dispatch(deleteTermFeeList(sendReq));
        console.log('deleteResponse::::----', deleteResponse);
        if (deleteResponse && Array.isArray(deleteResponse.payload)) {
          const results: any[] = deleteResponse.payload;
          const errorMessages = results.find((result) => result.dbResult === 'Failed');
          const successMessages = results.find((result) => result.dbResult === 'Success');
          // Reload only the specific message template with the deleted messageId

          if (!errorMessages) {
            const deleteSuccessMessage = <SuccessMessage loop={false} message="Delete successfully" />;
            await confirm(deleteSuccessMessage, 'Success', { okLabel: 'Ok', showOnlyOk: true });
          } else if (!successMessages) {
            const deleteErrorMessage = <DeleteMessage message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          } else {
            const deleteErrorMessage = <DeleteMessage message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          }
          // loadTermFeeList({ ...currentTermFeeListRequest });

          // setSnackBar(true);
          // setTermFeeData((prevDetails) => prevDetails.filter((item) => item.termId !== termId));
        }
      }
    },
    [confirm]
  );
  const parentLoginColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'Sl.No',
        dataKey: 'SlNo',
        headerLabel: 'Sl.No',
        sortable: true,
      },
      {
        name: 'Guardian Names',
        dataKey: 'GuardianNames',
        headerLabel: 'Guardian Names',
      },
      {
        name: 'Guardian Number',
        dataKey: 'GuardianNumber',
        headerLabel: 'Guardian Number',
      },
      {
        name: 'Students',
        dataKey: 'concatenatedStudents',
        headerLabel: 'Students',
      },
      {
        name: 'Class',
        dataKey: 'Class',
        headerLabel: 'Class',
      },
      {
        name: 'User Id',
        dataKey: 'UserId',
        headerLabel: 'User Id',
      },
      {
        name: 'Password',
        dataKey: 'Password',
        headerLabel: 'Password',
      },
      {
        name: 'Created Date',
        dataKey: 'CreatedDate',
        headerLabel: 'Created Date',
      },
      {
        name: 'actions',
        headerLabel: 'Actions',
        renderCell: () => {
          return (
            <Stack direction="row" gap={1}>
              <IconButton size="small" onClick={handleOpen} sx={{ padding: 0.5 }}>
                <ModeEditIcon />
              </IconButton>
              <IconButton size="small" color="error" onClick={handleDeleteTermFeeList} sx={{ padding: 0.5 }}>
                <DeleteIcon />
              </IconButton>
            </Stack>
          );
        },
      },
    ],
    []
  );

  const pageProps = useMemo(
    () => ({
      rowsPerPageOptions: [10, 20, 30],
      pageNumber: 0,
      pageSize: 5,
      totalRecords: 50,
    }),
    []
  );
  return (
    <Page title="List">
      <ParentLoginRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17}>
              Payment Category List
            </Typography>
            <Box pb={1} sx={{ flexShrink: 0 }}>
              <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                <SearchIcon />
              </IconButton>
              <Button onClick={handleOpen} sx={{ borderRadius: '20px' }} variant="outlined" size="small">
                <MdAdd size="20px" /> Create
              </Button>
            </Box>
          </Stack>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={3} alignItems="end">
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 250 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Category Name
                      </Typography>
                      <TextField placeholder="Enter Name" />
                    </FormControl>
                  </Grid>

                  <Grid item lg="auto" xs={12} sm={6}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Paper className="card-table-container" sx={{ marginTop: '12px' }}>
              <DataTable
                columns={parentLoginColumns}
                data={data}
                getRowKey={getRowKey}
                fetchStatus="success"
                allowPagination
                PaginationProps={pageProps}
              />
            </Paper>
          </div>
          {/* <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 2 }}>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="secondary">
                Cancel
              </Button>
              <Button onClick={handleOpen} variant="contained" color="primary">
                Save
              </Button>
            </Stack>
          </Box> */}
        </Card>
      </ParentLoginRoot>

      <TemporaryDrawer onClose={toggleDrawerClose} Title="Upload Details" state={drawerOpen} DrawerContent="" />
    </Page>
  );
}

export default ParentLogin;
