import { STATUS_SELECT } from '@/config/Selection';
import { CreateProps } from '@/types/Common';
import { Autocomplete, Box, Button, Stack, TextField, Typography } from '@mui/material';
import React from 'react';
import styled from 'styled-components';

const EditRoot = styled(Box)`
  .upload_Bg {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.primary.lighter : props.theme.palette.grey[900]};
  }
`;
export const Edit = ({ onClose, open, updates }: CreateProps) => {
  const handleClickOpen = () => {
    open();
    onClose();
  };
  return (
    <EditRoot mt={3}>
      <Stack sx={{ height: 'calc(100vh - 150px)' }}>
        <Typography variant="h6" fontSize={14}>
          Subject
        </Typography>
        <TextField defaultValue={updates.name} placeholder="Enter Message Title" />
        <Typography mt={2} variant="h6" fontSize={14}>
          Status
        </Typography>
        <Autocomplete
          defaultValue={updates.type}
          options={STATUS_SELECT}
          renderInput={(params) => <TextField {...params} placeholder="Select Type" />}
        />
        <Typography mt={2} variant="h6" fontSize={14}>
          Message content
        </Typography>
        <TextField
          defaultValue={updates.description}
          multiline
          minRows={2}
          InputProps={{ inputProps: { style: { resize: 'both' } } }}
          placeholder="Enter Message Content..."
        />
      </Stack>
      <Box sx={{ justifyContent: 'center', pt: 3, position: 'relative' }}>
        <Stack spacing={2} direction="row" sx={{}}>
          <Button onClick={onClose} fullWidth variant="contained" color="secondary">
            Cancel
          </Button>
          <Button onClick={handleClickOpen} fullWidth variant="contained" color="primary">
            Save
          </Button>
        </Stack>
      </Box>
    </EditRoot>
  );
};
