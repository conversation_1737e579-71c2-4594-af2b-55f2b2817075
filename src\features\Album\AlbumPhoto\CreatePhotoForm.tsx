import { useState } from 'react';
import { Button, TextField, Typography, Box, Stack, FormControl, IconButton, useTheme } from '@mui/material';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { AlbumImageInfo } from '@/types/Album';
import PrgressUploadImage from '@/components/shared/Progress';
import { TbCloudUpload } from 'react-icons/tb';
import LoadingButton from '@mui/lab/LoadingButton';
import CloseIcon from '@mui/icons-material/Close';

// Remove unused imports: LoadingButton, TbCloudUpload

export type CreateEditClassFormProps = {
  onSave: (values: AlbumImageInfo) => void;
  onCancel: () => void;
  onClose: () => void;
  albumInfo: AlbumImageInfo;
  isSubmitting: boolean;
};

const CreateAlbumValidationSchema = Yup.object({
  albumName: Yup.string().required('Please enter Album Name'),
  imageTitle: Yup.string().required('Please enter Image Title'),
  // imageFile: Yup.string().required('Please select an image'),
});

function CreatePhotoForm({ onSave, onCancel, isSubmitting, albumInfo }: CreateEditClassFormProps) {
  const [uploadedImages, setUploadedImages] = useState('');
  const theme = useTheme();

  const {
    values: { albumName, imageTitle, imageFile },
    handleChange,
    handleSubmit,
    touched,
    errors,
    setFieldValue,
  } = useFormik<AlbumImageInfo>({
    initialValues: {
      albumId: albumInfo.albumId,
      albumName: albumInfo.albumName,
      imageTitle: albumInfo.imageTitle,
      imageFile: uploadedImages,
    },
    validationSchema: CreateAlbumValidationSchema,
    onSubmit: (values) => {
      // Call the onSave function with the updated values
      // const existingArray = [];
      const existingArray = JSON.parse(localStorage.getItem('myDummyArray')) || [];
      const newEntry = {
        albumId: Math.floor(Math.random() * 100000),
        albumName: values.albumName,
        imageTitle: values.imageTitle,
        imageFile: uploadedImages,
      };
      existingArray.push(newEntry);
      localStorage.setItem('myDummyArray', JSON.stringify(existingArray));
      onSave(values);
    },
  });
  const [showProgress, setShowProgress] = useState(false);
  const [uploadedImageSuccessFully, setUploadedImageSuccessFully] = useState(false);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      const imageUrls = Array.from(files).map((file) => URL.createObjectURL(file));
      setUploadedImages(imageUrls);
      setFieldValue('images', Array.from(files));
      setShowProgress(true);
      setTimeout(() => {
        setShowProgress(false);
        if (showProgress === false) setUploadedImageSuccessFully(true);
      }, 2000);
    }
  };
  return (
    <Box mt={3}>
      <form noValidate onSubmit={handleSubmit}>
        <Stack
          sx={{
            height: 'calc(100vh - 150px)',
            overflow: 'auto',
            '&::-webkit-scrollbar': {
              width: 0,
            },
          }}
          direction="column"
        >
          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Album Name
            </Typography>
            <TextField
              placeholder="Album Name"
              name="albumName"
              value={albumName}
              onChange={handleChange}
              error={touched.albumName && !!errors.albumName}
              helperText={errors.albumName}
              disabled={isSubmitting}
            />
          </FormControl>
          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Image Title
            </Typography>
            <TextField
              placeholder="Image Title"
              name="imageTitle"
              value={imageTitle}
              onChange={handleChange}
              error={touched.imageTitle && !!errors.imageTitle}
              helperText={errors.imageTitle}
              disabled={isSubmitting}
            />
          </FormControl>
          {!uploadedImageSuccessFully && (
            <Box>
              <Box
                sx={{
                  mt: 3,
                  px: 3,
                  height: 150,
                  border: '2px dashed #e8e8e9',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
              >
                <Stack>
                  <IconButton
                    sx={{
                      '&.MuiIconButton-root:hover': {
                        backgroundColor: 'transparent',
                      },
                    }}
                    color="primary"
                    aria-label="upload picture"
                    component="label"
                    disableTouchRipple
                  >
                    <TextField
                      hidden
                      inputProps={{ accept: 'image/*' }}
                      value={imageFile}
                      name="imageFile"
                      onChange={handleFileChange}
                      type="file"
                      error={touched.imageFile && !!errors.imageFile}
                      helperText={errors.imageFile}
                      disabled={isSubmitting}
                    />
                    <Stack alignItems="center">
                      <TbCloudUpload fontSize={30} />
                      <Typography variant="h6" fontSize={12}>
                        Browse File
                      </Typography>
                      <Typography variant="h6" fontSize={13} color="secondary">
                        or drag and drop
                      </Typography>
                    </Stack>
                  </IconButton>
                  {showProgress && <PrgressUploadImage />}
                </Stack>
              </Box>
              {/* <Typography variant="subtitle1" color="error" fontSize={12} ml={2.5}>
                {touched.imageFile && !!errors.imageFile}
                {errors.imageFile}
              </Typography> */}
            </Box>
          )}
          {uploadedImageSuccessFully && (
            <Box sx={{ mt: 2, position: 'relative', width: 360 }}>
              <IconButton
                size="small"
                sx={{
                  position: 'absolute',
                  right: 5,
                  top: 5,
                  border: `1px solid ${theme.palette.grey[300]}`,
                  backgroundColor: '#fff',
                  '&:hover': {
                    backgroundColor: theme.palette.grey[200],
                  },
                }}
                onClick={() => {
                  setUploadedImageSuccessFully(false);
                  setUploadedImages(''); // Clear the uploaded images
                }}
              >
                <CloseIcon fontSize="small" />
              </IconButton>
              <Box display="flex">
                <img
                  style={{ borderRadius: '10px' }}
                  width="100%"
                  src={uploadedImages}
                  alt="Uploadedimage"
                  height="100%"
                />
                ;
              </Box>
            </Box>
          )}
        </Stack>
        <Box sx={{ justifyContent: 'center', pt: 3, position: 'relative' }}>
          <Stack spacing={2} direction="row" sx={{}}>
            <Button onClick={onCancel} fullWidth variant="contained" color="secondary" type="button">
              Cancel
            </Button>
            <LoadingButton
              loading={isSubmitting}
              fullWidth
              variant="contained"
              color="primary"
              type="submit"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Saving...' : 'Save'}
            </LoadingButton>
          </Stack>
        </Box>
      </form>
    </Box>
  );
}

export default CreatePhotoForm;
