/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  Collapse,
  FormControl,
  Checkbox,
} from '@mui/material';
import styled from 'styled-components';
import SearchIcon from '@mui/icons-material/Search';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { CLASS_SELECT, YEAR_SELECT } from '@/config/Selection';

const FeeAlertPendingListRoot = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      height: calc(100% - 45px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;
const dummyData = [
  {
    slNo: 1,
    rollNo: 1,
    pending: 750,
    class: 'X-A',
    student: 'Test Student',
    parent: 'Parent',
    phone: 7558966668,
  },
  {
    slNo: 2,
    rollNo: 2,
    pending: 750,
    class: 'X-A',
    student: 'Test Student',
    parent: 'Parent',
    phone: 7558966668,
  },
  {
    slNo: 3,
    rollNo: 3,
    pending: 750,
    class: 'X-A',
    student: 'Test Student',
    parent: 'Parent',
    phone: 7558966668,
  },
  {
    slNo: 4,
    rollNo: 4,
    pending: 750,
    class: 'X-A',
    student: 'Test Student',
    parent: 'Parent',
    phone: 7558966668,
  },
  {
    slNo: 5,
    rollNo: 5,
    pending: 750,
    class: 'X-A',
    student: 'Test Student',
    parent: 'Parent',
    phone: 7558966668,
  },
  {
    slNo: 6,
    rollNo: 6,
    pending: 750,
    class: 'X-A',
    student: 'Test Student',
    parent: 'Parent',
    phone: 7558966668,
  },
  {
    slNo: 7,
    rollNo: 7,
    pending: 750,
    class: 'X-A',
    student: 'Test Student',
    parent: 'Parent',
    phone: 7558966668,
  },
  {
    slNo: 8,
    rollNo: 8,
    pending: 750,
    class: 'X-A',
    student: 'Test Student',
    parent: 'Parent',
    phone: 7558966668,
  },
  {
    slNo: 9,
    rollNo: 9,
    pending: 750,
    class: 'X-A',
    student: 'Test Student',
    parent: 'Parent',
    phone: 7558966668,
  },
  {
    slNo: 10,
    rollNo: 10,
    pending: 750,
    class: 'X-A',
    student: 'Test Student',
    parent: 'Parent',
    phone: 7558966668,
  },
  {
    slNo: 11,
    rollNo: 11,
    pending: 750,
    class: 'X-A',
    student: 'Test Student',
    parent: 'Parent',
    phone: 7558966668,
  },
  {
    slNo: 12,
    rollNo: 12,
    pending: 750,
    class: 'X-A',
    student: 'Test Student',
    parent: 'Parent',
    phone: 7558966668,
  },
];
function FeeAlertPendingList() {
  const [showFilter, setShowFilter] = useState(false);

  const getRowKey = useCallback((row: any) => row.examId, []);
  const FeeAlertPendingListColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'select',
        renderHeader: () => {
          return <Checkbox />;
        },
        renderCell: () => {
          return <Checkbox />;
        },
      },
      {
        name: 'slNo',
        dataKey: 'slNo',
        headerLabel: 'Sl.No',
        sortable: true,
      },
      {
        name: 'student',
        dataKey: 'student',
        headerLabel: 'Student',
      },
      {
        name: 'rollNo',
        dataKey: 'rollNo',
        headerLabel: 'Roll No',
      },
      {
        name: 'parent',
        dataKey: 'parent',
        headerLabel: 'Contact Name',
      },
      {
        name: 'phone',
        dataKey: 'phone',
        headerLabel: 'Phone No.',
      },
    ],
    []
  );

  return (
    <Page title="Fee Alert">
      <FeeAlertPendingListRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17}>
              Fee Alert Pending List
            </Typography>
            <Box pb={1} sx={{ flexShrink: 0 }}>
              <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                <SearchIcon />
              </IconButton>
            </Box>
          </Stack>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={3} alignItems="end">
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 180 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Autocomplete
                        options={YEAR_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select Year" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 180 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Autocomplete
                        options={CLASS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select Class" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 180 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Term/Month
                      </Typography>
                      <Autocomplete
                        options={['Term - 1', 'Term - 2', 'Term - 3', 'Term - 4', 'Term - 5']}
                        renderInput={(params) => <TextField {...params} placeholder="Select Term" />}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item lg="auto" xs={12} sm={6}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Paper className="card-table-container" sx={{ marginTop: '12px' }}>
              <DataTable
                columns={FeeAlertPendingListColumns}
                data={dummyData}
                getRowKey={getRowKey}
                fetchStatus="success"
              />
            </Paper>
            <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 2 }}>
              <Stack spacing={2} direction="row">
                <Button variant="contained" color="secondary">
                  Cancel
                </Button>
                <Button variant="contained" color="primary">
                  Send
                </Button>
              </Stack>
            </Box>
          </div>
        </Card>
      </FeeAlertPendingListRoot>
    </Page>
  );
}

export default FeeAlertPendingList;
