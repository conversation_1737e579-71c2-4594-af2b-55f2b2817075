/* eslint-disable prettier/prettier */
import * as React from 'react';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';
import Page from '@/components/shared/Page';
import styled from 'styled-components';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import NewStudent from '@/features/ManageStudents/NewStudent';
import StudentParentInfo from '@/features/ManageStudents/StudentInfo/StudentParentInfo';
import StudentClassReAllocation from '@/features/ManageStudents/StudentReAllocation/StudentClassReAllocation';
import PromoteStudents from '@/features/ManageStudents/PromoteStudents';
import StudentsRemark from '@/features/ManageStudents/StudentsRemark';
import StudentsExtracurricular from '@/features/ManageStudents/StudentsExtracurricular';
import { Link, useLocation, useOutletContext } from 'react-router-dom';
import QuickUpdateStudent from '@/features/ManageStudents/QuickUpdateStudent';
import { useDispatch } from 'react-redux';
import { flushStore } from '@/store/flush.slice';
import { OutletContextType } from '@/types/Common';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const ManageStudentsRoot = styled.div`
  min-height: calc(100vh - ${TOP_BAR_HEIGHT});
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  .tab {
    background-color: ${(props) => (props.theme.themeMode === 'light' ? '#fff' : props.theme.palette.grey[800])};
  }
  .Mui-selected {
    color: ${(props) => props.theme.palette.primary.main};
  }
`;

function TabPanel({ children, value, index }: TabPanelProps) {
  return (
    <Box
      sx={{ paddingTop: '3rem' }}
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
    >
      {value === index && <Box>{children}</Box>}
    </Box>
  );
}

function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`,
  };
}

export default function ManageStudents() {
  const { topBarHeight } = useOutletContext<OutletContextType>();
  const location = useLocation();
  const [value, setValue] = React.useState(0);

  const dispatch = useDispatch();

  React.useEffect(() => {
    dispatch(flushStore());
    console.log('Flush Store');
  }, [dispatch, value]);

  React.useEffect(() => {
    const currentPath = location.pathname;
    const routePaths = [
      '/manage-students/new',
      '/manage-students/info',
      '/manage-students/quick-update',
      '/manage-students/reallocate',
      '/manage-students/promote',
      '/manage-students/remarks',
      '/manage-students/extracurricular',
    ];
    const index = routePaths.indexOf(currentPath);
    if (index !== -1) {
      setValue(index);
    }
  }, [location]);

  return (
    <Page title="Attendance Marking">
      <ManageStudentsRoot>
        <Tabs
          className="tab"
          variant="scrollable"
          scrollButtons
          allowScrollButtonsMobile
          value={value}
          aria-label="basic tabs"
          sx={{
            ml: '0px',
            borderBottom: 1,
            borderColor: 'divider',
            position: 'fixed',
             top: `${topBarHeight}px`,
            zIndex: 1,
            width: '100%',
          }}
        >
          <Tab {...a11yProps(0)} label="New Student" component={Link} to="/manage-students/new" />
          <Tab {...a11yProps(1)} label="Student Info" component={Link} to="/manage-students/info" />
          <Tab {...a11yProps(2)} label="Quick Update" component={Link} to="/manage-students/quick-update" />
          <Tab
            {...a11yProps(3)}
            label="Student Class Re-Allocation"
            component={Link}
            to="/manage-students/reallocate"
          />
          <Tab {...a11yProps(4)} label="Promote Students" component={Link} to="/manage-students/promote" />
          <Tab {...a11yProps(5)} label="Students Remark" component={Link} to="/manage-students/remarks" />
          <Tab
            {...a11yProps(6)}
            label="Students Extracurricular"
            component={Link}
            to="/manage-students/extracurricular"
          />
          {/* <Tab label="Gaurdian Quick Update" {...a11yProps(2)} />
          <Tab label="Gender Quick Update" {...a11yProps(3)} />
          <Tab label="Manage Convoyers" {...a11yProps(4)} /> */}
          {/* <Tab label="Re-Allocate Select Student" {...a11yProps(6)} /> */}
          {/* <Tab label="Promote Selected Students" {...a11yProps(8)} /> */}
          {/* <Tab label="Remove Students" {...a11yProps(4)} /> */}
        </Tabs>
        <TabPanel value={value} index={0}>
          <NewStudent />
        </TabPanel>
        <TabPanel value={value} index={1}>
          <StudentParentInfo />
        </TabPanel>
        <TabPanel value={value} index={2}>
          <QuickUpdateStudent />
        </TabPanel>
        <TabPanel value={value} index={3}>
          <StudentClassReAllocation />
        </TabPanel>
        {/* <TabPanel value={value} index={2}>
          <GaurdianQuickUpdate />
        </TabPanel>
        <TabPanel value={value} index={3}>
          <GenderQuickUpdate />
        </TabPanel>
        {/* <TabPanel value={value} index={3}>
          <ReAllocateSelectStudent />
        </TabPanel> */}
        <TabPanel value={value} index={4}>
          <PromoteStudents />
        </TabPanel>
        {/* <TabPanel value={value} index={5}>
          <SelectedStudentsPromote />
        </TabPanel> */}
        {/* <TabPanel value={value} index={6}>
          <RemoveStudents />
        </TabPanel> */}
        {/* <TabPanel value={value} index={4}>
          <RemoveSelectedStudents />
        </TabPanel> */}
        <TabPanel value={value} index={5}>
          <StudentsRemark />
        </TabPanel>
        <TabPanel value={value} index={6}>
          <StudentsExtracurricular />
        </TabPanel>
      </ManageStudentsRoot>
    </Page>
  );
}
