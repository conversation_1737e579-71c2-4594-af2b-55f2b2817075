// src/react-export-excel.d.ts
declare module 'react-export-excel' {
    import * as React from 'react';

    export interface ExcelFileProps {
        element?: React.ReactNode;
        filename?: string;
    }

    export class ExcelFile extends React.Component<ExcelFileProps> {}

    export interface ExcelSheetProps {
        data: any[];
        name: string;
    }

    export class ExcelSheet extends React.Component<ExcelSheetProps> {}

    export interface ExcelColumnProps {
        label: string;
        value: string;
    }

    export class ExcelColumn extends React.Component<ExcelColumnProps> {}
}
