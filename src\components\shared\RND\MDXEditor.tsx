import React from 'react';
import { Box, Typography } from '@mui/material';
import {
  MDXEditor,
  headingsPlugin,
  listsPlugin,
  quotePlugin,
  codeBlockPlugin,
  linkPlugin,
  toolbarPlugin,
  markdownShortcutPlugin,
  frontmatterPlugin,
} from '@mdxeditor/editor';

import '@mdxeditor/editor/style.css';

interface CustomMDXEditorProps {
  value: string;
  onChange: (val: string) => void;
  label?: string;
  height?: number;
  placeholder?: string;
}

const CustomMDXEditor: React.FC<CustomMDXEditorProps> = ({
  value,
  onChange,
  label = 'MDX Editor',
  height = 300,
  placeholder = 'Start writing...',
}) => {
  return (
    <Box>
      {label && (
        <Typography variant="subtitle1" sx={{ mb: 1, fontWeight: 600 }}>
          {label}
        </Typography>
      )}
      <Box
        sx={{
          border: '1px solid #ccc',
          borderRadius: 2,
          height,
          overflow: 'hidden',
        }}
      >
        <MDXEditor
          markdown={value}
          onChange={onChange}
          placeholder={placeholder}
          contentEditableClassName="editor-content"
          plugins={[
            toolbarPlugin(),
            headingsPlugin(),
            listsPlugin(),
            quotePlugin(),
            codeBlockPlugin(),
            linkPlugin(),
            markdownShortcutPlugin(),
            frontmatterPlugin(),
          ]}
        />
      </Box>
    </Box>
  );
};

export default CustomMDXEditor;
