/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Typography,
  Card,
  IconButton,
} from '@mui/material';
import styled, { useTheme } from 'styled-components';
import { CLASS_SELECT, YEAR_SELECT } from '@/config/Selection';
import { MdAdd } from 'react-icons/md';
import { EventsDetailsList } from '@/config/Events';
import DeleteIcon from '@mui/icons-material/Delete';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import Popup from '@/components/shared/Popup/Popup';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import { Create } from './Create&Edit';
// import { EventsDetailsListType } from '@/types/AcademicManagement';

const EventDetailsListRoot = styled.div`
  padding: 1rem;

  img {
    max-height: 100px;
  }
  .Card {
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }
  }
  .thumb_image {
    border-radius: 8px;
    width: 100%;
  }
  .MuiTableHead-root {
    .MuiTableCell-root {
      font-size: 12px;
    }
  }
`;

function EventDetailsList() {
  const theme = useTheme();
  const [Delete, setDelete] = React.useState(false);
  const [drawerOpen, setDrawerOpen] = React.useState<boolean>(false);
  const [popupSuccess, setPopupSuccess] = React.useState(false);
  const [titles, setTitles] = React.useState('');
  // const [datas, setDatas] = React.useState<EventsDetailsListType[] | undefined | null>([]);

  const handleClickDelete = () => setDelete(true);
  const handleClickCloseDelete = () => setDelete(false);

  const toggleDrawerOpen = (title: string) => {
    setDrawerOpen(true);
    setTitles(title);
    // setDatas(row);
    console.log(title);
  };
  const toggleDrawerOpenCreate = (title: any) => {
    setDrawerOpen(true);
    setTitles(title);
    console.log(title);
  };

  const toggleDrawerClose = () => {
    setDrawerOpen(false);
  };

  const handleClickOpen = () => {
    setDrawerOpen(false);
    setPopupSuccess(true);
  };

  const handleClickClose = () => setPopupSuccess(false);
  return (
    <Page title="Events Details List">
      <EventDetailsListRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17}>
              Events Details List
            </Typography>
            <Box pb={1}>
              <Button
                sx={{ borderRadius: '20px' }}
                variant="outlined"
                size="small"
                onClick={() => toggleDrawerOpenCreate('Create')}
              >
                <MdAdd size="20px" /> Create
              </Button>
            </Box>
          </Stack>
          <Divider />
          <Grid pb={4} pt={2} container spacing={3}>
            <Grid item lg={3} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Academic Year
              </Typography>
              <Autocomplete
                options={YEAR_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select" />}
              />
            </Grid>
            <Grid item lg={3} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Event Type
              </Typography>
              <Autocomplete
                options={CLASS_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select" />}
              />
            </Grid>
            <Grid item lg={3} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Event Title
              </Typography>
              <TextField placeholder="Enter Name" fullWidth />
            </Grid>
            <Grid item lg={3} xs={12}>
              <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                <Button variant="contained" color="secondary" fullWidth>
                  Reset
                </Button>
                <Button variant="contained" color="primary" fullWidth>
                  Search
                </Button>
              </Stack>
            </Grid>
          </Grid>

          <Box>
            <Paper
              sx={{
                border: `1px solid #e8e8e9`,
                width: '100%',
                overflow: 'auto',
                '&::-webkit-scrollbar': {
                  width: 0,
                },
              }}
            >
              <TableContainer
                sx={{
                  width: { xs: '1200px', md: '100%' },
                }}
              >
                <Table stickyHeader aria-label="simple table">
                  <TableHead>
                    <TableRow>
                      <TableCell>Sl.No</TableCell>
                      <TableCell>Event Type</TableCell>
                      <TableCell>Title</TableCell>
                      <TableCell>Description</TableCell>
                      <TableCell>Thumbnail</TableCell>
                      <TableCell>Video/YouTube Link</TableCell>
                      <TableCell>Academic Year</TableCell>
                      <TableCell>Created By</TableCell>
                      <TableCell>Action</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {EventsDetailsList?.map((row) => (
                      <TableRow hover key={row.slno}>
                        <TableCell width={20}>{row.slno}</TableCell>
                        <TableCell width={80} sx={{ fontSize: '10px', fontWeight: 600 }}>
                          {row.type}
                        </TableCell>
                        <TableCell width={80} sx={{ fontSize: '10px', fontWeight: 600 }}>
                          {row.title}
                        </TableCell>
                        <TableCell width={150} sx={{ fontSize: '10px', fontWeight: 600 }}>
                          {row.Description}
                        </TableCell>
                        <TableCell width={80}>
                          <img src={row.thumbnail} className="thumb_image" alt="EventImage" />
                        </TableCell>
                        <TableCell width={130} sx={{ fontSize: '8px', fontWeight: 600 }}>
                          <Paper
                            sx={{
                              border: '1px solid',
                              display: 'flex',
                              justifyContent: 'center',
                              p: 0.5,
                              maxWidth: 120,
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap',
                              overflow: 'hidden',
                              borderRadius: '20px',
                              backgroundColor: theme.palette.chart.violet[4],
                              color: theme.palette.chart.violet[0],
                            }}
                          >
                            <Typography
                              sx={{
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap',
                                overflow: 'hidden',
                              }}
                              fontSize={9}
                              fontWeight={600}
                            >
                              {row.link}
                            </Typography>
                          </Paper>
                        </TableCell>
                        <TableCell width={120} sx={{ fontSize: '10px', fontWeight: 600 }}>
                          {row.year}
                        </TableCell>
                        <TableCell width={100} sx={{ fontSize: '10px', fontWeight: 600 }}>
                          {row.create}
                        </TableCell>
                        <TableCell width={100}>
                          <Stack direction="row" gap={1}>
                            <IconButton size="small" onClick={() => toggleDrawerOpen('Edit')}>
                              <ModeEditIcon />
                            </IconButton>
                            <IconButton onClick={handleClickDelete} color="error" size="small">
                              <DeleteIcon />
                            </IconButton>
                          </Stack>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Paper>
          </Box>
        </Card>
      </EventDetailsListRoot>
      <Popup
        size="xs"
        state={Delete}
        onClose={handleClickCloseDelete}
        popupContent={<DeleteMessage message="Are you sure want to delete?" />}
      />
      <Popup
        size="xs"
        state={popupSuccess}
        onClose={handleClickClose}
        popupContent={<SuccessMessage message="File updated" />}
      />
      <TemporaryDrawer
        onClose={toggleDrawerClose}
        Title={titles}
        state={drawerOpen}
        DrawerContent={<Create open={handleClickOpen} onClose={toggleDrawerClose} />}
      />
    </Page>
  );
}

export default EventDetailsList;
