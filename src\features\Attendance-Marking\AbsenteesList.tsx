/* eslint-disable jsx-a11y/alt-text */
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Avatar,
  Typography,
  Card,
} from '@mui/material';
import styled from 'styled-components';
import Pdf from '@/assets/attendance/PDF.svg';
import { lnrows } from '@/config/TableData';
import { CLASS_SELECT, YEAR_SELECT } from '@/config/Selection';
import DateSelect from '@/components/shared/Selections/DateSelect';

const AbsenteesListRoot = styled.div`
  padding: 1rem;
  .Card {
    height: calc(100vh - 160px);
    @media screen and (max-width: 1199.5px) {
      height: 100%;
    }
  }
`;

function AbsenteesList() {
  return (
    <Page title="Detailed Class Marking">
      <AbsenteesListRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Typography variant="h6" fontSize={17}>
            Absentees List
          </Typography>
          <Divider />
          <Grid pt={2} pb={4} container spacing={3}>
            <Grid item lg={2.4} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Select Year
              </Typography>
              <Autocomplete
                options={YEAR_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select" />}
              />
            </Grid>
            <Grid item lg={2.4} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Select Class
              </Typography>
              <Autocomplete
                options={CLASS_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select" />}
              />
            </Grid>
            <Grid item lg={2.4} xs={12}>
              <Typography variant="h6" fontSize={14}>
                From
              </Typography>
              <DateSelect />
            </Grid>
            <Grid item lg={2.4} xs={12}>
              <Typography variant="h6" fontSize={14}>
                To
              </Typography>
              <DateSelect />
            </Grid>
            <Grid item lg={2.4} xs={12}>
              <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                <Button variant="contained" color="secondary" fullWidth>
                  Reset
                </Button>
                <Button variant="contained" color="primary" fullWidth>
                  Search
                </Button>
              </Stack>
            </Grid>
          </Grid>

          <Divider />

          <Box>
            <Paper
              sx={{
                border: `.0625rem solid #e8e8e9`,
                width: '100%',
                height: '100%',
                overflow: 'auto',
                '&::-webkit-scrollbar': {
                  width: 0,
                },
              }}
            >
              <TableContainer
                sx={{
                  height: 'calc(100vh - 375px)',
                  width: { xs: '43.75rem', md: '100%' },
                }}
              >
                <Table stickyHeader aria-label="simple table">
                  <TableHead>
                    <TableRow>
                      <TableCell>Sl.No</TableCell>
                      <TableCell>Student Name</TableCell>
                      <TableCell>Class</TableCell>
                      <TableCell>Roll.No</TableCell>
                      <TableCell>Absent Date</TableCell>
                      <TableCell>Academic Year</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {lnrows.map((lnrow) => (
                      <TableRow hover key={lnrow.id} sx={{ borderBottom: '.0625rem solid rgb(200,200,200)' }}>
                        <TableCell>{lnrow.id}</TableCell>
                        <TableCell>
                          <Box display="flex" alignItems="center" gap={1}>
                            <Avatar src="" />
                            <span>{lnrow.name}</span>
                          </Box>
                        </TableCell>
                        <TableCell>{lnrow.class}</TableCell>
                        <TableCell>{lnrow.id}</TableCell>
                        <TableCell>12/06/2023</TableCell>
                        <TableCell>2023-2024</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Paper>
          </Box>
          <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 2 }}>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="error" sx={{ gap: 1 }} size="medium">
                <img className="icon" src={Pdf} />{' '}
                <Typography color="white" fontSize={12}>
                  Download
                </Typography>
              </Button>
            </Stack>
          </Box>
        </Card>
      </AbsenteesListRoot>
    </Page>
  );
}

export default AbsenteesList;
