/* eslint-disable no-lonely-if */
/* eslint-disable no-else-return */
import React, { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { Box, Button, Dialog, DialogActions, DialogContent, DialogTitle } from '@mui/material';
import CropIcon from '@mui/icons-material/Crop';
import { <PERSON><PERSON>per, CropperRef } from 'react-advanced-cropper';
import 'react-advanced-cropper/dist/style.css';
import LoadingButton from '@mui/lab/LoadingButton';

type FileObjTypes = {
  id: number;
  name: string;
  type: string;
  imageUrl?: string;
  originalFile: File;
  // fieldName?: string | undefined;
};

interface ImageCropperProps {
  uploadedFile: FileObjTypes | undefined | string;
  onImageUpload?: (event: React.ChangeEvent<HTMLInputElement>, fieldName?: string | undefined) => void | undefined;
  uploadButton: React.ReactNode;
  // setUploaded: Dispatch<SetStateAction<File[]>>;
  setUploaded: Dispatch<SetStateAction<FileObjTypes[]>>;
  uploaded: FileObjTypes[];
  setIsDialogOpen: Dispatch<boolean>;
  isDialogOpen: boolean;
  multiple?: any;
}

const ImageCropper: React.FC<ImageCropperProps> = ({
  uploadedFile,
  onImageUpload,
  uploadButton,
  setUploaded,
  uploaded,
  setIsDialogOpen,
  isDialogOpen,
  multiple,
}) => {
  const [imageSrc, setImageSrc] = useState<string | null>(null);
  // const [currentImage, setCurrentImage] = useState<File | null>(null); // Store the image to edit
  const [loading, setLoading] = useState<boolean>(false);
  const cropperRef = React.useRef<CropperRef>(null);

  useEffect(() => {
    if (uploadedFile?.originalFile) {
      const reader = new FileReader();
      reader.onload = () => {
        setImageSrc(reader.result as string);
      };
      reader.readAsDataURL(uploadedFile.originalFile); // Use the original File instance
    }
  }, [uploadedFile]);

  const handleCrop = async () => {
    setLoading(true);

    const existFile = uploaded?.find((f) => f.id === uploadedFile?.id);
    if (existFile) {
      console.log('Exist File:', existFile.id);
    } else {
      console.log('File not found in uploaded array.');
    }

    if (cropperRef.current) {
      requestAnimationFrame(() => {
        cropperRef.current.getCanvas().toBlob(
          (blob) => {
            if (blob) {
              const reader = new FileReader();
              reader.onloadend = () => {
                const imgUrl = URL.createObjectURL(blob);
                const fileName = uploadedFile?.name || `Image_${Date.now()}`;
                const fieldName = uploadedFile?.fieldName;
                const randomId = Math.floor(Math.random() * 1_000_000);
                const croppedFile = new File([blob], fileName, { type: blob.type });

                const uploadedFileData = {
                  id: randomId,
                  name: fileName,
                  type: blob.type,
                  imageUrl: imgUrl,
                  originalFile: croppedFile,
                  fieldName,
                };

                setUploaded((prevImages: any) => {
                  if (existFile) {
                    // Replace the existing file in the array
                    return prevImages.map((file: any) => (file.id === existFile.id ? uploadedFileData : file));
                  } else {
                    // Add new file to the array
                    if (multiple) {
                      return [...prevImages, uploadedFileData]; // Append file if multiple uploads are allowed
                    } else {
                      return [
                        ...prevImages.filter((f: any) => f.fieldName !== uploadedFileData.fieldName), // Remove previous file with the same fieldName
                        uploadedFileData, // Add new file object
                      ];
                    }
                  }
                });

                console.log('Uploaded File:', uploadedFile);
                console.log('Uploaded File:', uploaded);
                console.log('Uploaded File Data:', uploadedFileData);
                setLoading(false);
                setIsDialogOpen(false);
              };

              reader.readAsDataURL(blob);
            } else {
              console.error('Failed to convert canvas to Blob.');
              setLoading(false);
            }
          },
          'image/png',
          0.9
        );
      });
    }
  };

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, width: '100%' }}>
      {uploadButton}
      <Dialog open={isDialogOpen} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>Crop Image</DialogTitle>
        <DialogContent>
          {imageSrc && <Cropper src={imageSrc} ref={cropperRef} style={{ width: '100%', height: '400px' }} />}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <LoadingButton
            loading={loading}
            loadingPosition="start"
            onClick={handleCrop}
            color="primary"
            variant="contained"
          >
            {loading ? 'Saving...' : 'Save'}
          </LoadingButton>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ImageCropper;
