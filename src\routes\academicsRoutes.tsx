import Loadable from '@/components/shared/Loadable';
import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';

const AcademicManagement = Loadable(lazy(() => import('@/pages/AcademicManagement')));
const ManageYear = Loadable(lazy(() => import('@/features/AcademicManagement/ManageYear/ManageYear')));
const ManageClass = Loadable(lazy(() => import('@/features/AcademicManagement/ManageClass/ManageClass')));
const ManageSection = Loadable(lazy(() => import('@/features/AcademicManagement/ManageSection/ManageSection')));
const ClassSort = Loadable(lazy(() => import('@/features/AcademicManagement/ClassSort/ClassSort')));
const Subject = Loadable(lazy(() => import('@/features/AcademicManagement/ManageSubject/ManageSubject')));
const SubjectCategory = Loadable(lazy(() => import('@/features/AcademicManagement/SubjectCategory/SubjectCategory')));
const LanguageStudent = Loadable(lazy(() => import('@/features/AcademicManagement/LanguageStudent/LanguageStudent')));
const MaterialsList = Loadable(lazy(() => import('@/features/AcademicManagement/MaterialsList/MaterialsList')));
const EventDetailsList = Loadable(lazy(() => import('@/features/AcademicManagement/EventDetails/EventDetailsList')));

export const academicsRoutes: RouteObject[] = [
  {
    path: '/academic-management',
    element: <AcademicManagement />,
    children: [
      {
        path: 'year',
        element: <ManageYear />,
      },
      {
        path: 'class',
        element: <ManageClass />,
      },
      {
        path: 'class-section',
        element: <ManageSection />,
      },
      {
        path: 'sort-class',
        element: <ClassSort />,
      },
      {
        path: 'subject',
        element: <Subject />,
      },
      {
        path: 'category-subject',
        element: <SubjectCategory />,
      },
      {
        path: 'language-student',
        element: <LanguageStudent />,
      },
      {
        path: 'study-materials',
        element: <MaterialsList />,
      },
      {
        path: 'events',
        element: <EventDetailsList />,
      },
    ],
  },
];
