import { PayloadAction, createSlice } from '@reduxjs/toolkit';
import { NotificationDataType, NotificationState } from '@/types/Notification';
import {
  DeleteNotificationList,
  createNotification,
  fetchNotificationList,
  fileUpload,
  notificationSendToAllParents,
  notificationSendToAllStaff,
  notificationSendToClassDivision,
  notificationSendToClassSection,
  notificationSendToParentsList,
  notificationSendToStaffList,
} from './appNotification.thunks';
import { flushStore } from '../flush.slice';

const initialState: NotificationState = {
  notificationList: {
    data: [],
    status: 'idle',
    error: null,
  },
  filesUploadList: {
    data: [],
    status: 'idle',
    error: null,
  },
  submitting: false,
  deletingRecords: {},
  error: null,
};

export const notificationSlice = createSlice({
  name: 'notificationList',
  initialState,
  reducers: {
    setnotificationList: (state, action: PayloadAction<NotificationDataType[]>) => {
      state.notificationList.data = action.payload;
    },
    setSubmitting: (state, action: PayloadAction<boolean>) => {
      state.submitting = action.payload;
    },
    // setProgress: (state, action: PayloadAction<number>) => {
    //   state.filesUploadList.progress = action.payload;
    // },
  },
  extraReducers(builder) {
    builder
      // extraReducers for fetching Notification List
      // ------Get------- //
      .addCase(fetchNotificationList.pending, (state) => {
        state.notificationList.status = 'loading';
        state.notificationList.error = null;
      })
      .addCase(fetchNotificationList.fulfilled, (state, action) => {
        state.notificationList.status = 'success';
        state.notificationList.data = action.payload;
        console.log('action.payload::::', action.payload);
        state.notificationList.error = null;
      })
      .addCase(fetchNotificationList.rejected, (state, action) => {
        state.notificationList.status = 'error';
        state.notificationList.error = action.payload || 'Unknown error in fetching Notification Template';
      })
      // ------Create------- //
      .addCase(createNotification.pending, (state) => {
        state.submitting = true;
        state.notificationList.error = null;
      })
      .addCase(createNotification.fulfilled, (state) => {
        state.submitting = false;
        state.notificationList.error = null;
      })
      .addCase(createNotification.rejected, (state, action) => {
        state.submitting = false;
        state.notificationList.error = action.error.message || 'Unknown error in fetching Notification List';
      })

      // ------File Upload------- //
      .addCase(fileUpload.pending, (state) => {
        state.submitting = true;
        state.filesUploadList.error = null;
      })
      .addCase(fileUpload.fulfilled, (state, action) => {
        state.submitting = false;
        state.filesUploadList.data = action.payload;
        state.filesUploadList.error = null;
      })
      .addCase(fileUpload.rejected, (state, action) => {
        state.submitting = false;
        state.filesUploadList.error = action.error.message || 'Unknown error in fetching Notification File Upload';
      })
      .addCase(DeleteNotificationList.pending, (state, action) => {
        const { notificationId } = action.meta.arg;
        state.deletingRecords[notificationId] = true;
        state.error = null;
      })
      .addCase(DeleteNotificationList.fulfilled, (state, action) => {
        const { notificationId } = action.meta.arg;
        if (action.payload.deleted) {
          const { [notificationId]: deletedRecord, ...restDeletingRecords } = state.deletingRecords;
          state.deletingRecords = restDeletingRecords;
        }
        state.error = null;
      })
      .addCase(DeleteNotificationList.rejected, (state, action) => {
        state.submitting = false;
        state.error = action.error.message || 'Unknown error in deleting Notification Template';
      })
      // -----Send To Parents List----- //
      .addCase(notificationSendToParentsList.pending, (state) => {
        state.submitting = true;
        state.notificationList.error = null;
      })
      .addCase(notificationSendToParentsList.fulfilled, (state) => {
        state.submitting = false;
        state.notificationList.error = null;
      })
      .addCase(notificationSendToParentsList.rejected, (state, action) => {
        state.submitting = false;
        state.notificationList.error = action.error.message || 'Unknown error in sending Notification to Parents List';
      })
      // -----Send To Staff List----- //
      .addCase(notificationSendToStaffList.pending, (state) => {
        state.submitting = true;
        state.notificationList.error = null;
      })
      .addCase(notificationSendToStaffList.fulfilled, (state) => {
        state.submitting = false;
        state.notificationList.error = null;
      })
      .addCase(notificationSendToStaffList.rejected, (state, action) => {
        state.submitting = false;
        state.notificationList.error = action.error.message || 'Unknown error in sending Notification to Staff List';
      })
      // -----Send To ClassDivision List----- //
      .addCase(notificationSendToClassDivision.pending, (state) => {
        state.submitting = true;
        state.notificationList.error = null;
      })
      .addCase(notificationSendToClassDivision.fulfilled, (state) => {
        state.submitting = false;
        state.notificationList.error = null;
      })
      .addCase(notificationSendToClassDivision.rejected, (state, action) => {
        state.submitting = false;
        state.notificationList.error =
          action.error.message || 'Unknown error in sending Notification to Class Division';
      })
      // -----Send To ClassSection List----- //
      .addCase(notificationSendToClassSection.pending, (state) => {
        state.submitting = true;
        state.notificationList.error = null;
      })
      .addCase(notificationSendToClassSection.fulfilled, (state) => {
        state.submitting = false;
        state.notificationList.error = null;
      })
      .addCase(notificationSendToClassSection.rejected, (state, action) => {
        state.submitting = false;
        state.notificationList.error = action.error.message || 'Unknown error in sending Notification to Class Section';
      })
      // -----Send To All Parent----- //
      .addCase(notificationSendToAllParents.pending, (state) => {
        state.submitting = true;
        state.notificationList.error = null;
      })
      .addCase(notificationSendToAllParents.fulfilled, (state) => {
        state.submitting = false;
        state.notificationList.error = null;
      })
      .addCase(notificationSendToAllParents.rejected, (state, action) => {
        state.submitting = false;
        state.notificationList.error =
          action.error.message || 'Unknown error in sending Notification to Group Members List';
      })
      // -----Send To All Staff----- //
      .addCase(notificationSendToAllStaff.pending, (state) => {
        state.submitting = true;
        state.notificationList.error = null;
      })
      .addCase(notificationSendToAllStaff.fulfilled, (state) => {
        state.submitting = false;
        state.notificationList.error = null;
      })
      .addCase(notificationSendToAllStaff.rejected, (state, action) => {
        state.submitting = false;
        state.notificationList.error =
          action.error.message || 'Unknown error in sending Notification to Group Members List';
      })
      .addCase(flushStore, () => initialState);
  },
});

export const { setnotificationList } = notificationSlice.actions;

export default notificationSlice.reducer;
