/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import {
  Autocomplete,
  Grid,
  Stack,
  TextField,
  Button,
  Typography,
  Box,
  FormControl,
  Select,
  MenuItem,
  RadioGroup,
  FormControlLabel,
  Radio,
  Avatar,
  IconButton,
  InputAdornment,
} from '@mui/material';
import styled from 'styled-components';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import { CLASS_SELECT, STATUS_OPTIONS, SUBJECT_SELECT, YEAR_SELECT } from '@/config/Selection';
import SelectDateTimePicker from '@/components/shared/Selections/TimePicker';
import InputFileUpload from '@/components/shared/Selections/FilesUpload';
import DateSelect from '@/components/shared/Selections/DateSelect';
import TextareaField from '@/components/shared/Selections/TextareaField';
import { ErrorIcon } from '@/theme/overrides/CustomIcons';
import CollectionsIcon from '@mui/icons-material/Collections';

const CreatePostRoot = styled.div`
  width: 100%;
  padding: 1.5rem;
`;

function CreatePost({ onClose }: any) {
  return (
    <CreatePostRoot>
      <form noValidate>
        <Stack direction="row" gap={1}>
          <Avatar
            src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?q=80&w=1780&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
            sx={{ width: '35px', height: '35px' }}
          />
          <TextareaField
            limit={1000}
            placeholder="Write your comment..."
            name="notificationContent"
            value=""
            //   onChange={handleChange}
            //   error={touched.notificationContent && !!errors.notificationContent}
            //   helperText={touched.notificationContent && errors.notificationContent}
            InputProps={{
              inputProps: {
                style: { resize: 'vertical', minHeight: '200px', maxHeight: '300px' },
                maxLength: 5000,
              },
              endAdornment: (
                <InputAdornment position="end">
                  <Box display="flex" flexDirection="column">
                    <IconButton size="small" aria-label="">
                      <CollectionsIcon />
                    </IconButton>
                  </Box>
                </InputAdornment>
              ),
            }}
          />
        </Stack>
        <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 3 }}>
          <Stack spacing={2} direction="row">
            <Button variant="contained" color="primary">
              Post
            </Button>
          </Stack>
        </Box>
      </form>
    </CreatePostRoot>
  );
}

export default CreatePost;
