/* eslint-disable react/self-closing-comp */
import styled from 'styled-components';
import Typography from '@mui/material/Typography';
import { Swiper, SwiperSlide } from 'swiper/react';
import KeyboardArrowLeftIcon from '@mui/icons-material/KeyboardArrowLeft';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import { Avatar, Stack, Box, Button, Chip, TextField } from '@mui/material';
import 'swiper/css';
import 'swiper/css/effect-coverflow';
import 'swiper/css/pagination';
import 'swiper/css/navigation';
import { Pagination, Navigation } from 'swiper';
import React, { useEffect, useRef } from 'react';
import { MarkRegisterCEDataType } from '@/types/ExamCenter';

const IndivitualEnrollCERoot = styled.div<{ theme: any }>`
  padding: 1;
  .swiper_container {
    position: relative;
    padding: 1rem;
  }
  .swiper-slide {
    position: relative;
    /* padding: 1rem 0rem; */
  }
  .swiper-button-next,
  .swiper-button-prev {
    position: absolute;
    top: var(--swiper-navigation-top-offset, 50%);
    margin-top: calc(0px - (var(--swiper-navigation-size) / 2));
    z-index: 10;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .events-card {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[800]};
    border: 1px solid
      ${(props) => (props.theme.themeMode === 'light' ? props.theme.palette.grey[300] : props.theme.palette.grey[900])};
  }
  .swiper-button-prev,
  .swiper-button-next {
    height: 30px;
    width: 30px;
    color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.primary.main : props.theme.palette.primary.main};
  }
`;

interface IndivitualEnrollCEProps {
  data: MarkRegisterCEDataType;
  setData: React.Dispatch<React.SetStateAction<MarkRegisterCEDataType[]>>;
  OnClose: () => void;
  handleNext: () => void;
  handlePrev: () => void;
  sendMarkRegisterData: (data: any, mode: 'single' | 'multiple') => void;
  passMark: string;
  outOfMark: string;
  passMarkCE: string;
  outOfMarkCE: string;
  dataCount: number;
  currentIndex: number;
  hasError: boolean;
  handleMarksChange: (studentId: number, field: string, value: string) => void;
}

const IndivitualEnrollCE = ({
  data,
  handleNext,
  handlePrev,
  OnClose,
  sendMarkRegisterData,
  passMark,
  outOfMark,
  passMarkCE,
  outOfMarkCE,
  dataCount,
  currentIndex,
  hasError,
  handleMarksChange,
}: IndivitualEnrollCEProps) => {
  const swiperRef = useRef<import('swiper').Swiper | null>(null);
  const textFieldRefs = useRef<(HTMLInputElement | null)[]>([]);

  useEffect(() => {
    textFieldRefs.current[0]?.focus();
  }, []);

  const handleKeyPress = (event: React.KeyboardEvent<HTMLDivElement>, index: number) => {
    if (event.key === 'Enter') {
      swiperRef.current?.slideNext();
      sendMarkRegisterData([data], 'single');
      handleNext();
      setTimeout(() => {
        textFieldRefs.current[index]?.focus();
      }, 100);
    }
  };

  return (
    <IndivitualEnrollCERoot>
      <Swiper
        spaceBetween={16}
        effect="coverflow"
        grabCursor
        breakpoints={{
          299: {
            slidesPerView: 1,
          },
          499: {
            slidesPerView: 1,
          },
          999: {
            slidesPerView: 1,
          },
          1599: {
            slidesPerView: 1,
          },
        }}
        coverflowEffect={{
          rotate: 0,
          depth: 0,
          stretch: 0,
        }}
        pagination={{ el: '.swiper-pagination', clickable: true }}
        navigation={{
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev',
        }}
        modules={[Pagination, Navigation]}
        className="swiper_container "
        onSwiper={(swiper) => {
          swiperRef.current = swiper;
        }}
      >
        <SwiperSlide key={data.studentId} className="swiper-slide ">
          <Box display="flex" flexDirection="column" textAlign="center" gap={1}>
            <Stack direction="row" justifyContent="center" gap={2} alignItems="center">
              <Box
                borderRadius={10}
                p={1.5}
                sx={{
                  position: 'relative',
                  overflow: 'hidden',
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    borderRadius: 'inherit',
                    padding: '1.3px',
                    WebkitMaskComposite: 'xor',
                    maskComposite: 'exclude',
                  },
                }}
              >
                <Avatar
                  sx={{
                    width: 100,
                    height: 100,
                  }}
                  alt={data.studentImage}
                  src={data.studentImage}
                />
              </Box>
            </Stack>
            <Stack direction="column" alignItems="center" gap={1}>
              <Typography variant="h6" fontSize={20}>
                {data.studentName}
              </Typography>
              <Stack direction="row" alignItems="center" gap={2}>
                <Chip size="small" label={data.className} color="success" />
                <Chip size="small" label="English" color="info" />
              </Stack>
              <Stack direction="row" alignItems="center" gap={2}>
                <Typography variant="subtitle1" fontSize={13}>
                  Pass-Mark&nbsp;(CE&nbsp;): {passMarkCE === '' ? 'N/A' : passMarkCE}
                </Typography>
                <Typography variant="subtitle1" fontSize={13}>
                  Out-of-Mark(CE): {outOfMarkCE === '' ? 'N/A' : outOfMarkCE}
                </Typography>
              </Stack>
              <Stack direction="row" alignItems="center" gap={2}>
                <Typography variant="subtitle1" fontSize={13}>
                  Pass-Mark(TE): {passMark === '' ? 'N/A' : passMark}
                </Typography>
                <Typography variant="subtitle1" fontSize={13}>
                  Out-of-Mark(TE): {outOfMark === '' ? 'N/A' : outOfMark}
                </Typography>
              </Stack>
              <Stack direction="row" alignItems="center" gap={2}>
                <Box display="flex" alignItems="center" gap={2}>
                  <Typography variant="subtitle2" fontSize={12}>
                    PT:
                  </Typography>
                  <TextField
                    sx={{ maxWidth: '150px' }}
                    inputRef={(el) => {
                      textFieldRefs.current[0] = el;
                    }}
                    onKeyPress={(event) => {
                      if (event.key === 'Enter') {
                        event.preventDefault();
                        textFieldRefs.current[1]?.focus();
                      }
                    }}
                    error={parseInt(data.periodicTest, 10) > parseInt(outOfMarkCE, 10) / 4}
                    helperText={parseInt(data.periodicTest, 10) > parseInt(outOfMarkCE, 10) / 4 ? 'Invalid' : ''}
                    value={data.periodicTest !== 'N' ? data.periodicTest : ''}
                    onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                      handleMarksChange(data.studentId, 'periodicTest', event.target.value);
                    }}
                  />
                </Box>
                <Box display="flex" alignItems="center" gap={2}>
                  <Typography variant="subtitle2" fontSize={12}>
                    MA:
                  </Typography>
                  <TextField
                    sx={{ maxWidth: '150px' }}
                    inputRef={(el) => {
                      textFieldRefs.current[1] = el;
                    }}
                    onKeyPress={(event) => {
                      if (event.key === 'Enter') {
                        event.preventDefault();
                        textFieldRefs.current[2]?.focus();
                      }
                    }}
                    error={parseInt(data.multipleAssesment, 10) > parseInt(outOfMarkCE, 10) / 4}
                    helperText={parseInt(data.multipleAssesment, 10) > parseInt(outOfMarkCE, 10) / 4 ? 'Invalid' : ''}
                    value={data.multipleAssesment !== 'N' ? data.multipleAssesment : ''}
                    onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                      handleMarksChange(data.studentId, 'multipleAssesment', event.target.value);
                    }}
                  />
                </Box>
                <Box display="flex" alignItems="center" gap={2}>
                  <Typography variant="subtitle2" fontSize={12}>
                    PO:
                  </Typography>
                  <TextField
                    sx={{ maxWidth: '150px' }}
                    inputRef={(el) => {
                      textFieldRefs.current[2] = el;
                    }}
                    onKeyPress={(event) => {
                      if (event.key === 'Enter') {
                        event.preventDefault();
                        textFieldRefs.current[3]?.focus();
                      }
                    }}
                    error={parseInt(data.portFolio, 10) > parseInt(outOfMarkCE, 10) / 4}
                    helperText={parseInt(data.portFolio, 10) > parseInt(outOfMarkCE, 10) / 4 ? 'Invalid' : ''}
                    value={data.portFolio !== 'N' ? data.portFolio : ''}
                    onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                      handleMarksChange(data.studentId, 'portFolio', event.target.value);
                    }}
                  />
                </Box>
                <Box display="flex" alignItems="center" gap={2}>
                  <Typography variant="subtitle2" fontSize={12}>
                    SE:
                  </Typography>
                  <TextField
                    sx={{ maxWidth: '150px' }}
                    inputRef={(el) => {
                      textFieldRefs.current[3] = el;
                    }}
                    onKeyPress={(event) => {
                      if (event.key === 'Enter') {
                        event.preventDefault();
                        textFieldRefs.current[4]?.focus();
                      }
                    }}
                    error={parseInt(data.subjectEnrichment, 10) > parseInt(outOfMarkCE, 10) / 4}
                    helperText={parseInt(data.subjectEnrichment, 10) > parseInt(outOfMarkCE, 10) / 4 ? 'Invalid' : ''}
                    value={data.subjectEnrichment !== 'N' ? data.subjectEnrichment : ''}
                    onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                      handleMarksChange(data.studentId, 'subjectEnrichment', event.target.value);
                    }}
                  />
                </Box>
              </Stack>
              <Box display="flex" alignItems="center" gap={2}>
                <Typography variant="subtitle2" fontSize={12}>
                  Enter Mark :
                </Typography>
                <TextField
                  sx={{ maxWidth: '150px' }}
                  inputRef={(el) => {
                    textFieldRefs.current[4] = el;
                  }}
                  onKeyPress={(event) => {
                    if (!hasError) {
                      handleKeyPress(event, 0);
                    }
                  }}
                  error={parseInt(data.scoredMarkTheory, 10) > parseInt(outOfMark, 10)}
                  helperText={parseInt(data.scoredMarkTheory, 10) > parseInt(outOfMark, 10) ? 'Invalid' : ''}
                  value={data.scoredMarkTheory !== 'N' ? data.scoredMarkTheory : ''}
                  onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                    handleMarksChange(data.studentId, 'scoredMarkTheory', event.target.value);
                  }}
                />
              </Box>
              <Stack direction="row" alignItems="center" gap={2}>
                {currentIndex !== 0 && (
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={() => {
                      handlePrev();
                      swiperRef.current?.slidePrev();
                      setTimeout(() => {
                        textFieldRefs.current[0]?.focus();
                      }, 100);
                    }}
                  >
                    Previous
                  </Button>
                )}
                <Button
                  variant="outlined"
                  color="primary"
                  onClick={() => {
                    if (!hasError) {
                      OnClose();
                      sendMarkRegisterData([data], 'multiple');
                    }
                  }}
                  disabled={hasError}
                >
                  Update
                </Button>
                {currentIndex !== dataCount && (
                  <Button
                    variant="contained"
                    color="primary"
                    className="slider-controler"
                    onClick={() => {
                      if (!hasError) {
                        handleNext();
                        swiperRef.current?.slideNext();
                        sendMarkRegisterData([data], 'single');
                        setTimeout(() => {
                          textFieldRefs.current[0]?.focus();
                        }, 100);
                      }
                    }}
                    disabled={hasError}
                  >
                    Update & Next
                  </Button>
                )}
              </Stack>
            </Stack>
          </Box>
        </SwiperSlide>
        <div className="slider-controler d-none d-sm-flex">
          <div>
            <KeyboardArrowLeftIcon
              onClick={() => {
                handlePrev();
                swiperRef.current?.slidePrev();
              }}
              className="swiper-button-prev slider-arrow border-0 card shadow rounded-circle"
              style={{ pointerEvents: 'auto', opacity: currentIndex === 0 ? 0.4 : 1 }}
            />
          </div>
          <div>
            <KeyboardArrowRightIcon
              onClick={() => {
                handleNext();
                swiperRef.current?.slideNext();
              }}
              className="swiper-button-next slider-arrow border-0 card shadow rounded-circle"
              style={{ pointerEvents: 'auto', opacity: currentIndex === dataCount ? 0.4 : 1 }}
            />
          </div>
        </div>
      </Swiper>
    </IndivitualEnrollCERoot>
  );
};

export default IndivitualEnrollCE;
