/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/alt-text */
import React, { FormEvent, useCallback, useEffect, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Autocomplete,
  Box,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  SnackbarOrigin,
  Divider,
  Collapse,
  Avatar,
  FormControl,
  MenuItem,
  Select,
  SelectChangeEvent,
  useTheme,Tooltip,
} from '@mui/material';
import styled from 'styled-components';
import SearchIcon from '@mui/icons-material/Search';
import VisibilityIcon from '@mui/icons-material/Visibility';
import { MONTH_SELECT, YEAR_SELECT } from '@/config/Selection';
import { MdAdd } from 'react-icons/md';
import DeleteIcon from '@mui/icons-material/Delete';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import { TeacherDataTypes } from '@/types/ManageStaff';
import Popup from '@/components/shared/Popup/Popup';
import { teacherData } from '@/config/TableData';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import PositionedSnackbar from '@/components/shared/Popup/SnackBar';
import deleteBin from '@/assets/ManageFee/deleteBin.json';
import deleteSuccess from '@/assets/ManageFee/deleteSuccess.json';
import Success from '@/assets/ManageFee/Success.json';
import errorIcon from '@/assets/ManageFee/Error.json';
import errorFailedIcon from '@/assets/ManageFee/ErrorIcon.json';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import { IoIosArrowUp } from 'react-icons/io';
import dayjs, { Dayjs } from 'dayjs';

import { useConfirm } from '@/components/shared/Popup/Confirmation';
import { StaffListInfo, StaffListRequest } from '@/types/StaffManagement';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import useAuth from '@/hooks/useAuth';
import { useAppSelector } from '@/hooks/useAppSelector';
import {
  getStaffDataList,
  getStaffDataListStatus,
  getStaffDeletingRecords,
  getStaffListPageInfo,
  getStaffSortColumn,
  getStaffSortDirection,
  getStaffSubmitting,
  getYearData,
  getYearStatus,
} from '@/config/storeSelectors';
import {
  addNewStaff,
  deleteStaff,
  fetchStaffDataList,
  updateStaff,
} from '@/store/StaffMangement/StaffMangement/staffMangement.thunks';
import { setSortColumn, setSortDirection } from '@/store/StaffMangement/StaffMangement/staffMangement.slice';
import { fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import { ErrorMessage } from '@/components/shared/Popup/ErrorMessage';
import useSettings from '@/hooks/useSettings';
import AddStaffMultiple from '../StaffManagement/ManageStaffs/AddStaffMultiple';
import StaffAddIndividual from '../StaffManagement/ManageStaffs/StaffAddIndividual';
import ViewStaffForm from '../StaffManagement/ManageStaffs/ViewStaff';
import MapStaff from '../StaffManagement/ManageStaffs/MapStaff';
import StaffPickerField from '@/components/shared/Selections/StaffPicker';
import DatePickers from '@/components/shared/Selections/DatePicker';

export interface State extends SnackbarOrigin {
  open: boolean;
}

const StaffWorkingDayListRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  padding: 1rem;
  @media screen and (max-width: 576px) {
    padding: 0.5rem;
  }
  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 50px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

const DefaultStaffInfo: StaffListInfo = {
  staffID: 0,
  academicId: 0,
  staffCode: '',
  staffJoinDate: '',
  staffName: '',
  staffGender: -1,
  staffDOB: '',
  staffBloodGroup: '',
  staffBirthPlace: '',
  staffNationality: '',
  staffMotherTongue: '',
  staffReligion: '',
  staffCaste: '',
  staffCastType: '',
  staffFatherName: '',
  staffMotherName: '',
  staffFatherOccupation: '',
  staffJobExperience: '',
  staffJobRole: '',
  staffJobDescription: '',
  staffPhoneNumber: '',
  staffPAddress: '',
  staffCAddress: '',
  staffEmailID: '',
  staffImage: '',
  staffStatus: '',
  staffCategory: 0,
  createdBy: 0,
};

function StaffWorkingDayList() {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const { confirm } = useConfirm();
  const dispatch = useAppDispatch();
  const { user } = useAuth();
  const adminId: number = user ? user.accountId : 0;
  const YearData = useAppSelector(getYearData);
  const YearStatus = useAppSelector(getYearStatus);
  const defualtYear = YearData[0]?.accademicId || '0';
  const [selectedStaffDetail, setSelectedStaffDetail] = useState<StaffListInfo>(DefaultStaffInfo);
  const [academicYearFilter, setAcademicYearFilter] = useState(defualtYear);
  const [staffNameFilter, setStaffNameFilter] = useState('');
  const [staffCodeFilter, setStaffCodeFilter] = useState('');
  const [staffMobileNumberFilter, setStaffMobileNumberFilter] = useState('');

  const [Delete, setDelete] = React.useState(false);
  const [addWorkingDay, setAddWorkingDay] = React.useState(false);
  const [staffView, setStaffView] = React.useState(false); // New state variable
  const [showFilter, setShowFilter] = useState(true);
  const [monthFilter, setMonthFilter] = useState(12);

  const handleClickDelete = () => setDelete(true);
  const handleClickCloseDelete = () => setDelete(false);

  const [staff, setStaffs] = React.useState(teacherData);
  const [createorEditOpen, setCreateorEditOpen] = React.useState(false);
  const [selectedStaff, setSelectedStaff] = React.useState(null);
  const [mapStaffOpen, setMapStaffOpen] = React.useState(false);
  const [showAddStaff, setShowAddStaff] = React.useState<'individual' | 'multiple' | 'manageStaff'>('manageStaff');

  const StaffDataListStatus = useAppSelector(getStaffDataListStatus);
  const StaffListData = useAppSelector(getStaffDataList);
  // const StaffListError = useAppSelector(getStaffListError);
  const paginationInfo = useAppSelector(getStaffListPageInfo);
  const sortColumn = useAppSelector(getStaffSortColumn);
  const sortDirection = useAppSelector(getStaffSortDirection);
  const isSubmitting = useAppSelector(getStaffSubmitting);
  const deletingRecords = useAppSelector(getStaffDeletingRecords);

  const { pagenumber, pagesize, totalrecords } = paginationInfo;

  const currentStaffListRequest = useMemo(
    () => ({
      pageNumber: pagenumber,
      pageSize: pagesize,
      sortColumn,
      sortDirection,
      filters: {
        adminId,
        academicId: academicYearFilter,
        staffCode: staffCodeFilter,
        staffName: staffNameFilter,
        staffPhoneNumber: staffMobileNumberFilter,
      },
    }),
    [
      adminId,
      academicYearFilter,
      staffCodeFilter,
      staffNameFilter,
      staffMobileNumberFilter,
      pagenumber,
      pagesize,
      sortColumn,
      sortDirection,
    ]
  );

  const loadStaffList = useCallback(
    (request: StaffListRequest) => {
      dispatch(fetchStaffDataList(request));
    },
    [dispatch]
  );

  useEffect(() => {
    if (YearStatus === 'idle') {
      setAcademicYearFilter(defualtYear);
    }
    if (StaffDataListStatus === 'idle') {
      dispatch(fetchYearList(adminId));
      loadStaffList(currentStaffListRequest);
    }
    // console.log('datass::', StaffListData);
  }, [loadStaffList, StaffDataListStatus, defualtYear, YearStatus, currentStaffListRequest, dispatch, adminId]);

  const handleYearChange = (e: SelectChangeEvent) => {
    const selectedAcademicId = e.target.value;
    setAcademicYearFilter(parseInt(selectedAcademicId, 10));
    loadStaffList({
      ...currentStaffListRequest,
      filters: {
        ...currentStaffListRequest.filters,
        academicId: selectedAcademicId,
      },
    });
  };

  const handleMonthChange = (e: SelectChangeEvent) => {
    const selectedMonth = parseInt(e.target.value, 10);
    if (selectedMonth) {
      setMonthFilter(selectedMonth);
    }
    // loadAttendanceCalendar({
    //   ...currentAttendanceCalendarRequest,
    //   month: selectedMonth || 0,
    // });
  };

  const handlePageChange = useCallback(
    (event: unknown, newPage: number) => {
      loadStaffList({ ...currentStaffListRequest, pageNumber: newPage + 1 });
    },
    [currentStaffListRequest, loadStaffList]
  );

  const handleChangeRowsPerPage = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const newPageSize = +event.target.value;
      loadStaffList({ ...currentStaffListRequest, pageNumber: 1, pageSize: newPageSize });
    },
    [currentStaffListRequest, loadStaffList]
  );

  const handleSort = useCallback(
    (column: string) => {
      const newReq = { ...currentStaffListRequest };
      if (column === sortColumn) {
        console.log('Toggling direction');
        const sortDirectionNew = sortDirection === 'asc' ? 'desc' : 'asc';
        newReq.pageNumber = 1;
        newReq.sortDirection = sortDirectionNew;
        dispatch(setSortDirection(sortDirectionNew));
      } else {
        newReq.pageNumber = 1;
        newReq.sortColumn = column;
        dispatch(setSortColumn(column));
      }

      loadStaffList(newReq);
    },
    [currentStaffListRequest, dispatch, loadStaffList, sortColumn, sortDirection]
  );

  const pageProps = useMemo(
    () => ({
      rowsPerPageOptions: [10, 20, 30, 50, 100, 200, 300, 500],
      pageNumber: pagenumber - 1,
      pageSize: pagesize,
      totalRecords: totalrecords,
      onPageChange: handlePageChange,
      onRowsPerPageChange: handleChangeRowsPerPage,
    }),
    [handleChangeRowsPerPage, handlePageChange, pagenumber, pagesize, totalrecords]
  );

  const handleReset = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      setAcademicYearFilter(0);
      setStaffNameFilter('');
      setStaffCodeFilter('');
      setStaffMobileNumberFilter('');
      loadStaffList({
        ...currentStaffListRequest,
        pageNumber: 1,
        pageSize: 20,
        sortColumn: 'staffID',
        sortDirection: 'desc',
        filters: {
          adminId,
          academicId: 0,
          staffCode: '',
          staffName: '',
          staffPhoneNumber: '',
        },
      });
    },
    [currentStaffListRequest, loadStaffList, adminId]
  );

  const handleEditSubject = useCallback((SubjectObj: StaffListInfo) => {
    setSelectedStaffDetail(SubjectObj);
    setCreateorEditOpen(true);
  }, []);

  const handleSaveorEdit = useCallback(
    async (value: StaffListInfo, mode: 'create' | 'edit') => {
      try {
        if (mode === 'create') {
          const { staffID, ...rest } = value;
          const response = await dispatch(addNewStaff(rest)).unwrap();

          if (response.id > 0) {
            const successMessage = (
              <SuccessMessage loop={false} jsonIcon={Success} message="Staff created successfully" />
            );
            await confirm(successMessage, 'Staff Created', { okLabel: 'Ok', showOnlyOk: true });
            setShowAddStaff('manageStaff');
            loadStaffList({
              ...currentStaffListRequest,
              pageNumber: 1,
              sortColumn: 'staffID',
              sortDirection: 'desc',
            });
          } else {
            const errorMessage = (
              <ErrorMessage loop={false} jsonIcon={errorFailedIcon} message="Staff created failed" />
            );
            await confirm(errorMessage, 'Staff Create', { okLabel: 'Ok', showOnlyOk: true });
          }
        } else {
          const { staffDOB, staffJoinDate } = value;

          const isDateFormatted = (date: string) => {
            const dateRegex = /^\d{2}\/\d{2}\/\d{4}$/;
            return dateRegex.test(date);
          };

          // Function to format dates
          const formatDate = (date: Dayjs | string | number | null): string | null => {
            if (date === null) return null;
            if (dayjs.isDayjs(date)) return date.format('DD/MM/YYYY');
            if (typeof date === 'string' && isDateFormatted(date)) return date; // Already formatted
            if (typeof date === 'string' || typeof date === 'number') return dayjs(date).format('DD/MM/YYYY');
            return null; // Fallback for any unexpected types
          };

          // Convert startDate and endDate to DD/MM/YYYY format
          const formattedStaffDOB = formatDate(staffDOB);
          const formattedStaffJoinDate = formatDate(staffJoinDate);
          const updateReq = {
            ...value,
            staffDOB: formattedStaffDOB,
            staffJoinDate: formattedStaffJoinDate,
            dbResult: '',
          };
          const response = await dispatch(updateStaff(updateReq)).unwrap();
          if (response) {
            loadStaffList({
              ...currentStaffListRequest,
            });
            setCreateorEditOpen(false);
            const successMessage = (
              <SuccessMessage loop={false} jsonIcon={Success} message="Staff updated successfully" />
            );
            await confirm(successMessage, 'Staff Updated', { okLabel: 'Ok', showOnlyOk: true });
          } else {
            const errorMessage = (
              <ErrorMessage loop={false} jsonIcon={errorFailedIcon} message="Staff created failed" />
            );
            await confirm(errorMessage, 'Staff Create', { okLabel: 'Ok', showOnlyOk: true });
          }
        }
      } catch {
        const errorMessage = <ErrorMessage loop={false} jsonIcon={errorFailedIcon} message="Staff created failed" />;
        await confirm(errorMessage, 'Staff Create', { okLabel: 'Ok', showOnlyOk: true });
      }
    },
    [confirm, currentStaffListRequest, dispatch, loadStaffList]
  );

  const currentItemCount = StaffListData.length;

  const handleDeleteStaff = useCallback(
    async (staffObj: StaffListInfo) => {
      try {
        const { staffID, staffName } = staffObj;

        const deleteConfirmMessage = (
          <DeleteMessage
            jsonIcon={deleteBin}
            message={
              <div>
                Are you sure you want to delete the Staff <br />
                <span style={{ color: theme.palette.error.main }}>&quot;{staffName}&quot;</span> ?
              </div>
            }
          />
        );
        if (await confirm(deleteConfirmMessage, 'Delete Staff?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
          const deleteResponse = await dispatch(deleteStaff(staffID)).unwrap();
          if (deleteResponse.deleted) {
            loadStaffList(currentStaffListRequest);
            const deleteDoneMessage = (
              <DeleteMessage loop={false} jsonIcon={deleteSuccess} message="Staff deleted successfully." />
            );
            await confirm(deleteDoneMessage, 'Deleted', { okLabel: 'Ok', showOnlyOk: true });
            let pageNumberToMove = pagenumber;
            if (paginationInfo.remainingpages === 0 && pagenumber > 1 && currentItemCount === 1) {
              pageNumberToMove = pagenumber - 1;
              loadStaffList({ ...currentStaffListRequest, pageNumber: pageNumberToMove });
            } else {
              // loadStaffList(currentStaffListRequest);
            }
          } else {
            const errorMessage = (
              <ErrorMessage loop={false} jsonIcon={errorFailedIcon} message="Staff deleted failed" />
            );
            await confirm(errorMessage, 'Staff Delete', { okLabel: 'Ok', showOnlyOk: true });
          }
        }
      } catch {
        const errorMessage = (
          <ErrorMessage loop={false} jsonIcon={errorFailedIcon} message="Something went wrong please try again." />
        );
        await confirm(errorMessage, 'Staff Delete', { okLabel: 'Ok', showOnlyOk: true });
      }
    },
    [
      confirm,
      currentStaffListRequest,
      currentItemCount,
      dispatch,
      loadStaffList,
      pagenumber,
      paginationInfo.remainingpages,
      theme,
    ]
  );

  const getRowKey = useCallback((row: StaffListInfo) => row.staffID, []);

  const handleCreateStaff = () => {
    setCreateorEditOpen(true);
  };

  const handleClose = () => {
    setCreateorEditOpen(false);
  };

  const handleUpdate = (updatedData: any, id: number) => {
    const updatedStaffs = staff.map((staffs) => (staffs.id === id ? { ...staffs, ...updatedData } : staffs));
    setStaffs(updatedStaffs);
    console.log(updatedStaffs);
    setCreateorEditOpen(false);
  };

  const handleCreate = (newStaff: any) => {
    const updatedStaffs = [...staff, newStaff];
    setStaffs(updatedStaffs);
    console.log(updatedStaffs);
    // setCreateOpen(false);
  };

  const [openSnackBar, setOpenSnackBar] = React.useState<boolean>(false);

  const handleOpenSnackBar = () => setOpenSnackBar(true);

  const handleCloseSnackBar = () => {
    setOpenSnackBar(false);
  };

  const ManageStaffColumns: DataTableColumn<StaffListInfo>[] = useMemo(
    () => [
      {
        name: 'staffID',
        dataKey: 'staffID',
        headerLabel: 'Staff Id',
        sortable: true,
      },
      {
        name: 'staffName',
        headerLabel: 'Staff Name',
        renderCell: (row) => {
          return (
            <Stack direction="row" gap={1} alignItems="center">
              <Avatar alt="" src={row.staffImage} />
              <Typography variant="subtitle2">{row.staffName}</Typography>
            </Stack>
          );
        },
      },
      {
        name: 'accademicYear',
        dataKey: 'staffCode',
        headerLabel: 'Accademic Year',
      },
      {
        name: 'month',
        dataKey: 'month',
        headerLabel: 'Month',
      },
      {
        name: 'date',
        dataKey: 'date',
        headerLabel: 'Date',
      },
      {
        name: 'actions',
        headerLabel: 'Actions',
        renderCell: (row) => {
          return (
            <Stack direction="row" gap={1}>
              {/* <IconButton size="small" color="secondary" sx={{ padding: 0.5 }} onClick={() => setStaffView(true)}>
                <VisibilityIcon />
              </IconButton>
              <IconButton size="small" sx={{ padding: 0.5 }} onClick={() => handleEditSubject(row)}>
                <ModeEditIcon />
              </IconButton> */}
              <IconButton size="small" color="error" sx={{ padding: 0.5 }} onClick={() => handleDeleteStaff(row)}>
                <DeleteIcon />
              </IconButton>
            </Stack>
          );
        },
      },
    ],
    [handleDeleteStaff, handleEditSubject]
  );

  return showAddStaff === 'multiple' ? (
    <AddStaffMultiple staffDetails={DefaultStaffInfo} onBackClick={() => setShowAddStaff('manageStaff')} />
  ) : showAddStaff === 'individual' ? (
    <StaffAddIndividual
      staffDetails={DefaultStaffInfo}
      onSave={handleSaveorEdit}
      onBackClick={() => setShowAddStaff('manageStaff')}
    />
  ) : (
    <Page title="Manage Staffs">
      <StaffWorkingDayListRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack
            direction="row"
            justifyContent="space-between"
            pb={1}
            alignItems="center"
            flexWrap="wrap"
            sx={{ '@media (max-width: 500px)': { flexDirection: 'column', alignItems: 'start' } }}
          >
            <Stack
              direction="row"
              alignItems="center"
              justifyContent="space-between"
              flex={3}
              width="100%"
              whiteSpace="nowrap"
            >
              <Typography variant="h6" fontSize={17}>
                Staff Working Day List
              </Typography>
              {showFilter === false ? (
                <Tooltip title="Search">
                  <IconButton
                    aria-label="delete"
                    color="primary"
                    sx={{ mr: 1 }}
                    onClick={() => setShowFilter((x) => !x)}
                  >
                    <SearchIcon />
                  </IconButton>
                </Tooltip>
              ) : (
                <IconButton aria-label="delete" color="primary" sx={{ mr: 1 }} onClick={() => setShowFilter((x) => !x)}>
                  <IoIosArrowUp />
                </IconButton>
              )}
            </Stack>
            <Box
              display="flex"
              columnGap={2}
              justifyContent="end"
              alignItems="center"
              sx={{
                flexShrink: 0,
                // flex: 1,
                '@media (max-width: 500px)': {
                  flexWrap: 'wrap',
                  rowGap: 1,
                },
              }}
            >
              <Button
                sx={{
                  borderRadius: '20px',
                  whiteSpace: 'nowrap',
                  '@media (max-width: 500px)': {
                    width: '100%',
                  },
                }}
                variant="outlined"
                size="small"
                startIcon={<MdAdd />}
                onClick={() => setAddWorkingDay(true)}
              >
                Add Day
              </Button>
            </Box>
          </Stack>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate onReset={handleReset}>
                <Grid pb={{ xs: 0, lg: 4 }} pt={2} container columnSpacing={3}>
                  <Grid item xxl={2} xl={2.5} lg={2.5} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Select
                        labelId="academicYearFilter"
                        id="academicYearFilterSelect"
                        value={academicYearFilter?.toString()}
                        // defaultValue={YearData[0]?.accademicId}
                        onChange={handleYearChange}
                        placeholder="Select Year"
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Year
                        </MenuItem>
                        {YearData.map((opt) => (
                          <MenuItem key={opt.accademicId} value={opt.accademicId}>
                            {opt.accademicTime}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2} lg={2.5} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Select Staff
                      </Typography>
                      <StaffPickerField width="100%" />
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={2.5} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Select Month
                      </Typography>
                      <Select
                        labelId="academicYearFilter"
                        id="academicYearFilterSelect"
                        value={monthFilter?.toString()}
                        // defaultValue={YearData[0]?.accademicId}
                        onChange={handleMonthChange}
                        placeholder="Select Month"
                      >
                        {MONTH_SELECT.map((opt) => (
                          <MenuItem key={opt.id} value={opt.id}>
                            {opt.month}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={2.5} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Mobile Number
                      </Typography>
                      <DatePickers />
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={1}>
                    <Stack
                      spacing={2}
                      direction="row"
                      justifyContent={{ xs: 'end', sm: 'start' }}
                      sx={{ pt: { xs: 2, sm: 3.79 } }}
                    >
                      <Button type="reset" variant="contained" color="secondary">
                        Reset
                      </Button>
                      {/* <Button variant="contained" color="primary" fullWidth>
                        Search
                      </Button> */}
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>
            <Paper className="card-table-container" sx={{ marginTop: '12px' }}>
              <DataTable
                columns={ManageStaffColumns}
                data={StaffListData}
                getRowKey={getRowKey}
                fetchStatus={StaffDataListStatus}
                allowPagination
                allowSorting
                PaginationProps={pageProps}
                deletingRecords={deletingRecords}
                sortColumn={sortColumn}
                sortDirection={sortDirection}
                onSort={handleSort}
              />
            </Paper>
          </div>
        </Card>
      </StaffWorkingDayListRoot>
      <Popup
        size="xs"
        state={addWorkingDay}
        onClose={() => setAddWorkingDay(false)}
        popupContent={
          <Box px={4}>
            <Typography variant="subtitle2" fontSize={18}>
              Add Working Day
            </Typography>
            <form noValidate onReset={handleReset}>
              <Grid pb={{ xs: 0, lg: 4 }} pt={2} container columnSpacing={3}>
                <Grid item xs={6}>
                  <FormControl fullWidth>
                    <Typography variant="subtitle1" fontSize={12} color="GrayText">
                      Academic Year
                    </Typography>
                    <Select
                      labelId="academicYearFilter"
                      id="academicYearFilterSelect"
                      value={academicYearFilter?.toString()}
                      // defaultValue={YearData[0]?.accademicId}
                      onChange={handleYearChange}
                      placeholder="Select Year"
                    >
                      <MenuItem value={0} sx={{ display: 'none' }}>
                        Select Year
                      </MenuItem>
                      {YearData.map((opt) => (
                        <MenuItem key={opt.accademicId} value={opt.accademicId}>
                          {opt.accademicTime}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={6}>
                  <FormControl fullWidth>
                    <Typography variant="subtitle1" fontSize={12} color="GrayText">
                      Select Date
                    </Typography>
                    <DatePickers />
                  </FormControl>
                </Grid>
                <Grid item xxl={12} xl={2} lg={2.5} md={4} sm={4} xs={12}>
                  <FormControl fullWidth>
                    <Typography variant="subtitle1" fontSize={12} color="GrayText">
                      Select Staff
                    </Typography>
                    <StaffPickerField width="100%" />
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={12}>
                  <Stack
                    spacing={2}
                    direction="row"
                    justifyContent={{ xs: 'center', sm: 'center' }}
                    sx={{ pt: { xs: 2, sm: 3.79 } }}
                  >
                    <Button sx={{ width: 100 }} type="reset" variant="outlined" color="secondary">
                      Cancel
                    </Button>
                    <Button sx={{ width: 100 }} type="reset" variant="contained" color="primary">
                      Save
                    </Button>
                  </Stack>
                </Grid>
              </Grid>
            </form>
          </Box>
        }
      />
      <Popup
        size="xl"
        disabled={isSubmitting}
        state={createorEditOpen}
        onClose={handleClose}
        title={selectedStaffDetail.staffID === 0 ? 'Create Staff' : 'Edit Staff Information'}
        popupContent={
          <StaffAddIndividual
            onSave={handleSaveorEdit}
            staffDetails={selectedStaffDetail}
            onBackClick={() => setShowAddStaff('individual')}
          />
        }
      />
      <Popup
        size="xs"
        title="View Staff Setting"
        state={staffView}
        onClose={() => setStaffView(false)}
        popupContent={<ViewStaffForm onSave={handleCreate} onCancel={() => setStaffView(false)} />}
      />
      <Popup
        size="lg"
        title="Map Staff"
        state={mapStaffOpen}
        onClose={() => setMapStaffOpen(false)}
        popupContent={<MapStaff onCancel={() => setMapStaffOpen(false)} />}
      />
    </Page>
  );
}

export default StaffWorkingDayList;
