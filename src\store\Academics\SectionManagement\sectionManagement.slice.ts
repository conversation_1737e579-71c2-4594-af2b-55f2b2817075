import { PayloadAction, createSlice } from '@reduxjs/toolkit';
import { flushStore } from '@/store/flush.slice';
import { OutletContextType } from '@/types/Common';
import type { SectionManagementState } from '@/types/AcademicManagement';
import {
  fetchSectionList,
  addNewSection,
  updateSection,
  deleteSection,
  // fetchSectionLists,
} from './sectionManagement.thunks';

const initialState: SectionManagementState = {
  sectionList: {
    status: 'idle',
    data: [],
    error: null,
    pageInfo: {
      totalrecords: 0,
      pagesize: 20,
      pagenumber: 1,
      totalpages: 0,
      remainingpages: 0,
    },
    sortColumn: 'sectionId',
    sortDirection: 'asc',
  },
  submitting: false,
  deletingRecords: {},
  error: null,
};

const sectionManagementSlice = createSlice({
  name: 'sectionManagement',
  initialState,
  reducers: {
    setSectionListPage: (state, action: PayloadAction<number>) => {
      state.sectionList.pageInfo.pagenumber = action.payload;
    },
    setSectionListPageSize: (state, action: PayloadAction<number>) => {
      state.sectionList.pageInfo.pagenumber = 1;
      state.sectionList.pageInfo.pagesize = action.payload;
    },
    setSortColumn: (state, action: PayloadAction<string>) => {
      state.sectionList.pageInfo.pagenumber = 1;
      state.sectionList.sortColumn = action.payload;
      state.sectionList.sortDirection = 'asc';
    },
    setSortDirection: (state, action: PayloadAction<'asc' | 'desc'>) => {
      state.sectionList.pageInfo.pagenumber = 1;
      state.sectionList.sortDirection = action.payload;
    },
    setSortColumnAndDirection: (state, action: PayloadAction<{ column: string; direction: 'asc' | 'desc' }>) => {
      state.sectionList.pageInfo.pagenumber = 1;
      state.sectionList.sortColumn = action.payload.column;
      state.sectionList.sortDirection = action.payload.direction;
    },
    setSubmitting: (state, action: PayloadAction<boolean>) => {
      state.submitting = action.payload;
    },
    setDeletingRecords: (state, action: PayloadAction<{ recordId: number }>) => {
      state.deletingRecords[action.payload.recordId] = true;
    },
    clearDeletingRecords: (state, action: PayloadAction<{ recordId: number }>) => {
      delete state.deletingRecords[action.payload.recordId];
    },
  },
  extraReducers: (builder) => {
    builder
      //   .addCase(fetchSectionLists.pending, (state) => {
      //     state.sectionList.status = 'loading';
      //     state.sectionList.error = null;
      //   })
      //   .addCase(fetchSectionLists.fulfilled, (state, action) => {
      //     const data = action.payload;
      //     state.sectionList.status = 'success';
      //     state.sectionList.data = data;
      //     state.sectionList.error = null;
      //     // state.sectionList.pageInfo = pageInfo;
      //   })
      //   .addCase(fetchSectionLists.rejected, (state, action) => {
      //     state.sectionList.status = 'error';
      //     state.sectionList.error = action.payload || 'Unknown error in fetching section list';
      //   })

      .addCase(fetchSectionList.pending, (state) => {
        state.sectionList.status = 'loading';
        state.sectionList.error = null;
      })
      .addCase(fetchSectionList.fulfilled, (state, action) => {
        const { data, ...pageInfo } = action.payload;
        state.sectionList.status = 'success';
        state.sectionList.data = data;
        state.sectionList.error = null;
        state.sectionList.pageInfo = pageInfo;
      })
      .addCase(fetchSectionList.rejected, (state, action) => {
        state.sectionList.status = 'error';
        state.sectionList.error = action.payload || 'Unknown error in fetching section list';
      })

      .addCase(addNewSection.pending, (state) => {
        state.submitting = true;
        state.error = null;
      })
      .addCase(addNewSection.fulfilled, (state) => {
        state.submitting = false;
        state.error = null;
      })
      .addCase(addNewSection.rejected, (state, action) => {
        state.submitting = false;
        state.error = action.error.message;
      })

      .addCase(updateSection.pending, (state) => {
        state.submitting = true;
        state.error = null;
      })
      .addCase(updateSection.fulfilled, (state, action) => {
        state.submitting = false;
        state.error = null;
        const dataIndex = state.sectionList.data.findIndex((x) => x.sectionId === action.meta.arg.sectionId);
        if (dataIndex > -1) {
          state.sectionList.data[dataIndex] = action.meta.arg;
        }
      })
      .addCase(updateSection.rejected, (state, action) => {
        state.submitting = false;
        state.error = action.error.message;
      })

      .addCase(deleteSection.pending, (state, action) => {
        state.deletingRecords[action.meta.arg] = true;
        state.error = null;
      })
      .addCase(deleteSection.fulfilled, (state, action) => {
        if (action.payload.deleted) {
          const { [action.meta.arg]: deletedRecord, ...restDeletingRecords } = state.deletingRecords;
          state.deletingRecords = restDeletingRecords;
        }
        state.error = null;
      })
      .addCase(deleteSection.rejected, (state, action) => {
        state.deletingRecords = { ...initialState.deletingRecords };
        state.error = action.error.message;
      })
      .addCase(flushStore, () => initialState);
  },
});

export const {
  setSectionListPage,
  setSectionListPageSize,
  setSortColumn,
  setSortDirection,
  setSortColumnAndDirection,
  setSubmitting,
  setDeletingRecords,
  clearDeletingRecords,
} = sectionManagementSlice.actions;

export default sectionManagementSlice.reducer;
