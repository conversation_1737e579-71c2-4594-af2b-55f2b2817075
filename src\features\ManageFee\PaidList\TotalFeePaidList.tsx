/* eslint-disable prefer-template */
/* eslint-disable import/no-extraneous-dependencies */
/* eslint-disable new-cap */
/* eslint-disable jsx-a11y/alt-text */
import React, { FormEvent, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  Button,
  Typography,
  Card,
  Tooltip,
  IconButton,
  Collapse,
  FormControl,
  Select,
  SelectChangeEvent,
  MenuItem,
  useTheme,
  TableRow,
  TableCell,
} from '@mui/material';
import styled from 'styled-components';
import SearchIcon from '@mui/icons-material/Search';
import { IoIosArrowUp } from 'react-icons/io';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import useAuth from '@/hooks/useAuth';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import {
  getClassSectionsData,
  getFeePaidListData,
  getFeePaidListStatus,
  getManageFeeclassListData,
  getStudentFilterData,
  getYearData,
} from '@/config/storeSelectors';
import { useAppSelector } from '@/hooks/useAppSelector';
import { fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import {
  fetchClassList,
  fetchClassSections,
  fetchFeePaidList,
  fetchStudentsFilter,
} from '@/store/ManageFee/manageFee.thunks';
import PrintIcon from '@mui/icons-material/Print';
import CurrencyRupeeIcon from '@mui/icons-material/CurrencyRupee';
import { GetFeePaidListDataType, GetFeePaidListType } from '@/types/ManageFee';
import DatePickers from '@/components/shared/Selections/DatePicker';
import dayjs from 'dayjs';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import { PayDrawer } from '@/components/shared/Popup/PayDrawer';
import useSettings from '@/hooks/useSettings';
import { useReactToPrint } from 'react-to-print';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import * as XLSX from 'xlsx';
import { FEE_TYPE_ID_OPTIONS } from '@/config/Selection';
import PictureAsPdfRoundedIcon from '@mui/icons-material/PictureAsPdfRounded';
import { ReceiptPDF } from '@/components/shared/ReceiptPDF';
import Popup from '@/components/shared/Popup/Popup';
import DTVirtuoso from '@/components/shared/RND/DataTableVirtuoso';

const TotalFeePaidListRoot = styled.div`
  padding: 1rem;
  @media screen and (max-width: 576px) {
    padding: 0.5rem;
  }
  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 200px);
    @media screen and (max-width: 576px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 50px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid ${(props) => props.theme.palette.grey[200]};
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
        .MuiTableCell-root:last-child {
          width: 300px;
        }
        .footer_row .MuiTableCell-root {
          font-size: 14px;
          font-weight: 600;
          color: ${(props) => props.theme.palette.common.black};
          border: 0px;
        }
        .receipt_btn {
          padding: 0px 5px 0px 5px;
          font-size: 13px;
        }
      }
    }
    @media screen and (min-width: 992px) {
      .MuiTableCell-head:nth-child(1) {
        padding-left: 4px;
        z-index: 11;
        position: sticky;
        left: 0;
        background-color: ${(props) =>
          props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.grey[800]};
      }
      .MuiTableCell-head:nth-child(2) {
        z-index: 11;
        position: sticky;
        left: 45px;
        background-color: ${(props) =>
          props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.grey[800]};
      }
      .MuiTableCell-head:nth-child(3) {
        z-index: 11;
        position: sticky;
        left: 255px;
        background-color: ${(props) =>
          props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.grey[800]};
      }
      .MuiTableCell-body:nth-child(1) {
        /* padding-left: 5px; */
        z-index: 11;
        position: sticky;
        left: 0;
        background-color: ${(props) =>
          props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.grey[800]};
      }
      .MuiTableCell-body:nth-child(2) {
        z-index: 11;
        position: sticky;
        left: 45px;
        background-color: ${(props) =>
          props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.grey[800]};
      }
      .MuiTableCell-body:nth-child(3) {
        z-index: 11;
        position: sticky;
        left: 255px;
        background-color: ${(props) =>
          props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.grey[800]};
      }
    }
  }
`;

export default function TotalFeePaidList() {
  const tableRef = useRef<HTMLDivElement>(null);
  const isLight = useSettings().themeMode === 'light';
  const theme = useTheme();
  const [showFilter, setShowFilter] = useState(true);
  const { user } = useAuth();
  const dispatch = useAppDispatch();
  const adminId: number = user ? user.accountId : 0;

  const YearData = useAppSelector(getYearData);
  const academicId = 10;
  const defaultYear = YearData[0]?.accademicId || 0;
   const [academicYearFilter, setAcademicYearFilter] = useState(defaultYear);
  const [feeTypeFilter, setFeeTypeFilter] = useState(0);
  const feePaidListData = useAppSelector(getFeePaidListData);
  const feePaidListStatus = useAppSelector(getFeePaidListStatus);
  const ClassSectionsData = useAppSelector(getClassSectionsData);
  const classListData = useAppSelector(getManageFeeclassListData);
  const studentFilterData = useAppSelector(getStudentFilterData);
  const [currentPage, setCurrentPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const [classSectionsFilter, setClassSectionsFilter] = useState(0);
  const [classFilter, setClassFilter] = useState(0);
  const [studentsFilter, setStudentsFilter] = useState(-1);
  const [fromDateFilter, setFromDateFilter] = useState('');
  const [toDateFilter, setToDateFilter] = useState('');
  const [totalAmount, setTotalAmount] = useState<number>();

  const [drawerOpen, setDrawerOpen] = React.useState<boolean>(false);
  const [receiptPopupOpen, setReceiptPopupOpen] = React.useState<boolean>(false);
  const [receiptId, setReceiptId] = React.useState<number>(0);
  const [receiptDrawer, setReceiptDrawer] = React.useState<'print' | 'unpay' | 'pdf' | ''>('');
  const toggleDrawerOpen = (id: number, receipt: 'print' | 'unpay' | 'pdf' | '') => {
    setReceiptId(id);
    setDrawerOpen(true);
    setReceiptDrawer(receipt);
  };

  const toggleDrawerClose = () => setDrawerOpen(false);

  const popupReceiptOpen = useCallback(
    (id: number, receipt: 'print' | 'unpay' | 'pdf' | '') => {
      setReceiptId(id);
      // setReceiptDrawer(receipt);
      setReceiptPopupOpen(true);
    },
    [setReceiptId, setReceiptPopupOpen] // dependencies
  );

  const currentFeePaidListRequest = useMemo(
    () => ({
      adminId,
      academicId: academicYearFilter,
      feeTypeId: feeTypeFilter,
      sectionId: classSectionsFilter,
      classId: classFilter,
      studentId: studentsFilter,
      fromDate: fromDateFilter,
      toDate: toDateFilter,
    }),
    [
      adminId,
      academicYearFilter,
      feeTypeFilter,
      classFilter,
      classSectionsFilter,
      fromDateFilter,
      toDateFilter,
      studentsFilter,
    ]
  );
  const loadFeePaidList = useCallback(
    async (request: GetFeePaidListType) => {
      try {
        const response = await dispatch(fetchFeePaidList(request)).unwrap();

        if (response) {
          const TotalAmnt = response.map((m) => m.totalPaid)[0];
          setTotalAmount(TotalAmnt);
          // Handle the response if needed
          console.log('response::::----', response);
        }
      } catch (error) {
        // Log the error or handle it appropriately
        console.error('Failed to load term fee list:', error);
      }
    },
    [dispatch]
  );
  useEffect(() => {
    dispatch(fetchYearList(adminId));
    dispatch(fetchClassSections({ adminId, academicId }));
    dispatch(fetchClassList({ adminId, academicId, sectionId: classSectionsFilter }));
    dispatch(
      fetchStudentsFilter({
        adminId,
        academicId,
        sectionId: classSectionsFilter,
        classId: classFilter,
        feeTypeId: feeTypeFilter,
      })
    );
    if (feePaidListStatus === 'idle') {
      loadFeePaidList(currentFeePaidListRequest);
    }
    console.log('studentsFilter::::----', studentsFilter);
  }, [
    feePaidListStatus,
    adminId,
    dispatch,
    classSectionsFilter,
    studentsFilter,
    loadFeePaidList,
    classFilter,
    currentFeePaidListRequest,
    feeTypeFilter,
  ]);

  const handleYearChange = (e: SelectChangeEvent) => {
    setAcademicYearFilter(parseInt(YearData.filter((item) => item.accademicId === e.target.value)[0].accademicId, 10));
    loadFeePaidList({ ...currentFeePaidListRequest, academicId: parseInt(e.target.value, 10) });
    setClassSectionsFilter(0);
    setClassFilter(0);
    setStudentsFilter(-1);
    setFeeTypeFilter(0);
  };

  const handleFeeTypeChange = (e: SelectChangeEvent) => {
    const feeTypeId = parseInt(e.target.value, 10);
    setFeeTypeFilter(feeTypeId);
    loadFeePaidList({ ...currentFeePaidListRequest, feeTypeId: parseInt(e.target.value, 10) });
  };

  const handleClassSectionChange = (e: SelectChangeEvent) => {
    setClassSectionsFilter(
      parseInt(ClassSectionsData.filter((item) => item.sectionId === e.target.value)[0].sectionId, 10)
    );
    loadFeePaidList({ ...currentFeePaidListRequest, sectionId: parseInt(e.target.value, 10) });
    setClassFilter(0);
    setStudentsFilter(-1);
  };

  const handleClassChange = (e: SelectChangeEvent) => {
    setClassFilter(parseInt(classListData.filter((item) => item.classId === e.target.value)[0].classId, 10));
    loadFeePaidList({ ...currentFeePaidListRequest, classId: parseInt(e.target.value, 10) });
    setStudentsFilter(-1);
  };

  const handleStudentChange = (e: SelectChangeEvent) => {
    setStudentsFilter(parseInt(studentFilterData.filter((item) => item.studentId === e.target.value)[0].studentId, 10));
    loadFeePaidList({ ...currentFeePaidListRequest, studentId: parseInt(e.target.value, 10) });
  };

  const handleReset = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      setFromDateFilter('');
      setToDateFilter('');
      setClassSectionsFilter(0);
      setClassFilter(0);
      setAcademicYearFilter(0);
      setStudentsFilter(-1);
      setFeeTypeFilter(0);
      loadFeePaidList({
        adminId,
        academicId,
        feeTypeId: 0,
        sectionId: 0,
        classId: 0,
        studentId: -1,
        fromDate: '',
        toDate: '',
      });
    },
    [loadFeePaidList, adminId, academicId]
  );

  const feePaidListColumnsForExport: DataTableColumn<GetFeePaidListDataType>[] = useMemo(
    () => [
      {
        name: '',
        headerLabel: 'SL.No',
        renderCell: (_, index) => {
          return (
            <Typography fontSize={13} variant="subtitle1">
              {index + 1}
            </Typography>
          );
        },
      },
      {
        name: 'studentName',
        headerLabel: 'Student Name',
        renderCell: (row) => {
          return (
            <Typography fontSize={13} variant="subtitle1">
              {row.studentName}
            </Typography>
          );
        },
      },
      {
        name: 'admissionNo',
        headerLabel: 'Admission No',
        renderCell: (row) => {
          return (
            <Typography fontSize={13} variant="subtitle1">
              {row.admissionNo}
            </Typography>
          );
        },
      },
      {
        name: 'className',
        headerLabel: 'Class',
        renderCell: (row) => {
          return (
            <Typography fontSize={13} variant="subtitle1">
              {row.className}
            </Typography>
          );
        },
      },
      {
        name: 'paidDate',
        headerLabel: 'Paid Date',
        renderCell: (row) => {
          const formattedDate = new Date(row.paidDate).toLocaleString('en-GB', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
          });
          const [date, time] = formattedDate.split(' ');
          return (
            <Typography fontSize={13} variant="subtitle1">
              {date}&nbsp;
              {time}
            </Typography>
          );
        },
      },
      {
        name: 'paidAmount',
        headerLabel: 'Paid Amount',
        renderCell: (row) => {
          return (
            <Stack direction="row" alignItems="center">
              <CurrencyRupeeIcon sx={{ fontSize: '13px' }} />
              <Typography fontSize={13} variant="subtitle1">
                {row.paidAmount}
              </Typography>
            </Stack>
          );
        },
      },
      {
        name: 'paymentType',
        headerLabel: 'Payment Type',
        renderCell: (row) => {
          return (
            <Typography fontSize={13} variant="subtitle1">
              {row.paymentType}
            </Typography>
          );
        },
      },
      {
        name: 'chequeNo',
        headerLabel: 'Cheque Number',
        renderCell: (row) => {
          return (
            <Typography fontSize={13} variant="subtitle1">
              {row.chequeNo}
            </Typography>
          );
        },
      },
      {
        name: 'billdeskOrderNumber',
        headerLabel: 'Order No.',
        width: '150px',
        renderCell: (row) => {
          const splitOrderNumber = row.billdeskOrderNumber
            ? row.billdeskOrderNumber.slice(0, 16) + '\n' + row.billdeskOrderNumber.slice(16)
            : '';
          return (
            <Typography maxWidth={150} fontSize={13} variant="subtitle1">
              {splitOrderNumber}
            </Typography>
          );
        },
      },
      {
        name: 'billdeskTransactionId',
        headerLabel: 'Transaction Id',
        width: '150px',
        renderCell: (row) => {
          const splitTransactionId = row.billdeskTransactionId
            ? row.billdeskTransactionId.slice(0, 16) + '\n' + row.billdeskTransactionId.slice(16)
            : '';
          return (
            <Typography maxWidth={150} fontSize={13} variant="subtitle1">
              {splitTransactionId}
            </Typography>
          );
        },
      },
    ],
    []
  );

  // Pagination handlers
  const handlePageChange = useCallback(
    (event: any, newPage: any) => {
      setCurrentPage(newPage); // Update current page
    },
    [setCurrentPage]
  );

  const handleChangeRowsPerPage = useCallback(
    (event: any) => {
      setRowsPerPage(parseInt(event.target.value, 10)); // Update rows per page
      // Reset to first page when changing rows per page
    },
    [setRowsPerPage]
  );

  const paginatedData = feePaidListData.slice(currentPage * rowsPerPage, currentPage * rowsPerPage + rowsPerPage);

  const pageProps = useMemo(
    () => ({
      rowsPerPageOptions: [10, 20, 30, 50, 100, 200, 300, 500],
      pageNumber: currentPage,
      pageSize: rowsPerPage,
      totalRecords: feePaidListData.length,
      onPageChange: handlePageChange,
      onRowsPerPageChange: handleChangeRowsPerPage,
    }),
    [handleChangeRowsPerPage, handlePageChange, currentPage, rowsPerPage, feePaidListData]
  );
  const feePaidListColumns: DataTableColumn<GetFeePaidListDataType>[] = useMemo(
    () => [
      {
        name: '',
        headerLabel: 'SL.No',
        renderCell: (_, index) => {
          return (
            <Typography fontSize={13} variant="subtitle1">
              {index + 1}
            </Typography>
          );
        },
      },
      {
        name: 'studentName',
        headerLabel: 'Student Name',
        renderCell: (row) => {
          return (
            <Typography minWidth={200} fontSize={13} variant="subtitle1">
              {row.studentName}
            </Typography>
          );
        },
      },
      {
        name: 'admissionNo',
        headerLabel: 'Admission No',
        renderCell: (row) => {
          return (
            <Typography minWidth={100} fontSize={13} variant="subtitle1">
              {row.admissionNo}
            </Typography>
          );
        },
      },
      {
        name: 'className',
        headerLabel: 'Class',
        renderCell: (row) => {
          return (
            <Typography minWidth={150} fontSize={13} variant="subtitle1">
              {row.className}
            </Typography>
          );
        },
      },
      {
        name: 'paidDate',
        headerLabel: 'Paid Date',
        renderCell: (row) => {
          const formattedDate = dayjs(row.paidDate, 'YYYY-MM-DDTHH:mm:ss').format('DD/MM/YYYY, h:mm a');
          return (
            <Typography minWidth={200} fontSize={13} variant="subtitle1">
              {formattedDate}
            </Typography>
          );
        },
      },
      {
        name: 'paidAmount',
        headerLabel: 'Paid Amount',
        renderCell: (row) => {
          return (
            <Stack direction="row" alignItems="center">
              <CurrencyRupeeIcon sx={{ fontSize: '13px' }} />
              <Typography minWidth={100} fontSize={13} variant="subtitle1">
                {row.paidAmount}
              </Typography>
            </Stack>
          );
        },
      },
      {
        name: 'paymentType',
        headerLabel: 'Payment Type',
        renderCell: (row) => {
          return (
            <Typography minWidth={150} fontSize={13} variant="subtitle1">
              {row.paymentType}
            </Typography>
          );
        },
      },
      {
        name: 'chequeNo',
        headerLabel: 'Cheque Number',
        renderCell: (row) => {
          return (
            <Typography minWidth={150} sx={{ userSelect: 'all' }} fontSize={13} variant="subtitle1">
              {row.chequeNo}
            </Typography>
          );
        },
      },
      {
        name: 'billdeskOrderNumber',
        headerLabel: 'Order No.',
        renderCell: (row) => {
          const splitOrderNumber = row.billdeskOrderNumber
            ? row.billdeskOrderNumber.slice(0, 16) + '\n' + row.billdeskOrderNumber.slice(16)
            : '';
          return (
            <Typography sx={{ userSelect: 'all' }} maxWidth={150} fontSize={13} variant="subtitle1">
              {splitOrderNumber}
            </Typography>
          );
        },
      },
      {
        name: 'billdeskTransactionId',
        headerLabel: 'Transaction Id',
        renderCell: (row) => {
          const splitTransactionId = row.billdeskTransactionId
            ? row.billdeskTransactionId.slice(0, 16) + '\n' + row.billdeskTransactionId.slice(16)
            : '';
          return (
            <Typography sx={{ userSelect: 'all' }} maxWidth={150} fontSize={13} variant="subtitle1">
              {splitTransactionId}
            </Typography>
          );
        },
      },
      {
        name: 'actions',
        headerLabel: 'Actions',
        renderCell: (row) => {
          // const disabledButton =row.receiptId ===
          return (
            <Stack direction="row" gap={1}>
              {/* <Button className="receipt_btn" size="small" variant="outlined" color="primary">
                Receipt
              </Button>
              <Button className="receipt_btn" size="small" variant="outlined" color="primary">
                Con.Receipt
              </Button> */}
              <Button
                className="receipt_btn"
                size="small"
                color="success"
                startIcon={<PrintIcon />}
                variant="outlined"
                onClick={() => toggleDrawerOpen(row.receiptId, 'print')}
              >
                Print
              </Button>
              <Button
                // disabled={row.status === 'Cancelled'}
                onClick={() => toggleDrawerOpen(row.receiptId, 'unpay')}
                className="receipt_btn"
                size="small"
                variant="outlined"
                color="primary"
              >
                Unpay
              </Button>
              {/* <Link to="paid-receipt"> */}
              <Button
                size="small"
                color="info"
                startIcon={<PictureAsPdfRoundedIcon />}
                className="receipt_btn"
                // sx={{ height: 0.8, width: 0.5 }}
                variant="outlined"
                // onClick={() => toggleReceiptOpen(row.receiptId, 'pdf')}
                onClick={() => popupReceiptOpen(row.receiptId, 'pdf')}
              >
                PDF
              </Button>
              {/* </Link> */}
            </Stack>
          );
        },
      },
    ],
    [popupReceiptOpen]
  );

  const handlePrint = useReactToPrint({
    content: () => tableRef.current,
  });

  const exportPDF = () => {
    const input: any = tableRef.current;
    html2canvas(input).then((canvas) => {
      const imgData = canvas.toDataURL('image/png');
      const pdf = new jsPDF();

      // Calculate the dimensions of the PDF page
      const pdfWidth = pdf.internal.pageSize.getWidth();
      const pdfHeight = pdf.internal.pageSize.getHeight();

      // Calculate the aspect ratio of the canvas image
      const aspectRatio = canvas.width / canvas.height;
      let imgWidth = pdfWidth;
      let imgHeight = pdfWidth / aspectRatio;

      if (imgHeight > pdfHeight) {
        imgHeight = pdfHeight;
        imgWidth = pdfHeight * aspectRatio;
      }

      pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);
      pdf.save('table.pdf');
    });
  };

  const exportExcel = () => {
    const ws = XLSX.utils.json_to_sheet(feePaidListData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
    XLSX.writeFile(wb, 'table.xlsx');
  };
  const getRowKey = useCallback((row: GetFeePaidListDataType, index: number | undefined) => index, []);

  return (
    <Page title="Paid List">
      <TotalFeePaidListRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Box display="flex" pb={0.5} justifyContent="space-between">
            <Typography variant="h6" fontSize={17}>
              Total Fee Paid List
            </Typography>
            <Stack direction="row" alignItems="center">
              {showFilter === false ? (
                <Tooltip title="Search">
                  <IconButton
                    aria-label="delete"
                    color="primary"
                    sx={{ ml: 1 }}
                    onClick={() => setShowFilter((x) => !x)}
                  >
                    <SearchIcon />
                  </IconButton>
                </Tooltip>
              ) : (
                <IconButton aria-label="delete" color="primary" sx={{ ml: 1 }} onClick={() => setShowFilter((x) => !x)}>
                  <IoIosArrowUp />
                </IconButton>
              )}
            </Stack>
          </Box>

          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate onReset={handleReset}>
                <Grid pb={{ xxl: 8, lg: 12, sm: 32 }} pt={1} container columnSpacing={2} alignItems="end">
                  <Grid item xxl={2} xl={3} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Select
                        labelId="academicYearFilter"
                        id="academicYearFilterSelect"
                        value={academicYearFilter.toString()}
                        onChange={handleYearChange}
                        placeholder="Select Year"
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Year
                        </MenuItem>
                        {YearData.map((opt) => (
                          <MenuItem key={opt.accademicId} value={opt.accademicId}>
                            {opt.accademicTime}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={3} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Select Type
                      </Typography>
                      <Select
                        labelId="feeTypeFilter"
                        id="feeTypeFilterSelect"
                        value={feeTypeFilter.toString()}
                        onChange={handleFeeTypeChange}
                        placeholder="Select Year"
                      >
                        <MenuItem sx={{ display: 'none' }} value={0}>
                          Select Type
                        </MenuItem>
                        {FEE_TYPE_ID_OPTIONS.map((opt) => (
                          <MenuItem key={opt.id} value={opt.id}>
                            {opt.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={3} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Section
                      </Typography>
                      <Select
                        labelId="classSectionsFilter"
                        id="classSectionsFilterSelect"
                        value={classSectionsFilter?.toString()}
                        onChange={handleClassSectionChange}
                        placeholder="Select Section"
                        MenuProps={{
                          PaperProps: {
                            style: {
                              maxHeight: '270px', // Adjust the value to your desired height
                            },
                          },
                        }}
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Section
                        </MenuItem>
                        {ClassSectionsData.map((opt) => (
                          <MenuItem key={opt.sectionId} value={opt.sectionId}>
                            {opt.sectionName}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={3} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Select
                        disabled={classListData.length === 0}
                        labelId="classFilter"
                        id="classFilter"
                        value={classFilter?.toString()}
                        onChange={handleClassChange}
                        placeholder="Select Class"
                        sx={{
                          '& .MuiInputBase-input.Mui-disabled': {
                            backgroundColor: isLight ? theme.palette.grey[200] : theme.palette.grey[700],
                          },
                        }}
                        MenuProps={{
                          PaperProps: {
                            style: {
                              maxHeight: '270px', // Adjust the value to your desired height
                            },
                          },
                        }}
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Class
                        </MenuItem>
                        {classListData.map((opt) => (
                          <MenuItem key={opt.classId} value={opt.classId}>
                            {opt.className}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={3} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Student
                      </Typography>
                      <Select
                        disabled={studentFilterData.length === 0}
                        labelId="studentsFilter"
                        id="studentsFilter"
                        value={studentsFilter?.toString()}
                        onChange={handleStudentChange}
                        placeholder="Select Student"
                        sx={{
                          '& .MuiInputBase-input.Mui-disabled': {
                            backgroundColor: isLight ? theme.palette.grey[200] : theme.palette.grey[700],
                          },
                        }}
                        MenuProps={{
                          PaperProps: {
                            style: {
                              maxHeight: '270px', // Adjust the value to your desired height
                            },
                          },
                        }}
                      >
                        <MenuItem value={-1} sx={{ display: 'none' }}>
                          Select Student
                        </MenuItem>
                        {studentFilterData.map((opt) => (
                          <MenuItem key={opt.studentId} value={opt.studentId}>
                            {opt.studentName}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={3} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        From Date
                      </Typography>
                      <DatePickers
                        disabled={studentFilterData.length === 0}
                        name="fromDateFilter"
                        value={dayjs(fromDateFilter, 'DD/MM/YYYY')}
                        onChange={(e) => {
                          const formattedDate = e ? e.format('DD/MM/YYYY') : '';
                          setFromDateFilter(formattedDate);
                          console.log('date::::', formattedDate);
                          // const formattedDate = e ? e.format('YYYY-MM-DD') : '';
                          // setSelectedDate(formattedDate);
                          loadFeePaidList({ ...currentFeePaidListRequest, fromDate: formattedDate });
                        }}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={3} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        To Date
                      </Typography>
                      <DatePickers
                        disabled={studentFilterData.length === 0}
                        name="toDateFilter"
                        value={dayjs(toDateFilter, 'DD/MM/YYYY')}
                        onChange={(e) => {
                          const formattedDate = e ? e.format('DD/MM/YYYY') : '';
                          setToDateFilter(formattedDate);
                          console.log('date::::', formattedDate);
                          // const formattedDate = e ? e.format('YYYY-MM-DD') : '';
                          // setSelectedDate(formattedDate);
                          loadFeePaidList({ ...currentFeePaidListRequest, toDate: formattedDate });
                        }}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto">
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button sx={{ mt: { xs: 2 } }} type="reset" variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Paper sx={{ display: 'none' }}>
              <div ref={tableRef}>
                <DataTable
                  columns={feePaidListColumnsForExport}
                  data={feePaidListData}
                  getRowKey={getRowKey}
                  fetchStatus={feePaidListStatus}
                  footerColumn={
                    <TableRow
                      style={{
                        backgroundColor: theme.palette.grey[300],
                      }}
                    >
                      <TableCell colSpan={4}> </TableCell>
                      <TableCell>
                        <Typography fontSize={14} variant="subtitle1">
                          Total Amount :
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Stack direction="row" alignItems="center">
                          <CurrencyRupeeIcon sx={{ fontSize: '14px' }} />
                          <Typography fontSize={14} variant="subtitle2">
                            {totalAmount}
                          </Typography>
                        </Stack>
                      </TableCell>
                      <TableCell> </TableCell>
                    </TableRow>
                  }
                />
              </div>
            </Paper>
            <Paper className="card-table-container" sx={{ marginTop: '12px' }}>
              <DTVirtuoso
                tableStyles={{ minWidth: { xs: '1700px' } }}
                columns={feePaidListColumns}
                data={paginatedData}
                getRowKey={getRowKey}
                fetchStatus={feePaidListStatus}
                showHorizontalScroll
                PaginationProps={pageProps}
                allowPagination
                footerColumn={
                  <TableRow
                    className="footer_row"
                    style={{
                      zIndex: 11,
                      position: 'sticky',
                      bottom: 0,
                      backgroundColor: theme.palette.grey[300],
                    }}
                  >
                    <TableCell colSpan={4}> </TableCell>
                    <TableCell>
                      <Typography fontSize={14} variant="subtitle1">
                        Total Amount :
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Stack direction="row" alignItems="center">
                        <CurrencyRupeeIcon sx={{ fontSize: '14px' }} />
                        <Typography fontSize={14} variant="subtitle2">
                          {totalAmount}
                        </Typography>
                      </Stack>
                    </TableCell>
                    <TableCell colSpan={5}> </TableCell>
                  </TableRow>
                }
              />
            </Paper>
          </div>
        </Card>
        <Box pt={2} display="flex" width="100%" sx={{ justifyContent: { xs: 'center', sm: 'right' } }}>
          <Stack spacing={2} direction="row">
            <Button
              disabled={feePaidListData.length === 0}
              variant="contained"
              size="medium"
              color="success"
              onClick={exportExcel}
            >
              Excel
            </Button>
            <Button
              disabled={feePaidListData.length === 0}
              variant="contained"
              size="medium"
              color="info"
              onClick={exportPDF}
            >
              Pdf
            </Button>
            <Button disabled={feePaidListData.length === 0} variant="contained" size="medium" onClick={handlePrint}>
              Print
            </Button>
          </Stack>
        </Box>
      </TotalFeePaidListRoot>
      <TemporaryDrawer
        closeIconDisable
        onClose={toggleDrawerClose}
        state={drawerOpen}
        DrawerContent={
          <PayDrawer
            load={() => loadFeePaidList(currentFeePaidListRequest)}
            receiptDrawer={receiptDrawer}
            onClose={toggleDrawerClose}
            receiptIdNo={receiptId}
          />
        }
      />
      <Popup
        size="xl"
        state={receiptPopupOpen}
        popupContent={<ReceiptPDF receiptIdNo={receiptId} onClose={() => setReceiptPopupOpen(false)} />}
      />
    </Page>
  );
}
