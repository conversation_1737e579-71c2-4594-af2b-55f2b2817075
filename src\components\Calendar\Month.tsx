/* eslint-disable react/no-array-index-key */
import { Box, Typography, Grid } from '@mui/material';
import React, { FC } from 'react';
import useSettings from '@/hooks/useSettings';
import dayjs from 'dayjs';
import { useTheme } from 'styled-components';
import Day from './Day';

type MonthProps = {
  month: dayjs.Dayjs[][];
};

const Month: FC<MonthProps> = ({ month }) => {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  return (
    <Box sx={{ width: '100%', my: 1, height: `calc(100vh - 290px)`, overflow: 'auto' }}>
      <Grid container sx={{ display: 'flex', flex: 1 }}>
        {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day, index) => (
          <Grid
            item
            key={index}
            xs={1.71}
            sx={{
              backgroundColor: isLight ? theme.palette.grey[300] : theme.palette.grey[700],
              border: '0.5px solid lightgrey',
              textAlign: 'center',
              py: 1,
            }}
          >
            <Typography variant="subtitle2">{day}</Typography>
          </Grid>
        ))}
      </Grid>
      <Grid container sx={{ height: '87%', display: 'flex', flex: 1 }}>
        {month.map((row, i) => (
          <Grid item xs={12} key={i}>
            <Grid container sx={{ height: '100%' }}>
              {row.map((day, idx) => (
                <Grid
                  item
                  xs={1.71}
                  sx={{
                    backgroundColor: isLight ? 'inherit' : theme.palette.grey[800],
                    border: '0.5px solid lightgrey',
                  }}
                >
                  <Day idx={idx} day={day} />
                </Grid>
              ))}
            </Grid>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

export default Month;
