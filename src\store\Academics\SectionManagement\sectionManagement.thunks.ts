import api from '@/api';
import {
  SectionCreateRequest,
  SectionListInfo,
  SectionListPagedData,
  SectionListRequest,
} from '@/types/AcademicManagement';
import { CreateResponse, DeleteResponse, UpdateResponse } from '@/types/Common';
import { createAsyncThunk } from '@reduxjs/toolkit';

export const fetchSectionList = createAsyncThunk<SectionListPagedData, SectionListRequest, { rejectValue: string }>(
  'SectionManagement/list',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.SectionManagement.GetSectionList(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in fetching Section List');
    }
  }
);
export const fetchSectionLists = createAsyncThunk<any, any, { rejectValue: string }>(
  'SectionManagement/lists',
  async (adminId, { rejectWithValue }) => {
    try {
      const response = await api.SectionManagement.SectionList(adminId);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in fetching Section List');
    }
  }
);

export const addNewSection = createAsyncThunk<CreateResponse, SectionCreateRequest, { rejectValue: string }>(
  'SectionManagement/addnew',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.SectionManagement.AddNewSection(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in adding new Section');
    }
  }
);

export const updateSection = createAsyncThunk<UpdateResponse, SectionListInfo, { rejectValue: string }>(
  'SectionManagement/update',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.SectionManagement.UpdateSection(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in updating Section');
    }
  }
);

export const deleteSection = createAsyncThunk<DeleteResponse, number, { rejectValue: string }>(
  'SectionManagement/delete',
  async (SectionId, { rejectWithValue }) => {
    try {
      const response = await api.SectionManagement.DeleteSection(SectionId);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in deleting Section');
    }
  }
);
