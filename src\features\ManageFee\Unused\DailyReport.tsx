/* eslint-disable jsx-a11y/alt-text */
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Stack,
  TextField,
  Button,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Typography,
  Card,
  Avatar,
  Paper,
} from '@mui/material';
import styled, { useTheme } from 'styled-components';
import { CLASS_SELECT, YEAR_SELECT } from '@/config/Selection';

const DailyReportRoot = styled.div`
  padding: 1rem;
  .Card {
    min-height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }
  }
  .MuiTableCell-root {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.grey[200] : props.theme.palette.grey[900]};
  }
  .TableCard {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.grey[200] : props.theme.palette.grey[900]};
  }
`;

export default function DailyReport() {
  const theme = useTheme();
  return (
    <Page title="Daily Report">
      <DailyReportRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Typography variant="h6" fontSize={17}>
            Daily Report
          </Typography>
          <Divider />
          <Grid pb={4} pt={2} container spacing={3}>
            <Grid item lg={2} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Select Year
              </Typography>
              <Autocomplete
                options={YEAR_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select Year" />}
              />
            </Grid>

            <Grid item lg={2} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Select Class
              </Typography>
              <Autocomplete
                options={CLASS_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select Class" />}
              />
            </Grid>
            <Grid item lg={2} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Select Student
              </Typography>
              <Autocomplete
                options={CLASS_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select Student" />}
              />
            </Grid>

            <Grid item lg={2} xs={12}>
              <Typography variant="h6" fontSize={14}>
                From
              </Typography>
              <Autocomplete
                options={YEAR_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select Title" />}
              />
            </Grid>
            <Grid item lg={2} xs={12}>
              <Typography variant="h6" fontSize={14}>
                To
              </Typography>
              <Autocomplete
                options={YEAR_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select Title" />}
              />
            </Grid>

            <Grid item lg={2} xs={12}>
              <Stack spacing={1} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                <Button variant="contained" color="secondary" fullWidth>
                  Reset
                </Button>
                <Button variant="contained" color="primary" fullWidth>
                  Search
                </Button>
              </Stack>
            </Grid>
          </Grid>

          <Box textAlign="center" pb={2}>
            <Typography variant="h6" fontSize={16}>
              School Name
            </Typography>
            <Typography variant="h6" fontSize={16} fontWeight={500} sx={{ color: theme.palette.grey[500] }}>
              Manjakulam Road Near Palakkad, 678014
            </Typography>
          </Box>

          <Card
            className="TableCard"
            sx={{
              boxShadow: 0,
            }}
          >
            <Paper
              className="TableCard"
              sx={{
                boxShadow: 0,
                width: '100%',
                overflow: 'auto',
                '&::-webkit-scrollbar': {
                  width: 0,
                },
              }}
            >
              <TableContainer
                sx={{
                  width: { xs: '75.75rem', md: '100%' },
                }}
              >
                <Table stickyHeader aria-label="simple table">
                  <TableHead>
                    <TableRow>
                      <TableCell>SL.No</TableCell>
                      <TableCell>Student Name</TableCell>
                      <TableCell>Class</TableCell>
                      <TableCell>Roll No</TableCell>
                      <TableCell>Payment Date</TableCell>
                      <TableCell>Total Payment</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {[1, 2, 3, 4, 5].map((row) => (
                      <TableRow hover key={row}>
                        <TableCell>01</TableCell>
                        <TableCell>
                          <Stack direction="row" alignItems="center" gap={1}>
                            <Avatar src="" /> Fionna Grand
                          </Stack>
                        </TableCell>
                        <TableCell>VII-A</TableCell>
                        <TableCell>01</TableCell>
                        <TableCell>20/05/2023</TableCell>
                        <TableCell>4000</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Paper>
            <Box
              sx={{ borderTop: '1.5px dashed gray', pl: '12%', pr: '20%' }}
              display="flex"
              justifyContent="space-between"
              py={2}
            >
              <Typography variant="body2" fontWeight={600}>
                Total
              </Typography>
              <Typography variant="body2" fontWeight={600}>
                20,000
              </Typography>
            </Box>
          </Card>

          <Box display="flex" justifyContent="center" mt={2}>
            <Button
              variant="contained"
              size="medium"
              color="secondary"
              sx={{ color: '#fff', backgroundColor: theme.palette.secondary.main }}
            >
              Print
            </Button>
          </Box>
        </Card>
      </DailyReportRoot>
    </Page>
  );
}
