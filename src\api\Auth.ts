import ApiUrls from '@/config/ApiUrls';
import { LoginRequest, LoginRequestNew, LoginResponse, TokenVerificationResponse } from '@/types/Auth';
import { publicApi } from './base/api';
import { APIResponse } from './base/types';

async function Login(loginRequest: LoginRequest): Promise<APIResponse<LoginResponse>> {
  const response = await publicApi.post<LoginResponse>(ApiUrls.LoginUrl, loginRequest);
  return response;
}

async function LoginNew(loginRequest: LoginRequestNew): Promise<APIResponse<LoginResponse>> {
  const response = await publicApi.post<LoginResponse>(ApiUrls.LoginUrlNew, loginRequest);
  return response;
}

async function VerifyAccessToken(accessToken: string): Promise<APIResponse<TokenVerificationResponse>> {
  const response = await publicApi.post<TokenVerificationResponse>(ApiUrls.VerifyTokenUrl, { accessToken });
  return response;
}

const methods = {
  Login,
  LoginNew,
  VerifyAccessToken,
};

export default methods;
