/* eslint-disable jsx-a11y/alt-text */
import React, { useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Card,
  Checkbox,
  IconButton,
} from '@mui/material';
import styled from 'styled-components';
import Popup from '@/components/shared/Popup/Popup';
import NotSuccess from '@/assets/attendance/notApprove.svg';
import RemoveCircleOutlineIcon from '@mui/icons-material/RemoveCircleOutline';
import { YEAR_SELECT } from '@/config/Selection';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import { STAFF_DATA, StaffDataProps, staffDataHead } from '@/config/TableData';
import DateSelect from '@/components/shared/Selections/DateSelect';

const StaffAttendanceRoot = styled.div`
  padding: 1rem;
  .Card {
    min-height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }
  }
  .student_name {
    width: 200px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    font-size: 14px;
    font-weight: 600;
  }
  .student_name2 {
    width: 130px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    font-size: 14px;
    font-weight: 600;
  }
`;
interface EnhancedTableProps {
  handleSelectAllClick: (event: React.ChangeEvent<HTMLInputElement>) => void;
  selected: StaffDataProps[];
}

function EnhancedTableHead(props: EnhancedTableProps) {
  const { handleSelectAllClick, selected } = props;
  return (
    <TableHead>
      <TableRow>
        <TableCell padding="checkbox">
          <Checkbox
            indeterminate={selected.length > 0 && selected.length < STAFF_DATA.length}
            checked={selected.length === STAFF_DATA.length}
            onChange={handleSelectAllClick}
            inputProps={{ 'aria-label': 'select all rows' }}
          />
        </TableCell>
        {staffDataHead.map((headCell) => (
          <TableCell key={headCell.id}>{headCell.label}</TableCell>
        ))}
      </TableRow>
    </TableHead>
  );
}

function StaffAttendance() {
  const [popup, setPopup] = React.useState(false);
  const [staffData, setStaffData] = useState<StaffDataProps[] | undefined>([]);
  const [selected, setSelected] = useState<StaffDataProps[]>([]);
  const [year, setYear] = useState<string | null>('');

  const handleClickOpen = () => setPopup(true);
  const handleClickClose = () => setPopup(false);

  const handleSearch = () => {
    const findStaffs: StaffDataProps[] | undefined =
      STAFF_DATA && STAFF_DATA?.length > 0 ? STAFF_DATA?.filter((list) => list?.year === year) : undefined;
    setStaffData(findStaffs);
    console.log('filterdata', findStaffs);
  };

  const handleSelectAllClick = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      setSelected(staffData);
    } else {
      setSelected([]);
    }
  };

  const handleRowClick = (row: StaffDataProps) => {
    const selectedIndex = selected.indexOf(row);
    let newSelected: StaffDataProps[] = [];

    if (selectedIndex === -1) {
      newSelected = newSelected.concat(selected, row);
    } else if (selectedIndex === 0) {
      newSelected = newSelected.concat(selected.slice(1));
    } else if (selectedIndex === selected.length - 1) {
      newSelected = newSelected.concat(selected.slice(0, -1));
    } else if (selectedIndex > 0) {
      newSelected = newSelected.concat(selected.slice(0, selectedIndex), selected.slice(selectedIndex + 1));
    }

    setSelected(newSelected);
  };

  const isSelected = (row: StaffDataProps) => selected.indexOf(row) !== -1;
  const isButtonDisabled = selected.length === 0;
  const isCancell = () => setSelected([]);

  return (
    <Page title="Staff Attendance">
      <StaffAttendanceRoot>
        <Card className="Card" elevation={5} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Typography variant="h6" fontSize={17}>
            Staff Attendance Marking
          </Typography>
          <Divider />
          <Grid pb={4} container spacing={3} pt={2}>
            <Grid item lg={4} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Select Year
              </Typography>
              <Autocomplete
                options={YEAR_SELECT}
                value={year}
                onChange={(e, v) => setYear(v)}
                renderInput={(params) => (
                  <TextField onChange={({ target }) => setYear(target.value)} {...params} placeholder="Select" />
                )}
              />
            </Grid>
            <Grid item lg={4} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Select Date
              </Typography>
              <DateSelect />
            </Grid>
            <Grid item lg={4} xs={12}>
              <Stack spacing={2} sx={{ pt: { xs: 0, md: 3.79 } }} direction="row">
                <Button onClick={() => {}} variant="contained" color="secondary" fullWidth>
                  Reset
                </Button>
                <Button onClick={handleSearch} variant="contained" color="primary" fullWidth>
                  Search
                </Button>
              </Stack>
            </Grid>
          </Grid>
          {staffData && staffData?.length === 0 && (
            <Stack
              direction="row"
              display="flex"
              justifyContent="center"
              alignItems="center"
              height="calc(100vh - 390px)"
              width="100%"
            >
              <Box>
                <img src={NotSuccess} alt="" width={150} />
                <Typography textAlign="center" pt={2} variant="body2">
                  No data found.
                </Typography>
              </Box>
            </Stack>
          )}
          {staffData && staffData?.length > 0 && (
            <Stack sx={{ width: '100%', flexDirection: { xs: 'column', md: 'row' } }} gap={3}>
              <Paper
                sx={{
                  border: `1px solid #e8e8e9`,
                  width: { xs: '100%', md: '60%' },
                  height: '100%',
                  overflow: 'auto',
                  '&::-webkit-scrollbar': {
                    height: 0,
                  },
                }}
              >
                <TableContainer
                  sx={{
                    width: { xs: '650px', md: '100%' },
                  }}
                >
                  <Table stickyHeader aria-labelledby="tableTitle">
                    <EnhancedTableHead selected={selected} handleSelectAllClick={handleSelectAllClick} />
                    <TableBody>
                      {staffData &&
                        staffData?.length > 0 &&
                        staffData?.map((row) => {
                          const isRowSelected = isSelected(row);

                          return (
                            <TableRow
                              hover
                              onClick={() => handleRowClick(row)}
                              role="checkbox"
                              aria-checked={isRowSelected}
                              tabIndex={-1}
                              key={row.name}
                              selected={isRowSelected}
                              sx={{ cursor: 'pointer' }}
                            >
                              <TableCell padding="checkbox">
                                <Checkbox
                                  color="primary"
                                  checked={isRowSelected}
                                  inputProps={{ 'aria-labelledby': `checkbox-${row.id}` }}
                                />
                              </TableCell>
                              <TableCell sx={{ fontWeight: 600 }} width={100}>
                                {row.id}
                              </TableCell>
                              <TableCell width={300}>
                                <Typography className="student_name">{row.name}</Typography>
                              </TableCell>
                              <TableCell width={300}>
                                <Typography className="student_name">{row.phone}</Typography>
                              </TableCell>
                              <TableCell
                                width={200}
                                sx={{
                                  fontWeight: 600,
                                  color: `${isRowSelected ? ' red' : 'green'}`,
                                }}
                              >
                                {isRowSelected ? row.status : 'Present'}
                              </TableCell>
                            </TableRow>
                          );
                        })}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Paper>
              {/* ----------------------- Absentees List Table ------------------------------------------ */}
              <Paper
                sx={{
                  border: '1px solid #e8e8e9',
                  overflow: 'auto',
                  height: '100%',
                  width: { xs: '100%', md: '40%' },
                  '&::-webkit-scrollbar': {
                    width: 0,
                    height: 0,
                  },
                }}
              >
                <Typography variant="h6" p={2}>
                  Absentees List
                </Typography>
                {selected && selected?.length > 0 && (
                  <TableContainer
                    sx={{
                      width: { xs: '450px', md: '100%' },
                      height: 'calc(100vh - 376px)',
                    }}
                  >
                    <Table stickyHeader>
                      <TableHead>
                        <TableRow>
                          {staffDataHead.map((headCell) => (
                            <TableCell key={headCell.id}>{headCell.label}</TableCell>
                          ))}
                          <TableCell> </TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {selected?.map((row) => (
                          <TableRow>
                            <TableCell sx={{ fontWeight: 600 }}>{row.id}</TableCell>
                            <TableCell>
                              <Typography className="student_name2"> {row.name}</Typography>
                            </TableCell>
                            <TableCell>
                              <Typography className="student_name2"> {row.phone}</Typography>
                            </TableCell>
                            <TableCell sx={{ color: 'red', fontWeight: 600 }}>{row.status}</TableCell>
                            <TableCell align="center">
                              <IconButton onClick={() => handleRowClick(row)}>
                                <RemoveCircleOutlineIcon sx={{ color: 'red' }} />
                              </IconButton>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                )}
                {selected && selected?.length === 0 && (
                  <Stack
                    direction="column"
                    display="flex"
                    justifyContent="center"
                    alignItems="center"
                    width="100%"
                    sx={{ height: 'calc(100vh - 376px)' }}
                  >
                    <Box pb={2}>
                      <img src={NotSuccess} alt="" />
                    </Box>

                    <Typography textAlign="center" variant="body2">
                      Not selected students.
                    </Typography>
                  </Stack>
                )}
              </Paper>
            </Stack>
          )}
          {staffData && staffData?.length > 0 && (
            <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 2 }}>
              <Stack spacing={2} direction="row">
                <Button variant="contained" onClick={isCancell} disabled={isButtonDisabled} color="secondary">
                  Cancel
                </Button>
                <Button onClick={handleClickOpen} disabled={isButtonDisabled} variant="contained" color="primary">
                  Confirm
                </Button>
              </Stack>
            </Box>
          )}
        </Card>
        <Popup
          size="xs"
          state={popup}
          onClose={handleClickClose}
          popupContent={<SuccessMessage message=" Passed Absentees Marked Successfully" />}
        />
      </StaffAttendanceRoot>
    </Page>
  );
}

export default StaffAttendance;
