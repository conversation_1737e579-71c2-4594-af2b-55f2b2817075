/* eslint-disable jsx-a11y/alt-text */
import React, { useState } from 'react';
import {
  Grid,
  Stack,
  TextField,
  Button,
  Typography,
  Box,
  Select,
  MenuItem,
  FormControlLabel,
  FormControl,
  useTheme,
  Radio,
  RadioGroup,
} from '@mui/material';
import styled from 'styled-components';
import { STATUS_OPTIONS } from '@/config/Selection';

const CreateBookRoot = styled.div`
  width: 100%;
  padding: 0.5rem 1.5rem;
`;

function CreateBook() {
  const theme = useTheme();
  return (
    <CreateBookRoot>
      <form noValidate>
        <Grid container spacing={3}>
          <Grid item md={6} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Book Code
              </Typography>
              <TextField placeholder="Enter Book Code" name="" />
            </FormControl>
          </Grid>
          <Grid item md={6} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Book Title
              </Typography>
              <TextField placeholder="Enter Book Title " name="" />
            </FormControl>
          </Grid>
          <Grid item md={12} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Author
              </Typography>
              <TextField placeholder="Enter Author Name " name="" />
            </FormControl>
          </Grid>
          <Grid item md={6} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Book Price
              </Typography>
              <TextField placeholder="Enter Price " name="" />
            </FormControl>
          </Grid>
          <Grid item md={6} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Publication Year
              </Typography>
              <TextField placeholder="Enter Year " name="" />
            </FormControl>
          </Grid>
          <Grid item md={12} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Book About
              </Typography>
              <TextField placeholder="Enter About " name="" />
            </FormControl>
          </Grid>
          <Grid item md={12} xs={12}>
            <FormControl>
              <RadioGroup
                sx={{ ml: 2 }}
                row
                defaultValue=""
                aria-labelledby="demo-row-radio-buttons-group-label"
                name="row-radio-buttons-group"
              >
                <FormControlLabel
                  sx={{ pr: 2, backgroundColor: theme.palette.primary.lighter, borderRadius: 10 }}
                  control={<Radio size="small" />}
                  label="V.Good"
                  value="v-good"
                />
                <FormControlLabel
                  sx={{ pr: 2, backgroundColor: theme.palette.primary.lighter, borderRadius: 10 }}
                  control={<Radio size="small" />}
                  label="Good"
                  value="good"
                />
                <FormControlLabel
                  sx={{ pr: 2, backgroundColor: theme.palette.primary.lighter, borderRadius: 10 }}
                  control={<Radio size="small" />}
                  label="Average"
                  value="avg"
                />
                <FormControlLabel
                  sx={{ pr: 2, backgroundColor: theme.palette.primary.lighter, borderRadius: 10 }}
                  control={<Radio size="small" />}
                  label="Bad"
                  value="bad"
                />
              </RadioGroup>
            </FormControl>
          </Grid>

          <Grid item md={6} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Category
              </Typography>
              <Select
                name=""
                value=""
                //   error={touched.classStatus && !!errors.classStatus}
              >
                {STATUS_OPTIONS.map((opt) => (
                  <MenuItem key={opt.id} value={opt.id}>
                    {opt.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item md={6} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Group Type
              </Typography>
              <Select
                name=""
                value=""
                //   error={touched.classStatus && !!errors.classStatus}
              >
                {STATUS_OPTIONS.map((opt) => (
                  <MenuItem key={opt.id} value={opt.id}>
                    {opt.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item md={12} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Book About
              </Typography>
              <TextField placeholder="Enter About " name="" />
            </FormControl>
          </Grid>
          <Grid item md={6} xs={12}>
            <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
              <Button variant="contained" color="secondary" fullWidth>
                Cancel
              </Button>
              <Button variant="contained" color="primary" fullWidth>
                Save
              </Button>
            </Stack>
          </Grid>
        </Grid>
      </form>
    </CreateBookRoot>
  );
}

export default CreateBook;
