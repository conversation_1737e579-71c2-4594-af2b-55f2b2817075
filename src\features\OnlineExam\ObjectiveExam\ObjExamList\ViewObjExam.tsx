/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import {
  Chip,
  Divider,
  Paper,
  Stack,
  Typography,
  Box,
  Avatar,
  Button,
  useTheme,
  IconButton,
  Card,
} from '@mui/material';
import styled from 'styled-components';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import CloseIcon from '@mui/icons-material/Close';
import BackButton from '@/components/shared/BackButton';
import Popup from '@/components/shared/Popup/Popup';
import ViewReport from './ViewReport';

const ViewObjExamRoot = styled.div`
  padding: 1rem;
  .Card {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 40px);
      flex-grow: 1;
      padding-bottom: 15px;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

export const data = [
  {
    slNo: 1,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'Joseph Alexander',
    answered: 15,
    correct: 10,
    timeTaken: '00:25:00',
    startTime: '10:00AM',
    endTime: '10:25AM',
    pause: '00',
    status: 'Ongoing',
  },
  {
    slNo: 2,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'Mathew Jeorge Washington',
    answered: 15,
    correct: 10,
    timeTaken: '00:25:00',
    startTime: '10:00AM',
    endTime: '10:25AM',
    pause: '00',
    status: 'Ongoing',
  },
  {
    slNo: 3,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'Alfred Peter Heisenberg',
    answered: 15,
    correct: 10,
    timeTaken: '00:25:00',
    startTime: '10:00AM',
    endTime: '10:25AM',
    pause: '00',
    status: 'Submitted',
    reattempt: 'Allow',
  },
  {
    slNo: 4,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'Alex Micheal Jackson',
    answered: 15,
    correct: 10,
    timeTaken: '00:25:00',
    startTime: '10:00AM',
    endTime: '10:25AM',
    pause: '00',
    status: 'Ongoing',
  },
  {
    slNo: 5,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'john',
    answered: 15,
    correct: 10,
    timeTaken: '00:25:00',
    startTime: '10:00AM',
    endTime: '10:25AM',
    pause: '00',
    status: 'Submitted',
    reattempt: 'Allowed',
  },
  {
    slNo: 6,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'Micheal',
    answered: 15,
    correct: 10,
    timeTaken: '00:25:00',
    startTime: '10:00AM',
    endTime: '10:25AM',
    pause: '00',
    status: 'Ongoing',
  },
  {
    slNo: 7,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'jack',
    answered: 15,
    correct: 10,
    timeTaken: '00:25:00',
    startTime: '10:00AM',
    endTime: '10:25AM',
    pause: '00',
    status: 'Submitted',
    reattempt: 'Allow',
  },
  {
    slNo: 1,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'Alex',
    answered: 15,
    correct: 10,
    timeTaken: '00:25:00',
    startTime: '10:00AM',
    endTime: '10:25AM',
    pause: '00',
    status: 'Submitted',
    reattempt: 'Allowed',
  },
  {
    slNo: 2,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'Mathew',
    answered: 15,
    correct: 10,
    timeTaken: '00:25:00',
    startTime: '10:00AM',
    endTime: '10:25AM',
    pause: '00',
    status: 'Ongoing',
  },
  {
    slNo: 3,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'Peter',
    answered: 15,
    correct: 10,
    timeTaken: '00:25:00',
    startTime: '10:00AM',
    endTime: '10:25AM',
    pause: '00',
    status: 'Submitted',
    reattempt: 'Allowed',
  },
  {
    slNo: 4,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'Alex Mic',
    answered: 15,
    correct: 10,
    timeTaken: '00:25:00',
    startTime: '10:00AM',
    endTime: '10:25AM',
    pause: '00',
    status: 'Submitted',
    reattempt: 'Allow',
  },
  {
    slNo: 5,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'john',
    answered: 15,
    correct: 10,
    timeTaken: '00:25:00',
    startTime: '10:00AM',
    endTime: '10:25AM',
    pause: '00',
    status: 'Ongoing',
  },
  {
    slNo: 6,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'Micheal',
    answered: 15,
    correct: 10,
    timeTaken: '00:25:00',
    startTime: '10:00AM',
    endTime: '10:25AM',
    pause: '00',
    status: 'Submitted',
    reattempt: 'Allowed',
  },
  {
    slNo: 7,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'jack',
    answered: 15,
    correct: 10,
    timeTaken: '00:25:00',
    startTime: '10:00AM',
    endTime: '10:25AM',
    pause: '00',
    status: 'Submitted',
    reattempt: 'Allowed',
  },
  {
    slNo: 8,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'Alex',
    answered: 15,
    correct: 10,
    timeTaken: '00:25:00',
    startTime: '10:00AM',
    endTime: '10:25AM',
    pause: '00',
    status: 'Submitted',
    reattempt: 'Allow',
  },
  {
    slNo: 9,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'Mathew',
    answered: 15,
    correct: 10,
    timeTaken: '00:25:00',
    startTime: '10:00AM',
    endTime: '10:25AM',
    pause: '00',
    status: 'Submitted',
    reattempt: 'Allowed',
  },
  {
    slNo: 10,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'Peter',
    answered: 15,
    correct: 10,
    timeTaken: '00:25:00',
    startTime: '10:00AM',
    endTime: '10:25AM',
    pause: '00',
    status: 'Submitted',
    reattempt: 'Allowed',
  },
  {
    slNo: 11,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'Alex Mic',
    answered: 15,
    correct: 10,
    timeTaken: '00:25:00',
    startTime: '10:00AM',
    endTime: '10:25AM',
    pause: '00',
    status: 'Submitted',
    reattempt: 'Allow',
  },
  {
    slNo: 12,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'john',
    answered: 15,
    correct: 10,
    timeTaken: '00:25:00',
    startTime: '10:00AM',
    endTime: '10:25AM',
    pause: '00',
    status: 'Submitted',
    reattempt: 'Allowed',
  },
  {
    slNo: 13,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'Micheal',
    answered: 15,
    correct: 10,
    timeTaken: '00:25:00',
    startTime: '10:00AM',
    endTime: '10:25AM',
    pause: '00',
    status: 'Submitted',
    reattempt: 'Allowed',
  },
  {
    slNo: 14,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'jack',
    answered: 15,
    correct: 10,
    timeTaken: '00:25:00',
    startTime: '10:00AM',
    endTime: '10:25AM',
    pause: '00',
    status: 'Submitted',
    reattempt: 'Allowed',
  },
];

function ViewObjExam({ onClose, onBackClick }: any) {
  const [tab, setTab] = useState('Detailed');
  const theme = useTheme();
  const [viewReport, setViewReport] = useState<boolean>(false);

  const getRowKey = useCallback((row: any) => row.examId, []);
  const detailedViewColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'slNo',
        dataKey: 'slNo',
        headerLabel: 'Sl.No',
        sortable: true,
      },
      {
        name: 'studentName',
        renderHeader: () => {
          return (
            <Typography variant="inherit" align="center">
              Student Name
            </Typography>
          );
        },
        renderCell: (row) => {
          return (
            <Stack direction="row" gap={1} alignItems="center">
              <Avatar alt="" src="/static/images/avatar/1.jpg" />
              <Typography variant="subtitle2">{row.studentName}</Typography>
            </Stack>
          );
        },
      },
      {
        name: 'answered',
        headerLabel: 'Correct / Answered',
        renderCell: (row) => {
          return (
            <Box pl={4}>
              <Chip
                size="small"
                label={`${row.correct} / ${row.answered}`}
                sx={{ border: '0px', color: theme.palette.info.dark }}
              />
            </Box>
          );
        },
      },
      // {
      //   name: 'correct',
      //   headerLabel: 'Correct',
      //   renderCell: (row) => {
      //     return (
      //       <Chip
      //         size="small"
      //         label={`${row.correct}/${row.answered}`}
      //         sx={{ border: '0px', color: theme.palette.success.main }}
      //       />
      //     );
      //   },
      // },
      {
        name: 'timeTaken',
        dataKey: 'timeTaken',
        headerLabel: 'Time Taken',
      },
      {
        name: 'startTime',
        dataKey: 'startTime',
        headerLabel: 'Start Time',
      },
      {
        name: 'endTime',
        dataKey: 'endTime',
        headerLabel: 'End Time',
      },
      {
        name: 'pause',
        dataKey: 'pause',
        headerLabel: 'Pause',
      },
      {
        name: 'status',
        dataKey: 'status',
        renderHeader: () => {
          return (
            <Typography variant="inherit" align="center">
              Status
            </Typography>
          );
        },
        renderCell: (row) => {
          return (
            <Box display="flex" justifyContent="center">
              <Chip
                size="small"
                label={row.status}
                variant="filled"
                color={row.status === 'Submitted' ? 'success' : 'warning'}
              />
            </Box>
          );
        },
      },
      {
        name: 'actions',
        headerLabel: 'Actions',
        renderCell: (row) => {
          return (
            <Stack direction="row" gap={2}>
              <Chip
                size="small"
                label="Report"
                variant="filled"
                clickable
                color="info"
                onClick={() => setViewReport(true)}
              />
              {row.status === 'Submitted' && (
                <Chip
                  size="small"
                  label={row.reattempt}
                  variant="filled"
                  clickable={row.reattempt === 'Allow' ? true : false}
                  color={row.reattempt === 'Allowed' ? 'secondary' : 'primary'}
                />
              )}
            </Stack>
          );
        },
      },
    ],
    [theme]
  );
  const unattendedListColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'slNo',
        dataKey: 'slNo',
        headerLabel: 'Sl.No',
        sortable: true,
      },
      {
        name: 'studentName',
        headerLabel: 'Student Name',
        renderCell: (row) => {
          return (
            <Stack direction="row" gap={1} alignItems="center">
              <Avatar alt="" src="/static/images/avatar/1.jpg" />
              <Typography variant="subtitle2">{row.studentName}</Typography>
            </Stack>
          );
        },
      },
      {
        name: 'guardianName',
        dataKey: 'guardianName',
        headerLabel: 'Guardian Name',
        renderCell: () => {
          return <Typography>Parent</Typography>;
        },
      },
      {
        name: 'guardianNumber',
        dataKey: 'guardianNumber',
        headerLabel: 'Guardian Number',
        renderCell: () => {
          return <Typography>1000000000</Typography>;
        },
      },
    ],
    []
  );

  return (
    <ViewObjExamRoot>
      <Card className="Card" sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
        <Stack direction="row" pb={1}>
          <BackButton onBackClick={onBackClick} />
          <Stack direction="row" spacing={1}>
            <Typography
              variant="h6"
              fontWeight={tab === 'Detailed' ? 900 : 100}
              onClick={() => setTab('Detailed')}
              sx={{ cursor: 'pointer' }}
              color={tab === 'Detailed' ? 'inherit' : 'GrayText'}
            >
              Detailed View
            </Typography>
            <Typography variant="h6" fontWeight={900}>
              /
            </Typography>
            <Typography
              variant="h6"
              fontWeight={tab === 'Unattended' ? 900 : 100}
              onClick={() => setTab('Unattended')}
              sx={{ cursor: 'pointer' }}
              color={tab === 'Unattended' ? 'inherit' : 'GrayText'}
            >
              Unattended Students
            </Typography>
          </Stack>
          {onClose ? (
            <IconButton
              className="close-button"
              aria-label="close"
              onClick={onClose}
              sx={{
                // position: 'absolute',
                // right: 8,
                // top: 8,
                color: theme.palette.grey[500],
              }}
            >
              <CloseIcon />
            </IconButton>
          ) : null}
        </Stack>
        <Divider sx={{ mb: 2 }} />
        <div className="card-main-body">
          <Paper className="card-table-container">
            {tab === 'Detailed' ? (
              <DataTable columns={detailedViewColumns} data={data} getRowKey={getRowKey} fetchStatus="success" />
            ) : (
              <DataTable
                ShowCheckBox
                columns={unattendedListColumns}
                data={data}
                getRowKey={getRowKey}
                fetchStatus="success"
              />
            )}
          </Paper>
          {tab === 'Unattended' && (
            <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 2 }}>
              <Stack spacing={2} direction="row">
                <Button variant="contained" color="secondary">
                  Cancel
                </Button>
                <Button variant="contained" color="primary">
                  Notify
                </Button>
              </Stack>
            </Box>
          )}
        </div>
      </Card>
      <Popup size="xl" title="" state={viewReport} onClose={() => setViewReport(false)} popupContent={<ViewReport />} />
    </ViewObjExamRoot>
  );
}

export default ViewObjExam;
