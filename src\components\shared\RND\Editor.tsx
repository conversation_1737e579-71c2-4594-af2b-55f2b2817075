// PlateEditor.tsx
import React from 'react';
import { Plate, usePlateEditor } from 'platejs/react';
import {
  BoldPlugin,
  ItalicPlugin,
  UnderlinePlugin,
  H1Plugin,
  H2Plugin,
  H3Plugin,
  BlockquotePlugin,
} from 'platejs/basic-nodes/react';
import { FixedToolbar } from 'platejs/react'; // or from your own UI wrapper
import { MarkToolbarButton } from 'platejs/react'; // same
import { ToolbarButton } from '@mui/material'; // MUI button

import { EditorContainer, Editor } from './ui'; // assume your styled container

interface PlateEditorProps {
  value?: any;
  onChange?: (value: any) => void;
  placeholder?: string;
}

const PlateEditor: React.FC<PlateEditorProps> = ({ value, onChange, placeholder = 'Start typing...' }) => {
  const editor = usePlateEditor({
    plugins: [BoldPlugin, ItalicPlugin, UnderlinePlugin, H1Plugin, H2Plugin, H3Plugin, BlockquotePlugin],
    value,
  });

  return (
    <Plate editor={editor} onChange={onChange}>
      <FixedToolbar sx={{ bgcolor: '#f5f5f5', px: 1 }}>
        <MarkToolbarButton nodeType="bold" tooltip="Bold (⌘+B)">
          B
        </MarkToolbarButton>
        <MarkToolbarButton nodeType="italic" tooltip="Italic (⌘+I)">
          I
        </MarkToolbarButton>
        <MarkToolbarButton nodeType="underline" tooltip="Underline (⌘+U)">
          U
        </MarkToolbarButton>

        <ToolbarButton onClick={() => editor.tf.h1.toggle()}>H1</ToolbarButton>
        <ToolbarButton onClick={() => editor.tf.h2.toggle()}>H2</ToolbarButton>
        <ToolbarButton onClick={() => editor.tf.h3.toggle()}>H3</ToolbarButton>

        <ToolbarButton onClick={() => editor.tf.blockquote.toggle()}>Quote</ToolbarButton>

        <ToolbarButton sx={{ ml: 'auto' }} onClick={() => editor.tf.setValue(value)}>
          Reset
        </ToolbarButton>
      </FixedToolbar>

      <EditorContainer>
        <Editor placeholder={placeholder} />
      </EditorContainer>
    </Plate>
  );
};

export default PlateEditor;
