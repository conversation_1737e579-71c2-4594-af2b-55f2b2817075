/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import { Autocomplete, Box, Grid, Stack, TextField, Button, Typography } from '@mui/material';
import styled from 'styled-components';
import { CLASS_SELECT, SUBJECT_SELECT, YEAR_SELECT } from '@/config/Selection';

const MapOnlineExamRoot = styled.div`
  width: 100%;
`;

function MapOnlineExam() {
  return (
    <MapOnlineExamRoot>
      <form noValidate>
        <Grid px={5} pt={1} container spacing={5}>
          <Grid item md={6} xs={12}>
            <Stack spacing={1}>
              <Typography variant="h6" color="GrayText" pb={1}>
                From :
              </Typography>
              <Typography variant="h6" fontSize={14}>
                Academic Year
              </Typography>
              <Autocomplete
                options={YEAR_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select Year" />}
              />
              <Typography variant="h6" fontSize={14}>
                Class
              </Typography>
              <Autocomplete
                options={CLASS_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select Class" />}
              />
              <Typography variant="h6" fontSize={14}>
                Subject
              </Typography>
              <Autocomplete
                options={SUBJECT_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select Subject" />}
              />
            </Stack>
          </Grid>
          <Grid item md={6} xs={12}>
            <Stack spacing={1}>
              <Typography variant="h6" color="GrayText" pb={1}>
                To :
              </Typography>
              <Typography variant="h6" fontSize={14}>
                Academic Year
              </Typography>
              <Autocomplete
                options={YEAR_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select Year" />}
              />
              <Typography variant="h6" fontSize={14}>
                Class
              </Typography>
              <Autocomplete
                options={CLASS_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select Class" />}
              />
              <Typography variant="h6" fontSize={14}>
                Subject
              </Typography>
              <Autocomplete
                options={SUBJECT_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select Subject" />}
              />
            </Stack>
          </Grid>
        </Grid>
        <Box px={5} py={1}>
          <Typography variant="h6" fontSize={14}>
            Exams
          </Typography>
          <Autocomplete
            multiple
            options={['Annual Examination', 'Unit Test 1', 'Unit Test 2', 'Unit Test 3']}
            renderInput={(params) => <TextField {...params} placeholder="Select Exams" />}
          />
        </Box>
        <Stack
          spacing={2}
          direction="row"
          sx={{ py: { xs: 3.5, md: 3.79 }, pr: 5, ml: 'auto', width: { xs: '95%', md: '35%' } }}
        >
          <Button variant="contained" color="secondary" fullWidth>
            Cancel
          </Button>
          <Button variant="contained" color="primary" fullWidth>
            Map
          </Button>
        </Stack>
      </form>
    </MapOnlineExamRoot>
  );
}

export default MapOnlineExam;
