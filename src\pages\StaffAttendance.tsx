/* eslint-disable prettier/prettier */
import * as React from 'react';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';
import Page from '@/components/shared/Page';
import styled from 'styled-components';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import { Link, useLocation, useOutletContext } from 'react-router-dom';
import LeaveList from '@/features/StaffAttendance/LeaveList';
import { useDispatch } from 'react-redux';
import { flushStore } from '@/store/flush.slice';
import { OutletContextType } from '@/types/Common';
import StaffWorkingDayList from '@/features/StaffAttendance/StaffWorkingDayList';
import StaffPunchTimeSet from '@/features/StaffAttendance/StaffPunchTimeSet';
import PunchList from '@/features/StaffAttendance/PunchList/PunchList';
import PunchReport from '@/features/StaffAttendance/PunchReport';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const StaffManagementRoot = styled.div`
  min-height: calc(100vh - ${TOP_BAR_HEIGHT});
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  .tab {
    background-color: ${(props) => (props.theme.themeMode === 'light' ? '#fff' : props.theme.palette.grey[800])};
    @media screen and (min-width: 1267px) {
      width: 100%;
    }
    @media screen and (max-width: 720px) {
      width: 100%; 
    }
  }
  .Mui-selected {
    color: ${(props) => props.theme.palette.primary.main};
  }
  /* .MuiTabs-scrollButtons.Mui-disabled {
    opacity: 0.3;
  } */
`;

function TabPanel({ children, value, index }: TabPanelProps) {
  return (
    <Box
      sx={{ paddingTop: '3rem' }}
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
    >
      {value === index && <Box>{children}</Box>}
    </Box>
  );
}

function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`,
  };
}

export default function StaffManagement() {
  const { topBarHeight } = useOutletContext<OutletContextType>();
  const location = useLocation();
  const [value, setValue] = React.useState(0);

  const dispatch = useDispatch();

  React.useEffect(() => {
    dispatch(flushStore());
    console.log('Flush Store');
  }, [dispatch, value]);

  React.useEffect(() => {
    const currentPath = location.pathname;
    const routePaths = [
      '/staff-attendance/leave-list',
      '/staff-attendance/working-day-list',
      '/staff-attendance/punch-time-set',
      '/staff-attendance/punch-list',
      '/staff-attendance/punch-report'
    ];
    const index = routePaths.indexOf(currentPath);
    if (index !== -1) {
      setValue(index);
    }
  }, [location]);

  return (
    <Page title="Staff Management">
      <StaffManagementRoot>
        <Tabs
          className="tab"
          variant="scrollable"
          scrollButtons
          allowScrollButtonsMobile
          value={value}
          aria-label="basic tabs"
          sx={{
            ml: '0px',
            borderBottom: 1,
            borderColor: 'divider',
            position: 'fixed',
             top: `${topBarHeight}px`,
            zIndex: 1,
            width: '100%',
          }}
        >
          <Tab {...a11yProps(0)} label="Leave List" component={Link} to="/staff-attendance/leave-list" />
          <Tab {...a11yProps(1)} label="Staff Working Day List " component={Link} to="/staff-attendance/working-day-list" />
          <Tab {...a11yProps(2)} label="Staff Punch Time Set " component={Link} to="/staff-attendance/punch-time-set" />
          <Tab {...a11yProps(3)} label="Punch List" component={Link} to="/staff-attendance/punch-list" />
          <Tab {...a11yProps(4)} label="Punch Report" component={Link} to="/staff-attendance/punch-report" />
        </Tabs>
        <TabPanel value={value} index={0}>
          <LeaveList />
        </TabPanel>
        <TabPanel value={value} index={1}>
          <StaffWorkingDayList />
        </TabPanel>
        <TabPanel value={value} index={2}>
          <StaffPunchTimeSet />
        </TabPanel>
        <TabPanel value={value} index={3}>
          <PunchList />
        </TabPanel>
        <TabPanel value={value} index={4}>
          <PunchReport />
        </TabPanel>
      </StaffManagementRoot>
    </Page>
  );
}
