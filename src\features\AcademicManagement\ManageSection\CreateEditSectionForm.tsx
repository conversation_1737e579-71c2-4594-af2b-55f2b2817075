import { STATUS_OPTIONS } from '@/config/Selection';
import { SectionListInfo } from '@/types/AcademicManagement';
import LoadingButton from '@mui/lab/LoadingButton/LoadingButton';
import { Button, TextField, Typography, Box, Stack, MenuItem, Select, FormControl } from '@mui/material';
import { useFormik } from 'formik';
import * as Yup from 'yup';

export type CreateEditSectionFormProps = {
  onSave: (values: SectionListInfo, mode: 'create' | 'edit') => void;
  onCancel: (mode: 'create' | 'edit') => void;
  onClose: (mode: 'create' | 'edit') => void;
  sectionDetail: SectionListInfo;
  isSubmitting: boolean;
};

const CreateEditSectionValidationSchema = Yup.object({
  sectionName: Yup.string().required('Please enter Section Name'),
  sectionStatus: Yup.number().oneOf([0, 1], 'Please select Section Status'),
});

function CreateEditSectionForm({ sectionDetail, onSave, onCancel, isSubmitting }: CreateEditSectionFormProps) {
  const mode = sectionDetail.sectionId === 0 ? 'create' : 'edit';
  const {
    values: { sectionName, sectionStatus },
    handleChange,
    handleSubmit,
    touched,
    errors,
  } = useFormik<SectionListInfo>({
    initialValues: {
      sectionId: sectionDetail.sectionId,
      sectionName: sectionDetail.sectionName,
      sectionStatus: sectionDetail.sectionStatus,
    },
    validationSchema: CreateEditSectionValidationSchema,
    onSubmit: (values) => {
      onSave(values, mode);
    },
  });

  return (
    <Box mt={3}>
      <form noValidate onSubmit={handleSubmit}>
        <Stack sx={{ height: 'calc(100vh - 150px)' }} direction="column">
          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Section Name
            </Typography>
            <TextField
              placeholder="Section Name"
              name="sectionName"
              value={sectionName}
              onChange={handleChange}
              error={touched.sectionName && !!errors.sectionName}
              helperText={errors.sectionName}
              disabled={isSubmitting}
            />
          </FormControl>
          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Status
            </Typography>
            <Select
              name="sectionStatus"
              value={sectionStatus}
              onChange={handleChange}
              error={touched.sectionStatus && !!errors.sectionStatus}
              disabled={isSubmitting}
            >
              {STATUS_OPTIONS.map((opt) => (
                <MenuItem key={opt.id} value={opt.id}>
                  {opt.name}
                </MenuItem>
              ))}
            </Select>
            {touched.sectionStatus && !!errors.sectionStatus && (
              <Typography color="red" fontSize="0.625rem" variant="subtitle1">
                {errors.sectionStatus}
              </Typography>
            )}
          </FormControl>
        </Stack>
        <Box sx={{ justifyContent: 'center', pt: 3, position: 'relative' }}>
          <Stack spacing={2} direction="row" sx={{}}>
            <Button onClick={() => onCancel(mode)} fullWidth variant="contained" color="secondary" type="button">
              Cancel
            </Button>
            <LoadingButton
              loading={isSubmitting}
              fullWidth
              variant="contained"
              color="primary"
              type="submit"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Saving...' : 'Save'}
            </LoadingButton>
          </Stack>
        </Box>
      </form>
    </Box>
  );
}

export default CreateEditSectionForm;
