/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  Collapse,
  FormControl,
} from '@mui/material';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import styled from 'styled-components';
import SearchIcon from '@mui/icons-material/Search';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import DeleteIcon from '@mui/icons-material/Delete';
import Pdf from '@/assets/attendance/PDF.svg';
import { CLASS_SELECT, SUBJECT_SELECT, YEAR_SELECT } from '@/config/Selection';

const SubjectChapterDetailsRoot = styled.div`
  padding: 16px;

  .Card {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    height: calc(100vh - 10rem);
    @media screen and (max-width: 62.25rem) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 2.5rem);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 0.0625rem solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

export const data = [
  {
    slNo: 1,
    status: 'Unblock',
    class: 'VII-D',
    subject: 'English',
    chapter: 'Sit urna',
  },
  {
    slNo: 2,
    status: 'Block',
    class: 'VII-D',
    subject: 'English',
    chapter: 'Sit urna',
  },
  {
    slNo: 3,
    status: 'Block',
    class: 'VII-D',
    subject: 'English',
    chapter: 'Sit urna',
  },
  {
    slNo: 4,
    status: 'Block',
    class: 'VII-D',
    subject: 'English',
    chapter: 'Sit urna',
  },
  {
    slNo: 5,
    status: 'Unblock',
    class: 'VII-D',
    subject: 'English',
    chapter: 'Sit urna',
  },
  {
    slNo: 6,
    status: 'Unblock',
    class: 'VII-D',
    subject: 'English',
    chapter: 'Sit urna',
  },
  {
    slNo: 7,
    status: 'Block',
    class: 'VII-D',
    subject: 'English',
    chapter: 'Sit urna',
  },
];

function SubjectChapterDetails() {
  const [showFilter, setShowFilter] = useState(false);

  const getRowKey = useCallback((row: any) => row.examId, []);
  const AuthorizeKeywordsColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'slNo',
        dataKey: 'slNo',
        headerLabel: 'Sl.No',
        sortable: true,
      },
      {
        name: 'class',
        dataKey: 'class',
        headerLabel: 'Class',
      },
      {
        name: 'subject',
        dataKey: 'subject',
        headerLabel: 'Subject',
      },
      {
        name: 'chapter',
        dataKey: 'chapter',
        headerLabel: 'Chapter',
      },
      {
        name: 'actions',
        headerLabel: 'Actions',
        renderCell: (row) => {
          return (
            <Stack direction="row" gap={1}>
              <IconButton size="small" sx={{ padding: 0.5 }}>
                <ModeEditIcon />
              </IconButton>
              <IconButton size="small" color="error" sx={{ padding: 0.5 }}>
                <DeleteIcon />
              </IconButton>
            </Stack>
          );
        },
      },
    ],
    []
  );

  return (
    <Page title="Subject Chapter Details">
      <SubjectChapterDetailsRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17} width="80%">
              Subject Chapter Details
            </Typography>
            <Box pb={1} sx={{ flexShrink: 0 }}>
              <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                <SearchIcon />
              </IconButton>
            </Box>
          </Stack>
          <Divider />

          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={5} pt={1} container spacing={2}>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 200 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Autocomplete
                        options={YEAR_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select year" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 200 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Autocomplete
                        options={CLASS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select class" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 200 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Subject
                      </Typography>
                      <Autocomplete
                        options={SUBJECT_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select subject" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 200 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Chapter
                      </Typography>
                      <Autocomplete
                        options={SUBJECT_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select chapter" />}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item lg={1} xs={12}>
                    <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                      <Button variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                      {/* <Button variant="contained" color="primary" fullWidth>
                          Search
                        </Button> */}
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Paper className="card-table-container" sx={{ marginTop: '.75rem' }}>
              <DataTable
                columns={AuthorizeKeywordsColumns}
                data={data}
                getRowKey={getRowKey}
                fetchStatus="success"
                allowPagination
                PaginationProps={[]}
              />
            </Paper>
          </div>
        </Card>
      </SubjectChapterDetailsRoot>
    </Page>
  );
}

export default SubjectChapterDetails;
