import DateSelect from '@/components/shared/Selections/DateSelect';
import {
  Avatar,
  Stack,
  useTheme,
  Box,
  Typography,
  Divider,
  Button,
  Grid,
  Autocomplete,
  TextField,
  FormControlLabel,
  Radio,
  RadioGroup,
  IconButton,
  MenuItem,
} from '@mui/material';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import React, { useState } from 'react';
import styled from 'styled-components';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { StaffListInfo } from '@/types/StaffManagement';
import { Select } from '@mui/material';
import { GENDER_SELECT } from '@/config/Selection';

const VisuallyHiddenInput = styled('input')({
  clip: 'rect(0 0 0 0)',
  clipPath: 'inset(50%)',
  height: 1,
  overflow: 'hidden',
  position: 'absolute',
  bottom: 0,
  left: 0,
  whiteSpace: 'nowrap',
  width: 1,
});

const CreateEditStaffFormRoot = styled.div`
  width: 100%;
`;

const sectionOptions = [
  { value: 'highSchool', label: 'High School' },
  { value: 'primary', label: 'Primary' },
  { value: 'nursery', label: 'Nursery' },
  { value: 'cbsc', label: 'CBSC' },
];

const CreateEditStaffValidationSchema = Yup.object({
  // staffName: Yup.string().required('Please enter the first name').min(2, 'Name must be at least 2 characters long'),
  // staffCode: Yup.string()
  //   .required('Please enter the staff code')
  //   .matches(/^[A-Za-z0-9]+$/, 'Staff code must be alphanumeric'),
  // staffPhoneNumber: Yup.string()
  //   .required('Please enter a phone number')
  //   .matches(/^\d{10}$/, 'Phone number must be exactly 10 digits'),
  // staffEmailID: Yup.string().required('Please enter an email').email('Enter a valid email'),
  // staffJoinDate: Yup.date().required('Please select the joining date'),
  // staffDOB: Yup.date().required('Please select the date of birth'),
  // staffBloodGroup: Yup.string()
  //   .oneOf(['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'], 'Please select a valid blood group')
  //   .required('Blood group is required'),
  // staffGender: Yup.string().oneOf(['Male', 'Female'], 'Please select a gender').required(),
  // staffNationality: Yup.string().required('Please enter nationality'),
  // staffReligion: Yup.string().required('Please enter religion'),
  // staffPAddress: Yup.string().required('Please enter a permanent address'),
  // staffCAddress: Yup.string().required('Please enter a current address'),
});

const CreateEditStaffForm = () => {
  // const mode = staffDetail.subjectId === 0 ? 'create' : 'edit';
  const {
    values: {
      staffID,
      academicId,
      staffCode,
      staffJoinDate,
      staffName,
      staffGender,
      staffDOB,
      staffBloodGroup,
      staffBirthPlace,
      staffNationality,
      staffMotherTongue,
      staffReligion,
      staffCaste,
      staffFatherName,
      staffMotherName,
      staffFatherOccupation,
      staffJobExperience,
      staffJobRole,
      staffJobDescription,
      staffPhoneNumber,
      staffPAddress,
      staffCAddress,
      staffEmailID,
      staffImage,
      staffCategory,
    },
    handleChange,
    handleSubmit,
    touched,
    errors,
  } = useFormik<StaffListInfo>({
    initialValues: {
      staffID: 0,
      academicId: 0,
      staffCode: '',
      staffJoinDate: '',
      staffName: '',
      staffGender: 0,
      staffDOB: '',
      staffBloodGroup: '',
      staffBirthPlace: '',
      staffNationality: '',
      staffMotherTongue: '',
      staffReligion: '',
      staffCaste: '',
      staffFatherName: '',
      staffMotherName: '',
      staffFatherOccupation: '',
      staffJobExperience: '',
      staffJobRole: '',
      staffJobDescription: '',
      staffPhoneNumber: '',
      staffPAddress: '',
      staffCAddress: '',
      staffEmailID: '',
      staffImage: '',
      staffCategory: 0,
      createdBy: 0,
    },
    validationSchema: CreateEditStaffValidationSchema,
    onSubmit: (values) => {
      // onSave(values);
    },
  });

  const theme = useTheme();
  const [uploadedImage, setUploadedImage] = useState('');
  const [selectedSection, setSelectedSection] = React.useState('highSchool');

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const image = event.target.files;
    if (image) {
      const img = URL.createObjectURL(image);
      setUploadedImage(img);
    }
  };

  const handleSectionChange = (event) => {
    setSelectedSection(event.target.value);
  };

  return (
    <CreateEditStaffFormRoot>
      <Divider sx={{ mt: '1rem' }} />
      <Box sx={{ py: ' 0.5rem', px: ' 1.5rem' }}>
        <Stack direction="row" gap={2} alignItems="center">
          <Avatar alt="" sx={{ width: 100, height: 100 }} src={uploadedImage} />
          {/* <Button size="small" component="label" variant="contained" color="secondary" sx={{ borderRadius: 50 }}>
            Add Photo
            <VisuallyHiddenInput type="file" multiple />
          </Button> */}
          <Button size="small" component="label" variant="contained" color="secondary" sx={{ borderRadius: 50 }}>
            <VisuallyHiddenInput type="file" accept="image/*" onChange={handleFileChange} />
            Add Photo
          </Button>
        </Stack>
        <form noValidate>
          <Grid container pt={3} spacing={3}>
            <Grid item md={6} xs={12}>
              <Typography variant="h6" fontSize={14}>
                First Name
              </Typography>
              <TextField
                fullWidth
                placeholder="Enter name"
                name="staffName"
                value={staffName}
                onChange={handleChange}
                error={touched.staffName && !!errors.staffName}
                helperText={errors.staffName}
                // disabled={isSubmitting}
              />
            </Grid>
            <Grid item md={6} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Gender
              </Typography>
              <Select value={staffGender} onChange={handleChange} placeholder="Select Gender">
                <MenuItem value={0} sx={{ display: 'none' }}>
                  Select Gender
                </MenuItem>
                {GENDER_SELECT.map((opt) => (
                  <MenuItem key={opt.id} value={opt.gender}>
                    {opt.gender}
                  </MenuItem>
                ))}
              </Select>
            </Grid>
            <Grid item md={6} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Staff Code
              </Typography>
              <TextField fullWidth placeholder="Enter name" />
            </Grid>
            <Grid item md={6} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Joining Date
              </Typography>
              <DateSelect />
            </Grid>
            <Grid item md={6} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Date of Birth
              </Typography>
              <DateSelect />
            </Grid>
            <Grid item md={6} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Blood Group
              </Typography>
              <DateSelect />
            </Grid>
            <Grid item md={6} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Birth Place
              </Typography>
              <TextField fullWidth placeholder="Enter place" />
            </Grid>
            <Grid item md={6} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Nationality
              </Typography>
              <TextField fullWidth placeholder="Enter nationality" />
            </Grid>
            <Grid item md={6} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Mother Tongue
              </Typography>
              <TextField fullWidth placeholder="Enter mother tongue" />
            </Grid>
            <Grid item md={6} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Religion
              </Typography>
              <TextField fullWidth placeholder="Enter your religion" />
            </Grid>
            <Grid item md={6} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Previous Organization
              </Typography>
              <TextField fullWidth placeholder="Enter organization name" />
            </Grid>
            <Grid item md={6} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Organization Location
              </Typography>
              <TextField fullWidth placeholder="Enter organization location" />
            </Grid>
            <Grid item md={6} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Job Experience
              </Typography>
              <TextField fullWidth placeholder="Enter experience" />
            </Grid>
            <Grid item md={6} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Job Role
              </Typography>
              <Autocomplete
                options={['Male', 'Female']}
                renderInput={(params) => <TextField {...params} placeholder="Select Role" />}
              />
            </Grid>
            <Grid item md={12} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Job Description
              </Typography>
              <TextField multiline rows={2} fullWidth placeholder="Enter description" />
            </Grid>
            <Grid item md={6} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Mobile Number
              </Typography>
              <TextField fullWidth placeholder="Enter number" />
            </Grid>
            <Grid item md={6} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Job Email Id
              </Typography>
              <TextField fullWidth placeholder="Enter email id" />
            </Grid>
            <Grid item md={6} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Permanent Address
              </Typography>
              <TextField multiline rows={2} fullWidth placeholder="Enter address" />
            </Grid>
            <Grid item md={6} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Current Address
              </Typography>
              <TextField multiline rows={2} fullWidth placeholder="Enter address" />
            </Grid>
            <Grid item md={6} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Father Name
              </Typography>
              <TextField fullWidth placeholder="Enter father name" />
            </Grid>
            <Grid item md={6} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Mother Name
              </Typography>
              <TextField fullWidth placeholder="Enter mother name" />
            </Grid>
            <Grid item md={12} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Section Type
              </Typography>
              <RadioGroup
                row
                value={selectedSection}
                onChange={handleSectionChange}
                aria-labelledby="demo-row-radio-buttons-group-label"
                name="row-radio-buttons-group"
              >
                {sectionOptions.map((option) => (
                  <FormControlLabel
                    key={option.value}
                    value={option.value}
                    sx={{
                      pr: 2,
                      borderRadius: 50,
                      backgroundColor:
                        selectedSection === option.value ? theme.palette.primary.lighter : theme.palette.grey[300],
                    }}
                    control={<Radio size="small" />}
                    label={option.label}
                  />
                ))}
              </RadioGroup>
            </Grid>
          </Grid>

          <Stack direction="row" justifyContent="end" sx={{ pt: { xs: 3.5, md: 3.79 } }}>
            <Button variant="contained" color="primary">
              Save
            </Button>
          </Stack>
        </form>
      </Box>
    </CreateEditStaffFormRoot>
  );
};

export default CreateEditStaffForm;
