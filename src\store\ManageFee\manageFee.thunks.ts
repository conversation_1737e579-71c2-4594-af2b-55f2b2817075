import api from '@/api';
import { CreateResponse, DeleteResponse, UpdateResponse } from '@/types/Common';
import {
  ClassListDataType,
  ClassSectionsDataType,
  CreateBasicFeeSettingDataType,
  CreateBasicFeeSettingTitleDataType,
  CreateScholarshipFeeSettingDataType,
  CreateScholarshipSettingsDataType,
  CreateTermFeeSettingDataType,
  CreateTermFeeSettingTitleDataType,
  FeeDateSettingsType,
  ScholarshipFeeListType,
  GetOptionalFeeSettingsDataType,
  GetIndividualFeeSettingsDataType,
  GetScholarshipSettingsDataType,
  OptionalFeeSettingsDataType,
  IndividualFeeSettingsDataType,
  StudentFeeStatusDataType,
  StudentTermFeePayType,
  StudentTermFeeStatusDataType,
  StudentTermFeeStatusNewType,
  TermFeeDetailsDataType,
  DeleteScholarshipType,
  DeleteTermFeeListType,
  UpdateTermFeeListType,
  DeleteBasicFeeListType,
  UpdateBasicFeeListType,
  GetFeeOverviewStatusType,
  GetFeeOverviewChartType,
  GetFeeOverviewPaidListType,
  GetFeeOverviewModeListType,
  GetReceiptForPrintType,
  GetFeePaidListType,
  GetFeePaidListDataType,
  GetStudentsFilterDataType,
  GetFeePaidBasicListRequestType,
  GetFeePaidBasicListDataType,
  GetBasicFeeFilterDataType,
  GetFeePaidTermListRequestType,
  GetTermFeeFilterDataType,
  GetFeePaidTermListDataType,
  GetFeePendingListRequestType,
  GetFeePendingListDataType,
  GetFeePendingBasicListRequestType,
  GetFeePendingBasicListDataType,
  GetFeePendingTermListDataType,
  GetFeePendingTermListRequestType,
  GetFineListDataType,
  GetFineListRequestType,
  CreateEditFineListResponseType,
  CreateEditFineListRequestType,
  DeleteFineListResponseType,
  DeleteFineListRequestType,
  GetFineMappingListDataType,
  FineMapInsertDataType,
  FineMapDeleteDataType,
  BasicFeeMappedDeleteDataType,
  BasicFeeMappedDeleteAllDataType,
  TermFeeMappedDeleteDataType,
  TermFeeMappedDeleteAllDataType,
  GetBasicFeeListRequestType,
  ReceiptCancelRequestType,
  CheckReceiptNoType,
  GetTermFeeListType,
  OptionalFeeMappedDeleteType,
  OptionalFeeMappedDeleteAllType,
  ScholarshipMappedDeleteAllType,
  ScholarshipMappedDeleteType,
  StopMappingSettingsType,
  CreateStopMappingType,
  DeleteBusMappedType,
  DeleteAllBusMappedStudentType,
  ReceiptApproveRequestType,
  FeeOverviewPaidListRequest,
  StudentPickerRequest,
  GetStudentPickerDataType,
} from '@/types/ManageFee';
import { createAsyncThunk } from '@reduxjs/toolkit';

export const fetchGetTermFee = createAsyncThunk<
  TermFeeDetailsDataType[],
  { adminId: number | undefined; academicId: number | undefined; feeTypeId: number | undefined },
  { rejectValue: string }
>('manageFee/termFee', async ({ adminId, academicId, feeTypeId }, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.GetTermFee(adminId, academicId, feeTypeId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching Manage Fee term fee List');
  }
});

export const fetchClassSections = createAsyncThunk<
  ClassSectionsDataType[],
  { adminId: number | undefined; academicId: number | undefined },
  { rejectValue: string }
>('manageFee/classSections', async ({ adminId, academicId }, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.GetClassSections(adminId, academicId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching Manage Fee Class Sections List');
  }
});

export const fetchFeeDateSettings = createAsyncThunk<
  FeeDateSettingsType[],
  {
    adminId: number | undefined;
    academicId: number | undefined;
    sectionId: number | undefined;
    feeTypeId: number | undefined;
  },
  { rejectValue: string }
>('manageFee/feeDateSettings', async ({ adminId, academicId, sectionId, feeTypeId }, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.GetFeeDateSettings(adminId, academicId, sectionId, feeTypeId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching Manage Fee Date Settings List');
  }
});

export const fetchStudentTermFeeStatus = createAsyncThunk<
  StudentTermFeeStatusDataType[],
  {
    adminId: number | undefined;
    academicId: number | undefined;
    sectionId: number | undefined;
    classId: number | undefined;
    studentId: number | undefined;
  },
  { rejectValue: string }
>('manageFee/studentTermFee', async ({ adminId, academicId, sectionId, classId, studentId }, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.GetStudentTermFeeStatus(adminId, academicId, sectionId, classId, studentId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching Manage Fee Student Term Fee Status List');
  }
});

export const fetchClassList = createAsyncThunk<
  ClassListDataType[],
  { adminId: number | undefined; academicId: number | undefined; sectionId: number | undefined },
  { rejectValue: string }
>('manageFee/classData', async ({ adminId, academicId, sectionId }, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.GetClassListData(adminId, academicId, sectionId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching Manage Fee Class List');
  }
});

export const fetchStudentsFeeStatus = createAsyncThunk<
  StudentFeeStatusDataType[],
  {
    adminId: number | undefined;
    academicId: number | undefined;
    sectionId: number | undefined;
    classId: number | undefined;
  },
  { rejectValue: string }
>('manageFee/studentsFeeStatus', async ({ adminId, academicId, sectionId, classId }, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.GetStudentsFeeStatus(adminId, academicId, sectionId, classId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching Manage Fee Students Fee Status List');
  }
});

export const fetchStudentTermFeeStatusNew = createAsyncThunk<
  StudentTermFeeStatusNewType,
  {
    adminId: number | undefined;
    academicId: number | undefined;
    sectionId: number | undefined;
    classId: number | undefined;
    studentId: number | undefined;
    feeTypeId: number | undefined;
  },
  { rejectValue: string }
>(
  'manageFee/studentTermFeeStatusNew',
  async ({ adminId, academicId, sectionId, classId, studentId, feeTypeId }, { rejectWithValue }) => {
    try {
      const response = await api.ManageFee.GetStudentTermFeeStatusNew(
        adminId,
        academicId,
        sectionId,
        classId,
        studentId,
        feeTypeId
      );
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in fetching Student Term Fee StatusNew List');
    }
  }
);

export const studentTermFeePay = createAsyncThunk<
  StudentTermFeePayType,
  StudentTermFeePayType,
  { rejectValue: string }
>('manageFee/studentTermFeePay', async (request, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.StudentTermFeePay(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in Student Term Fee Pay');
  }
});
// Create Basic Fee Setting

export const createBasicFeeSetting = createAsyncThunk<
  CreateResponse,
  CreateBasicFeeSettingDataType[],
  { rejectValue: string }
>('manageFee/createBasicFeeSetting', async (request, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.CreateBasicFeeSetting(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in Create Basic Fee Setting');
  }
});

// Create Basic Fee Setting

export const createTermFeeSetting = createAsyncThunk<
  CreateResponse,
  CreateTermFeeSettingDataType,
  { rejectValue: string }
>('manageFee/createTermFeeSetting', async (request, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.CreateTermFeeSetting(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in Create Term Fee Setting');
  }
});

// Create Basic Fee Setting Title
export const createBasicFeeSettingTitle = createAsyncThunk<
  CreateResponse,
  CreateBasicFeeSettingTitleDataType,
  { rejectValue: string }
>('manageFee/createBasicFeeSettingTitle', async (request, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.CreateBasicFeeSettingTitle(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in Create Basic Fee Setting Title');
  }
});

// Create Term Fee Setting Title
export const createTermFeeSettingTitle = createAsyncThunk<
  CreateResponse,
  CreateTermFeeSettingTitleDataType,
  { rejectValue: string }
>('manageFee/createTermFeeSettingTitle', async (request, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.CreateTermFeeSettingTitle(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in Create Term Fee Setting Title');
  }
});

// Create Scholarship Fee Setting
export const createScholarshipFeeSetting = createAsyncThunk<
  CreateScholarshipFeeSettingDataType,
  CreateScholarshipFeeSettingDataType,
  { rejectValue: string }
>('manageFee/createScholarshipFeeSetting', async (request, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.CreateScholarshipFeeSetting(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in Create Scholarship Fee Setting');
  }
});

export const fetchScholarshipFeeList = createAsyncThunk<
  ScholarshipFeeListType,
  { adminId: number | undefined; academicId: number | undefined; feeTypeId: number | undefined },
  { rejectValue: string }
>('manageFee/getScholarshipFeeList', async ({ adminId, academicId, feeTypeId }, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.GetScholarshipFeeList(adminId, academicId, feeTypeId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching Manage Fee ScholarshipFee List');
  }
});
// Get Optional Fee Settings

export const fetchGetOptionalFeeSettings = createAsyncThunk<
  GetOptionalFeeSettingsDataType[],
  {
    adminId: number | undefined;
    academicId: number | undefined;
    sectionId: number | undefined;
    classId: number | undefined;
    feeTypeId: number | undefined;
  },
  { rejectValue: string }
>(
  'manageFee/getOptionalFeeSettings ',
  async ({ adminId, academicId, sectionId, classId, feeTypeId }, { rejectWithValue }) => {
    try {
      const response = await api.ManageFee.GetOptionalFeeSettings(adminId, academicId, sectionId, classId, feeTypeId);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in fetching Manage Fee Get Optional Fee Settings');
    }
  }
);
// Optional Fee Settings

export const optionalFeeSettings = createAsyncThunk<
  CreateResponse,
  OptionalFeeSettingsDataType,
  { rejectValue: string }
>('manageFee/optionalFeeSettings', async (request, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.OptionalFeeSettings(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in Optional Fee Settings');
  }
});

// Get Optional Fee Settings Individual

export const fetchGetOptionalFeeSettingsIndividual = createAsyncThunk<
  GetIndividualFeeSettingsDataType,
  {
    adminId: number | undefined;
    academicId: number | undefined;
    sectionId: number | undefined;
    classId: number | undefined;
    feeTypeId: number | undefined;
  },
  { rejectValue: string }
>(
  'manageFee/getOptionalFeeSettingsIndividual',
  async ({ adminId, academicId, sectionId, classId, feeTypeId }, { rejectWithValue }) => {
    try {
      const response = await api.ManageFee.GetOptionalFeeSettingsIndividual(adminId, academicId, sectionId, classId, feeTypeId);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in fetching Manage Fee Get Individual Fee Settings');
    }
  }
);

// Optional Fee Settings Individual

export const optionalFeeSettingsIndividual = createAsyncThunk<
  CreateResponse,
  IndividualFeeSettingsDataType,
  { rejectValue: string }
>('manageFee/optionalFeeSettingsIndividual', async (request, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.OptionalFeeSettingsIndividual(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in Individual Fee Settings');
  }
});

// Get Scholarship Settings

export const fetchScholarshipSettings = createAsyncThunk<
  GetScholarshipSettingsDataType[],
  {
    adminId: number | undefined;
    academicId: number | undefined;
    sectionId: number | undefined;
    classId: number | undefined;
    feeTypeId: number | undefined;
  },
  { rejectValue: string }
>(
  'manageFee/getScholarshipSettings ',
  async ({ adminId, academicId, sectionId, classId, feeTypeId }, { rejectWithValue }) => {
    try {
      const response = await api.ManageFee.GetScholarshipSettings(adminId, academicId, sectionId, classId, feeTypeId);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in fetching Manage Fee Get Scholarship Settings');
    }
  }
);
// Scholarship Settings

export const createScholarshipSettings = createAsyncThunk<
  CreateScholarshipSettingsDataType,
  CreateScholarshipSettingsDataType,
  { rejectValue: string }
>('manageFee/createScholarshipSettings', async (request, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.CreateScholarshipSettings(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in Scholarship Settings');
  }
});
// Delete Scholarship
export const DeleteScholarship = createAsyncThunk<DeleteResponse, DeleteScholarshipType[], { rejectValue: string }>(
  'manageFee/deleteScholarship',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.ManageFee.DeleteScholarship(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in deleting Scholarship');
    }
  }
);

// Get Basic Fee List
export const fetchBasicFeeList = createAsyncThunk<
  CreateBasicFeeSettingTitleDataType[],
  GetBasicFeeListRequestType,
  { rejectValue: string }
>('manageFee/getBasicFeeList', async (request, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.GetBasicFeeList(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in Get Basic Fee List');
  }
});

// Delete Basic Fee List
export const deleteBasicFeeList = createAsyncThunk<DeleteResponse, DeleteBasicFeeListType[], { rejectValue: string }>(
  'manageFee/deleteBasicFeeList',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.ManageFee.DeleteBasicFeeList(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in deleting Basic Fee List');
    }
  }
);
// Update Basic Fee List
export const editBasicFeeList = createAsyncThunk<UpdateResponse, UpdateBasicFeeListType, { rejectValue: string }>(
  'manageFee/updateBasicFeeList',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.ManageFee.UpdateBasicFeeList(request);
      return response.data;
    } catch (error) {
      console.error(error);
      return rejectWithValue('Something went wrong in updating Basic Fee List');
    }
  }
);

// Get Basic Fee List
export const fetchTermFeeList = createAsyncThunk<
  CreateTermFeeSettingTitleDataType[],
  GetTermFeeListType,
  { rejectValue: string }
>('manageFee/getTermFeeList', async (request, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.GetTermFeeList(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in Get Term Fee List');
  }
});

// Delete Basic Fee List
export const deleteTermFeeList = createAsyncThunk<DeleteResponse, DeleteTermFeeListType[], { rejectValue: string }>(
  'manageFee/deleteTermFeeList',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.ManageFee.DeleteTermFeeList(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in deleting Term Fee List');
    }
  }
);
// Update Basic Fee List
export const editTermFeeList = createAsyncThunk<UpdateResponse, UpdateTermFeeListType, { rejectValue: string }>(
  'manageFee/updateTermFeeList',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.ManageFee.UpdateTermFeeList(request);
      return response.data;
    } catch (error) {
      console.error(error);
      return rejectWithValue('Something went wrong in updating Term Fee List');
    }
  }
);

// ------ Manage Fee OverView ------

// Get Fee Overview Status
export const fetchFeeOverviewStatus = createAsyncThunk<
  GetFeeOverviewStatusType,
  {
    adminId: number | undefined;
    academicId: number | undefined;
    classId: number | undefined;
    feeTypeId: number | undefined;
  },
  { rejectValue: string }
>('manageFee/getFeeOverviewStatus ', async ({ adminId, academicId, classId, feeTypeId }, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.GetFeeOverviewStatus(adminId, academicId, classId, feeTypeId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching Manage Fee Get Fee Overview Status');
  }
});

// Get Fee Overview Chart
export const fetchFeeOverviewChart = createAsyncThunk<
  GetFeeOverviewChartType[],
  {
    adminId: number | undefined;
    academicId: number | undefined;
    classId: number | undefined;
    feeTypeId: number | undefined;
  },
  { rejectValue: string }
>('manageFee/getFeeOverviewChart ', async ({ adminId, academicId, classId, feeTypeId }, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.GetFeeOverviewChart(adminId, academicId, classId, feeTypeId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching Manage Fee Get Fee Overview Chart');
  }
});

export const fetchFeeOverviewPaidList = createAsyncThunk<
  GetFeeOverviewPaidListType[],
  FeeOverviewPaidListRequest,
  { rejectValue: string }
>('manageFee/getFeeOverviewPaidList', async (request, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.GetFeeOverviewPaidList(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching Manage Fee Get Fee Overview Paid List');
  }
});

// Get Fee Overview Mode List
export const fetchFeeOverviewModeList = createAsyncThunk<
  GetFeeOverviewModeListType[],
  {
    adminId: number | undefined;
    academicId: number | undefined;
    classId: number | undefined;
    feeTypeId: number | undefined;
  },
  { rejectValue: string }
>('manageFee/getFeeOverviewModeList ', async ({ adminId, academicId, classId, feeTypeId }, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.GetFeeOverviewModeList(adminId, academicId, classId, feeTypeId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching Manage Fee Get Fee Overview Mode List');
  }
});

// Get Receipt For Print
export const fetchReceiptForPrint = createAsyncThunk<
  GetReceiptForPrintType[],
  {
    adminId: number | undefined;
    receiptId: number | undefined;
  },
  { rejectValue: string }
>('manageFee/getReceiptForPrint ', async ({ adminId, receiptId }, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.GetReceiptForPrint(adminId, receiptId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching Manage Fee Get Receipt For Print List');
  }
});
// -----------------------------------------------
// Get Fee Paid List
export const fetchFeePaidList = createAsyncThunk<GetFeePaidListDataType[], GetFeePaidListType, { rejectValue: string }>(
  'manageFee/getFeePaidList',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.ManageFee.GetFeePaidList(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in Get Fee Paid List');
    }
  }
);
// Get Students Filter
export const fetchStudentsFilter = createAsyncThunk<
  GetStudentsFilterDataType[],
  {
    adminId: number | undefined;
    academicId: number | undefined;
    sectionId: number | undefined;
    classId: number | undefined;
    feeTypeId: number | undefined;
  },
  { rejectValue: string }
>(
  'manageFee/getStudentsFilter ',
  async ({ adminId, academicId, sectionId, classId, feeTypeId }, { rejectWithValue }) => {
    try {
      const response = await api.ManageFee.GetStudentsFilter(adminId, academicId, sectionId, classId, feeTypeId);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in fetching Manage Fee Get Students Filter');
    }
  }
);
// -----------------------------------------------

// Get Fee Paid Basic List
export const fetchFeePaidBasicList = createAsyncThunk<
  GetFeePaidBasicListDataType[],
  GetFeePaidBasicListRequestType,
  { rejectValue: string }
>('manageFee/getFeePaidBasicList', async (request, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.GetFeePaidBasicList(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in Get Fee Paid Basic List');
  }
});
// Get Basic Fee Filter
export const fetchBasicFeeFilter = createAsyncThunk<
  GetBasicFeeFilterDataType[],
  {
    adminId: number | undefined;
    academicId: number | undefined;
    feeTypeId: number | undefined;
  },
  { rejectValue: string }
>('manageFee/getBasicFeeFilter ', async ({ adminId, academicId, feeTypeId }, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.GetBasicFeeFilter(adminId, academicId, feeTypeId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching Manage Fee Get Basic Fee Filter');
  }
});
// -----------------------------------------------

// Get Fee Paid Term List
export const fetchFeePaidTermList = createAsyncThunk<
  GetFeePaidTermListDataType[],
  GetFeePaidTermListRequestType,
  { rejectValue: string }
>('manageFee/getFeePaidTermList', async (request, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.GetFeePaidTermList(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in Get Fee Paid Term List');
  }
});
// Get Term Fee Filter
export const fetchTermFeeFilter = createAsyncThunk<
  GetTermFeeFilterDataType[],
  {
    adminId: number | undefined;
    academicId: number | undefined;
    feeTypeId: number | undefined;
  },
  { rejectValue: string }
>('manageFee/getTermFeeFilter ', async ({ adminId, academicId, feeTypeId }, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.GetTermFeeFilter(adminId, academicId, feeTypeId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching Manage Fee Get Term Fee Filter');
  }
});
// -----------------------------------------------

// Get Fee Pending List
export const fetchFeePendingList = createAsyncThunk<
  GetFeePendingListDataType[],
  GetFeePendingListRequestType,
  { rejectValue: string }
>('manageFee/getFeePendingList', async (request, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.GetFeePendingList(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in Get Fee Pending List');
  }
});

// Get Fee Pending Basic List
export const fetchFeePendingBasicList = createAsyncThunk<
  GetFeePendingBasicListDataType[],
  GetFeePendingBasicListRequestType,
  { rejectValue: string }
>('manageFee/getFeePendingBasicList', async (request, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.GetFeePendingBasicList(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in  Get Fee Pending Basic List');
  }
});

// Get Fee Pending Term List
export const fetchFeePendingTermList = createAsyncThunk<
  GetFeePendingTermListDataType[],
  GetFeePendingTermListRequestType,
  { rejectValue: string }
>('manageFee/getFeePendingTermList', async (request, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.GetFeePendingTermList(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in  Get Fee Pending Term List');
  }
});

// Get Fine List
export const fetchFineList = createAsyncThunk<GetFineListDataType[], GetFineListRequestType, { rejectValue: string }>(
  'manageFee/getFineList',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.ManageFee.GetFineList(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in Fetching Fine List');
    }
  }
);
// CreateEdit Fine List
export const CreateEditFineList = createAsyncThunk<
  CreateEditFineListResponseType,
  CreateEditFineListRequestType,
  { rejectValue: string }
>('manageFee/CreateEditFineList', async (request, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.CreateEditFineList(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in Create/Edit Fine List');
  }
});
// Delete Fine List
export const DeleteFineList = createAsyncThunk<
  DeleteFineListResponseType,
  DeleteFineListRequestType,
  { rejectValue: string }
>('manageFee/DeleteFineList', async (request, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.DeleteFineList(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in Deleting Fine List');
  }
});

// Get Fine Mapping List
export const fetchFineMappingList = createAsyncThunk<
  GetFineMappingListDataType[],
  {
    adminId: number | undefined;
    academicId: number | undefined;
    feeTypeId: number | undefined;
  },
  { rejectValue: string }
>('manageFee/getFineMappingList ', async ({ adminId, academicId, feeTypeId }, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.GetFineMappingList(adminId, academicId, feeTypeId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching Fine Mapping List');
  }
});

// Fine Map Insert
export const createFineMap = createAsyncThunk<FineMapInsertDataType, FineMapInsertDataType, { rejectValue: string }>(
  'manageFee/fineMapInsert',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.ManageFee.FineMapInsert(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in Fine Map Insert');
    }
  }
);

// Delete Fine Map
export const DeleteFineMap = createAsyncThunk<
  FineMapDeleteDataType[],
  FineMapDeleteDataType[],
  { rejectValue: string }
>('manageFee/DeleteFineMap', async (request, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.DeleteFineMap(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in Deleting Fine Map');
  }
});

// Delete Basic Fee Mapped
export const DeleteBasicFee = createAsyncThunk<
  BasicFeeMappedDeleteDataType,
  BasicFeeMappedDeleteDataType[],
  { rejectValue: string }
>('manageFee/DeleteBasicFee', async (request, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.DeleteBasicFee(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in Deleting Basic Fee');
  }
});
// export const DeleteBasicFee = createAsyncThunk<
//   BasicFeeMappedDeleteDataType[],
//   BasicFeeMappedDeleteDataType[],
//   { rejectValue: string }
// >('manageFee/DeleteBasicFee', async (request, { rejectWithValue }) => {
//   try {
//     const response = await api.ManageFee.DeleteBasicFee(request);
//     return response.data;
//   } catch {
//     return rejectWithValue('Something went wrong in Deleting Basic Fee');
//   }
// });

// Delete All Basic Fee Mapped
export const DeleteAllBasicFee = createAsyncThunk<
  DeleteResponse,
  BasicFeeMappedDeleteAllDataType[],
  { rejectValue: string }
>('manageFee/DeleteAllBasicFee', async (request, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.DeleteAllBasicFee(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in Deleting All Basic Fee');
  }
});
// export const DeleteAllBasicFee = createAsyncThunk<
//   BasicFeeMappedDeleteAllDataType[],
//   BasicFeeMappedDeleteAllDataType[],
//   { rejectValue: string }
// >('manageFee/DeleteAllBasicFee', async (request, { rejectWithValue }) => {
//   try {
//     const response = await api.ManageFee.DeleteAllBasicFee(request);
//     return response.data;
//   } catch {
//     return rejectWithValue('Something went wrong in Deleting All Basic Fee');
//   }
// });

// Delete Term Fee Mapped
export const DeleteTermFee = createAsyncThunk<
  TermFeeMappedDeleteDataType[],
  TermFeeMappedDeleteDataType[],
  { rejectValue: string }
>('manageFee/DeleteTermFee', async (request, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.DeleteTermFee(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in Deleting Term Fee');
  }
});

// Delete All Term Fee Mapped
export const DeleteAllTermFee = createAsyncThunk<
  TermFeeMappedDeleteAllDataType[],
  TermFeeMappedDeleteAllDataType[],
  { rejectValue: string }
>('manageFee/DeleteAllTermFee', async (request, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.DeleteAllTermFee(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in Deleting All Term Fee');
  }
});

// Receipt Cancel
export const ReceiptCancel = createAsyncThunk<
  ReceiptCancelRequestType,
  ReceiptCancelRequestType,
  { rejectValue: string }
>('manageFee/receiptCancel', async (request, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.ReceiptCancel(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in Receipt Cancel');
  }
});

// Check Receipt No
export const CheckReceiptNo = createAsyncThunk<
  CheckReceiptNoType,
  {
    receiptNo: number | undefined;
    feeTypeId: number | undefined;
  },
  { rejectValue: string }
>('manageFee/checkReceiptNo', async ({ receiptNo, feeTypeId }, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.CheckReceiptNo(receiptNo, feeTypeId);
    console.log('responsereceiptNo::::----', receiptNo);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching Check Receipt No');
  }
});

// Delete Optional Fee Mapped
export const DeleteOptionalFee = createAsyncThunk<
  OptionalFeeMappedDeleteType[],
  OptionalFeeMappedDeleteType[],
  { rejectValue: string }
>('manageFee/deleteOptionalFee', async (request, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.DeleteOptionalFee(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in Delete Optional Fee');
  }
});
// Delete All Optional Fee Mapped
export const DeleteAllOptionalFee = createAsyncThunk<
  OptionalFeeMappedDeleteAllType[],
  OptionalFeeMappedDeleteAllType[],
  { rejectValue: string }
>('manageFee/deleteAllOptionalFee', async (request, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.DeleteAllOptionalFee(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in Delete All Optional Fee');
  }
});

// Delete Scholarship Mapped
export const DeleteScholarshipMapped = createAsyncThunk<
  ScholarshipMappedDeleteType[],
  ScholarshipMappedDeleteType[],
  { rejectValue: string }
>('manageFee/deleteScholarshipMapped', async (request, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.DeleteScholarshipMapped(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in Delete Scholarship Mapped');
  }
});
// Delete All Scholarship Mapped
export const DeleteAllScholarshipMapped = createAsyncThunk<
  ScholarshipMappedDeleteAllType[],
  ScholarshipMappedDeleteAllType[],
  { rejectValue: string }
>('manageFee/deleteAllScholarshipMapped', async (request, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.DeleteAllScholarshipMapped(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in Delete All Scholarship Mapped');
  }
});

// Get Stop Mapping Settings
export const fetchStopMappingSettings = createAsyncThunk<
  StopMappingSettingsType[],
  {
    adminId: number | undefined;
    academicId: number | undefined;
    sectionId: number | undefined;
    classId: number | undefined;
    feeTypeId: number | undefined;
  },
  { rejectValue: string }
>(
  'manageFee/getStopMappingSettings ',
  async ({ adminId, academicId, sectionId, classId, feeTypeId }, { rejectWithValue }) => {
    try {
      const response = await api.ManageFee.GetStopMappingSettings(adminId, academicId, sectionId, classId, feeTypeId);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in fetching Manage Fee Get Stop Mapping Settings');
    }
  }
);
// Create Stop Mapping
export const createStopMapping = createAsyncThunk<
  CreateStopMappingType[],
  CreateStopMappingType[],
  { rejectValue: string }
>('manageFee/createStopMapping', async (request, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.CreateStopMapping(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in CreateStop Mapping');
  }
});

// Delete Bus Mapped
export const DeleteBusMapped = createAsyncThunk<DeleteBusMappedType[], DeleteBusMappedType[], { rejectValue: string }>(
  'manageFee/deleteBusMapped',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.ManageFee.DeleteBusMapped(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in Delete Bus Mapped');
    }
  }
);

// Delete All Bus Mapped Student
export const DeleteAllBusMappedStudent = createAsyncThunk<
  DeleteAllBusMappedStudentType[],
  DeleteAllBusMappedStudentType[],
  { rejectValue: string }
>('manageFee/deleteAllBusMappedStudent', async (request, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.DeleteAllBusMappedStudent(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in Delete All Bus Mapped Student');
  }
});

// Receipt Approve
export const ReceiptApprove = createAsyncThunk<
  ReceiptApproveRequestType,
  ReceiptApproveRequestType,
  { rejectValue: string }
>('manageFee/receiptApprove', async (request, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.ReceiptApprove(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in Receipt Approve');
  }
});

export const fetchStudentPickerData = createAsyncThunk<
  GetStudentPickerDataType[],
  StudentPickerRequest,
  { rejectValue: string }
>('manageFee/getStudentPickerData', async (request, { rejectWithValue }) => {
  try {
    const response = await api.ManageFee.GetStudentPickerData(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching Student Picker Data List');
  }
});
