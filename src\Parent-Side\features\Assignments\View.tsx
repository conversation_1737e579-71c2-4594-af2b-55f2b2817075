/* eslint-disable jsx-a11y/alt-text */
import React, { useRef, useState } from 'react';
import { Stack, Button, Typography, Box, Card, IconButton, Chip } from '@mui/material';
import styled, { useTheme } from 'styled-components';
import { CloseIcon } from '@/theme/overrides/CustomIcons';
import TextIcon from '@/Parent-Side/assets/TextIcon.svg';
import WordIcon from '@/Parent-Side/assets/WordIcon.svg';
import PowerpointIcon from '@/Parent-Side/assets/PowerpointIcon.svg';
import { MdAddCircle } from 'react-icons/md';

const ViewRoot = styled.div`
  width: 100%;
  padding: 1.5rem;
`;

function View() {
  const theme = useTheme();
  const fileInputRef = useRef(null);

  const [files, setFiles] = useState([
    { id: 1, fileIcon: TextIcon, name: 'Text File', date: 'Sept 05, 2023, 10:00 am' },
    { id: 2, fileIcon: WordIcon, name: 'Word File', date: 'Sept 05, 2023, 10:00 am' },
    { id: 3, fileIcon: PowerpointIcon, name: 'PowerPoint File', date: 'Sept 05, 2023, 10:00 am' },
  ]);

  const handleAddFile = (event) => {
    const file = event.target.files[0];

    if (file) {
      const invalidTypes = ['image/', 'video/'];
      const isInvalid = invalidTypes.some((type) => file.type.startsWith(type));

      if (isInvalid) {
        alert('Images and videos are not allowed.');
      } else {
        const newFile = {
          id: Date.now(),
          fileIcon: TextIcon, // You can dynamically assign icon based on file.type
          name: file.name,
          date: new Date().toLocaleString(),
        };
        setFiles((prev) => [...prev, newFile]);
      }
    }

    event.target.value = null; // Reset input
  };

  const handleRemoveFile = (id) => {
    setFiles((prev) => prev.filter((file) => file.id !== id));
  };

  return (
    <ViewRoot>
      <Box>
        <Typography variant="subtitle2" mb={1} fontSize={16}>
          Question:
        </Typography>
        <Typography variant="subtitle1" fontSize={14} mb={2}>
          Lorem ipsum dolor sit amet, consectetur adipisicing elit. Quam quis, voluptatem minus placeat facere fugit
          quibusdam dolorem.
        </Typography>
        <Card sx={{ backgroundColor: theme.palette.grey[300], p: 2 }}>
          <Typography pb={2} variant="subtitle2" fontSize={16}>
            Submit Your Answer:
          </Typography>

          {files.map((item) => (
            <Card sx={{ p: 2, mb: 2 }} key={item.id}>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Stack direction="row" alignItems="center" gap={3}>
                  <div>
                    <img src={item.fileIcon} alt="" />
                  </div>
                  <Typography variant="body2">
                    {item.name}, {item.date}
                  </Typography>
                </Stack>
                <IconButton size="small" onClick={() => handleRemoveFile(item.id)}>
                  <CloseIcon color="error" />
                </IconButton>
              </Box>
            </Card>
          ))}

          <Button
            variant="outlined"
            sx={{ borderRadius: 5 }}
            startIcon={<MdAddCircle />}
            onClick={() => fileInputRef.current?.click()}
          >
            Add File
          </Button>

          <input
            accept=".pdf,.doc,.docx,.txt,.ppt,.pptx,.xls,.xlsx"
            ref={fileInputRef}
            type="file"
            hidden
            onChange={handleAddFile}
          />
        </Card>

        <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 3 }}>
          <Stack spacing={2} direction="row">
            <Button variant="contained" color="secondary">
              Cancel
            </Button>
            <Button variant="contained" color="success">
              Submit
            </Button>
          </Stack>
        </Box>
      </Box>
    </ViewRoot>
  );
}

export default View;
