import React from 'react';
import { Typography, IconButton, Tooltip, Stack, useTheme } from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';

type BackButtonProps = {
  onBackClick?: () => void;
  title?: string;
  disabled?: boolean;
};

const BackButton = ({ onBackClick, title, disabled }: BackButtonProps) => {
  const theme = useTheme();

  return (
    <Stack gap={2} direction="row" alignItems="center">
      <Tooltip title="Back">
        <IconButton
          disabled={disabled}
          onClick={onBackClick}
          sx={{
            backgroundColor: theme.palette.secondary.main,
            color: theme.palette.common.white,
            '&:hover': { backgroundColor: theme.palette.secondary.dark },
            width: '25px',
            height: '25px',
          }}
          size="small"
        >
          <ArrowBackIcon fontSize="small" />
        </IconButton>
      </Tooltip>
      <Typography whiteSpace='nowrap' variant="h6" fontSize={17}>
        {title}
      </Typography>
    </Stack>
  );
};

export default BackButton;
