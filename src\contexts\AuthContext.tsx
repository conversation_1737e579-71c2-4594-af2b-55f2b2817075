import api from '@/api';
import { StatusCodes } from '@/config/Constants';
import { GetActiveAccountFromToken } from '@/utils/Auth';
import TokenStorage from '@/utils/TokenStorage';
import { AuthAction, AuthContextState, LoginRequest, Account, LoginRequestNew } from '@/types/Auth';
import { createContext, ReactNode, Reducer, useCallback, useEffect, useMemo, useReducer, useState } from 'react';

const initialState: AuthContextState = {
  method: 'custom',
  isAuthenticated: false,
  isInitialized: false,
  loading: false,
  error: null,
  user: null,
  login: () => Promise.resolve(),
  loginNew: () => Promise.resolve(),
  logout: () => {},
  parentMode: localStorage.getItem('parentMode') === 'true',
  setParentMode: () => {},
  setLoginMode: 0 || 1,
  loginMode: localStorage.getItem('loginMode') === 'true',
};

const handlers: { [K in string]: Reducer<AuthContextState, AuthAction> } = {
  INITIALIZE: (state, action) => {
    const { isAuthenticated, user } = action.payload!;
    return { ...state, isAuthenticated: !!isAuthenticated, isInitialized: true, user: user || null };
  },
  LOGIN_START: (state) => {
    return { ...state, isAuthenticated: false, user: null, loading: true, error: null };
  },
  LOGIN_SUCCESS: (state, action) => {
    return { ...state, isAuthenticated: true, user: action.payload!.user!, loading: false, error: null };
  },
  LOGIN_ERROR: (state, action) => {
    return { ...state, isAuthenticated: false, user: null, loading: false, error: action.payload!.error! };
  },
  LOGOUT: (state) => ({
    ...state,
    isAuthenticated: false,
    loading: false,
    error: null,
    user: null,
  }),
};

const rootReducer: Reducer<AuthContextState, AuthAction> = (state, action) =>
  handlers[action.type] ? handlers[action.type](state, action) : state;

const AuthContext = createContext(initialState);

export type AuthProviderProps = {
  children: ReactNode;
};

function AuthProvider({ children }: AuthProviderProps) {
  const [state, dispatch] = useReducer(rootReducer, initialState);
  const [parentMode, setParentModeState] = useState<boolean>(initialState.parentMode);
  const [loginMode, setLoginModeState] = useState<boolean>(initialState.loginMode);

  const setParentMode = useCallback((mode: boolean) => {
    localStorage.setItem('parentMode', mode.toString());
    setParentModeState(mode);
  }, []);

  const setLoginMode = useCallback((mode: boolean) => {
    localStorage.setItem('loginMode', mode.toString());
    setLoginModeState(mode);
  }, []);

  const login = useCallback(async (request: LoginRequest) => {
    try {
      dispatch({ type: 'LOGIN_START' });
      const response = await api.Auth.Login(request);
      if (response.status === StatusCodes.Ok) {
        const loginResponse = response.data;

        if (loginResponse.status === 'Success' && loginResponse.accessToken && loginResponse.account) {
          TokenStorage.setAccessToken(loginResponse.accessToken);
          dispatch({ type: 'LOGIN_SUCCESS', payload: { user: loginResponse.account } });
        } else {
          TokenStorage.clearAccessToken();
          dispatch({ type: 'LOGIN_ERROR', payload: { error: loginResponse.status } });
        }
      } else {
        TokenStorage.clearAccessToken();
        dispatch({ type: 'LOGIN_ERROR', payload: { error: 'Unexpected error in login' } });
      }
    } catch (e) {
      TokenStorage.clearAccessToken();
      dispatch({ type: 'LOGIN_ERROR', payload: { error: 'Something went wrong in login' } });
    }
  }, []);

  const loginNew = useCallback(async (request: LoginRequestNew) => {
    try {
      dispatch({ type: 'LOGIN_START' });
      const response = await api.Auth.LoginNew(request);
      if (response.status === StatusCodes.Ok) {
        const loginResponse = response.data;

        if (loginResponse.status === 'Success' && loginResponse.accessToken && loginResponse.account) {
          TokenStorage.setAccessToken(loginResponse.accessToken);
          dispatch({ type: 'LOGIN_SUCCESS', payload: { user: loginResponse.account } });
        } else {
          TokenStorage.clearAccessToken();
          dispatch({ type: 'LOGIN_ERROR', payload: { error: loginResponse.status } });
        }
      } else {
        TokenStorage.clearAccessToken();
        dispatch({ type: 'LOGIN_ERROR', payload: { error: 'Unexpected error in login new' } });
      }
    } catch (e) {
      TokenStorage.clearAccessToken();
      dispatch({ type: 'LOGIN_ERROR', payload: { error: 'Something went wrong in login new' } });
    }
  }, []);

  const logout = useCallback(() => {
    TokenStorage.clearAccessToken();
    dispatch({ type: 'LOGOUT' });
  }, []);

  const authMemoizedState = useMemo(
    () => ({
      ...state,
      login,
      loginNew,
      logout,
      parentMode,
      setParentMode,
      setLoginMode,
      loginMode,
    }),
    [login, logout, loginNew, state, parentMode, setParentMode, setLoginMode, loginMode]
  );

  useEffect(() => {
    if (authMemoizedState.parentMode) {
      setParentMode(true);
    }
    if (authMemoizedState.loginMode) {
      setLoginMode(true);
    }
    const initialize = async () => {
      try {
        const activeAccount: Account | null = await GetActiveAccountFromToken();
        const isAuthenticated = activeAccount !== null;
        if (isAuthenticated) {
          dispatch({
            type: 'INITIALIZE',
            payload: { isAuthenticated, user: activeAccount, loading: false, error: null },
          });
        } else {
          TokenStorage.clearAccessToken();
          dispatch({
            type: 'INITIALIZE',
            payload: { isAuthenticated, user: null, loading: true, error: null },
          });
        }
      } catch (err) {
        console.error(err);
        dispatch({
          type: 'INITIALIZE',
          payload: { isAuthenticated: false, user: null },
        });
      }
    };

    initialize();
  }, [authMemoizedState.parentMode, setParentMode, authMemoizedState.loginMode, setLoginMode]);
  return <AuthContext.Provider value={authMemoizedState}>{children}</AuthContext.Provider>;
}

export { AuthContext, AuthProvider };
