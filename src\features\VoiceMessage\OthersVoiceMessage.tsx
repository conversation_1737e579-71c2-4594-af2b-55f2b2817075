import React from 'react';
import Page from '@/components/shared/Page';
import { TextField, Box, Typography, Stack, Button, Card, Divider, Autocomplete } from '@mui/material';
import styled from 'styled-components';
import Popup from '@/components/shared/Popup/Popup';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import EmojiIcon from '@/assets/voice/emoji-message.svg';
import { STATUS_SELECT } from '@/config/Selection';

const OthersVoiceMessageRoot = styled.div`
  padding: 1rem;

  li {
    padding-bottom: 5px;
  }
  .sub-heading {
    font-size: 90%;
    color: ${(props) => props.theme.palette.grey[600]};
  }
  .Card {
    min-height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }
  }
  .MuiIconButton-root:hover {
    background-color: transparent;
  }
`;

function OthersVoiceMessage() {
  const [popup, setPopup] = React.useState(false);
  const handleClickOpen = () => {
    setPopup(true);
  };
  const handleClickClose = () => {
    setPopup(false);
  };

  return (
    <Page title="Voice Message To Others">
      <OthersVoiceMessageRoot>
        <Card className="Card" elevation={5} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Typography variant="h6" fontSize={17}>
            Voice Message To Others
          </Typography>
          <Divider />
          <Stack pb={4} pt={2}>
            <Typography variant="h6" fontSize={14}>
              Select Voice
            </Typography>
            <Autocomplete
              sx={{ pb: 3 }}
              options={STATUS_SELECT}
              renderInput={(params) => <TextField {...params} placeholder="Select" />}
            />
            <Typography variant="h6" fontSize={14}>
              Phone Number
            </Typography>
            <TextField placeholder="Enter Phone Number" />
          </Stack>
          <Box display="flex" sx={{ justifyContent: 'center', pt: 3 }}>
            <Stack>
              <img src={EmojiIcon} alt="" style={{ maxHeight: 200 }} />
              <Typography>You can sent voice message to others</Typography>
            </Stack>
          </Box>
          <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 3 }}>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="secondary">
                Cancel
              </Button>
              <Button onClick={handleClickOpen} variant="contained" color="primary">
                Send
              </Button>
            </Stack>
          </Box>
        </Card>
      </OthersVoiceMessageRoot>
      {/* <TemporaryDrawer onclickdrwer={onclick} open="right" /> */}
      <Popup
        size="xs"
        state={popup}
        onClose={handleClickClose}
        popupContent={<SuccessMessage message="Message Sent Successfully" />}
      />
    </Page>
  );
}

export default OthersVoiceMessage;
