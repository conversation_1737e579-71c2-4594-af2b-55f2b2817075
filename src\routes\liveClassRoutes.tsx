import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import Loadable from '@/components/shared/Loadable';

const LiveClass = Loadable(lazy(() => import('@/pages/LiveClass')));
const ScheduleList = Loadable(lazy(() => import('@/features/LiveClass/ScheduleList/ScheduleList')));
const LiveClassList = Loadable(lazy(() => import('@/features/LiveClass/LiveClassList/LiveClassList')));
const ZoomKeyList = Loadable(lazy(() => import('@/features/LiveClass/ZoomKeyList/ZoomKeyList')));

export const liveClassRoutes: RouteObject[] = [
  {
    path: '/live-class',
    element: <LiveClass />,
    children: [
      {
        path: 'schedule-list',
        element: <ScheduleList />,
      },
      {
        path: 'live-class-list',
        element: <LiveClassList />,
      },
      {
        path: 'zoom-key-list',
        element: <ZoomKeyList />,
      },
    ],
  },
];
