/* eslint-disable prettier/prettier */
import React, { useState } from 'react';
import { Box, Typography, TextField, Button, Stack } from '@mui/material';

const EditStudent = ({ student, onCancel, onUpdate }: any) => {
  const [editedStudent, setEditedStudent] = useState(student);

  const handleChange = (e: any) => {
    const { name, value } = e.target;
    setEditedStudent((prevStudent: any) => ({
      ...prevStudent,
      [name]: value,
    }));
  };

  const handleUpdate = () => {
    onUpdate(editedStudent.id, editedStudent);
  };

  return (
    <Box mt={3}>
      <Stack spacing={2} sx={{ height: 'calc(100vh - 150px)' }}>
        <Typography variant="h6" mb={5}>
          Edit Student Class
        </Typography>

        <TextField
          name="rollNo"
          label="Roll No"
          variant="outlined"
          value={editedStudent.rollNo}
          onChange={handleChange}
          fullWidth
        />

        <TextField
          name="admissionNo"
          label="Adm No"
          variant="outlined"
          value={editedStudent.admissionNo}
          onChange={handleChange}
          fullWidth
        />

        <TextField
          name="name"
          label="Student Name"
          variant="outlined"
          value={editedStudent.name}
          onChange={handleChange}
          fullWidth
        />

        <TextField
          name="className"
          label="Class"
          variant="outlined"
          value= "VIII-A"
          onChange={handleChange}
          fullWidth
        />

        <TextField
          name="academic"
          label="Academic"
          variant="outlined"
          value={editedStudent.academic}
          onChange={handleChange}
          fullWidth
        />
      </Stack>
      <Box sx={{ justifyContent: 'center', pt: 3, position: 'relative' }}>
        <Stack spacing={2} direction="row" sx={{}}>
          <Button onClick={onCancel} fullWidth variant="contained" color="secondary">
            Cancel
          </Button>
          <Button
            onClick={handleUpdate}
            // onKeyDown={toggleDrawer(false)}
            fullWidth
            variant="contained"
            color="primary"
          >
            Update
          </Button>
        </Stack>
      </Box>
    </Box>
  );
};

export default EditStudent;
