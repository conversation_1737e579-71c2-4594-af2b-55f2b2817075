import { STATUS_OPTIONS } from '@/config/Selection';
import { ClassListInfo } from '@/types/AcademicManagement';
import LoadingButton from '@mui/lab/LoadingButton/LoadingButton';
import { Button, TextField, Typography, Box, Stack, MenuItem, Select, FormControl } from '@mui/material';
import { useFormik } from 'formik';
import * as Yup from 'yup';

export type CreateEditClassFormProps = {
  onSave: (values: ClassListInfo, mode: 'create' | 'edit') => void;
  onCancel: (mode: 'create' | 'edit') => void;
  onClose: (mode: 'create' | 'edit') => void;
  classDetail: ClassListInfo;
  isSubmitting: boolean;
};

const CreateEditClassValidationSchema = Yup.object({
  className: Yup.string().required('Please enter Class Name'),
  classDescription: Yup.string().required('Please enter Class description'),
  classStatus: Yup.number().oneOf([0, 1], 'Please select Class Status'),
});

function CreateEditClassForm({ classDetail, onSave, onCancel, isSubmitting }: CreateEditClassFormProps) {
  const mode = classDetail.classId === 0 ? 'create' : 'edit';
  const {
    values: { className, classDescription, classStatus },
    handleChange,
    handleSubmit,
    touched,
    errors,
  } = useFormik<ClassListInfo>({
    initialValues: {
      classId: classDetail.classId,
      className: classDetail.className,
      classDescription: classDetail.classDescription,
      classStatus: classDetail.classStatus,
    },
    validationSchema: CreateEditClassValidationSchema,
    onSubmit: (values) => {
      onSave(values, mode);
    },
  });

  return (
    <Box mt={3}>
      <form noValidate onSubmit={handleSubmit}>
        <Stack sx={{ height: 'calc(100vh - 150px)' }} direction="column">
          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Class Name
            </Typography>
            <TextField
              placeholder="Class Name"
              name="className"
              value={className}
              onChange={handleChange}
              error={touched.className && !!errors.className}
              helperText={errors.className}
              disabled={isSubmitting}
            />
          </FormControl>
          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Description
            </Typography>
            <TextField
              multiline
              fullWidth
              minRows={2}
              InputProps={{ inputProps: { style: { resize: 'vertical' } } }}
              placeholder="Enter description..."
              name="classDescription"
              value={classDescription}
              onChange={handleChange}
              error={touched.classDescription && !!errors.classDescription}
              helperText={errors.classDescription}
              disabled={isSubmitting}
            />
          </FormControl>
          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Status
            </Typography>
            <Select
              name="classStatus"
              value={classStatus}
              onChange={handleChange}
              error={touched.classStatus && !!errors.classStatus}
              disabled={isSubmitting}
            >
              {STATUS_OPTIONS.map((opt) => (
                <MenuItem key={opt.id} value={opt.id}>
                  {opt.name}
                </MenuItem>
              ))}
            </Select>
            {touched.classStatus && !!errors.classStatus && (
              <Typography color="red" fontSize="0.625rem" variant="subtitle1">
                {errors.classStatus}
              </Typography>
            )}
          </FormControl>
        </Stack>
        <Box sx={{ justifyContent: 'center', pt: 3, position: 'relative' }}>
          <Stack spacing={2} direction="row" sx={{}}>
            <Button onClick={() => onCancel(mode)} fullWidth variant="contained" color="secondary" type="button">
              Cancel
            </Button>
            <LoadingButton
              loading={isSubmitting}
              fullWidth
              variant="contained"
              color="primary"
              type="submit"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Saving...' : 'Save'}
            </LoadingButton>
          </Stack>
        </Box>
      </form>
    </Box>
  );
}

export default CreateEditClassForm;
