/* eslint-disable jsx-a11y/alt-text */
import React, { useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  useTheme,
  IconButton,
  Collapse,
  Tooltip,
  FormControl,
  Chip,
  Avatar,
  Popover,
} from '@mui/material';
import styled from 'styled-components';
import { CLASS_SELECT, STATUS_SELECT, YEAR_SELECT } from '@/config/Selection';
import SearchIcon from '@mui/icons-material/Search';

import useSettings from '@/hooks/useSettings';
import { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { BiLogoAndroid, BiLogoApple } from 'react-icons/bi';
import { RiComputerFill } from 'react-icons/ri';

export const data = [
  {
    studentName: '<PERSON>',
    class: 'VII-B',
    admission: '34765',
    academicYear: '2023-2024',
    device: 'Android',
    image:
      'https://images.unsplash.com/photo-1633332755192-727a05c4013d?q=80&w=1780&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    studentName: 'Peter Jack',
    class: 'VII-B',
    admission: '34765',
    academicYear: '2023-2024',
    device: 'IOS',
  },
  {
    studentName: 'Peter Jack',
    class: 'VII-B',
    admission: '34765',
    academicYear: '2023-2024',
    device: 'Computer & Laptop',
  },
  {
    studentName: 'Peter Jack',
    class: 'VII-B',
    admission: '34765',
    academicYear: '2023-2024',
    device: 'Computer & Laptop',
  },
  {
    studentName: 'Peter Jack',
    class: 'VII-B',
    admission: '34765',
    academicYear: '2023-2024',
    device: 'IOS',
  },
  {
    studentName: 'Peter Jack',
    class: 'VII-B',
    admission: '34765',
    academicYear: '2023-2024',
    device: 'Android',
  },
  {
    studentName: 'Peter Jack',
    class: 'VII-B',
    admission: '34765',
    academicYear: '2023-2024',
    device: 'Computer & Laptop',
  },
  {
    studentName: 'Peter Jack',
    class: 'VII-B',
    admission: '34765',
    academicYear: '2023-2024',
    device: 'IOS',
  },
  {
    studentName: 'Peter Jack',
    class: 'VII-B',
    admission: '34765',
    academicYear: '2023-2024',
    device: 'Computer & Laptop',
  },
  {
    studentName: 'Peter Jack',
    class: 'VII-B',
    admission: '34765',
    academicYear: '2023-2024',
    device: 'Computer & Laptop',
  },
  {
    studentName: 'Peter Jack',
    class: 'VII-B',
    admission: '34765',
    academicYear: '2023-2024',
    device: 'Android',
  },
  {
    studentName: 'Peter Jack',
    class: 'VII-B',
    admission: '34765',
    academicYear: '2023-2024',
    device: 'Computer & Laptop',
  },
];

const StudentsDeviceDetailsRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;
  .Card {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    height: calc(100vh - 100px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 40px);
      flex-grow: 1;

      .card-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: auto;
        ::-webkit-scrollbar {
          width: 0px;
        }
      }
    }
  }
`;

function StudentsDeviceDetails() {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const [showFilter, setShowFilter] = useState(false);

  const [selectedChips, setSelectedChips] = useState(
    data.map((student) => {
      if (student.device === 'Android') {
        return 0;
      } else if (student.device === 'IOS') {
        return 1;
      } else {
        return 2;
      }
    })
  );
  const handleChipClick = (cardIndex, chipIndex) => {
    setSelectedChips((prevSelectedChips) => {
      const updatedSelectedChips = [...prevSelectedChips];
      updatedSelectedChips[cardIndex] = chipIndex;
      return updatedSelectedChips;
    });
  };
  const [anchorEl, setAnchorEl] = useState(null);

  const handlePopoverOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handlePopoverClose = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);
  const id = open ? 'avatar-popover' : undefined;

  const StudentsDeviceDetailsColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'studentName',
        headerLabel: 'Student Name',
        dataKey: 'studentName',
      },

      {
        name: 'class',
        dataKey: 'class',
        headerLabel: 'Class',
      },
      {
        name: 'admission',
        dataKey: 'admission',
        headerLabel: 'Admission',
      },
      {
        name: 'academicYear',
        dataKey: 'academicYear',
        headerLabel: 'Academic',
      },
    ],
    []
  );

  return (
    <Page title="Student Device Details">
      <StudentsDeviceDetailsRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17} width="100%">
              Student Device Details
            </Typography>
            <Box pb={1} sx={{ flexShrink: 0 }}>
              <Tooltip title="Search">
                <IconButton aria-label="" color="primary" sx={{ mr: 1 }} onClick={() => setShowFilter((x) => !x)}>
                  <SearchIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Stack>
          <Stack>
            <Divider />
          </Stack>
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={5} pt={1} container spacing={2}>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Autocomplete
                        options={YEAR_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select year" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Autocomplete
                        options={CLASS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select class" />}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item lg={1} xs={12}>
                    <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                      <Button variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                      {/* <Button variant="contained" color="primary" fullWidth>
                          Search
                        </Button> */}
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Box className="card-container" mt={2}>
              <Grid container spacing={2}>
                {data.map((student, cardIndex) => (
                  <Grid item xl={4} lg={6} md={12} sm={6} xs={12}>
                    <Card
                      className="student_card"
                      sx={{ border: `1px solid ${theme.palette.grey[300]}`, p: 2, boxShadow: 0 }}
                    >
                      <Avatar src={student.image} onMouseEnter={handlePopoverOpen} onMouseLeave={handlePopoverClose} />
                      <Popover
                        id={id}
                        open={open}
                        anchorEl={anchorEl}
                        onClose={handlePopoverClose}
                        anchorOrigin={{
                          vertical: 'bottom',
                          horizontal: 'center',
                        }}
                        transformOrigin={{
                          vertical: 'top',
                          horizontal: 'left',
                        }}
                      >
                        <img width={100} src={student.image} alt="Avatar" />
                      </Popover>
                      {StudentsDeviceDetailsColumns.map((item) => (
                        <Grid container>
                          <Grid item lg={6} xs={6}>
                            <Typography key={item.name} variant="subtitle1" color="GrayText" fontSize={13} mr={2}>
                              {item.headerLabel}
                            </Typography>
                          </Grid>
                          <Grid item lg={5} xs={6} mb={0} mt="auto">
                            <Typography
                              variant="h6"
                              fontSize={13}
                              color={item.dataKey === 'meetingLink' ? theme.palette.info.main : ''}
                            >
                              : {(student as { [key: string]: string })[item.dataKey ?? '']}
                            </Typography>
                          </Grid>
                        </Grid>
                      ))}
                      <Typography my={2} variant="subtitle2">
                        Device details
                      </Typography>
                      <Stack direction="row" justifyContent="space-between">
                        <Chip
                          clickable
                          color={selectedChips[cardIndex] === 0 ? 'primary' : 'default'}
                          size="small"
                          variant={selectedChips[cardIndex] === 0 ? 'filled' : 'outlined'}
                          label="Android"
                          icon={<BiLogoAndroid />}
                          onClick={() => handleChipClick(cardIndex, 0)}
                        />
                        <Chip
                          clickable
                          color={selectedChips[cardIndex] === 1 ? 'info' : 'default'}
                          size="small"
                          variant={selectedChips[cardIndex] === 1 ? 'filled' : 'outlined'}
                          label="IOS"
                          icon={<BiLogoApple />}
                          onClick={() => handleChipClick(cardIndex, 1)}
                        />
                        <Chip
                          clickable
                          color={selectedChips[cardIndex] === 2 ? 'warning' : 'default'}
                          size="small"
                          variant={selectedChips[cardIndex] === 2 ? 'filled' : 'outlined'}
                          label="Computer & Laptop"
                          icon={<RiComputerFill />}
                          onClick={() => handleChipClick(cardIndex, 2)}
                        />
                      </Stack>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Box>
            <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 3 }}>
              <Stack spacing={2} direction="row">
                <Button variant="contained" color="primary">
                  Update
                </Button>
              </Stack>
            </Box>
          </div>
        </Card>
      </StudentsDeviceDetailsRoot>
    </Page>
  );
}

export default StudentsDeviceDetails;
