import { ClassListInfo } from './AcademicManagement';
import { FetchStatus } from './Common';

export type DashboardStatsType = {
  count: number;
  total?: number;
  name: string;
};
export type DashboardEventsType = {
  eventId: number;
  eventTitle: string;
  eventDescription: string;
  eventThumbNail: string;
  eventDate: string;
  eventType: number;
  eventLinkFile: string;
};
export type DashboardTimeTableType = {
  ttableId: number;
  classId: number;
  dayId: number;
  periodId: number;
  subjectId: number;
  staffId: number;
  accademicId: number;
  ttableStatus: number;
  periodName: number;
  className: string;
  subjectName: string;
  subjectIcon: string;
  accademicTime: string;
  staffName: string;
};
export type DashboardBdayType = {
  studentId: number;
  studentName: string;
  studentImage: string;
  studentDob: string;
  studentAge: number;
  className: string;
  studentRollNo: number;
};
export type DashboardFeeChartType = {
  monthName: string;
  totalFeeCollected: number;
};
export type DashboardAttendanceType = {
  totalStudents: number;
  absentStudents: number;
  nottakenStudents: number;
  presentStudents: number;
};
export type DashboardVideosType = {
  youtubeId: number;
  classId: number;
  subjectId: number;
  youtubeTitle: string;
  youtubeDescription: string;
  youtubeLink: string;
  createdDate: string;
  originalFile: string;
};
export type YearSelectItem = {
  id: string;
  option: string;
};
export type YearDataType = {
  accademicId: number;
  accademicTime: string;
  accademicStatus: number;
};

export type DashboardState = {
  dashboardStats: {
    status: FetchStatus;
    data: DashboardStatsType[];
    error: string | null;
  };
  dashboardEvents: {
    status: FetchStatus;
    data: DashboardEventsType[];
    error: string | null;
  };
  dashboardTimetable: {
    status: FetchStatus;
    data: DashboardTimeTableType[];
    error: string | null;
  };
  dashboardBday: {
    status: FetchStatus;
    data: DashboardBdayType[];
    error: string | null;
  };
  dashboardFeeChart: {
    status: FetchStatus;
    data: DashboardFeeChartType[];
    error: string | null;
  };
  dashboardAttendance: {
    status: FetchStatus;
    data: DashboardAttendanceType[];
    error: string | null;
  };
  dashboardVideos: {
    status: FetchStatus;
    data: DashboardVideosType[];
    error: string | null;
  };
  classList: {
    status: FetchStatus;
    data: ClassListInfo[];
    error: string | null;
  };
  yearList: {
    status: FetchStatus;
    data: YearDataType[];
    error: string | null;
  };
  submitting: boolean;
};
