/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import {
  Autocomplete,
  Grid,
  Stack,
  TextField,
  Button,
  Typography,
  Box,
  FormControl,
  Select,
  MenuItem,
} from '@mui/material';
import styled from 'styled-components';
import { CLASS_SELECT, STATUS_OPTIONS, SUBJECT_SELECT, YEAR_SELECT } from '@/config/Selection';
import SelectDateTimePicker from '@/components/shared/Selections/TimePicker';
import InputFileUpload from '@/components/shared/Selections/FilesUpload';

const MapOGFileRoot = styled.div`
  width: 100%;
  padding: 1.5rem;
`;

function MapOGFile({ onClose }: any) {
  return (
    <MapOGFileRoot>
      <form noValidate>
        <Grid container spacing={4}>
          {/* -------From-------- */}
          <Grid item lg={6} md={6} sm={6}>
            <Grid container spacing={2}>
              <Grid item lg={10} xs={12}>
                <FormControl fullWidth>
                  <Typography variant="h6" fontSize={14}>
                    Academic Year From
                  </Typography>
                  <Autocomplete
                    options={YEAR_SELECT}
                    renderInput={(params) => <TextField {...params} placeholder="Select Year" />}
                  />
                </FormControl>
              </Grid>
              <Grid item lg={10} xs={12}>
                <FormControl fullWidth>
                  <Typography variant="h6" fontSize={14}>
                    Class From
                  </Typography>
                  <Autocomplete
                    options={CLASS_SELECT}
                    renderInput={(params) => <TextField {...params} placeholder="Select class" />}
                  />
                </FormControl>
              </Grid>

              <Grid item lg={10} xs={12}>
                <FormControl fullWidth>
                  <Typography variant="h6" fontSize={14}>
                    Subject From
                  </Typography>
                  <Autocomplete
                    options={SUBJECT_SELECT}
                    renderInput={(params) => <TextField {...params} placeholder="Select class" />}
                  />
                </FormControl>
              </Grid>
              <Grid item lg={10} xs={12}>
                <FormControl fullWidth>
                  <Typography variant="h6" fontSize={14}>
                    Chapter From
                  </Typography>
                  <Autocomplete
                    options={SUBJECT_SELECT}
                    renderInput={(params) => <TextField {...params} placeholder="Select class" />}
                  />
                </FormControl>
              </Grid>

              <Grid item lg={10} xs={12}>
                <FormControl fullWidth>
                  <Typography variant="h6" fontSize={14}>
                    Online Class From
                  </Typography>
                  <Autocomplete
                    options={SUBJECT_SELECT}
                    renderInput={(params) => <TextField {...params} placeholder="Select class" />}
                  />
                </FormControl>
              </Grid>
            </Grid>
          </Grid>
          {/* -------To-------- */}
          <Grid item lg={6} md={6} sm={6}>
            <Grid container spacing={2}>
              <Grid item lg={10} xs={12}>
                <FormControl fullWidth>
                  <Typography variant="h6" fontSize={14}>
                    Academic Year To
                  </Typography>
                  <Autocomplete
                    options={YEAR_SELECT}
                    renderInput={(params) => <TextField {...params} placeholder="Select Year" />}
                  />
                </FormControl>
              </Grid>
              <Grid item lg={10} xs={12}>
                <FormControl fullWidth>
                  <Typography variant="h6" fontSize={14}>
                    Class To
                  </Typography>
                  <Autocomplete
                    options={CLASS_SELECT}
                    renderInput={(params) => <TextField {...params} placeholder="Select class" />}
                  />
                </FormControl>
              </Grid>
              <Grid item lg={10} xs={12}>
                <FormControl fullWidth>
                  <Typography variant="h6" fontSize={14}>
                    Subject To
                  </Typography>
                  <Autocomplete
                    options={SUBJECT_SELECT}
                    renderInput={(params) => <TextField {...params} placeholder="Select class" />}
                  />
                </FormControl>
              </Grid>
              <Grid item lg={10} xs={12}>
                <FormControl fullWidth>
                  <Typography variant="h6" fontSize={14}>
                    Chapter To
                  </Typography>
                  <Autocomplete
                    options={SUBJECT_SELECT}
                    renderInput={(params) => <TextField {...params} placeholder="Select class" />}
                  />
                </FormControl>
              </Grid>
              <Grid item lg={10} xs={12}>
                <FormControl fullWidth>
                  <Typography variant="h6" fontSize={14}>
                    Online Class To
                  </Typography>
                  <Autocomplete
                    options={SUBJECT_SELECT}
                    renderInput={(params) => <TextField {...params} placeholder="Select class" />}
                  />
                </FormControl>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
        <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 3 }}>
          <Stack spacing={2} direction="row">
            <Button variant="contained" onClick={onClose} color="secondary">
              Cancel
            </Button>
            <Button variant="contained" color="primary">
              Save
            </Button>
          </Stack>
        </Box>
      </form>
    </MapOGFileRoot>
  );
}

export default MapOGFile;
