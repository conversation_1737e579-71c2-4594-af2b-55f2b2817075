import english from '@/assets/timetable/English.svg';
import mathematics from '@/assets/timetable/Math.svg';
import physics from '@/assets/timetable/Physics.svg';
import biology from '@/assets/timetable/Biology.svg';
import social from '@/assets/timetable/Social.svg';
import arabic from '@/assets/timetable/Arabic.svg';
import chemistry from '@/assets/timetable/Chemistry.svg';
import computer from '@/assets/timetable/Computer.svg';
import malayalam from '@/assets/timetable/Malayalam.svg';
import hindi from '@/assets/timetable/Hindi.svg';
import economics from '@/assets/timetable/Economics.svg';
import accountancy from '@/assets/timetable/Accountancy.svg';
import thamil from '@/assets/timetable/Thamil.svg';
import business from '@/assets/timetable/Business.svg';
import general from '@/assets/timetable/General.svg';
import marathi from '@/assets/timetable/Marathi.svg';

export const TimetableIconMap: any = {
  English: english,
  Maths: mathematics,
  Physics: physics,
  Biology: biology,
  Social: social,
  Arabic: arabic,
  Chemistry: chemistry,
  Computer: computer,
  Malayalam: malayalam,
  Hindi: hindi,
  Economics: economics,
  Accountancy: accountancy,
  Thamil: thamil,
  Business: business,
  General: general,
  Marathi: marathi,
};


export const capitalizeIcon = (icon: string) => {
  const map: Record<string, string> = {
    math: 'Maths',
    english: 'English',
    malayalam: 'Malayalam',
    arabic: 'Arabic',
    hindi: 'Hindi',
    biology: 'Biology',
    physics: 'Physics',
    social: 'Social',
    chemistry: 'Chemistry',
    computer: 'Computer',
    economics: 'Economics',
    accountancy: 'Accountancy',
    thamil: 'Thamil',
    business: 'Business',
    general: 'General',
    marathi: 'Marathi',
  };
  return map[icon.toLowerCase()] || 'General'; // fallback if icon not found
};
