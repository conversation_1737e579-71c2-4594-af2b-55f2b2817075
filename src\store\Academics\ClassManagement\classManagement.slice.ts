import { PayloadAction, createSlice } from '@reduxjs/toolkit';
import { flushStore } from '@/store/flush.slice';
import { OutletContextType } from '@/types/Common';
import type { ClassManagementState } from '@/types/AcademicManagement';
import {
  fetchClassList,
  addNewClass,
  updateClass,
  deleteClass,
  addNewClassMulti,
  fetchClassLists,
  fetchClassSortList,
  updateClassSort,
} from './classManagement.thunks';

const initialState: ClassManagementState = {
  classList: {
    status: 'idle',
    data: [],
    error: null,
    pageInfo: {
      totalrecords: 0,
      pagesize: 20,
      pagenumber: 1,
      totalpages: 0,
      remainingpages: 0,
    },
    sortColumn: 'classId',
    sortDirection: 'asc',
  },
  classSortList: {
    status: 'idle',
    data: [],
    error: null,
    pageInfo: {
      totalrecords: 0,
      pagesize: 20,
      pagenumber: 1,
      totalpages: 0,
      remainingpages: 0,
    },
    sortColumn: 'classId',
    sortDirection: 'asc',
  },
  submitting: false,
  deletingRecords: {},
  error: null,
};

export const classManagementSlice = createSlice({
  name: 'classManagement',
  initialState,
  reducers: {
    setClassListPage: (state, action: PayloadAction<number>) => {
      state.classList.pageInfo.pagenumber = action.payload;
    },
    setClassListPageSize: (state, action: PayloadAction<number>) => {
      state.classList.pageInfo.pagenumber = 1;
      state.classList.pageInfo.pagesize = action.payload;
    },
    setSortColumn: (state, action: PayloadAction<string>) => {
      state.classList.pageInfo.pagenumber = 1;
      state.classList.sortColumn = action.payload;
      state.classList.sortDirection = 'asc';
    },
    setSortDirection: (state, action: PayloadAction<'asc' | 'desc'>) => {
      state.classList.pageInfo.pagenumber = 1;
      state.classList.sortDirection = action.payload;
    },
    setSortColumnAndDirection: (state, action: PayloadAction<{ column: string; direction: 'asc' | 'desc' }>) => {
      state.classList.pageInfo.pagenumber = 1;
      state.classList.sortColumn = action.payload.column;
      state.classList.sortDirection = action.payload.direction;
    },
    setSubmitting: (state, action: PayloadAction<boolean>) => {
      state.submitting = action.payload;
    },
    setDeletingRecords: (state, action: PayloadAction<{ recordId: number }>) => {
      state.deletingRecords[action.payload.recordId] = true;
    },
    clearDeletingRecords: (state, action: PayloadAction<{ recordId: number }>) => {
      delete state.deletingRecords[action.payload.recordId];
    },
    setClassSortListPage: (state, action: PayloadAction<number>) => {
      state.classSortList.pageInfo.pagenumber = action.payload;
    },
    setClassSortListPageSize: (state, action: PayloadAction<number>) => {
      state.classSortList.pageInfo.pagenumber = 1;
      state.classSortList.pageInfo.pagesize = action.payload;
    },
    setSortClassSortColumn: (state, action: PayloadAction<string>) => {
      state.classSortList.pageInfo.pagenumber = 1;
      state.classSortList.sortColumn = action.payload;
      state.classSortList.sortDirection = 'asc';
    },
    setSortClassSortDirection: (state, action: PayloadAction<'asc' | 'desc'>) => {
      state.classSortList.pageInfo.pagenumber = 1;
      state.classSortList.sortDirection = action.payload;
    },
    setSortClassSortColumnAndDirection: (
      state,
      action: PayloadAction<{ column: string; direction: 'asc' | 'desc' }>
    ) => {
      state.classSortList.pageInfo.pagenumber = 1;
      state.classSortList.sortColumn = action.payload.column;
      state.classSortList.sortDirection = action.payload.direction;
    },
  },
  extraReducers(builder) {
    builder
      .addCase(fetchClassLists.pending, (state) => {
        state.classList.status = 'loading';
        state.classList.error = null;
      })
      .addCase(fetchClassLists.fulfilled, (state, action) => {
        const data = action.payload;
        state.classList.status = 'success';
        state.classList.data = data;
        state.classList.error = null;
        // state.classList.pageInfo = pageInfo;
      })
      .addCase(fetchClassLists.rejected, (state, action) => {
        state.classList.status = 'error';
        state.classList.error = action.payload || 'Unknown error in fetching class list';
      })

      .addCase(fetchClassList.pending, (state) => {
        state.classList.status = 'loading';
        state.classList.error = null;
      })
      .addCase(fetchClassList.fulfilled, (state, action) => {
        const { data, ...pageInfo } = action.payload;
        state.classList.status = 'success';
        state.classList.data = data;
        state.classList.error = null;
        state.classList.pageInfo = pageInfo;
      })
      .addCase(fetchClassList.rejected, (state, action) => {
        state.classList.status = 'error';
        state.classList.error = action.payload || 'Unknown error in fetching class list';
      })

      .addCase(addNewClass.pending, (state) => {
        state.submitting = true;
        state.error = null;
      })
      .addCase(addNewClass.fulfilled, (state) => {
        state.submitting = false;
        state.error = null;
      })
      .addCase(addNewClass.rejected, (state, action) => {
        state.submitting = false;
        state.error = action.error.message;
      })

      .addCase(addNewClassMulti.pending, (state) => {
        state.submitting = true;
        state.error = null;
      })
      .addCase(addNewClassMulti.fulfilled, (state) => {
        state.submitting = false;
        state.error = null;
      })
      .addCase(addNewClassMulti.rejected, (state, action) => {
        state.submitting = false;
        state.error = action.error.message;
      })

      .addCase(updateClass.pending, (state) => {
        state.submitting = true;
        state.error = null;
      })
      .addCase(updateClass.fulfilled, (state, action) => {
        state.submitting = false;
        state.error = null;
        const dataIndex = state.classList.data.findIndex((x) => x.classId === action.meta.arg.classId);
        if (dataIndex > -1) {
          state.classList.data[dataIndex] = action.meta.arg;
        }
      })
      .addCase(updateClass.rejected, (state, action) => {
        state.submitting = false;
        state.error = action.error.message;
      })

      .addCase(deleteClass.pending, (state, action) => {
        state.deletingRecords[action.meta.arg] = true;
        state.error = null;
      })
      .addCase(deleteClass.fulfilled, (state, action) => {
        if (action.payload.deleted) {
          const { [action.meta.arg]: deletedRecord, ...restDeletingRecords } = state.deletingRecords;
          state.deletingRecords = restDeletingRecords;
        }
        state.error = null;
      })
      .addCase(deleteClass.rejected, (state, action) => {
        state.deletingRecords = { ...initialState.deletingRecords };
        state.error = action.error.message;
      })
      .addCase(fetchClassSortList.pending, (state) => {
        state.classSortList.status = 'loading';
        state.classSortList.error = null;
      })
      .addCase(fetchClassSortList.fulfilled, (state, action) => {
        const { data, ...pageInfo } = action.payload;
        state.classSortList.status = 'success';
        state.classSortList.data = data;
        state.classSortList.error = null;
        state.classSortList.pageInfo = pageInfo;
      })
      .addCase(fetchClassSortList.rejected, (state, action) => {
        state.classSortList.status = 'error';
        state.classSortList.error = action.payload || 'Unknown error in fetching class list';
      })

      .addCase(updateClassSort.pending, (state) => {
        state.submitting = true;
        state.error = null;
      })
      .addCase(updateClassSort.fulfilled, (state, action) => {
        state.submitting = false;
        state.error = null;
        const dataIndex = state.classSortList.data.findIndex((x) => x.classId === action.meta.arg.classId);
        if (dataIndex > -1) {
          state.classSortList.data[dataIndex] = action.meta.arg;
        }
      })
      .addCase(updateClassSort.rejected, (state, action) => {
        state.submitting = false;
        state.error = action.error.message;
      })
      .addCase(flushStore, () => initialState);
  },
});

export const {
  setClassListPage,
  setClassListPageSize,
  setSortColumn,
  setSortDirection,
  setSortColumnAndDirection,
  setSubmitting,
  setDeletingRecords,
  clearDeletingRecords,
  setClassSortListPage,
  setClassSortListPageSize,
  setSortClassSortColumn,
  setSortClassSortDirection,
  setSortClassSortColumnAndDirection,
} = classManagementSlice.actions;

export default classManagementSlice.reducer;
