/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable no-nested-ternary */
/* eslint-disable react/no-array-index-key */
/* eslint-disable jsx-a11y/alt-text */
import React, { Dispatch, FormEvent, SetStateAction, useCallback, useState } from 'react';
import {
  Divider,
  Checkbox,
  Grid,
  Paper,
  Stack,
  Button,
  Typography,
  Box,
  FormControl,
  Select,
  MenuItem,
  SelectChangeEvent,
  useTheme,
  TextField,
} from '@mui/material';
import styled from 'styled-components';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useAppSelector } from '@/hooks/useAppSelector';
import { getScholarshipFeeListStatus, getYearData } from '@/config/storeSelectors';
import useAuth from '@/hooks/useAuth';
import { fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import LoadingButton from '@mui/lab/LoadingButton';
import SaveIcon from '@mui/icons-material/Save';
import { fetchScholarshipFeeList } from '@/store/ManageFee/manageFee.thunks';

const FeeAllocationRoot = styled.div`
  width: 100%;
  padding: 8px 24px;
`;
export type ScholarshipAllocationType = {
  feeId: number;
  termId: number;
  type: number;
  value: string;
  dbResult: string;
  scholarshipId: number;
  scholarshipMapId: number;
};
export type FeeAllocationProps = {
  scholarshipId: number;
  academicYearId: number;
  isSubmitting: boolean;
  scholarshipValues: ScholarshipAllocationType[];
  onClose: () => void;
  setScholarshipValues: Dispatch<SetStateAction<ScholarshipAllocationType[]>>;
};

function FeeAllocation({
  onClose,
  scholarshipId,
  academicYearId,
  isSubmitting,
  scholarshipValues,
  setScholarshipValues,
}: FeeAllocationProps) {
  const { user } = useAuth();
  const theme = useTheme();
  const dispatch = useAppDispatch();
  const adminId: number = user ? user.accountId : 0;
  const [selectedRows, setSelectedRows] = useState([]);
  const YearData = useAppSelector(getYearData);
  const [academicYearFilter, setAcademicYearFilter] = useState(academicYearId);
  const ScholarshipFeeStatus = useAppSelector(getScholarshipFeeListStatus);
  const [selectedData, setSelectedData] = useState([{ feeId: 0, feeTitle: '' }]);
  const [selectedTerms, setSelectedTerms] = useState([{ termId: 0, termTitle: '' }]);
  const [isLoading, setIsloading] = useState<boolean>(false);
  const [disabledCheckBoxes, setDisabledCheckBoxes] = useState<boolean[]>([]);

  const initialClassSectionListRequest = React.useMemo(
    () => ({
      adminId,
      academicId: academicYearId,
      feeTypeId: 1,
    }),
    [adminId, academicYearId]
  );
  const currentClassSectionListRequest = React.useMemo(
    () => ({
      adminId,
      academicId: academicYearFilter,
      feeTypeId: 1,
    }),
    [adminId, academicYearFilter]
  );

  const loadClassSectionList = useCallback(
    async (request: { adminId: number; academicId: number; feeTypeId: number }) => {
      try {
        const data: any = await dispatch(fetchScholarshipFeeList(request)).unwrap();
        setSelectedData(data.basicFeeDetails);
        setSelectedTerms(data.termFeeDetails);
        setSelectedRows([]);
      } catch (error) {
        console.error('Error loading Scholarship list:', error);
      }
    },
    [dispatch]
  );

  React.useEffect(() => {
    dispatch(fetchYearList(adminId));
    loadClassSectionList(initialClassSectionListRequest);
    console.log('scholarshipId::::----', scholarshipId);
    console.log('scholarshipValues::::----', scholarshipValues);
  }, [dispatch, adminId, initialClassSectionListRequest, loadClassSectionList, academicYearFilter]);

  const handleYearChange = (e: SelectChangeEvent) => {
    setAcademicYearFilter(parseInt(YearData.filter((item) => item.accademicId === e.target.value)[0].accademicId, 10));
    loadClassSectionList({ ...currentClassSectionListRequest, academicId: parseInt(e.target.value, 10) });
    setSelectedRows([]);
  };

  const handleReset = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      setAcademicYearFilter(parseInt(YearData[0].accademicId, 10));
      setDisabledCheckBoxes([]);
      loadClassSectionList(currentClassSectionListRequest);
    },
    [YearData]
  );
  const handleCancel = () => {
    loadClassSectionList(currentClassSectionListRequest);
    setDisabledCheckBoxes([]);
  };
  const handleSubmit = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      loadClassSectionList(currentClassSectionListRequest);
      setSelectedRows([]);
    },
    [loadClassSectionList, currentClassSectionListRequest]
  );
  const OnCheckboxClick = (selectRows: { feeId: number; feeTitle: string }[]) => {
    const existingValue = scholarshipValues.filter(
      (value) =>
        selectRows.map((row) =>
          selectedTerms.map((term) => value.feeId === row.feeId && value.termId === term.termId)
        ) && value.scholarshipId === scholarshipId
    );
    console.log(existingValue);
    if (existingValue.length > 0) {
      if (existingValue.length === selectedTerms.length) {
        setScholarshipValues((prev) =>
          prev.filter(
            (val) =>
              !existingValue.map(
                (row) =>
                  selectedTerms.map((term) => val.feeId === row.feeId && val.termId === term.termId) &&
                  val.scholarshipId === scholarshipId
              )
          )
        );
        console.log('Selected:', scholarshipValues);
      }
    } else {
      setScholarshipValues((prev) => [
        ...prev,
        ...selectRows.flatMap((row: { feeId: number; feeTitle: string }) =>
          selectedTerms.map((term) => ({
            feeId: row.feeId,
            termId: term.termId,
            value: '',
            type: 1,
            dbResult: '',
            scholarshipId,
            scholarshipMapId: 0,
          }))
        ),
      ]);
      console.log('Selected::', scholarshipValues);
    }
  };
  const getRowKey = useCallback((row: { feeId: number; feeTitle: string }) => row.feeId, []);

  const ClassSectionListColumns: DataTableColumn<{ feeId: number; feeTitle: string }>[] = [
    {
      name: 'fee',
      dataKey: 'feeTitle',
      headerLabel: 'Fee Name',
    },
    ...selectedTerms.map((term) => ({
      name: `${term.termId}`,
      headerLabel: `${term.termTitle}`,
      renderCell: (row: any) => {
        return (
          <Paper sx={{ minHeight: 20, width: 200 }}>
            <Stack direction="row" gap={1}>
              <Checkbox
                checked={
                  !!scholarshipValues.find(
                    (value) =>
                      value.feeId === row.feeId && value.termId === term.termId && value.scholarshipId === scholarshipId
                  )
                }
                onChange={() => {
                  const existingValue = scholarshipValues.find(
                    (value) =>
                      value.feeId === row.feeId && value.termId === term.termId && value.scholarshipId === scholarshipId
                  );
                  if (existingValue) {
                    setScholarshipValues((prev) =>
                      prev.filter(
                        (val) =>
                          !(
                            val.feeId === row.feeId &&
                            val.termId === term.termId &&
                            val.scholarshipId === scholarshipId
                          )
                      )
                    );
                    console.log(scholarshipValues);
                  } else {
                    setScholarshipValues((prev) => [
                      ...prev,
                      {
                        feeId: row.feeId,
                        termId: term.termId,
                        value: '',
                        type: 1,
                        dbResult: '',
                        scholarshipId,
                        scholarshipMapId: 0,
                      },
                    ]);
                  }
                }}
              />
              {!!scholarshipValues.find(
                (value) =>
                  value.feeId === row.feeId && value.termId === term.termId && value.scholarshipId === scholarshipId
              ) && (
                <TextField
                  value={
                    scholarshipValues.filter(
                      (value) =>
                        value.feeId === row.feeId &&
                        value.termId === term.termId &&
                        value.scholarshipId === scholarshipId
                    )[0]?.value
                  }
                  onChange={(e) =>
                    setScholarshipValues((prev) =>
                      prev.map((val) =>
                        val.feeId === row.feeId && val.termId === term.termId && val.scholarshipId === scholarshipId
                          ? { ...val, value: e.target.value }
                          : val
                      )
                    )
                  }
                  type="number"
                  InputProps={{
                    style: {
                      padding: 0,
                    },
                    endAdornment: (
                      <Select
                        variant="outlined"
                        value={
                          scholarshipValues.filter(
                            (value) =>
                              value.feeId === row.feeId &&
                              value.termId === term.termId &&
                              value.scholarshipId === scholarshipId
                          )[0]?.type
                        }
                        onChange={(e) =>
                          setScholarshipValues((prev) =>
                            prev.map((value) =>
                              value.feeId === row.feeId &&
                              value.termId === term.termId &&
                              value.scholarshipId === scholarshipId
                                ? { ...value, type: Number(e.target.value) }
                                : value
                            )
                          )
                        }
                        size="small"
                        sx={{ borderEndEndRadius: 5, backgroundColor: theme.palette.grey[100] }}
                      >
                        <MenuItem value={1}>%</MenuItem>
                        <MenuItem value={2}>₹</MenuItem>
                      </Select>
                    ),
                  }}
                />
              )}
            </Stack>
          </Paper>
        );
      },
    })),
  ];

  return (
    <FeeAllocationRoot>
      <Box>
        <Divider />
        <form noValidate onReset={handleReset} onSubmit={handleSubmit}>
          <Grid pb={2} pt={1} container spacing={3} alignItems="end">
            <Grid item xl={2} lg={3} md={3} sm={6} xs={12}>
              <FormControl fullWidth>
                <Typography variant="h6" fontSize={12} color="GrayText">
                  Academic Year
                </Typography>
                <Select
                  labelId="academicYearFilter"
                  id="academicYearFilterSelect"
                  value={academicYearFilter.toString()}
                  onChange={handleYearChange}
                  placeholder="Select Year"
                  disabled
                >
                  {YearData.map((opt) => (
                    <MenuItem key={opt.accademicId} value={opt.accademicId}>
                      {opt.accademicTime}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xl={2} lg={4} md={3} sm={6} xs={12}>
              <FormControl fullWidth>
                <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                  <Button disabled variant="contained" color="secondary" type="reset" fullWidth>
                    Reset
                  </Button>
                </Stack>
              </FormControl>
            </Grid>
          </Grid>
        </form>
        <Paper
          sx={{
            border: selectedData?.length === 0 ? `0px` : `1px solid ${theme.palette.grey[300]}`,
            width: '100%',
            overflow: 'auto',
          }}
        >
          <Box
            sx={{
              height: 'calc(100vh - 23.7rem)',
              width: { xs: selectedData?.length !== 0 ? '43.75rem' : '100%', md: '100%' },
            }}
          >
            <DataTable
              ShowCheckBox
              isSubmitting={isSubmitting}
              disabledCheckBox={disabledCheckBoxes}
              onCheckboxClick={OnCheckboxClick}
              RowSelected
              setSelectedRows={setSelectedRows}
              selectedRows={selectedRows}
              columns={ClassSectionListColumns}
              data={selectedData}
              getRowKey={getRowKey}
              fetchStatus="success"
            />
          </Box>
        </Paper>
        <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 3 }}>
          {selectedData.length !== 0 && (
            <Stack spacing={2} direction="row">
              <Button disabled={isSubmitting} onClick={onClose} variant="contained" color="secondary">
                Cancel
              </Button>
              <LoadingButton
                endIcon={<SaveIcon />}
                fullWidth
                loadingPosition="start"
                onClick={onClose}
                loading={isSubmitting}
                variant="contained"
                color="primary"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Saving...' : 'Save'}
              </LoadingButton>
            </Stack>
          )}
        </Box>
      </Box>
    </FeeAllocationRoot>
  );
}

export default FeeAllocation;
