import { useCallback, useMemo } from 'react';
import { Box, Grid, Card, useTheme, Chip, Paper } from '@mui/material';
import styled from 'styled-components';
import useSettings from '@/hooks/useSettings';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { AssignmentReportType } from '@/types/Reports';

const AssignmentReportData = [
  {
    id: 1,
    assignment: 'Vacation',
    mark: '10/20',
    comment: 'Improved',
    status: 'Not Added',
  },
  {
    id: 2,
    assignment: 'Vacation',
    mark: '10/20',
    comment: 'Good',
    status: 'Added',
  },
  {
    id: 3,
    assignment: 'Vacation',
    mark: '10/20',
    comment: 'Very Good',
    status: 'Not Added',
  },

  {
    id: 4,
    assignment: 'Vacation',
    mark: '10/20',
    comment: 'Improved',
    status: 'Not Added',
  },
  {
    id: 5,
    assignment: 'Vacation',
    mark: '10/20',
    comment: 'Improved',
    status: 'Pending',
  },
  {
    id: 6,
    assignment: 'Vacation',
    mark: '10/20',
    comment: 'Poor',
    status: 'Added',
  },
  {
    id: 7,
    assignment: 'Vacation',
    mark: '10/20',
    comment: 'Good',
    status: 'Not Added',
  },
];

const AssignmentReportRoot = styled.div`
  .ReportCard {
    display: flex;
    flex-direction: column;
    height: 350px;
    background-color: ${(props) => (props.theme.themeMode === 'light' ? '#f9fded' : props.theme.palette.grey[900])};
    /* border: 1px solid ${(props) => props.theme.palette.grey[300]}; */

    .card-main-body {
      display: flex;
      flex-direction: column;
      min-height: calc(100% - 5px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }
    }
  }
`;

function AssignmentReport() {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const tableStyles = { backgroundColor: isLight ? '#f9fded' : theme.palette.grey[900] };
  const getRowKeyAssignmentReport = useCallback((row: AssignmentReportType) => row.id, []);

  const AssignmentReportColumns: DataTableColumn<AssignmentReportType>[] = useMemo(
    () => [
      {
        name: 'assignment',
        dataKey: 'assignment',
        headerLabel: 'Assignment',
      },
      {
        name: 'mark',
        dataKey: 'mark',
        headerLabel: 'Mark',
      },
      {
        name: 'comment',
        dataKey: 'comment',
        headerLabel: 'Teacher Comment',
      },
      {
        name: 'status',
        headerLabel: 'Status',
        renderCell: (row) => {
          let colorStatus = 'default';
          if (row.status === 'Added') {
            colorStatus = 'success';
          } else if (row.status === 'Not Added') {
            colorStatus = 'secondary';
          } else if (row.status === 'Pending') {
            colorStatus = 'warning';
          }
          return <Chip variant="outlined" sx={{ height: 25 }} label={row.status} color={colorStatus} />;
        },
      },
    ],
    []
  );

  return (
    <AssignmentReportRoot>
      <Grid container spacing={3}>
        <Grid item xl={12} lg={12} xs={12}>
          <Card className="ReportCard" sx={{ mt: 3, boxShadow: 0, px: { xs: 3, md: 2 }, py: { xs: 2, md: 2 } }}>
            <div className="card-main-body">
              <Box>
                <Chip
                  label="Assignment"
                  variant="filled"
                  sx={{
                    height: 25,
                    background: isLight ? theme.palette.common.black : theme.palette.common.white,
                    color: isLight ? theme.palette.common.white : theme.palette.common.black,
                  }}
                />
              </Box>
              <Paper className="card-table-container">
                <DataTable
                  columns={AssignmentReportColumns}
                  data={AssignmentReportData}
                  getRowKey={getRowKeyAssignmentReport}
                  fetchStatus="success"
                  tableStyles={tableStyles}
                />
              </Paper>
            </div>
          </Card>
        </Grid>
      </Grid>
    </AssignmentReportRoot>
  );
}

export default AssignmentReport;
