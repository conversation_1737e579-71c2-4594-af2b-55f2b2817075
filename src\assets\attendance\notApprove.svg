<svg xmlns="http://www.w3.org/2000/svg" width="97.619" height="69.993" viewBox="0 0 97.619 69.993">
  <g id="Group_58263" data-name="Group 58263" transform="translate(124 16002.998)">
    <g id="Group_57804" data-name="Group 57804" transform="translate(-148.715 -16040.825)">
      <path id="Path_44938" data-name="Path 44938" d="M8938.35,2227.948s1.406-4.319,1.406-4.581-.61-7.77-.61-8.293-.631-6.722-.8-7.77.1-5.063.8-5.238a13.846,13.846,0,0,0,2.618-1.658s1.572.262,1.833,0a20.883,20.883,0,0,1,3.841-.349s5.151.436,5.413.349,15.364-.349,15.364-.349l8.643.349,1.833.524,1.66,1.135,1.309,1.484,1.223,1.746s1.046.7,1.134.96a8.086,8.086,0,0,0,1.834,1.047l2.706.7h2.706l4.714-.349,7.333.349h7.771l1.746,1.833.785,1.921-.785,6.722-.7,8.992-1.571,14.753-.7,5.849-.961,1.746-1.31,1.659-15.364.437-40.158,1.047-7.857-1.484s-2.357-2.182-2.443-2.881-1.747-11.349-2.008-12.047S8938.35,2227.948,8938.35,2227.948Z" transform="translate(-8903.81 -2147.459)" fill="#f3eef3"/>
      <g id="__TEMP__SVG__" transform="translate(83.13 65.605)">
        <g id="Group_57584" data-name="Group 57584" transform="translate(0 0)">
          <g id="Group_57583" data-name="Group 57583" transform="translate(0 0)">
            <path id="Path_44915" data-name="Path 44915" d="M19.191,18.265a.657.657,0,1,0-.93.93l3.8,3.8-3.8,3.8a.657.657,0,1,0,.93.93l3.8-3.8,3.8,3.8a.657.657,0,1,0,.93-.93l-3.8-3.8,3.8-3.8a.657.657,0,1,0-.93-.93l-3.8,3.8-3.8-3.8Z" transform="translate(-18.068 -18.072)"/>
          </g>
        </g>
      </g>
      <g id="__TEMP__SVG__2" data-name="__TEMP__SVG__" transform="translate(54.6 65.604)">
        <g id="Group_57584-2" data-name="Group 57584" transform="translate(0 0)">
          <g id="Group_57583-2" data-name="Group 57583" transform="translate(0 0)">
            <path id="Path_44915-2" data-name="Path 44915" d="M19.191,18.265a.657.657,0,1,0-.93.93l3.8,3.8-3.8,3.8a.657.657,0,1,0,.93.93l3.8-3.8,3.8,3.8a.657.657,0,1,0,.93-.93l-3.8-3.8,3.8-3.8a.657.657,0,1,0-.93-.93l-3.8,3.8-3.8-3.8Z" transform="translate(-18.068 -18.072)"/>
          </g>
        </g>
      </g>
      <path id="Path_44930" data-name="Path 44930" d="M203.606,486.774c1.139.116,2.139.186,3.138.349.837.14,1.581.465,1.7,1.464a2.016,2.016,0,0,1-1.488,1.929,5.3,5.3,0,0,1-1.7.279c-16.016.279-32.055.442-48.071,0-5.16-.139-10.321-.256-15.458-.674a14.318,14.318,0,0,1-3.3-.651,5.17,5.17,0,0,1-3.7-4.091,47.79,47.79,0,0,1-.674-6.323q-.872-9.763-1.7-19.526c-.4-4.626-.814-9.252-1.209-13.877a12.788,12.788,0,0,1,2.766-9.019,4.042,4.042,0,0,1,2.046-1.534c.581-.186.674-.488.651-1a10.862,10.862,0,0,1,.093-2.6,6.153,6.153,0,0,1,6.253-5c8.647.093,17.271.233,25.918.349q14.261.209,28.522.4c4.114.07,6.625,2.627,6.648,6.741,0,2.6.023,5.23,0,7.834,0,.581.139.79.744.883a5.153,5.153,0,0,1,4.393,5.742q-1.534,15.481-3.045,30.939c-.116,1.116-.209,2.255-.325,3.371A6.315,6.315,0,0,1,203.606,486.774Zm-32.59-1h29.312a3.2,3.2,0,0,0,3.394-2.813c.535-5.346,1.046-10.693,1.581-16.039.6-6.23,1.209-12.483,1.836-18.712A2.956,2.956,0,0,0,206,445.444a3.891,3.891,0,0,0-2.6-.744c-6.718,0-13.459-.023-20.177.023a7.308,7.308,0,0,1-5.951-2.673c-.721-.86-1.534-1.674-2.232-2.557A7.2,7.2,0,0,0,168.9,436.7c-10.088.047-20.177.023-30.265.023-.325,0-.628,0-.953.023a3.171,3.171,0,0,0-3.045,3.673c.232,2.278.418,4.579.6,6.857.488,5.486.976,10.995,1.464,16.481.558,6.206,1.139,12.436,1.65,18.643.186,2.278,1.325,3.394,3.626,3.394Q156.488,485.763,171.016,485.775Zm-.837-56.788v-.023h-1.906l-25.384-.349a4.02,4.02,0,0,0-4.3,3.464c-.093.628,0,1.278-.07,1.906-.093.767.163.93.93.93,9.949-.023,19.875,0,29.824-.023a8.7,8.7,0,0,1,6.834,3.022c.837.953,1.674,1.929,2.534,2.859a5.612,5.612,0,0,0,4.37,1.976h18.34c.558,0,.721-.116.721-.7-.023-2.882,0-5.741-.023-8.624a3.747,3.747,0,0,0-.139-1.023c-.535-1.929-2.092-3.022-4.417-3.045C188.357,429.219,179.268,429.1,170.179,428.987Z" transform="translate(-98.508 -383.248)"/>
      <path id="Path_44931" data-name="Path 44931" d="M458.951,454.452c-.465.023-1.093-.07-1.185-.86-.093-.814.442-1.093,1.185-1.139,2.092-.139,4.207-.325,6.3-.465.814-.046,1.418.372,1.348.953-.07.744-.581.953-1.279,1C463.274,454.081,461.206,454.266,458.951,454.452Z" transform="translate(-349.227 -402.811)"/>
      <path id="Path_44932" data-name="Path 44932" d="M457.548,420.312a1.025,1.025,0,0,1-.581.976c-1.976,1.255-3.952,2.51-5.9,3.766a.954.954,0,1,1-1.069-1.581c2.046-1.348,4.114-2.673,6.183-3.975A.92.92,0,0,1,457.548,420.312Z" transform="translate(-342.92 -377.781)"/>
      <path id="Path_44933" data-name="Path 44933" d="M435.495,408.608c.256-.558.535-1.232.837-1.883a17.731,17.731,0,0,1,1.883-3.022c.372-.465.953-.744,1.441-.372.511.4.372.953-.023,1.464a14.791,14.791,0,0,0-2.255,4.277.9.9,0,0,1-1.139.674C435.7,409.63,435.472,409.258,435.495,408.608Z" transform="translate(-332.14 -365.332)"/>
      <path id="Path_44935" data-name="Path 44935" d="M251.128,609.964a14.543,14.543,0,0,1,10.236,4.165c.537.518.592,1.018.167,1.37-.5.407-.925.185-1.333-.222a12.314,12.314,0,0,0-6.7-3.48,12.733,12.733,0,0,0-11.791,3.814c-.3.3-.574.63-1.055.463a.821.821,0,0,1-.259-1.444,14.3,14.3,0,0,1,6.478-4.054A13.394,13.394,0,0,1,251.128,609.964Z" transform="translate(-177.008 -523.031)"/>
      <path id="Path_44936" data-name="Path 44936" d="M490.083,587l.581,1.185,1.325.186-.953.93.232,1.3-1.185-.628-1.162.628.232-1.3-.953-.93,1.3-.186Z" transform="translate(-372.596 -506.441)"/>
      <path id="Path_44937" data-name="Path 44937" d="M99.85,565l.814,1.674,1.836.256-1.325,1.3.3,1.813-1.627-.86-1.65.86.325-1.813-1.325-1.3,1.836-.256Z" transform="translate(-72.485 -489.555)"/>
      <circle id="Ellipse_3811" data-name="Ellipse 3811" cx="1.906" cy="1.906" r="1.906" transform="translate(25.366 64.984)" fill="none" stroke="#000" stroke-miterlimit="10" stroke-width="1"/>
      <circle id="Ellipse_3812" data-name="Ellipse 3812" cx="3.835" cy="3.835" r="3.835" transform="translate(113.664 88.18)" fill="none" stroke="#000" stroke-miterlimit="10" stroke-width="2"/>
    </g>
  </g>
</svg>
