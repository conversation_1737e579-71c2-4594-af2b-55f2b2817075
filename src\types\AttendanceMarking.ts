import { Dayjs } from 'dayjs';
import { FetchStatus } from './Common';
import { PagedData, PageRequestWithFilters, PaginationInfo } from './Pagination';

/* eslint-disable prettier/prettier */
export type AttendanceListInfo = {
  studentId: number;
  admissionNumber: string;
  rollNo: string;
  studentName: string;
  gender: number;
  dateOfBirth: string;
  classId: number;
  studentImage: string;
  sessionId: number;
  voiceId: string;
  absents: number;
  attendanceId: number;
};

export type AttendanceListRequest = {
  accademicId: number;
  adminId: number;
  classId: number;
  absentDate: Dayjs | string | null;
};

export type MarkAttendance = {
  accademicId: number;
  studentId: number;
  admissionNumber: string;
  rollNo: number;
  classId: number;
  sessionId: number;
  adminId: number;
  absentDate: Dayjs | string | null;
  absentStatus: string;
};

export type LeaveNoteListInfo = {
  leaveId: number;
  subject: string;
  description: string;
  studentId: number;
  rollNo: number;
  studentName: string;
  classId: number;
  className: string;
  fromDate: Dayjs | string | null;
  todate: Dayjs | string | null;
  appliedDate: Dayjs | string | null;
  aprovedDate: Dayjs | string | null;
  aprovedBy: number;
  status: number;
};

export type LeaveNoteListFilters = {
  adminId: number;
  academicId: number;
  classId: number;
  fromdate: Dayjs | string | null;
  todate: Dayjs | string | null;
};

export type ApproveLeaveRequest = {
  leaveId: number;
  approvedBy: number;
};

export type RejectLeaveRequest = {
  leaveId: number;
  rejectedBy: number;
};

export type AttendanceCalendarRequest = {
  adminId: number;
  academicId: number;
  academicTime: string;
  classId: number;
  month: number;
};

export type AttendanceCalendarDataType = {
  reprtHeader: [
    {
      reportDate: Dayjs | string | null;
      absentDay: number;
    }
  ];
  studentList: [
    {
      rollNo: number;
      studentName: string;
      studentAbentDays: [
        {
          rollNo: number;
          absentDay: number;
        }
      ];
    }
  ];
  holidays: [
    {
      holiday: number;
    }
  ];
  attendanceTakenDays: [
    {
      attendanceTakenDays: number;
    }
  ];
};
//  Student Enquiry List Types
export type StudentEnquiryListInfo = {
  queryId: number;
  subject: string;
  description: string;
  studentId: number;
  rollNo: number;
  studentName: string;
  classId: number;
  className: string;
  enquiredDate: string;
  reply: string;
  repliedDate: string;
  repliedBy: number;
  status: number;
};

export type StudentEnquiryListFilters = {
  adminId: number;
  academicId: number;
  classId: number;
  fromdate: Dayjs | string | null;
  todate: Dayjs | string | null;
};

export type EnquiryReplyRequestData = {
  queryId: number | null;
  repliedBy: number;
  reply: string;
};

//  Student Attendance Summary Report Types
export type AttendanceSummaryReportInfo = {
  rollNo: number;
  studentName: string;
  totalWorking: number;
  totalPresent: number;
  totalAbsent: number;
  percentage: number;
  attendanceTaken: number;
  studentImage?:string;
};

export type AttendanceSummaryReportRequest = {
  adminId: number;
  academicId: number;
  academicTime: string;
  classId: number;
  month: number ;
};

// --------------------------------------------
export type LeaveNoteListRequest = PageRequestWithFilters<LeaveNoteListFilters>;
export type LeaveNoteListPagedData = PagedData<LeaveNoteListInfo>;

export type StudentEnquiryListRequest = PageRequestWithFilters<StudentEnquiryListFilters>;
export type StudentEnquiryListPagedData = PagedData<StudentEnquiryListInfo>;

// --------------------------------------------

// export type LeaveNoteCreateRequest = Omit<LeaveNoteListInfo, 'classId'>;

export type AttendanceMarkingState = {
  attendanceList: {
    status: FetchStatus;
    data: AttendanceListInfo[];
    error: string | null;
  };
  leaveNoteList: {
    status: FetchStatus;
    data: LeaveNoteListInfo[];
    error: string | null;
    pageInfo: PaginationInfo;
    sortColumn: string;
    sortDirection: 'asc' | 'desc';
  };
  studentEnquiryList: {
    status: FetchStatus;
    data: StudentEnquiryListInfo[];
    error: string | null;
    pageInfo: PaginationInfo;
    sortColumn: string;
    sortDirection: 'asc' | 'desc';
  };
  attendanceCalendar: {
    status: FetchStatus;
    data: AttendanceCalendarDataType;
    error: string | null;
  };
  attendanceSummaryReport: {
    status: FetchStatus;
    data: AttendanceSummaryReportInfo[];
    error: string | null;
  };
  // absenteesList: AttendanceListInfo[];
  submitting: boolean;
  deletingRecords: Record<number, boolean>;
  error: string | null | undefined;
};

export type Data = {
  rollNo: number;
  name: string;
  status: string;
};

export type HeadCell = {
  disablePadding: boolean;
  id: keyof Data;
  label: string;
  numeric: boolean;
};
export type AbsenteesProps = {
  rollno: number;
  name: string;
  mark: 'Present' | 'Absent';
};
export type StudentProps = {
  rollNo: number;
  studentName: string;
  class: string;
  year: string;
  attendanceStatus: number;
  image: string;
};

export type SwitchDataMapProps = {
  '1': string[];
  '2': string[];
  '3': string[];
  '4': string[];
  '5': string[];
  '6': null;
};

export type YearSelect = {
  id: number;
  year: string;
};
export type ClassSelect = {
  id: number;
  class: string;
};
