import React, { useCallback, useState } from 'react';
import { Link, useLocation, useOutletContext } from 'react-router-dom';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';
import styled from 'styled-components';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import Page from '@/components/shared/Page';
import StudentDetails from '@/features/ExtraReports/StudentDetails/StudentDetails';
import StudentsAttendance from '@/features/Attendance-Marking/StudentsAttendance';
import AbsenteesList from '@/features/Attendance-Marking/AbsenteesList';
import Subject from '@/features/AcademicManagement/ManageSubject/ManageSubject';
import CtsList from '@/features/StaffManagement/CTSAllocation/CTSAllocation';
import ManageStaffs from '@/features/StaffManagement/ManageStaffs/ManageStaffs ';
import ManageConveyers from '@/features/StaffManagement/ManageConveyors/ManageConveyers';
import PtaMembersList from '@/features/ExtraReports/PtaMembersList';
import StudentBday from '@/features/ExtraReports/StudentBday';
import MasterReport from '@/features/ExtraReports/StudentDetails/MasterReport/MasterReport';
import { useDispatch } from 'react-redux';
import { flushStore } from '@/store/flush.slice';
import { OutletContextType } from '@/types/Common';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
  state?: string; 
}

const ReportRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */

  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};

  .tab {
    background-color: ${(props) => (props.theme.themeMode === 'light' ? '#fff' : props.theme.palette.grey[800])};
    @media screen and (min-width: 1375px) {
      width: 100%;
    }
    @media screen and (max-width: 768px) {
      width: 100%;
    }
  }
  .Mui-selected {
    color: ${(props) => props.theme.palette.primary.main};
  }
`;

function TabPanel({ children, value, index, state }: TabPanelProps) {
  return (
    <Box
      sx={{ paddingTop: state === 'MasterReport' ? '0px' : '3rem' }}
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
    >
      {value === index && <Box>{children}</Box>}
    </Box>
  );
}
function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`,
  };
}
const DefaultStudentInfo: any = {
  className: '',
  name: '',
  academicYear: '',
  admissionNumber: '',
  adm_date: '',
  gardianName: '',
  gardianNumber: '',
};

function Report() {
  const { topBarHeight } = useOutletContext<OutletContextType>();
  const location = useLocation();
  const [value, setValue] = React.useState(0);
  const [studentData, setStudentData] = React.useState(DefaultStudentInfo);
  const [masterReportView, setMasterReportView] = useState<'StudentDetails' | 'MasterReport'>('StudentDetails');


  const dispatch = useDispatch();

  React.useEffect(() => {
    dispatch(flushStore());
    console.log('Flush Store');
  }, [dispatch, value]);
  const handleMasterReport = useCallback((studentObj: any) => {
    setStudentData(studentObj);
    setMasterReportView('MasterReport');
  }, []);

  const handleBackFromMasterReport = () => {
    setMasterReportView('StudentDetails');
  };

  React.useEffect(() => {
    const currentPath = location.pathname;
    const routePaths = [
      '/report/student-details',
      '/report/student-birthday-list',
      '/report/attendance-report',
      '/report/absentees-list',
      '/report/teacher-subject-list',
      '/report/class-teacher-info',
      '/report/pta-members-list',
      '/report/staff-list',
      '/report/conveyors-list',
    ];
    const index = routePaths.indexOf(currentPath);
    if (index !== -1) {
      setValue(index);
    }
  }, [location]);

  return (
    <ReportRoot>
      <Tabs
        className="tab"
        variant="scrollable"
        scrollButtons
        allowScrollButtonsMobile
        value={value}
        aria-label="basic tabs"
        sx={{
          ml: '0px',
          borderBottom: 1,
          borderColor: 'divider',
          position: 'fixed',
           top: `${topBarHeight}px`,
          zIndex: 1,
          display: masterReportView === 'MasterReport' ? 'none' : '',
        }}
      >
        <Tab {...a11yProps(0)} label="Student Details" component={Link} to="/report/student-details" />
        <Tab {...a11yProps(1)} label="B'day List" component={Link} to="/report/student-birthday-list" />
        <Tab {...a11yProps(2)} label="Attendance Report" component={Link} to="/report/attendance-report" />
        <Tab {...a11yProps(3)} label="Absentees List" component={Link} to="/report/absentees-list" />
        <Tab {...a11yProps(4)} label="Teacher Subject List" component={Link} to="/report/teacher-subject-list" />
        <Tab {...a11yProps(5)} label="Class Teacher Info" component={Link} to="/report/class-teacher-info" />
        <Tab {...a11yProps(6)} label="PTA Members List" component={Link} to="/report/pta-members-list" />
        <Tab {...a11yProps(7)} label="Staff Details" component={Link} to="/report/staff-list" />
        <Tab {...a11yProps(8)} label="Conveyors Details" component={Link} to="/report/conveyors-list" />
      </Tabs>

      <TabPanel value={value} state={masterReportView} index={0}>
        {masterReportView === 'StudentDetails' ? (
          <StudentDetails handleMasterReport={handleMasterReport} />
        ) : (
          <MasterReport onBackClick={handleBackFromMasterReport} datas={studentData} />
        )}
      </TabPanel>
      <TabPanel value={value} index={1}>
        <StudentBday />
      </TabPanel>
      <TabPanel value={value} index={2}>
        <StudentsAttendance />
      </TabPanel>
      <TabPanel value={value} index={3}>
        <AbsenteesList />
      </TabPanel>
      <TabPanel value={value} index={4}>
        <Subject />
      </TabPanel>
      <TabPanel value={value} index={5}>
        <CtsList />
      </TabPanel>
      <TabPanel value={value} index={6}>
        <PtaMembersList />
      </TabPanel>
      <TabPanel value={value} index={7}>
        <ManageStaffs />
      </TabPanel>
      <TabPanel value={value} index={8}>
        <ManageConveyers />
      </TabPanel>
    </ReportRoot>
  );
}

export default Report;
