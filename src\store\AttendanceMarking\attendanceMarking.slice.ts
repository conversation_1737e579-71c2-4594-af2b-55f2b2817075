import { AttendanceCalendarDataType, AttendanceListInfo, AttendanceMarkingState } from '@/types/AttendanceMarking';
import { PayloadAction, createSlice } from '@reduxjs/toolkit';
import { flushStore } from '@/store/flush.slice';
import { OutletContextType } from '@/types/Common';
import {
  ApproveLeave,
  MarkAbsentees,
  MarkPresent,
  RejectLeave,
  fetchAttendanceCalendar,
  fetchAttendanceList,
  fetchAttendanceSummaryReport,
  fetchLeaveNoteList,
  fetchStudentEnquiryList,
  sendEnquiryReply,
} from './attendanceMarking.thunks';

const initialState: AttendanceMarkingState = {
  attendanceList: {
    status: 'idle',
    data: [],
    error: null,
  },
  attendanceCalendar: {
    status: 'idle',
    data: {} as AttendanceCalendarDataType,
    error: null,
  },
  leaveNoteList: {
    status: 'idle',
    data: [],
    error: null,
    pageInfo: {
      totalrecords: 0,
      pagesize: 20,
      pagenumber: 1,
      totalpages: 0,
      remainingpages: 0,
    },
    sortColumn: 'classId',
    sortDirection: 'asc',
  },
  studentEnquiryList: {
    status: 'idle',
    data: [],
    error: null,
    pageInfo: {
      totalrecords: 0,
      pagesize: 20,
      pagenumber: 1,
      totalpages: 0,
      remainingpages: 0,
    },
    sortColumn: 'classId',
    sortDirection: 'asc',
  },
  attendanceSummaryReport: {
    status: 'idle',
    data: [],
    error: null,
  },
  // absenteesList: [],
  submitting: false,
  deletingRecords: {},
  error: null,
};

const attendanceMarkingSlice = createSlice({
  name: 'attendanceMarking',
  initialState,
  reducers: {
    setAttendanceList: (state, action: PayloadAction<AttendanceListInfo[]>) => {
      state.attendanceList.data = action.payload;
    },
    // setAbsenteesList: (state, action: PayloadAction<AttendanceListInfo[]>) => {
    //   state.absenteesList = action.payload;
    // },
    setSortColumn: (state, action: PayloadAction<string>) => {
      state.studentEnquiryList.pageInfo.pagenumber = 1;
      state.studentEnquiryList.sortColumn = action.payload;
      state.studentEnquiryList.sortDirection = 'asc';
    },
    setSortDirection: (state, action: PayloadAction<'asc' | 'desc'>) => {
      state.studentEnquiryList.pageInfo.pagenumber = 1;
      state.studentEnquiryList.sortDirection = action.payload;
    },
    setSortColumnAndDirection: (state, action: PayloadAction<{ column: string; direction: 'asc' | 'desc' }>) => {
      state.studentEnquiryList.pageInfo.pagenumber = 1;
      state.studentEnquiryList.sortColumn = action.payload.column;
      state.studentEnquiryList.sortDirection = action.payload.direction;
    },
    setSubmitting: (state, action: PayloadAction<boolean>) => {
      state.submitting = action.payload;
    },
  },
  extraReducers(builder) {
    builder
      .addCase(fetchAttendanceList.pending, (state) => {
        state.attendanceList.status = 'loading';
        state.attendanceList.error = null;
      })
      .addCase(fetchAttendanceList.fulfilled, (state, action) => {
        state.attendanceList.status = 'success';
        state.attendanceList.data = action.payload;
        state.attendanceList.error = null;
      })
      .addCase(fetchAttendanceList.rejected, (state, action) => {
        state.attendanceList.status = 'error';
        state.attendanceList.error = action.payload || 'Unknown error in fetching Attendance list';
      })
      // Mark absentees
      .addCase(MarkAbsentees.pending, (state) => {
        state.submitting = true;
        state.attendanceList.error = null;
      })
      .addCase(MarkAbsentees.fulfilled, (state) => {
        state.submitting = false;
        // state.absenteesList = state.attendanceList.data.filter((f) => f.attendanceId > 0);
        // console.log('state.absenteesList', state.absenteesList);
        state.attendanceList.error = null;
      })
      .addCase(MarkAbsentees.rejected, (state, action) => {
        state.submitting = false;
        state.attendanceList.error = action.payload || 'Unknown error in fetching Attendance list';
      })
      // Mark present
      .addCase(MarkPresent.pending, (state) => {
        state.submitting = true;
        state.attendanceList.error = null;
      })
      .addCase(MarkPresent.fulfilled, (state) => {
        state.submitting = false;
        state.attendanceList.error = null;
      })
      .addCase(MarkPresent.rejected, (state, action) => {
        state.submitting = false;
        state.attendanceList.error = action.payload || 'Unknown error in fetching Leave Note list';
      })
      .addCase(fetchLeaveNoteList.pending, (state) => {
        state.leaveNoteList.status = 'loading';
        state.leaveNoteList.error = null;
      })
      .addCase(fetchLeaveNoteList.fulfilled, (state, action) => {
        const { data, ...pageInfo } = action.payload;
        state.leaveNoteList.status = 'success';
        state.leaveNoteList.data = data;
        state.leaveNoteList.error = null;
        state.leaveNoteList.pageInfo = pageInfo;
      })
      .addCase(fetchLeaveNoteList.rejected, (state, action) => {
        state.leaveNoteList.status = 'error';
        state.leaveNoteList.error = action.payload || 'Unknown error in fetching Leave Note list';
      })
      .addCase(fetchStudentEnquiryList.pending, (state) => {
        state.studentEnquiryList.status = 'loading';
        state.studentEnquiryList.error = null;
      })
      .addCase(fetchStudentEnquiryList.fulfilled, (state, action) => {
        const { data, ...pageInfo } = action.payload;
        state.studentEnquiryList.status = 'success';
        state.studentEnquiryList.data = data;
        state.studentEnquiryList.error = null;
        state.studentEnquiryList.pageInfo = pageInfo;
      })
      .addCase(fetchStudentEnquiryList.rejected, (state, action) => {
        state.studentEnquiryList.status = 'error';
        state.studentEnquiryList.error = action.payload || 'Unknown error in fetching Student Enquiry list';
      })
      .addCase(sendEnquiryReply.pending, (state) => {
        state.submitting = true;
        state.attendanceList.error = null;
      })
      .addCase(sendEnquiryReply.fulfilled, (state) => {
        state.submitting = false;
        state.attendanceList.error = null;
      })
      .addCase(sendEnquiryReply.rejected, (state, action) => {
        state.submitting = false;
        state.attendanceList.error = action.payload || 'Unknown error in fetching Send Enquiry Reply list';
      })
      .addCase(ApproveLeave.pending, (state) => {
        state.submitting = true;
        state.attendanceList.error = null;
      })
      .addCase(ApproveLeave.fulfilled, (state) => {
        state.submitting = false;
        state.attendanceList.error = null;
      })
      .addCase(ApproveLeave.rejected, (state, action) => {
        state.submitting = false;
        state.attendanceList.error = action.payload || 'Unknown error in Approving Leave Note';
      })
      .addCase(RejectLeave.pending, (state) => {
        state.submitting = true;
        state.attendanceList.error = null;
      })
      .addCase(RejectLeave.fulfilled, (state) => {
        state.submitting = false;
        state.attendanceList.error = null;
      })
      .addCase(RejectLeave.rejected, (state, action) => {
        state.submitting = false;
        state.attendanceList.error = action.payload || 'Unknown error in Rejecting Leave Note';
      })
      .addCase(fetchAttendanceCalendar.pending, (state) => {
        state.submitting = true;
        state.attendanceCalendar.error = null;
      })
      .addCase(fetchAttendanceCalendar.fulfilled, (state, action) => {
        state.submitting = false;
        state.attendanceCalendar.data = action.payload;
        state.attendanceCalendar.status = 'success';
        state.attendanceCalendar.error = null;
      })
      .addCase(fetchAttendanceCalendar.rejected, (state, action) => {
        state.submitting = false;
        state.attendanceCalendar.error = action.payload || 'Unknown error in Rejecting Attendance Calendar';
      })
      .addCase(fetchAttendanceSummaryReport.pending, (state) => {
        state.attendanceSummaryReport.status = 'loading';
        state.attendanceSummaryReport.error = null;
      })
      .addCase(fetchAttendanceSummaryReport.fulfilled, (state, action) => {
        state.attendanceSummaryReport.status = 'success';
        state.attendanceSummaryReport.data = action.payload;
        state.attendanceSummaryReport.error = null;
      })
      .addCase(fetchAttendanceSummaryReport.rejected, (state, action) => {
        state.attendanceSummaryReport.status = 'error';
        state.attendanceSummaryReport.error =
          action.payload || 'Unknown error in fetching Attendance Summary Report list';
      })
      .addCase(flushStore, () => initialState);
  },
});

export const { setSortColumnAndDirection, setSortColumn, setSortDirection, setSubmitting, setAttendanceList } =
  attendanceMarkingSlice.actions;

export default attendanceMarkingSlice.reducer;
