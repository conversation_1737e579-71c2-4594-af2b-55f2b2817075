import React, { useState, useRef, useEffect, useCallback } from 'react';
import ReactPlayer from 'react-player';
import {
  Box,
  Button,
  Typography,
  Card,
  CardContent,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  CardMedia,
  Grid,
} from '@mui/material';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import Cropper from 'react-easy-crop';
import Slider from '@mui/material/Slider';

interface VideoData {
  id: string;
  url: string;
  thumbnail: string;
  name: string;
}

const VideoUpload = () => {
  const [videoFile, setVideoFile] = useState<File | null>(null);
  const [videoURL, setVideoURL] = useState<string | ''>('');
  const [thumbnail, setThumbnail] = useState<string | ''>('');
  const [storedVideos, setStoredVideos] = useState<VideoData[]>([]);
  const [dialogOpen, setDialogOpen] = useState(false);

  const [cropping, setCropping] = useState(false);
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const [crop, setCrop] = useState({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<{
    x: number;
    y: number;
    width: number;
    height: number;
  } | null>(null);
  const videoRef = useRef<HTMLVideoElement | null>(null);

  useEffect(() => {
    const storedData = localStorage.getItem('videos');
    if (storedData) {
      try {
        const parsedVideos = JSON.parse(storedData);
        if (Array.isArray(parsedVideos)) {
          setStoredVideos(parsedVideos);
        } else {
          console.error('Stored videos is not an array.');
          setStoredVideos([]);
        }
      } catch (error) {
        console.error('Error parsing stored videos:', error);
        setStoredVideos([]);
      }
    }
  }, []);

  const handleVideoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type.startsWith('video')) {
      setVideoFile(file);
      const url = URL.createObjectURL(file);
      setVideoURL(url);
      setThumbnail('');
    } else {
      alert('Please upload a valid video file.');
    }
  };

  const handleThumbnailUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type.startsWith('image')) {
      const url = URL.createObjectURL(file);
      setUploadedImage(url);
      setCropping(true);
    } else {
      alert('Please upload a valid image file.');
    }
  };

  const captureThumbnail = () => {
    if (videoRef.current) {
      const canvas = document.createElement('canvas');
      canvas.width = videoRef.current.videoWidth || 640;
      canvas.height = videoRef.current.videoHeight || 360;
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.drawImage(videoRef.current, 0, 0, canvas.width, canvas.height);
        setThumbnail(canvas.toDataURL('image/jpeg'));
      }
    }
  };

  const handleCropComplete = useCallback(
    (_: any, croppedAreaPixel: { x: number; y: number; width: number; height: number }) => {
      setCroppedAreaPixels(croppedAreaPixel);
    },
    []
  );

  const generateCroppedImage = useCallback(async () => {
    if (uploadedImage && croppedAreaPixels) {
      const canvas = document.createElement('canvas');
      const img = new Image();
      img.src = uploadedImage;

      img.onload = () => {
        const ctx = canvas.getContext('2d');
        canvas.width = croppedAreaPixels.width;
        canvas.height = croppedAreaPixels.height;

        ctx?.drawImage(
          img,
          croppedAreaPixels.x,
          croppedAreaPixels.y,
          croppedAreaPixels.width,
          croppedAreaPixels.height,
          0,
          0,
          croppedAreaPixels.width,
          croppedAreaPixels.height
        );

        const croppedImage = canvas.toDataURL('image/jpeg');
        setThumbnail(croppedImage);
        setCropping(false);
      };
    }
  }, [uploadedImage, croppedAreaPixels]);

  const handleSubmit = () => {
    if (videoFile && thumbnail) {
      const newVideoData: VideoData = {
        id: Date.now().toString(),
        url: videoURL || '',
        thumbnail,
        name: videoFile.name,
      };
      const updatedVideos = [...storedVideos, newVideoData];
      setStoredVideos(updatedVideos);
      localStorage.setItem('videos', JSON.stringify(updatedVideos));
      setDialogOpen(true); // Open the dialog on successful save
      setVideoFile(null);
      setVideoURL('');
      setThumbnail('');
      
    }
  };

  const handleDialogClose = () => {
    setDialogOpen(false);
  };
  return (
    <Box sx={{ p: 2 }}>
      <Card>
        <CardContent>
          <Typography variant="h5" sx={{ mb: 2 }}>
            Upload Video with Custom or Generated Thumbnail
          </Typography>

          {/* Upload Video */}

          <Grid container columnSpacing={5} mt={3}>
            <Grid item lg={6}>
              <Button sx={{ mt: 5, mb: 2 }} variant="contained" component="label" startIcon={<CloudUploadIcon />}>
                Upload Video
                <input hidden accept="video/*" type="file" onChange={handleVideoUpload} />
              </Button>
            </Grid>

            <Grid item lg={6}>
              {videoURL && (
                <div>
                  <Typography variant="body1" sx={{ mb: 1 }}>
                    Or Upload Custom Thumbnail:
                  </Typography>
                  <Button variant="contained" component="label">
                    Upload Thumbnail
                    <input hidden accept="image/*" type="file" onChange={handleThumbnailUpload} />
                  </Button>
                </div>
              )}
            </Grid>
            <Grid item lg={6}>
              {videoURL && <Typography variant="body1">Selected Video: {videoFile?.name}</Typography>}
            </Grid>

            <Grid item lg={6}>
              <Typography variant="body1">
                {thumbnail ? 'Current Thumbnail:' : 'Thumbnail will appear here.'}
              </Typography>
            </Grid>

            {/* Video Preview and Generate Thumbnail */}
            <Grid item lg={6}>
              {videoURL && typeof videoURL === 'string' && (
                <Box>
                  <video ref={videoRef} src={videoURL} controls width="100%" height="400px" />
                  <Button variant="outlined" sx={{ mt: 2 }} onClick={captureThumbnail}>
                    Generate Thumbnail
                  </Button>
                </Box>
              )}
            </Grid>

            {/* Upload Custom Thumbnail */}
            <Grid item lg={6}>
              {thumbnail && (
                <Box>
                  <CardMedia
                    component="img"
                    image={thumbnail}
                    alt="Thumbnail Preview"
                    sx={{ height: '400px', width: '100%', objectFit: 'cover', border: '1px solid #ccc' }}
                  />
                </Box>
              )}
            </Grid>
          </Grid>

          {/* Submit Button */}
          <Box sx={{ mt: 3 }}>
            <Button variant="contained" color="primary" onClick={handleSubmit} disabled={!videoFile || !thumbnail}>
              Submit
            </Button>
          </Box>
        </CardContent>
      </Card>
      {/* Cropping Modal */}
      {cropping && uploadedImage && (
        <Dialog open={cropping} onClose={() => setCropping(false)}>
          <DialogTitle>Crop Thumbnail</DialogTitle>
          <DialogContent>
            <Box sx={{ position: 'relative', width: 400, height: 400 }}>
              <Cropper
                image={uploadedImage}
                crop={crop}
                zoom={zoom}
                aspect={16 / 9}
                onCropChange={setCrop}
                onZoomChange={setZoom}
                onCropComplete={handleCropComplete}
              />
            </Box>
            <Slider
              value={zoom}
              min={1}
              max={3}
              step={0.1}
              onChange={(e, newValue) => setZoom(typeof newValue === 'number' ? newValue : zoom)}
              sx={{ mt: 2 }}
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setCropping(false)}>Cancel</Button>
            <Button onClick={generateCroppedImage} variant="contained" color="primary">
              Crop
            </Button>
          </DialogActions>
        </Dialog>
      )}

      {/* Success Dialog */}
      <Dialog open={dialogOpen} onClose={() => setCropping(false)}>
        <DialogTitle>Success</DialogTitle>
        <DialogContent>
          <DialogContentText>Video and thumbnail saved successfully!</DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDialogClose} color="primary">
            OK
          </Button>
        </DialogActions>
      </Dialog>

      {/* Display Stored Videos */}
      {storedVideos.length > 0 && (
        <Box sx={{ mt: 4 }}>
          <Typography variant="h6">Stored Videos:</Typography>
          <Grid container spacing={5}>
            {storedVideos.map((video) => {
              return (
                <Grid item lg={6} key={video.id}>
                  <Card sx={{ mt: 2 }}>
                    <CardContent>
                      <Typography variant="body1">{video.name}</Typography>
                      <ReactPlayer playing url={video.url} light={video.thumbnail || false} controls width="100%" />
                    </CardContent>
                  </Card>
                </Grid>
              );
            })}
          </Grid>
        </Box>
      )}
    </Box>
  );
};

export default VideoUpload;
