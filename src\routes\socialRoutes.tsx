import Loadable from '@/components/shared/Loadable';
import StaffActivityReport from '@/features/StaffManagement/StaffActivityReport/StaffActivityReport';

import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';

const SocialIndex = Loadable(lazy(() => import('@/pages/SocialIndex')));

export const socialRoutes: RouteObject[] = [
  {
    path: '/social',
    element: <SocialIndex />,
  },
];
