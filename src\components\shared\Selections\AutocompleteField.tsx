import * as React from 'react';
import Chip from '@mui/material/Chip';
import Autocomplete from '@mui/material/Autocomplete';
import TextField from '@mui/material/TextField';
import Stack from '@mui/material/Stack';
import { useTheme } from '@mui/material';
import useSettings from '@/hooks/useSettings';
import styled from 'styled-components';

const AutocompleteRoot = styled.div`
  .MuiAutocomplete-tag {
    margin: 1px;
  }
`;
type AutocompleteFieldProps = {
  multiple?: boolean | undefined;
  options: any[];
  placeholder?: string;
  getOptionLabel?: ((option: any) => string) | undefined;
  renderTags?: (value: any[], getTagProps: any) => React.ReactNode;
};
export default function AutocompleteField({
  multiple,
  options,
  getOptionLabel,
  renderTags,
  placeholder,
}: AutocompleteFieldProps) {
  // const theme = useTheme();
  // const isLight = useSettings().themeMode === 'light';

  return (
    <AutocompleteRoot>
      <Stack spacing={3} sx={{ width: 350 }}>
        <Autocomplete
          multiple={multiple}
          id="tags-outlined"
          limitTags={1}
          // getLimitTagsText={1}
          options={options}
          // disableCloseOnSelect
          // forcePopupIcon={false}
          openOnFocus={false}
          disablePortal
          getOptionLabel={getOptionLabel}
          renderTags={renderTags}
          filterSelectedOptions
          renderInput={(params) => <TextField variant="outlined" {...params} placeholder={placeholder} />}
        />
      </Stack>
    </AutocompleteRoot>
  );
}
