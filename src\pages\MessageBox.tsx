import * as React from 'react';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';
import styled from 'styled-components';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import QuickSend from '@/features/MessageBox/QuickSend/QuickSend';
import SmsTemplete from '@/features/MessageBox/SmsTemplate/SmsTemplete';
import SendToOthers from '@/features/MessageBox/SendToOthers';
import DeliveryReport from '@/features/MessageBox/DeliveryReport/DeliveryReport';
import { Link, useLocation, useOutletContext } from 'react-router-dom';
import Page from '@/components/shared/Page';
import QuickSendNew from '@/features/MessageBox/QuickSend/QuickSend2';
import { useDispatch } from 'react-redux';
import { flushStore } from '@/store/flush.slice';
import { OutletContextType } from '@/types/Common';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const MessageBoxRoot = styled.div`
  min-height: calc(100vh - ${TOP_BAR_HEIGHT});

  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};

  .tab {
    background-color: ${(props) => (props.theme.themeMode === 'light' ? '#fff' : props.theme.palette.grey[800])};
    /* @media screen and (min-width: 1414px) {
      width: 100%;
    }
    @media screen and (max-width: 720px) {
    } */
    width: 100%;
  }
  .Mui-selected {
    color: ${(props) => props.theme.palette.primary.main};
  }
`;

function TabPanel({ children, value, index }: TabPanelProps) {
  return (
    <Box
      sx={{ paddingTop: '3rem' }}
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
    >
      {value === index && <Box>{children}</Box>}
    </Box>
  );
}
function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`,
  };
}
export default function MessageBox() {
  const { topBarHeight } = useOutletContext<OutletContextType>();
  const location = useLocation();
  const [value, setValue] = React.useState(0);

  const dispatch = useDispatch();

  React.useEffect(() => {
    dispatch(flushStore());
    console.log('Flush Store');
  }, [dispatch, value]);

  React.useEffect(() => {
    const currentPath = location.pathname;
    const routePaths = [
      '/message-box/quick-send',
      '/message-box/quick-send2',
      '/message-box/sms-template',
      '/message-box/send-others',
      '/message-box/delivery-report',
    ];
    const index = routePaths.indexOf(currentPath);
    if (index !== -1) {
      setValue(index);
    }
  }, [location]);

  return (
    <Page title="">
      <MessageBoxRoot>
        <Tabs
          className="tab"
          variant="scrollable"
          scrollButtons
          allowScrollButtonsMobile
          value={value}
          aria-label="basic tabs"
          sx={{
            ml: '0px',
            borderBottom: 1,
            borderColor: 'divider',
            position: 'fixed',
            top: `${topBarHeight}px`,
            zIndex: 1,
            width: '100%',
          }}
        >
          <Tab {...a11yProps(0)} label="Quick Send" component={Link} to="/message-box/quick-send" />
          <Tab {...a11yProps(1)} label="Quick Send2" component={Link} to="/message-box/quick-send2" />
          <Tab {...a11yProps(2)} label="SMS Template" component={Link} to="/message-box/sms-template" />
          <Tab {...a11yProps(3)} label="Send to Others" component={Link} to="/message-box/send-others" />
          <Tab {...a11yProps(4)} label="Delivery Report" component={Link} to="/message-box/delivery-report" />
        </Tabs>
        <TabPanel value={value} index={0}>
          <QuickSend />
        </TabPanel>
        <TabPanel value={value} index={1}>
          <QuickSendNew />
        </TabPanel>
        <TabPanel value={value} index={2}>
          <SmsTemplete />
        </TabPanel>
        <TabPanel value={value} index={3}>
          <SendToOthers />
        </TabPanel>
        <TabPanel value={value} index={4}>
          <DeliveryReport />
        </TabPanel>
      </MessageBoxRoot>
    </Page>
  );
}
