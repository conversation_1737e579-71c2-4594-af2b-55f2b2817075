import React, { useState } from 'react';
import {
  Box,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  IconButton,
  Tooltip,
  Typography,
  Stack,
  Snackbar,
  Alert,
} from '@mui/material';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';

const CopyItem = ({
  text,
  onCopy,
  copiedText,
}: {
  text: string;
  onCopy: (value: string) => void;
  copiedText: string;
}) => {
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(text);
      onCopy(text);
    } catch (err) {
      console.error('Copy failed', err);
    }
  };

  return (
    <Stack direction="row" alignItems="center" spacing={1}>
      <Typography
        sx={{
          px: 1,
          py: 0.5,
          borderRadius: 1,
          bgcolor: copiedText === text ? 'green' : 'transparent',
          color: copiedText === text ? 'white' : 'inherit',
          border: text === '@mountseenaenglishschool.com' || text === 'Mount Seena English School' ? 1 : 0,
          borderColor: text === '@mountseenaenglishschool.com' || text === 'Mount Seena English School' ? 'green' : '',
          transition: 'background-color 0.3s, color 0.3s',
        }}
      >
        {text}
      </Typography>
      <Tooltip title="Copy">
        <IconButton size="small" sx={{ border: 1 }} onClick={handleCopy}>
          <ContentCopyIcon fontSize="small" />
        </IconButton>
      </Tooltip>
    </Stack>
  );
};

export default function CopyListPopup() {
  const [open, setOpen] = useState(false);
  const [snackOpen, setSnackOpen] = useState(false);
  const [copiedText, setCopiedText] = useState('');

  const handleCopy = (value: string) => {
    setCopiedText(value);
    setSnackOpen(true);
  };

  return (
    <Box sx={{ p: 0 }}>
      <Button variant="contained" onClick={() => setOpen(true)}>
        Copy List
      </Button>

      <Dialog open={open} onClose={() => setOpen(false)}>
        <DialogTitle>Copyable Items</DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
            {/* <CopyItem text="@mountseenapublicschool.com" onCopy={handleCopy} copiedText={copiedText} /> */}
            {/* <CopyItem text="@mountseenaenglishschool.com" onCopy={handleCopy} copiedText={copiedText} /> */}
            <CopyItem text="@assisiemhss.com" onCopy={handleCopy} copiedText={copiedText} />
            <CopyItem text="Passdaily$2030" onCopy={handleCopy} copiedText={copiedText} />

            <u>
              <b>Scope:</b>
            </u>
            <CopyItem text="meeting:read:meeting:admin" onCopy={handleCopy} copiedText={copiedText} />
            <CopyItem text="meeting:write:meeting:admin" onCopy={handleCopy} copiedText={copiedText} />
            <CopyItem text="meeting:write:meeting:master" onCopy={handleCopy} copiedText={copiedText} />
            <CopyItem text="user:write:user:master" onCopy={handleCopy} copiedText={copiedText} />
            <CopyItem text="user:write:user:admin" onCopy={handleCopy} copiedText={copiedText} />

            <u>
              <b>SignUp Info:</b>
            </u>
            {/* <CopyItem text="Mount Seena Public School" onCopy={handleCopy} copiedText={copiedText} /> */}
            {/* <CopyItem text="Mount Seena English School" onCopy={handleCopy} copiedText={copiedText} /> */}
            {/* <CopyItem text="Nagaripuram Post, Pathirippala" onCopy={handleCopy} copiedText={copiedText} /> */}
            {/* <CopyItem text="Palakkad" onCopy={handleCopy} copiedText={copiedText} /> */}
            {/* <CopyItem text="678642" onCopy={handleCopy} copiedText={copiedText} /> */}
            {/* <CopyItem text="http://mountseenapublicschool.com" onCopy={handleCopy} copiedText={copiedText} /> */}
            <CopyItem
              text="Assisi English Medium Higher Secondary School"
              onCopy={handleCopy}
              copiedText={copiedText}
            />
            <CopyItem text="Assisi Bhavan, Kanjikode" onCopy={handleCopy} copiedText={copiedText} />
            <CopyItem text="Palakkad" onCopy={handleCopy} copiedText={copiedText} />
            <CopyItem text="678621" onCopy={handleCopy} copiedText={copiedText} />
            <CopyItem text="http://assisiemhss.com" onCopy={handleCopy} copiedText={copiedText} />

            <u>
              <b>Details:</b>
            </u>
            <CopyItem
              text="For Taking Zoom Live Classes, and For Join Meeting for students"
              onCopy={handleCopy}
              copiedText={copiedText}
            />
            {/* <CopyItem text="Mount Seena Public School" onCopy={handleCopy} copiedText={copiedText} /> */}
            {/* <CopyItem text="Mount Seena English School" onCopy={handleCopy} copiedText={copiedText} /> */}
            <CopyItem
              text="Assisi English Medium Higher Secondary School"
              onCopy={handleCopy}
              copiedText={copiedText}
            />
            <CopyItem text="Anver.s" onCopy={handleCopy} copiedText={copiedText} />
            <CopyItem text="<EMAIL>" onCopy={handleCopy} copiedText={copiedText} />
          </Box>
        </DialogContent>
      </Dialog>

      <Snackbar
        open={snackOpen}
        autoHideDuration={2000}
        onClose={() => setSnackOpen(false)}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert severity="success" sx={{ width: '100%' }} onClose={() => setSnackOpen(false)}>
          Copied: <strong>{copiedText}</strong>
        </Alert>
      </Snackbar>
    </Box>
  );
}
