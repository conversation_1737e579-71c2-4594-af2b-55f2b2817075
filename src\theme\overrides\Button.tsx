import useSettings from '@/hooks/useSettings';
import { Components, Theme } from '@mui/material';

export default function Button(theme: Theme): Components<Theme> {
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  return {
    MuiButton: {
      styleOverrides: {
        root: {
          '&:hover': {
            boxShadow: 'none',
          },
          fontFamily: 'Poppins Regular',
          fontWeight: 500,
        },
        sizeLarge: {
          height: 48,
          fontFamily: 'Poppins Regular',
        },
        // contained
        containedInherit: {
          color: theme.palette.grey[800],
          boxShadow: theme.customShadows.z8,
          '&:hover': {
            backgroundColor: theme.palette.grey[400],
          },
        },
        containedPrimary: {
          boxShadow: 'none',
          '&:hover': {
            boxShadow: theme.customShadows.primary,
          },
          '&.MuiLoadingButton-root.Mui-disabled': {
            backgroundColor: theme.palette.primary.main,
            color: '#5e5d5d',
          },
        },
        containedSecondary: {
          backgroundColor: theme.palette.secondary.main,
          boxShadow: 'none',
          color: theme.palette.common.white,
          '&:hover': {
            backgroundColor: theme.palette.secondary.light,
            boxShadow: theme.customShadows.secondary,
          },
        },

        containedInfo: {
          backgroundColor: theme.palette.info.main,
          color: theme.palette.common.white,
          boxShadow: 'none',
        },
        containedSuccess: {
          backgroundColor: theme.palette.success.main,
          color: theme.palette.common.white,
          boxShadow: 'none',
          '&:hover': {
            boxShadow: theme.customShadows.success,
          },
        },
        containedWarning: {
          backgroundColor: theme.palette.warning.main,
          color: theme.palette.common.white,
          boxShadow: 'none',
          '&:hover': {
            boxShadow: theme.customShadows.warning,
          },
        },
        containedError: {
          backgroundColor: theme.palette.error.main,
          color: theme.palette.common.white,
          boxShadow: 'none',
          '&:hover': {
            boxShadow: theme.customShadows.error,
          },
        },
        // outlined
        outlinedInherit: {
          border: `1px solid ${theme.palette.grey[500_32]}`,
          '&:hover': {
            backgroundColor: theme.palette.action.hover,
          },
        },
        outlinedPrimary: {
          backgroundColor: isLight ? theme.palette.primary.lighter : theme.palette.grey[800],
          border: `1px solid ${theme.palette.primary.main}`,
        },
        outlinedSuccess: {
          // backgroundColor: isLight ? theme.palette.success.lighter : theme.palette.success.lighter,
          color: theme.palette.success.main,
          border: `1px solid ${theme.palette.success.main}`,
        },
        textInherit: {
          '&:hover': {
            backgroundColor: theme.palette.action.hover,
          },
        },
      },
    },
  };
}
