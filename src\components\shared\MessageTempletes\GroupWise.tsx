/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/alt-text */
import React, { FormEvent, useCallback, useState } from 'react';
import {
  Divider,
  Grid,
  Paper,
  Stack,
  Button,
  Typography,
  Box,
  FormControl,
  MenuItem,
  useTheme,
  Select,
  SelectChangeEvent,
  Tooltip,
} from '@mui/material';
import styled from 'styled-components';
import useAuth from '@/hooks/useAuth';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useAppSelector } from '@/hooks/useAppSelector';
import { GroupWiseDataType, SendToGroupWiseType } from '@/types/MessageBox';
import {
  getGroupWiseListData,
  getGroupWiseListStatus,
  getMessageTempSubmitting,
  getYearData,
} from '@/config/storeSelectors';
import { fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import LoadingButton from '@mui/lab/LoadingButton';
import { fetchGroupWise, messageSendToGroupWise } from '@/store/MessageBox/messageBox.thunks';
import ErrorMsg from '@/assets/MessageIcons/error-message.gif';
import ErrorMsg1 from '@/assets/MessageIcons/error-message1.gif';
import ErrorMsg2 from '@/assets/MessageIcons/error-message2.gif';
import errorIcon from '@/assets/MessageIcons/error-message.json';
import successIcon from '@/assets/MessageIcons/success.json';
import SendIcon from '@mui/icons-material/Send';
import Lottie from 'lottie-react';
import SuccessMsg from '@/assets/MessageIcons/message-success.gif';
import { SendPopupProps } from '@/types/Common';
import { voiceMessageSendToGroupWise } from '@/store/VoiceMessage/voiceMessage.thunk';
import { SendVoiceMessageToGroupWiseRequest } from '@/types/VoiceMessage';
import { SuccessIcon } from '@/theme/overrides/CustomIcons';
import { SuccessMessage } from '../Popup/SuccessMessage';
import DataTable, { DataTableColumn } from '../TableComponents/DataTable';
import { ErrorMessage } from '../Popup/ErrorMessage';
import { useConfirm } from '../Popup/Confirmation';

const GroupWiseRoot = styled.div`
  width: 100%;
  padding: 0.5rem 1.5rem;
`;

export type GroupWiseListRequest = {
  adminId: number;
  academicId: number;
};

interface GroupWiseDataWithStatus extends GroupWiseDataType {
  sendStatus: 'Success' | 'Failed' | '' | undefined;
}

function GroupWise({ messageId, voiceId, isSubmitting, templateId }: SendPopupProps) {
  const { user } = useAuth();
  const theme = useTheme();
  const dispatch = useAppDispatch();
  const { confirm } = useConfirm();
  const adminId: number = user ? user.accountId : 0;
  const [selectedRows, setSelectedRows] = useState<GroupWiseDataWithStatus[]>([]);
  const [sendResults, setSendResults] = useState<GroupWiseDataWithStatus[]>([]);
  const GroupWiseListData = useAppSelector(getGroupWiseListData);
  const GroupWiseListStatus = useAppSelector(getGroupWiseListStatus);
  const YearData = useAppSelector(getYearData);
  const defaultYearId = 10;
  const [academicYearFilter, setAcademicYearFilter] = useState(defaultYearId);
  const [selectedData, setSelectedData] = useState<GroupWiseDataWithStatus[]>([]);
  const [individualSendLoadingMap, setIndividualSendLoadingMap] = useState<Record<string, boolean>>({});
  const [isLoading, setIsloading] = useState<boolean>(false);
  const [errMsgTooltip, setErrMsgTooltip] = useState('');
  const [showSuccessMap, setShowSuccessMap] = useState<{ [key: string]: boolean }>({});
  const [disabledCheckBoxes, setDisabledCheckBoxes] = useState<boolean[]>([]);
  const initialGroupWiseListRequest = React.useMemo(
    () => ({
      adminId,
      academicId: defaultYearId,
    }),
    [adminId, defaultYearId]
  );
  const currentGroupWiseListRequest = React.useMemo(
    () => ({
      adminId,
      academicId: academicYearFilter,
    }),
    [adminId, academicYearFilter]
  );
  const loadGroupWiseList = useCallback(
    async (request: GroupWiseListRequest) => {
      try {
        const data = await dispatch(fetchGroupWise(request)).unwrap();
        const dataWithStatus = data.map((item) => ({
          ...item,
          sendStatus: sendResults.find((i) => i.groupId === item.groupId)?.sendStatus,
        }));
        setSelectedData(dataWithStatus);
        setSelectedRows([]);
      } catch (error) {
        console.error('Error loading Group Wise list:', error);
      }
    },
    [dispatch, setSelectedData, sendResults]
  );

  React.useEffect(() => {
    dispatch(fetchYearList(adminId));
    // loadGroupWiseList(initialGroupWiseListRequest);
  }, [dispatch, adminId, loadGroupWiseList, initialGroupWiseListRequest, academicYearFilter]);

  const handleSubmit = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      loadGroupWiseList(currentGroupWiseListRequest);
      // setSelectedRows([]);
    },
    [loadGroupWiseList, currentGroupWiseListRequest]
  );

  const handleReset = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      setAcademicYearFilter(parseInt(YearData[0].accademicId, 10));
      setDisabledCheckBoxes([]);
      loadGroupWiseList(currentGroupWiseListRequest);
    },
    [YearData, loadGroupWiseList, currentGroupWiseListRequest]
  );
  const handleCancel = () => {
    loadGroupWiseList(currentGroupWiseListRequest);
    setDisabledCheckBoxes([]);
  };

  // Send Message Multiple //
  const handleSendMessage = useCallback(async () => {
    setIsloading(true);
    try {
      if (!selectedRows || selectedRows.length === 0) {
        const errorMessage = <ErrorMessage icon={ErrorMsg1} message="Please select atleast one group to sent" />;
        await confirm(errorMessage, 'Group not selected', { okLabel: 'Ok', showOnlyOk: true });
        return;
      }
      const sendRequests: SendToGroupWiseType[] = await Promise.all(
        selectedRows?.map(async (item) => {
          const { ...rest } = item;
          const sendReq = { messageId, adminId, accademicId: 10, ...rest };
          return sendReq;
        }) || []
      );
      const messageSendCount = sendRequests.length;

      sendRequests.map((row) => setIndividualSendLoadingMap((prevMap) => ({ ...prevMap, [row.groupId]: true })));

      const actionResult = await dispatch(messageSendToGroupWise(sendRequests));

      sendRequests.map((row) => setIndividualSendLoadingMap((prevMap) => ({ ...prevMap, [row.groupId]: false })));

      if (actionResult && Array.isArray(actionResult.payload)) {
        const results: GroupWiseDataWithStatus[] = actionResult.payload;

        setSendResults([...sendResults, ...results]);

        const errorMessages = results.find((result) => result.sendStatus === 'Failed');
        const successMessages = results.find((result) => result.sendStatus === 'Success');
        if (!errorMessages) {
          const successMessage = (
            <SuccessMessage icon={SuccessMsg} message={`Message sent to ${messageSendCount} Recipient.`} />
          );
          await confirm(successMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        } else if (!successMessages) {
          setErrMsgTooltip('No messages sent to any Class Sections, Please check the numbers and Try again');
          const errorMessage = (
            <ErrorMessage
              icon={ErrorMsg2}
              message="No messages sent to any Class Sections, Please check the numbers and Try again"
            />
          );
          await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        } else {
          setErrMsgTooltip('Error sending messages to some Class Sections, Please recheck and Try again');
          const errorMessage = (
            <ErrorMessage
              icon={ErrorMsg}
              message="Error sending messages to some Class Sections, Please recheck and Try again"
            />
          );
          await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        }
        const dataResult = selectedData.map((item) => ({
          ...item,
          sendStatus:
            item.sendStatus !== 'Success' && item.sendStatus !== 'Failed'
              ? results.find((result) => result.groupId === item.groupId)?.sendStatus
              : item.sendStatus,
        }));

        // setSelectedRows([]);
        setSelectedData(dataResult);
      } else {
        setErrMsgTooltip('Error while sending messages, Please Try again');
        const errorMessage = <ErrorMessage icon={ErrorMsg} message="Error while sending messages, Please Try again" />;
        await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
      }
      setSelectedRows([]);

      sendRequests.map((row) => setShowSuccessMap((prevMap) => ({ ...prevMap, [row.groupId]: true })));
      // Update disabled state for each selected row
      const updatedDisabledCheckBoxes = selectedData.map((selectedItem) =>
        selectedRows.includes(selectedItem)
          ? !disabledCheckBoxes[selectedData.indexOf(selectedItem)]
          : disabledCheckBoxes[selectedData.indexOf(selectedItem)]
      );

      setDisabledCheckBoxes(updatedDisabledCheckBoxes);

      setTimeout(() => {
        sendRequests.map((row) => setShowSuccessMap((prevMap) => ({ ...prevMap, [row.groupId]: false })));
      }, 5000);
    } catch (error) {
      console.error('Error sending messages:', error);
    }
  }, [
    dispatch,
    confirm,
    messageId,
    selectedRows,
    setSelectedRows,
    adminId,
    selectedData,
    sendResults,
    disabledCheckBoxes,
  ]);

  const handleSendMessageIndivitual = useCallback(
    async (row: GroupWiseDataWithStatus, rowIndex: number) => {
      setIsloading(false);
      try {
        const { ...rest } = row;

        // Check if the row is already loading, if yes, return
        if (individualSendLoadingMap[row.groupId]) {
          return;
        }

        // Set loading state for the specific row
        setIndividualSendLoadingMap((prevMap) => ({ ...prevMap, [row.groupId]: true }));

        const updatedDisabledCheckBoxes = [...disabledCheckBoxes];
        updatedDisabledCheckBoxes[rowIndex] = !updatedDisabledCheckBoxes[rowIndex];
        setDisabledCheckBoxes(updatedDisabledCheckBoxes);

        const sendRequests: SendToGroupWiseType[] = [{ messageId, adminId, accademicId: 10, ...rest }];

        const actionResult = await dispatch(messageSendToGroupWise(sendRequests));

        // Reset loading state after the action is complete
        setIndividualSendLoadingMap((prevMap) => ({ ...prevMap, [row.groupId]: false }));

        if (actionResult && Array.isArray(actionResult.payload)) {
          const results: GroupWiseDataWithStatus[] = actionResult.payload;

          // Update the sendResults state based on the previous state
          setSendResults((prevResults) => [...prevResults, ...results]);

          // Update the selectedData state with the new status based on the previous state
          setSelectedData((prevResults) =>
            prevResults.map((item) => {
              const resultForItem = results.find((result) => result.groupId === item.groupId);

              if (resultForItem) {
                return {
                  ...item,
                  sendStatus:
                    item.sendStatus !== 'Success' && item.sendStatus !== 'Failed'
                      ? resultForItem.sendStatus
                      : item.sendStatus,
                };
              }

              return item;
            })
          );
          setSelectedRows([]);

          const errorMessages = results.find((result) => result.sendStatus === 'Failed');
          if (errorMessages) {
            setErrMsgTooltip('No messages sent to any Class Sections, Please check the numbers and Try again');
          } else {
            setErrMsgTooltip('Error sending messages to some Class Sections, Please recheck and Try again');
          }
          setSelectedRows([]);
        } else {
          setErrMsgTooltip('Error while sending messages, Please Try again');
        }
        setShowSuccessMap((prevMap) => ({ ...prevMap, [row.groupId]: true }));

        setTimeout(() => {
          setShowSuccessMap((prevMap) => ({ ...prevMap, [row.groupId]: false }));
          // setIsButtonDisabledMap((prevMap) => ({ ...prevMap, [row.classSectionName]: true }));
        }, 5000);
      } catch (error) {
        console.error('Error sending messages:', error);
      }
    },
    [dispatch, messageId, adminId, setSendResults, individualSendLoadingMap, disabledCheckBoxes]
  );

  // Send Voice Message Multiple //
  const handleSendVoiceMessage = useCallback(async () => {
    setIsloading(true);
    try {
      if (!selectedRows || selectedRows.length === 0) {
        const errorMessage = <ErrorMessage icon={ErrorMsg1} message="Please select atleast one group to sent" />;
        await confirm(errorMessage, 'Group not selected', { okLabel: 'Ok', showOnlyOk: true });
        return;
      }
      const sendRequests: SendVoiceMessageToGroupWiseRequest[] = await Promise.all(
        selectedRows?.map(async (item) => {
          const { ...rest } = item;
          const sendReq = { voiceId, templateId, adminId, accademicId: 10, ...rest };
          return sendReq;
        }) || []
      );
      const messageSendCount = sendRequests.length;

      sendRequests.map((row) => setIndividualSendLoadingMap((prevMap) => ({ ...prevMap, [row.groupId]: true })));

      const actionResult = await dispatch(voiceMessageSendToGroupWise(sendRequests));

      sendRequests.map((row) => setIndividualSendLoadingMap((prevMap) => ({ ...prevMap, [row.groupId]: false })));

      if (actionResult && Array.isArray(actionResult.payload)) {
        const results: GroupWiseDataWithStatus[] = actionResult.payload;

        setSendResults([...sendResults, ...results]);

        const errorMessages = results.find((result) => result.sendStatus === 'Failed');
        const successMessages = results.find((result) => result.sendStatus === 'Success');
        if (!errorMessages) {
          const successMessage = (
            <SuccessMessage icon={SuccessMsg} message={`Message sent to ${messageSendCount} Recipient.`} />
          );
          await confirm(successMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        } else if (!successMessages) {
          setErrMsgTooltip('No messages sent to any Class Sections, Please check the numbers and Try again');
          const errorMessage = (
            <ErrorMessage
              icon={ErrorMsg2}
              message="No messages sent to any Class Sections, Please check the numbers and Try again"
            />
          );
          await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        } else {
          setErrMsgTooltip('Error sending messages to some Class Sections, Please recheck and Try again');
          const errorMessage = (
            <ErrorMessage
              icon={ErrorMsg}
              message="Error sending messages to some Class Sections, Please recheck and Try again"
            />
          );
          await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        }
        const dataResult = selectedData.map((item) => ({
          ...item,
          sendStatus:
            item.sendStatus !== 'Success' && item.sendStatus !== 'Failed'
              ? results.find((result) => result.groupId === item.groupId)?.sendStatus
              : item.sendStatus,
        }));

        // setSelectedRows([]);
        setSelectedData(dataResult);
      } else {
        setErrMsgTooltip('Error while sending messages, Please Try again');
        const errorMessage = <ErrorMessage icon={ErrorMsg} message="Error while sending messages, Please Try again" />;
        await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
      }
      setSelectedRows([]);

      sendRequests.map((row) => setShowSuccessMap((prevMap) => ({ ...prevMap, [row.groupId]: true })));
      // Update disabled state for each selected row
      const updatedDisabledCheckBoxes = selectedData.map((selectedItem) =>
        selectedRows.includes(selectedItem)
          ? !disabledCheckBoxes[selectedData.indexOf(selectedItem)]
          : disabledCheckBoxes[selectedData.indexOf(selectedItem)]
      );

      setDisabledCheckBoxes(updatedDisabledCheckBoxes);

      setTimeout(() => {
        sendRequests.map((row) => setShowSuccessMap((prevMap) => ({ ...prevMap, [row.groupId]: false })));
      }, 5000);
    } catch (error) {
      console.error('Error sending messages:', error);
    }
  }, [
    dispatch,
    confirm,
    voiceId,
    templateId,
    selectedRows,
    setSelectedRows,
    adminId,
    selectedData,
    sendResults,
    disabledCheckBoxes,
  ]);

  const handleSendVoiceMessageIndivitual = useCallback(
    async (row: GroupWiseDataWithStatus, rowIndex: number) => {
      setIsloading(false);
      try {
        const { ...rest } = row;

        // Check if the row is already loading, if yes, return
        if (individualSendLoadingMap[row.groupId]) {
          return;
        }

        // Set loading state for the specific row
        setIndividualSendLoadingMap((prevMap) => ({ ...prevMap, [row.groupId]: true }));

        const updatedDisabledCheckBoxes = [...disabledCheckBoxes];
        updatedDisabledCheckBoxes[rowIndex] = !updatedDisabledCheckBoxes[rowIndex];
        setDisabledCheckBoxes(updatedDisabledCheckBoxes);

        const sendRequests: SendVoiceMessageToGroupWiseRequest[] = [
          { voiceId, templateId, adminId, accademicId: 10, ...rest },
        ];

        const actionResult = await dispatch(voiceMessageSendToGroupWise(sendRequests));

        // Reset loading state after the action is complete
        setIndividualSendLoadingMap((prevMap) => ({ ...prevMap, [row.groupId]: false }));

        if (actionResult && Array.isArray(actionResult.payload)) {
          const results: GroupWiseDataWithStatus[] = actionResult.payload;

          // Update the sendResults state based on the previous state
          setSendResults((prevResults) => [...prevResults, ...results]);

          // Update the selectedData state with the new status based on the previous state
          setSelectedData((prevResults) =>
            prevResults.map((item) => {
              const resultForItem = results.find((result) => result.groupId === item.groupId);

              if (resultForItem) {
                return {
                  ...item,
                  sendStatus:
                    item.sendStatus !== 'Success' && item.sendStatus !== 'Failed'
                      ? resultForItem.sendStatus
                      : item.sendStatus,
                };
              }

              return item;
            })
          );
          setSelectedRows([]);

          const errorMessages = results.find((result) => result.sendStatus === 'Failed');
          if (errorMessages) {
            setErrMsgTooltip('No messages sent to any Class Sections, Please check the numbers and Try again');
          } else {
            setErrMsgTooltip('Error sending messages to some Class Sections, Please recheck and Try again');
          }
          setSelectedRows([]);
        } else {
          setErrMsgTooltip('Error while sending messages, Please Try again');
        }
        setShowSuccessMap((prevMap) => ({ ...prevMap, [row.groupId]: true }));

        setTimeout(() => {
          setShowSuccessMap((prevMap) => ({ ...prevMap, [row.groupId]: false }));
          // setIsButtonDisabledMap((prevMap) => ({ ...prevMap, [row.classSectionName]: true }));
        }, 5000);
      } catch (error) {
        console.error('Error sending messages:', error);
      }
    },
    [dispatch, voiceId, adminId, setSendResults, templateId, individualSendLoadingMap, disabledCheckBoxes]
  );
  const handleYearChange = (e: SelectChangeEvent) => {
    setAcademicYearFilter(parseInt(YearData.filter((item) => item.accademicId === e.target.value)[0].accademicId, 10));
    loadGroupWiseList({ ...currentGroupWiseListRequest, academicId: parseInt(e.target.value, 10) });
    setSelectedRows([]);
  };

  const getRowKey = useCallback((row: GroupWiseDataWithStatus) => row.groupId, []);

  const getStatusButton = (row: GroupWiseDataWithStatus, rowIndex: number) => (
    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
      {row.sendStatus === 'Failed' ? (
        <Tooltip title={errMsgTooltip} placement="left">
          <Stack>
            <Lottie animationData={errorIcon} loop={false} style={{ width: '30px' }} />
          </Stack>
        </Tooltip>
      ) : row.sendStatus === 'Success' ? (
        showSuccessMap[row.groupId] === true ? (
          <Stack>
            <Lottie animationData={successIcon} loop={false} style={{ width: '30px' }} />
          </Stack>
        ) : (
          <LoadingButton
            endIcon={<SuccessIcon color="success" />}
            variant="outlined"
            size="small"
            color="primary"
            disabled
            // disabled={isButtonDisabledMap[row.groupId]}
          >
            Sent
          </LoadingButton>
        )
      ) : !selectedRows.includes(row) ? (
        <LoadingButton
          endIcon={<SendIcon />}
          onClick={() => {
            if (messageId && messageId !== 0) {
              handleSendMessageIndivitual(row, rowIndex);
            } else {
              handleSendVoiceMessageIndivitual(row, rowIndex);
            }
          }}
          variant="outlined"
          size="small"
          color="primary"
          disabled={isSubmitting && isLoading}
          loading={individualSendLoadingMap[row.groupId]}
        >
          Send
        </LoadingButton>
      ) : (
        <LoadingButton
          endIcon={<SendIcon />}
          variant="outlined"
          size="small"
          color="primary"
          disabled
          loading={individualSendLoadingMap[row.groupId]}
        >
          Send
        </LoadingButton>
      )}
    </Box>
  );

  // const getStatusIcon = (row: ClassSectionWithStatus) => (
  //   <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
  //     {row.sendStatus === 'Failed' ? (
  //       <Tooltip title={errMsg} placement="right">
  //         <div>
  //           <FcCancel style={{ height: 20, width: 20 }} />
  //         </div>
  //       </Tooltip>
  //     ) : row.sendStatus === 'Success' ? (
  //       <FcOk style={{ height: 20, width: 20 }} />
  //     ) : null}
  //   </Box>
  // );

  const groupWiseListColumns: DataTableColumn<GroupWiseDataWithStatus>[] = [
    {
      name: 'groupId',
      dataKey: 'groupId',
      headerLabel: 'Sl.No',
      sortable: true,
    },
    {
      name: 'groupName',
      dataKey: 'groupName',
      headerLabel: 'Group Name',
    },
    {
      name: 'accademicTime',
      dataKey: 'accademicTime',
      headerLabel: 'Accademic Year',
    },
    {
      name: 'sendOption',
      headerLabel: 'Send Option',
      renderCell: getStatusButton,
    },
  ];
  return (
    <GroupWiseRoot>
      <Divider />
      <form noValidate onReset={handleReset} onSubmit={handleSubmit}>
        <Grid pb={2} pt={1} container spacing={3} alignItems="end">
          <Grid item lg={3} md={4} sm={6} xs={12}>
            <FormControl fullWidth>
              <Typography variant="h6" fontSize={14}>
                Select Year
              </Typography>
              <Select
                labelId="academicYearFilter"
                id="academicYearFilterSelect"
                value={academicYearFilter.toString()}
                onChange={handleYearChange}
                placeholder="Select Year"
              >
                {YearData.map((opt) => (
                  <MenuItem key={opt.accademicId} value={opt.accademicId}>
                    {opt.accademicTime}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item lg={2} md={4} sm={6} xs={12}>
            <FormControl fullWidth>
              <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                <Button variant="contained" color="secondary" type="reset" fullWidth>
                  Reset
                </Button>
                <Button variant="contained" color="primary" type="submit" fullWidth>
                  Search
                </Button>
              </Stack>
            </FormControl>
          </Grid>
        </Grid>
      </form>
      <Paper
        sx={{
          border: selectedData?.length === 0 ? `0px` : `1px solid ${theme.palette.grey[300]}`,
          width: '100%',
          overflow: 'auto',
        }}
      >
        <Box
          sx={{
            height: 'calc(100vh - 23.7rem)',
            width: { xs: selectedData?.length !== 0 ? '43.75rem' : '100%', md: '100%' },
          }}
        >
          <DataTable
            ShowCheckBox
            isSubmitting={isSubmitting}
            disabledCheckBox={disabledCheckBoxes}
            // tableRowSelect={disabledCheckBoxes}
            RowSelected
            setSelectedRows={setSelectedRows}
            selectedRows={selectedRows}
            columns={groupWiseListColumns}
            data={selectedData}
            getRowKey={getRowKey}
            fetchStatus={GroupWiseListStatus}
          />
        </Box>
      </Paper>
      <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 3 }}>
        {selectedData.length !== 0 && (
          <Stack spacing={2} direction="row">
            <Button disabled={isSubmitting} onClick={handleCancel} variant="contained" color="secondary">
              Cancel
            </Button>
            <LoadingButton
              fullWidth
              loadingPosition="start"
              endIcon={<SendIcon />}
              onClick={messageId && messageId !== 0 ? handleSendMessage : handleSendVoiceMessage}
              loading={isLoading && isSubmitting}
              variant="contained"
              color="primary"
              disabled={isSubmitting}
            >
              {isSubmitting && isLoading ? 'Sending...' : 'Send'}
            </LoadingButton>
          </Stack>
        )}
      </Box>
    </GroupWiseRoot>
  );
}

export default GroupWise;
