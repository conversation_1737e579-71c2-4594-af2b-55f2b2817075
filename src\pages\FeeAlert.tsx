/* eslint-disable prettier/prettier */
import * as React from 'react';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';
import Page from '@/components/shared/Page';
import styled from 'styled-components';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import { Link, useLocation, useOutletContext } from 'react-router-dom';
import UpdateFees from '@/features/FeeAlert/UpdateFees';
import FeeAlertList from '@/features/FeeAlert/FeeAlertList';
import FeeAlertPendingList from '@/features/FeeAlert/PendingList';
import { useDispatch } from 'react-redux';
import { flushStore } from '@/store/flush.slice';
import { OutletContextType } from '@/types/Common';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const FeeAlertRoot = styled.div`
  min-height: calc(100vh - ${TOP_BAR_HEIGHT});
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  .tab {
    background-color: ${(props) => (props.theme.themeMode === 'light' ? '#fff' : props.theme.palette.grey[800])};
  }
  .Mui-selected {
    color: ${(props) => props.theme.palette.primary.main};
  }
`;

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <Box
      sx={{ paddingTop: '3rem' }}
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </Box>
  );
}

function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`,
  };
}

export default function FeeAlert() {
  const { topBarHeight } = useOutletContext<OutletContextType>();
  const location = useLocation();
  const [value, setValue] = React.useState(0);

  const dispatch = useDispatch();

  React.useEffect(() => {
    dispatch(flushStore());
    console.log('Flush Store');
  }, [dispatch, value]);

  React.useEffect(() => {
    const currentPath = location.pathname;
    const routePaths = ['/fee-alert/update-fees', '/fee-alert/list', '/fee-alert/pending'];
    const index = routePaths.indexOf(currentPath);
    if (index !== -1) {
      setValue(index);
    }
  }, [location]);

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  return (
    <Page title="Fee Alert">
      <FeeAlertRoot>
        <Tabs
          className="tab"
          variant="scrollable"
          scrollButtons
          allowScrollButtonsMobile
          value={value}
          onChange={handleChange}
          aria-label="basic tabs"
          sx={{
            ml: '0px',
            borderBottom: 1,
            borderColor: 'divider',
            position: 'fixed',
             top: `${topBarHeight}px`,
            zIndex: 1,
            width: '100%',
          }}
        >
          <Tab {...a11yProps(0)} label="Update Fees" component={Link} to="/fee-alert/update-fees" />
          <Tab {...a11yProps(1)} label="Fee Alert" component={Link} to="/fee-alert/list" />
          <Tab {...a11yProps(2)} label="Fee Alert Pending" component={Link} to="/fee-alert/pending" />
        </Tabs>
        <TabPanel value={value} index={0}>
          <UpdateFees />
        </TabPanel>
        <TabPanel value={value} index={1}>
          <FeeAlertList />
        </TabPanel>
        <TabPanel value={value} index={2}>
          <FeeAlertPendingList />
        </TabPanel>
      </FeeAlertRoot>
    </Page>
  );
}
