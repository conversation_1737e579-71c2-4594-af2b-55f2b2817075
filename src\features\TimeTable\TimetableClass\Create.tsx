import React from 'react';
import { <PERSON>complete, Box, Button, Stack, TextField, Typography } from '@mui/material';
import { CLASS_SELECT, STATUS_SELECT, SUBJECT_SELECT } from '@/config/Selection';
import { CreateProps } from '@/types/Common';

export const Create = ({ onClose, open }: CreateProps) => {
  return (
    <Box mt={3}>
      <Stack sx={{ height: 'calc(100vh - 150px)' }}>
        <Typography variant="body2" fontSize={14} fontWeight={600}>
          Academic year
        </Typography>
        <Autocomplete
          fullWidth
          sx={{ mb: 2 }}
          value=""
          options={STATUS_SELECT}
          renderInput={(params) => <TextField {...params} placeholder="Select status" />}
        />
        <Typography variant="h6" fontSize={14}>
          Day
        </Typography>
        <Autocomplete
          fullWidth
          sx={{ mb: 2 }}
          value=""
          options={STATUS_SELECT}
          renderInput={(params) => <TextField {...params} placeholder="Select status" />}
        />
        <Typography variant="h6" fontSize={14}>
          Class
        </Typography>
        <Autocomplete
          sx={{ mb: 2 }}
          options={CLASS_SELECT}
          renderInput={(params) => <TextField {...params} placeholder="Select Class" />}
        />
        <Typography variant="h6" fontSize={14}>
          Period
        </Typography>
        <Autocomplete
          sx={{ mb: 2 }}
          options={Array.from({ length: 8 }, (_, index) => index + 1)}
          renderInput={(params) => <TextField {...params} placeholder="Select Period" />}
        />
        <Typography variant="h6" fontSize={14}>
          Subject
        </Typography>
        <Autocomplete
          sx={{ mb: 2 }}
          options={SUBJECT_SELECT}
          renderInput={(params) => <TextField {...params} placeholder="Select Type" />}
        />
        <Typography variant="h6" fontSize={14}>
          Status
        </Typography>
        <Autocomplete
          sx={{ mb: 2 }}
          options={STATUS_SELECT}
          renderInput={(params) => <TextField {...params} placeholder="Select Status" />}
        />
      </Stack>
      <Box sx={{ justifyContent: 'center', pt: 3, position: 'relative' }}>
        <Stack spacing={2} direction="row" sx={{}}>
          <Button onClick={onClose} fullWidth variant="contained" color="secondary">
            Cancel
          </Button>
          <Button
            onClick={open}
            // onKeyDown={toggleDrawer(false)}
            fullWidth
            variant="contained"
            color="primary"
          >
            Save
          </Button>
        </Stack>
      </Box>
    </Box>
  );
};
