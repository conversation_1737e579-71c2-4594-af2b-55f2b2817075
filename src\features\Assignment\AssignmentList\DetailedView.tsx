/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import {
  ButtonGroup,
  Divider,
  Paper,
  Stack,
  Typography,
  Box,
  Avatar,
  Button,
  Card,
  IconButton,
  Chip,
} from '@mui/material';
import styled, { useTheme } from 'styled-components';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import Page from '@/components/shared/Page';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import useSettings from '@/hooks/useSettings';
import Popup from '@/components/shared/Popup/Popup';
import ReportView from './ReportView/ReportView';

const DetailedViewRoot = styled.div`
  padding: 16px;
  .Card {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 40px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

export const data = [
  {
    slNo: 1,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'Alex',
    guardianName: 'Fionna Peter',
    guardianNumber: '+91-9856258745',
  },
  {
    slNo: 2,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'Mathew',
    guardianName: 'Fionna Peter',
    guardianNumber: '+91-9856258745',
  },
  {
    slNo: 3,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'Peter',
    guardianName: 'Fionna Peter',
    guardianNumber: '+91-9856258745',
  },
  {
    slNo: 4,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'Alex Mic',
    guardianName: 'Fionna Peter',
    guardianNumber: '+91-9856258745',
  },
  {
    slNo: 5,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'john',
    guardianName: 'Fionna Peter',
    guardianNumber: '+91-9856258745',
  },
  {
    slNo: 6,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'Micheal',
    guardianName: 'Fionna Peter',
    guardianNumber: '+91-9856258745',
  },
  {
    slNo: 7,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'jack',
    guardianName: 'Fionna Peter',
    guardianNumber: '+91-9856258745',
  },
  {
    slNo: 1,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'Alex',
    guardianName: 'Fionna Peter',
    guardianNumber: '+91-9856258745',
  },
  {
    slNo: 2,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'Mathew',
    guardianName: 'Fionna Peter',
    guardianNumber: '+91-9856258745',
  },
  {
    slNo: 3,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'Peter',
    guardianName: 'Fionna Peter',
    guardianNumber: '+91-9856258745',
  },
  {
    slNo: 4,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'Alex Mic',
    guardianName: 'Fionna Peter',
    guardianNumber: '+91-9856258745',
  },
  {
    slNo: 5,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'john',
    guardianName: 'Fionna Peter',
    guardianNumber: '+91-9856258745',
  },
  {
    slNo: 6,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'Micheal',
    guardianName: 'Fionna Peter',
    guardianNumber: '+91-9856258745',
  },
  {
    slNo: 7,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'jack',
    guardianName: 'Fionna Peter',
    guardianNumber: '+91-9856258745',
  },
  {
    slNo: 1,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'Alex',
    guardianName: 'Fionna Peter',
    guardianNumber: '+91-9856258745',
  },
  {
    slNo: 2,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'Mathew',
    guardianName: 'Fionna Peter',
    guardianNumber: '+91-9856258745',
  },
  {
    slNo: 3,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'Peter',
    guardianName: 'Fionna Peter',
    guardianNumber: '+91-9856258745',
  },
  {
    slNo: 4,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'Alex Mic',
    guardianName: 'Fionna Peter',
    guardianNumber: '+91-9856258745',
  },
  {
    slNo: 5,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'john',
    guardianName: 'Fionna Peter',
    guardianNumber: '+91-9856258745',
  },
  {
    slNo: 6,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'Micheal',
    guardianName: 'Fionna Peter',
    guardianNumber: '+91-9856258745',
  },
  {
    slNo: 7,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'jack',
    guardianName: 'Fionna Peter',
    guardianNumber: '+91-9856258745',
  },
];

const DefaultDetailedViewInfo: any = {
  studentName: 'Peter',
  assignment: 'English Poets',
  class: 'VII-A',
  description: 'Williams Hills',
  uploadFiles: '',
  outOffMark: '50',
};

function DetailedView({ onBackClick }: any) {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const [tab, setTab] = useState('View List');
  const [reportViewPopup, setReportViewPopup] = useState(false);

  const handleClickReport = () => {
    setReportViewPopup(true);
  };
  const getRowKey = useCallback((row: any) => row.examId, []);
  const detailedViewColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'slNo',
        dataKey: 'slNo',
        headerLabel: 'Sl.No',
        sortable: true,
      },
      {
        name: 'studentName',
        headerLabel: 'Student Name',
        renderCell: (row) => {
          return (
            <Stack direction="row" gap={1} alignItems="center">
              <Avatar alt="" src="/static/images/avatar/1.jpg" />
              <Typography variant="subtitle2">{row.studentName}</Typography>
            </Stack>
          );
        },
      },
      {
        name: 'class',
        dataKey: 'class',
        headerLabel: 'Class',
      },
      {
        name: 'guardianName',
        dataKey: 'guardianName',
        headerLabel: 'Guardian Name',
      },
      {
        name: 'guardianNumber',
        dataKey: 'guardianNumber',
        headerLabel: 'Guardian Number',
      },
      {
        name: 'actions',
        headerLabel: 'Actions',
        renderCell: (row) => {
          return <Chip clickable onClick={handleClickReport} size="small" label="Report" color="secondary" />;
        },
      },
    ],
    []
  );
  const unattendedListColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'slNo',
        dataKey: 'slNo',
        headerLabel: 'Sl.No',
        sortable: true,
      },
      {
        name: 'studentName',
        headerLabel: 'Student Name',
        renderCell: (row) => {
          return (
            <Stack direction="row" gap={1} alignItems="center">
              <Avatar alt="" src="/static/images/avatar/1.jpg" />
              <Typography variant="subtitle2">{row.studentName}</Typography>
            </Stack>
          );
        },
      },
      {
        name: 'class',
        dataKey: 'class',
        headerLabel: 'Class',
      },
      {
        name: 'guardianName',
        dataKey: 'guardianName',
        headerLabel: 'Guardian Name',
      },
      {
        name: 'guardianNumber',
        dataKey: 'guardianNumber',
        headerLabel: 'Guardian Number',
      },
    ],
    []
  );

  return (
    <Page title="Detailed View">
      <DetailedViewRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" alignItems="center" gap={3} pb={2}>
            <Button
              onClick={onBackClick}
              sx={{ minWidth: '0px', borderRadius: '20px' }}
              size="small"
              variant="contained"
            >
              <ArrowBackIcon fontSize="small" />
            </Button>
            <Stack direction="row" spacing={1}>
              <Typography
                variant="h6"
                fontSize={16}
                onClick={() => setTab('View List')}
                sx={{ cursor: 'pointer' }}
                color={tab === 'View List' ? 'Black' : 'GrayText'}
              >
                Detailed View
              </Typography>
              <Typography variant="h6" fontSize={16}>
                /
              </Typography>
              <Typography
                variant="h6"
                fontSize={16}
                onClick={() => setTab('Unview List')}
                sx={{ cursor: 'pointer' }}
                color={tab === 'Unview List' ? 'Black' : 'GrayText'}
              >
                Un Submitted Student
              </Typography>
            </Stack>
            <Divider />
          </Stack>
          <div className="card-main-body">
            <Paper className="card-table-container">
              {tab === 'View List' ? (
                <DataTable columns={detailedViewColumns} data={data} getRowKey={getRowKey} fetchStatus="success" />
              ) : (
                <DataTable columns={unattendedListColumns} data={data} getRowKey={getRowKey} fetchStatus="success" />
              )}
            </Paper>
          </div>
        </Card>
      </DetailedViewRoot>
      <Popup
        size="md"
        title="Report View"
        state={reportViewPopup}
        onClose={() => setReportViewPopup(false)}
        popupContent={<ReportView onClose={() => setReportViewPopup(false)} />}
      />
    </Page>
  );
}

export default DetailedView;
