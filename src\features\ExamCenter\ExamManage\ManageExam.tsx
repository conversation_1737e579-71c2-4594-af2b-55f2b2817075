/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Typography,
  Card,
  Collapse,
  FormControl,
  Select,
} from '@mui/material';
import deleteBin from '@/assets/ManageFee/deleteBin.json';
import styled, { useTheme } from 'styled-components';
import SearchIcon from '@mui/icons-material/Search';
import { IoIosArrowUp } from 'react-icons/io';
import { ManageExamData } from '@/config/TableData';
import Popup from '@/components/shared/Popup/Popup';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import { MdAdd } from 'react-icons/md';
import MenuEditDelete from '@/components/shared/Selections/MenuEditDelete';
import { Create } from './Create';
import { MenuItem } from '@mui/material';
import { FEE_TYPE_ID_OPTIONS } from '@/config/Selection';
import { Tooltip } from '@mui/material';
import { IconButton } from '@mui/material';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { deleteTermFeeList } from '@/store/ManageFee/manageFee.thunks';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';

const status = ['Publish', 'Unpublish'];

const ManageExamRoot = styled.div`
  padding: 1rem;
  .Card {
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }
  }
  .btn_status {
    border-radius: 20px;
    width: 7.8125rem;
    font-size: 10px;
    font-weight: 600;
  }
`;

function ManageExam() {
  const theme = useTheme();
  const { confirm } = useConfirm();
  const dispatch = useAppDispatch();
  const [Delete, setDelete] = React.useState(false);
  const [drawerOpen, setDrawerOpen] = React.useState<boolean>(false);
  const [EditData, setEditData] = React.useState();
  const [showFilter, setShowFilter] = useState(true);

  const handleClickDelete = () => setDelete(true);
  const handleClickCloseDelete = () => setDelete(false);
  const update = (data: any) => {
    setEditData(data);
  };
  const toggleDrawerOpen = () => {
    setDrawerOpen(true);
  };

  const toggleDrawerClose = () => setDrawerOpen(false);

  const handleClickOpenPopup = () => {
    setDrawerOpen(false);
  };

  const handleDeleteTermFeeList = useCallback(
    async (termObj: any) => {
      const { termId } = termObj;
      const deleteConfirmMessage = (
        <DeleteMessage
          jsonIcon={deleteBin}
          message={
            <div>
              Are you sure you want to delete the Row <br />
              &quot;{termObj.termTitle}&quot; ?
            </div>
          }
        />
      );
      if (await confirm(deleteConfirmMessage, 'Delete Row?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
        const sendReq = [{ dbResult: 'string', deletedId: 0 }];

        console.log('sendReq', sendReq);

        const deleteResponse = await dispatch(deleteTermFeeList(sendReq));
        console.log('deleteResponse::::----', deleteResponse);
        if (deleteResponse && Array.isArray(deleteResponse.payload)) {
          const results: any[] = deleteResponse.payload;
          const errorMessages = results.find((result) => result.dbResult === 'Failed');
          const successMessages = results.find((result) => result.dbResult === 'Success');
          // Reload only the specific message template with the deleted messageId

          if (!errorMessages) {
            const deleteSuccessMessage = <SuccessMessage loop={false} message="Delete successfully" />;
            await confirm(deleteSuccessMessage, 'Success', { okLabel: 'Ok', showOnlyOk: true });
          } else if (!successMessages) {
            const deleteErrorMessage = <DeleteMessage message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          } else {
            const deleteErrorMessage = <DeleteMessage message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          }
          // loadTermFeeList({ ...currentTermFeeListRequest });

          // setSnackBar(true);
          // setTermFeeData((prevDetails) => prevDetails.filter((item) => item.termId !== termId));
        }
      }
    },
    [confirm]
  );

  return (
    <Page title="List">
      <ManageExamRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between" flexWrap="wrap" alignItems="center">
            <Typography variant="h6" fontSize={17}>
              Manage Exam
            </Typography>
            <Box
              pb={1}
              sx={{ flexShrink: 0 }}
              width={{ xs: '100%', sm: 'fit-content', md: '100%', lg: 'fit-content' }}
              display="flex"
              alignItems="center"
              justifyContent="end"
            >
              {showFilter === false ? (
                <Tooltip title="Search">
                  <IconButton
                    aria-label="delete"
                    color="primary"
                    sx={{ mr: { xs: 0, sm: 1 } }}
                    onClick={() => setShowFilter((x) => !x)}
                  >
                    <SearchIcon />
                  </IconButton>
                </Tooltip>
              ) : (
                <IconButton
                  aria-label="delete"
                  color="primary"
                  sx={{ mr: { xs: 0, sm: 1 } }}
                  onClick={() => setShowFilter((x) => !x)}
                >
                  <IoIosArrowUp />
                </IconButton>
              )}
              <Button sx={{ borderRadius: '20px' }} variant="outlined" size="small" onClick={toggleDrawerOpen}>
                <MdAdd size="20px" /> New
              </Button>
            </Box>
          </Stack>

          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={1} container spacing={2}>
                  <Grid item xxl={2} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Exam Name
                      </Typography>
                      <TextField fullWidth placeholder="Enter Name" />
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Status
                      </Typography>
                      <Select
                        labelId="feeTypeFilter"
                        id="feeTypeFilterSelect"
                        // value={feeTypeFilter.toString()}
                        // onChange={handleFeeTypeChange}
                        placeholder="Select Year"
                      >
                        <MenuItem sx={{ display: 'none' }} value={0}>
                          Select Status
                        </MenuItem>
                        {FEE_TYPE_ID_OPTIONS.map((opt) => (
                          <MenuItem key={opt.id} value={opt.id}>
                            {opt.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item lg={1} md={4} sm={4} xs={12}>
                    <Stack spacing={1} direction="row" sx={{ pt: { xs: 0, sm: 3.79 } }}>
                      <Button type="reset" variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>
          </div>

          <Box>
            <Paper
              sx={{
                border: `1px solid #e8e8e9`,
                width: '100%',
                height: '100%',
                overflow: 'auto',
                '&::-webkit-scrollbar': {
                  width: 0,
                },
              }}
            >
              <TableContainer
                sx={{
                  width: { xs: '700px', md: '100%' },
                }}
              >
                <Table stickyHeader aria-label="simple table">
                  <TableHead>
                    <TableRow>
                      <TableCell>SL.No</TableCell>
                      <TableCell>Exam Name</TableCell>
                      <TableCell>Exam Description</TableCell>
                      <TableCell>Staff Status</TableCell>
                      <TableCell>Parent Status</TableCell>
                      <TableCell>Action</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {ManageExamData.map((row) => (
                      <TableRow hover key={row.id}>
                        <TableCell>{row.slNo}</TableCell>
                        <TableCell>{row.name}</TableCell>
                        <TableCell>{row.description}</TableCell>
                        <TableCell>
                          <Paper
                            sx={{
                              display: 'flex',
                              justifyContent: 'center',
                              p: 0.5,
                              borderRadius: '20px',
                              maxWidth: 95,
                              backgroundColor:
                                row.staffStatus === 'Published' ? theme.palette.grey[300] : theme.palette.grey[500],
                              color:
                                row.staffStatus === 'Published' ? theme.palette.grey[600] : theme.palette.grey[100],
                            }}
                          >
                            <Typography fontSize={13}>{row.staffStatus}</Typography>
                          </Paper>
                        </TableCell>
                        <TableCell>
                          <Paper
                            sx={{
                              display: 'flex',
                              justifyContent: 'center',
                              p: 0.5,
                              borderRadius: '20px',
                              maxWidth: 95,
                              backgroundColor:
                                row.parentStatus === 'Published' ? theme.palette.grey[300] : theme.palette.grey[600],
                              color:
                                row.parentStatus === 'Published' ? theme.palette.grey[600] : theme.palette.grey[100],
                            }}
                          >
                            <Typography fontSize={13}>{row.parentStatus}</Typography>
                          </Paper>
                        </TableCell>
                        <TableCell width={30}>
                          <Stack direction="row" gap={1}>
                            <Button
                              variant="outlined"
                              className="btn_status"
                              color={row.staffStatus === 'Published' ? 'primary' : 'secondary'}
                            >
                              Staff {row.staffStatus === 'Published' ? 'Unpublish' : 'Publish'}
                            </Button>
                            <Button
                              variant="outlined"
                              className="btn_status"
                              color={row.parentStatus === 'Published' ? 'primary' : 'secondary'}
                            >
                              Parent {row.parentStatus === 'Published' ? 'Unpublish' : 'Publish'}
                            </Button>
                            <MenuEditDelete
                              Delete={() => handleDeleteTermFeeList(row.id)}
                              Edit={toggleDrawerOpen}
                              updatedatas={() => update(row)}
                            />
                          </Stack>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Paper>
          </Box>
        </Card>
      </ManageExamRoot>
      <Popup
        size="xs"
        state={Delete}
        onClose={handleClickCloseDelete}
        popupContent={<DeleteMessage message="Are you sure want to delete?" />}
      />
      <TemporaryDrawer
        onClose={toggleDrawerClose}
        state={drawerOpen}
        Title="Create Exam Details"
        DrawerContent={<Create updates={EditData} open={handleClickOpenPopup} onClose={toggleDrawerClose} />}
      />
    </Page>
  );
}

export default ManageExam;
