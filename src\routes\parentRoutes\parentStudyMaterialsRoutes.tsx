import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import Loadable from '@/components/shared/Loadable';

const StudyMaterialsIndex = Loadable(lazy(() => import('@/Parent-Side/pages/StudyMaterialsIndex')));
const StudyMaterials = Loadable(lazy(() => import('@/Parent-Side/features/StudyMaterials/StudyMaterials')));

export const parentStudyMaterialsRoutes: RouteObject[] = [
  {
    path: '/parent-study-materials',
    element: <StudyMaterialsIndex />,
    children: [
      {
        path: 'study-materials',
        element: <StudyMaterials />,
      },
    ],
  },
];
