/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  useTheme,
  IconButton,
  Collapse,
  Tooltip,
  FormControl,
  Chip,
} from '@mui/material';
import styled from 'styled-components';
import { CLASS_SELECT, STATUS_SELECT, YEAR_SELECT } from '@/config/Selection';
import SearchIcon from '@mui/icons-material/Search';
import Popup from '@/components/shared/Popup/Popup';

import { MdAdd } from 'react-icons/md';
import { Student, StudentInfoData } from '@/config/StudentDetails';
import useSettings from '@/hooks/useSettings';
import AutorenewOutlinedIcon from '@mui/icons-material/AutorenewOutlined';
import { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import DateSelect from '@/components/shared/Selections/DateSelect';
import { purplePreset } from '@/utils/Colors';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import MenuEditDeleteView from '@/components/shared/Selections/MenuEditDeleteView';
import CreateAssignment from './CreateAssignment';
import MapAssignment from './MapAssignment';
import DetailedView from './DetailedView';

export const data = [
  {
    subject: 'English',
    assignment: 'English Poets',
    class: 'VII-B',
    outOffMark: '100',
    totalSubmit: '30',
    totalStudent: '50',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
  {
    subject: 'English',
    assignment: 'English Poets',
    class: 'VI-C',
    outOffMark: '100',
    totalSubmit: '30',
    totalStudent: '50',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
  {
    subject: 'English',
    assignment: 'English Poets',
    class: 'XII-B',
    outOffMark: '100',
    totalSubmit: '30',
    totalStudent: '50',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
  {
    subject: 'English',
    assignment: 'English Poets',
    class: 'V-A',
    outOffMark: '100',
    totalSubmit: '30',
    totalStudent: '50',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
  {
    subject: 'English',
    assignment: 'English Poets',
    class: 'II-B',
    outOffMark: '100',
    totalSubmit: '30',
    totalStudent: '50',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
  {
    subject: 'English',
    assignment: 'English Poets',
    class: 'XII-C',
    outOffMark: '100',
    totalSubmit: '30',
    totalStudent: '50',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
  {
    subject: 'English',
    assignment: 'English Poets',
    class: 'I-B',
    outOffMark: '100',
    totalSubmit: '30',
    totalStudent: '50',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
  {
    subject: 'English',
    assignment: 'English Poets',
    class: 'VII-A',
    outOffMark: '100',
    totalSubmit: '30',
    totalStudent: '50',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
  {
    subject: 'English',
    assignment: 'English Poets',
    class: 'VI-C',
    outOffMark: '100',
    totalSubmit: '30',
    totalStudent: '50',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
  {
    subject: 'English',
    assignment: 'English Poets',
    class: 'X-B',
    outOffMark: '100',
    totalSubmit: '30',
    totalStudent: '50',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
];

const AssignmentListRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  .student_card {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  }

  .Card {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 40px);
      flex-grow: 1;

      .card-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: auto;
        ::-webkit-scrollbar {
          width: 0px;
        }

        .MuiTableContainer-root {
          max-height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

function AssignmentList() {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const [showFilter, setShowFilter] = useState(false);
  const [detailedView, setDetailedView] = useState<'AssignmentList' | 'DetailedView'>('AssignmentList');
  const [Delete, setDelete] = useState(false);
  const [createPopup, setCreatePopup] = useState(false);
  const [mapAssignmentPopup, setMapAssignmentPopup] = useState(false);

  const [students, setStudents] = useState(StudentInfoData);
  const [open, setOpen] = useState(false);
  const [openNew, setOpenNew] = useState(false);
  const [showNewMultiple, setShowNewMultiple] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [updatedData, setData] = useState(data);

  const handleStatusChange = (index) => {
    const copyData = [...updatedData];
    copyData[index].status = copyData[index].status === 'Published' ? 'Unpublished' : 'Published';
    setData(copyData);
  };

  const handleClickView = () => {
    setDetailedView('DetailedView');
  };
  const handleClickBack = () => {
    setDetailedView('AssignmentList');
  };
  const handleClickDelete = () => setDelete(true);
  const handleClickCloseDelete = () => setDelete(false);
  const handlePageChange = useCallback((event: unknown, newPage: number) => {
    // loadClassList({ ...currentClassListRequest, pageNumber: newPage + 1 });
  }, []);

  const handleChangeRowsPerPage = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const newPageSize = +event.target.value;
    // loadClassList({ ...currentClassListRequest, pageNumber: 1, pageSize: newPageSize });
  }, []);

  const pageProps = useMemo(
    () => ({
      rowsPerPageOptions: [10, 20, 30],
      pageNumber: 1,
      pageSize: 10,
      totalRecords: 100,
      onPageChange: handlePageChange,
      onRowsPerPageChange: handleChangeRowsPerPage,
    }),
    [handleChangeRowsPerPage, handlePageChange]
  );

  const handleClose = () => {
    setOpen(false);
  };
  const handleOpenNew = () => {
    setOpenNew(true);
  };

  const handleCloseNew = () => {
    setOpenNew(false);
  };
  const handleToggleNewMultiple = () => {
    setShowNewMultiple((prevState) => !prevState);
  };

  const handleUpdate = (id: number, updatedData: Student) => {
    const updatedStudents = students.map((student) => (student.id === id ? { ...student, ...updatedData } : student));
    setStudents(updatedStudents);
    setOpen(false);
  };

  const getRowKey = useCallback((row: any) => row.examId, []);

  const [isSnackbarOpen, setIsSnackbarOpen] = useState(false);

  const handleCopyClick = (text: string) => {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        setIsSnackbarOpen(true);
      })
      .catch((err) => {
        console.error('Unable to copy link to clipboard', err);
      });
  };

  const handleCloseSnackbar = () => {
    setIsSnackbarOpen(false);
  };

  const AssignmentListColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'subject',
        dataKey: 'subject',
        headerLabel: 'Subject',
      },
      {
        name: 'assignment',
        dataKey: 'assignment',
        headerLabel: 'Assignment',
      },
      {
        name: 'class',
        dataKey: 'class',
        headerLabel: 'Class',
      },
      {
        name: 'meetingStartDate',
        dataKey: 'meetingStartDate',
        headerLabel: 'Start Date',
      },
      {
        name: 'outOffMark',
        dataKey: 'outOffMark',
        headerLabel: 'Out Off Mark',
      },
      {
        name: 'totalSubmit',
        headerLabel: 'Total Submit',
        renderCell: (row) => {
          return (
            <Chip
              size="small"
              label={row.totalSubmit}
              variant="outlined"
              // color="success"
              sx={{
                border: '0px',
                borderRadius: '5px',
                bgcolor: theme.palette.info.light,
                color: theme.palette.info.dark,
              }}
            />
          );
        },
      },
      {
        name: 'totalStudent',
        headerLabel: 'Total Student',
        renderCell: (row) => {
          return (
            <Chip
              size="small"
              label={row.totalStudent}
              variant="outlined"
              // color={theme.palette.info.main}
              sx={{
                border: '0px',
                borderRadius: '5px',
                bgcolor: purplePreset.lighter,
                color: purplePreset.dark,
              }}
            />
          );
        },
      },
    ],
    [theme, isLight]
  );

  return detailedView === 'AssignmentList' ? (
    <Page title="Assignment List">
      <AssignmentListRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17} width="100%">
              Assignment List
            </Typography>
            <Box pb={1} sx={{ flexShrink: 0 }}>
              <Tooltip title="Search">
                <IconButton aria-label="" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                  <SearchIcon />
                </IconButton>
              </Tooltip>
              <Button
                sx={{ borderRadius: '20px', mr: 2 }}
                size="small"
                variant="outlined"
                onClick={() => setMapAssignmentPopup(true)}
              >
                Map Assignment
              </Button>
              <Button
                sx={{ borderRadius: '20px' }}
                size="small"
                variant="outlined"
                onClick={() => setCreatePopup(true)}
              >
                <MdAdd size="20px" /> Create
              </Button>
            </Box>
          </Stack>
          <Stack>
            <Divider />
          </Stack>
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={2}>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Autocomplete
                        options={YEAR_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select year" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Autocomplete
                        options={CLASS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select class" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Meeting Title
                      </Typography>
                      <TextField fullWidth placeholder="Enter title" />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Select Date
                      </Typography>
                      <DateSelect />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" md={6} xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', lg: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Status
                      </Typography>
                      <Autocomplete
                        options={STATUS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select" />}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item lg={1} xs={12}>
                    <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                      <Button variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                      {/* <Button variant="contained" color="primary" fullWidth>
                          Search
                        </Button> */}
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Box className="card-container" my={2}>
              <Grid container spacing={2}>
                {updatedData.map((student, rowIndex) => (
                  <Grid item xl={4} lg={6} md={12} sm={6} xs={12}>
                    <Card
                      className="student_card"
                      sx={{ p: 2, boxShadow: 0, border: `1px solid ${theme.palette.grey[300]}` }}
                    >
                      <Box display="flex" flexDirection="column">
                        <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                          <Chip
                            icon={<AutorenewOutlinedIcon sx={{ transform: 'rotate(180deg)' }} />}
                            size="small"
                            label={student.status === 'Published' ? 'Click to Unpublish' : 'Click to Publish'}
                            variant="outlined"
                            color="primary"
                            clickable
                            onClick={() => handleStatusChange(rowIndex)}
                          />

                          <Stack direction="row" alignItems="center" gap={1} ml={1}>
                            <Chip
                              icon={student.status === 'Published' ? <CheckCircleIcon /> : ''}
                              size="small"
                              label={student.status === 'Published' ? ' Published' : 'Unpublished'}
                              variant="outlined"
                              color={student.status === 'Published' ? 'success' : 'error'}
                              sx={{ border: '0px' }}
                            />
                            <MenuEditDeleteView
                              Edit={() => setCreatePopup(true)}
                              Delete={handleClickDelete}
                              View={handleClickView}
                            />
                          </Stack>
                        </Box>

                        {AssignmentListColumns.map((item) => (
                          <Stack direction="row" ml={1}>
                            <Grid container>
                              <Grid item lg={5} xs={6}>
                                <Typography mt={0.5} key={item.name} variant="subtitle1" fontSize={13} mr={2}>
                                  {item.headerLabel}
                                </Typography>
                              </Grid>
                              <Grid item lg={7} xs={6} mb={0} mt="auto">
                                <Typography
                                  variant="h6"
                                  mt={0.5}
                                  fontSize={13}
                                  color={item.dataKey === 'meetingLink' ? theme.palette.info.main : ''}
                                >
                                  {item.dataKey
                                    ? `:${' '}${(student as { [key: string]: any })[item.dataKey ?? '']}`
                                    : item && item.renderCell && item.renderCell(student, rowIndex)}
                                </Typography>
                              </Grid>
                            </Grid>
                          </Stack>
                        ))}
                      </Box>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Box>
          </div>
        </Card>
      </AssignmentListRoot>
      <Popup
        size="lg"
        title="Assignment Create"
        state={createPopup}
        onClose={() => setCreatePopup(false)}
        popupContent={<CreateAssignment onClose={() => setCreatePopup(false)} />}
      />
      <Popup
        size="sm"
        title="Map to Subject"
        state={mapAssignmentPopup}
        onClose={() => setMapAssignmentPopup(false)}
        popupContent={<MapAssignment onClose={() => setCreatePopup(false)} />}
      />
    </Page>
  ) : (
    <DetailedView onBackClick={handleClickBack} />
  );
}

export default AssignmentList;
