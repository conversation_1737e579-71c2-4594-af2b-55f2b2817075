import ApiUrls from '@/config/ApiUrls';
import {
  SectionCreateRequest,
  SectionListInfo,
  SectionListPagedData,
  SectionListRequest,
} from '@/types/AcademicManagement';
import { CreateResponse, DeleteResponse, UpdateResponse } from '@/types/Common';
import { privateApi } from '@/api/base/api';
import { APIResponse } from '@/api/base/types';

async function GetSectionList(request: SectionListRequest): Promise<APIResponse<SectionListPagedData>> {
  const response = await privateApi.post<SectionListPagedData>(ApiUrls.GetSectionList, request);
  return response;
}

async function SectionList(sectionId: number | undefined): Promise<APIResponse<SectionListPagedData[]>> {
  if (sectionId === undefined) {
    throw new Error('sectionId is required');
  }

  const url = ApiUrls.GetSection.replace('{sectionId}', sectionId.toString());
  const response = await privateApi.get<any>(url);
  return response;
}

async function AddNewSection(request: SectionCreateRequest): Promise<APIResponse<CreateResponse>> {
  const response = await privateApi.post<CreateResponse>(ApiUrls.AddSection, request);
  return response;
}

async function UpdateSection(request: SectionListInfo): Promise<APIResponse<UpdateResponse>> {
  const response = await privateApi.post<UpdateResponse>(ApiUrls.UpdateSection, request);
  return response;
}

async function DeleteSection(sectionId: number): Promise<APIResponse<DeleteResponse>> {
  const url = ApiUrls.DeleteSection.replace('{sectionId}', sectionId.toString());
  const response = await privateApi.get<DeleteResponse>(url);
  return response;
}

async function SectionNameExists(sectionName: string): Promise<APIResponse<boolean>> {
  const url = `${ApiUrls.SectionNameExists}?q=${sectionName}`;
  const response = await privateApi.get<boolean>(url);
  return response;
}

const methods = {
  GetSectionList,
  AddNewSection,
  UpdateSection,
  DeleteSection,
  SectionNameExists,
  SectionList,
};

export default methods;
