/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  Collapse,
  FormControl,
} from '@mui/material';
import styled from 'styled-components';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import SearchIcon from '@mui/icons-material/Search';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import DeleteIcon from '@mui/icons-material/Delete';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import { MdAdd } from 'react-icons/md';

const AppDetailsListRoot = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      height: calc(100% - 40px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

export const data = [
  {
    SlNo: 1,
    StudentName: 'A N SHIVAKAVIYAN',
    GuardianName: 'ARUN P',
    GuardianNumber: 9857634125,
    Class: '1A',
    InstalledDate: '19 Aug 2023 01:57 PM',
    AppInstalled: 'Yes',
  },
  {
    SlNo: 2,
    StudentName: 'AADIV P',
    GuardianName: 'PRASADA',
    GuardianNumber: 7076489321,
    Class: '8A',
    InstalledDate: '08 Aug 2023 04:35 PM',
    AppInstalled: 'No',
  },
  {
    SlNo: 3,
    StudentName: 'AAGNEYA P',
    GuardianName: 'PRASADS',
    GuardianNumber: 9875142369,
    Class: '4A',
    InstalledDate: '27 Aug 2023 07:12 AM',
    AppInstalled: 'Yes',
  },
  {
    SlNo: 4,
    StudentName: 'AARIYAN M',
    GuardianName: 'MATHESWARAN',
    GuardianNumber: 8745123698,
    Class: '2B',
    InstalledDate: '16 Aug 2023 10:42 AM',
    AppInstalled: 'No',
  },
  {
    SlNo: 5,
    StudentName: 'ADEESWARN E',
    GuardianName: 'DEEPIKAV',
    GuardianNumber: 7998745632,
    Class: '9A',
    InstalledDate: '12 Aug 2023 09:15 AM',
    AppInstalled: 'Yes',
  },
  {
    SlNo: 6,
    StudentName: 'ADVIKA V',
    GuardianName: 'PARENT',
    GuardianNumber: 9061160913,
    Class: '1B',
    InstalledDate: '23 Aug 2023 03:22 PM',
    AppInstalled: 'Yes',
  },
  {
    SlNo: 7,
    StudentName: 'FRANCIS',
    GuardianName: 'ALNA A',
    GuardianNumber: 8085632497,
    Class: '3B',
    InstalledDate: '21 Aug 2023 08:45 PM',
    AppInstalled: 'Yes',
  },
  {
    SlNo: 8,
    StudentName: 'DHYAN LAL H',
    GuardianName: 'HARILAL S',
    GuardianNumber: 9807654321,
    Class: '9A',
    InstalledDate: '13 Aug 2023 06:11 AM',
    AppInstalled: 'Yes',
  },
  {
    SlNo: 10,
    StudentName: 'NANYA SAM M',
    GuardianName: 'MURALIK',
    GuardianNumber: 9978543210,
    Class: '5B',
    InstalledDate: '10 Aug 2023 02:30 PM',
    AppInstalled: 'No',
  },
  {
    SlNo: 9,
    StudentName: 'SAFFEL RAHMAN',
    GuardianName: 'SHAHBADSHA',
    GuardianNumber: 7654982301,
    Class: '9B',
    InstalledDate: '17 Aug 2023 07:58 AM',
    AppInstalled: 'Yes',
  },
  {
    SlNo: 11,
    StudentName: 'LEVIN JOSE',
    GuardianName: 'ANDREWG',
    GuardianNumber: 8974123654,
    Class: '7B',
    InstalledDate: '20 Aug 2023 11:59 AM',
    AppInstalled: 'No',
  },
  {
    SlNo: 12,
    StudentName: 'MAHDIVA',
    GuardianName: 'AZHARUDDIN',
    GuardianNumber: 9998887776,
    Class: '6A',
    InstalledDate: '09 Aug 2023 05:47 PM',
    AppInstalled: 'Yes',
  },
  {
    SlNo: 13,
    StudentName: 'MANHA',
    GuardianName: 'ANSAR',
    GuardianNumber: 9745989992,
    Class: '8B',
    InstalledDate: '24 Aug 2023 12:06 PM',
    AppInstalled: 'No',
  },
  {
    SlNo: 14,
    StudentName: 'MOSES PAUL',
    GuardianName: 'PAUL JOYP',
    GuardianNumber: 7854632190,
    Class: '4B',
    InstalledDate: '18 Aug 2023 06:33 AM',
    AppInstalled: 'Yes',
  },
  {
    SlNo: 15,
    StudentName: 'NIVIN PAUL',
    GuardianName: 'PAUL AROKIAR A',
    GuardianNumber: 8889997770,
    Class: '6B',
    InstalledDate: '28 Aug 2023 03:14 PM',
    AppInstalled: 'No',
  },
  {
    SlNo: 16,
    StudentName: 'RISHYA R',
    GuardianName: 'ROBIN BABU',
    GuardianNumber: 7896541235,
    Class: '2A',
    InstalledDate: '22 Aug 2023 02:19 AM',
    AppInstalled: 'No',
  },
  {
    SlNo: 17,
    StudentName: 'SAFFEL RAHMAN',
    GuardianName: 'SHAHBAD SHA',
    GuardianNumber: 7654982301,
    Class: '9B',
    InstalledDate: '17 Aug 2023 07:58 AM',
    AppInstalled: 'Yes',
  },
];

function AppDetailsList() {
  const [drawerOpen, setDrawerOpen] = React.useState<boolean>(false);
  const [showFilter, setShowFilter] = useState(false);

  const handleOpen = () => {
    setDrawerOpen(true);
  };

  const toggleDrawerClose = useCallback(() => setDrawerOpen(false), []);

  const getRowKey = useCallback((row: any) => row.examId, []);
  const AppDetailsListColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'slNo',
        dataKey: 'SlNo',
        headerLabel: 'Sl.No',
        sortable: true,
      },
      {
        name: 'name',
        dataKey: 'StudentName',
        headerLabel: 'Student Name',
      },
      {
        name: 'title',
        dataKey: 'GuardianName',
        headerLabel: 'Guardian Name',
      },
      {
        name: 'file',
        dataKey: 'GuardianNumber',
        headerLabel: 'Guardian Number',
      },
      {
        name: 'class',
        dataKey: 'Class',
        headerLabel: 'Class',
      },
      {
        name: 'installedDate',
        dataKey: 'InstalledDate',
        headerLabel: 'Installed Date',
      },
      {
        name: 'appInstalled',
        dataKey: 'AppInstalled',
        headerLabel: 'App Installed',
      },
      {
        name: 'actions',
        headerLabel: 'Actions',
        renderCell: () => {
          return (
            <Stack direction="row" gap={1}>
              <IconButton size="small" onClick={handleOpen} sx={{ padding: 0.5 }}>
                <ModeEditIcon />
              </IconButton>
              <IconButton size="small" color="error" onClick={handleOpen} sx={{ padding: 0.5 }}>
                <DeleteIcon />
              </IconButton>
            </Stack>
          );
        },
      },
    ],
    []
  );

  return (
    <Page title="List">
      <AppDetailsListRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17}>
              Parent App Details
            </Typography>
            <Box pb={1} sx={{ flexShrink: 0 }}>
              <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                <SearchIcon />
              </IconButton>
              <Button onClick={handleOpen} sx={{ borderRadius: '20px' }} variant="outlined" size="small">
                <MdAdd size="20px" /> Create
              </Button>
            </Box>
          </Stack>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={3} alignItems="end">
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 250 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Category Name
                      </Typography>
                      <TextField placeholder="Enter Name" />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 250 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Sub Category Name
                      </Typography>
                      <TextField placeholder="Enter Name" />
                    </FormControl>
                  </Grid>

                  <Grid item lg="auto" xs={12} sm={6}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Paper className="card-table-container" sx={{ marginTop: '12px' }}>
              <DataTable columns={AppDetailsListColumns} data={data} getRowKey={getRowKey} fetchStatus="success" />
            </Paper>
          </div>
          {/* <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 2 }}>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="secondary">
                Cancel
              </Button>
              <Button onClick={handleOpen} variant="contained" color="primary">
                Save
              </Button>
            </Stack>
          </Box> */}
        </Card>
      </AppDetailsListRoot>

      <TemporaryDrawer onClose={toggleDrawerClose} Title="Upload Details" state={drawerOpen} DrawerContent="" />
    </Page>
  );
}

export default AppDetailsList;
