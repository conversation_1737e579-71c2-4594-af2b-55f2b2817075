import { ListMenuItem } from '@/types/Layout';

export const SideBarMenuDataHoly: ListMenuItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: 'dashboard',
    link: 'http://holy.passdaily.in/NewDesign/Dashboard.aspx',
    url: 'http://holy.passdaily.in/NewDesign/Dashboard.aspx',
  },
  {
    id: 'manageFee',
    label: 'Manage Fees',
    icon: 'manageFee',
    link: '/manage-fee',
    url: '/manage-fee/overview',
  },
];
export const SideBarMenuData: ListMenuItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: 'dashboard',
    link: '/',
    url: '/',
  },
  {
    id: 'attendanceMarking',
    label: 'Attendance Marking',
    icon: 'attendanceMarking',
    link: '/attendance-marking',
    url: '/attendance-marking/class-marking',
  },
  {
    id: 'messageBoxSMS',
    label: 'Message Box (SMS)',
    icon: 'messageBoxSMS',
    link: '/message-box',
    url: '/message-box/quick-send',
  },
  {
    id: 'voiceMessage',
    label: 'Voice Message',
    icon: 'voiceMessage',
    link: '/voice-message',
    url: '/voice-message/create',
  },
  {
    id: 'appNotification',
    label: 'App Notification',
    icon: 'appNotification',
    link: '/app-notification',
    url: '/app-notification/create',
  },
  {
    id: 'academicManagement',
    label: 'Manage Academics',
    icon: 'academicManagement',
    link: '/academic-management',
    url: '/academic-management/year',
  },
  {
    id: 'manageStudents',
    label: 'Manage Students',
    icon: 'manageStudents',
    link: '/manage-students',
    url: '/manage-students/new',
  },
  {
    id: 'staffManagement',
    label: 'Staff Management',
    icon: 'staffManagement',
    link: '/staff-management',
    url: '/staff-management/list',
  },
  {
    id: 'staffAttendance',
    label: 'Staff Attendance',
    icon: 'staffManagement',
    link: '/staff-attendance',
    url: '/staff-attendance/leave-list',
  },
  {
    id: 'manageFee',
    label: 'Manage Fees',
    icon: 'manageFee',
    link: '/manage-fee',
    url: '/manage-fee/overview',
  },
  {
    id: 'manageBusFee',
    label: 'Manage Bus Fees',
    icon: 'busFee',
    link: '/manage-bus-fees',
    url: '/manage-bus-fees/bus-list',
  },
  {
    id: 'feeAlert',
    label: 'Fee Alert',
    icon: 'feeAlert',
    link: '/fee-alert',
    url: '/fee-alert/update-fees',
  },
  {
    id: 'parentEnquiry',
    label: 'Parent Enquiry',
    icon: 'parentEnquiry',
    link: '/parent-enquiry',
    url: '/parent-enquiry/list',
  },
  {
    id: 'vehicleTracking',
    label: 'Vehicle Tracking',
    icon: 'vehicleTracking',
    link: '/vehicle-tracking',
    url: '/vehicle-tracking/new',
  },
  {
    id: 'timeTable',
    label: 'Time Table',
    icon: 'timeTable',
    link: '/time-table',
    url: '/time-table/new',
  },
  {
    id: 'examCenter',
    label: 'Exam Center',
    icon: 'examCenter',
    link: '/exam-center',
    url: '/exam-center/online-marks',
  },
  {
    id: 'publishResult',
    label: 'Publish Result',
    icon: 'publishResult',
    link: '/publish-result',
    url: '/publish-result/update',
  },
  {
    id: 'album',
    label: 'Album',
    icon: 'album',
    link: '/album',
    url: '/album/full-list',
  },
  {
    id: 'payment',
    label: 'Payment Details',
    icon: 'paymentDetails',
    link: '/payments',
    url: '/payments/category',
  },
  {
    id: 'library',
    label: 'Library Management',
    icon: 'library',
    link: '/library-management',
    url: '/library-management/new-book',
  },
  {
    id: 'store',
    label: 'Store Management',
    icon: 'store',
    link: '/store-management',
    url: '/store-management/product-list',
  },
  {
    id: 'calendar',
    label: 'School Calendar',
    icon: 'calendar',
    link: '/school-calendar',
    url: '/school-calendar/calendar',
  },
  {
    id: 'group',
    label: 'Manage Groups',
    icon: 'group',
    link: '/group-settings',
    url: '/group-settings/groups',
  },
  {
    id: 'liveClass',
    label: 'Live Class',
    icon: 'liveClass',
    link: '/live-class',
    url: '/live-class/schedule-list',
  },
  {
    id: 'onlineVideoClass',
    label: 'Online Video Class',
    icon: 'onlineVideoClass',
    link: '/online-video-class',
    url: '/online-video-class/student-device-details',
  },
  {
    id: 'assignment',
    label: 'Assignment',
    icon: 'assignment',
    link: '/assignment',
    url: '/assignment/assignment-list',
  },
  {
    id: 'onlineExam',
    label: 'Online Exam',
    icon: 'onlineExam',
    link: '/online-exam',
    url: '/online-exam/exam-details',
  },
  {
    id: 'certificate',
    label: 'Certificates',
    icon: 'certificate',
    link: '/certificate',
    url: '/certificates/TC-list',
  },
  {
    id: 'report',
    label: 'Reports',
    icon: 'report',
    link: '/report',
    url: '/report/student-details',
  },
  {
    id: 'pta',
    label: 'PTA Forum',
    icon: 'pta',
    link: '/pta-forum',
    url: '/pta-forum/members-list',
  },
  {
    id: 'adminControls',
    label: 'Admin Controls',
    icon: 'adminControls',
    link: '/admin-controls',
    url: '/admin-controls/send-updates',
  },

  {
    id: 'settings',
    label: 'General Settings',
    icon: 'settings',
    link: '/general-settings',
    url: '/general-settings/about-us',
  },
  {
    id: 'tc&cc',
    label: 'TC & CC',
    icon: 'tcandcc',
    link: '/tc-cc',
    url: '/tc-cc/tc-list',
  },
  {
    id: 'social',
    label: 'Social',
    icon: 'social',
    link: '/social',
    url: '/social',
  },
  {
    id: 'schoolplan',
    label: 'School Plan',
    icon: 'schoolplan',
    link: '/school-plan',
    url: '/school-plan/session-plan-list',
  },
  {
    id: 'rnd',
    label: 'RND',
    icon: 'rnd',
    link: '/research&development',
    url: '/research&development/rnd',
  },
];

//                   List in Alphabetical ORDER
// ----------------------------------------------------------------
// export const SideBarMenuData: ListMenuItem[] = [
//   {
//     id: 'dashboard',
//     label: 'Dashboard',
//     icon: 'dashboard',
//     link: '/',
//     url: '/',
//   },
//   {
//     id: 'adminControls',
//     label: 'Admin Controls',
//     icon: 'adminControls',
//     link: '/admin-controls',
//     url: '/admin-controls/send-updates',
//   },
//   {
//     id: 'album',
//     label: 'Album',
//     icon: 'album',
//     link: '/album',
//     url: '/album/full-list',
//   },
//   {
//     id: 'appNotification',
//     label: 'App Notification',
//     icon: 'appNotification',
//     link: '/app-notification',
//     url: '/app-notification/create',
//   },
//   {
//     id: 'attendanceMarking',
//     label: 'Attendance Marking',
//     icon: 'attendanceMarking',
//     link: '/attendance-marking',
//     url: '/attendance-marking/class-marking',
//   },
//   {
//     id: 'examCenter',
//     label: 'Exam Center',
//     icon: 'examCenter',
//     link: '/exam-center',
//     url: '/exam-center/online-marks',
//   },
//   {
//     id: 'feeAlert',
//     label: 'Fee Alert',
//     icon: 'feeAlert',
//     link: '/fee-alert',
//     url: '/fee-alert/update-fees',
//   },
//   {
//     id: 'library',
//     label: 'Library Management',
//     icon: 'library',
//     link: '/library-management',
//     url: '/library-management/new-book',
//   },
//   {
//     id: 'academicManagement',
//     label: 'Manage Academics',
//     icon: 'academicManagement',
//     link: '/academic-management',
//     url: '/academic-management/year',
//   },
//   {
//     id: 'manageBusFee',
//     label: 'Manage Bus Fees',
//     icon: 'busFee',
//     link: '/manage-bus-fees',
//     url: '/manage-bus-fees/bus-list',
//   },
//   {
//     id: 'manageFee',
//     label: 'Manage Fees',
//     icon: 'manageFee',
//     link: '/manage-fee',
//     url: '/manage-fee/overview',
//   },
//   {
//     id: 'manageStudents',
//     label: 'Manage Students',
//     icon: 'manageStudents',
//     link: '/manage-students',
//     url: '/manage-students/new',
//   },
//   {
//     id: 'messageBoxSMS',
//     label: 'Message Box (SMS)',
//     icon: 'messageBoxSMS',
//     link: '/message-box',
//     url: '/message-box/quick-send',
//   },
//   {
//     id: 'parentEnquiry',
//     label: 'Parent Enquiry',
//     icon: 'parentEnquiry',
//     link: '/parent-enquiry',
//     url: '/parent-enquiry/list',
//   },
//   {
//     id: 'paymentDetails',
//     label: 'Payment Details',
//     icon: 'paymentDetails',
//     link: '/payments',
//     url: '/payments/category',
//   },
//   {
//     id: 'publishResult',
//     label: 'Publish Result',
//     icon: 'publishResult',
//     link: '/publish-result',
//     url: '/publish-result/update',
//   },
//   {
//     id: 'staffManagement',
//     label: 'Staff Management',
//     icon: 'staffManagement',
//     link: '/staff-management',
//     url: '/staff-management/list',
//   },
//   {
//     id: 'store',
//     label: 'Store Management',
//     icon: 'store',
//     link: '/store-management',
//     url: '/store-management/product-list',
//   },
//   {
//     id: 'timeTable',
//     label: 'Time Table',
//     icon: 'timeTable',
//     link: '/time-table',
//     url: '/time-table/new',
//   },
//   {
//     id: 'vehicleTracking',
//     label: 'Vehicle Tracking',
//     icon: 'vehicleTracking',
//     link: '/vehicle-tracking',
//     url: '/vehicle-tracking/new',
//   },
//   {
//     id: 'voiceMessage',
//     label: 'Voice Message',
//     icon: 'voiceMessage',
//     link: '/voice-message',
//     url: '/voice-message/create',
//   },
// ];
