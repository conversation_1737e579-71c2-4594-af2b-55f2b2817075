/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  useTheme,
  IconButton,
  Collapse,
  Tooltip,
  FormControl,
  Chip,
  Snackbar,
  Alert,
} from '@mui/material';
import styled from 'styled-components';
import AccessTimeRoundedIcon from '@mui/icons-material/AccessTimeRounded';
import CheckCircleOutlineRoundedIcon from '@mui/icons-material/CheckCircleOutlineRounded';
import HelpOutlineRoundedIcon from '@mui/icons-material/HelpOutlineRounded';
import PauseCircleOutlineRoundedIcon from '@mui/icons-material/PauseCircleOutlineRounded';
import MenuRoundedIcon from '@mui/icons-material/MenuRounded';
import AlarmRoundedIcon from '@mui/icons-material/AlarmRounded';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import SearchIcon from '@mui/icons-material/Search';
import Popup from '@/components/shared/Popup/Popup';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import empty from '@/Parent-Side/assets/descriptiveExam/emptystate.png';
import { MdAdd, MdContentCopy } from 'react-icons/md';
import { CloseIcon } from '@/theme/overrides/CustomIcons';
import { Student, StudentInfoData } from '@/config/StudentDetails';
import useSettings from '@/hooks/useSettings';
import AutorenewOutlinedIcon from '@mui/icons-material/AutorenewOutlined';
import { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import DateSelect from '@/components/shared/Selections/DateSelect';
import MenuEditDelete from '@/components/shared/Selections/MenuEditDelete';
import CreateScheduleList from '@/features/LiveClass/ScheduleList/CreateScheduleList';
import InsertDriveFileIcon from '@mui/icons-material/InsertDriveFile';
import View from '../StudyMaterials/View';
import BackButton from '@/components/shared/BackButton';
import Exam from './Exam';

const ViewResultRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  .student_card {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  }

  .Card {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    min-height: calc(100vh - 110px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 40px);
      flex-grow: 1;

      .card-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: auto;
        ::-webkit-scrollbar {
          width: 0px;
        }

        .MuiTableContainer-root {
          max-height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

function ViewResult({ onBackClick, resultView }: any) {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const [changeView, setChangeView] = useState<'DetailsPage' | 'ExamPage'>('DetailsPage');
  const [showDetails, setShowDetails] = useState<'DetailsPage' | ''>('');
  // const [resultView, setResultView] = useState<boolean>(false);
  const [showFilter, setShowFilter] = useState(false);
  const [Delete, setDelete] = useState(false);
  const [createPopup, setCreatePopup] = useState(false);

  const [students, setStudents] = useState(StudentInfoData);
  const [open, setOpen] = useState(false);
  const [openNew, setOpenNew] = useState(false);
  const [showNewMultiple, setShowNewMultiple] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState(null);

  const handleToggle = () => {
    setChangeView((prevChangeView) => !prevChangeView);
  };

  const handleClickDelete = () => setDelete(true);
  const handleClickCloseDelete = () => setDelete(false);
  const handlePageChange = useCallback((event: unknown, newPage: number) => {
    // loadClassList({ ...currentClassListRequest, pageNumber: newPage + 1 });
  }, []);

  const handleChangeRowsPerPage = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const newPageSize = +event.target.value;
    // loadClassList({ ...currentClassListRequest, pageNumber: 1, pageSize: newPageSize });
  }, []);

  const pageProps = useMemo(
    () => ({
      rowsPerPageOptions: [10, 20, 30],
      pageNumber: 1,
      pageSize: 10,
      totalRecords: 100,
      onPageChange: handlePageChange,
      onRowsPerPageChange: handleChangeRowsPerPage,
    }),
    [handleChangeRowsPerPage, handlePageChange]
  );

  const handleClose = () => {
    setOpen(false);
  };
  const handleOpenNew = () => {
    setOpenNew(true);
  };

  const handleCloseNew = () => {
    setOpenNew(false);
  };
  const handleToggleNewMultiple = () => {
    setShowNewMultiple((prevState) => !prevState);
  };

  const handleUpdate = (id: number, updatedData: Student) => {
    const updatedStudents = students.map((student) => (student.id === id ? { ...student, ...updatedData } : student));
    setStudents(updatedStudents);
    setOpen(false);
  };

  const getRowKey = useCallback((row: any) => row.examId, []);

  const [isSnackbarOpen, setIsSnackbarOpen] = useState(false);

  const handleCopyClick = (text: string) => {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        setIsSnackbarOpen(true);
      })
      .catch((err) => {
        console.error('Unable to copy link to clipboard', err);
      });
  };

  const handleCloseSnackbar = () => {
    setIsSnackbarOpen(false);
  };

  return changeView! !== 'ExamPage' ? (
    <Page title="Schedule List">
      <ViewResultRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 1, md: 2 } }}>
          <Stack direction="row" mb={2} justifyContent="space-between">
            <BackButton
              onBackClick={onBackClick}
              title={
                <div>
                  <span style={{ color: 'grey' }}>Descriptive Exam</span>
                  <ChevronRightIcon fontSize="small" />
                  View Result
                </div>
              }
            />
            <MenuRoundedIcon />
          </Stack>
          <Divider />
          <Box mt={1} display="flex" justifyContent="space-between">
            <Stack>
              <Typography variant="h6" fontSize={17}>
                Alex Peter
              </Typography>
              <Typography variant="subtitle1" color="secondary" fontSize={14}>
                Class : X-A
              </Typography>
            </Stack>
            <Stack direction="row" alignItems="center">
              <Chip size="small" label="Sceince" color="secondary" />
            </Stack>
          </Box>
          <Box display="flex" justifyContent="space-between" mt={3} mr={15}>
            <Box display="flex" gap={2} justifyContent="start" alignItems="start">
              <Stack
                p={0.5}
                display="flex"
                alignItems="center"
                justifyContent="center"
                sx={{ backgroundColor: '#ffe8ee', borderRadius: 5 }}
              >
                <AccessTimeRoundedIcon color="error" sx={{ fontSize: '25px' }} />
              </Stack>
              <Stack>
                <Typography variant="subtitle2" fontSize={16}>
                  Durations
                </Typography>
                <Typography variant="body1" fontSize={13} color="secondary">
                  5 Minutes
                </Typography>
              </Stack>
            </Box>

            <Box display="flex" justifyContent="start" gap={2} alignItems="start">
              <Stack
                p={0.5}
                display="flex"
                alignItems="center"
                justifyContent="center"
                sx={{ backgroundColor: '#fef4ea', borderRadius: 5 }}
              >
                <AlarmRoundedIcon sx={{ fontSize: '25px', color: '#fe9c43' }} />
              </Stack>
              <Stack>
                <Typography variant="subtitle2" fontSize={16}>
                  Time Taken
                </Typography>
                <Typography variant="body1" fontSize={13} color="secondary">
                  10 Minutes
                </Typography>
              </Stack>
            </Box>

            <Box display="flex" gap={2} justifyContent="start" alignItems="start">
              <Stack
                p={0.5}
                display="flex"
                alignItems="center"
                justifyContent="center"
                sx={{ backgroundColor: '#f0f9e8', borderRadius: 5 }}
              >
                <CheckCircleOutlineRoundedIcon sx={{ fontSize: '25px', color: '#73bf2b' }} />
              </Stack>
              <Stack>
                <Typography variant="subtitle2" fontSize={16}>
                  Level
                </Typography>
                <Typography variant="body1" fontSize={13} color="secondary">
                  Medium
                </Typography>
              </Stack>
            </Box>
          </Box>
          <Box color="black" display="flex" justifyContent="space-between" mt={3} mr={10}>
            <Stack width={150} p={2} alignItems="center" bgcolor={theme.palette.success.lighter}>
              <Typography variant="subtitle2" fontSize={16}>
                07
              </Typography>
              <Typography variant="subtitle1" fontSize={12}>
                Correct
              </Typography>
            </Stack>

            <Stack p={2} width={150} alignItems="center" bgcolor={theme.palette.primary.lighter}>
              <Typography variant="subtitle2" fontSize={16}>
                10
              </Typography>
              <Typography variant="subtitle1" fontSize={12}>
                Total Question
              </Typography>
            </Stack>

            <Stack p={2} width={150} alignItems="center" bgcolor={theme.palette.error.lighter}>
              <Typography variant="subtitle2" fontSize={16}>
                03
              </Typography>
              <Typography variant="subtitle1" fontSize={12}>
                Unanswer
              </Typography>
            </Stack>
          </Box>
          <Stack mt={5} mb={3} justifyContent="center" alignItems="center" mr={10}>
            <Typography variant="subtitle2" fontSize={14} mb={1}>
              {/* (N/A/10) */}
              Total Mark : 2/10
            </Typography>
            <Button onClick={() => setChangeView('ExamPage')} variant="contained" color="info">
              Preview
            </Button>
          </Stack>
          <Divider />
          <Box>
            <Stack my={2} direction="row" justifyContent="space-between">
              <Typography variant="subtitle2" fontSize={14}>
                Exam Toppers
              </Typography>
              <Typography color="secondary" variant="subtitle1" fontSize={12}>
                Result Published
              </Typography>
            </Stack>
            <Grid container spacing={2}>
              {[1, 2, 3, 4, 5, 6].map(() => (
                <Grid item lg={6} xs={12}>
                  <Card sx={{ p: 2, backgroundColor: !isLight && theme.palette.grey[900] }}>
                    <Stack direction="row" alignItems="center" justifyContent="space-between">
                      <div>
                        <Typography variant="subtitle2">Alex Peter</Typography>
                        <Typography variant="subtitle1" fontSize={12} color="secondary">
                          Class : X-A
                        </Typography>
                      </div>
                      <div>
                        <Typography variant="subtitle2">10/20</Typography>
                      </div>
                    </Stack>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Box>
        </Card>
      </ViewResultRoot>
      <Popup
        size="xs"
        state={Delete}
        onClose={handleClickCloseDelete}
        popupContent={<DeleteMessage message="Are you sure want to delete?" />}
      />
      <Popup
        size="sm"
        title="Assignment Details"
        state={createPopup}
        onClose={() => setCreatePopup(false)}
        popupContent={<View />}
      />
    </Page>
  ) : (
    <Exam onBackClick={() => setChangeView('DetailsPage')} resultView={resultView} />
  );
}

export default ViewResult;
