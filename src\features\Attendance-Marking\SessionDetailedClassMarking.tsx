/* eslint-disable jsx-a11y/alt-text */
import React, { useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Avatar,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Checkbox,
  Card,
  IconButton,
} from '@mui/material';
import NotSuccess from '@/assets/attendance/notApprove.svg';
import Popup from '@/components/shared/Popup/Popup';
import styled from 'styled-components';
import RemoveCircleOutlineIcon from '@mui/icons-material/RemoveCircleOutline';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import { CLASS_SELECT, SESSION_SELECT, YEAR_SELECT } from '@/config/Selection';
import { Stdtype, studentsDetails } from '@/config/StudentDetails';
import { sessionDetailedHead } from '@/config/TableData';
import DateSelect from '@/components/shared/Selections/DateSelect';

const DetailedClassMarkingRoot = styled.div`
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  h6 {
    font-weight: 600;
  }
  .MuiTextField-root:hover .MuiInputBase-input {
    cursor: text;
  }
  .Card {
    min-height: calc(100vh - 160px);
    @media screen and (max-width: 1199.5px) {
      height: 100%;
    }
  }
  .student_name {
    width: 200px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    font-size: 14px;
    font-weight: 600;
  }
  .student_name2 {
    width: 130px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    font-size: 14px;
    font-weight: 600;
  }
`;

interface EnhancedTableProps {
  handleSelectAllClick: (event: React.ChangeEvent<HTMLInputElement>) => void;
  selected: Stdtype[];
}

function EnhancedTableHead(props: EnhancedTableProps) {
  const { handleSelectAllClick, selected } = props;

  return (
    <TableHead>
      <TableRow>
        <TableCell padding="checkbox">
          <Checkbox
            indeterminate={selected.length > 0 && selected.length < studentsDetails.length}
            checked={selected.length === studentsDetails.length}
            onChange={handleSelectAllClick}
            inputProps={{ 'aria-label': 'select all rows' }}
          />
        </TableCell>
        {sessionDetailedHead.map((headCell) => (
          <TableCell key={headCell.id}>{headCell.label}</TableCell>
        ))}
      </TableRow>
    </TableHead>
  );
}

function SessionDetailedClassMarking() {
  const [popup, setPopup] = React.useState(false);
  const [studentData, setStudentData] = useState<Stdtype[] | undefined>([]);
  const [selected, setSelected] = useState<Stdtype[]>([]);
  const [year, setYear] = useState<string | null>('');
  const [Class, setClass] = useState<string | null>('');

  const handleClickOpen = () => setPopup(true);
  const handleClickClose = () => setPopup(false);

  const handleSearch = () => {
    const findSudents: Stdtype[] | undefined =
      studentsDetails && studentsDetails?.length > 0
        ? studentsDetails?.filter((list) => list?.year === year && list?.class === Class)
        : undefined;
    setStudentData(findSudents);
    console.log('filterdata', findSudents);
  };

  const handleSelectAllClick = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      setSelected(studentsDetails);
    } else {
      setSelected([]);
    }
  };

  const handleRowClick = (row: Stdtype) => {
    const selectedIndex = selected.indexOf(row);
    let newSelected: Stdtype[] = [];

    if (selectedIndex === -1) {
      newSelected = newSelected.concat(selected, row);
    } else if (selectedIndex === 0) {
      newSelected = newSelected.concat(selected.slice(1));
    } else if (selectedIndex === selected.length - 1) {
      newSelected = newSelected.concat(selected.slice(0, -1));
    } else if (selectedIndex > 0) {
      newSelected = newSelected.concat(selected.slice(0, selectedIndex), selected.slice(selectedIndex + 1));
    }

    setSelected(newSelected);
  };
  console.log('data', selected);
  const isSelected = (row: Stdtype) => selected.indexOf(row) !== -1;
  const isButtonDisabled = selected.length === 0;
  const isCancell = () => setSelected([]);

  return (
    <Page title="Detailed Class Marking Session">
      <DetailedClassMarkingRoot>
        <Card className="Card" elevation={5} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Typography variant="h6" fontSize={17}>
            Detailed Class Marking Session
          </Typography>
          <Divider />
          <Grid pb={4} pt={2} container spacing={3}>
            <Grid item lg={2.4} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Select Year
              </Typography>
              <Autocomplete
                options={YEAR_SELECT}
                value={year}
                onChange={(e, v) => setYear(v)}
                renderInput={(params) => (
                  <TextField onChange={({ target }) => setYear(target.value)} {...params} placeholder="Select" />
                )}
              />
            </Grid>
            <Grid item lg={2.4} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Select Class
              </Typography>
              <Autocomplete
                options={CLASS_SELECT}
                onChange={(e, v) => setClass(v)}
                value={Class}
                renderInput={(params) => (
                  <TextField onChange={({ target }) => setClass(target.value)} {...params} placeholder="Select" />
                )}
              />
            </Grid>
            <Grid item lg={2.4} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Select Date
              </Typography>
              <DateSelect />
            </Grid>
            <Grid item lg={2.4} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Select Session
              </Typography>
              <Autocomplete
                options={SESSION_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select" />}
              />
            </Grid>
            <Grid item lg={2.4} xs={12}>
              <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                <Button variant="contained" color="secondary" size="medium" fullWidth>
                  Reset
                </Button>
                <Button onClick={handleSearch} variant="contained" color="primary" size="medium" fullWidth>
                  Search
                </Button>
              </Stack>
            </Grid>
          </Grid>
          {studentData && studentData?.length === 0 && (
            <Stack
              direction="row"
              display="flex"
              justifyContent="center"
              alignItems="center"
              height="calc(100vh - 390px)"
              width="100%"
            >
              <Box>
                <img className="Notsuccess_img_bg" src={NotSuccess} alt="" width={150} />
                <Typography textAlign="center" pt={2} variant="body2">
                  No data found.
                </Typography>
              </Box>
            </Stack>
          )}
          {studentData && studentData?.length > 0 && (
            <Stack sx={{ width: '100%', flexDirection: { xs: 'column', md: 'row' } }} gap={3}>
              <Paper
                sx={{
                  border: `1px solid #e8e8e9`,
                  width: { xs: '100%', md: '60%' },
                  height: '100%',
                  overflow: 'auto',
                  '&::-webkit-scrollbar': {
                    height: 0,
                  },
                }}
              >
                <TableContainer
                  sx={{
                    width: { xs: '650px', md: '100%' },
                    '&::-webkit-scrollbar': {
                      width: 0,
                    },
                  }}
                >
                  <Table stickyHeader aria-labelledby="tableTitle">
                    <EnhancedTableHead selected={selected} handleSelectAllClick={handleSelectAllClick} />
                    <TableBody>
                      {studentData &&
                        studentData?.length > 0 &&
                        studentData?.map((row) => {
                          const isRowSelected = isSelected(row);

                          return (
                            <TableRow
                              hover
                              onClick={() => handleRowClick(row)}
                              role="checkbox"
                              aria-checked={isRowSelected}
                              tabIndex={-1}
                              key={row.studentName}
                              selected={isRowSelected}
                              sx={{ cursor: 'pointer' }}
                            >
                              <TableCell padding="checkbox">
                                <Checkbox
                                  color="primary"
                                  checked={isRowSelected}
                                  inputProps={{ 'aria-labelledby': `checkbox-${row.rollNo}` }}
                                />
                              </TableCell>
                              <TableCell sx={{ fontWeight: 600 }} width={100}>
                                {row.rollNo}
                              </TableCell>
                              <TableCell width={300}>
                                <Stack direction="row" gap={1} alignItems="center">
                                  <Avatar src={row.image} />
                                  <Typography className="student_name">{row.studentName}</Typography>
                                </Stack>
                              </TableCell>
                              <TableCell
                                width={200}
                                sx={{
                                  fontWeight: 600,
                                  color: `${isRowSelected ? ' red' : 'green'}`,
                                }}
                              >
                                {isRowSelected ? row.attendanceStatus : 'Present'}
                              </TableCell>
                            </TableRow>
                          );
                        })}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Paper>
              {/* ----------------------- Absentees List Table ------------------------------------------ */}
              <Paper
                sx={{
                  border: '1px solid #e8e8e9',
                  overflow: 'auto',
                  height: '100%',
                  width: { xs: '100%', md: '40%' },
                  '&::-webkit-scrollbar': {
                    width: 0,
                    height: 0,
                  },
                }}
              >
                <Typography variant="h6" p={2}>
                  Absentees List
                </Typography>
                {selected && selected?.length > 0 && (
                  <TableContainer
                    sx={{
                      width: { xs: '450px', md: '100%' },
                      height: 'calc(100vh - 376px)',
                      '&::-webkit-scrollbar': {
                        width: 0,
                      },
                    }}
                  >
                    <Table stickyHeader>
                      <TableHead>
                        <TableRow>
                          {sessionDetailedHead.map((headCell) => (
                            <TableCell key={headCell.id}>{headCell.label}</TableCell>
                          ))}
                          <TableCell> </TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {selected?.map((row) => (
                          <TableRow>
                            <TableCell sx={{ fontWeight: 600 }}>{row.rollNo}</TableCell>
                            <TableCell>
                              <Typography className="student_name2"> {row.studentName}</Typography>
                            </TableCell>
                            <TableCell sx={{ color: 'red', fontWeight: 600 }}>{row.attendanceStatus}</TableCell>
                            <TableCell align="center">
                              <IconButton onClick={() => handleRowClick(row)}>
                                <RemoveCircleOutlineIcon sx={{ color: 'red' }} />
                              </IconButton>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                )}
                {selected && selected?.length === 0 && (
                  <Stack
                    direction="column"
                    display="flex"
                    justifyContent="center"
                    alignItems="center"
                    width="100%"
                    sx={{ height: 'calc(100vh - 376px)' }}
                  >
                    <Box pb={2}>
                      <img className="Notsuccess_img_bg" src={NotSuccess} alt="" />
                    </Box>

                    <Typography textAlign="center" variant="body2">
                      Not selected students.
                    </Typography>
                  </Stack>
                )}
              </Paper>
            </Stack>
          )}
          {studentData && studentData?.length > 0 && (
            <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 2 }}>
              <Stack spacing={2} direction="row">
                <Button
                  variant="contained"
                  onClick={isCancell}
                  disabled={isButtonDisabled}
                  color="secondary"
                  size="medium"
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleClickOpen}
                  disabled={isButtonDisabled}
                  variant="contained"
                  color="primary"
                  size="medium"
                >
                  Confirm
                </Button>
              </Stack>
            </Box>
          )}
        </Card>

        <Popup
          size="xs"
          state={popup}
          onClose={handleClickClose}
          popupContent={<SuccessMessage message=" Passed Absentees Marked Successfully" />}
        />
      </DetailedClassMarkingRoot>
    </Page>
  );
}

export default SessionDetailedClassMarking;
