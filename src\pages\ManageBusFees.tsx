import * as React from 'react';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';
import Page from '@/components/shared/Page';
import styled from 'styled-components';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import { Link, useLocation, useOutletContext } from 'react-router-dom';
import BusList from '@/features/ManageBusFees/BusList';
import ManageTerms from '@/features/ManageBusFees/ManageTerms';
import BusStopMap from '@/features/ManageBusFees/BusStopMap';
import TermFeesMap from '@/features/ManageBusFees/TermFeesMap';
import TermStudentMap from '@/features/ManageBusFees/TermStudentMap';
import PayBusFee from '@/features/ManageBusFees/PayBusFee/PayBusFee';
import FeesPaidList from '@/features/ManageBusFees/FeesPaidList';
import FeesPendingList from '@/features/ManageBusFees/FeesPendingList';
import { useDispatch } from 'react-redux';
import { flushStore } from '@/store/flush.slice';
import { OutletContextType } from '@/types/Common';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const ManageBusFeesRoot = styled.div`
  min-height: calc(100vh - ${TOP_BAR_HEIGHT});
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  .tab {
    background-color: ${(props) => (props.theme.themeMode === 'light' ? '#fff' : props.theme.palette.grey[800])};
    /* @media screen and (min-width: 1414px) {
      width: 100%;
    }
    @media screen and (max-width: 720px) {
    } */
    width: 100%;
  }
  .Mui-selected {
    color: ${(props) => props.theme.palette.primary.main};
  }
`;
function TabPanel({ children, value, index }: TabPanelProps) {
  return (
    <Box
      sx={{ paddingTop: '3rem' }}
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
    >
      {value === index && <Box>{children}</Box>}
    </Box>
  );
}

function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`,
  };
}

export default function ManageBusFees() {
  const { topBarHeight } = useOutletContext<OutletContextType>();
  const location = useLocation();
  const [value, setValue] = React.useState(0);

  const dispatch = useDispatch();

  React.useEffect(() => {
    dispatch(flushStore());
    console.log('Flush Store');
  }, [dispatch, value]);

  React.useEffect(() => {
    const currentPath = location.pathname;
    const routePaths = [
      '/manage-bus-fees/bus-list',
      '/manage-bus-fees/bus-stop-map',
      '/manage-bus-fees/terms-list',
      '/manage-bus-fees/term-fees-map',
      '/manage-bus-fees/term-student-map',
      '/manage-bus-fees/pay-bus-fees',
      '/manage-bus-fees/paid-list',
      '/manage-bus-fees/pending-list',
    ];
    const index = routePaths.indexOf(currentPath);
    if (index !== -1) {
      setValue(index);
    }
  }, [location]);

  return (
    <Page title="Manage Fee">
      <ManageBusFeesRoot>
        <Tabs
          className="tab"
          variant="scrollable"
          scrollButtons
          allowScrollButtonsMobile
          value={value}
          aria-label="basic tabs"
          sx={{
            ml: '0px',
            borderBottom: 1,
            borderColor: 'divider',
            position: 'fixed',
             top: `${topBarHeight}px`,
            zIndex: 1,
            // width: '100%',
          }}
        >
          <Tab {...a11yProps(0)} label="Bus List" component={Link} to="/manage-bus-fees/bus-list" />
          <Tab {...a11yProps(1)} label="Bus Stop Map" component={Link} to="/manage-bus-fees/bus-stop-map" />
          <Tab {...a11yProps(2)} label="Manage Terms" component={Link} to="/manage-bus-fees/terms-list" />
          <Tab {...a11yProps(3)} label="Term Fees Map" component={Link} to="/manage-bus-fees/term-fees-map" />
          <Tab {...a11yProps(4)} label="Term Student Map" component={Link} to="/manage-bus-fees/term-student-map" />
          <Tab {...a11yProps(5)} label="Pay Fee" component={Link} to="/manage-bus-fees/pay-bus-fees" />
          <Tab {...a11yProps(6)} label="Fees Paid List" component={Link} to="/manage-bus-fees/paid-list" />
          <Tab {...a11yProps(7)} label="Fees Pending List" component={Link} to="/manage-bus-fees/pending-list" />
        </Tabs>
        <TabPanel value={value} index={0}>
          <BusList />
        </TabPanel>
        <TabPanel value={value} index={1}>
          <BusStopMap />
        </TabPanel>
        <TabPanel value={value} index={2}>
          <ManageTerms />
        </TabPanel>
        <TabPanel value={value} index={3}>
          <TermFeesMap />
        </TabPanel>
        <TabPanel value={value} index={4}>
          <TermStudentMap />
        </TabPanel>
        <TabPanel value={value} index={5}>
          <PayBusFee />
        </TabPanel>
        <TabPanel value={value} index={6}>
          <FeesPaidList />
        </TabPanel>
        <TabPanel value={value} index={7}>
          <FeesPendingList />
        </TabPanel>
      </ManageBusFeesRoot>
    </Page>
  );
}
