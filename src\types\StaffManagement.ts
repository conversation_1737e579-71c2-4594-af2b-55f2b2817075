import { FetchStatus } from './Common';
import { PagedData, PageRequestWithFilters, PaginationInfo } from './Pagination';

export type StaffListFilters = {
  adminId: number;
  academicId: number | string;
  staffCode: string;
  staffName: string;
  staffPhoneNumber: string;
};

export type StaffListInfo = {
  rowKey?: string;
  staffID?: number | undefined;
  academicId: number | string;
  staffCode: string;
  staffJoinDate: string;
  staffName: string;
  staffGender: number;
  staffDOB: string;
  staffBloodGroup: string;
  staffBirthPlace: string;
  staffNationality: string;
  staffMotherTongue: string;
  staffReligion: string;
  staffCaste: string;
  staffCastType: string;
  staffFatherName: string;
  staffMotherName: string;
  staffFatherOccupation: string;
  staffJobExperience: string;
  staffJobRole: string;
  staffJobDescription: string;
  staffPhoneNumber: string;
  staffPAddress: string;
  staffCAddress: string;
  staffEmailID: string;
  staffImage: string;
  staffStatus: string;
  staffCategory: number;
  createdBy: number;
};

export type CTSFilterInfo = {
  currentAcademicId: number;
  yearList: {
    academicId: number;
    academicTime: string;
  };
  classList: {
    classId: number;
    className: string;
  };
  staffList: {
    staffId: number;
    staffName: string;
  };
  subjectList: {
    subjectId: number;
    subjectName: string;
  };
};

type YearList = {
  academicId: number;
  academicTime: string;
};

type ClassList = {
  classId: number;
  className: string;
};
type StaffList = {
  staffId: number;
  staffName: string;
};
type SubjectList = {
  subjectId: number;
  subjectName: string;
};

export type CTSDetailsFilter = {
  adminId?: number;
  academicId?: number;
  classId?: number;
  staffId?: number;
  subjectId?: number;
};
export type CTSDetailsInfo = {
  cteacherId: number;
  academicId: number;
  classId: number;
  className: string;
  subjectId: number;
  subjectName: string;
  staffId: number;
  staffName: string;
  staffRole: number;
  academicTime: string;
  // totalrecords: number;
  // pagesize: number;
  // pagenumber: number;
  // totalpages: number;
  // remainingpages: number;
};

export type CTSAllocationCreateUpdateInfo = {
  adminId: number;
  academicId: number;
  classId: number;
  subjectId: number;
  staffId: number;
  staffRole: number;
  cteacherId?: number;
};

export type CTSAllocationMappingFilter = {
  adminId: number;
  academicId: number;
  classId: number;
};

export type CTSAllocationTeacherWiseMappingFilter = {
  adminId: number;
  academicId: number;
  staffId: number;
};

export type CTSAllocationMappedInfo = {
  id?: number;
  subjectId: number;
  subjectName?: string;
  cteacherId: number;
  staffId: number;
  staffRole: number;
  classId?: number;
};
export type CTSAllocationResponseInfo = {
  academicId: number;
  adminId: number;
  classId: number;
  dbResult: 'Success' | 'Exist' | 'Failed';
  id: number;
  staffId: number;
  staffRole: number;
  subjectId: number;
};
export type CTSAllocationMappedResponseInfo = CTSAllocationResponseInfo & CTSAllocationMappedInfo;

export type CTSAllocationStaffList = {
  staffId: number;
  staffName: string;
};

export type CTSAllocationSubjectList = {
  subjectId: number;
  subjectName: string;
};

export type CTSAllocationClassList = {
  classId: number;
  className: string;
};

export type AddCTSAllocationMapInfo = {
  adminId: number;
  academicId: number;
  classId: number;
  subjectId: number;
  staffId: number;
  staffRole: number;
  dbResult: string;
  id: number;
};

export type DeleteCTSAllocationRequest = {
  adminId: number;
  cteacherId: number | undefined;
  dbResult: string;
  id: number;
};
export type StaffCreateRequest = Omit<StaffListInfo, 'staffID'>;

export type StaffCreateRow = { rowKey: string } & StaffCreateRequest;

export type StaffListRequest = PageRequestWithFilters<StaffListFilters>;
export type StaffListPagedData = PagedData<StaffListInfo>;

export type CTSDetailsListRequest = PageRequestWithFilters<CTSDetailsFilter>;
export type CTSDetailsListPagedData = PagedData<CTSDetailsInfo>;

export type StaffManagementState = {
  staffList: {
    [key: string]: any;
    status: FetchStatus;
    data: StaffListInfo[];
    error: string | null;
    pageInfo: PaginationInfo;
    sortColumn: string;
    sortDirection: 'asc' | 'desc';
  };
  CTSFiltersList: {
    status: FetchStatus;
    data: CTSFilterInfo;
    yearList: YearList[];
    classList: ClassList[];
    subjectList: SubjectList[];
    staffList: StaffList[];
    error: string | null;
  };
  CTSDetailsList: {
    status: FetchStatus;
    data: CTSDetailsInfo[];
    error: string | null;
    pageInfo: PaginationInfo;
    sortColumn: string;
    sortDirection: 'asc' | 'desc';
  };
  CTSUpdateDetailList: {
    status: FetchStatus;
    data: CTSAllocationCreateUpdateInfo;
    error: string | null;
  };
  CTSAllocationMapedList: {
    status: FetchStatus;
    data: CTSAllocationMappedInfo[];
    staffList: CTSAllocationStaffList[];
    error: string | null;
  };
  CTSAllocationTeacherWiseMapedList: {
    status: FetchStatus;
    data: CTSAllocationMappedInfo[];
    subjectList: CTSAllocationSubjectList[];
    classList: CTSAllocationClassList[];
    error: string | null;
  };
  submitting: boolean;
  deletingRecords: Record<number, boolean>;
  error: string | null | undefined;
};
