import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import useAuth from '@/hooks/useAuth';
import { Box, Button, Divider } from '@mui/material';
import styled from 'styled-components';

const DetailedClassMarkingRoot = styled.div`
  min-height: calc(100vh - ${TOP_BAR_HEIGHT});
  padding: 15px;
`;

function DetailedClassMarking() {
  const { logout } = useAuth();

  return (
    <Page title="Detailed Class Marking">
      <DetailedClassMarkingRoot>
        <h3>Detailed Class Marking</h3>
        <Divider />
        <Box sx={{ mt: 1 }}>
          <Button
            variant="contained"
            color="secondary"
            size="small"
            type="button"
            disableElevation
            onClick={() => {
              logout();
            }}
          >
            Logout
          </Button>
        </Box>
      </DetailedClassMarkingRoot>
    </Page>
  );
}

export default DetailedClassMarking;
