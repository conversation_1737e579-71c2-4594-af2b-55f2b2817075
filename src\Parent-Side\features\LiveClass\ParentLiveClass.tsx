/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  useTheme,
  IconButton,
  Collapse,
  Tooltip,
  FormControl,
  Chip,
  Snackbar,
  Alert,
} from '@mui/material';
import styled from 'styled-components';
import { CLASS_SELECT, STATUS_SELECT, YEAR_SELECT } from '@/config/Selection';
import SearchIcon from '@mui/icons-material/Search';
import Popup from '@/components/shared/Popup/Popup';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import Physics from '@/Parent-Side/assets/liveclass/Icons/Physics.svg';
import { MdAdd, MdContentCopy } from 'react-icons/md';
import { CloseIcon } from '@/theme/overrides/CustomIcons';
import { Student, StudentInfoData } from '@/config/StudentDetails';
import useSettings from '@/hooks/useSettings';
import AutorenewOutlinedIcon from '@mui/icons-material/AutorenewOutlined';
import { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import DateSelect from '@/components/shared/Selections/DateSelect';
import MenuEditDelete from '@/components/shared/Selections/MenuEditDelete';
import CreateScheduleList from '@/features/LiveClass/ScheduleList/CreateScheduleList';
import LiveClass from '@/Parent-Side/assets/liveclass/LiveClass.png';
import PasswordField from '@/components/shared/Selections/PasswordField';
import LoadingButton from '@mui/lab/LoadingButton';

export const data = [
  {
    createdBy: 'Passdaily',
    class: 'VII-B',
    meetingTitle: 'Daily Class',
    meetingID: 'IDGHDGB',
    academicYear: '2023-2024',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
  {
    createdBy: 'Passdaily',
    class: 'VI-C',
    academicYear: '2023-2024',
    meetingID: 'IDGHDGB',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
  {
    createdBy: 'Passdaily',
    class: 'XII-B',
    meetingTitle: 'Peter',
    academicYear: '2023-2024',
    meetingID: 'IDGHDGB',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
  {
    createdBy: 'Passdaily',
    class: 'V-A',
    meetingTitle: 'Daily Class Mic',
    academicYear: '2023-2024',
    meetingID: 'IDGHDGB',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
  {
    createdBy: 'Passdaily',
    class: 'II-B',
    meetingTitle: 'john',
    academicYear: '2023-2024',
    meetingID: 'IDGHDGB',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
  {
    createdBy: 'Passdaily',
    class: 'XII-C',
    meetingTitle: 'Micheal',
    academicYear: '2023-2024',
    meetingID: 'IDGHDGB',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
  {
    createdBy: 'Passdaily',
    class: 'I-B',
    meetingTitle: 'jack',
    academicYear: '2023-2024',
    meetingID: 'IDGHDGB',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
  {
    createdBy: 'Passdaily',
    class: 'VII-A',
    meetingTitle: 'Daily Class',
    academicYear: '2023-2024',
    meetingID: 'IDGHDGB',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
  {
    createdBy: 'Passdaily',
    class: 'VI-C',
    meetingTitle: 'Daily Class',
    academicYear: '2023-2024',
    meetingID: 'IDGHDGB',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
  {
    createdBy: 'Passdaily',
    class: 'X-B',
    meetingTitle: 'Daily Class',
    academicYear: '2023-2024',
    meetingID: 'IDGHDGB',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
];

const LiveClassRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  .student_card {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  }

  .Card {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 40px);
      flex-grow: 1;

      .card-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: auto;
        ::-webkit-scrollbar {
          width: 0px;
        }

        .MuiTableContainer-root {
          max-height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

function ParentLiveClass() {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const [changeView, setChangeView] = useState(false);
  const [showFilter, setShowFilter] = useState(false);
  const [Delete, setDelete] = useState(false);
  const [createPopup, setCreatePopup] = useState(false);

  const [students, setStudents] = useState(StudentInfoData);
  const [open, setOpen] = useState(false);
  const [openNew, setOpenNew] = useState(false);
  const [showNewMultiple, setShowNewMultiple] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [updatedData, setData] = useState(data);

  const handleStatusChange = (index) => {
    const copyData = [...updatedData];
    copyData[index].status = copyData[index].status === 'Published' ? 'Unpublished' : 'Published';
    setData(copyData);
  };

  const handleToggle = () => {
    setChangeView((prevChangeView) => !prevChangeView);
  };

  const handleClickDelete = () => setDelete(true);
  const handleClickCloseDelete = () => setDelete(false);
  const handlePageChange = useCallback((event: unknown, newPage: number) => {
    // loadClassList({ ...currentClassListRequest, pageNumber: newPage + 1 });
  }, []);

  const handleChangeRowsPerPage = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const newPageSize = +event.target.value;
    // loadClassList({ ...currentClassListRequest, pageNumber: 1, pageSize: newPageSize });
  }, []);

  const pageProps = useMemo(
    () => ({
      rowsPerPageOptions: [10, 20, 30],
      pageNumber: 1,
      pageSize: 10,
      totalRecords: 100,
      onPageChange: handlePageChange,
      onRowsPerPageChange: handleChangeRowsPerPage,
    }),
    [handleChangeRowsPerPage, handlePageChange]
  );

  const handleClose = () => {
    setOpen(false);
  };
  const handleOpenNew = () => {
    setOpenNew(true);
  };

  const handleCloseNew = () => {
    setOpenNew(false);
  };
  const handleToggleNewMultiple = () => {
    setShowNewMultiple((prevState) => !prevState);
  };

  const handleUpdate = (id: number, updatedData: Student) => {
    const updatedStudents = students.map((student) => (student.id === id ? { ...student, ...updatedData } : student));
    setStudents(updatedStudents);
    setOpen(false);
  };

  const getRowKey = useCallback((row: any) => row.examId, []);

  const [isSnackbarOpen, setIsSnackbarOpen] = useState(false);

  const handleCopyClick = (text: string) => {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        setIsSnackbarOpen(true);
      })
      .catch((err) => {
        console.error('Unable to copy link to clipboard', err);
      });
  };

  const handleCloseSnackbar = () => {
    setIsSnackbarOpen(false);
  };

  const LiveClassColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'class',
        dataKey: 'class',
        headerLabel: 'Class',
      },
      {
        name: 'startTime',
        dataKey: 'startTime',
        headerLabel: 'Start Time',
      },
      {
        name: 'teacher',
        dataKey: 'teacher',
        headerLabel: 'Teacher',
      },
      {
        name: 'student',
        headerLabel: 'Student',
        renderCell: (row) => {
          return (
            <Stack direction="row" alignItems="center" maxWidth="90%">
              <Tooltip title={`${row.meetingLink}`} arrow>
                <Chip
                  size="small"
                  label="40"
                  variant="outlined"
                  sx={{
                    border: '0px',
                    mr: '1px',
                    mb: 2,
                    backgroundColor: isLight ? theme.palette.info.lighter : theme.palette.grey[700],
                    color: isLight ? theme.palette.info.dark : theme.palette.grey[100],
                    width: 'auto',
                  }}
                />
              </Tooltip>
            </Stack>
          );
        },
      },
    ],
    [theme, isLight]
  );

  //   const renderContent = () => {
  //     return students.map((student, rowIndex) => (
  //       <Card key={student.id}>
  //         {LiveClassColumns.map((column) => (
  //           <Typography key={column.name}>{student[column.dataKey]}</Typography>
  //         ))}
  //       </Card>
  //     ));
  //   };

  // const content: ReactNode = data.map((item, rowIndex: number) => {
  //   let cellData: ReactNode = null;

  //   return (
  //     <Typography key={column.name} variant="subtitle1" mb={1} fontSize={13}>
  //       <b>{cellData}</b>
  //     </Typography>
  //   );
  // });

  // Content = content;

  return (
    <Page title="Schedule List">
      <LiveClassRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17} width="100%">
              Zoom Live Class
            </Typography>
          </Stack>
          <Stack>
            <Divider />
          </Stack>
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={2}>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Autocomplete
                        options={YEAR_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select year" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Autocomplete
                        options={CLASS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select class" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Meeting Title
                      </Typography>
                      <TextField fullWidth placeholder="Enter title" />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Select Date
                      </Typography>
                      <DateSelect />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" md={6} xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', lg: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Status
                      </Typography>
                      <Autocomplete
                        options={STATUS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select" />}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item lg={1} xs={12}>
                    <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                      <Button variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                      {/* <Button variant="contained" color="primary" fullWidth>
                          Search
                        </Button> */}
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Box className="card-container" display="flex" justifyContent="center" alignItems="center" my={2}>
              <Stack>
                <img width={250} src={LiveClass} alt="" />
              </Stack>
              <Stack width="30%">
                <form noValidate>
                  <div className="w-100 mb-3 mt-3">
                    <TextField
                      id="name"
                      name="name"
                      variant="outlined"
                      color="primary"
                      placeholder="Enter name"
                      fullWidth
                      autoComplete="off"
                      // value={username}
                      // onChange={handleChange}
                      // error={touched.username && Boolean(errors.username)}
                      // helperText={
                      //   touched.username && errors.username && <span style={{ color: 'red' }}>{errors.username}</span>
                      // }
                    />
                  </div>
                  <div className="w-100 mb-3">
                    <TextField
                      id="meetingId"
                      name="meetingId"
                      variant="outlined"
                      color="primary"
                      placeholder="Enter meeting id"
                      fullWidth
                      autoComplete="off"
                      // value={username}
                      // onChange={handleChange}
                      // error={touched.username && Boolean(errors.username)}
                      // helperText={
                      //   touched.username && errors.username && <span style={{ color: 'red' }}>{errors.username}</span>
                      // }
                    />
                  </div>
                  <div className="w-100 mb-4">
                    <PasswordField
                      id="password"
                      name="password"
                      color="primary"
                      placeholder="Enter password"
                      passwordViewable
                      fullWidth
                      // value={password}
                      // onChange={handleChange}
                      // error={touched.password && Boolean(errors.password)}
                      // helperText={touched.password && errors.password}
                    />
                  </div>
                  <div className="w-100 mb-3">
                    <LoadingButton
                      // loading={loading}
                      loadingPosition="start"
                      startIcon={<span />}
                      fullWidth
                      type="submit"
                      variant="contained"
                      color="success"
                    >
                      Join
                    </LoadingButton>
                  </div>
                </form>
              </Stack>
            </Box>
          </div>
        </Card>
      </LiveClassRoot>
      <Popup
        size="xs"
        state={Delete}
        onClose={handleClickCloseDelete}
        popupContent={<DeleteMessage message="Are you sure want to delete?" />}
      />
      <Popup
        size="md"
        title="Schedule New Live Class"
        state={createPopup}
        onClose={() => setCreatePopup(false)}
        popupContent={<CreateScheduleList />}
      />
    </Page>
  );
}

export default ParentLiveClass;
