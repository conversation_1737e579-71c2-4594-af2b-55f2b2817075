import { useEffect, useCallback, useRef, useState, FormEvent } from 'react';
import { STATUS_OPTIONS } from '@/config/Selection';
import { ClassCreateRow } from '@/types/AcademicManagement';
import {
  Stack,
  Typography,
  Button,
  Table,
  TableHead,
  TableRow,
  Paper,
  TableBody,
  TableCell,
  TableContainer,
  TextField,
  MenuItem,
  Select,
  IconButton,
  Card,
  Tooltip,
  Alert,
} from '@mui/material';
import { FormikErrors, useFormik } from 'formik';
import { MdArrowBack } from 'react-icons/md';
import styled from 'styled-components';
import { v4 as uuidv4 } from 'uuid';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import Page from '@/components/shared/Page';
import ErrorIcon from '@mui/icons-material/Error';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { addNewClassMulti } from '@/store/Academics/ClassManagement/classManagement.thunks';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import * as Yup from 'yup';
import api from '@/api';
import BackButton from '@/components/shared/BackButton';

export type CreateClassMultipleProps = {
  onBackClick: () => void;
};

export type CreateClassMultipleState = {
  classes: ClassCreateRow[];
};

const CreateClassMultipleRoot = styled.div`
  padding: 1rem;

  .Card {
    height: calc(100vh - 160px);
    display: flex;
    flex-direction: column;

    form {
      height: calc(100% - 40px);
      display: flex;
      flex-direction: column;

      .form-container {
        flex-grow: 1;
        height: calc(100% - 84px);

        .MuiTableContainer-root {
          height: 100%;
          overflow: auto;
          overflow-x: hidden;
        }
      }

      .button-container {
        border-top: 1px solid #ddd;
      }
    }
  }
`;

const formValidationSchema = Yup.object({
  classes: Yup.array().of(
    Yup.object({
      className: Yup.string()
        .required('Please enter class name')
        .test('exists', 'Class name already used', async (val) => {
          try {
            if (val) {
              const existsResponse = await api.ClassManagement.ClassNameExists(val);
              return !existsResponse.data;
            }

            return true;
          } catch {
            return true;
          }
        }),
    })
  ),
});

function CreateClassMultiple({ onBackClick }: CreateClassMultipleProps) {
  const dispatch = useAppDispatch();
  const { confirm } = useConfirm();
  const [error, setError] = useState<string | null>(null);
  const scrollContainerRef = useRef<any>(null);
  const textBoxRefs = useRef<(HTMLInputElement | null)[]>([]);

  const initialRowKey = uuidv4();

  const getNewRow = useCallback(
    (): ClassCreateRow => ({ rowKey: uuidv4(), className: '', classDescription: '', classStatus: 1 }),
    []
  );

  const defaultRow: ClassCreateRow = { rowKey: initialRowKey, className: '', classDescription: '', classStatus: 1 };

  const { values, handleChange, handleBlur, handleSubmit, setFieldValue, touched, errors, resetForm } =
    useFormik<CreateClassMultipleState>({
      initialValues: {
        classes: [defaultRow],
      },
      onSubmit: async (data) => {
        try {
          const response = await dispatch(addNewClassMulti(data.classes)).unwrap();

          if (response.inserted) {
            const successMessage = <SuccessMessage message={`${data.classes.length} Classes created successfully`} />;
            await confirm(successMessage, 'Classes Created', { okLabel: 'Ok', showOnlyOk: true });
            resetForm();
            setFieldValue('classes', []);
            if (response.inserted) {
              setError(null);
              onBackClick();
            } else {
              setError('Something went wrong in creating classes');
            }
          }
        } catch {
          setError('Something went wrong in creating classes');
        }
      },
      validateOnBlur: false,
      validationSchema: formValidationSchema,
      validate: (classVals) => {
        const errorObj: any = {};
        classVals.classes.forEach(async (classRow, rowIndex, arr) => {
          if (arr.some((x, i) => classRow.className !== '' && x.className === classRow.className && i !== rowIndex)) {
            if (!errorObj.classes) {
              errorObj.classes = [];
            }
            errorObj.classes[rowIndex] = {};
            errorObj.classes[rowIndex].className = 'Duplicate class name';
          }
        });
        return errorObj;
      },
    });

  useEffect(() => {
    textBoxRefs.current[values.classes.length - 1]?.focus();
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollTop = scrollContainerRef.current.scrollHeight;
    }
  }, [values.classes.length]);

  const handleAddNewRow = () => {
    const newRow = getNewRow();
    values.classes = [...values.classes, newRow];
    setFieldValue('classes', values.classes);
  };

  const handleRowDelete = (rowkey: string) => {
    const updatedRows = values.classes.filter((x) => x.rowKey !== rowkey);
    setFieldValue('classes', updatedRows);
  };

  const hasClassNameFieldError = (rowIndex: number) => {
    if (touched.classes && touched.classes.length > 0 && errors.classes && errors.classes.length > 0) {
      return (
        !!touched.classes[rowIndex]?.className &&
        !!(errors.classes as FormikErrors<ClassCreateRow>[])[rowIndex]?.className
      );
    }

    return false;
  };

  const getClassNameFieldError = (rowIndex: number) => {
    return (errors.classes as FormikErrors<ClassCreateRow>[])[rowIndex]?.className;
  };

  const handleReset = async () => {
    if (await confirm('Are you sure you want to reset form?', 'Reset?')) {
      resetForm();
      setFieldValue('classes', [defaultRow]);
    }
  };

  const handleResetForm = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    handleReset();
  };

  return (
    <Page title="Create Multiple Classes">
      <CreateClassMultipleRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" alignItems="center" justifyContent="space-between" spacing={2}>
            <BackButton onBackClick={onBackClick} title="Add Multiple Class" />
            <Button
              type="button"
              variant="contained"
              size="small"
              sx={{ borderRadius: '20px' }}
              startIcon={<AddIcon />}
              onClick={handleAddNewRow}
            >
              Add New Row
            </Button>
          </Stack>
          <form noValidate onSubmit={handleSubmit} onReset={handleResetForm}>
            <div className="form-container">
              {!!error && (
                <Alert color="error" sx={{ marginBottom: '10px' }}>
                  {error}
                </Alert>
              )}
              <TableContainer component={Paper} ref={scrollContainerRef}>
                <Table sx={{ minWidth: 650 }} aria-label="simple table" stickyHeader>
                  <TableHead>
                    <TableRow>
                      <TableCell>Class Name</TableCell>
                      <TableCell>Class Desc.</TableCell>
                      <TableCell>Class Status</TableCell>
                      <TableCell>&nbsp;</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {values.classes.map((classRow, rowIndex) => (
                      <TableRow
                        key={classRow.rowKey}
                        sx={rowIndex === values.classes.length - 1 ? { '& td': { border: 0 } } : null}
                      >
                        <TableCell>
                          <TextField
                            name={`classes[${rowIndex}].className`}
                            // label="Class Name"
                            value={classRow.className}
                            variant="outlined"
                            onChange={handleChange}
                            onBlur={handleBlur}
                            placeholder="Class name"
                            fullWidth
                            error={hasClassNameFieldError(rowIndex)}
                            inputRef={(el) => {
                              textBoxRefs.current[rowIndex] = el;
                            }}
                            InputProps={{
                              endAdornment: hasClassNameFieldError(rowIndex) && (
                                <Tooltip title={getClassNameFieldError(rowIndex)} arrow>
                                  <ErrorIcon color="error" />
                                </Tooltip>
                              ),
                            }}
                          />
                        </TableCell>
                        <TableCell>
                          <TextField
                            name={`classes[${rowIndex}].classDescription`}
                            // label="Class Description"
                            value={classRow.classDescription}
                            variant="outlined"
                            onChange={handleChange}
                            onBlur={handleBlur}
                            placeholder="Class description"
                            fullWidth
                          />
                        </TableCell>
                        <TableCell>
                          <Select
                            name={`classes[${rowIndex}].classStatus`}
                            value={classRow.classStatus}
                            onChange={handleChange}
                            fullWidth
                          >
                            {STATUS_OPTIONS.map((opt) => (
                              <MenuItem key={opt.id} value={opt.id}>
                                {opt.name}
                              </MenuItem>
                            ))}
                          </Select>
                        </TableCell>
                        <TableCell>
                          <IconButton
                            aria-label="Delete Row"
                            onClick={() => handleRowDelete(classRow.rowKey)}
                            disabled={values.classes.length === 1}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </div>
            <Stack className="button-container" direction="row" justifyContent="end" gap={1} padding={2}>
              <Button type="button" color="secondary" variant="contained" onClick={handleReset}>
                Reset
              </Button>
              <Button color="primary" variant="contained" type="submit">
                Save
              </Button>
            </Stack>
          </form>
        </Card>
      </CreateClassMultipleRoot>
    </Page>
  );
}

export default CreateClassMultiple;
