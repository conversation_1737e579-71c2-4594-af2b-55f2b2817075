import { Dayjs } from 'dayjs';
import { createContext, Dispatch, SetStateAction } from 'react';

export type EventType = {
  day: number;
  description: string;
  id: number;
  label: string;
  title: string;
  type?: string;
};

export type LabelType = {
  label: string;
  checked: boolean;
  name?: string;
  type?: string;
};

export type CalendarContextType = {
  monthIndex: number;
  setMonthIndex: Dispatch<SetStateAction<number>>;
  smallCalendarMonth: number;
  setSmallCalendarMonth: Dispatch<SetStateAction<number>>;
  daySelected: Dayjs;
  setDaySelected: Dispatch<SetStateAction<Dayjs>>;
  showEventModal: boolean;
  setShowEventModal: Dispatch<SetStateAction<boolean>>;
  dispatchCalEvent: Dispatch<{ type: string; payload?: any }>;
  selectedEvent: EventType;
  setSelectedEvent: Dispatch<SetStateAction<EventType>>;
  savedEvents: EventType[];
  setLabels: Dispatch<SetStateAction<LabelType[]>>;
  labels: LabelType[];
  updateLabel: (label: LabelType) => void;
  filteredEvents: EventType[];
};

const CalendarContext = createContext<CalendarContextType>({
  monthIndex: 0,
  setMonthIndex: () => {},
  smallCalendarMonth: 0,
  setSmallCalendarMonth: () => {},
  daySelected: null,
  setDaySelected: () => {},
  showEventModal: false,
  setShowEventModal: () => {},
  dispatchCalEvent: () => {},
  savedEvents: [],
  selectedEvent: null,
  setSelectedEvent: () => {},
  setLabels: () => {},
  labels: [],
  updateLabel: () => {},
  filteredEvents: [],
});

export default CalendarContext;
