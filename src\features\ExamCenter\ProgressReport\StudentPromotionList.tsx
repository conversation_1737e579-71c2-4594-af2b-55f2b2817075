import React, { useRef, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Box,
  Button,
  Divider,
  FormControl,
  Card,
  Stack,
  Collapse,
  Grid,
  IconButton,
  TextField,
  Typography,
  Select,
  MenuItem,
  Paper,
  useTheme,
  Table,
  TableHead,
  TableCell,
  TableBody,
  TableRow,
  TableContainer,
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import styled from 'styled-components';
import { EXAM_SELECT_OPTIONS } from '@/config/Selection';
import CircleIcon from '@mui/icons-material/Circle';
import useSettings from '@/hooks/useSettings';
import Excel from '@/assets/attendance/Excel.svg';
import { breakPointsMaxwidth } from '@/config/breakpoints';
import { useReactToPrint } from 'react-to-print';
import TabButton from '@/components/shared/TabButton';

const SubjectDemo = ['SS', 'ISL', 'ENG', 'MAL', 'HIN', 'ARA', 'PHY', 'CHE', 'BIO', 'CS', 'MAT', 'BS'];

const StudentPromotionListRoot = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    min-height: calc(100vh - 100px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      height: calc(100% - 100px);
      flex-grow: 1;
      .main-card-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid ${(props) => props.theme.palette.secondary.lighter};
        border-radius: 6px;
        overflow: hidden;
        .card-top {
          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.primary.lighter : props.theme.palette.grey[900]};
        }

        .card-table-container {
          flex-grow: 1;
          width: 100%;
          height: 100%;
          display: flex;
          flex-direction: column;
          border: 1px solid ${(props) => props.theme.palette.secondary.lighter};
          overflow: hidden;
          border-radius: 0px;

          .MuiTableContainer-root {
            height: 100%;
          }
          .MuiTable-root {
          }

          .MuiTablePagination-root {
            flex-grow: 1;
            flex-shrink: 0;
          }
          .MuiTableCell-root {
            border: 1px solid ${(props) => props.theme.palette.secondary.lighter};
            border-radius: 0px;
          }
        }
      }
      .avg_circle_icon {
        font-size: 10px;
        color: ${(props) => props.theme.palette.primary.light};
        margin-right: 3px;
      }
      @media ${breakPointsMaxwidth.xl} {
        .MuiTableCell-root {
          font-size: 11px;
        }
        .MuiTableCell-root .MuiTypography-root {
          font-size: 11px;
        }
      }
      @media screen and (max-width: 1217px) {
        .MuiFormControl-root {
          width: 200px;
        }
        .select_box {
          width: 200px;
        }
      }
      @media screen and (max-width: 1160px) {
        .MuiTableContainer-root {
          /* width: 900px; */
        }
        .card-table-container {
          /* overflow: auto; */
        }
      }
    }
    .card_top {
      display: flex;
      justify-content: space-between;
      flex-direction: row;
    }
    .title_searchbar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex: 1;
    }
    .progress_report_tab {
      display: flex;
      align-items: center;
      flex-direction: row;
    }
    @media screen and (max-width: 1160px) {
      .card_top {
        display: flex;
        flex-direction: column;
      }
      .progress_report_tab {
        display: flex;
        justify-content: end;
        padding-bottom: 10px;
        overflow-x: scroll;
        ::-webkit-scrollbar {
          height: 10px;
        }
        ::-webkit-scrollbar-thumb {
          background-color: ${(props) => props.theme.palette.grey[400]};
          border-radius: 20px;
        }
      }
    }
    @media screen and (max-width: 998px) {
      .progress_report_tab {
        justify-content: start;
      }
    }
    @media screen and (max-width: 768px) {
      .progress_report_tab {
        ::-webkit-scrollbar {
          height: 0px;
        }
      }
    }
  }
`;
export type StudentPromotionListProps = {
  onClickStudentWise: () => void;
  onClickClassWise: () => void;
  onClickTopper: () => void;
  onClickGradeWise: () => void;
};
function StudentPromotionList({
  onClickStudentWise,
  onClickClassWise,
  onClickTopper,
  onClickGradeWise,
}: StudentPromotionListProps) {
  const [showFilter, setShowFilter] = useState(false);
  const theme = useTheme();
  const { themeMode } = useSettings();

  const componentRef = useRef<HTMLInputElement>(null);
  const handlePrint = useReactToPrint({
    content: () => componentRef.current,
    pageStyle: `
      @page {
        size: landscape;
      }
      @media print {
        body {
          -webkit-print-color-adjust: exact;
        }
      }
    `,
  });

  return (
    <Page title="Student Promotion List">
      <StudentPromotionListRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <div className="card_top ">
            <div className="title_searchbar">
              <Typography variant="h6" fontSize={17}>
                Student Promotion List
              </Typography>

              <Box sx={{ flexShrink: 0 }}>
                <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                  <SearchIcon />
                </IconButton>
              </Box>
            </div>
            <Stack className="progress_report_tab">
              <div style={{ flexShrink: 0 }}>
                <TabButton title="Student Individual" variant="outlined" onClick={onClickStudentWise} />
                <TabButton title="Student Promotion List" variant="contained" />
                <TabButton title="Class Wise" variant="outlined" onClick={onClickClassWise} />
                <TabButton title="Topper" variant="outlined" onClick={onClickTopper} />
                <TabButton title="Grade Wise" variant="outlined" onClick={onClickGradeWise} />
              </div>
            </Stack>
          </div>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={3} alignItems="end">
                  <Grid item sm="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', xl: 240 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Year
                      </Typography>
                      <TextField name="year" sx={{ minWidth: { xs: '100%', xl: 240 } }} />
                    </FormControl>
                  </Grid>
                  <Grid item sm="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', xl: 240 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <TextField name="className" sx={{ minWidth: { xs: '100%', xl: 240 } }} />
                    </FormControl>
                  </Grid>
                  <Grid item sm="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', xl: 240 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Exam
                      </Typography>
                      <Select
                        sx={{ minWidth: { xs: '100%', xl: 240 } }}
                        className="select_box"
                        labelId="classStatusFilter"
                        id="classStatusFilterSelect"
                      >
                        <MenuItem value={-1}>All</MenuItem>
                        {EXAM_SELECT_OPTIONS.map((opt) => (
                          <MenuItem key={opt.id} value={opt.id}>
                            {opt.exam}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item sm="auto" xs={12}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button type="reset" variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>
            <Box ref={componentRef} className="main-card-container" sx={{ mt: 2 }}>
              <Box className="card-top text-center" sx={{ py: 2 }}>
                <Stack direction="column" gap={1}>
                  <Typography variant="h6" fontSize={17} sx={{ color: theme.palette.primary.main }}>
                    PROMOTION RECORD OF THE YEAR - 2022-2023
                  </Typography>
                  <Typography variant="h6" fontSize={15}>
                    MODERN HIGHER SECONDARY SCHOOL, POTTUR
                  </Typography>
                  <Typography variant="h5" fontSize={14}>
                    <Stack direction="row" flexWrap="wrap" justifyContent="center" gap={2}>
                      <span>
                        Standard :<b> VIII-A </b>
                      </span>
                      <span>
                        Education District : <b> TIRUR </b>
                      </span>
                      <span>
                        Sub District : <b> EDAPPAL </b>
                      </span>
                      <span>
                        Total Number of Working Days : <b> 206 </b>
                      </span>
                    </Stack>
                  </Typography>
                </Stack>
              </Box>
              <Paper className="card-table-container">
                <TableContainer>
                  <Table stickyHeader sx={{ overflow: 'auto', minWidth: { xs: '1250px', xl: '100%' } }}>
                    <TableHead>
                      <TableRow className="sticky-top">
                        <TableCell colSpan={6}> </TableCell>
                        {SubjectDemo?.map((subject) => {
                          return <TableCell className="text-center">{subject}</TableCell>;
                        })}
                      </TableRow>
                      <TableRow className="sticky-top" sx={{ top: 40 }}>
                        <TableCell className="text-center">Sl.No</TableCell>
                        <TableCell className="text-center">Adm No</TableCell>
                        <TableCell className="text-center">Name</TableCell>
                        <TableCell className="text-center">Year of Study</TableCell>
                        <TableCell className="text-center">% of Attendance </TableCell>
                        <TableCell className="text-center">Term</TableCell>
                        {SubjectDemo?.map(() => {
                          return <TableCell className="text-center">M,G</TableCell>;
                        })}
                      </TableRow>
                    </TableHead>

                    <TableBody>
                      {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map(() => {
                        return (
                          <TableRow>
                            <TableCell className="text-center">
                              <Stack direction="column" justifyContent="start" height="100%" width="100%">
                                1
                              </Stack>
                            </TableCell>
                            <TableCell className="text-center">
                              <Stack direction="column" justifyContent="start" height="100%" width="100%">
                                31755
                              </Stack>
                            </TableCell>
                            <TableCell className="text-center">
                              <Stack direction="column" justifyContent="space-between" height="100%" width="100%">
                                <Typography variant="subtitle2"> Alex Peter </Typography>
                                <Typography variant="subtitle2" fontSize={10} color={theme.palette.grey[600]}>
                                  <CircleIcon className="avg_circle_icon" />
                                  Average
                                </Typography>
                              </Stack>
                            </TableCell>
                            <TableCell className="text-center">--</TableCell>
                            <TableCell className="text-center">--</TableCell>
                            <TableCell className="text-center">--</TableCell>
                            {/* --- */}
                            {SubjectDemo?.map(() => {
                              return (
                                <TableCell className="text-center p-0">
                                  <Stack width="100%">
                                    <TableRow
                                      sx={{ borderBottom: '1px solid ', borderColor: theme.palette.secondary.lighter }}
                                    >
                                      32,D
                                    </TableRow>
                                    <TableRow>32</TableRow>
                                  </Stack>
                                </TableCell>
                              );
                            })}

                            {/* --- */}
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Paper>
            </Box>
          </div>
          <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, mt: 3 }}>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="success" sx={{ gap: 1 }} size="medium">
                <img className="icon" alt="Excel" src={Excel} />
                <Typography color="white" fontSize={12}>
                  Download
                </Typography>
              </Button>
              <Button onClick={handlePrint} variant="contained" color="primary">
                Print
              </Button>
            </Stack>
          </Box>
        </Card>
      </StudentPromotionListRoot>
    </Page>
  );
}

export default StudentPromotionList;
