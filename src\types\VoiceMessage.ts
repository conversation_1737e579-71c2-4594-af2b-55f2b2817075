import { Dayjs } from 'dayjs';
import { FetchStatus } from './Common';

export type VoiceMessageFilter = {
  voiceTitle: string;
  voiceStatus: number | string;
  createdDate?: Dayjs | string | null;
  accademicId?: number;
};
export type VoiceCreateRequest = {
  voiceId?: number;
  voiceTitle: string;
  voiceFile: string;
  templateId: string | number | undefined;
  voiceStatus?: number | string;
  adminId?: string | number | undefined;
};

export type VoiceDataType = {
  voiceId: number;
  voiceTitle: string;
  voiceFile: string;
  createdDate: string;
  templateId: number;
  voiceStatus: number | string;
  createdBy: string | number | undefined;
};

// upload type
export type UplaodFilesType = {
  result: string;
  voiceFile: string;
  templateId: string;
  conversionMessage: string;
};

// Delete notification
export type DeleteMultipleVoiceMessageType = {
  voiceId: number;
  adminId?: number;
};

// Send to parent
export type SendVoiceToParentsRequest = {
  voiceId: number | undefined;
  templateId: string | number;
  studentId: number;
  studentRollNo: number;
  studentName: string;
  guardianName: string;
  guardianNumber: string;
  className: string;
  classId: number;
  accademicTime: string;
  adminId: number;
  // sendStatus?: 'Success' | 'Failed' | '' | undefined;
};
export type SendToParentsResponse = SendVoiceToParentsRequest & { sendStatus: 'Success' | 'Failed' };

// Send to Staff //
export type SendVoiceToStaffRequest = {
  voiceId: number | undefined;
  templateId: string | number;
  staffId: number;
  staffName: string;
  staffPhoneNumber: string;
  adminId: number;
};
// Send to Pta

export type SendVoiceToPtaRequest = {
  voiceId: number | undefined;
  templateId: string | number;
  ptaMemberName: string;
  ptaMemberMobile: string;
  adminId: number;
};
// Send to Convyor
export type SendVoiceToConveyorRequest = {
  voiceId: number | undefined;
  templateId: string | number;
  conveyorsName: string;
  conveyorsMobile: string;
  adminId: number;
};

// Send to Group
export type SendVoiceToGroupRequest = {
  voiceId: number | undefined;
  templateId: string | number;
  groupName: string;
  studentId: number;
  studentName: string;
  guardianName: string;
  guardianNumber: string;
  className: string;
  classId: number;
  adminId: number;
};
// Send to public Group
export type SendVoiceToPublicGroupRequest = {
  voiceId: number | undefined;
  templateId: string | number;
  gmemberName: string;
  gmemberNumber: string;
  groupName: string;
  adminId: number;
};
// Send to Class Division
export type SendVoiceToClassDivisionRequest = {
  voiceId: number | undefined;
  templateId: string | number | undefined;
  accademicId: number;
  classId: number;
  adminId: number;
  sendStatus: 'Success' | 'Failed' | '' | undefined;
};

// Send to Class Section
export type SendVoiceToClassSectionRequest = {
  voiceId: number | undefined;
  templateId: string | number | undefined;
  accademicId: number;
  classSectionName: string;
  adminId: number;
  sendStatus: 'Success' | 'Failed' | '' | undefined;
};
// Send to Group Wise
export type SendVoiceMessageToGroupWiseRequest = {
  voiceId: number | undefined;
  templateId: string | number | undefined;
  accademicId: number;
  groupId: number;
  adminId: number;
  sendStatus: 'Success' | 'Failed' | '' | undefined;
};
// Send to Public GroupW ise
export type SendVoiceMessageToPublicGroupWiseRequest = {
  voiceId: number | undefined;
  templateId: string | number | undefined;
  accademicId: number;
  groupId: number;
  adminId: number;
  sendStatus: 'Success' | 'Failed' | '' | undefined;
};
// Voice
export type VoiceState = {
  voiceList: {
    data: VoiceDataType[];
    status: FetchStatus;
    error: string | null;
  };
  filesUploadList: {
    status: FetchStatus;
    data: UplaodFilesType[];
    error: string | null;
  };

  submitting: boolean;
  deletingRecords: Record<number, boolean>;
  error: string | null | undefined;
};
