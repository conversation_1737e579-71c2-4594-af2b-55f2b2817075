﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <system.webServer>
    <handlers>
      <add name="html" path="*.html" verb="*" modules="IsapiModule"
        scriptProcessor="%windir%\system32\inetsrv\asp.dll" resourceType="Unspecified"
        requireAccess="None" />
    </handlers>
    <rewrite>
      <rules>

        <rule name="React Routes" stopProcessing="true">
          <match url=".*" />
          <conditions logicalGrouping="MatchAll">
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
            <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
          </conditions>
          <action type="Rewrite" url="/" />
        </rule>

      </rules>
    </rewrite>


  </system.webServer>
  <system.web>
    <compilation tempDirectory="C:\Inetpub\vhosts\passdaily.in\tmp" />
  </system.web>
</configuration>