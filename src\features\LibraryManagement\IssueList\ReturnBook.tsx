/* eslint-disable no-else-return */
import React, { useState } from 'react';
import { Box, Stack, Typography, useTheme, Divider, Grid } from '@mui/material';
import CurrencyRupeeIcon from '@mui/icons-material/CurrencyRupee';

import useSettings from '@/hooks/useSettings';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { Button } from '@mui/material';
import LoadingButton from '@mui/lab/LoadingButton';
import { Chip } from '@mui/material';
import { VoiceCreateRequest } from '@/types/VoiceMessage';

export type ReturnBookProps = {
  onSave?: (values: VoiceCreateRequest, mode: 'create' | 'edit') => void;
  onCancel: () => void;
  isSubmitting: boolean;
  adminId: number | undefined;
  bookDetails?: { label: string; value: string }[];
  fineDetails?: { label: string; value: string }[];
};

export const ReturnBook = ({ fineDetails, bookDetails, onCancel, isSubmitting, onSave, adminId }: ReturnBookProps) => {
  const [uploaded, setUploaded] = useState<File[]>([]);
  const theme = useTheme();
  const isLight = useSettings().themeMode === 'light';
  const dispatch = useAppDispatch();

  return (
    <Box width={300}>
      <Divider />
      <Stack
        sx={{
          height: 'calc(100vh - 110px)',
          overflow: 'auto',
          '&::-webkit-scrollbar': {
            width: 0,
          },
        }}
      >
        <Grid container rowSpacing={2} mt={1} mb={2}>
          {bookDetails.map((item, index) => (
            <React.Fragment key={index}>
              <Grid item lg={5} xs={6}>
                <Stack direction="row" alignItems="center" spacing={1}>
                  {item.icon && <Box component="img" src={item.icon} alt={item.label} width={20} height={20} />}
                  <Typography variant="subtitle1" fontSize={13} color="GrayText" whiteSpace="nowrap">
                    {item.label}
                  </Typography>
                </Stack>
              </Grid>

              <Grid item lg={7} xs={6} mb={0.5} mt="auto">
                {item.label.toLowerCase().includes('book status') ? (
                  <Chip
                    label={item.value}
                    color={item.value.toLowerCase() === 'good' ? 'success' : 'error'}
                    size="small"
                    //   variant="contained"
                    sx={{ borderRadius: 0.5 }}
                  />
                ) : (
                  <Typography variant="subtitle2" fontSize={13}>
                    : {item.value}
                  </Typography>
                )}
              </Grid>
            </React.Fragment>
          ))}
        </Grid>

        <Divider />
        <Typography mt={2} variant="subtitle1" fontSize={20}>
          Fine Details
        </Typography>

        <Grid container rowSpacing={2} my={0} mb={2}>
          {fineDetails.map((item, index) => (
            <React.Fragment key={index}>
              <Grid item lg={5} xs={6}>
                <Stack direction="row" alignItems="center" spacing={1}>
                  {item.icon && <Box component="img" src={item.icon} alt={item.label} width={20} height={20} />}
                  <Typography variant="subtitle1" fontSize={13} color="GrayText" whiteSpace="nowrap">
                    {item.label}
                  </Typography>
                </Stack>
              </Grid>
              <Grid item lg={7} xs={6} mb={0.5} mt="auto">
                <Typography variant="subtitle2" fontSize={13}>
                  : {item.value}
                </Typography>
              </Grid>
            </React.Fragment>
          ))}
        </Grid>
        <Divider style={{ border: '1px dashed grey' }} />

        <Grid container rowSpacing={2} my={0} mb={2}>
          <Grid item lg={5} xs={6}>
            <Stack direction="row" alignItems="center" spacing={1}>
              <Typography variant="subtitle2" fontSize={15} color="GrayText" whiteSpace="nowrap">
                Total
              </Typography>
            </Stack>
          </Grid>
          <Grid item lg={7} xs={6} mb={0.5} mt="auto">
            <Stack direction="row" alignItems="center" spacing={0.5}>
              <Typography variant="subtitle2" fontSize={15}>
                :
              </Typography>
              <CurrencyRupeeIcon sx={{ fontSize: 15 }} />
              <Typography variant="subtitle2" fontSize={15}>
                2000
              </Typography>
            </Stack>
          </Grid>
        </Grid>
      </Stack>
      <Box sx={{ justifyContent: 'center', pt: 0, position: 'relative' }}>
        <Stack spacing={2} direction="row" sx={{}}>
          <Button
            disabled={isSubmitting}
            onClick={onCancel}
            fullWidth
            variant="outlined"
            color="secondary"
            type="button"
          >
            Cancel
          </Button>
          <LoadingButton
            loading={isSubmitting}
            fullWidth
            variant="contained"
            color="primary"
            type="submit"
            loadingPosition="start"
            disabled={isSubmitting}
          >
            {/* {isSubmitting ? 'Saving...' : 'Save'} */}
            Return Book
          </LoadingButton>
        </Stack>
      </Box>
    </Box>
  );
};
