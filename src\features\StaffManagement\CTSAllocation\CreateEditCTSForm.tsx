import React from 'react';
import { <PERSON>, Button, FormControl, MenuItem, Select, Stack, Typography } from '@mui/material';
import { ROLL_OPTIONS } from '@/config/Selection';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import LoadingButton from '@mui/lab/LoadingButton';
import useAuth from '@/hooks/useAuth';
import {
  getCTSClassList,
  getCTSFilterListStatus,
  getCTSStaffList,
  getCTSSubjectList,
  getCTSYearList,
  getStaffSubmitting,
} from '@/config/storeSelectors';
import { useAppSelector } from '@/hooks/useAppSelector';
import styled from 'styled-components';
import { CTSAllocationCreateUpdateInfo } from '@/types/StaffManagement';
import { fetchCTSFilter } from '@/store/StaffMangement/StaffMangement/staffMangement.thunks';
import { useAppDispatch } from '@/hooks/useAppDispatch';

export type CreateEditCTSFormProps = {
  onCancel: (mode: 'create' | 'edit') => void;
  onSave: (values: CTSAllocationCreateUpdateInfo, mode: 'create' | 'edit') => void;
  ctsAllocationDetails: CTSAllocationCreateUpdateInfo;
};

const CreateFormRoot = styled.div`
  .MuiStack-root {
    width: 100%;
  }
`;

export const CreateEditCTSForm = ({ onCancel, onSave, ctsAllocationDetails }: CreateEditCTSFormProps) => {
  const mode = ctsAllocationDetails.cteacherId === 0 ? 'create' : 'edit';
  const { user } = useAuth();
  const adminId: number = user ? user.accountId : 0;
  const dispatch = useAppDispatch();
  const isSubmitting = useAppSelector(getStaffSubmitting);
  const CTSFilterListStatus = useAppSelector(getCTSFilterListStatus);
  const CTSYearList = useAppSelector(getCTSYearList);
  const CTSClassList = useAppSelector(getCTSClassList);
  const CTSStaffList = useAppSelector(getCTSStaffList);
  const CTSSubjectList = useAppSelector(getCTSSubjectList);

  const CreateEditBasicFeeTitleValidationSchema = Yup.object({
    academicId: Yup.string()
      .oneOf(
        CTSYearList.map((item) => item.academicId.toString()),
        'Please select a valid year'
      )
      .required('Please select a year'),
    classId: Yup.string()
      .oneOf(
        CTSClassList.map((item) => item.classId.toString()),
        'Please select a valid class'
      )
      .required('Please select a class'),
    staffId: Yup.string()
      .oneOf(
        CTSStaffList.map((item) => item.staffId.toString()),
        'Please select a valid staff'
      )
      .required('Please select a staff'),
    subjectId: Yup.string()
      .oneOf(
        CTSSubjectList.map((item) => item.subjectId.toString()),
        'Please select a valid subject'
      )
      .required('Please select a subject'),
    staffRole: Yup.number().oneOf([0, 1], 'Please select role'),
  });

  const {
    values: { academicId, classId, staffId, subjectId, staffRole },
    handleChange,
    handleBlur,
    handleSubmit,
    touched,
    errors,
    setFieldValue,
  } = useFormik<CTSAllocationCreateUpdateInfo>({
    initialValues: {
      adminId,
      academicId: ctsAllocationDetails.academicId,
      classId: ctsAllocationDetails.classId,
      staffId: ctsAllocationDetails.staffId,
      subjectId: ctsAllocationDetails.subjectId,
      staffRole: ctsAllocationDetails.staffRole,
      cteacherId: ctsAllocationDetails.cteacherId,
    },
    validationSchema: CreateEditBasicFeeTitleValidationSchema,
    onSubmit: async (values) => {
      onSave(values, mode);
    },
    validateOnBlur: false,
    enableReinitialize: true,
  });

  React.useEffect(() => {
    if (CTSFilterListStatus === 'idle') {
      dispatch(fetchCTSFilter(adminId));
    }
  }, [dispatch, adminId, CTSFilterListStatus]);

  return (
    <CreateFormRoot>
      <form noValidate onSubmit={handleSubmit}>
        <Stack
          sx={{
            height: 'calc(100vh - 150px)',
            overflow: 'auto',
            '&::-webkit-scrollbar': {
              width: 0,
            },
          }}
          direction="column"
        >
          <FormControl>
            <Typography variant="subtitle1" fontSize={12}>
              Academic Year
            </Typography>
            <Select
              name="academicId"
              value={academicId}
              onChange={handleChange}
              error={touched.academicId && !!errors.academicId}
              disabled={isSubmitting}
            >
              <MenuItem sx={{ display: 'none' }} value={0}>
                Select Year
              </MenuItem>
              {CTSYearList.map((opt) => (
                <MenuItem key={opt.academicId} value={opt.academicId}>
                  {opt.academicTime}
                </MenuItem>
              ))}
            </Select>
            {touched.academicId && !!errors.academicId && (
              <Typography color="red" fontSize="12px" ml={2.5} mt={1} variant="subtitle1">
                {errors.academicId}
              </Typography>
            )}
          </FormControl>
          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Select Class
            </Typography>
            <Select
              name="classId"
              value={classId}
              onChange={handleChange}
              error={touched.classId && !!errors.classId}
              disabled={isSubmitting}
            >
              <MenuItem sx={{ display: 'none' }} value={0}>
                Select Class
              </MenuItem>
              {CTSClassList.map((opt) => (
                <MenuItem key={opt.classId} value={opt.classId}>
                  {opt.className}
                </MenuItem>
              ))}
            </Select>
            {touched.classId && !!errors.classId && (
              <Typography color="red" fontSize="12px" ml={2.5} mt={1} variant="subtitle1">
                {errors.classId}
              </Typography>
            )}
          </FormControl>
          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Select Staff
            </Typography>
            <Select
              name="staffId"
              value={staffId}
              onChange={handleChange}
              error={touched.staffId && !!errors.staffId}
              disabled={isSubmitting}
            >
              <MenuItem sx={{ display: 'none' }} value={0}>
                Select Staff
              </MenuItem>
              {CTSStaffList.map((opt) => (
                <MenuItem key={opt.staffId} value={opt.staffId}>
                  {opt.staffName}
                </MenuItem>
              ))}
            </Select>
            {touched.staffId && !!errors.staffId && (
              <Typography color="red" fontSize="12px" ml={2.5} mt={1} variant="subtitle1">
                {errors.staffId}
              </Typography>
            )}
          </FormControl>
          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Select Subject
            </Typography>
            <Select
              name="subjectId"
              value={subjectId}
              onChange={handleChange}
              error={touched.subjectId && !!errors.subjectId}
              disabled={isSubmitting}
            >
              <MenuItem sx={{ display: 'none' }} value={0}>
                Select Subject
              </MenuItem>
              {CTSSubjectList.map((opt) => (
                <MenuItem key={opt.subjectId} value={opt.subjectId}>
                  {opt.subjectName}
                </MenuItem>
              ))}
            </Select>
            {touched.subjectId && !!errors.subjectId && (
              <Typography color="red" fontSize="12px" ml={2.5} mt={1} variant="subtitle1">
                {errors.subjectId}
              </Typography>
            )}
          </FormControl>
          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Select Role
            </Typography>
            <Select
              name="staffRole"
              value={staffRole}
              onChange={handleChange}
              error={touched.staffRole && !!errors.staffRole}
              disabled={isSubmitting}
            >
              <MenuItem sx={{ display: 'none' }} value={-1}>
                Select Role
              </MenuItem>
              {ROLL_OPTIONS.map((opt) => (
                <MenuItem key={opt.id} value={opt.id}>
                  {opt.name}
                </MenuItem>
              ))}
            </Select>
            {touched.staffRole && !!errors.staffRole && (
              <Typography color="red" fontSize="12px" ml={2.5} mt={1} variant="subtitle1">
                {errors.staffRole}
              </Typography>
            )}
          </FormControl>
        </Stack>
        <Box sx={{ justifyContent: 'center', pt: 3, position: 'relative' }}>
          <Stack spacing={2} direction="row" sx={{}}>
            <Button
              disabled={isSubmitting}
              onClick={() => onCancel(mode)}
              fullWidth
              variant="contained"
              color="secondary"
              type="button"
            >
              Cancel
            </Button>
            <LoadingButton
              loading={isSubmitting}
              fullWidth
              variant="contained"
              color="primary"
              type="submit"
              loadingPosition="start"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Saving...' : 'Save'}
            </LoadingButton>
          </Stack>
        </Box>
      </form>
    </CreateFormRoot>
  );
};
