export const donutChartData = [70, 50, 30];

export const donutChartOption = {
  chart: {
    foreColor: '#818594',
  },
  labels: ['Present', 'Absent', 'Not Taken'],
  colors: ['#732ebc', '#F85A00', '#919EAB'],
  legend: {
    position: 'bottom',
    fontSize: '10px',
    fontFamily: 'Poppins semibold',
    labels: {
      //   colors: '#000',
    },
  },
  title: {
    // text: 'Medal Country Name',
    // align: 'left',
  },
  fill: {
    type: 'gradient',
  },
  stroke: {
    width: 0,
  },
  responsive: [
    {
      breakpoint: 1000,
      options: {
        Chart: {
          width: 500,
        },
      },
    },
  ],

  plotOptions: {
    pie: {
      // customScale: 1,
      // expandOnClick: false,
      donut: {
        size: '70%',
        labels: {
          show: true,
          total: {
            label: 'TOTAL',
            show: true,
            showAlways: false,
            // formatter: () => '343',
            fontSize: '1.25rem',
            // color: '#000',
            fontFamily: 'Poppins semibold',
          },
        },
      },
    },
  },

  dataLabels: {
    enabled: false,
  },
};
export const barChartDataFeeStatistic = [
  { name: 'Fee', data: [1000, 2000, 3500, 5000, 2000, 3000, 5000, 1000, 4000, 4500, 1500, 2000] },
];

export const barChartOptionFeeStatistic = {
  chart: {
    foreColor: '#818594',
  },
  colors: ['#732ebe'],
  // stroke: ['100px'],
  noData: { style: {} },
  xaxis: {
    categories: ['Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec', 'Jan', 'Feb', 'Mar'],
    labels: {
      style: {
        fontWeight: 600,
        fontSize: '10',
      },
    },
    axisBorder: {
      show: true,
    },
    axisTicks: {
      show: false,
    },
  },
  yaxis: {
    labels: {
      style: {
        fontWeight: 600,
      },
    },
  },
  grid: {
    borderColor: 'rgba(163, 174, 208, 0.3)',
    show: true,
    yaxis: {
      lines: {
        show: false,
      },
    },
    row: {
      opacity: 0.5,
    },
    xaxis: {
      lines: {
        show: false,
      },
    },
  },
  fill: {
    type: 'gradient',
  },
  plotOptions: {
    bar: {
      borderRadius: 10,
      borderRadiusApplication: 0,
      columnWidth: '50%',
    },
  },
  tooltip: {
    style: {},
    theme: 'dark',
  },

  dataLabels: {
    enabled: false,
    style: {
      colors: ['#fff'],
      // fontSize: '8',
    },
  },
};
