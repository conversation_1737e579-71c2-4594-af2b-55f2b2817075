/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import {
  Autocomplete,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Box,
  Avatar,
  IconButton,
  Collapse,
} from '@mui/material';
import styled from 'styled-components';
import { YEAR_SELECT } from '@/config/Selection';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import SearchIcon from '@mui/icons-material/Search';

const MapStaffRoot = styled.div`
  width: 100%;
  padding: 0.5rem 1.5rem;
  .Card {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    height: calc(100vh - 200px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }
    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 10px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

export const data = [
  {
    slNo: 1,
    staffName: 'Alex',
    staffCode: '2023-2024',
    mobileNumber: '+91-7685736354',
  },
  {
    slNo: 1,
    staffName: 'Alex',
    staffCode: '2023-2024',
    mobileNumber: '+91-7685736354',
  },
  {
    slNo: 1,
    staffName: 'Alex',
    staffCode: '2023-2024',
    mobileNumber: '+91-7685736354',
  },
  {
    slNo: 1,
    staffName: 'Alex',
    staffCode: '2023-2024',
    mobileNumber: '+91-7685736354',
  },
  {
    slNo: 1,
    staffName: 'Alex',
    staffCode: '2023-2024',
    mobileNumber: '+91-7685736354',
  },
  {
    slNo: 1,
    staffName: 'Alex',
    staffCode: '2023-2024',
    mobileNumber: '+91-7685736354',
  },
  {
    slNo: 1,
    staffName: 'Alex',
    staffCode: '2023-2024',
    mobileNumber: '+91-7685736354',
  },
  {
    slNo: 2,
    staffName: 'Mathew',
    staffCode: '2023-2024',
    mobileNumber: '+91-7685736354',
  },
  {
    slNo: 4,
    staffName: 'Alex Mic',
    staffCode: '2023-2024',
    mobileNumber: '+91-7685736354',
  },
  {
    slNo: 5,
    staffName: 'john',
    staffCode: '2023-2024',
    mobileNumber: '+91-7685736354',
  },
  {
    slNo: 7,
    staffName: 'jack',
    staffCode: '2023-2024',
    mobileNumber: '+91-7685736354',
  },
];

function MapStaff({ onCancel }: any) {
  const [showFilter, setShowFilter] = useState(false);

  const getRowKey = useCallback((row: any) => row.examId, []);
  const AuthorizeKeywordsColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'slNo',
        dataKey: 'slNo',
        headerLabel: 'Sl.No',
        sortable: true,
      },
      {
        name: 'staffName',
        headerLabel: 'Staff Name',
        renderCell: (row) => {
          return (
            <Stack direction="row" gap={1} alignItems="center">
              <Avatar
                alt=""
                src="https://images.unsplash.com/photo-1633332755192-727a05c4013d?q=80&w=1780&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
              />
              <Typography variant="subtitle2">{row.staffName}</Typography>
            </Stack>
          );
        },
      },
      {
        name: 'staffCode',
        dataKey: 'staffCode',
        headerLabel: 'Staff Code',
      },
      {
        name: 'mobileNumber',
        dataKey: 'mobileNumber',
        headerLabel: 'Mobile Number',
      },
    ],
    []
  );
  return (
    <MapStaffRoot>
      <Box className="Card">
        <Box sx={{ flexShrink: 0 }}>
          <IconButton
            aria-label="delete"
            color="primary"
            sx={{ mr: 2, position: 'absolute', top: '19px', right: '65px' }}
            onClick={() => setShowFilter((x) => !x)}
          >
            <SearchIcon />
          </IconButton>
        </Box>
        <Divider />
        <div className="card-main-body">
          <Collapse in={showFilter}>
            <form noValidate>
              <Grid pb={4} container spacing={3}>
                <Grid item md={2.5} xs={12}>
                  <Typography variant="h6" fontSize={14}>
                    Academic Year
                  </Typography>
                  <Autocomplete
                    options={YEAR_SELECT}
                    renderInput={(params) => <TextField {...params} placeholder="Select" />}
                  />
                </Grid>
                <Grid item md={2.5} xs={12}>
                  <Typography variant="h6" fontSize={14}>
                    Name
                  </Typography>
                  <TextField fullWidth placeholder="Enter name" />
                </Grid>
                <Grid item md={2.5} xs={12}>
                  <Typography variant="h6" fontSize={14}>
                    Staff Code
                  </Typography>
                  <TextField fullWidth placeholder="Enter name" />
                </Grid>
                <Grid item md={2.5} xs={12}>
                  <Typography variant="h6" fontSize={14}>
                    Mobile
                  </Typography>
                  <TextField fullWidth placeholder="Enter name" />
                </Grid>
                <Grid item md={1} xs={12}>
                  <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                    <Button variant="contained" color="secondary" fullWidth>
                      Reset
                    </Button>
                    {/* <Button variant="contained" color="primary" fullWidth>
                      Search
                    </Button> */}
                  </Stack>
                </Grid>
              </Grid>
            </form>
          </Collapse>

          <Paper className="card-table-container" sx={{ marginTop: '12px' }}>
            <DataTable
              ShowCheckBox
              columns={AuthorizeKeywordsColumns}
              data={data}
              getRowKey={getRowKey}
              fetchStatus="success"
            />
          </Paper>
          <Box mt={1.2}>
            <form noValidate>
              <Grid
                container
                display="flex"
                justifyContent={{ xs: 'space-between', sm: 'space-between' }}
                alignItems="end"
              >
                <Grid item xs={8} sm={6} md={4}>
                  <Typography variant="h6" fontSize={14}>
                    Map To
                  </Typography>
                  <Autocomplete
                    options={YEAR_SELECT}
                    renderInput={(params) => <TextField {...params} fullWidth placeholder="Select year" />}
                  />
                </Grid>
                <Stack spacing={2} direction="row" justifyContent="end" sx={{ pt: { xs: 0, md: 3.79 } }}>
                  <Button variant="contained" color="primary" fullWidth>
                    Map
                  </Button>
                </Stack>
              </Grid>
            </form>
          </Box>
        </div>
      </Box>
    </MapStaffRoot>
  );
}

export default MapStaff;
