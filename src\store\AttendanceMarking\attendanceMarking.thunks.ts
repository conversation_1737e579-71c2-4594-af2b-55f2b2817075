import api from '@/api';
import {
  ApproveLeaveRequest,
  AttendanceCalendarDataType,
  AttendanceCalendarRequest,
  AttendanceListInfo,
  AttendanceListRequest,
  AttendanceSummaryReportInfo,
  AttendanceSummaryReportRequest,
  EnquiryReplyRequestData,
  LeaveNoteListInfo,
  LeaveNoteListPagedData,
  LeaveNoteListRequest,
  MarkAttendance,
  RejectLeaveRequest,
  StudentEnquiryListPagedData,
  StudentEnquiryListRequest,
} from '@/types/AttendanceMarking';
import { SendResponse, SubmitResponse } from '@/types/Common';
import { createAsyncThunk } from '@reduxjs/toolkit';

export const fetchAttendanceList = createAsyncThunk<
  AttendanceListInfo[],
  AttendanceListRequest,
  { rejectValue: string }
>('AttendanceMarking/list', async (request, { rejectWithValue }) => {
  try {
    const response = await api.AttendanceMarking.GetAttendanceList(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching Attendance List');
  }
});

export const MarkAbsentees = createAsyncThunk<MarkAttendance[], MarkAttendance[], { rejectValue: string }>(
  'AttendanceMarking/MarkAbsentees',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.AttendanceMarking.MarkAbsentees(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in fetching Attendance List');
    }
  }
);

export const MarkPresent = createAsyncThunk<
  SendResponse,
  {
    adminId: number | undefined;
    studentId: number | undefined;
    classId: number | undefined;
    attendanceId: number | undefined;
  },
  { rejectValue: string }
>('AttendanceMarking/MarkPresent', async ({ adminId, studentId, classId, attendanceId }, { rejectWithValue }) => {
  try {
    const response = await api.AttendanceMarking.MarkPresent(adminId, studentId, classId, attendanceId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching attendance mark to present List');
  }
});

export const fetchLeaveNoteList = createAsyncThunk<
  LeaveNoteListPagedData,
  LeaveNoteListRequest,
  { rejectValue: string }
>('LeaveNote/list', async (request, { rejectWithValue }) => {
  try {
    const response = await api.AttendanceMarking.GetLeaveNoteList(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching Leave Note List');
  }
});

export const ApproveLeave = createAsyncThunk<SubmitResponse, ApproveLeaveRequest, { rejectValue: string }>(
  'LeaveNote/Approve',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.AttendanceMarking.ApproveLeave(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in approving Leave Note');
    }
  }
);

export const RejectLeave = createAsyncThunk<SubmitResponse, RejectLeaveRequest, { rejectValue: string }>(
  'LeaveNote/Reject',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.AttendanceMarking.RejectLeave(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in rejecting Leave Note');
    }
  }
);

export const fetchStudentEnquiryList = createAsyncThunk<
  StudentEnquiryListPagedData,
  StudentEnquiryListRequest,
  { rejectValue: string }
>('StudentEnquiry/list', async (request, { rejectWithValue }) => {
  try {
    const response = await api.AttendanceMarking.GetStudentEnquiryList(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching Student Enquiry List');
  }
});

export const sendEnquiryReply = createAsyncThunk<
  EnquiryReplyRequestData,
  EnquiryReplyRequestData,
  { rejectValue: string }
>('AttendanceMarking/SendEnquiryReply', async (request, { rejectWithValue }) => {
  try {
    const response = await api.AttendanceMarking.SendEnquiryReply(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in Send Enquiry Reply');
  }
});

export const fetchAttendanceCalendar = createAsyncThunk<
  AttendanceCalendarDataType,
  AttendanceCalendarRequest,
  { rejectValue: string }
>('AttendanceCalendar/list', async (request, { rejectWithValue }) => {
  try {
    const response = await api.AttendanceMarking.GetAttendanceCalendar(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching Attendance Calendar');
  }
});
export const fetchAttendanceSummaryReport = createAsyncThunk<
  AttendanceSummaryReportInfo[],
  AttendanceSummaryReportRequest,
  { rejectValue: string }
>('AttendanceSummaryReport/list', async (request, { rejectWithValue }) => {
  try {
    const response = await api.AttendanceMarking.GetAttendanceSummaryReport(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching Attendance Summary Report List');
  }
});


