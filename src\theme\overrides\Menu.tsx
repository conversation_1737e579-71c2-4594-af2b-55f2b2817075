import { Components, Theme } from '@mui/material';

export default function <PERSON>u(theme: Theme): Components<Theme> {
  return {
    // MuiMenu: {
    //   defaultProps: {
    //     disableScrollLock: true,
    //   },
    MuiMenuItem: {
      styleOverrides: {
        root: {
          '&.Mui-selected': {
            backgroundColor: theme.palette.action.selected,
            '&:hover': {
              backgroundColor: theme.palette.action.hover,
            },
          },
        },
      },
    },
  };
}
