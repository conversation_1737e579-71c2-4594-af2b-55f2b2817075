import ApiUrls from '@/config/ApiUrls';
import {
  DashboardAttendanceType,
  DashboardBdayType,
  DashboardEventsType,
  DashboardFeeChartType,
  DashboardStatsType,
  DashboardTimeTableType,
  DashboardVideosType,
  YearDataType,
} from '@/types/Dashboard';
import { privateApi } from '@/api/base/api';
import { APIResponse } from '@/api/base/types';
import { ClassListInfo } from '@/types/AcademicManagement';

async function GetDashboardStats(adminId: number | undefined): Promise<APIResponse<DashboardStatsType[]>> {
  if (adminId === undefined) {
    throw new Error('adminId is required');
  }

  const url = ApiUrls.DashboardStats.replace('{adminId}', adminId.toString());
  const response = await privateApi.get<any>(url);
  return response;
}

async function GetDashboardEvents(adminId: number | undefined): Promise<APIResponse<DashboardEventsType[]>> {
  if (adminId === undefined) {
    throw new Error('adminId is required');
  }

  const url = ApiUrls.DashboardEvents.replace('{adminId}', adminId.toString());
  const response = await privateApi.get<any>(url);
  return response;
}
async function GetDashboardBday(adminId: number | undefined): Promise<APIResponse<DashboardBdayType[]>> {
  if (adminId === undefined) {
    throw new Error('adminId is required');
  }

  const url = ApiUrls.DashboardBday.replace('{adminId}', adminId.toString());
  const response = await privateApi.get<any>(url);
  return response;
}
async function GetDashboardFeeChart(
  adminId: number | undefined,
  academicId: number | undefined,
  classId: number | undefined
): Promise<APIResponse<DashboardFeeChartType[]>> {
  if (adminId === undefined || academicId === undefined || classId === undefined) {
    throw new Error('adminId, academicId, and classId are required');
  }

  const url = ApiUrls.DashboardFeeChart.replace('{adminId}', adminId.toString())
    .replace('{academicId}', academicId.toString())
    .replace('{classId}', classId.toString());

  const response = await privateApi.get<any>(url);
  return response;
}
async function GetDashboardTimetable(
  adminId: number | undefined,
  classId: number | undefined
): Promise<APIResponse<DashboardTimeTableType[]>> {
  if (adminId === undefined || classId === undefined) {
    throw new Error('adminId, academicId, and classId are required');
  }

  const url = ApiUrls.DashboardTimetable.replace('{adminId}', adminId.toString()).replace(
    '{classId}',
    classId.toString()
  );

  const response = await privateApi.get<any>(url);
  return response;
}
async function GetDashboardAttendance(
  adminId: number | undefined,
  classId: number | undefined
): Promise<APIResponse<DashboardAttendanceType[]>> {
  if (adminId === undefined || classId === undefined) {
    throw new Error('adminId, academicId, and classId are required');
  }

  const url = ApiUrls.DashboardAttendance.replace('{adminId}', adminId.toString()).replace(
    '{classId}',
    classId.toString()
  );

  const response = await privateApi.get<any>(url);
  return response;
}
async function GetDashboardVideos(
  adminId: number | undefined,
  classId: number | undefined
): Promise<APIResponse<DashboardVideosType[]>> {
  if (adminId === undefined || classId === undefined) {
    throw new Error('adminId, academicId, and classId are required');
  }

  const url = ApiUrls.DashboardVideos.replace('{adminId}', adminId.toString()).replace('{classId}', classId.toString());

  const response = await privateApi.get<any>(url);
  return response;
}
async function GetClassList(adminId: number | undefined): Promise<APIResponse<ClassListInfo[]>> {
  if (adminId === undefined) {
    throw new Error('adminId is required');
  }

  const url = ApiUrls.ClassList.replace('{adminId}', adminId.toString());
  const response = await privateApi.get<any>(url);
  return response;
}
async function GetYearList(adminId: number | undefined): Promise<APIResponse<YearDataType[]>> {
  if (adminId === undefined) {
    throw new Error('adminId is required');
  }

  const url = ApiUrls.YearList.replace('{adminId}', adminId.toString());
  const response = await privateApi.get<any>(url);
  return response;
}

const methods = {
  GetDashboardStats,
  GetDashboardEvents,
  GetDashboardBday,
  GetDashboardFeeChart,
  GetDashboardTimetable,
  GetDashboardAttendance,
  GetDashboardVideos,
  GetClassList,
  GetYearList,
};

export default methods;
