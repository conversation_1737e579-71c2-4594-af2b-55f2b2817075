import styled from 'styled-components';
import { Card, CardContent, Typography, CardActionArea, Grid, Box } from '@mui/material';
import { ParentLinkCardData } from '@/config/LinkCard';
import { Link } from 'react-router-dom';

const LinkCardRoot = styled.div`
  padding: 1rem 0.5rem 1rem 0.5rem;
  /* margin-top: 1rem; */
  /* margin-bottom: 1rem; */
  .cards {
    transition: 0.5s;
  }
  .icon {
    transition: 0.5s;
  }
  .cards:hover {
    transform: scale(1.03);
  }
  .icon:hover {
    transform: scale(1.1);
  }
  .content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
  .icon-circle {
    border-radius: 100%;
  }

  a {
    text-decoration: none;
    color: black;
  }
`;

function LinkCard() {
  // const theme = useTheme();
  return (
    <LinkCardRoot className="linkcard">
      <Box sx={{ width: '100%' }}>
        <Grid container spacing={{ xxl: 20, xl: 10, lg: 2, md: 4, sm: 5, xs: 5 }}>
          {ParentLinkCardData?.map((item) => (
            <Grid item xxl={3} xl={3} lg={3} md={3} sm={3} xs={6} key={item.id}>
              <Link to={`${item.link}`}>
                <Card
                  className="cards"
                  sx={{ background: item.color }}
                  // sx={{ backgroundColor: item.color }}
                  // sx={{
                  //   background: theme.themeMode === 'light' ? item.color : theme.palette.grey[50032],
                  // }}
                >
                  <CardActionArea>
                    <CardContent className="content">
                      <div className="icon-circle p-2 mb-1">
                        <img src={item.icon} className="icon" alt="" />
                      </div>
                      <Typography
                        variant="subtitle2"
                        sx={{
                          color: '#000',
                          fontFamily: 'Poppins Regular',
                          fontSize: '13px',
                          // fontSize: { xxl: '13px', xl: '12px', lg: '10px', md: '10px', sm: '11px' },
                          fontWeight: '800',
                        }}
                      >
                        {item.label}
                      </Typography>
                    </CardContent>
                  </CardActionArea>
                </Card>
              </Link>
            </Grid>
          ))}
        </Grid>
      </Box>
    </LinkCardRoot>
  );
}

export default LinkCard;
