// import api from '@/api';
import { createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

export const fetchSignature = createAsyncThunk(
  'meeting/signature/fetch',
  async ({ meetingNumber, role }: { meetingNumber: string; role: number }) => {
    const { data } = await axios.post(
      'http://demostaff.passdaily.in/ElixirApi/Accademic/TakeJwtandZak?AdminId=14&Type=0',
      { meetingNumber, role }
    );
    return data.JwtToken;
  }
);

// export const fetchReceiptOnline = createAsyncThunk<GetReceiptForPrintType[], number, { rejectValue: string }>(
//   'manageFee/getReceiptOnline ',
//   async (request, { rejectWithValue }) => {
//     try {
//       const response = await api.Payment.GetReceiptOnline(request);
//       return response.data;
//     } catch {
//       return rejectWithValue('Something went wrong in fetching Manage Fee Get Receipt Online List');
//     }
//   }
// );
