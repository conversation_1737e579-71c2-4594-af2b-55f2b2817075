import Anchor from '@/components/Dashboard/Anchor';
import { Card, Typography } from '@mui/material';
import styled from 'styled-components';

const InfoCardRoot = styled(Card)`
  padding: 0.9375rem;
  height: 180px;

  .content {
    display: flex;
    flex-direction: column;

    a {
      align-self: flex-end;
      text-decoration: none;
    }
  }
`;

export type InfoCardProps = {
  content: string;
  url: string;
};

function InfoCard3({ content, url }: InfoCardProps) {
  return (
    <InfoCardRoot  className="infoCard" elevation={0}>
      <div className="content">
        <Typography variant="body2">LOVE & SERVICE:</Typography>
        <Typography variant="body2">
          he circle signifies the universe. The star stands for the star of the sea,OUR LADY of MOUNT CARMEL to whom the
          school is dedicated .The lamp tells us that the students should be lamps radiating light to millions who are
          still in darkness.
        </Typography>
        <Anchor href={url}>View more</Anchor>
      </div>
    </InfoCardRoot>
  );
}

export default InfoCard3;
