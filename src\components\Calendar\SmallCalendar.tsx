/* eslint-disable no-nested-ternary */
/* eslint-disable react/no-array-index-key */
/* eslint-disable no-else-return */
import React, { useContext, useEffect, useState } from 'react';
import dayjs from 'dayjs';
import { Box, Grid, ButtonGroup, IconButton, Typography } from '@mui/material';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import CalendarContext from '@/contexts/CalendarContext';
import { getMonth } from '@/utils/Calendar';
import { useTheme } from 'styled-components';

const SmallCalendar = () => {
  const theme = useTheme();
  const isLight = theme.palette.mode === 'light';
  const [currentMonthIdx, setCurrentMonthIdx] = useState(dayjs().month());
  const [currentMonth, setCurrentMonth] = useState(getMonth());

  useEffect(() => {
    setCurrentMonth(getMonth(currentMonthIdx));
  }, [currentMonthIdx]);

  const { monthIndex, setSmallCalendarMonth, setDaySelected, daySelected } = useContext(CalendarContext);

  useEffect(() => {
    setCurrentMonthIdx(monthIndex);
  }, [monthIndex]);

  function handlePrevMonth() {
    setCurrentMonthIdx(currentMonthIdx - 1);
  }

  function handleNextMonth() {
    setCurrentMonthIdx(currentMonthIdx + 1);
  }

  const format = 'DD-MM-YY';
  const today = dayjs().format(format);
  const slcDay = daySelected && daySelected.format(format);

  return (
    <Box sx={{ display: { xs: 'none', lg: 'flex' }, flexDirection: 'column', overflow: 'auto', width: '100%', pl: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
        <Typography variant="subtitle2" fontSize={17} display="flex" alignItems="center">
          {dayjs(new Date(dayjs().year(), currentMonthIdx)).format('MMMM YYYY')}
        </Typography>
        <ButtonGroup>
          <IconButton size="small" onClick={() => handlePrevMonth()}>
            <ChevronLeftIcon />
          </IconButton>
          <IconButton size="small" onClick={() => handleNextMonth()}>
            <ChevronRightIcon />
          </IconButton>
        </ButtonGroup>
      </Box>
      <Box sx={{ minWidth: '160px', py: 1 }}>
        <Grid container sx={{ display: 'flex', flex: 1 }}>
          {['S', 'M', 'T', 'W', 'T', 'F', 'S'].map((day, index) => (
            <Grid item key={index} xs={1.71}>
              <Typography pl={1} variant="subtitle2">
                {day}
              </Typography>
            </Grid>
          ))}
        </Grid>
        <Grid container sx={{ height: '87%', display: 'flex', flex: 1 }}>
          {currentMonth.map((row, i) => (
            <Grid item xs={12} key={i}>
              <Grid container sx={{ height: '100%' }}>
                {row.map((day, idx) => (
                  <Grid item xs={1.71} key={idx}>
                    <Box
                      sx={{
                        width: 22,
                        mb: 0.5,
                        borderRadius: '50%',
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        height: 22,
                        cursor: 'pointer',
                        backgroundColor:
                          day.format(format) === today
                            ? theme.palette.primary.main
                            : day.format(format) === slcDay
                            ? theme.palette.primary.light
                            : null,
                      }}
                      onClick={() => {
                        setSmallCalendarMonth(currentMonthIdx);
                        setDaySelected(day);
                      }}
                    >
                      <Typography
                        color={
                          day.format('M') === dayjs(new Date(dayjs().year(), currentMonthIdx)).format('M')
                            ? day.format(format) === today
                              ? theme.palette.common.white
                              : 'inherit'
                            : theme.palette.grey[700]
                        }
                        textAlign="center"
                        fontSize={12}
                        sx={{ pt: 0.1 }}
                      >
                        {day.format('D')}
                      </Typography>
                    </Box>
                  </Grid>
                ))}
              </Grid>
            </Grid>
          ))}
        </Grid>
      </Box>
    </Box>
  );
};

export default SmallCalendar;
