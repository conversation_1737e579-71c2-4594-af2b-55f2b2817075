import { Components, Theme } from '@mui/material';

export default function Card(theme: Theme): Components<Theme> {
  return {
    MuiCard: {
      defaultProps: {},
      styleOverrides: {
        root: {
          position: 'relative',
          boxShadow: theme.customShadows.card,
          borderRadius: Number(theme.shape.borderRadius) * 2,
          zIndex: 0, // Fix Safari overflow: hidden with border radius
        },
      },
    },
    MuiCardHeader: {
      defaultProps: {
        titleTypographyProps: { fontWeight: '600', fontSize: '18px' },
        subheaderTypographyProps: { variant: 'body2', marginTop: theme.spacing(0.5) },
      },
      styleOverrides: {
        root: {
          padding: theme.spacing(2.5, 3, 0),
        },
      },
    },
    MuiCardContent: {
      styleOverrides: {
        root: {
          padding: theme.spacing(2),
          fontSize: '12px',
          fontFamily: 'Poppins Regular',
        },
      },
    },
  };
}
