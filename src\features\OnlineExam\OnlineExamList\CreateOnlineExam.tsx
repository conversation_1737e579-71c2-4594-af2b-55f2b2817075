/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import { Autocomplete, Grid, Stack, TextField, Button, Typography, Box, FormControl } from '@mui/material';
import styled from 'styled-components';
import { CLASS_SELECT, STATUS_SELECT, SUBJECT_SELECT, YEAR_SELECT } from '@/config/Selection';
import SelectDateTimePicker from '@/components/shared/Selections/TimePicker';
import dayjs from 'dayjs';

const CreateOnlineExamRoot = styled.div`
  width: 100%;
  padding: 1.5rem;
`;

function CreateOnlineExam() {
  return (
    <CreateOnlineExamRoot>
      <form noValidate>
        <Grid container spacing={3}>
          <Grid item md={8} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Exam Name
              </Typography>
              <TextField placeholder="Enter Exam Name " />
            </FormControl>
          </Grid>
          <Grid item md={4} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Exam Type
              </Typography>
              <Autocomplete
                options={['Objective(MCQ)', 'Descriptive']}
                renderInput={(params) => <TextField {...params} placeholder="Select Type" />}
              />
            </FormControl>
          </Grid>
          <Grid item md={6} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Academic Year
              </Typography>
              <Autocomplete
                options={YEAR_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select year" />}
              />
            </FormControl>
          </Grid>
          <Grid item md={6} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Class
              </Typography>
              <Autocomplete
                options={CLASS_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select Class" />}
              />
            </FormControl>
          </Grid>
          <Grid item md={6} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Subject
              </Typography>
              <Autocomplete
                options={SUBJECT_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select Subject" />}
              />
            </FormControl>
          </Grid>
          <Grid item md={6} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Exam Duration
              </Typography>
              <TextField placeholder="Enter Duration(Min)" type="time" />
            </FormControl>
          </Grid>
          <Grid item md={6} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Start Date/Time
              </Typography>
              <SelectDateTimePicker disablePast />
            </FormControl>
          </Grid>
          <Grid item md={6} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                End Date/Time
              </Typography>
              <SelectDateTimePicker disablePast />
            </FormControl>
          </Grid>

          <Grid item md={4} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Status
              </Typography>
              <Autocomplete
                options={STATUS_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select Subject" />}
              />
            </FormControl>
          </Grid>
          <Grid item md={8} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Exam Description
              </Typography>
              <TextField placeholder="Enter Description " />
            </FormControl>
          </Grid>
        </Grid>
        <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 5 }}>
          <Stack spacing={2} direction="row">
            <Button variant="contained" color="secondary">
              Cancel
            </Button>
            <Button variant="contained" color="primary">
              Save
            </Button>
          </Stack>
        </Box>
      </form>
    </CreateOnlineExamRoot>
  );
}

export default CreateOnlineExam;
