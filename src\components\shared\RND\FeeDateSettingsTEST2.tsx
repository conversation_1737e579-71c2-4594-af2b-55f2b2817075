/* eslint-disable no-else-return */
/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/alt-text */
import React, { ChangeEvent, FormEvent, useCallback, useMemo, useRef, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  Collapse,
  Select,
  MenuItem,
  FormControl,
  useTheme,
  Tooltip,
  SelectChangeEvent,
  InputAdornment,
  TableCell,
  TableRow,
  Table,
  Snackbar,
} from '@mui/material';
import { IoIosArrowUp } from 'react-icons/io';
import SearchIcon from '@mui/icons-material/Search';
import DeleteIcon from '@mui/icons-material/Delete';
import deleteBin from '@/assets/ManageFee/deleteBin.json';
import errorIcon from '@/assets/ManageFee/Error.json';
import deleteSuccess from '@/assets/ManageFee/deleteSuccess.json';
import AddIcon from '@mui/icons-material/Add';
import SaveIcon from '@mui/icons-material/Save';
import DeleteAnimatedIcon from '@/assets/ManageFee/trash-bin.gif';
import styled from 'styled-components';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import Popup from '@/components/shared/Popup/Popup';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import { getTextColor } from '@/utils/Colors';
import useAuth from '@/hooks/useAuth';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import {
  DeleteAllTermFee,
  DeleteTermFee,
  createTermFeeSetting,
  createTermFeeSettingTitle,
  fetchClassSections,
  fetchFeeDateSettings,
} from '@/store/ManageFee/manageFee.thunks';
import { useAppSelector } from '@/hooks/useAppSelector';
import {
  getClassSectionsData,
  getYearData,
  getcreateTermFeeSettingListData,
  getfeeDateSettingsData,
  getfeeDateSettingsStatus,
} from '@/config/storeSelectors';
import { MdAdd } from 'react-icons/md';
import ErrorIcon from '@/assets/ManageFee/ErrorIcon.json';
import Success from '@/assets/ManageFee/Success.json';
import Updated from '@/assets/ManageFee/Updated.json';
import UpdateLoading from '@/assets/ManageFee/UpdateLoading.json';
import SaveLoading from '@/assets/ManageFee/SaveLoading.json';
import successDateSet from '@/assets/ManageFee/successDateSet.json';
import {
  CreateTermFeeSettingTitleDataType,
  FeeDateSettingsType,
  TermFeeDataType,
  TermFeeDetailsType,
  TermFeeMappedDeleteAllDataType,
} from '@/types/ManageFee';
import { fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import useSettings from '@/hooks/useSettings';
import Lottie from 'lottie-react';
import LoadingButton from '@mui/lab/LoadingButton';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import { ErrorMessage } from '@/components/shared/Popup/ErrorMessage';
import { FEE_TYPE_ID_OPTIONS } from '@/config/Selection';
import CurrencyRupeeIcon from '@mui/icons-material/CurrencyRupee';
import { TableBody } from '@mui/material';
import { Skeleton } from '@mui/material';
import TestComponent from './TestComponent';
import { TestScrollTable } from '@/components/shared/TableComponents/DataTable2';
import DataTableVirtuoso from '@/components/shared/TableComponents/DataTableVirtuoso';
import { CreateEditTermFeeTitleForm } from '@/features/ManageFee/FeeDateSetting/CreateEditTermFeeTitleForm';
import { Alert } from '@mui/material';
import { debounce } from 'lodash';
import VirtuosoTable from '../TableComponents/VirtuosoTable';
import DTVirtuoso from './DataTableVirtuoso';

const FeeDateSettingsRoot = styled.div`
  padding: 1rem;
  @media screen and (max-width: 576px) {
    padding: 0.5rem;
  }
  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    /* height: 100%; */
    @media screen and (max-width: 576px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 45px);
      /* height: 100%; */
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        /* border: 1px solid #e8e8e9; */
        border-radius: 0px;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
        .MuiTableCell-root {
          border: 1px solid #f5f5f5;
          border-radius: 0px;
        }
        /* == */
        .MuiTableCell-head {
          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.info.light : props.theme.palette.grey[900]};
        }
        .MuiTableCell-head:nth-child(1) {
          z-index: 11;
          position: sticky;
          left: 0;
          width: 50px;
        }
        .MuiTableCell-head:nth-child(2) {
          z-index: 11;
          position: sticky;
          left: 51.5px;
        }
        .MuiTableCell-head:nth-child(3) {
          z-index: 11;
          position: sticky;
          left: 192px;
        }
        .MuiTableCell-head:nth-child(4) {
          z-index: 11;
          position: sticky;
          left: 313px;
        }
        .MuiTableCell-head:last-child {
          z-index: 11;
          position: sticky;
          right: 0px;
          /* width: 50px; */
        }

        .MuiTableCell-root.MuiTableCell-body:nth-child(1) {
          position: sticky;
          left: 0;
          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.info.lighter : props.theme.palette.grey[900]};
          width: 50px;
          z-index: 1;
        }
        .MuiTableCell-root.MuiTableCell-body:nth-child(2) {
          position: sticky;
          left: 51.5px;
          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.info.lighter : props.theme.palette.grey[900]};
          /* width: 142px; */
          z-index: 1;
        }
        .MuiTableCell-root.MuiTableCell-body:nth-child(3) {
          position: sticky;
          left: 192px;
          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.info.lighter : props.theme.palette.grey[900]};
          z-index: 1;
        }
        .MuiTableCell-root.MuiTableCell-body:nth-child(4) {
          position: sticky;
          left: 313px;
          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.info.lighter : props.theme.palette.grey[900]};
          z-index: 1;
        }
        .MuiTableCell-root.MuiTableCell-body:last-child {
          position: sticky;
          right: 0px;
          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.info.lighter : props.theme.palette.grey[800]};
          width: 100px;
          z-index: 1;
        }

        /* == */

        .MuiTableCell-root.MuiTableCell-body {
          padding: 0px;
        }
        /* .MuiTableCell-root.MuiTableCell-head {
          font-size: 10px;
        } */

        .MuiTableCell-root:first-child {
          padding: 7px;
        }
        .MuiTableRow-root:hover {
          background-color: transparent; /* Change this to your desired hover color */
        }
        .header-color {
          color: ${(props) => getTextColor(props.theme)};
        }
        .footer_row .MuiTableCell-root {
          font-size: 14px;
          font-weight: 600;
          color: ${(props) => getTextColor(props.theme)};
          border: 0px;
          text-align: center;
          border: 1px solid #e8e8e9;
        }
      }
    }
    .Payment_Info .MuiTableCell-root {
      font-size: 12px;
    }
    .Payment_Info .MuiOutlinedInput-input {
      background-color: red;
    }
    .Payment_Info .MuiTableCell-root.MuiTableCell-head {
      background-color: ${(props) =>
        props.theme.themeMode === 'light' ? props.theme.palette.grey[100] : props.theme.palette.grey[900]};
      border-top-right-radius: 0px;
      border-top-left-radius: 0px;
    }
    .Payment_Info input {
      width: 10px;
    }
    .cellActive {
      border-color: ${(props) => props.theme.palette.warning.main};
      background: linear-gradient(90deg, rgba(255, 249, 232, 1) 0%, rgba(255, 255, 255, 1) 100%);
    }
    .cellInActive {
      border-color: ${(props) => props.theme.palette.success.main};
      background: linear-gradient(90deg, #e3f5d3 0%, rgba(255, 255, 255, 1) 100%);
    }
    .inputCellActive {
      color: ${(props) => props.theme.palette.grey[400]};
      background-color: ${(props) => props.theme.palette.grey[50]};
    }
    .inputCellInActive {
      color: ${(props) => props.theme.palette.grey[500]};
    }
  }
`;

type TermFeeDetailsDataType = {
  feeId: number;
  feeTitle: string;
  feeType: number;
  termFeeMapped: {
    termId: number;
    amount: number;
    academicTermId: number;
  };
};

interface TermAmount {
  termId: number;
  amount: number;
  academicTermId: number;
}

interface TermFeeDetail {
  feeId: any;
  feeTitle: string;
  feeType: number;
  amount: number;
  termFeeMapped: TermAmount[];
}

export default function FeeDateSettings2() {
  const isLight = useSettings().themeMode === 'light';
  const theme = useTheme();
  const { user } = useAuth();
  const dispatch = useAppDispatch();
  const { confirm } = useConfirm();
  const adminId: number = user ? user.accountId : 0;
  const [showFilter, setShowFilter] = useState(true);
  const [deleteFee, setDeleteFee] = useState(false);
  const YearData = useAppSelector(getYearData);

  const createTermFeeSettingListData = useAppSelector(getcreateTermFeeSettingListData);
  const feeDateSettingsData = useAppSelector(getfeeDateSettingsData);
  const feeDateSettingsStatus = useAppSelector(getfeeDateSettingsStatus);

  // const academicId = 10;
  const defaultYear = YearData[0]?.accademicId || 0;
  const [academicYearFilter, setAcademicYearFilter] = useState(defaultYear);
  const [feeTypeFilter, setFeeTypeFilter] = useState(0);

  const ClassSectionsData = useAppSelector(getClassSectionsData);
  const [classSectionsFilter, setClassSectionsFilter] = useState(0);

  const [classSections, setClassSections] = useState([]);

  const [termFee, setTermFeeData] = useState<any>([]);
  const [termFeeDetails, setTermFeeDetails] = useState<TermFeeDetail[]>([]);
  const [clickedCells, setClickedCells] = useState<{ feeId?: number; termId?: number; value?: string }[]>([]);

  const [individualSaveLoading, setIndividualSaveLoading] = useState<Record<string, boolean>>({});
  const [saving, setSaving] = useState(false);
  const [savingAll, setSavingAll] = useState(false);
  const [switchToUpdateButton, setSwitchToUpdateButton] = useState(false);
  const [loading, setLoading] = useState(false);
  const [succesResponse, setSuccesResponse] = useState('');
  const [showSuccessIcon, setShowSuccessIcon] = useState<{ [key: string]: boolean }>({});
  const [showErrorIcon, setShowErrorIcon] = useState<{ [key: string]: boolean }>({});
  const [individualSaveButtonEnabled, setIndividualSaveButtonEnabled] = useState<{ [key: string]: boolean }>({});
  const [termAmounts, setTermAmounts] = useState<{ [key: string]: number }>({});
  const [checkedRows, setCheckedRows] = useState({});
  const [feeAmounts, setFeeAmounts] = useState<{ [key: string]: number }>({});
  const [amountEnter, setAmountEnter] = useState<any>(0 || '');
  const [selectedRows, setSelectedRows] = useState<TermFeeDetail[]>([]);
  const [drawerOpen, setDrawerOpen] = React.useState<boolean>(false);
  const [textBoxValue, setTextBoxValue] = useState<boolean>(true);
  const [errMessages, setErrMessages] = useState<{ [key: string]: boolean }>({});
  const [cellErrorMessages, setCellErrorMessages] = useState<{ [key: string]: boolean }>({});
  const [currentPage, setCurrentPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [enteredValues, setEnteredValues] = useState<number[]>([]);
  const [enteredCells, setEnteredCells] = useState({});
  const [rowSums, setRowSums] = useState<Record<string, number>>({});
  const [cellValidationError, setCellValidationError] = useState(false);
  const [totalTermAmount, seTtotalTermAmount] = useState<number>(0);
  const [disabledCheckBoxes, setDisabledCheckBoxes] = useState<boolean[]>([]);

  const toggleDrawerOpen = () => {
    setDrawerOpen(true);
  };
  const toggleDrawerClose = () => setDrawerOpen(false);

  const initialTermFeeListRequest = React.useMemo(
    () => ({
      adminId,
      academicId: academicYearFilter,
      feeTypeId: feeTypeFilter,
      sectionId: classSectionsFilter,
    }),
    [adminId, academicYearFilter, classSectionsFilter, feeTypeFilter]
  );
  const currentTermFeeListRequest = React.useMemo(
    () => ({
      adminId,
      academicId: academicYearFilter,
      feeTypeId: feeTypeFilter,
      sectionId: classSectionsFilter,
    }),
    [adminId, academicYearFilter, classSectionsFilter, feeTypeFilter]
  );

  const currentCreateTermFeeListRequest = React.useMemo(
    () => ({
      adminId,
      accademicId: academicYearFilter,
      academicFeeId: 0,
      sectionId: classSectionsFilter,
      feeId: 1,
      termAmount: {},
    }),
    [adminId, classSectionsFilter, academicYearFilter]
  );
  const loadCreateTermFeeList = useCallback(async () => {
    try {
      const data = createTermFeeSettingListData;
      console.log('data::::', data);
    } catch (error) {
      console.error('Error loading term fee list:', error);
    }
  }, [createTermFeeSettingListData]);

  const loadFeeDateSettingsList = useCallback(
    async (request: { adminId: number; academicId: number; sectionId: number; feeTypeId: number }) => {
      try {
        setClickedCells([]);
        const data: any = await dispatch(fetchFeeDateSettings(request)).unwrap();
        console.log('data::::', data);
        if (data) {
          setFeeAmounts({});
          setRowSums({});
          setTermAmounts({});
          setIndividualSaveButtonEnabled({});
        }
        // setTermFeeData(data);
        // const ClassSectionArray = data.sections ? data.sections.map((item) => ({ ...item })) : [];
        // setClassSections(ClassSectionArray);
        // console.log('ClassSectionArray::::', ClassSectionArray);
        const TermFeeDetails = data.termFeeDetails
          ? data.termFeeDetails.map((item: TermFeeDetail) => ({ ...item }))
          : [];
        setTermFeeDetails(TermFeeDetails);
        console.log('TermFeeDetails::::', TermFeeDetails);
        // console.log('TermFeeDetails::::', TermFeeDetails);
      } catch (error) {
        console.error('Error loading term fee list:', error);
      }
    },
    [dispatch]
  );

  const handleSave = useCallback(
    async (row: TermFeeDetailsDataType) => {
      const { termFeeMapped, feeId, amount } = row;
      const isSelectedRow = selectedRows.includes(row);

      try {
        // Ensure termFee and its terms are defined and an array
        const termFeeMappedArray = termFeeMapped || [];
        const termsLength = termFee && Array.isArray(termFee.terms) ? termFee.terms.length : 0;
        const termFeeMappedLength =
          termFeeMappedArray && Array.isArray(termFeeMappedArray) ? termFeeMappedArray?.length : 0;

        let calAmount;
        if (isSelectedRow) {
          // Calculate the total new amount based on termFeeMapped
          calAmount = termFeeMappedLength * (termAmounts[`${feeId}_`] || 0);
        } else {
          // Calculate the total new amount based on termAmounts
          const length = termsLength - termFeeMappedLength || 0;
          calAmount = length * (termAmounts[`${feeId}_`] || 0);
        }

        // Sum the amounts from the existing termFeeMapped array
        const rowMappedSum = termFeeMappedArray?.reduce((acc, item) => acc + (item.amount || 0), 0) || 0;

        // Sum the amounts from feeAmounts for this feeId
        const rowSum = Object.entries(feeAmounts || {})
          .filter(([key]) => key.startsWith(`${feeId}_`)) // Filter by row's feeId
          .reduce((acc, [, amnt]) => acc + (amnt || 0), 0);

        const minusAmount = Object.entries(feeAmounts || {})?.map(([key]) => {
          return termFeeMappedArray
            .filter((mappedItem) => mappedItem.termId === parseInt(key.split('_')[1], 10))
            .map((mappedItem) => mappedItem.amount);
        });

        const flattenedMinusAmount = minusAmount?.flat().reduce((acc, amnt) => acc + (amnt || 0), 0);

        console.log('minusAmount----::::', minusAmount);

        // Calculate the total cell amount
        const cellAmounts = rowSum + rowMappedSum - flattenedMinusAmount;

        console.log('calAmount----::::', calAmount + rowMappedSum);
        console.log('cellAmounts----::::', cellAmounts);

        // Validation check: Ensure total amounts do not exceed the row's allowed amount
        if ((!isSelectedRow && calAmount + rowMappedSum > amount) || (isSelectedRow && cellAmounts > amount)) {
          setCellValidationError(true);
          seTtotalTermAmount(amount);
        } else {
          setSaving(true);
          console.log('row::::----', row);

          const allTermAmntWithFeeIdUpdates: { [key: number]: TermAmount[] } = {};

          // Check if the first feeId in the feeAmounts matches the current row's feeId
          const feeIdArray = Object.keys(feeAmounts).map((key) => parseInt(key.split('_')[0], 10));
          // if (firstFeeId !== feeId) {
          //   // If the feeId doesn't match, return early without executing further logic
          //   return;
          // }

          // Construct feeAmount array based on section amounts
          const feeAmountsArray = Object.keys(feeAmounts)
            .filter((key) => parseInt(key.split('_')[0], 10) === feeId) // Filter objects where feeId matches row?.feeId
            .map((key) => {
              const amnt = feeAmounts[key];
              if (!Number.isNaN(amnt)) {
                return {
                  termId: parseInt(key.split('_')[1], 10),
                  amount: amnt,
                  dbResult: 'string',
                  academicTermId: 0,
                };
              }
              return null;
            })
            .filter((feeAmount) => feeAmount !== null); // Remove any null values from the array

          const feeAmountsKeys = Object.keys(feeAmountsArray);

          // Check if any of the amounts is zero
          const zeroAmountExists = feeAmountsKeys.some((key) => feeAmounts[key] === 0);

          // const zeroAmountExists = Object.values(feeAmounts).some((amount) => amount === 0);

          // Check if any of the amounts is zero
          console.log('feeAmountsArray----::::', feeAmountsArray);
          console.log('feeIdArray----::::', feeIdArray);
          console.log('feeAmounts----::::', feeAmounts);
          if (zeroAmountExists) {
            setShowErrorIcon((prevMap) => ({ ...prevMap, [feeId]: true }));
            setTimeout(() => {
              setShowErrorIcon((prevMap) => ({ ...prevMap, [feeId]: false }));
            }, 2000);
            setTermAmounts({});
            setFeeAmounts({});
            // Display error message for zero amount
            console.error('Error: Amount cannot be zero');
          } else {
            // Store feeAmounts in state

            setIndividualSaveLoading((prevMap) => ({ ...prevMap, [feeId]: true }));

            const sendReq = [
              {
                adminId,
                accademicId: academicYearFilter,
                academicFeeId: 0,
                sectionId: classSectionsFilter,
                feeId,
                feeTypeId: feeTypeFilter,
                termAmount: feeAmountsArray,
              },
            ];
            const actionResult = await dispatch(createTermFeeSetting(sendReq));

            if (actionResult && Array.isArray(actionResult.payload)) {
              const results = actionResult.payload;
              console.log('results::::----', results);
              // setSelectedRows([]);
              setSelectedRows((prev) => prev.filter((f) => f.feeId !== row?.feeId));

              // Filter out items without termAmount property
              const filteredResults = results.filter((f) => f.termAmount);
              console.log('filteredResults::::----', filteredResults);

              // Extract termAmount arrays
              const termAmountsArray = filteredResults.map((f) => f.termAmount);

              // Flatten the array of termAmount arrays
              const termAmnt = termAmountsArray.flat();
              console.log('termAmnt::::----', termAmnt);

              // Find the item with dbResult === 'Success'
              const SuccessResult = termAmnt.find((f) => f.dbResult === 'Success');
              console.log('SuccessResult::::----', SuccessResult);
              if (SuccessResult) {
                setSuccesResponse(SuccessResult.dbResult);
              }

              // Find the item with dbResult === 'Updated'
              const UpdateResult = termAmnt.find((f) => f.dbResult === 'Updated');
              console.log('UpdateResult::::----', UpdateResult);
              if (UpdateResult) {
                setSuccesResponse(UpdateResult.dbResult);
              }
              setSaving(false);
              // setTermFeeDetails(response);
              setIndividualSaveLoading((prevMap) => ({ ...prevMap, [row?.feeId]: false }));
              setIndividualSaveButtonEnabled((prevEnabled) => ({
                ...prevEnabled,
                [feeId]: false, // Enable the button again after the operation completes
              }));
              console.log('checkedRows::::----', checkedRows);

              // setFeeAmounts([]);

              setClickedCells((prev) => {
                return prev.filter((f) => f.feeId !== feeId);
              });

              setFeeAmounts((prevAmounts) => {
                const updatedAmounts = { ...prevAmounts };

                // Remove the values corresponding to the current row's feeId
                Object.keys(updatedAmounts).forEach((key: any) => {
                  const [currentFeeId, currentTermId] = key.split('_').map(Number);
                  if (currentFeeId === feeId) {
                    delete updatedAmounts[key];
                  }
                });

                return updatedAmounts;
              });

              setTermAmounts((prevAmounts) => {
                const updatedAmounts = { ...prevAmounts };

                // Remove the values corresponding to the current row's feeId
                Object.keys(updatedAmounts).forEach((key: any) => {
                  const [currentFeeId, currentTermId] = key.split('_').map(Number);
                  if (currentFeeId === feeId) {
                    delete updatedAmounts[key];
                  }
                });

                return updatedAmounts;
              });

              const termAmntWithFeeId = termAmnt.map((item) => ({
                // ...item,
                academicTermId: item.academicTermId,
                amount: item.amount,
                termId: item.termId,
              }));
              // console.log('termAmntWithFeeId::::----', termAmntWithFeeId);

              // loadFeeDateSettingsList(currentTermFeeListRequest);

              // Collect all updates for each feeId
              if (!allTermAmntWithFeeIdUpdates[feeId]) {
                allTermAmntWithFeeIdUpdates[feeId] = [];
              }
              allTermAmntWithFeeIdUpdates[feeId] = [...allTermAmntWithFeeIdUpdates[feeId], ...termAmntWithFeeId];

              if (SuccessResult) {
                // Update termFeeMapped for the current row
                const updatedTermFeeDetails = termFeeDetails.map((item) => {
                  if (item.feeId === feeId) {
                    const termFeeMappedArr = Array.isArray(item.termFeeMapped) ? item.termFeeMapped : [];
                    const updatedTermFeeMapped = [...termFeeMappedArr, ...termAmntWithFeeId];

                    console.log('termAmntWithFeeId1st::::----', termAmntWithFeeId); // Correct placement of console.log
                    return { ...item, termFeeMapped: updatedTermFeeMapped };
                  }
                  return item;
                });

                setTermFeeDetails(updatedTermFeeDetails);
              } else if (UpdateResult) {
                // const updatedTermFeeDetails = termFeeDetails.map((item) => {
                //   if (allTermAmntWithFeeIdUpdates[item.feeId] && Array.isArray(item.termFeeMapped)) {
                //     const updatedTermFeeMapped = [...item.termFeeMapped, ...allTermAmntWithFeeIdUpdates[item.feeId]];
                //     return { ...item, termFeeMapped: updatedTermFeeMapped };
                //   }
                //   return item;
                // });

                const updatedTermFeeDetails = termFeeDetails.map((item) => {
                  if (item.feeId === feeId && Array.isArray(item.termFeeMapped)) {
                    const existingTermMap = item.termFeeMapped.find((i) =>
                      termAmntWithFeeId.some((fee) => fee.academicTermId === i.academicTermId)
                    );

                    if (existingTermMap) {
                      // If an existing academicTermId is found, update that instead of adding new duplicates
                      const updatedTermFeeMapped = item.termFeeMapped.map((i) =>
                        termAmntWithFeeId.some((fee) => fee.academicTermId === i.academicTermId)
                          ? { ...i, ...termAmntWithFeeId.find((fee) => fee.academicTermId === i.academicTermId) }
                          : i
                      );
                      return { ...item, termFeeMapped: updatedTermFeeMapped };
                    } else {
                      // If no existing academicTermId matches, push all termAmntWithFeeId
                      const updatedTermFeeMapped = [...item.termFeeMapped, ...termAmntWithFeeId];
                      return { ...item, termFeeMapped: updatedTermFeeMapped };
                    }
                  }
                  return item;
                });

                setTermFeeDetails(updatedTermFeeDetails);

                console.log('termAmntWithFeeId2nd::::----', termAmntWithFeeId);
              }
              // Update termFeeMapped for the current row
              // const updatedTermFeeDetails = termFeeDetails.map((item) => {
              //   if (item.feeId === feeId) {
              //     const updatedTermFeeMapped = [...item.termFeeMapped];
              //     return { ...item, termFeeMapped: [...updatedTermFeeMapped, ...termAmnt] };
              //   }
              //   return item;
              // });
              // setTermFeeDetails(updatedTermFeeDetails);
              // console.log('updatedTermFeeDetails::::----', updatedTermFeeDetails);

              // Update checkedRows when the row is unchecked
              // const updatedCheckedRows = { ...checkedRows };
              // if (checkedRows[row?.feeId]) {
              //   delete updatedCheckedRows[row?.feeId];
              // }
              // setCheckedRows(updatedCheckedRows);

              setShowSuccessIcon((prevMap) => ({ ...prevMap, [row?.feeId]: true }));
              setTimeout(() => {
                setShowSuccessIcon((prevMap) => ({ ...prevMap, [row?.feeId]: false }));
              }, 5000);
            } else {
              setIndividualSaveLoading((prevMap) => ({ ...prevMap, [row?.feeId]: false }));
              setShowErrorIcon((prevMap) => ({ ...prevMap, [row?.feeId]: true }));
              setTimeout(() => {
                setShowErrorIcon((prevMap) => ({ ...prevMap, [row?.feeId]: false }));
              }, 2000);
            }
            // loadFeeDateSettingsList(currentTermFeeListRequest);
          }
          // const updatedTermFeeDetails = termFeeDetails.map((item) => {
          //   if (allTermAmntWithFeeIdUpdates[item.feeId] && Array.isArray(item.termFeeMapped)) {
          //     const updatedTermFeeMapped = [...item.termFeeMapped, ...allTermAmntWithFeeIdUpdates[item.feeId]];
          //     return { ...item, termFeeMapped: updatedTermFeeMapped };
          //   }
          //   return item;
          // });

          // setTermFeeDetails(updatedTermFeeDetails);
        }
      } catch (error) {
        // Handle errors here
        setIndividualSaveButtonEnabled((prevEnabled) => ({
          ...prevEnabled,
          [row?.feeId]: true,
        }));
        setShowErrorIcon((prevMap) => ({ ...prevMap, [row?.feeId]: true }));
        setTimeout(() => {
          setShowErrorIcon((prevMap) => ({ ...prevMap, [row?.feeId]: false }));
        }, 2000);
        setIndividualSaveLoading((prevMap) => ({ ...prevMap, [row?.feeId]: false }));
        const errorMessage = (
          <ErrorMessage loop={false} jsonIcon={ErrorIcon} message="Something went wrong please try again." />
        );
        await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
      }
    },
    [
      dispatch,
      adminId,
      academicYearFilter,
      classSectionsFilter,
      feeAmounts,
      checkedRows,
      feeTypeFilter,
      termFeeDetails,
      confirm,
      termAmounts,
      termFee,
      selectedRows,
    ]
  );

  const handleAllSave = useCallback(async () => {
    try {
      setSavingAll(true);
      const feeIdArray = Object.keys(feeAmounts).map((key) => parseInt(key.split('_')[0], 10));

      // Ensure feeIdArray contains unique values
      const uniqueFeeIds = [...new Set(feeIdArray)];

      type FeeAmounts = {
        [key: string]: any;
      };

      const allTermAmntWithFeeIdUpdates: { [key: number]: TermAmount[] } = {};

      // Use Promise.all to handle asynchronous dispatch calls for each feeId
      await Promise.all(
        uniqueFeeIds.map(async (feeId) => {
          // Filter feeAmounts for the current feeId
          const filteredFeeAmounts = Object.entries(feeAmounts)
            .filter(([key]) => parseInt(key.split('_')[0], 10) === feeId)
            .reduce((obj: FeeAmounts, [key, value]) => {
              obj[key] = value;
              return obj;
            }, {});

          const feeAmountsArray = Object.entries(filteredFeeAmounts)
            .map(([key, value]) => {
              const amount = parseInt(value, 10);
              if (!Number.isNaN(amount)) {
                return {
                  termId: parseInt(key.split('_')[1], 10),
                  amount,
                  dbResult: 'string',
                  academicTermId: 0,
                };
              }
              return null;
            })
            .filter((termFeeAmount) => termFeeAmount !== null); // Filter out null values

          const zeroAmountExists = Object.values(filteredFeeAmounts).some((amount) => amount === 0);

          if (!zeroAmountExists) {
            const sendReq = [
              {
                adminId,
                accademicId: academicYearFilter,
                academicFeeId: 0,
                sectionId: classSectionsFilter,
                feeId,
                feeTypeId: feeTypeFilter,
                termAmount: feeAmountsArray,
              },
            ];

            const actionResult = await dispatch(createTermFeeSetting(sendReq));

            if (actionResult && Array.isArray(actionResult.payload)) {
              setSwitchToUpdateButton(false);
              setSelectedRows([]);
              const results = actionResult.payload;
              const filteredResults = results.filter((f) => f.termAmount);
              const termAmountsArray = filteredResults.map((f) => f.termAmount);
              const termAmnt = termAmountsArray.flat();

              const termAmntWithFeeId = termAmnt.map((item) => ({
                ...item,
              }));

              // Collect all updates for each feeId
              if (!allTermAmntWithFeeIdUpdates[feeId]) {
                allTermAmntWithFeeIdUpdates[feeId] = [];
              }
              allTermAmntWithFeeIdUpdates[feeId] = [...allTermAmntWithFeeIdUpdates[feeId], ...termAmntWithFeeId];

              const SuccessResult = termAmnt.find((f) => f.dbResult === 'Success');
              if (SuccessResult) {
                setSuccesResponse(SuccessResult.dbResult);
              }

              const UpdateResult = termAmnt.find((f) => f.dbResult === 'Updated');
              if (UpdateResult) {
                setSuccesResponse(UpdateResult.dbResult);
              }

              setIndividualSaveButtonEnabled((prevEnabled) => ({
                ...prevEnabled,
                [feeId]: false, // Enable the button again after the operation completes
              }));

              setFeeAmounts((prevAmounts) => {
                const updatedAmounts = { ...prevAmounts };

                // Remove the values corresponding to the current row's feeId
                Object.keys(updatedAmounts).forEach((key: any) => {
                  const [currentFeeId, currentTermId] = key.split('_').map(Number);
                  if (currentFeeId === feeId) {
                    delete updatedAmounts[key];
                  }
                });

                return updatedAmounts;
              });

              // Update termFeeMapped for the current row
              // setTermFeeDetails((prevDetails) =>
              //   prevDetails.map((item) => {
              //     if (item.feeId === feeId) {
              //       const updatedTermFeeMapped = Array.isArray(item.termFeeMapped) ? [...item.termFeeMapped] : [];
              //       return { ...item, termFeeMapped: [...updatedTermFeeMapped, ...termAmnt] };
              //     }
              //     return item;
              //   })
              // );
              // console.log('Term Fee Details Updated:', termFeeDetails);
              setCheckedRows([]);
              loadFeeDateSettingsList(currentTermFeeListRequest);
            }
          }
        })
      );
      // Update termFeeDetails with all collected updates
      const updatedTermFeeDetails = termFeeDetails.map((item) => {
        if (allTermAmntWithFeeIdUpdates[item.feeId]) {
          const updatedTermFeeMapped = Array.isArray(item.termFeeMapped)
            ? [...item.termFeeMapped, ...allTermAmntWithFeeIdUpdates[item.feeId]]
            : [...allTermAmntWithFeeIdUpdates[item.feeId]]; // Ensure feeMapped is an array before spreading
          return { ...item, termFeeMapped: updatedTermFeeMapped };
        }
        return item;
      });

      setTermFeeDetails(updatedTermFeeDetails);
      setSavingAll(false); // Set saving to false after all asynchronous operations are completed
    } catch (error) {
      // Handle errors here
      // await showConfirmation(<ErrorMessage icon={ErrorMsg} message="Message content already created" />, '');
    }
  }, [
    dispatch,
    adminId,
    academicYearFilter,
    classSectionsFilter,
    feeAmounts,
    loadFeeDateSettingsList,
    currentTermFeeListRequest,
    feeTypeFilter,
    termFeeDetails,
  ]);

  const handleAmountChange = useCallback(
    (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>, row: TermFeeDetail, termId: number, id: number) => {
      const { feeId } = row;
      const isSelectedRow = selectedRows.includes(row);
      const cellKey: string = `${feeId}_${termId}`;

      const newValue = parseInt(e.target.value, 10);
      const newValueString = e.target.value;

      setAmountEnter(newValue || newValueString);
      setEnteredValues((prevValues) => [...prevValues, newValue]);
      setEnteredCells((prevCells) => ({ ...prevCells, [cellKey]: newValue }));

      console.log('enteredValues::::----', enteredValues);
      console.log('feeAmounts::::----', feeAmounts);

      const termFeeMapped = Array.isArray(row?.termFeeMapped) ? row?.termFeeMapped : [];
      const amountObj = termFeeMapped.find((f) => f.termId === termId);
      const amount = amountObj ? amountObj.amount : 0;

      // Update the enabled state for the corresponding row
      const isZeroOrEmpty = newValue === 0 || newValueString === '';
      setIndividualSaveButtonEnabled((prevEnabled) => ({
        ...prevEnabled,
        [`${feeId}`]: !isZeroOrEmpty,
      }));

      // // Update the feeAmounts array based on the new value
      // setFeeAmounts((prevAmounts) => {
      //   const updatedAmounts = { ...prevAmounts };

      //   // Check if the feeId exists in the feeAmounts array
      //   if (updatedAmounts[`${feeId}_${termId}`]) {
      //     // If the newValue is different, update the feeAmounts array
      //     if (updatedAmounts[`${feeId}_${termId}`] !== newValue) {
      //       updatedAmounts[`${feeId}_${termId}`] = newValue;
      //     }
      //   } else {
      //     // If the `${feeId}_${termId}` doesn't exist, add it to the feeAmounts array
      //     updatedAmounts[`${feeId}_${termId}`] = newValue;
      //   }

      //   return updatedAmounts;
      // });
      const { value } = e.target;
      const newAmount = parseFloat(value) || 0;
      // setFeeAmounts(newTermAmounts);

      // Incorporate termFeeMapped array into the rowSum calculation
      const rowMappedSum = termFeeMapped.reduce((acc, item) => acc + (item.amount || 0), 0);

      // Calculate the total sum for the row, including both termFeeMapped and new amounts
      const termsLength = termFee.terms.length || 0;
      const termFeeMappedLength = termFeeMapped.length || 0;
      let newTermAmounts;
      let rowSum;

      if (id !== 1) {
        newTermAmounts = { ...feeAmounts, [cellKey]: newValue };
        rowSum = Object.entries(newTermAmounts)
          .filter(([key]) => key.startsWith(`${row?.feeId}_`)) // Filter by row's feeId
          .reduce((acc, [, amnt]) => {
            console.log('Accumulator (acc):', acc); // Logs the current accumulator value
            console.log('Amount (amnt):', amnt); // Logs the current amount being added
            return acc + Number(amnt);
          }, 0);
      } else {
        if (!isSelectedRow) {
          const length = termsLength - termFeeMappedLength;
          rowSum = length * newValue;
        } else {
          rowSum = termFeeMappedLength * newValue;
        }
      }

      // Add the amounts as numbers

      const minusAmount = Object.entries(newTermAmounts || {})?.map(([key]) => {
        return termFeeMapped
          .filter((mappedItem) => mappedItem.termId === parseInt(key.split('_')[1], 10))
          .map((mappedItem) => mappedItem.amount);
      });

      const flattenedMinusAmount = minusAmount?.flat().reduce((acc, amnt) => acc + (amnt || 0), 0);

      console.log('minusAmount----::::', minusAmount);
      console.log('newTermAmounts----::::', newTermAmounts);

      // Calculate the total cell amount
      const cellAmounts = rowSum - flattenedMinusAmount;
      if (id !== 1) {
        // Update the row sum state
        setRowSums((prevSums) => ({ ...prevSums, [cellKey]: rowSum + rowMappedSum }));
      } else {
        setRowSums((prevSums) => ({ ...prevSums, [cellKey]: cellAmounts }));
      }
      console.log('rowSum::::----', rowSum);
      console.log('rowMappedSum::::----', rowMappedSum);
      console.log('cellAmounts----::::', cellAmounts);

      // console.log('newTermAmounts::::----', newTermAmounts);
      // console.log('feeAmounts::::----', feeAmounts);
      // console.log('newValue::::----', newValue);

      // Update the feeAmounts array based on the new value
      // if (id !== 1) {
      setFeeAmounts((prevAmounts) => {
        const updatedAmounts = { ...prevAmounts };
        if (newValue === 0 || Number.isNaN(newValue)) {
          if (isSelectedRow && id === 1) {
            updatedAmounts[cellKey] = amount;
          } else {
            updatedAmounts[cellKey] = '';
          }
           setCellErrorMessages((prev) => ({
              ...prev,
              [cellKey]: '',
            }));
          // delete updatedAmounts[cellKey];
        } else if (newValue !== 0 || !Number.isNaN(newValue)) {
          updatedAmounts[cellKey] = newValue;
          if (newValue > row?.amount || rowSum + rowMappedSum > row?.amount) {
            setCellErrorMessages((prev) => ({
              ...prev,
              [cellKey]: 'Enter a valid amount',
            }));
          } else {
            setCellErrorMessages((prev) => ({
              ...prev,
              [cellKey]: '',
            }));
          }
        } else {
          updatedAmounts[cellKey] = amount;
        }

        return updatedAmounts;
      });
      // }
    },
    [
      setAmountEnter,
      setEnteredValues,
      feeAmounts,
      selectedRows,
      setIndividualSaveButtonEnabled,
      enteredValues,
      setFeeAmounts,
      termFee,
    ]
  );

  // const handleAmountChange = useCallback(
  //   (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>, row: TermFeeDetail, termId: number, id: number) => {
  //     const { feeId } = row;
  //     const cellKey = `${feeId}_${termId}`;
  //     const inputValue = e.target.value.trim(); // Trim to handle accidental spaces
  //     const newValue = parseFloat(inputValue) || 0;

  //     // Update state for entered amount and entered cells
  //     setAmountEnter(newValue);
  //     setEnteredCells((prevCells) => ({
  //       ...prevCells,
  //       [cellKey]: newValue,
  //     }));

  //     // Update the enabled state for individual save button
  //     setIndividualSaveButtonEnabled((prevEnabled) => ({
  //       ...prevEnabled,
  //       [feeId]: newValue > 0,
  //     }));

  //     // Handle feeAmounts updates
  //     setFeeAmounts((prevAmounts) => {
  //       const updatedAmounts = { ...prevAmounts };

  //       if (!inputValue || newValue === 0 || Number.isNaN(newValue)) {
  //         delete updatedAmounts[cellKey];
  //       } else {
  //         updatedAmounts[cellKey] = newValue;
  //       }

  //       return updatedAmounts;
  //     });

  //     // Calculate the total sum for the row
  //     const updatedAmounts = { ...feeAmounts, [cellKey]: newValue };
  //     const rowSum = Object.entries(updatedAmounts)
  //       .filter(([key]) => key.startsWith(`${feeId}_`)) // Only consider entries for the current row
  //       .reduce((sum, [, value]) => sum + value, 0);

  //     // Update row sums
  //     setRowSums((prevSums) => ({
  //       ...prevSums,
  //       [cellKey]: rowSum,
  //     }));

  //     console.log('Updated Values:', {
  //       cellKey,
  //       newValue,
  //       rowSum,
  //       updatedAmounts,
  //     });
  //   },
  //   [feeAmounts, setAmountEnter, setEnteredCells, setFeeAmounts, setIndividualSaveButtonEnabled, setRowSums]
  // );

  const handleYearChange = (e: SelectChangeEvent) => {
    setAcademicYearFilter(parseInt(YearData.filter((item) => item.accademicId === e.target.value)[0].accademicId, 10));
    loadFeeDateSettingsList({ ...currentTermFeeListRequest, academicId: parseInt(e.target.value, 10) });
    setSelectedRows([]);
    setCheckedRows(false);
    setErrMessages({});
    setIndividualSaveButtonEnabled({});
  };

  const handleFeeTypeChange = (e: SelectChangeEvent) => {
    const feeTypeId = parseInt(e.target.value, 10);
    setFeeTypeFilter(feeTypeId);
    loadFeeDateSettingsList({ ...currentTermFeeListRequest, feeTypeId: parseInt(e.target.value, 10) });
    setErrMessages({});
    setSelectedRows([]);
    setIndividualSaveButtonEnabled({});
  };

  const handleClassSectionChange = (e: SelectChangeEvent) => {
    setClassSectionsFilter(
      parseInt(ClassSectionsData.filter((item) => item.sectionId === e.target.value)[0].sectionId, 10)
    );
    loadFeeDateSettingsList({ ...currentTermFeeListRequest, sectionId: parseInt(e.target.value, 10) });
    setCheckedRows(false);
    setErrMessages({});
    setSelectedRows([]);
    setIndividualSaveButtonEnabled({});
  };

  React.useEffect(() => {
    if (feeDateSettingsStatus === 'idle') {
      dispatch(fetchYearList(adminId));
      dispatch(fetchClassSections({ adminId, academicId: academicYearFilter }));
      loadFeeDateSettingsList(currentTermFeeListRequest);
      // dispatch(fetchGetTermFee(initialTermFeeListRequest));
    }
    setTermFeeData(feeDateSettingsData);
    // console.log('TermFeeData----::::', TermFeeData);
  }, [
    dispatch,
    adminId,
    currentTermFeeListRequest,
    feeDateSettingsData,
    feeDateSettingsStatus,
    loadFeeDateSettingsList,
    academicYearFilter,
  ]);

  const calculateMonthlyTotals = useCallback(
    (item: TermFeeDataType) => {
      let total = 0;
      termFeeDetails.forEach((fee) => {
        const monthData = fee.termFeeMapped && fee.termFeeMapped.find((data) => data.termId === item.termId);
        if (monthData) {
          const { amount } = monthData;
          total += amount || 0;
        }
      });
      return total;
    },
    [termFeeDetails]
  );

  // Calculate total annual amount
  const totalAnnualAmount = useMemo(() => {
    return termFeeDetails.reduce((acc, fee) => acc + fee.amount, 0);
  }, [termFeeDetails]);

  const handleReset = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      setClickedCells([]);
      setSelectedRows([]);
      loadFeeDateSettingsList({ ...initialTermFeeListRequest, sectionId: 0 });
      setAcademicYearFilter(defaultYear);
      setFeeTypeFilter(0);
      setClassSectionsFilter(0);
      setErrMessages({});
      setFeeAmounts({});
      setRowSums({});
      setTermAmounts({});
      // dispatch(fetchFeeDateSettings({ adminId, academicId: 10, sectionId: 0 }));
    },
    [initialTermFeeListRequest, loadFeeDateSettingsList, defaultYear]
  );

  const [headerValues, setHeaderValues] = useState<string[]>([]);
  const [selectedCells, setSelectedCells] = useState<{ rowIndex: number; columnIndex: number }[]>([]);

  const handleCellClick = useCallback(
    (row: TermFeeDetail, termId: number, i: any) => {
      const { feeId, termFeeMapped, amount } = row;
      // const { termId } = column;

      const key = `${feeId}_`;
      const enteredAmountsSum = rowSums[key] || 0;
      // console.log('enteredAmountsSum::::----', enteredAmountsSum);

      // // Calculate sumRowAmounts
      const sumRowAmounts = termFeeMapped?.reduce((total, item) => total + (item.amount || 0), 0) || 0;
      const tAmount = sumRowAmounts + enteredAmountsSum;

      if (tAmount < amount) {
        setClickedCells((prevClickedCells) => [...prevClickedCells, { feeId, termId }]);
      } else {
        setCellValidationError(true);
        seTtotalTermAmount(amount);
      }
      console.log('tAmount::::----', tAmount);
      console.log('rowSums5::::----', rowSums);
      console.log('sumRowAmounts::::----', sumRowAmounts);
    },
    [setClickedCells, rowSums] // Dependencies
  );

  // const handleCheckboxClick = (row) => {
  //   // setSwitchToUpdateButton(true);
  //   setCheckedRows((prevCheckedRows) => {
  //     const updatedCheckedRows = { ...prevCheckedRows };
  //     updatedCheckedRows[row?.feeId] = !prevCheckedRows[row?.feeId];
  //     return updatedCheckedRows;
  //   });
  //   setIndividualSaveButtonEnabled((prevEnabled) => ({
  //     ...prevEnabled,
  //     [row?.feeId]: false,
  //   }));
  // };

  // Add a function to handle select all checkboxes:
  // const handleSelectAll = () => {
  //   setSwitchToUpdateButton(true);
  //   const allSelected = Object.keys(checkedRows).length === termFeeDetails.length;

  //   if (allSelected) {
  //     // If all rows are already selected, deselect all rows
  //     setCheckedRows({});
  //     setIndividualSaveButtonEnabled([]);
  //     setSwitchToUpdateButton(false);
  //   } else {
  //     // Otherwise, select all rows
  //     const updatedCheckedRows = {};
  //     termFeeDetails.forEach((row) => {
  //       updatedCheckedRows[row?.feeId] = true;
  //     });
  //     setCheckedRows(updatedCheckedRows);
  //   }
  // };

  const handleTermTitleSave = useCallback(
    async (values: CreateTermFeeSettingTitleDataType) => {
      try {
        console.log('values::::----', values);
        const response = await dispatch(createTermFeeSettingTitle(values)).unwrap();
        console.log('response::::----', response);
        if (response.id > 0) {
          toggleDrawerClose();
          loadFeeDateSettingsList({ ...currentTermFeeListRequest });
          const successMessage = (
            <SuccessMessage icon="" loop={false} jsonIcon={Success} message="Term title create successfully" />
          );
          await confirm(successMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        } else if (response.id === 0) {
          toggleDrawerClose();
          const errorMessage = <ErrorMessage icon="" jsonIcon={ErrorIcon} message="Term title already created" />;
          await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
          toggleDrawerOpen();
        } else {
          const errorMessage = <ErrorMessage icon="" jsonIcon={ErrorIcon} message="Term title create failed" />;
          await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        }
      } catch (error) {
        console.error(error);
        const errorMessage = (
          <ErrorMessage
            icon=""
            jsonIcon={ErrorIcon}
            message="Term title creating something went wrong please try again."
          />
        );
        await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
      }
    },
    [dispatch, confirm, loadFeeDateSettingsList, currentTermFeeListRequest]
  );

  const handleDeleteRow = useCallback(
    async (row: TermFeeDetailsDataType) => {
      const { feeId } = row;
      console.log('row::::----', row);
      // const fineMappedIds = row?.filter((f) => f.termId === term.termId).map((m) => m.fineMappedId);

      const sendConfirmMessage = (
        <DeleteMessage
          jsonIcon={deleteBin}
          message={
            <div style={{ color: theme.palette.error.main }}>
              Are you sure you want to delete the row &quot;{}&quot; ?
            </div>
          }
        />
      );

      if (await confirm(sendConfirmMessage, 'Delete Row?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
        const sendReq = [
          {
            adminId,
            accademicId: academicYearFilter,
            feeId,
            sectionId: classSectionsFilter,
            dbResult: '',
            deletedId: 0,
          },
        ];

        console.log('sendReq', sendReq);

        const deleteResponse = await dispatch(DeleteAllTermFee(sendReq));
        console.log('deleteResponse::::----', deleteResponse);
        if (deleteResponse && Array.isArray(deleteResponse.payload)) {
          const results = deleteResponse.payload;
          const errorMessages = results.find((result) => result.dbResult === 'Failed');
          const successMessages = results.find((result) => result.dbResult === 'Deleted');

          if (!errorMessages) {
            loadFeeDateSettingsList({ ...currentTermFeeListRequest, sectionId: 1 });
            const deleteSuccessMessage = (
              <SuccessMessage loop={false} jsonIcon={deleteSuccess} message="Delete successfully" />
            );
            await confirm(deleteSuccessMessage, 'Success', { okLabel: 'Ok', showOnlyOk: true });
          } else if (!successMessages) {
            const deleteErrorMessage = <DeleteMessage loop={false} jsonIcon={errorIcon} message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          } else {
            const deleteErrorMessage = <DeleteMessage loop={false} jsonIcon={errorIcon} message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          }
        }
      }
    },
    [
      confirm,
      dispatch,
      adminId,
      academicYearFilter,
      theme,
      classSectionsFilter,
      loadFeeDateSettingsList,
      currentTermFeeListRequest,
    ]
  );

  const handleDeleteCell = useCallback(
    async (academicTermId: number) => {
      const sendConfirmMessage = (
        <DeleteMessage
          jsonIcon={deleteBin}
          message={
            <div style={{ color: theme.palette.error.main }}>
              Are you sure you want to delete the cell &quot;{}&quot; ?
            </div>
          }
        />
      );

      if (await confirm(sendConfirmMessage, 'Delete Row?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
        const sendReq = [
          {
            adminId,
            accademicId: academicYearFilter,
            academicTermId,
            dbResult: '',
            deletedId: 0,
          },
        ];

        console.log('sendReq', sendReq);

        const deleteResponse = await dispatch(DeleteTermFee(sendReq));
        console.log('deleteResponse::::----', deleteResponse);
        if (deleteResponse && Array.isArray(deleteResponse.payload)) {
          const results = deleteResponse.payload;
          const errorMessages = results.find((result) => result.dbResult === 'Failed');
          const successMessages = results.find((result) => result.dbResult === 'Deleted');

          if (!errorMessages) {
            loadFeeDateSettingsList({ ...currentTermFeeListRequest });
            const deleteSuccessMessage = (
              <SuccessMessage loop={false} jsonIcon={deleteSuccess} message="Delete successfully" />
            );
            await confirm(deleteSuccessMessage, 'Success', { okLabel: 'Ok', showOnlyOk: true });
          } else if (!successMessages) {
            const deleteErrorMessage = <DeleteMessage loop={false} jsonIcon={errorIcon} message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          } else {
            const deleteErrorMessage = <DeleteMessage loop={false} jsonIcon={errorIcon} message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          }
        }
      }
    },
    [confirm, dispatch, adminId, academicYearFilter, theme, loadFeeDateSettingsList, currentTermFeeListRequest]
  );

  const handleDeleteMultiple = useCallback(async () => {
    const sendConfirmMessage = (
      <DeleteMessage jsonIcon={deleteBin} message={<div>Are you sure you want to delete all ?</div>} />
    );
    if (await confirm(sendConfirmMessage, 'Delete Basic Fee?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
      // const deleteResponse = await dispatch(deleteBasicFeeList(sendReq));
      const sendRequests: TermFeeMappedDeleteAllDataType[] = await Promise.all(
        selectedRows?.map(async (row) => {
          const sendReq = {
            adminId,
            accademicId: academicYearFilter,
            feeId: row?.feeId,
            sectionId: classSectionsFilter,
            dbResult: '',
            deletedId: 0,
          };
          return sendReq;
        }) || []
      );
      const deleteResponse = await dispatch(DeleteAllTermFee(sendRequests));

      console.log('deleteResponse', deleteResponse);
      if (deleteResponse && Array.isArray(deleteResponse.payload)) {
        const results: TermFeeMappedDeleteAllDataType[] = deleteResponse.payload;
        const errorMessages = results.find((result) => result.dbResult === 'Failed');
        const successMessages = results.find((result) => result.dbResult === 'Success');

        if (!errorMessages) {
          loadFeeDateSettingsList({ ...currentTermFeeListRequest });
          const deleteSuccessMessage = (
            <SuccessMessage loop={false} jsonIcon={deleteSuccess} message="Delete All Successfully" />
          );
          await confirm(deleteSuccessMessage, 'Success', { okLabel: 'Ok', showOnlyOk: true });
        } else if (!successMessages) {
          const deleteErrorMessage = <DeleteMessage loop={false} jsonIcon={errorIcon} message="Delete Failed" />;
          await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
        } else {
          const deleteErrorMessage = (
            <DeleteMessage loop={false} jsonIcon={errorIcon} message="Something went wrong please try again" />
          );
          await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
        }
        setSelectedRows([]);
        setTermFeeDetails((prevDetails) => prevDetails.filter((item) => !selectedRows.includes(item.feeId)));
      }
    }
  }, [
    confirm,
    dispatch,
    selectedRows,
    adminId,
    academicYearFilter,
    loadFeeDateSettingsList,
    currentTermFeeListRequest,
    classSectionsFilter,
  ]);

  // Pagination handlers
  const handlePageChange = useCallback(
    (event: any, newPage: any) => {
      setCurrentPage(newPage); // Update current page
    },
    [setCurrentPage]
  );

  const handleChangeRowsPerPage = useCallback(
    (event: any) => {
      setRowsPerPage(parseInt(event.target.value, 10)); // Update rows per page
      // Reset to first page when changing rows per page
    },
    [setRowsPerPage]
  );

  const paginatedData = termFeeDetails.slice(currentPage * rowsPerPage, currentPage * rowsPerPage + rowsPerPage);

  const pageProps = useMemo(
    () => ({
      rowsPerPageOptions: [10, 20, 30, 50, 100, 200, 300, 500],
      pageNumber: currentPage,
      pageSize: rowsPerPage,
      totalRecords: termFeeDetails.length,
      onPageChange: handlePageChange,
      onRowsPerPageChange: handleChangeRowsPerPage,
    }),
    [handleChangeRowsPerPage, handlePageChange, currentPage, rowsPerPage, termFeeDetails]
  );
  // const inputRef = useRef<HTMLInputElement | null>(null);
  const inputRef = useRef({}); // Assuming you're managing multiple inputs

  const debouncedHandleAmountChange = debounce((e, row, termId, id) => {
    handleAmountChange(e, row, termId, id);
  }, 300);

  const [isAutoFocus, setIsAutoFocus] = useState({});

  const FeeDateSettingsListColumns: DataTableColumn<TermFeeDetail>[] = useMemo(() => {
    const baseColumns: DataTableColumn<any>[] = [
      {
        name: 'feeTitle',
        renderHeader: () => (
          <Typography minWidth={130} textAlign="start" className="header-color" variant="subtitle2" fontSize={14}>
            Title
          </Typography>
        ),
        renderCell: (row) => (
          <Typography
            className="header-color"
            // color="primary"
            px={1}
            minWidth={130}
            textAlign="start"
            variant="subtitle2"
            fontSize={14}
          >
            {row?.feeTitle}
          </Typography>
        ),
      },
      {
        name: 'amount',
        renderHeader: () => (
          <Typography minWidth={110} textAlign="start" className="header-color" variant="subtitle2" fontSize={14}>
            Amount
          </Typography>
        ),
        renderCell: (row) => (
          <Typography minWidth={100} px={1} textAlign="start" variant="subtitle1" fontSize={14}>
            {row?.amount}
          </Typography>
        ),
      },
      {
        name: 'termfeeAmount',
        renderHeader: () => (
          <Typography minWidth={110} className="header-color" variant="subtitle2" fontSize={13}>
            Term Fee Amount
          </Typography>
        ),
        renderCell: (row) => {
          const { feeId } = row;
          const isSelectedRow = selectedRows.includes(row);
          const termIds = termFee.terms.map((m: TermAmount) => m.termId);
          const termFeeMapped = Array.isArray(row?.termFeeMapped) ? row?.termFeeMapped : [];
          const id = 1;
          // Check if all termIds in termFeeMapped are included in termIds
          const allAmountCheck = termIds.every((termId: number) =>
            termFeeMapped.some((f: TermAmount) => f.termId === termId)
          );

          const termFeeAmountsSum = termFeeMapped.reduce((total, i) => total + i.amount, 0);
          const disabled = row?.amount <= termFeeAmountsSum;

          const termFeeMappedArray = termFeeMapped || [];
          const termsLength = termFee && Array.isArray(termFee.terms) ? termFee.terms.length : 0;
          const termFeeMappedLength =
            termFeeMappedArray && Array.isArray(termFeeMappedArray) ? termFeeMappedArray?.length : 0;

          // let calAmount;
          // if (isSelectedRow) {
          //   // Calculate the total new amount based on termFeeMapped
          //   calAmount = termFeeMappedLength * (termAmounts[`${feeId}_`] || 0);
          // } else {
          //   // Calculate the total new amount based on termAmounts
          //   const length = termsLength - termFeeMappedLength + termFeeAmountsSum || 0;
          //   calAmount = length * (termAmounts[`${feeId}_`] || 0);
          // }

          return (
            <Stack minWidth={130} position="relative">
              <TextField
                disabled={
                  (!isSelectedRow && disabled) ||
                  (!isSelectedRow && allAmountCheck) ||
                  (isSelectedRow && termFeeMapped.length === 0)
                  // (isSelectedRow && allAmountCheck && disabled)
                  //  || (isSelectedRow && allAmountCheck) || allAmountCheck
                  // || (isSelectedRow && !allAmountCheck)
                }
                // key={`cell_${row?.feeId}_${item.sectionId}`}
                name="amount"
                type="number"
                variant="outlined"
                size="small"
                placeholder="Enter amount"
                error={!!errMessages[row?.feeId]}
                // helperText={errMessages[row?.feeId]}
                sx={{
                  // backgroundColor: theme.palette.common.white,
                  // backgroundColor: isLight ? theme.palette.common.white : theme.palette.grey[900],
                  borderRadius: 1,
                  '& .MuiInputBase-input.Mui-disabled': {
                    backgroundColor: theme.palette.grey[300],
                    borderRadius: 1,
                    padding: 1,
                    '&:hover': {
                      borderColor: theme.palette.grey[900],
                      // border:2,
                    },
                  },
                  mx: 1,
                  '& .MuiInputBase-input': {
                    borderRadius: 1,
                    padding: 1,
                  },
                }}
                // onClick={() => {
                //   const validTermIds = isSelectedRow
                //     ? termIds.filter((termId: number) => termFeeMapped.some((f: TermAmount) => f.termId === termId))
                //     : termIds.filter((termId: number) => !termFeeMapped.some((f: TermAmount) => f.termId === termId));
                //   validTermIds.forEach((termId: number) => {
                //     handleCellClick(row, termId, totalSum);
                //   });
                // }}
                onChange={(e) => {
                  const { value } = e.target;
                  const numericValue = parseFloat(value);

                  // Determine the valid term IDs based on the selection state
                  const validTermIds = isSelectedRow
                    ? termIds.filter((termId: number) => termFeeMapped.some((f: TermAmount) => f.termId === termId))
                    : termIds.filter((termId: number) => !termFeeMapped.some((f: TermAmount) => f.termId === termId));

                  // Update the term amounts for valid term IDs
                  validTermIds.forEach((termId: number) => {
                    setClickedCells((prevClickedCells) => [...prevClickedCells, { feeId, termId }]);
                    // handleCellClick(row, termId, totalSum);
                    handleAmountChange(e, row, termId, id);
                    setFeeAmounts((prevAmounts) => {
                      const updatedAmounts = { ...prevAmounts };
                      if (numericValue === 0 || Number.isNaN(numericValue)) {
                        if (isSelectedRow && id === 1) {
                          updatedAmounts[`${row?.feeId}_${termId}`] = row?.amount;
                        } else {
                          updatedAmounts[`${row?.feeId}_${termId}`] = '';
                        }
                        // delete updatedAmounts[`${row?.feeId}_${termId}`];
                      } else if (numericValue !== 0 || !Number.isNaN(numericValue)) {
                        updatedAmounts[`${row?.feeId}_${termId}`] = numericValue;
                      } else {
                        updatedAmounts[`${row?.feeId}_${termId}`] = row?.amount;
                      }

                      return updatedAmounts;
                    });
                  });
                  let calAmount;
                  setTermAmounts((prevAmounts) => {
                    const updatedAmounts = { ...prevAmounts };
                    if (numericValue === 0 || Number.isNaN(numericValue)) {
                      updatedAmounts[`${row?.feeId}_`] = '';
                      // individualSaveButtonEnabled[`${row?.feeId}_`] = false;
                      // feeAmounts[`${row?.feeId}_${termId}`] = row?.amount;
                      setIsAutoFocus((prev) => ({
                        ...prev,
                        [row?.feeId]: true,
                      }));
                      setClickedCells((prevClickedCells) => prevClickedCells.filter((f) => f.feeId !== row?.feeId));
                    } else if (numericValue !== 0 || !Number.isNaN(numericValue)) {
                      updatedAmounts[`${row?.feeId}_`] = numericValue;
                      setIsAutoFocus((prev) => ({
                        ...prev,
                        [row?.feeId]: false,
                      }));
                      // feeAmounts[`${row?.feeId}_${termId}`] = numericValue;
                      // Update feeAmounts with the new value

                      if (isSelectedRow) {
                        // Calculate the total new amount based on termFeeMapped
                        calAmount = termFeeMappedLength * numericValue || 0;
                      } else {
                        // Calculate the total new amount based on termAmounts
                        const length = termsLength - termFeeMappedLength || 0;
                        calAmount = length * numericValue + termFeeAmountsSum || 0;
                        console.log('calAmountsss::::----', calAmount);
                      }
                      if (numericValue > row?.amount || calAmount > row?.amount || numericValue === 0) {
                        setCellValidationError(true);
                        seTtotalTermAmount(row?.amount);
                        setErrMessages((prev) => ({
                          ...prev,
                          [row?.feeId]: 'Enter a valid amount',
                        }));
                      } else {
                        setErrMessages((prev) => ({
                          ...prev,
                          [row?.feeId]: '',
                        }));
                      }
                    }
                    return updatedAmounts;

                    // else {
                    //   updatedAmounts[`${row?.feeId}_`] = row?.amount;
                    // }
                  });

                  // setTermAmounts((prev) => ({
                  //   ...prev,
                  //   [`${row?.feeId}_`]: numericValue,
                  // }));

                  console.log('Valid Term IDs:', validTermIds);
                  console.log('feeAmounts::::----', feeAmounts);
                  console.log('termAmounts::::----', termAmounts);
                }}
                // value={termAmounts[`${row?.feeId}_${termIds[0]}`]}
                value={termAmounts[`${row?.feeId}_`] || ''}
                // autoFocus={false}
                // onFocus={(e) => {
                //   const fieldId = `${row?.feeId}_`;
                //   console.log(`Field focused: ${fieldId}, Value: ${e.target.value}`);
                //   // Perform additional logic if needed, e.g., tracking focus state or updating UI
                // }}
                InputProps={{
                  inputProps: {
                    maxLength: 2,
                  },
                  style: {
                    // padding: '0px 5px 0px 0px',
                    margin: '25px 1px',
                    minWidth: 85,
                    color: isSelectedRow ? theme.palette.warning.main : '',
                    // backgroundColor: '#fbf7e6',
                  },
                }}
                FormHelperTextProps={{
                  style: {
                    fontSize: '10px',
                    margin: '0px',
                  },
                }}
              />
              {!!errMessages[row?.feeId] && (
                <Typography
                  position="absolute"
                  bottom={5}
                  minWidth={100}
                  variant="subtitle1"
                  fontSize={10}
                  pl={1.5}
                  color={theme.palette.error.main}
                >
                  {errMessages[row?.feeId]}
                </Typography>
              )}
            </Stack>
          );
        },
      },
    ];

    const headerAndBodyCells = termFee.terms
      ? termFee.terms.map((item: TermFeeDataType, columnIndex: number) => ({
          name: `${item.termId}`,
          renderHeader: () => (
            <Stack minWidth={110} key={item.termId} className="header-color" direction="row" justifyContent="center">
              <Typography variant="subtitle2" fontSize={12}>
                {item.termTitle}
              </Typography>
            </Stack>
          ),
          renderCell: (row: TermFeeDetail, rowIndex: number) => {
            const { feeId } = row;
            const { termId } = item;
            const isCellClicked = clickedCells.some((cell) => cell.feeId === row?.feeId && cell.termId === item.termId);
            const isSelectedRow = selectedRows.includes(row);
            const monthData = row?.termFeeMapped && row?.termFeeMapped.find((data) => data.termId === item.termId);
            // Summing up all amounts in row?.termFeeMapped
            const key = `${row?.feeId}`;
            const enteredAmountsSum = rowSums[key] || 0; // Ensure a default value of 0 if rowSums[key] is undefined

            // Calculate the sum of `termFeeMapped` amounts
            const termFeeAmountsSum = Array.isArray(row?.termFeeMapped)
              ? row?.termFeeMapped.reduce((total, i) => total + i.amount, 0)
              : 0; // Default to 0 if termFeeMapped is null or not an array

            // Add `enteredAmountsSum` to the calculated sum
            const totalSum = termFeeAmountsSum + enteredAmountsSum;

            console.log('Total Sum:', enteredAmountsSum);
            const id = 0;
            if (monthData) {
              const { amount, academicTermId } = monthData;
              return (
                <Stack
                  position="relative"
                  direction="row"
                  alignItems="center"
                  key={`cell_${row?.feeId}_${item.termId}`}
                  // onClick={() => {
                  //   if (!isCellClicked) {
                  //     setClickedCells([...clickedCells, { rowIndex, columnIndex, value: '' }]);
                  //   }
                  // }}
                  // onClick={() => !isSelectedRow && handleCellClick(row, item.termId, totalSum)}
                  className={isSelectedRow ? 'cellActive' : 'cellInActive'}
                  sx={{
                    borderLeft: 3,
                  }}
                  color="#000"
                  gap={2}
                  pl={1}
                  height="80px"
                  minWidth={110}
                >
                  {!isSelectedRow && (
                    <>
                      <Stack direction="row" alignItems="center">
                        <CurrencyRupeeIcon sx={{ fontSize: '14px' }} color="success" />
                        <Typography fontSize={12} variant="subtitle2">
                          {amount}
                        </Typography>
                      </Stack>
                      <Stack position="absolute" top={2} right={2}>
                        <IconButton onClick={() => handleDeleteCell(academicTermId)} size="small">
                          <DeleteIcon sx={{ fontSize: 16 }} />
                        </IconButton>
                      </Stack>
                    </>
                  )}
                  {isSelectedRow && (
                    <>
                      {/* <Typography position="absolute" top={0} right={2} variant="subtitle2" fontSize={10}>
                        Edit
                      </Typography> */}
                      <Stack direction="row" position="relative" alignItems="center" justifyContent="center">
                        <CurrencyRupeeIcon sx={{ fontSize: '14px', fontWeight: 600 }} color="warning" />
                        {/* <input
                          style={{
                            padding: '30px 1px',
                            width: '100%',
                            border: 0,
                            backgroundColor: 'transparent',
                            // fontFamily: ,
                            color: theme.palette.warning.main,
                            outline: 'none',
                          }}
                          type="text"
                          onChange={(e) => handleAmountChange(e, row, item.termId, id)}
                          defaultValue={feeAmounts[`${row?.feeId}_${item.termId}`] || amount}
                        /> */}
                        <TextField
                          type="number"
                          variant="outlined"
                          size="small"
                          sx={{
                            '& fieldset': { border: '1px' },
                          }}
                          inputProps={{
                            maxLength: 6,
                            style: {
                              padding: '30px 0px',
                              minWidth: 70,
                              // fontFamily: ,
                              fontSize: 12,
                              fontWeight: 600,
                              color: theme.palette.warning.main,
                            },
                            // '& input::placeholder': {
                            //   fontSize: '1px',
                            // },
                          }}
                          // placeholder="₹"
                          // onChange={(e) => handleAmountChange(e, row, item.termId, id)}
                          onChange={(e) => handleAmountChange(e, row, item.termId, id)}
                          defaultValue={amount}
                          value={feeAmounts[`${row?.feeId}_${item.termId}`]}
                        />
                        {rowSums[`${row?.feeId}_${item.termId}`] > row?.amount && (
                          <Typography
                            position="absolute"
                            bottom={0}
                            left={4}
                            minWidth={140}
                            variant="subtitle2"
                            fontSize={8}
                            color={theme.palette.error.main}
                          >
                            Enter valid amount
                          </Typography>
                        )}
                      </Stack>
                    </>
                  )}
                </Stack>
              );
            } else {
              // const monthData2 = row?.termFeeMapped && row?.termFeeMapped.find((data) => data.termId === item.termId);
              // if (monthData2) {
              //   const { amount } = monthData2;
              return (
                <Stack
                  alignItems="center"
                  justifyContent="center"
                  key={`cell_${row?.feeId}_${item.termId}`}
                  // color={isSelectedRow ? theme.palette.grey[400] : theme.palette.grey[500]}
                  // bgcolor={isSelectedRow ? theme.palette.grey[50] : ''}
                  className={isSelectedRow ? 'inputCellActive' : 'inputCellInActive'}
                  gap={2}
                  minWidth={110}
                  height="100%"
                >
                  {isCellClicked ? (
                    <Stack pl={1} position="relative" direction="row" alignItems="center" justifyContent="center">
                      <CurrencyRupeeIcon sx={{ fontSize: '14px' }} />
                      <TextField
                        name=""
                        type="number"
                        variant="outlined"
                        size="small"
                        sx={{
                          '& fieldset': { border: '1px' },
                          '& .MuiOutlinedInput-root': {
                            '&:has(> input[data-com-onepassword-filled="light"])': {
                              backgroundColor: 'red',
                            },
                          },
                        }}
                        inputProps={{
                          maxLength: 6,
                          style: {
                            padding: '30px 0px',
                            minWidth: 70,
                            fontSize: 12,
                            fontWeight: 600,
                            // fontFamily: 'sans-serif',
                            color: rowSums[`${row?.feeId}_${item.termId}`] > row?.amount ? 'red' : '',
                          },
                          // '& input::placeholder': {
                          //   fontSize: '1px',
                          // },
                        }}
                        // placeholder="₹"
                        autoFocus={isAutoFocus[`${row?.feeId}`] ?? true}
                        inputRef={(el) => {
                          if (!inputRef.current[`${row?.feeId}_${item.termId}`]) {
                            inputRef.current[`${row?.feeId}_${item.termId}`] = el;
                          }
                        }}
                        onKeyPress={(event) => {
                          if (event.key === 'Enter') {
                            event.preventDefault();

                            // Current cell identification
                            const currentKey = `${row?.feeId}_${item.termId}`;
                            const allKeys = Object.keys(inputRef.current);
                            const currentIndex = allKeys.indexOf(currentKey);
                            // const [fId, tId] = nextKey.split('_').map(Number); // Convert strings to numbers

                            setClickedCells((prevClickedCells) => [...prevClickedCells, { feeId, termId: termId + 1 }]);
                            if (currentIndex !== -1 && currentIndex + 1 < allKeys.length) {
                              const nextKey = allKeys[currentIndex + 1];
                              // Move focus to the next cell

                              // Move focus to the next cell
                              inputRef.current[nextKey]?.focus();
                              console.log('c::::----', clickedCells);

                              // Add the next cell to the clickedCells array if it's not already present
                              // setClickedCells((prevClickedCells) => {
                              //   const isAlreadyClicked = prevClickedCells.some(
                              //     (cell) => cell.fId === fId && cell.tId === tId
                              //   );

                              //   if (!isAlreadyClicked) {
                              //     return [...prevClickedCells, { fId, tId }];
                              //   }
                              //   return prevClickedCells;
                              // });
                            }
                          }
                        }}
                        onChange={(e) => handleAmountChange(e, row, item.termId, id)}
                        // onChange={(e) => debouncedHandleAmountChange(e, row, item.termId, id)}
                        value={feeAmounts[`${row?.feeId}_${item.termId}`]}
                        disabled={isSelectedRow}
                      />

                      {!!cellErrorMessages[`${row?.feeId}_${item.termId}`] && (
                        <Typography
                          position="absolute"
                          bottom={0}
                          left={4}
                          minWidth={140}
                          variant="subtitle2"
                          fontSize={8}
                          color={theme.palette.error.main}
                        >
                          {cellErrorMessages[`${row?.feeId}_${item.termId}`]}
                        </Typography>
                      )}
                    </Stack>
                  ) : (
                    !isCellClicked && (
                      <Stack
                        // onClick={() => {
                        //   const key = `${row?.feeId}_${item.termId}`;
                        //   // Check conditions for setting clicked cells
                        //   if (sumRowAmounts < row?.amount) {
                        //     if (!isSelectedRow || rowSums[key] < row?.amount) {
                        //       setClickedCells((prevClickedCells) => [
                        //         ...prevClickedCells,
                        //         { rowIndex, columnIndex, value: '' },
                        //       ]);
                        //     }
                        //   }
                        // }}
                        onClick={() => !isSelectedRow && handleCellClick(row, item.termId, totalSum)}
                        sx={{
                          width: '100%',
                          height: '100%',
                          transform: 'scale(0.9)',
                          transition: '.1s',
                          '&:hover': {
                            color: !isSelectedRow ? theme.palette.success.main : '',
                            transform: !isSelectedRow ? 'scale(1.2)' : '',
                            transition: !isSelectedRow ? '.1s' : '',
                          },
                        }}
                        direction="row"
                        justifyContent="center"
                        alignItems="center"
                      >
                        <AddIcon />
                      </Stack>
                    )
                  )}
                </Stack>
              );
            }
          },
        }))
      : [];

    const baseColumns2: DataTableColumn<any>[] = [
      // {
      //   name: 'total',
      //   headerLabel: 'Total',
      //   renderCell: (row, index) => (
      //     <Typography px={1} textAlign="start" variant="subtitle1" fontSize={13}>
      //       {calculateTotals(row, index)}
      //     </Typography>
      //   ),
      // },
      {
        name: 'Actions',
        renderHeader: () => (
          <Typography width={110} textAlign="center" className="header-color" variant="subtitle2" fontSize={14}>
            Actions
          </Typography>
        ),
        renderCell: (row) => {
          const isSelectedRow = selectedRows.includes(row);
          const isEmpty = Object.keys(feeAmounts[`${row?.feeId}_`] || {}).length === 0;
          console.log('isEmpty::::----', isEmpty);

          return (
            <Stack minWidth={90} direction="row" alignItems="center" justifyContent="center" gap={1}>
              {
                showSuccessIcon[row?.feeId] === true ? (
                  <Stack direction="row" justifyContent="center" flexDirection="column" alignItems="center">
                    {succesResponse === 'Success' ? (
                      <>
                        <Lottie animationData={Success} loop={false} style={{ width: '30px' }} />
                        <Typography color={theme.palette.success.main} fontSize={7} variant="subtitle2">
                          Saved
                        </Typography>
                      </>
                    ) : (
                      <>
                        <Lottie animationData={Updated} loop={false} style={{ width: '30px' }} />
                        <Typography color={theme.palette.warning.main} fontSize={7} variant="subtitle2">
                          Updated
                        </Typography>
                      </>
                    )}
                  </Stack>
                ) : showErrorIcon[row?.feeId] === true ? (
                  <Stack direction="row" justifyContent="center">
                    <Lottie animationData={Error} loop={false} style={{ width: '30px' }} />
                  </Stack>
                ) : !individualSaveLoading[row?.feeId] === true ? (
                  <IconButton
                    disabled={
                      !individualSaveButtonEnabled[row?.feeId] || saving || rowSums[`${row?.feeId}_`] > row?.amount
                    }
                    size="small"
                    color={isSelectedRow ? 'warning' : 'success'}
                    aria-label=""
                    onClick={() => handleSave(row)}
                  >
                    <SaveIcon fontSize="small" />
                  </IconButton>
                ) : (
                  <Stack direction="row" justifyContent="center" flexDirection="column" alignItems="center">
                    <>
                      <Lottie
                        animationData={isSelectedRow ? UpdateLoading : SaveLoading}
                        loop
                        style={{ width: '20px' }}
                      />
                      <Typography
                        color={isSelectedRow ? theme.palette.warning.main : theme.palette.success.main}
                        fontSize={7}
                        variant="subtitle2"
                      >
                        {isSelectedRow ? 'Updating...' : 'Saving...'}
                      </Typography>
                    </>
                  </Stack>
                )
                // <LoadingButton
                //   loading={individualSaveLoading[row?.feeId]}
                //   onClick={() => handleSave(row)}
                //   variant="contained"
                //   size="small"
                //   color="success"
                //   disabled={!individualSaveButtonEnabled[row?.feeId] || saving}
                //   sx={{ py: 0.5, fontSize: '10px' }}
                // >
                //   {!individualSaveLoading[row?.feeId] ? 'Save' : ''}
                // </LoadingButton>
              }

              <IconButton
                disabled={row?.termFeeMapped === null}
                onClick={() => handleDeleteRow(row)}
                size="small"
                color="error"
                aria-label=""
              >
                <DeleteIcon fontSize="small" />
              </IconButton>
            </Stack>
          );
        },
      },
    ];

    return [...baseColumns, ...headerAndBodyCells, ...baseColumns2];
  }, [
    theme,
    handleSave,
    clickedCells,
    showSuccessIcon,
    saving,
    feeAmounts,
    termFee,
    individualSaveButtonEnabled,
    showErrorIcon,
    succesResponse,
    individualSaveLoading,
    handleAmountChange,
    handleDeleteCell,
    handleDeleteRow,
    selectedRows,
    errMessages,
    rowSums,
    handleCellClick,
    termAmounts,
    isAutoFocus,cellErrorMessages
  ]);

  // Check if any cell in the table has a value other than 0 or ''
  const anyCellNonZeroOrEmpty = Object.values(feeAmounts).some((value) => value !== 0 && value !== '');
  const getRowKey = useCallback((row: any) => row?.feeId, []);
  return (
    <Page title="Fees Collection">
      <FeeDateSettingsRoot>
        <Card className="Card" elevation={1} sx={{ pt: { xs: 1, md: 1 } }}>
          <Box display="flex" pb={0.5} justifyContent="space-between" alignItems="center" sx={{ px: { xs: 3, md: 5 } }}>
            <Stack gap={2} direction="row" alignItems="center">
              <Typography variant="h6" fontSize={17}>
                Fee Date Settings
              </Typography>
            </Stack>
            <Stack direction="row" alignItems="center" gap={1}>
              {selectedRows.length > 0 && (
                <Tooltip title="Delete">
                  <IconButton
                    sx={{ visibility: { xs: 'hidden', sm: 'visible' } }}
                    aria-label="delete"
                    color="error"
                    onClick={handleDeleteMultiple}
                  >
                    <DeleteIcon />
                  </IconButton>
                </Tooltip>
              )}
              {showFilter === false ? (
                <Tooltip title="Search">
                  <IconButton
                    aria-label="delete"
                    color="primary"
                    sx={{ ml: 1 }}
                    onClick={() => setShowFilter((x) => !x)}
                  >
                    <SearchIcon />
                  </IconButton>
                </Tooltip>
              ) : (
                <IconButton aria-label="delete" color="primary" onClick={() => setShowFilter((x) => !x)}>
                  <IoIosArrowUp />
                </IconButton>
              )}
              <Button sx={{ borderRadius: '20px' }} variant="outlined" size="small" onClick={toggleDrawerOpen}>
                <MdAdd size="20px" /> Create
              </Button>
            </Stack>
          </Box>

          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter} sx={{ px: { xs: 3, md: 5 } }}>
              <form noValidate onReset={handleReset}>
                <Grid pb={4} container spacing={2} alignItems="end">
                  <Grid item xxl={2} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Select
                        labelId="academicYearFilter"
                        id="academicYearFilterSelect"
                        value={academicYearFilter.toString()}
                        onChange={handleYearChange}
                        placeholder="Select Year"
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Year
                        </MenuItem>
                        {YearData.map((opt) => (
                          <MenuItem key={opt.accademicId} value={opt.accademicId}>
                            {opt.accademicTime}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Select Type
                      </Typography>
                      <Select
                        labelId="feeTypeFilter"
                        id="feeTypeFilterSelect"
                        value={feeTypeFilter.toString()}
                        onChange={handleFeeTypeChange}
                        placeholder="Select Type"
                      >
                        <MenuItem sx={{ display: 'none' }} value={0}>
                          Select Type
                        </MenuItem>
                        {FEE_TYPE_ID_OPTIONS.map((opt) => (
                          <MenuItem key={opt.id} value={opt.id}>
                            {opt.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Section
                      </Typography>
                      <Select
                        labelId="classSectionsFilter"
                        id="classSectionsFilterSelect"
                        value={classSectionsFilter.toString()}
                        onChange={handleClassSectionChange}
                        placeholder="Select Section"
                        MenuProps={{
                          PaperProps: {
                            style: {
                              maxHeight: '250px', // Adjust the value to your desired height
                            },
                          },
                        }}
                      >
                        <MenuItem sx={{ display: 'none' }} value={0}>
                          Select Section
                        </MenuItem>
                        {ClassSectionsData.map((opt) => (
                          <MenuItem key={opt.sectionId} value={opt.sectionId}>
                            {opt.sectionName}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto">
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button type="reset" variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>
            {termFeeDetails.length !== 0 && (
              <Box pr={1} mb={2} mt={!showFilter ? 2 : 0} display="flex" justifyContent="end">
                <LoadingButton
                  loading={savingAll}
                  onClick={handleAllSave}
                  disabled={amountEnter === 0 || amountEnter === '' || !anyCellNonZeroOrEmpty}
                  color={selectedRows.length > 0 ? 'warning' : 'success'}
                  startIcon={<SaveIcon fontSize="small" />}
                  size="small"
                  variant="contained"
                  sx={{ py: 0.2, px: 1, fontSize: 12 }}
                >
                  {selectedRows.length > 0 ? 'Update All ' : 'Save All'}
                </LoadingButton>
              </Box>
            )}
            <Paper className="card-table-container">
              {feeDateSettingsStatus === 'loading' && termFeeDetails.length === 0 ? (
                <Table>
                  <TableBody>
                    {[...Array(rowsPerPage)].map((_, rowIndex: number) => (
                      <TableRow key={rowIndex}>
                        {FeeDateSettingsListColumns.map((column, colIndex) => (
                          <TableCell key={colIndex}>
                            <Skeleton variant="text" height={20} />
                          </TableCell>
                        ))}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                // <VirtuosoTable
                <DTVirtuoso
                  arrayState={setClickedCells}
                  setFeeAmounts={setFeeAmounts}
                  setTermAmounts={setTermAmounts}
                  setErrMessages={setErrMessages}
                  // isDisabled={termFeeDetails.length === 0}
                  columns={FeeDateSettingsListColumns}
                  tableStyles={{ minWidth: { xs: '1200px' } }}
                  ShowCheckBox
                  setSelectedRows={setSelectedRows}
                  selectedRows={selectedRows}
                  data={termFeeDetails}
                  getRowKey={getRowKey}
                  fetchStatus={feeDateSettingsStatus}
                  // fetchStatus="success"
                  showHorizontalScroll
                  // PaginationProps={pageProps}
                  // allowPagination
                  footerColumn={
                    <TableRow
                      className="footer_row"
                      style={{
                        zIndex: 11,
                        position: 'sticky',
                        bottom: 0,
                        backgroundColor: isLight ? theme.palette.info.light : theme.palette.grey[900],
                      }}
                    >
                      <TableCell
                        sx={{
                          position: 'sticky',
                          left: 0,
                          zIndex: 11,
                          backgroundColor: isLight ? theme.palette.info.light : theme.palette.grey[900],
                        }}
                        colSpan={2}
                      >
                        Monthly Total
                      </TableCell>
                      {/* Empty cell for alignment */}
                      <TableCell
                        sx={{
                          position: 'sticky',
                          left: 193,
                          zIndex: 11,
                          backgroundColor: isLight ? theme.palette.info.light : theme.palette.grey[900],
                        }}
                      >
                        {totalAnnualAmount}
                      </TableCell>
                      <TableCell
                        sx={{
                          position: 'sticky',
                          left: 305,
                          zIndex: 11,
                          backgroundColor: isLight ? theme.palette.info.light : theme.palette.grey[900],
                        }}
                      >
                        {' '}
                      </TableCell>
                      {termFee.terms
                        ? termFee.terms.map((item: TermFeeDataType) => (
                            <TableCell key={item.termId}>{calculateMonthlyTotals(item)}</TableCell>
                          ))
                        : []}
                      {/* <TableCell> {totalAmount}</TableCell> */}
                      <TableCell
                        sx={{
                          position: 'sticky',
                          right: 0,
                          zIndex: 11,
                          backgroundColor: isLight ? theme.palette.info.light : theme.palette.grey[900],
                        }}
                      >
                        {' '}
                      </TableCell>
                    </TableRow>
                  }
                  // ShowCheckBox
                />
              )}
            </Paper>
          </div>
        </Card>
        {/* <TestComponent /> */}
        {/* <TestScrollTable /> */}
      </FeeDateSettingsRoot>
      {/* <TemporaryDrawer
        onClose={toggleDrawerClose}
        state={drawerOpen}
        DrawerContent={<EditList onClose={toggleDrawerClose} open={handleClickOpen} />}
      /> */}
      <TemporaryDrawer
        onClose={toggleDrawerClose}
        state={drawerOpen}
        Title="Create Term Title"
        DrawerContent={<CreateEditTermFeeTitleForm onSave={handleTermTitleSave} onCancel={toggleDrawerClose} />}
      />
      {/* <Popup size="md" state={popup} onClose={handleClickClose} popupContent={<EditedTable />} /> */}
      <Popup
        size="xs"
        state={deleteFee}
        onClose={() => setDeleteFee(false)}
        popupContent={<DeleteMessage icon={DeleteAnimatedIcon} message="Are you sure want to delete?" />}
      />
      <Snackbar
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
        open={cellValidationError}
        autoHideDuration={3000}
        onClose={() => setCellValidationError(false)}
        // message="Receipt number already exist"
      >
        <Alert severity="error" variant="filled">
          <Typography variant="subtitle2" display="flex" alignItems="center">
            Maximum Amount <CurrencyRupeeIcon sx={{ fontSize: 15 }} />
            {totalTermAmount}
          </Typography>
        </Alert>
      </Snackbar>
    </Page>
  );
}
