/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import {
  Autocomplete,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Box,
  Avatar,
  IconButton,
  Collapse,
} from '@mui/material';
import styled from 'styled-components';
import { YEAR_SELECT } from '@/config/Selection';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import SearchIcon from '@mui/icons-material/Search';

const MembersListRoot = styled.div`
  width: 100%;
  padding: 0.5rem 1.5rem;
  .Card {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    height: calc(100vh - 200px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }
    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 10px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

export const data = [
  {
    slNo: 1,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'Alex',
    description: 'Welcome to PassDaily - eConnect for Schools and College',
    group: 'Welcome to PassDaily',
  },
  {
    slNo: 1,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'Alex',
    description: 'Welcome to PassDaily - eConnect for Schools and College',
    group: 'Welcome to PassDaily',
  },
  {
    slNo: 1,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'Alex',
    description: 'Welcome to PassDaily - eConnect for Schools and College',
    group: 'Welcome to PassDaily',
  },
  {
    slNo: 1,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'Alex',
    description: 'Welcome to PassDaily - eConnect for Schools and College',
    group: 'Welcome to PassDaily',
  },
  {
    slNo: 1,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'Alex',
    description: 'Welcome to PassDaily - eConnect for Schools and College',
    group: 'Welcome to PassDaily',
  },
  {
    slNo: 1,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'Alex',
    description: 'Welcome to PassDaily - eConnect for Schools and College',
    group: 'Welcome to PassDaily',
  },
  {
    slNo: 1,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'Alex',
    description: 'Welcome to PassDaily - eConnect for Schools and College',
    group: 'Welcome to PassDaily',
  },
  {
    slNo: 2,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'Mathew',
    description: 'Keep your password safe',
    group: 'Password',
  },
  {
    slNo: 3,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'Peter',
    description: 'PassDaily Team Welcomes you to the eConnect tool for Schools... - eConnect for Schools and College',
    group: 'Message',
  },
  {
    slNo: 4,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'Alex Mic',
    description: 'Welcome to PassDaily - eConnect for Schools and College',
    group: 'PassDaily',
  },
  {
    slNo: 5,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'john',
    description: 'Keep your password safe',
    group: 'Password',
  },
  {
    slNo: 6,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'Micheal',
    description: 'PassDaily Team Welcomes you to the eConnect tool for Schools... - eConnect for Schools and College',
    group: 'Message',
  },
  {
    slNo: 7,
    class: 'VII-D',
    academicYear: '2023-2024',
    studentName: 'jack',
    description: 'Welcome to PassDaily - eConnect for Schools and College',
    group: 'Welcome',
  },
];

function MembersList() {
  const [showFilter, setShowFilter] = useState(false);

  const getRowKey = useCallback((row: any) => row.examId, []);
  const AuthorizeKeywordsColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'slNo',
        dataKey: 'slNo',
        headerLabel: 'Sl.No',
        sortable: true,
      },
      {
        name: 'studentName',
        headerLabel: 'Student Name',
        renderCell: (row) => {
          return (
            <Stack direction="row" gap={1} alignItems="center">
              <Avatar alt="" src="/static/images/avatar/1.jpg" />
              <Typography variant="subtitle2">{row.studentName}</Typography>
            </Stack>
          );
        },
      },
      {
        name: 'group',
        dataKey: 'group',
        headerLabel: 'Group',
      },
      {
        name: 'class',
        dataKey: 'class',
        headerLabel: 'Class',
      },
      {
        name: 'academicYear',
        dataKey: 'academicYear',
        headerLabel: 'AcademicYear',
      },
    ],
    []
  );
  return (
    <MembersListRoot>
      <Box className="Card">
        <Box sx={{ flexShrink: 0 }}>
          <IconButton
            aria-label="delete"
            color="primary"
            sx={{ mr: 2, position: 'absolute', top: '19px', right: '65px' }}
            onClick={() => setShowFilter((x) => !x)}
          >
            <SearchIcon />
          </IconButton>
        </Box>
        <Divider />
        <div className="card-main-body">
          <Collapse in={showFilter}>
            <form noValidate>
              <Grid pb={4} container spacing={3}>
                <Grid item md={4} xs={12}>
                  <Typography variant="h6" fontSize={14}>
                    Academic Year
                  </Typography>
                  <Autocomplete
                    options={YEAR_SELECT}
                    renderInput={(params) => <TextField {...params} placeholder="Select" />}
                  />
                </Grid>
                <Grid item md={4} xs={12}>
                  <Typography variant="h6" fontSize={14}>
                    Class
                  </Typography>
                  <Autocomplete
                    options={YEAR_SELECT}
                    renderInput={(params) => <TextField {...params} placeholder="Select" />}
                  />
                </Grid>
                <Grid item md={1} xs={12}>
                  <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                    <Button variant="contained" color="secondary" fullWidth>
                      Reset
                    </Button>
                    {/* <Button variant="contained" color="primary" fullWidth>
                      Search
                    </Button> */}
                  </Stack>
                </Grid>
              </Grid>
            </form>
          </Collapse>

          <Paper className="card-table-container" sx={{ marginTop: '12px' }}>
            <DataTable
              ShowCheckBox
              columns={AuthorizeKeywordsColumns}
              data={data}
              getRowKey={getRowKey}
              fetchStatus="success"
            />
          </Paper>
          <Box mt={1.2}>
            <form noValidate>
              <Grid container spacing={3}>
                <Grid item md={4} xs={12}>
                  <Typography variant="h6" fontSize={14}>
                    Group
                  </Typography>
                  <Autocomplete
                    options={YEAR_SELECT}
                    renderInput={(params) => <TextField {...params} placeholder="Select" />}
                  />
                </Grid>
                <Grid item md={4} xs={12}>
                  <Typography variant="h6" fontSize={14}>
                    Academic Year
                  </Typography>
                  <Autocomplete
                    options={YEAR_SELECT}
                    renderInput={(params) => <TextField {...params} placeholder="Select" />}
                  />
                </Grid>
                <Grid item md={1} xs={12}>
                  <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                    <Button variant="contained" color="primary" fullWidth>
                      Group
                    </Button>
                  </Stack>
                </Grid>
              </Grid>
            </form>
          </Box>
        </div>
      </Box>
    </MembersListRoot>
  );
}

export default MembersList;
