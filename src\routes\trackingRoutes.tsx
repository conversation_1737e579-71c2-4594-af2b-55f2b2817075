import Loadable from '@/components/shared/Loadable';
import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';

const VehicleTracking = Loadable(lazy(() => import('@/pages/VehicleTracking')));
const NewVehicle = Loadable(lazy(() => import('@/features/VehicleTracking/NewVehicle')));
const VehicleList = Loadable(lazy(() => import('@/features/VehicleTracking/VehicleList/VehicleList')));

export const trackingRoutes: RouteObject[] = [
  {
    path: '/vehicle-tracking',
    element: <VehicleTracking />,
    children: [
      {
        path: 'new',
        element: <NewVehicle />,
      },
      {
        path: 'list',
        element: <VehicleList />,
      },
    ],
  },
];
