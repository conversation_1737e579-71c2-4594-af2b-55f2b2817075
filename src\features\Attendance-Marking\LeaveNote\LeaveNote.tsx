/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Avatar,
  Typography,
  Card,
} from '@mui/material';
import styled, { useTheme } from 'styled-components';
import { lnrows } from '@/config/TableData';
import { ApprovedPopup } from '@/components/shared/Popup/ApprovedMessage';
import { CLASS_SELECT, YEAR_SELECT } from '@/config/Selection';
import Popup from '@/components/shared/Popup/Popup';
import DateSelect from '@/components/shared/Selections/DateSelect';
import { View } from './View';

const LeaveNoteRoot = styled.div`
  padding: 1rem;
  .Card {
    height: calc(100vh - 160px);
    @media screen and (max-width: 1199.5px) {
      height: 100%;
    }
  }
`;

function LeaveNote() {
  const theme = useTheme();
  const [popupView, setPopupView] = React.useState(false);
  const [approved, setApproved] = React.useState(false);
  const [data, setData] = React.useState();
  const [notApproved, setNotApproved] = React.useState(false);

  const handleClickOpenView = (datas: any) => {
    setPopupView(true);
    setData(datas);
    console.log('data', data);
  };
  const handleClickCloseView = () => setPopupView(false);

  const handleClickApproved = () => {
    setPopupView(false);
    setApproved(true);
  };

  const handleCloseApproved = () => setApproved(false);
  const handleClickNotApproved = () => {
    setPopupView(false);
    setNotApproved(true);
  };
  const handleCloseNotApproved = () => setNotApproved(false);

  return (
    <Page title="Detailed Class Marking">
      <LeaveNoteRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Typography variant="h6" fontSize={17}>
            Leave Note
          </Typography>
          <Divider />
          <Grid pb={4} pt={2} container spacing={3}>
            <Grid item lg={2.4} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Select Year
              </Typography>
              <Autocomplete
                options={YEAR_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select" />}
              />
            </Grid>
            <Grid item lg={2.4} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Select Class
              </Typography>
              <Autocomplete
                options={CLASS_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select" />}
              />
            </Grid>
            <Grid item lg={2.4} xs={12}>
              <Typography variant="h6" fontSize={14}>
                From
              </Typography>
              <DateSelect />
            </Grid>
            <Grid item lg={2.4} xs={12}>
              <Typography variant="h6" fontSize={14}>
                To
              </Typography>
              <DateSelect />
            </Grid>
            <Grid item lg={2.4} xs={12}>
              <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                <Button variant="contained" color="secondary" fullWidth>
                  Reset
                </Button>
                <Button variant="contained" color="primary" fullWidth>
                  Search
                </Button>
              </Stack>
            </Grid>
          </Grid>

          <Box>
            <Paper
              sx={{
                border: `1px solid #e8e8e9`,
                width: '100%',
                height: '100%',
                overflow: 'auto',
                '&::-webkit-scrollbar': {
                  width: 0,
                },
              }}
            >
              <TableContainer
                sx={{
                  height: 'calc(100vh - 340px)',
                  width: { xs: '800px', md: '100%' },
                  '&::-webkit-scrollbar': {
                    width: 0,
                  },
                }}
              >
                <Table stickyHeader aria-label="simple table">
                  <TableHead>
                    <TableRow>
                      <TableCell>Roll.No</TableCell>
                      <TableCell>Student Name</TableCell>
                      <TableCell>Class</TableCell>
                      <TableCell>Subject</TableCell>
                      <TableCell>Desription</TableCell>
                      <TableCell>Submitted Date</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Option</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {lnrows?.map((lnrow) => (
                      <TableRow hover key={lnrow.id}>
                        <TableCell>{lnrow.id}</TableCell>
                        <TableCell>
                          <Box display="flex" alignItems="center" gap={1}>
                            <Avatar src="" />
                            <span>{lnrow.name}</span>
                          </Box>
                        </TableCell>
                        <TableCell>{lnrow.class}</TableCell>
                        <TableCell>{lnrow.subject}</TableCell>
                        <TableCell>{lnrow.description}</TableCell>
                        <TableCell>12/02/2023</TableCell>
                        <TableCell>
                          <Paper
                            sx={{
                              border: '1px solid ',
                              display: 'flex',
                              justifyContent: 'center',
                              p: 0.5,
                              maxWidth: 85,
                              borderRadius: '20px',
                              backgroundColor: theme.palette.primary.lighter,
                              color: theme.palette.primary.main,
                            }}
                          >
                            Pending
                          </Paper>
                        </TableCell>
                        <TableCell>
                          <Button
                            onClick={() => handleClickOpenView(lnrow)}
                            variant="contained"
                            size="small"
                            color="secondary"
                          >
                            view
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Paper>
          </Box>
        </Card>
      </LeaveNoteRoot>
      <Popup
        size="md"
        state={popupView}
        onClose={handleClickCloseView}
        popupContent={
          <View
            datadummy={data}
            setPopupView={setPopupView}
            onClick={handleClickApproved}
            onClick1={handleClickApproved}
            onClick2={handleClickNotApproved}
          />
        }
      />
      <Popup
        size="xs"
        state={approved}
        onClose={handleCloseApproved}
        popupContent={<ApprovedPopup approved="Approved" />}
      />
      <Popup
        size="xs"
        state={notApproved}
        onClose={handleCloseNotApproved}
        popupContent={<ApprovedPopup approved="Not Approved" />}
      />
    </Page>
  );
}

export default LeaveNote;
