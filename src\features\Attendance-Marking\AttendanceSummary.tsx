/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/alt-text */
import Page from '@/components/shared/Page';
import {
  Divider,
  Grid,
  Paper,
  Stack,
  Button,
  Box,
  Avatar,
  Typography,
  Card,
  Tooltip,
  IconButton,
  MenuItem,
  Collapse,
  FormControl,
  useTheme,
  SelectChangeEvent,
  Select,
} from '@mui/material';
import styled from 'styled-components';
import Excel from '@/assets/attendance/Excel.svg';
import Pdf from '@/assets/attendance/PDF.svg';
import SearchIcon from '@mui/icons-material/Search';
import { MONTH_SELECT } from '@/config/Selection';
import React, { useCallback, useMemo, useState } from 'react';
import useSettings from '@/hooks/useSettings';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import useAuth from '@/hooks/useAuth';
import {
  getAttendanceSummaryReportData,
  getAttendanceSummaryReportStatus,
  getClassData,
  getYearData,
  getYearStatus,
} from '@/config/storeSelectors';
import { useAppSelector } from '@/hooks/useAppSelector';
import { AttendanceSummaryReportInfo, AttendanceSummaryReportRequest } from '@/types/AttendanceMarking';
import { fetchAttendanceSummaryReport } from '@/store/AttendanceMarking/attendanceMarking.thunks';
import { fetchClassList, fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import * as XLSX from 'xlsx';
import { jsPDF } from 'jspdf';
import 'jspdf-autotable';
import AnimatedProgressBar from '@/components/shared/RND/ProgressBarAnimation';

const AttendanceSummaryRoot = styled.div`
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 576px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 100px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid ${(props) => props.theme.palette.grey[200]};
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
        /* .MuiTableCell-root:first-child {
          padding: 5px;
        } */
      }
    }
  }
`;

// const getProgressColor = (progress: number) => {
//   switch (true) {
//     case progress >= 0 && progress < 20:
//       return '#ff0000'; // Red
//     case progress >= 20 && progress < 40:
//       return '#ff8000'; // Orange
//     case progress >= 40 && progress < 60:
//       return '#ffff00'; // Yellow
//     case progress >= 60 && progress < 80:
//       return '#008000'; // Green
//     case progress >= 80:
//       return '#0000ff'; // Blue
//     default:
//       return '#000000'; // Fallback color (should not be used)
//   }
// };

// Styled progress bar
// const BorderLinearProgress = styled(LinearProgress)<{ progress: number }>(({ theme, progress }) => ({
//   height: 10,
//   borderRadius: 5,
//   [`&.${linearProgressClasses.colorPrimary}`]: {
//     backgroundColor: theme.palette.grey[theme.palette.mode === 'light' ? 300 : 700],
//   },
//   [`& .${linearProgressClasses.bar}`]: {
//     borderRadius: 5,
//     backgroundColor: getProgressColor(progress),
//   },
// }));

// // Component for the progress bar with label
// function LinearProgressWithLabel({ value }: { value: number }) {
//   return (
//     <Box sx={{ display: 'flex', alignItems: 'center' }}>
//       <Box sx={{ width: '100%', mr: 1 }}>
//         <BorderLinearProgress variant="determinate" value={value} progress={value} />
//       </Box>
//       <Box sx={{ minWidth: 35 }}>
//         <Typography variant="body2" fontSize={12}>{`${value}%`}</Typography>
//       </Box>
//     </Box>
//   );
// }

function AttendanceSummary() {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const dispatch = useAppDispatch();
  const { user } = useAuth();
  const adminId: number = user ? user.accountId : 0;
  const YearData = useAppSelector(getYearData);
  const ClassData = useAppSelector(getClassData);
  const YearStatus = useAppSelector(getYearStatus);
  const AttendanceSummaryReportStatus = useAppSelector(getAttendanceSummaryReportStatus);
  const defualtYear = YearData[0]?.accademicId || 0;

  const [showFilter, setShowFilter] = useState(true);
  const [academicYearFilter, setAcademicYearFilter] = useState(0);
  const [academicTimeYearFilter, setAcademicTimeYearFilter] = useState('');
  const [classFilter, setClassFilter] = useState(-1);
  const [monthFilter, setMonthFilter] = useState(0);
  const [attendanceSummaryData, setAttendanceSummaryData] = useState<AttendanceSummaryReportInfo[]>([]);

  const currentAttendanceSummaryRequest: AttendanceSummaryReportRequest = React.useMemo(
    () => ({
      academicId: academicYearFilter,
      adminId,
      academicTime: academicTimeYearFilter,
      classId: classFilter,
      month: 0,
    }),
    [academicYearFilter, adminId, classFilter, academicTimeYearFilter]
  );

  const loadAttendanceSummary = React.useCallback(
    async (request: AttendanceSummaryReportRequest) => {
      try {
        const data = await dispatch(fetchAttendanceSummaryReport(request)).unwrap();
        setAttendanceSummaryData(data);
      } catch (error) {
        console.error('Error loading list:', error);
      }
    },
    [dispatch]
  );

  React.useEffect(() => {
    if (YearStatus === 'idle') {
      setAcademicYearFilter(defualtYear);
    }
    if (AttendanceSummaryReportStatus === 'idle') {
      dispatch(fetchYearList(adminId));
      dispatch(fetchClassList(adminId));
      loadAttendanceSummary(currentAttendanceSummaryRequest);
    }
  }, [
    dispatch,
    adminId,
    currentAttendanceSummaryRequest,
    loadAttendanceSummary,
    YearStatus,
    AttendanceSummaryReportStatus,
    defualtYear,
  ]);

  const handleYearChange = (e: SelectChangeEvent) => {
    const selectedAcademicId = e.target.value;
    setAcademicYearFilter(parseInt(selectedAcademicId, 10));
    const selectedAccademicTime =
      YearData.find((x) => x.accademicId === parseInt(selectedAcademicId, 10))?.accademicTime || '';
    setAcademicTimeYearFilter(selectedAccademicTime);
    loadAttendanceSummary({
      ...currentAttendanceSummaryRequest,
      academicId: parseInt(selectedAcademicId, 10),
      academicTime: selectedAccademicTime,
    });
  };
  const handleClassChange = (e: SelectChangeEvent) => {
    const selectedClass = parseInt(e.target.value, 10);
    if (selectedClass) {
      setClassFilter(selectedClass);
    }
    loadAttendanceSummary({
      ...currentAttendanceSummaryRequest,
      classId: selectedClass || 0,
    });
  };
  const handleMonthChange = (e: SelectChangeEvent) => {
    const selectedMonth = parseInt(e.target.value, 10);
    if (selectedMonth) {
      setMonthFilter(selectedMonth);
    }
    loadAttendanceSummary({
      ...currentAttendanceSummaryRequest,
      month: selectedMonth || 0,
    });
  };

  const handleReset = React.useCallback(
    (e: any) => {
      e.preventDefault();
      setClassFilter(-1);
      setAcademicTimeYearFilter('');
      setAcademicYearFilter(0);
      setMonthFilter(0);
      loadAttendanceSummary({
        academicId: 0,
        adminId,
        academicTime: '',
        classId: -1,
        month: 0,
      });
    },
    [loadAttendanceSummary, adminId]
  );

  const AttendanceSummaryReportColumns: DataTableColumn<AttendanceSummaryReportInfo>[] = useMemo(
    () => [
      {
        name: 'rollNo',
        headerLabel: 'Roll No',
        dataKey: 'rollNo',
      },
      {
        name: 'studentName',
        headerLabel: 'Student Name',
        renderCell: (row) => {
          return (
            <Stack width={200} direction="row" gap={1} alignItems="center">
              <Avatar alt="" src="" />
              <Typography variant="subtitle2">{row.studentName}</Typography>
            </Stack>
          );
        },
      },
      {
        name: 'totalWorking',
        headerLabel: 'Total Working Day',
        dataKey: 'totalWorking',
      },
      {
        name: 'totalPresent',
        headerLabel: 'Total Present',
        dataKey: 'totalPresent',
      },
      {
        name: 'totalAbsent',
        headerLabel: 'Total Absent',
        dataKey: 'totalAbsent',
      },
      {
        name: 'attendanceTaken',
        headerLabel: 'Total Attendance Taken',
        dataKey: 'attendanceTaken',
      },
      {
        name: 'percentage',
        headerLabel: 'Percentage',
        renderCell: (row: AttendanceSummaryReportInfo, index: number) => {
          const delay = (index + 1) * 50; // Each row gets an increasing delay (e.g., 500ms, 1000ms, 1500ms, etc.)
          return (
            <Stack width={200} spacing={1}>
              <AnimatedProgressBar targetPercentage={row.percentage} delay={delay} />
            </Stack>
          );
        },
      },
    ],
    []
  );

  const handleDownloadExcel = useCallback(() => {
    const worksheet = XLSX.utils.json_to_sheet(attendanceSummaryData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Attendance Summary');
    XLSX.writeFile(workbook, 'Attendance_Summary.xlsx');
  }, [attendanceSummaryData]);

  const handleDownloadPDF = useCallback(() => {
    const doc = new jsPDF();

    // Define table columns
    const tableColumn = AttendanceSummaryReportColumns.map((col) => col.headerLabel);

    // Define table rows
    const tableRows = attendanceSummaryData.map((row) => [
      row.rollNo,
      row.studentName, // Directly access the student's name
      row.totalWorking,
      row.totalPresent,
      row.totalAbsent,
      row.attendanceTaken,
      `${Math.round(row.percentage)}%`, // Percentage with formatting
    ]);

    // Add a title
    doc.setFontSize(16);
    // doc.text('Attendance Summary Report', 14, 20);

    // Add a border
    const pageWidth = doc.internal.pageSize.getWidth();
    const pageHeight = doc.internal.pageSize.getHeight();

    // Draw a border around the entire page
    doc.setDrawColor(0); // Black color for the border
    doc.setLineWidth(0.2); // Border thickness
    doc.rect(10, 10, pageWidth - 20, pageHeight - 20, 'S'); // (x, y, width, height, style)

    // Add table
    doc.autoTable({
      head: [tableColumn],
      body: tableRows,
      startY: 15,
      theme: 'grid',
      styles: { fontSize: 10 }, // Adjust font size
      headStyles: { fillColor: '#F85A00', textColor: '#fff', fontSize: 8 }, // Black header with white text
    });

    // Add student images as needed
    attendanceSummaryData.forEach((row, index) => {
      if (row.studentImage) {
        const imageYPosition = 30 + index * 10; // Adjust position based on the row index
        doc.addImage(row.studentImage, 'JPEG', 180, imageYPosition, 10, 10); // Example dimensions
      }
    });

    // Save the PDF
    doc.save('Attendance_Summary.pdf');
  }, [AttendanceSummaryReportColumns, attendanceSummaryData]);

  const getRowKey = useCallback((row: AttendanceSummaryReportInfo, index: number) => index, []);
  return (
    <Page title="Attendance Summary">
      <AttendanceSummaryRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack pb={1} direction="row" justifyContent="space-between" alignItems="center" flexWrap="wrap">
            <Stack direction="row" alignItems="center" justifyContent="space-between" flex={1} whiteSpace="nowrap">
              <Typography variant="h6" fontSize={17} width="100%">
                Attendance Summary
              </Typography>
              <Tooltip title="Search">
                <IconButton aria-label="" color="primary" sx={{ mr: 1 }} onClick={() => setShowFilter((x) => !x)}>
                  <SearchIcon />
                </IconButton>
              </Tooltip>
            </Stack>
          </Stack>
          <Stack>
            <Divider />
          </Stack>
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={5} pt={1} container spacing={2}>
                  <Grid item xxl={2} xl={2.5} lg={2.5} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Select
                        labelId="academicYearFilter"
                        id="academicYearFilterSelect"
                        value={academicYearFilter?.toString()}
                        // defaultValue={YearData[0]?.accademicId}
                        onChange={handleYearChange}
                        placeholder="Select Year"
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Year
                        </MenuItem>
                        {YearData.map((opt) => (
                          <MenuItem key={opt.accademicId} value={opt.accademicId}>
                            {opt.accademicTime}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={2.5} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Select Class
                      </Typography>
                      <Select
                        labelId="academicYearFilter"
                        id="academicYearFilterSelect"
                        value={classFilter?.toString()}
                        // defaultValue={YearData[0]?.accademicId}
                        onChange={handleClassChange}
                        placeholder="Select Class"
                        MenuProps={{
                          PaperProps: {
                            style: {
                              maxHeight: '250px', // Adjust the value to your desired height
                            },
                          },
                        }}
                      >
                        <MenuItem value={-1}>Select All</MenuItem>
                        {ClassData.map((opt) => (
                          <MenuItem key={opt.classId} value={opt.classId}>
                            {opt.className}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={2.5} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Select Month
                      </Typography>
                      <Select
                        labelId="monthFilterFilter"
                        id="monthFilterSelect"
                        value={monthFilter?.toString()}
                        // defaultValue={YearData[0]?.accademicId}
                        onChange={handleMonthChange}
                        placeholder="Select Month"
                      >
                        <MenuItem value={0}>Select Month</MenuItem>
                        {MONTH_SELECT.map((opt) => (
                          <MenuItem key={opt.id} value={opt.id}>
                            {opt.month}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item sm={1} xs={12}>
                    <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, lg: 3.79 } }}>
                      <Button variant="contained" color="secondary" fullWidth onClick={handleReset}>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Paper className="card-table-container" sx={{mt:'18px'}}>
              <DataTable
                showHorizontalScroll
                columns={AttendanceSummaryReportColumns}
                tableStyles={{ minWidth: { xs: '1100px' } }}
                data={attendanceSummaryData}
                getRowKey={getRowKey}
                fetchStatus={AttendanceSummaryReportStatus}
                // fetchStatus="success"
              />
            </Paper>
          </div>
          <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 2 }}>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="info" sx={{ gap: 1 }} size="small" onClick={handleDownloadPDF}>
                <img className="icon" src={Pdf} />
                <Typography color="white" fontSize={12}>
                  Download PDF
                </Typography>
              </Button>
              <Button variant="contained" color="success" sx={{ gap: 1 }} size="small" onClick={handleDownloadExcel}>
                <img className="icon" src={Excel} />
                <Typography color="white" fontSize={12}>
                  Download Excel
                </Typography>
              </Button>
            </Stack>
          </Box>
        </Card>
      </AttendanceSummaryRoot>
    </Page>
  );
}

export default AttendanceSummary;
