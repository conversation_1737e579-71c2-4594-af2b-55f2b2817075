import React, { useContext } from 'react';
import dayjs from 'dayjs';
import { Button, ButtonGroup, Typography, Box, IconButton, AppBar, Tool<PERSON>, Stack } from '@mui/material';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import CalendarContext from '@/contexts/CalendarContext';
import useSettings from '@/hooks/useSettings';

export default function CalendarHeader() {
  const isLight = useSettings().themeMode === 'light';
  const { monthIndex, setMonthIndex } = useContext(CalendarContext);

  const handlePrevMonth = () => {
    setMonthIndex(monthIndex - 1);
  };

  const handleNextMonth = () => {
    setMonthIndex(monthIndex + 1);
  };

  const handleReset = () => {
    if (monthIndex !== dayjs().month()) {
      setMonthIndex(dayjs().month());
    }
    // setMonthIndex(monthIndex === dayjs().month()dayjs().month());
  };

  return (
    <Stack direction="row" justifyContent="space-between" alignItems="center" m={1}>
      <Button
        size="small"
        variant="contained"
        color="secondary"
        sx={{ border: '1px solid', height: '35px', fontWeight: 'bold' }}
        onClick={handleReset}
      >
        Today
      </Button>
      <Typography variant="h6">{dayjs(new Date(dayjs().year(), monthIndex)).format('MMMM YYYY')}</Typography>
      <ButtonGroup>
        <IconButton onClick={handlePrevMonth}>
          <ChevronLeftIcon />
        </IconButton>
        <IconButton onClick={handleNextMonth}>
          <ChevronRightIcon />
        </IconButton>
      </ButtonGroup>
    </Stack>
  );
}
