import CircleButton from '@/components/Dashboard/CircleButton';
import {
  Avatar,
  Badge,
  Box,
  Button,
  ClickAwayListener,
  Divider,
  Grow,
  IconButton,
  ListItemIcon,
  ListItemText,
  MenuItem,
  MenuList,
  Paper,
  Popper,
  Stack,
  Typography,
  useTheme,
} from '@mui/material';
import React, { ComponentProps } from 'react';
import { HiOutlineBell } from 'react-icons/hi';
import CloseIcon from '@mui/icons-material/Close';
import styled from 'styled-components';
import useSettings from '@/hooks/useSettings';

const NotificationCircleButton = styled(CircleButton)`
  .notification-badge {
    background-color: #732ebe;
    color: ${(props) => props.theme.palette.secondary.lighter};
    font-family: 'Poppins Regular';
    font-weight: 400;
    font-size: 0.65rem;
  }
`;

export type NotificationCircleButtonProps = Omit<ComponentProps<typeof CircleButton>, 'size' | 'children'>;

function NotificationButton(props: NotificationCircleButtonProps) {
  const theme = useTheme();
  const isLight = useSettings().themeMode === 'light';
  const [open, setOpen] = React.useState(false);
  const anchorRef = React.useRef<HTMLButtonElement>(null);
  const handleToggle = () => {
    setOpen((prevOpen) => !prevOpen);
  };
  const handleClose = (event: Event | React.SyntheticEvent) => {
    if (anchorRef.current && anchorRef.current.contains(event.target as HTMLElement)) {
      return;
    }

    setOpen(false);
  };

  return (
    <>
      <NotificationCircleButton
        size="small"
        {...props}
        ref={anchorRef}
        id="composition-button"
        aria-controls={open ? 'composition-menu' : undefined}
        aria-expanded={open ? 'true' : undefined}
        aria-haspopup="true"
        onClick={handleToggle}
      >
        <Badge badgeContent={4} color="default" classes={{ badge: 'notification-badge' }}>
          <HiOutlineBell size="1.3rem" />
        </Badge>
      </NotificationCircleButton>
      <Popper
        open={open}
        anchorEl={anchorRef.current}
        role={undefined}
        placement="top-end"
        sx={{ width: 350 }}
        transition
        disablePortal
      >
        {({ TransitionProps, placement }) => (
          <Grow
            {...TransitionProps}
            style={{
              transformOrigin: placement === 'top-end' ? 'top right ' : 'top right ',
            }}
          >
            <Paper sx={{ boxShadow: 10, overFlow: 'hidden' }}>
              <ClickAwayListener onClickAway={handleClose}>
                <MenuList autoFocusItem={open} id="composition-menu" aria-labelledby="composition-button">
                  <Stack direction="row" justifyContent="space-between" alignItems="center" px={2} py={1} pt={2}>
                    <Typography variant="subtitle2" textAlign="start">
                      Notification
                    </Typography>
                    <IconButton sx={{ backgroundColor: theme.palette.grey[100] }} size="small" onClick={handleClose}>
                      <CloseIcon sx={{ fontSize: 15 }} />
                    </IconButton>
                  </Stack>
                  {/* <Divider /> */}
                  {[1, 2, 3, 4, 5, 6].map((m) => (
                    <MenuItem
                      key={m}
                      onClick={handleClose}
                      sx={{ borderBottom: 1, borderColor: isLight ? theme.palette.grey[200] : theme.palette.grey[700] }}
                    >
                      <Box display="flex" alignItems="center" gap={1.5}>
                        <Avatar
                          sx={{
                            width: '35px',
                            height: '35px',
                            color: theme.palette.common.white,
                          }}
                        />
                        <Stack direction="row" alignItems="center" gap={2}>
                          <Stack direction="column" alignItems="start">
                            <Typography variant="subtitle2" fontSize={12}>
                              Sports Day
                            </Typography>
                            <Typography
                              variant="body2"
                              fontSize={10}
                              sx={{
                                width: 250,
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap',
                                overflow: 'hidden',
                              }}
                            >
                              Lorem ipsum dolor sit, amet consectetur adipisicing elit. Nulla error dignissimos
                              perspiciatis consectetur, laudantium iste ad minima velit reiciendis doloremque earum
                              accusantium aperiam tempore at soluta vel? Nostrum, ducimus laborum.
                            </Typography>
                            <Typography variant="body2" fontSize={10}>
                              2 hour ago
                            </Typography>
                          </Stack>
                          <Badge color="success" sx={{ width: 5, height: 5 }} variant="dot" />
                        </Stack>
                      </Box>
                    </MenuItem>
                  ))}
                  <Button onClick={handleClose} sx={{ fontSize: 12, py: 0.5, my: 0.5 }} size="small">
                    View All
                  </Button>
                </MenuList>
              </ClickAwayListener>
            </Paper>
          </Grow>
        )}
      </Popper>
    </>
  );
}

export default NotificationButton;
