/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Typography,
  Card,
  IconButton,
} from '@mui/material';
import styled from 'styled-components';
import { CLASS_SELECT } from '@/config/Selection';
import { conveyorsData } from '@/config/TableData';
import DeleteIcon from '@mui/icons-material/Delete';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import Popup from '@/components/shared/Popup/Popup';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import { MdAdd } from 'react-icons/md';
import EditDrivers from './EditDrivers';

const ManageConveyersRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  h6 {
    font-weight: 600;
  }
  .MuiTextField-root:hover .MuiInputBase-input {
    cursor: text;
  }
`;

function ManageConveyers() {
  const [Delete, setDelete] = React.useState(false);

  const handleClickDelete = () => setDelete(true);
  const handleClickCloseDelete = () => setDelete(false);

  const [staff, setStaffs] = React.useState(conveyorsData);
  const [open, setOpen] = React.useState(false);
  const [selectedStaff, setSelectedStaff] = React.useState(null);

  const handleOpen = (Staff: any) => {
    setSelectedStaff(Staff);
    setOpen(true);
  };
  const handleClose = () => {
    setOpen(false);
  };

  const handleUpdate = (id: number, updatedData: any) => {
    const updatedStaffs = staff.map((staffs) => (staffs.id === id ? { ...staffs, ...updatedData } : staffs));
    setStaffs(updatedStaffs);
    setOpen(false);
  };

  return (
    <Page title="Manage Conveyors">
      <ManageConveyersRoot>
        <Card elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17} width="80%">
              Manage Conveyors
            </Typography>
            <Box>
              <Button sx={{ borderRadius: '20px' }} variant="outlined" size="small" onClick={() => handleOpen(staff)}>
                <MdAdd size="20px" /> Create
              </Button>
            </Box>
          </Stack>
          <Divider />
          <Grid pb={4} pt={2} container spacing={3}>
            <Grid item lg={3} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Class
              </Typography>
              <Autocomplete
                options={CLASS_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select" />}
              />
            </Grid>
            <Grid item lg={3} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Driver Name
              </Typography>
              <TextField placeholder="Enter Name" fullWidth />
            </Grid>
            <Grid item lg={3} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Driver Number
              </Typography>
              <TextField placeholder="Enter Number" fullWidth />
            </Grid>
            <Grid item lg={3} xs={12}>
              <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                <Button variant="contained" color="secondary" fullWidth>
                  Reset
                </Button>
                <Button variant="contained" color="primary" fullWidth>
                  Search
                </Button>
              </Stack>
            </Grid>
          </Grid>
          <Box>
            <Paper
              sx={{
                border: `1px solid #e8e8e9`,
                width: '100%',
                overflow: 'auto',
                '&::-webkit-scrollbar': {
                  width: 0,
                },
              }}
            >
              <TableContainer
                sx={{
                  maxHeight: 410,
                  width: { xs: '750px', md: '100%' },
                  '&::-webkit-scrollbar': {
                    width: 0,
                  },
                }}
              >
                <Table stickyHeader aria-label="simple table">
                  <TableHead>
                    <TableRow>
                      <TableCell>ID No</TableCell>
                      <TableCell>Driver Name</TableCell>
                      <TableCell>Driver Number</TableCell>
                      <TableCell>Last Updated</TableCell>
                      <TableCell>Action</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {conveyorsData.map((r) => (
                      <TableRow hover key={r.id}>
                        <TableCell>{r.idNo}</TableCell>
                        <TableCell>{r.driverName}</TableCell>
                        <TableCell>{r.driverNumber}</TableCell>
                        <TableCell>{r.lastUpdated}</TableCell>
                        <TableCell>
                          <Stack direction="row" gap={1}>
                            <IconButton onClick={() => handleOpen(r)} size="small">
                              <ModeEditIcon />
                            </IconButton>
                            <IconButton onClick={handleClickDelete} color="error" size="small">
                              <DeleteIcon />
                            </IconButton>
                          </Stack>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Paper>
          </Box>
        </Card>
      </ManageConveyersRoot>
      <Popup
        size="xs"
        state={Delete}
        onClose={handleClickCloseDelete}
        popupContent={<DeleteMessage message="Are you sure want to delete?" />}
      />
      <Popup
        size="sm"
        state={open}
        onClose={handleClose}
        popupContent={
          selectedStaff && <EditDrivers staff={selectedStaff} onUpdate={handleUpdate} onCancel={handleClose} />
        }
      />
    </Page>
  );
}

export default ManageConveyers;
