/* eslint-disable no-nested-ternary */
/* eslint-disable react/no-array-index-key */
/* eslint-disable jsx-a11y/alt-text */
import React, { FormEvent, useCallback, useState } from 'react';
import {
  Divider,
  Grid,
  Paper,
  Stack,
  Button,
  Typography,
  TextField,
  Box,
  Avatar,
  FormControl,
  Select,
  MenuItem,
  SelectChangeEvent,
  useTheme,
  IconButton,
  Tooltip,
} from '@mui/material';
import styled from 'styled-components';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useAppSelector } from '@/hooks/useAppSelector';
import {
  getStaffListData,
  getStaffListStatus,
  getYearData,
  getMessageTempSubmitting,
  getNotificationSubmitting,
} from '@/config/storeSelectors';
import useAuth from '@/hooks/useAuth';
import { fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import {
  fetchStaffList,
  // messageSendToStaff,
  messageSendToStaffList,
} from '@/store/MessageBox/messageBox.thunks';
import LoadingButton from '@mui/lab/LoadingButton';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import SendIcon from '@mui/icons-material/Send';
import LoadingMsg from '@/assets/MessageIcons/loading-message.gif';
import { SendToStaffType as SendToStaffType2 } from '@/types/Notification';
import { SendToStaffType, StaffDataListRequest, StaffDataType } from '@/types/MessageBox';
import { GENDER_SELECT, STAFF_CATEGORY_SELECT } from '@/config/Selection';
import ErrorMsg from '@/assets/MessageIcons/error-message.gif';
import ErrorMsg1 from '@/assets/MessageIcons/error-message1.gif';
import ErrorMsg2 from '@/assets/MessageIcons/error-message2.gif';
import SuccessMsg from '@/assets/MessageIcons/message-success.gif';
import Lottie from 'lottie-react';
import successIcon from '@/assets/MessageIcons/success.json';
import errorIcon from '@/assets/MessageIcons/error-message.json';
import { breakPointsMaxwidth } from '@/config/breakpoints';
import { SendPopupProps } from '@/types/Common';
import { notificationSendToStaffList } from '@/store/AppNotification/appNotification.thunks';
import { voiceMessageSendToStaffList } from '@/store/VoiceMessage/voiceMessage.thunk';
import { SendVoiceToStaffRequest } from '@/types/VoiceMessage';
import { SuccessMessage } from '../Popup/SuccessMessage';
import { ErrorMessage } from '../Popup/ErrorMessage';
import { useConfirm } from '../Popup/Confirmation';
import LoadingPopup from '../Popup/LoadingPopup';
import { LoadingMessage } from '../Popup/LoadingMessage';

const StaffRoot = styled.div`
  width: 100%;
  padding: 8px 24px;
  .delivery-report-btn {
    @media ${breakPointsMaxwidth.xs} {
      width: 100%;
    }
  }
  .send-btn {
    @media ${breakPointsMaxwidth.sm} {
      width: 50%;
    }
    @media ${breakPointsMaxwidth.xs} {
      width: 100%;
    }
  }
  .cancel-btn {
    @media ${breakPointsMaxwidth.xs} {
      width: 25%;
    }
    @media ${breakPointsMaxwidth.sm} {
      width: 50%;
    }
  }
`;

interface StaffDataWithStatus extends StaffDataType {
  sendStatus: 'Success' | 'Failed' | '' | undefined;
}

function Staff({ messageId, notificationId, voiceId, isSubmitting, templateId }: SendPopupProps) {
  const { user } = useAuth();
  const theme = useTheme();
  const dispatch = useAppDispatch();
  const { confirm } = useConfirm();
  const adminId: number = user ? user.accountId : 0;
  const [view, setView] = useState<'main' | 'report'>('main');
  const [academicYearFilter, setAcademicYearFilter] = useState(0);
  const [staffCodeFilter, setStaffCodeFilter] = useState('');
  const [staffNameFilter, setStaffNameFilter] = useState('');
  const [staffPhoneNumberFilter, setStaffPhoneNumberFilter] = useState('');
  const [staffRoleFilter, setStaffRoleFilter] = useState('');
  const [genderFilter, setGenderFilter] = useState('-1');
  const [categoryFilter, setCategoryFilter] = useState(-1);
  const [sendResults, setSendResults] = useState<StaffDataWithStatus[]>([]);
  const [selectedRows, setSelectedRows] = useState<StaffDataWithStatus[]>([]);
  const YearData = useAppSelector(getYearData);
  const StaffListData = useAppSelector(getStaffListData);
  const StaffListStatus = useAppSelector(getStaffListStatus);
  // const isSubmittingSms = useAppSelector(getMessageTempSubmitting);
  // const isSubmittingNotification = useAppSelector(getNotificationSubmitting);
  const [selectedData, setSelectedData] = useState<StaffDataWithStatus[]>([]);
  const [errMsgTooltip, setErrMsgTooltip] = useState<React.ReactNode>(undefined);
  const [disabledCheckBoxes, setDisabledCheckBoxes] = useState<boolean[]>([]);

  const initialStaffListRequest = React.useMemo(
    () => ({
      staffCode: '',
      staffName: '',
      staffPhoneNumber: '',
      accademicId: 10,
      jobRole: '',
      gender: '-1',
      category: -1,
    }),
    []
  );

  const currentStaffListRequest = React.useMemo(
    () => ({
      staffCode: staffCodeFilter,
      staffName: staffNameFilter,
      staffPhoneNumber: staffPhoneNumberFilter,
      accademicId: academicYearFilter,
      jobRole: staffRoleFilter,
      gender: genderFilter,
      category: categoryFilter,
    }),
    [
      academicYearFilter,
      staffCodeFilter,
      staffNameFilter,
      staffPhoneNumberFilter,
      staffRoleFilter,
      genderFilter,
      categoryFilter,
    ]
  );

  const loadStaffList = useCallback(
    async (request: StaffDataListRequest) => {
      try {
        const data = await dispatch(fetchStaffList(request)).unwrap();
        const dataWithStatus = data.map((item) => ({
          ...item,
          sendStatus: sendResults.find((i) => i.staffPhoneNumber === item.staffPhoneNumber)?.sendStatus,
        }));
        console.log(dataWithStatus);
        setSelectedData(dataWithStatus);
        setSelectedRows([]);
      } catch (error) {
        console.error('Error loading Staff list:', error);
      }
    },
    [dispatch, setSelectedData, sendResults]
  );

  React.useEffect(() => {
    dispatch(fetchYearList(adminId));
    if (StaffListStatus === 'idle') {
      loadStaffList(currentStaffListRequest);
    }
  }, [dispatch, adminId, currentStaffListRequest, StaffListStatus, loadStaffList]);

  const handleYearChange = (e: SelectChangeEvent) => {
    console.log('handleYearChange', e.target.value);
    setAcademicYearFilter(parseInt(YearData.filter((item) => item.accademicId === e.target.value)[0].accademicId, 10));
    loadStaffList({ ...currentStaffListRequest, accademicId: parseInt(e.target.value, 10) });
    setSelectedRows([]);
  };
  const handleGenderChange = (e: SelectChangeEvent) => {
    setGenderFilter(GENDER_SELECT.filter((item) => item.id === e.target.value)[0].id);
    loadStaffList({ ...currentStaffListRequest, gender: e.target.value });
    setSelectedRows([]);
  };
  const handleCategoryChange = (e: SelectChangeEvent) => {
    setCategoryFilter(STAFF_CATEGORY_SELECT.filter((item) => item.id === parseInt(e.target.value, 10))[0].id);
    loadStaffList({ ...currentStaffListRequest, category: parseInt(e.target.value, 10) });
    setSelectedRows([]);
  };

  const handleReset = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      setAcademicYearFilter(parseInt(YearData[0].accademicId, 10));
      setStaffCodeFilter('');
      setStaffNameFilter('');
      setStaffPhoneNumberFilter('');
      setStaffRoleFilter('');
      setGenderFilter('-1');
      setCategoryFilter(-1);
      setSelectedRows([]);
      loadStaffList(initialStaffListRequest);
    },
    [YearData, loadStaffList, initialStaffListRequest]
  );
  const handleSubmit = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      loadStaffList(currentStaffListRequest);
      setSelectedRows([]);
    },
    [loadStaffList, currentStaffListRequest]
  );

  const handleCancel = useCallback(() => {
    setSelectedRows([]);
    loadStaffList(currentStaffListRequest);
    setDisabledCheckBoxes([]);
  }, [currentStaffListRequest, loadStaffList]);

  const handleBack = useCallback(() => {
    setView('main');
    setSelectedRows([]);
  }, []);

  const handleSendMessage = useCallback(async () => {
    try {
      console.log(selectedRows);
      if (!selectedRows || selectedRows.length === 0) {
        const errorMessage = <ErrorMessage icon={ErrorMsg1} message="Please Select Atleast one Staff to sent" />;
        await confirm(errorMessage, 'Staff not selected', { okLabel: 'Ok', showOnlyOk: true });
        return;
      }
      const sendRequests: SendToStaffType[] = await Promise.all(
        selectedRows?.map(async (item) => {
          const { ...rest } = item;
          const sendReq = { messageId, adminId, ...rest };
          return sendReq;
        }) || []
      );
      const actionResult = await dispatch(messageSendToStaffList(sendRequests));
      if (actionResult && Array.isArray(actionResult.payload)) {
        const results: StaffDataWithStatus[] = actionResult.payload;
        setSendResults([...sendResults, ...results]);
        const errorMessages = results.find((result) => result.sendStatus === 'Failed');
        const successMessages = results.find((result) => result.sendStatus === 'Success');
        if (!errorMessages) {
          const successMessage = (
            <SuccessMessage icon={SuccessMsg} message="Message sent to all selected Staff Successfully" />
          );
          await confirm(successMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        } else if (!successMessages) {
          setErrMsgTooltip(
            <Typography variant="subtitle2" fontSize={12} style={{ color: theme.palette.error.main }}>
              No notifications sent to Staff, Please check the numbers and Try again
            </Typography>
          );
          const errorMessage = (
            <ErrorMessage
              icon={ErrorMsg2}
              message="No notifications sent to any Staff, Please check the numbers and Try again"
            />
          );
          await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        } else {
          setErrMsgTooltip(
            <Typography variant="subtitle2" fontSize={12} style={{ color: theme.palette.error.main }}>
              Error sending messages to Staff, Please recheck and Try again
            </Typography>
          );
          const errorMessage = (
            <ErrorMessage
              icon={ErrorMsg}
              message="Error sending messages to some Staff, Please recheck and Try again"
            />
          );
          await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        }
        const dataResult = selectedData.map((item) => ({
          ...item,
          sendStatus:
            item.sendStatus !== 'Success' && item.sendStatus !== 'Failed'
              ? results.find((result) => result.staffPhoneNumber === item.staffPhoneNumber)?.sendStatus
              : item.sendStatus,
        }));
        setSelectedData(dataResult);
      } else {
        const errorMessage = <ErrorMessage message="Error while sending messages, Please Try again" />;
        await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
      }
      setSelectedRows([]);
      const updatedDisabledCheckBoxes = selectedData.map((selectedItem) =>
        selectedRows.includes(selectedItem)
          ? !disabledCheckBoxes[selectedData.indexOf(selectedItem)]
          : disabledCheckBoxes[selectedData.indexOf(selectedItem)]
      );

      setDisabledCheckBoxes(updatedDisabledCheckBoxes);
    } catch (error) {
      console.error('Error sending messages:', error);
    }
  }, [dispatch, confirm, messageId, selectedRows, adminId, selectedData, disabledCheckBoxes, sendResults, theme]);

  const handleSendNotification = useCallback(async () => {
    try {
      console.log(selectedRows);
      if (!selectedRows || selectedRows.length === 0) {
        const errorMessage = <ErrorMessage icon={ErrorMsg1} message="Please Select Atleast one Staff to sent" />;
        await confirm(errorMessage, 'Staff not selected', { okLabel: 'Ok', showOnlyOk: true });
        return;
      }
      const sendRequests: SendToStaffType2[] = await Promise.all(
        selectedRows?.map(async (item) => {
          const { ...rest } = item;
          const sendReq = { notificationId, adminId, ...rest };
          return sendReq;
        }) || []
      );
      const actionResult = await dispatch(notificationSendToStaffList(sendRequests));
      if (actionResult && Array.isArray(actionResult.payload)) {
        const results: StaffDataWithStatus[] = actionResult.payload;

        const errorMessages = results.find((result) => result.sendStatus === 'Failed');
        const successMessages = results.find((result) => result.sendStatus === 'Success');
        if (!errorMessages) {
          const successMessage = (
            <SuccessMessage icon={SuccessMsg} message="Notification sent to all selected Staff Successfully" />
          );
          await confirm(successMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        } else if (!successMessages) {
          setErrMsgTooltip(
            <Typography variant="subtitle2" fontSize={12} style={{ color: theme.palette.error.main }}>
              No notifications sent to Staff, Please check the numbers and Try again
            </Typography>
          );
          const errorMessage = (
            <ErrorMessage
              icon={ErrorMsg2}
              message="No notifications sent to any Staff, Please check the numbers and Try again"
            />
          );
          await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        } else {
          setErrMsgTooltip(
            <Typography variant="subtitle2" fontSize={12} style={{ color: theme.palette.error.main }}>
              Error sending notifications to Staff, Please recheck and Try again
            </Typography>
          );
          const errorMessage = (
            <ErrorMessage
              icon={ErrorMsg}
              message="Error sending notifications to some Staff, Please recheck and Try again"
            />
          );
          await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        }
        setSendResults([...sendResults, ...results]);
        const dataResult = selectedData.map((item) => ({
          ...item,
          sendStatus:
            item.sendStatus !== 'Success' && item.sendStatus !== 'Failed'
              ? results.find((result) => result.staffPhoneNumber === item.staffPhoneNumber)?.sendStatus
              : item.sendStatus,
        }));
        setSelectedData(dataResult);
      } else {
        const errorMessage = <ErrorMessage message="Error while sending notifications, Please Try again" />;
        await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
      }
      setSelectedRows([]);
      const updatedDisabledCheckBoxes = selectedData.map((selectedItem) =>
        selectedRows.includes(selectedItem)
          ? !disabledCheckBoxes[selectedData.indexOf(selectedItem)]
          : disabledCheckBoxes[selectedData.indexOf(selectedItem)]
      );

      setDisabledCheckBoxes(updatedDisabledCheckBoxes);
    } catch (error) {
      console.error('Error sending messages:', error);
    }
  }, [dispatch, confirm, selectedRows, adminId, selectedData, disabledCheckBoxes, sendResults, theme, notificationId]);

  const handleSendVoiceMessage = useCallback(async () => {
    try {
      console.log(selectedRows);
      if (!selectedRows || selectedRows.length === 0) {
        const errorMessage = <ErrorMessage icon={ErrorMsg1} message="Please Select Atleast one Staff to sent" />;
        await confirm(errorMessage, 'Staff not selected', { okLabel: 'Ok', showOnlyOk: true });
        return;
      }
      const sendRequests: SendVoiceToStaffRequest[] = await Promise.all(
        selectedRows?.map(async (item) => {
          const { ...rest } = item;
          const sendReq = { voiceId, adminId, templateId, ...rest };
          return sendReq;
        }) || []
      );
      const actionResult = await dispatch(voiceMessageSendToStaffList(sendRequests));
      if (actionResult && Array.isArray(actionResult.payload)) {
        const results: StaffDataWithStatus[] = actionResult.payload;
        setSendResults([...sendResults, ...results]);
        const errorMessages = results.find((result) => result.sendStatus === 'Failed');
        const successMessages = results.find((result) => result.sendStatus === 'Success');
        if (!errorMessages) {
          const successMessage = (
            <SuccessMessage icon={SuccessMsg} message="Message sent to all selected Staff Successfully" />
          );
          await confirm(successMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        } else if (!successMessages) {
          setErrMsgTooltip(
            <Typography variant="subtitle2" fontSize={12} style={{ color: theme.palette.error.main }}>
              No notifications sent to Staff, Please check the numbers and Try again
            </Typography>
          );
          const errorMessage = (
            <ErrorMessage
              icon={ErrorMsg2}
              message="No notifications sent to any Staff, Please check the numbers and Try again"
            />
          );
          await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        } else {
          setErrMsgTooltip(
            <Typography variant="subtitle2" fontSize={12} style={{ color: theme.palette.error.main }}>
              Error sending messages to Staff, Please recheck and Try again
            </Typography>
          );
          const errorMessage = (
            <ErrorMessage
              icon={ErrorMsg}
              message="Error sending messages to some Staff, Please recheck and Try again"
            />
          );
          await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        }
        const dataResult = selectedData.map((item) => ({
          ...item,
          sendStatus:
            item.sendStatus !== 'Success' && item.sendStatus !== 'Failed'
              ? results.find((result) => result.staffPhoneNumber === item.staffPhoneNumber)?.sendStatus
              : item.sendStatus,
        }));
        setSelectedData(dataResult);
      } else {
        const errorMessage = <ErrorMessage message="Error while sending messages, Please Try again" />;
        await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
      }
      setSelectedRows([]);
      const updatedDisabledCheckBoxes = selectedData.map((selectedItem) =>
        selectedRows.includes(selectedItem)
          ? !disabledCheckBoxes[selectedData.indexOf(selectedItem)]
          : disabledCheckBoxes[selectedData.indexOf(selectedItem)]
      );

      setDisabledCheckBoxes(updatedDisabledCheckBoxes);
    } catch (error) {
      console.error('Error sending messages:', error);
    }
  }, [dispatch, confirm, messageId, selectedRows, adminId, selectedData, disabledCheckBoxes, sendResults, theme]);

  const getRowKey = useCallback((row: StaffDataWithStatus) => row.staffId, []);

  //  StatusIcon
  const getStatusIcon = (row: StaffDataWithStatus) => {
    let iconComponent;

    if (row.sendStatus === 'Failed') {
      iconComponent = (
        <Tooltip title={errMsgTooltip} placement="left">
          <Stack>
            <Lottie animationData={errorIcon} loop={false} style={{ width: '30px' }} />
          </Stack>
        </Tooltip>
      );
    } else if (row.sendStatus === 'Success') {
      iconComponent = (
        <Stack>
          <Lottie animationData={successIcon} loop={false} style={{ width: '30px' }} />
        </Stack>
      );
    } else if (row.sendStatus === null) {
      iconComponent = 'empty';
    } else {
      iconComponent = null;
    }

    return <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>{iconComponent}</Box>;
  };
  const StaffListColumns: DataTableColumn<StaffDataWithStatus>[] = [
    {
      name: 'status',
      renderCell: getStatusIcon,
    },
    {
      name: 'slNo',
      headerLabel: 'Sl.No',
      dataKey: 'staffId',
      sortable: true,
    },
    {
      name: 'studentName',
      headerLabel: 'Staff Name',
      renderCell: (row) => {
        return (
          <Stack direction="row" gap={1} alignItems="center">
            {row.staffImage !== '' ? (
              <Avatar src={`http://demo.passdaily.in/Photos/StaffImage/${row.staffImage}`} />
            ) : (
              <Avatar />
            )}
            <Typography variant="subtitle2">{row.staffName}</Typography>
          </Stack>
        );
      },
    },
    {
      name: 'gender',
      dataKey: 'staffGender',
      headerLabel: 'Gender',
    },
    {
      name: 'staffRole',
      dataKey: 'staffJobRole',
      headerLabel: 'Role',
    },
    {
      name: 'mobileNumber',
      dataKey: 'staffPhoneNumber',
      headerLabel: 'Mobile Number',
    },
  ];
  return (
    <StaffRoot>
      <Box>
        <Divider />
        {view === 'main' ? (
          <form noValidate onReset={handleReset} onSubmit={handleSubmit}>
            <Grid pb={2} pt={1} spacing={3} container alignItems="end">
              <Grid item lg={3} md={3} sm={4} xs={12}>
                <FormControl fullWidth>
                  <Typography variant="h6" fontSize={12} color="GrayText">
                    Academic Year
                  </Typography>
                  <Select
                    labelId="academicYearFilter"
                    id="academicYearFilterSelect"
                    value={academicYearFilter.toString()}
                    onChange={handleYearChange}
                    placeholder="Select Year"
                  >
                    <MenuItem value={0}>Select Year</MenuItem>
                    {YearData.map((opt) => (
                      <MenuItem key={opt.accademicId} value={opt.accademicId}>
                        {opt.accademicTime}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item lg={3} md={3} sm={4} xs={12}>
                <FormControl fullWidth>
                  <Typography variant="subtitle2" fontSize={12} color="GrayText">
                    Staff Name
                  </Typography>
                  <TextField
                    name="staffName"
                    value={staffNameFilter}
                    placeholder="Enter name"
                    onChange={(e) => {
                      setStaffNameFilter(e.target.value);
                      loadStaffList({
                        ...currentStaffListRequest,
                        staffName: e.target.value,
                      });
                    }}
                  />
                </FormControl>
              </Grid>
              <Grid item lg={3} md={3} sm={4} xs={12}>
                <FormControl fullWidth>
                  <Typography variant="subtitle2" fontSize={12} color="GrayText">
                    Staff Code
                  </Typography>
                  <TextField
                    name="staffCode"
                    value={staffCodeFilter}
                    placeholder="Enter code"
                    onChange={(e) => {
                      setStaffCodeFilter(e.target.value);
                      loadStaffList({
                        ...currentStaffListRequest,
                        staffCode: e.target.value,
                      });
                    }}
                  />
                </FormControl>
              </Grid>
              <Grid item lg={3} md={3} sm={4} xs={12}>
                <FormControl fullWidth>
                  <Typography variant="subtitle2" fontSize={12} color="GrayText">
                    Staff Role
                  </Typography>
                  <TextField
                    name="staffRole"
                    value={staffRoleFilter}
                    placeholder="Enter role"
                    onChange={(e) => {
                      setStaffRoleFilter(e.target.value);
                      loadStaffList({
                        ...currentStaffListRequest,
                        jobRole: e.target.value,
                      });
                    }}
                  />
                </FormControl>
              </Grid>
              <Grid item lg={3} md={3} sm={4} xs={12}>
                <FormControl fullWidth>
                  <Typography variant="subtitle2" fontSize={12} color="GrayText">
                    Phone Number
                  </Typography>
                  <TextField
                    name="staffPhoneNumber"
                    value={staffPhoneNumberFilter}
                    placeholder="Enter number"
                    onChange={(e) => {
                      setStaffPhoneNumberFilter(e.target.value);
                      loadStaffList({
                        ...currentStaffListRequest,
                        staffPhoneNumber: e.target.value,
                      });
                    }}
                  />
                </FormControl>
              </Grid>
              <Grid item lg={3} md={3} sm={4} xs={12}>
                <FormControl fullWidth>
                  <Typography variant="subtitle2" fontSize={12} color="GrayText">
                    Staff Gender
                  </Typography>
                  <Select
                    labelId="genderFilter"
                    id="genderFilterSelect"
                    value={genderFilter}
                    onChange={handleGenderChange}
                    placeholder="Select Gender"
                  >
                    {GENDER_SELECT.map((opt, index) => (
                      <MenuItem key={index} value={opt.id}>
                        {opt.gender}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item lg={3} md={3} sm={4} xs={12}>
                <FormControl fullWidth>
                  <Typography variant="h6" fontSize={12} color="GrayText">
                    Section
                  </Typography>
                  <Select
                    labelId="categoryFilter"
                    id="categoryFilterSelect"
                    value={categoryFilter.toString()}
                    onChange={handleCategoryChange}
                    placeholder="Select Category"
                  >
                    {STAFF_CATEGORY_SELECT.map((opt) => (
                      <MenuItem key={opt.id} value={opt.id}>
                        {opt.category}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item lg={3} md={3} sm={4} xs={12}>
                <FormControl fullWidth>
                  <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                    <Button variant="contained" color="secondary" type="reset" fullWidth>
                      Reset
                    </Button>
                    <Button variant="contained" color="primary" type="submit" fullWidth>
                      Search
                    </Button>
                  </Stack>
                </FormControl>
              </Grid>
            </Grid>
          </form>
        ) : (
          <Stack direction="row" alignItems="center" gap={1}>
            <IconButton onClick={handleBack} color="primary" size="small">
              <ArrowBackIcon fontSize="small" />
            </IconButton>
            <Typography onClick={handleBack} variant="subtitle2" fontSize={17} py={2}>
              DELIVERY REPORT
            </Typography>
          </Stack>
        )}
        <Paper
          sx={{
            border: selectedData?.length === 0 ? `0px` : `1px solid ${theme.palette.grey[300]}`,
            width: '100%',
            overflow: 'auto',
          }}
        >
          <Box
            sx={{
              height: 'calc(100vh - 23.7rem)',
              width: { xs: selectedData?.length !== 0 ? '43.75rem' : '100%', md: '100%' },
            }}
          >
            {view === 'main' ? (
              <DataTable
                ShowCheckBox
                disabledCheckBox={disabledCheckBoxes}
                RowSelected
                setSelectedRows={setSelectedRows}
                selectedRows={selectedRows}
                columns={StaffListColumns}
                data={selectedData}
                getRowKey={getRowKey}
                fetchStatus={StaffListStatus}
              />
            ) : (
              <DataTable
                setSelectedRows={setSelectedRows}
                columns={StaffListColumns}
                data={sendResults}
                getRowKey={getRowKey}
                fetchStatus={StaffListStatus}
              />
            )}
          </Box>
        </Paper>
        <Box
          display="flex"
          sx={{
            justifyContent: {
              xs: 'right',
            },
            pr: {
              lg: '5',
            },
            pt: 3,
          }}
        >
          {view === 'main' && selectedData.length !== 0 && (
            <Stack
              spacing={2}
              direction={{ xs: 'column', sm: 'row' }}
              justifyContent={{ xs: 'ceneter', sm: 'space-between' }}
              width={sendResults.length > 0 ? '100%' : 'auto'}
            >
              {sendResults && sendResults.length > 0 && (
                <Button
                  className="delivery-report-btn"
                  sx={{ width: { xs: '48.5%', sm: 'auto' } }}
                  color="warning"
                  onClick={() => setView('report')}
                  variant="contained"
                >
                  Delivery Report
                </Button>
              )}
              <Stack spacing={2} direction="row">
                <Button
                  className="cancel-btn"
                  // sx={{ width: { xs: '25%', sm: 'auto' } }}
                  onClick={handleCancel}
                  variant="contained"
                  color="secondary"
                >
                  Cancel
                </Button>
                <LoadingButton
                  className="send-btn"
                  loadingPosition="start"
                  endIcon={<SendIcon />}
                  fullWidth
                  // sx={{ width: { sm: 'auto' } }}
                  onClick={
                    notificationId && notificationId !== 0
                      ? handleSendNotification
                      : messageId && messageId !== 0
                      ? handleSendMessage
                      : handleSendVoiceMessage
                  }
                  loading={isSubmitting}
                  variant="contained"
                  color="primary"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Sending...' : 'Send'}
                </LoadingButton>
              </Stack>
            </Stack>
          )}
        </Box>
      </Box>
      {isSubmitting ? (
        <LoadingPopup popupContent={<LoadingMessage icon={LoadingMsg} message="Sending messages please wait." />} />
      ) : null}
    </StaffRoot>
  );
}

export default Staff;
