/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Avatar,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  Button,
  Typography,
  Card,
  useTheme,
  IconButton,
  Collapse,
  Tooltip,
  FormControl,
  Select,
  MenuItem,
  SelectChangeEvent,
  Alert,
  ToggleButtonGroup,
  ToggleButton,
  Snackbar,
} from '@mui/material';
import styled from 'styled-components';
import { GENDER_SELECT } from '@/config/Selection';
import SearchIcon from '@mui/icons-material/Search';
import NoData from '@/assets/no-datas.png';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { StudentListInfo, StudentListRequest } from '@/types/StudentManagement';
import { YearDataType } from '@/types/Dashboard';
import { fetchStudentList } from '@/store/Students/studentManagement.thunks';
import {
  getStudentListData,
  getStudentListPageInfo,
  getStudentListStatus,
  getStudentDeletingRecords,
  getStudentSortColumn,
  getStudentSortDirection,
  getStudentSubmitting,
  getClassData,
  getYearData,
  getYearStatus,
} from '@/config/storeSelectors';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useAppSelector } from '@/hooks/useAppSelector';
import useAuth from '@/hooks/useAuth';
import { fetchClassList, fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import { setSortColumn, setSortDirection } from '@/store/Students/studentManagement.slice';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import { ClassListInfo } from '@/types/AcademicManagement';
import dayjs from 'dayjs';
import useSettings from '@/hooks/useSettings';
import { useNavigate } from 'react-router-dom';
import BackButton from '@/components/shared/BackButton';
import * as XLSX from 'xlsx';
// import { saveAs } from 'file-saver';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import { BsFiletypeXls } from 'react-icons/bs';
import { SuccessIcon } from '@/theme/overrides/CustomIcons';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import { AddStudentMultipleProps } from './AddStudentMultiple';

const DownloadStudentInfoRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  .Card {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    /* min-height: calc(100vh - 1160px); */
    /* @media screen and (max-width: 768px) {
      margin-bottom: 6px;
    } */
    @media screen and (max-width: 576px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      /* max-height: calc(100% - 40px); */
      flex-grow: 1;
      .card-table-container {
        flex-grow: 1;
        width: 100%;
        /* height: calc(100vh - 255px); */
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

function DownloadStudentInfo({ onBackClick }: AddStudentMultipleProps) {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const { user } = useAuth();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const adminId: number = user ? user.accountId : 0;
  const [showFilter, setShowFilter] = useState(true);
  const { confirm } = useConfirm();

  const ClassData = useAppSelector(getClassData);
  const YearData = useAppSelector(getYearData);
  const YearStatus = useAppSelector(getYearStatus);

  const AllClassOption = {
    classId: -1,
    className: 'All Class',
    classDescription: 'string',
    classStatus: 1,
  };
  const AllYearOption: YearDataType = {
    accademicId: 0,
    accademicTime: 'Select Year',
    accademicStatus: 1,
  };
  const AllGenderOption = {
    id: '-1',
    gender: 'All',
  };
  const defualtYear = YearData[0]?.accademicId || 0;
  const classDataWithAllClass = [AllClassOption, ...ClassData];
  const yearDataWithAllYear = [AllYearOption, ...YearData];
  const gendersDataWithAll = [AllGenderOption, ...GENDER_SELECT];
  const [classFilter, setClassFilter] = useState(classDataWithAllClass[0]);
  const [studentNameFilter, setStudentNameFilter] = useState('');
  const [studentGenderFilter, setStudentGenderFilter] = useState(-1);
  const [admissionNumberFilter, setAdmissionNumberFilter] = useState('');
  const [academicYearFilter, setAcademicYearFilter] = useState(0);
  const [studentFatherNameFilter, setStudentFatherNameFilter] = useState('');
  const [studentFatherNumberFilter, setStudentFatherNumberFilter] = useState('');
  const [selectedFields, setSelectedFields] = useState<string[]>([]); // Default columns
  const StudentListStatus = useAppSelector(getStudentListStatus);
  const StudentListData = useAppSelector(getStudentListData);
  // const StudentListError = useAppSelector(getStudentListError);
  const paginationInfo = useAppSelector(getStudentListPageInfo);
  const sortColumn = useAppSelector(getStudentSortColumn);
  const sortDirection = useAppSelector(getStudentSortDirection);
  const isSubmitting = useAppSelector(getStudentSubmitting);
  const deletingRecords = useAppSelector(getStudentDeletingRecords);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  const { pagenumber, pagesize, totalrecords } = paginationInfo;
  const { classId, className } = classFilter || {};
  // const { gender } = studentGenderFilter || {};

  const currentStudentListRequest = useMemo(
    () => ({
      pageNumber: pagenumber,
      pageSize: pagesize,
      sortColumn,
      sortDirection,
      filters: {
        adminId,
        academicId: academicYearFilter,
        classId,
        studentName: studentNameFilter,
        studentGender: studentGenderFilter,
        admissionDate: '',
        admissionNumber: admissionNumberFilter,
        studentDob: '',
        studentFatherName: studentFatherNameFilter,
        studentFatherNumber: studentFatherNumberFilter,
        studentBloodGroup: '',
        studentCaste: '',
      },
    }),
    [
      adminId,
      pagenumber,
      pagesize,
      sortColumn,
      sortDirection,
      classId,
      academicYearFilter,
      studentNameFilter,
      studentGenderFilter,
      admissionNumberFilter,
      studentFatherNameFilter,
      studentFatherNumberFilter,
    ]
  );

  const loadStudentList = useCallback(
    (request: StudentListRequest) => {
      dispatch(fetchStudentList(request));
    },
    [dispatch]
  );

  const handleClassChange = (e: SelectChangeEvent) => {
    const selectedClass = classDataWithAllClass.find((item) => item.className === e.target.value);
    if (selectedClass) {
      setClassFilter(selectedClass);
    }
    loadStudentList({
      ...currentStudentListRequest,
      pageNumber: 1,
      filters: { ...currentStudentListRequest.filters, classId: selectedClass ? selectedClass.classId : 0 },
    });
  };
  const handleYearChange = (e: SelectChangeEvent) => {
    setAcademicYearFilter(parseInt(e.target.value, 10));
    loadStudentList({
      ...currentStudentListRequest,
      filters: { ...currentStudentListRequest.filters, academicId: parseInt(e.target.value, 10) },
    });
  };

  useEffect(() => {
    if (YearStatus === 'idle') {
      setAcademicYearFilter(defualtYear);
    }
    if (StudentListStatus === 'idle') {
      loadStudentList(currentStudentListRequest);
      dispatch(fetchYearList(adminId));
      dispatch(fetchClassList(adminId));
    }
    console.log('datass::', StudentListData);
  }, [
    loadStudentList,
    defualtYear,
    YearStatus,
    StudentListStatus,
    currentStudentListRequest,
    StudentListData,
    adminId,
    dispatch,
  ]);

  const getRowKey = useCallback((row: StudentListInfo) => row.studentId, []);

  const handlePageChange = useCallback(
    (event: unknown, newPage: number) => {
      loadStudentList({ ...currentStudentListRequest, pageNumber: newPage + 1 });
    },
    [currentStudentListRequest, loadStudentList]
  );

  const handleChangeRowsPerPage = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const newPageSize = +event.target.value;
      loadStudentList({ ...currentStudentListRequest, pageNumber: 1, pageSize: newPageSize });
    },
    [currentStudentListRequest, loadStudentList]
  );

  const handleSort = useCallback(
    (column: string) => {
      const newReq = { ...currentStudentListRequest };
      if (column === sortColumn) {
        console.log('Toggling direction');
        const sortDirectionNew = sortDirection === 'asc' ? 'desc' : 'asc';
        newReq.pageNumber = 1;
        newReq.sortDirection = sortDirectionNew;
        dispatch(setSortDirection(sortDirectionNew));
      } else {
        newReq.pageNumber = 1;
        newReq.sortColumn = column;
        dispatch(setSortColumn(column));
      }

      loadStudentList(newReq);
    },
    [currentStudentListRequest, dispatch, loadStudentList, sortColumn, sortDirection]
  );

  const pageProps = useMemo(
    () => ({
      rowsPerPageOptions: [10, 20, 30, 50, 100, 200, 300, 500],
      pageNumber: pagenumber - 1,
      pageSize: pagesize,
      totalRecords: totalrecords,
      onPageChange: handlePageChange,
      onRowsPerPageChange: handleChangeRowsPerPage,
    }),
    [handleChangeRowsPerPage, handlePageChange, pagenumber, pagesize, totalrecords]
  );

  const handleReset = useCallback(
    (e: any) => {
      e.preventDefault();
      setClassFilter(classDataWithAllClass[0]);
      setAcademicYearFilter(0);
      setStudentNameFilter('');
      setStudentNameFilter('');
      setStudentGenderFilter(-1);
      setAdmissionNumberFilter('');
      setStudentFatherNameFilter('');
      setStudentFatherNumberFilter('');
      setSelectedFields([]);
      loadStudentList({
        pageNumber: 1,
        pageSize: 20,
        sortColumn: 'studentId',
        sortDirection: 'asc',
        filters: {
          adminId,
          academicId: 0,
          classId: -1,
          studentName: '',
          studentGender: -1,
          admissionDate: '',
          admissionNumber: '',
          studentDob: '',
          studentFatherName: '',
          studentFatherNumber: '',
          studentBloodGroup: '',
          studentCaste: '',
        },
      });
    },
    [loadStudentList, classDataWithAllClass, adminId]
  );
  const StudentsParentInfoColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'admissionNumber',
        dataKey: 'admissionNumber',
        headerLabel: 'Admission No',
      },
      {
        name: 'admissionDate',
        dataKey: 'admissionDate',
        headerLabel: 'Admission Date',
        renderCell: (row) => {
          const formattedDate = dayjs(row.admissionDate, 'YYYY/MM/DD').format('DD/MM/YYYY');
          return (
            <Typography fontSize={13} variant="subtitle1">
              {formattedDate}
            </Typography>
          );
        },
      },
      {
        name: 'studentName',
        headerLabel: 'Student Name',
        renderCell: (row) => {
          return (
            <Stack direction="row" gap={1} alignItems="center">
              <Avatar alt="" src={row.studentImage} />
              <Typography variant="subtitle2">{row.studentName}</Typography>
            </Stack>
          );
        },
      },
      {
        name: 'class',
        dataKey: 'className',
        headerLabel: 'Class',
      },
      {
        name: 'academicYear',
        dataKey: 'academicTime',
        headerLabel: 'Year',
      },
      {
        name: 'gender',
        dataKey: 'studentGender',
        headerLabel: 'Gender',
        renderCell: (row) => {
          return (
            <Typography variant="subtitle1" fontSize={14}>
              {row.studentGender === 0 ? 'Male' : row.studentGender === 1 ? 'Female' : 'Other'}
            </Typography>
          );
        },
      },
      {
        name: 'studentDOB',
        dataKey: 'studentDOB',
        headerLabel: 'DOB',
        renderCell: (row) => {
          const formattedDate = dayjs(row.studentDOB, 'YYYY/MM/DD').format('DD/MM/YYYY');
          return (
            <Typography fontSize={13} variant="subtitle1">
              {formattedDate}
            </Typography>
          );
        },
      },
      {
        name: 'studentBloodGroup',
        dataKey: 'studentBloodGroup',
        headerLabel: 'Blood',
      },
      {
        name: 'studentBirthPlace',
        dataKey: 'studentBirthPlace',
        headerLabel: 'Birth Place',
      },
      {
        name: 'studentNationality',
        dataKey: 'studentNationality',
        headerLabel: 'Nationality',
      },

      {
        name: 'studentMotherTongue',
        dataKey: 'studentMotherTongue',
        headerLabel: 'Mother Tongue',
      },
      {
        name: 'studentReligion',
        dataKey: 'studentReligion',
        headerLabel: 'Religion',
      },
      {
        name: 'studentCaste',
        dataKey: 'studentCaste',
        headerLabel: 'Caste',
      },

      {
        name: 'studentFatherName',
        dataKey: 'studentFatherName',
        headerLabel: 'Father',
      },
      {
        name: 'studentMotherName',
        dataKey: 'studentMotherName',
        headerLabel: 'Mother',
      },
      {
        name: 'studentFatherQualification',
        dataKey: 'studentFatherQualification',
        headerLabel: 'Father Qualification',
      },
      {
        name: 'studentMotherQualification',
        dataKey: 'studentMotherQualification',
        headerLabel: 'Mother Qualification',
      },
      {
        name: 'studentFatherNumber',
        dataKey: 'studentFatherNumber',
        headerLabel: 'Phone Number',
      },
      {
        name: 'studentCAddress',
        dataKey: 'studentCAddress',
        headerLabel: 'Address',
      },
      {
        name: 'studentEmailId',
        dataKey: 'studentEmailId',
        headerLabel: 'Email Id',
      },
      {
        name: 'studentLastStudied',
        dataKey: 'studentLastStudied',
        headerLabel: 'Last Studied',
      },
    ],
    []
  );

  const handleFieldToggle = (event: React.MouseEvent<HTMLElement>, newFields: string[]) => {
    if (newFields.length) {
      setSelectedFields(newFields);
    }
  };

  const filteredColumns = useMemo(
    () => StudentsParentInfoColumns.filter((col) => selectedFields.includes(col.name)),
    [StudentsParentInfoColumns, selectedFields]
  );

  // const handleExportExcel = () => {
  //   const exportData = StudentListData.map((student) => ({
  //     Name: student.studentName,
  //     'Admission Number': student.admissionNumber,
  //     Class: student.className,
  //     'Academic Year': student.academicTime,
  //     Gender: student.studentGender === 0 ? 'Male' : student.studentGender === 1 ? 'Female' : 'Other',
  //     'Blood Group': student.studentBloodGroup,
  //     'D.O.B': dayjs(student.studentDOB).format('DD/MM/YYYY'),
  //     Parent: student.studentFatherName,
  //     Phone: student.studentMobile,
  //   }));

  //   const worksheet = XLSX.utils.json_to_sheet(exportData);
  //   const workbook = XLSX.utils.book_new();
  //   XLSX.utils.book_append_sheet(workbook, worksheet, 'StudentInfo');

  //   const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
  //   const data = new Blob([excelBuffer], { type: 'application/octet-stream' });
  //   saveAs(data, 'Student_Info.xlsx');
  // };

  const handleExportPDF = async () => {
    const input = document.querySelector('.student_card') as HTMLElement;

    if (!input) {
      setSnackbarMessage('Export area not found!');
      setSnackbarOpen(true);
      return;
    }

    const canvas = await html2canvas(input, { scale: 2 });
    const imgData = canvas.toDataURL('image/png');

    // 👇 Dynamically set orientation
    const orientation = filteredColumns.length >= 8 ? 'l' : 'p'; // 'l' for landscape, 'p' for portrait
    const pdf = new jsPDF(orientation, 'mm', 'a4');

    const pdfWidth = pdf.internal.pageSize.getWidth();
    const pdfHeight = pdf.internal.pageSize.getHeight();

    const margin = 8; // in mm

    const availableWidth = pdfWidth - margin * 2;
    const availableHeight = pdfHeight - margin * 2;

    const imgProps = pdf.getImageProperties(imgData);
    const imgRatio = imgProps.width / imgProps.height;
    const availableRatio = availableWidth / availableHeight;

    let imgWidth = availableWidth;
    let imgHeight = availableHeight;

    // Adjust image size to preserve aspect ratio
    if (imgRatio > availableRatio) {
      imgHeight = availableWidth / imgRatio;
    } else {
      imgWidth = availableHeight * imgRatio;
    }

    const x = (pdfWidth - imgWidth) / 2;
    const y = margin; // Align to top

    pdf.addImage(imgData, 'PNG', x, y, imgWidth, imgHeight);
    pdf.save('Student_Info.pdf');
  };

  const handleExportExcel = () => {
    // Step 1: Map of column name → actual data key
    const columnKeyMap: Record<string, string> = {
      gender: 'studentGender',
      academicYear: 'academicTime',
      class: 'className',
    };

    // Step 2: Get selected keys
    const selectedKeys = filteredColumns.map((col) => col.name);

    // Step 3: Filter and remap each student object
    const filteredData = StudentListData.map((student) => {
      const filteredStudent: Record<string, any> = {};
      selectedKeys.forEach((key) => {
        const actualKey = columnKeyMap[key] || key;
        let value = student[actualKey];

        // Special case for Gender → convert 0/1 to Male/Female
        if (actualKey === 'studentGender') {
          value = value === 0 ? 'Male' : value === 1 ? 'Female' : '';
        }

        filteredStudent[key] = value !== undefined ? value : '';
      });
      return filteredStudent;
    });

    console.log('filteredData::::----', filteredData);

    // Step 4: Export to Excel
    const ws = XLSX.utils.json_to_sheet(filteredData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
    XLSX.writeFile(wb, 'Student_Info.xlsx');
  };

  const firstHalf = StudentsParentInfoColumns.slice(0, Math.ceil(StudentsParentInfoColumns.length / 2));
  const secondHalf = StudentsParentInfoColumns.slice(Math.ceil(StudentsParentInfoColumns.length / 2));

  return (
    <Page title="Student Info">
      <DownloadStudentInfoRoot>
        <Card className="Card" elevation={1}>
          <Stack
            direction="row"
            flexWrap="wrap"
            justifyContent="space-between"
            alignItems="center"
            sx={{ px: { xs: 3, md: 5 }, pt: { xs: 2, md: 3 }, pb: 1 }}
          >
            <Stack direction="row" alignItems="center" justifyContent="space-between" flex={1} whiteSpace="nowrap">
              <Stack direction="row" alignItems="center">
                <BackButton
                  onBackClick={() => {
                    onBackClick();
                    navigate('info');
                  }}
                />
                <Typography variant="h6" fontSize={17}>
                  Download Student Info
                </Typography>
              </Stack>
            </Stack>
            <div>
              <Tooltip title="Search">
                <IconButton aria-label="search" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                  <SearchIcon />
                </IconButton>
              </Tooltip>
            </div>
          </Stack>
          <Divider />

          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={2} sx={{ px: { xs: 3, md: 5 } }}>
                  <Grid item lg={2.5} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Select
                        labelId="academicYearFilter"
                        id="academicYearFilterSelect"
                        value={academicYearFilter.toString()}
                        onChange={handleYearChange}
                        placeholder="Select Year"
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Year
                        </MenuItem>
                        {YearData.map((opt) => (
                          <MenuItem key={opt.accademicId} value={opt.accademicId}>
                            {opt.accademicTime}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item lg={2.5} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Select
                        labelId="classFilter"
                        id="classFilterSelect"
                        value={className}
                        onChange={handleClassChange}
                        placeholder="Select"
                        MenuProps={{
                          PaperProps: {
                            style: {
                              maxHeight: '250px', // Adjust the value to your desired height
                            },
                          },
                        }}
                      >
                        {classDataWithAllClass?.map((item: ClassListInfo) => (
                          <MenuItem key={item.classId} value={item.className}>
                            {item.className}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item lg={1} sm={1} xs={12}>
                    <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, sm: 3.79 } }}>
                      <Button variant="contained" color="secondary" fullWidth onClick={handleReset}>
                        Reset
                      </Button>
                      {/* <Button variant="contained" color="primary" fullWidth>
                          Search
                        </Button> */}
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Box
            // bgcolor={changeView === true ? (isLight ? theme.palette.primary.lighter : theme.palette.grey[800]) : ''}
            >
              <Stack mx={5} mt={showFilter ? 0 : 2}>
                <ToggleButtonGroup
                  value={selectedFields}
                  onChange={handleFieldToggle}
                  aria-label="field selection"
                  size="small"
                  color="primary"
                  disabled={StudentListData.length === 0}
                  sx={{ whiteSpace: 'nowrap', flexWrap: 'wrap' }}
                >
                  {StudentsParentInfoColumns.map((col) => {
                    const isSelected = selectedFields.includes(col.name);
                    return (
                      <ToggleButton sx={{ fontWeight: 'bold' }} key={col.name} value={col.name}>
                        {isSelected && <SuccessIcon fontSize="small" sx={{ mr: 1 }} />}
                        {col.headerLabel || col.name}
                      </ToggleButton>
                    );
                  })}
                </ToggleButtonGroup>
              </Stack>
              <Stack mt={2} direction="row" justifyContent="end" alignItems="center" mx={5}>
                <Stack direction="row" justifyContent="center" alignItems="center" gap={2}>
                  <Button
                    disabled={filteredColumns.length === 0}
                    startIcon={<PictureAsPdfIcon />}
                    variant="contained"
                    color="info"
                    onClick={handleExportPDF}
                  >
                    Export to PDF
                  </Button>
                  <Button
                    disabled={filteredColumns.length === 0}
                    startIcon={<BsFiletypeXls />}
                    variant="contained"
                    color="success"
                    onClick={handleExportExcel}
                  >
                    Export to Excel
                  </Button>
                </Stack>
              </Stack>
              <Box className="student_table " sx={{ px: { xs: 3, md: 5 }, pb: { xs: 2, md: 2.5 } }}>
                <Paper className="card-table-container student_card" sx={{ marginTop: '12px' }}>
                  {filteredColumns.length !== 0 ? (
                    <DataTable
                      // tableStyles={{ minWidth: { xs: '2000px' } }}
                      columns={filteredColumns}
                      data={StudentListData}
                      getRowKey={getRowKey}
                      fetchStatus="success"
                      // allowPagination
                      allowSorting
                      sortColumn={sortColumn}
                      sortDirection={sortDirection}
                      onSort={handleSort}
                      // PaginationProps={pageProps}
                      deletingRecords={deletingRecords}
                    />
                  ) : (
                    <Box
                      display="flex"
                      alignItems="center"
                      justifyContent="center"
                      width="100%"
                      height={{ xs: 'calc(100vh - 400px)', sm: 'calc(100vh - 400px)', lg: 'calc(100vh - 100px)' }}
                    >
                      <Stack direction="column" alignItems="center">
                        <img src={NoData} width="150px" alt="" />
                        <Typography variant="subtitle2" mt={2} color="GrayText">
                          No data found !
                        </Typography>
                      </Stack>
                    </Box>
                  )}
                </Paper>
              </Box>
            </Box>
          </div>
        </Card>
      </DownloadStudentInfoRoot>
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={3000}
        onClose={() => setSnackbarOpen(false)}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert severity="error" variant="filled" onClose={() => setSnackbarOpen(false)}>
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Page>
  );
}

export default DownloadStudentInfo;
