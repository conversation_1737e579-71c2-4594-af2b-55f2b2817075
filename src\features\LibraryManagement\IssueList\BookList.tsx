/* eslint-disable no-nested-ternary */
import React, { useState } from 'react';
import {
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Button,
  IconButton,
  Paper,
  Box,
  Select,
  MenuItem,
  Typography,
  useTheme,
  Stack,
  Chip,
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import useSettings from '@/hooks/useSettings';
import LoadingButton from '@mui/lab/LoadingButton';
import AddIcon from '@mui/icons-material/Add';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import SendIcon from '@mui/icons-material/Send';
import LibraryBooksIcon from '@mui/icons-material/LibraryBooks';
import MenuBookIcon from '@mui/icons-material/MenuBook';
import PersonIcon from '@mui/icons-material/Person';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import CancelIcon from '@mui/icons-material/Cancel';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import LibraryBookPickerField from '@/components/shared/Selections/LibraryBookPicker';

interface BookRow {
  id: number;
  bookCode: string;
  bookName: string;
  authorName: string;
  year: string;
  price: string;
  aboutBook: string;
  status: string;
  category: string;
  bookLocation: string;
  remarks: string;
}

const bookOptions: Omit<BookRow, 'id' | 'remarks'>[] = [
  {
    bookCode: 'BC001',
    bookName: 'React Basics',
    authorName: 'Dan Abramov',
    year: '2021',
    price: '100',
    aboutBook: 'Intro to React',
    status: 'Good',
    category: 'Programming',
    bookLocation: 'A1',
  },
  {
    bookCode: 'BC002',
    bookName: 'TypeScript Deep Dive',
    authorName: 'Basarat Ali',
    year: '2020',
    price: '120',
    aboutBook: 'Advanced TS Concepts',
    status: 'Bad',
    category: 'Programming',
    bookLocation: 'B2',
  },
];

const BookTable: React.FC = () => {
  const theme = useTheme();
  const isLight = useSettings().themeMode === 'light';
  const [selectedBooktIds, setSelectedBooktIds] = useState<string>('');
  const [selectedBooktData, setSelectedBooktData] = useState<any[]>([]);

  const defaultEmptyRow = (): BookRow => ({
    id: Date.now(),
    bookCode: '',
    bookName: '',
    authorName: '',
    year: '',
    price: '',
    aboutBook: '',
    status: '',
    category: '',
    bookLocation: '',
    remarks: '',
  });

  const [rows, setRows] = useState<BookRow[]>([defaultEmptyRow()]);

  const handleAddRow = () => {
    setRows((prevRows) => [...prevRows, defaultEmptyRow()]);
  };

  const handleDeleteRow = (id: number) => {
    if (rows.length === 1) {
      // Only one row: clear its values
      setRows((prevRows) => prevRows.map((row) => (row.id === id ? { ...defaultEmptyRow(), id } : row)));
    } else {
      // More than one row: remove the row
      setRows((prevRows) => prevRows.filter((row) => row.id !== id));
    }
  };

  const handleBookCodeChange = (id: number, value: string) => {
    const selected = bookOptions.find((book) => book.bookCode === value);
    setRows((prevRows) =>
      prevRows.map((row) =>
        row.id === id && selected
          ? {
              ...row,
              ...selected,
              bookCode: value,
              id: row.id,
              remarks: row.remarks,
            }
          : row
      )
    );
  };

  // const handleInputChange = (id: number, field: keyof BookRow, value: string) => {
  //   setRows((prevRows) => prevRows.map((row) => (row.id === id ? { ...row, [field]: value } : row)));
  // };
  const handleInputChange = (id: number, field: keyof BookRow, value: string) => {
    if (field === 'status' && value === '') {
      // Clear all status fields
      setRows((prevRows) =>
        prevRows.map((row) => ({
          ...row,
          status: '',
        }))
      );
    } else {
      setRows((prevRows) => prevRows.map((row) => (row.id === id ? { ...row, [field]: value } : row)));
    }
  };

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Typography variant="h6" fontSize={17}>
          Issued List
        </Typography>
        <Button size="small" variant="contained" color="secondary" onClick={handleAddRow} startIcon={<AddIcon />}>
          Add Row
        </Button>
      </Box>

      <Paper
        sx={{
          border: 1,
          borderColor: theme.palette.grey[300],
          height: 380,
          overflow: 'auto',
          '&::-webkit-scrollbar': {
            height: '15px',
          },
        }}
        elevation={isLight ? 0 : 0}
      >
        <Table stickyHeader sx={{ minWidth: { xs: '1000px', lg: '1200px', xl: '1300px', xxl: '100% ' } }}>
          <TableHead sx={{ backgroundColor: isLight ? theme.palette.grey[200] : theme.palette.grey[900] }}>
            <TableRow
              sx={{
                '& > th:first-of-type': {
                  paddingLeft: 2,
                },
              }}
            >
              {[
                'BOOK CODE',
                'BOOK NAME',
                'AUTHOR NAME',
                'YEAR',
                'PRICE',
                'ABOUT BOOK',
                'STATUS',
                'CATEGORY',
                'BOOK LOCATION',
                'REMARKS',
                'ACTION',
              ].map((label) => (
                <TableCell key={label} sx={{ fontSize: '13px', fontWeight: 'bold' }}>
                  {label}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>

          <TableBody>
            {rows.map((row) => (
              <TableRow
                key={row.id}
                sx={{
                  '& > td:first-of-type': {
                    paddingLeft: 2,
                  },
                }}
              >
                {/* Book Code - Select */}
                <TableCell>
                  {/* <LibraryBookPickerField
                    loadRecentPaidList={() => {}}
                    currentRecentPaidListRequest={() => {}}
                    setSelectedBooktIds={setSelectedBooktIds}
                    setSelectedBooktData={setSelectedBooktData}
                    // classId={classFilter}
                    academicId={11}
                    width="100%"
                    componentsPropsWidth={350}
                  /> */}
                  <Select
                    value={row.bookCode}
                    onChange={(e) => handleBookCodeChange(row.id, e.target.value)}
                    fullWidth
                    displayEmpty
                    variant="outlined"
                    sx={{ width: 125, fontSize: '13px' }}
                    renderValue={(selected) => (selected === '' ? 'Select Code' : selected)}
                    MenuProps={{
                      PaperProps: {
                        style: {
                          maxHeight: 250,
                        },
                      },
                    }}
                  >
                    <MenuItem value="" sx={{ borderBottom: 1, borderColor: theme.palette.grey[300], fontSize: '11px' }}>
                      Select Code
                    </MenuItem>
                    {bookOptions.map((book) => (
                      <MenuItem
                        sx={{
                          '&:last-child': {
                            borderBottom: 0,
                          },
                          borderBottom: 1,
                          borderColor: theme.palette.grey[300],
                          width: 300,
                        }}
                        key={book.bookCode}
                        value={book.bookCode}
                      >
                        <Box display="flex" width="100%" justifyContent="space-between">
                          <Stack direction="column" gap={0.5}>
                            <Typography variant="subtitle2" whiteSpace="pre-wrap" fontSize="11px" fontWeight="bold">
                              Code&nbsp;: {book.bookCode}
                            </Typography>
                            <Typography
                              variant="subtitle2"
                              whiteSpace="pre-wrap"
                              fontSize="11px"
                              color={theme.palette.grey[600]}
                            >
                              Title : {book.bookName}
                            </Typography>
                            <Typography
                              variant="subtitle2"
                              whiteSpace="pre-wrap"
                              fontSize="11px"
                              color={theme.palette.grey[600]}
                            >
                              Author : {book.authorName}
                            </Typography>
                          </Stack>
                          <Stack direction="column" justifyContent="space-between" alignItems="flex-end" gap={0.5}>
                            <Chip
                              label={book.status}
                              color={book.status === 'Good' ? 'success' : 'error'}
                              size="small"
                              variant="filled"
                              sx={{ width: 50, fontSize: '11px', fontWeight: 600, height: 18 }}
                            />

                            <Typography variant="subtitle2" fontSize="11px" color={theme.palette.warning.dark}>
                              Shelf : {book.bookLocation}
                            </Typography>
                            <Typography variant="subtitle2" fontSize="11px">
                              Rs. {book.price}
                            </Typography>
                          </Stack>
                        </Box>
                      </MenuItem>
                    ))}
                  </Select>
                </TableCell>

                {/* Other Fields - TextFields */}
                {(Object.keys(row) as (keyof BookRow)[])
                  .filter((key) => key !== 'id' && key !== 'bookCode')
                  .map((field) => (
                    <TableCell key={field}>
                      {field === 'status' ? (
                        row[field] ? (
                          <Chip
                            label={row.status}
                            color={row.status.toLowerCase() === 'good' ? 'success' : 'error'}
                            size="small"
                            variant="filled"
                          />
                        ) : (
                          <Typography variant="body2">--</Typography>
                        )
                      ) : row[field] ? (
                        <Typography
                          variant="body2"
                          sx={{ fontSize: '13px', fontWeight: field === 'bookName' ? 'bold' : 'normal' }}
                        >
                          {row[field]}
                        </Typography>
                      ) : (
                        <Typography textAlign="start" variant="body2">
                          --
                        </Typography>
                      )}

                      {/* <TextField
                        variant="outlined"
                        value={row[field]}
                        onChange={(e) => handleInputChange(row.id, field, e.target.value)}
                        fullWidth 
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            borderRadius: 0.5,
                          },
                        }}
                      /> */}
                    </TableCell>
                  ))}

                {/* Delete Button */}
                <TableCell>
                  <IconButton
                    size="small"
                    color="error"
                    onClick={() => handleDeleteRow(row.id)}
                    disabled={
                      rows.length === 1 &&
                      Object.values(row).every(
                        (val, i) => (i === 0 ? true : val === '') // Skip id (first field), check others
                      )
                    }
                  >
                    <DeleteIcon />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </Paper>
      <Box mt={2} sx={{ justifyContent: 'center', pt: 0, position: 'relative' }}>
        <Stack spacing={2} direction="row" justifyContent="center" sx={{}}>
          <Button
            // disabled={isSubmitting}
            // onClick={onCancel}
            variant="outlined"
            color="secondary"
            type="button"
          >
            Cancel
          </Button>
          <LoadingButton
            variant="contained"
            color="primary"
            type="submit"
            loadingPosition="start"
            // startIcon={<CheckCircleIcon />} // or <SendIcon />
          >
            Issue Book
          </LoadingButton>
        </Stack>
      </Box>
    </Box>
  );
};

export default BookTable;
