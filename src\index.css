@charset 'UTF-8';

@font-face {
  font-family: 'Poppins Regular';
  font-style: normal;
  font-weight: normal;
  src: local('Poppins Regular'), url('/fonts/Poppins-Regular.woff') format('woff');
}

@font-face {
  font-family: 'Poppins Italic';
  font-style: normal;
  font-weight: normal;
  src: local('Poppins Italic'), url('/fonts/Poppins-Italic.woff') format('woff');
}

@font-face {
  font-family: 'Poppins Thin';
  font-style: normal;
  font-weight: normal;
  src: local('Poppins Thin'), url('/fonts/Poppins-Thin.woff') format('woff');
}

@font-face {
  font-family: 'Poppins Thin Italic';
  font-style: normal;
  font-weight: normal;
  src: local('Poppins Thin Italic'), url('/fonts/Poppins-ThinItalic.woff') format('woff');
}

@font-face {
  font-family: 'Poppins ExtraLight';
  font-style: normal;
  font-weight: normal;
  src: local('Poppins ExtraLight'), url('/fonts/Poppins-ExtraLight.woff') format('woff');
}

@font-face {
  font-family: 'Poppins ExtraLight Italic';
  font-style: normal;
  font-weight: normal;
  src: local('Poppins ExtraLight Italic'), url('/fonts/Poppins-ExtraLightItalic.woff') format('woff');
}

@font-face {
  font-family: 'Poppins Light';
  font-style: normal;
  font-weight: normal;
  src: local('Poppins Light'), url('/fonts/Poppins-Light.woff') format('woff');
}

@font-face {
  font-family: 'Poppins Light Italic';
  font-style: normal;
  font-weight: normal;
  src: local('Poppins Light Italic'), url('/fonts/Poppins-LightItalic.woff') format('woff');
}

@font-face {
  font-family: 'Poppins Medium';
  font-style: normal;
  font-weight: normal;
  src: local('Poppins Medium'), url('/fonts/Poppins-Medium.woff') format('woff');
}

@font-face {
  font-family: 'Poppins Medium Italic';
  font-style: normal;
  font-weight: normal;
  src: local('Poppins Medium Italic'), url('/fonts/Poppins-MediumItalic.woff') format('woff');
}

@font-face {
  font-family: 'Poppins SemiBold';
  font-style: normal;
  font-weight: normal;
  src: local('Poppins SemiBold'), url('/fonts/Poppins-SemiBold.woff') format('woff');
}

@font-face {
  font-family: 'Poppins SemiBold Italic';
  font-style: normal;
  font-weight: normal;
  src: local('Poppins SemiBold Italic'), url('/fonts/Poppins-SemiBoldItalic.woff') format('woff');
}

@font-face {
  font-family: 'Poppins Bold';
  font-style: normal;
  font-weight: normal;
  src: local('Poppins Bold'), url('/fonts/Poppins-Bold.woff') format('woff');
}

@font-face {
  font-family: 'Poppins Bold Italic';
  font-style: normal;
  font-weight: normal;
  src: local('Poppins Bold Italic'), url('/fonts/Poppins-BoldItalic.woff') format('woff');
}

@font-face {
  font-family: 'Poppins ExtraBold';
  font-style: normal;
  font-weight: normal;
  src: local('Poppins ExtraBold'), url('/fonts/Poppins-ExtraBold.woff') format('woff');
}

@font-face {
  font-family: 'Poppins ExtraBold Italic';
  font-style: normal;
  font-weight: normal;
  src: local('Poppins ExtraBold Italic'), url('/fonts/Poppins-ExtraBoldItalic.woff') format('woff');
}

body {
  user-select: none;
}

/* Slider */
.slick-slider {
  position: relative;

  display: block;
  box-sizing: border-box;

  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  -webkit-touch-callout: none;
  -khtml-user-select: none;
  -ms-touch-action: pan-y;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
}

.slick-list {
  position: relative;

  display: block;
  overflow: hidden;

  margin: 0;
  padding: 0;
}

.slick-list:focus {
  outline: none;
}

.slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.slick-slider .slick-track,
.slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  -o-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.slick-track {
  position: relative;
  top: 0;
  left: 0;

  display: block;
  margin-left: auto;
  margin-right: auto;
}

.slick-track:before,
.slick-track:after {
  display: table;

  content: '';
}

.slick-track:after {
  clear: both;
}

.slick-loading .slick-track {
  visibility: hidden;
}

.slick-slide {
  display: none;
  float: left;

  height: 100%;
  min-height: 1px;
}

[dir='rtl'] .slick-slide {
  float: right;
}

.slick-slide img {
  display: block;
}

.slick-slide.slick-loading img {
  display: none;
}

.slick-slide.dragging img {
  pointer-events: none;
}

.slick-initialized .slick-slide {
  display: block;
}

.slick-loading .slick-slide {
  visibility: hidden;
}

.slick-vertical .slick-slide {
  display: block;

  height: auto;

  border: 1px solid transparent;
}

.slick-arrow.slick-hidden {
  display: none;
}

/* Slider */
.slick-loading .slick-list {
  background: #fff url('/ajax-loader.gif') center center no-repeat;
}

/* Icons */
@font-face {
  font-family: 'slick';
  font-weight: normal;
  font-style: normal;

  src: url('/fonts/slick.eot');
  src: url('/fonts/slick.eot?#iefix') format('embedded-opentype'), url('/fonts/slick.woff') format('woff'),
    url('/fonts/slick.ttf') format('truetype'), url('/fonts/slick.svg#slick') format('svg');
}

/* Arrows */
.slick-prev,
.slick-next {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0, -50%);
  -ms-transform: translate(0, -50%);
  transform: translate(0, -50%);
  cursor: pointer;
}

.slick-prev:hover:before,
.slick-prev:focus:before,
.slick-next:hover:before,
.slick-next:focus:before {
  opacity: 1;
}

.slick-prev.slick-disabled:before,
.slick-next.slick-disabled:before {
  opacity: 0.25;
}

.slick-prev {
  left: -25px;
}

[dir='rtl'] .slick-prev {
  right: -25px;
  left: auto;
}

.slick-next {
  right: -25px;
}

[dir='rtl'] .slick-next {
  right: auto;
  left: -25px;
}

/* Dots */
.slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.slick-dots {
  position: absolute;
  bottom: -25px;

  display: block;

  width: 100%;
  padding: 0;
  margin: 0;

  list-style: none;

  text-align: center;
}

.slick-dots li {
  position: relative;

  display: inline-block;

  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;

  cursor: pointer;
}

.slick-dots li button {
  font-size: 0;
  line-height: 0;

  display: block;

  width: 20px;
  height: 20px;
  padding: 5px;

  cursor: pointer;

  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.slick-dots li button:hover,
.slick-dots li button:focus {
  outline: none;
}

.slick-dots li button:hover:before,
.slick-dots li button:focus:before {
  opacity: 1;
}

.slick-dots li button:before {
  font-family: 'slick';
  font-size: 6px;
  line-height: 20px;

  position: absolute;
  top: 0;
  left: 0;

  width: 20px;
  height: 20px;

  content: '•';
  text-align: center;

  opacity: 0.25;
  color: black;

  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.MuiDrawer-root .MuiBackdrop-root {
  opacity: 0.3 !important;
}

.Notsuccess_img_bg {
  background-color: #F4F6F8;
  padding: 10px;
  border-radius: 50%;
  height: 150px;
  width: 150px;
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  /* background: #FFF8F5;  */
}

::-webkit-scrollbar-thumb {
  background: #fdb489;
  border-radius: 5px;
}


