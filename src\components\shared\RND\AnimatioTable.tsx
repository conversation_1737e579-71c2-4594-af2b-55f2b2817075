import React, { useState } from 'react';
import { Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Button, Stack } from '@mui/material';
import { motion } from 'framer-motion';

const initialStudentData = [
  {
    id: 1,
    name: '<PERSON>',
    age: 18,
    grade: 'A',
    email: '<EMAIL>',
    phone: '************',
    address: 'New York, USA',
    gender: 'Male',
  },
  {
    id: 2,
    name: '<PERSON>',
    age: 19,
    grade: 'B',
    email: '<EMAIL>',
    phone: '************',
    address: 'Los Angeles, USA',
    gender: 'Female',
  },
  {
    id: 3,
    name: '<PERSON>',
    age: 17,
    grade: 'A',
    email: '<EMAIL>',
    phone: '************',
    address: 'Chicago, USA',
    gender: 'Male',
  },
  {
    id: 4,
    name: '<PERSON>',
    age: 18,
    grade: 'C',
    email: '<EMAIL>',
    phone: '************',
    address: 'Houston, USA',
    gender: 'Female',
  },
  {
    id: 5,
    name: '<PERSON>',
    age: 20,
    grade: 'B',
    email: '<EMAIL>',
    phone: '************',
    address: 'Boston, USA',
    gender: 'Male',
  },
  {
    id: 6,
    name: '<PERSON> Portman',
    age: 21,
    grade: 'A',
    email: '<EMAIL>',
    phone: '************',
    address: 'Washington, USA',
    gender: 'Female',
  },
  {
    id: 7,
    name: 'Robert Downey',
    age: 19,
    grade: 'C',
    email: '<EMAIL>',
    phone: '************',
    address: 'San <PERSON>, USA',
    gender: 'Male',
  },
  {
    id: 8,
    name: 'Scarlett Johansson',
    age: 22,
    grade: 'A',
    email: '<EMAIL>',
    phone: '************',
    address: 'Miami, USA',
    gender: 'Female',
  },
  {
    id: 9,
    name: 'Tom Holland',
    age: 18,
    grade: 'B',
    email: '<EMAIL>',
    phone: '************',
    address: 'London, UK',
    gender: 'Male',
  },
  {
    id: 10,
    name: 'Emma Watson',
    age: 23,
    grade: 'A',
    email: '<EMAIL>',
    phone: '************',
    address: 'Paris, France',
    gender: 'Female',
  },
];
const rowVariants = {
  hidden: { opacity: 0, y: 10, x: -10 },
  visible: (i: number) => ({
    opacity: 1,
    y: 0,
    x: 0,
    transition: { delay: i * 0.1, duration: 0.3 },
  }),
};

const StudentTable: React.FC = () => {
  const [studentData, setStudentData] = useState(initialStudentData);
  const [refreshKey, setRefreshKey] = useState(0);

  const handleRefresh = () => {
    setStudentData([...initialStudentData]); // Reset data
    setRefreshKey((prev) => prev + 1); // Force re-render
  };

  return (
    <>
      <Stack direction="row" justifyContent="end">
        <Button variant="contained" color="primary" onClick={handleRefresh} sx={{ marginBottom: 2 }}>
          Refresh Table
        </Button>
      </Stack>

      <TableContainer component={Paper} sx={{ margin: 'auto', mt: 4 }}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>ID</TableCell>
              <TableCell>Name</TableCell>
              <TableCell>Age</TableCell>
              <TableCell>Grade</TableCell>
              <TableCell>Email</TableCell>
              <TableCell>Phone</TableCell>
              <TableCell>Address</TableCell>
              <TableCell>Gender</TableCell>
            </TableRow>
          </TableHead>
          <TableBody key={refreshKey}>
            {studentData.map((student, index) => (
              <motion.tr
                key={student.id}
                variants={rowVariants}
                initial="hidden"
                animate="visible"
                custom={index}
                style={{ display: 'table-row' }} // Ensures correct styling
              >
                <TableCell>{student.id}</TableCell>
                <TableCell>{student.name}</TableCell>
                <TableCell>{student.age}</TableCell>
                <TableCell>{student.grade}</TableCell>
                <TableCell>{student.email}</TableCell>
                <TableCell>{student.phone}</TableCell>
                <TableCell>{student.address}</TableCell>
                <TableCell>{student.gender}</TableCell>
              </motion.tr>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </>
  );
};

export default StudentTable;
