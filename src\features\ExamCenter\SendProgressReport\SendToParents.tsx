import React, { useCallback, useMemo, useRef, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Box,
  Button,
  Divider,
  FormControl,
  Card,
  Stack,
  Collapse,
  Grid,
  IconButton,
  TextField,
  Typography,
  Select,
  MenuItem,
  Paper,
  useTheme,
  TableContainer,
  TableRow,
  TableCell,
  TableBody,
  Table,
  TableHead,
  Autocomplete,
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import { breakPointsMaxwidth } from '@/config/breakpoints';
import { useReactToPrint } from 'react-to-print';
import styled from 'styled-components';
import useSettings from '@/hooks/useSettings';
import { CLASS_SELECT, YEAR_SELECT, YEAR_SELECT_OPTIONS } from '@/config/Selection';
import { TopperProgrssReport } from '@/types/ExamCenter';
import typography from '@/theme/typography';
import TabButton from '@/components/shared/TabButton';

const SendProgressReportRoot = styled.div`
  padding: 1rem;
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.grey[100] : props.theme.palette.grey[900]};

  h6 {
    font-weight: 600;
  }
  .sub-heading {
    font-size: 0.75rem;
    color: ${(props) => props.theme.palette.grey[600]};
    font-weight: 500;
  }
  .MuiAutocomplete-input {
  }
  .MuiCard-root {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[800]};
  }
  .Card {
    min-height: calc(100vh - 165px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }
  }
  .card_top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-direction: row;
    flex-wrap: wrap;
    padding-bottom: 10px;
  }
  .Card_title {
  }
  .progress_report_tab {
    display: flex;
    align-items: center;
    flex-direction: row;
    padding-top: 10px;
  }
  @media screen and (max-width: 1160px) {
    .card_top {
      display: flex;
      align-items: center;
      justify-content: baseline;
      padding-bottom: 0px;
    }
    .progress_report_tab {
      display: flex;
      justify-content: end;
      overflow-x: scroll;
      ::-webkit-scrollbar {
        height: 10px;
      }
      ::-webkit-scrollbar-thumb {
        background-color: ${(props) => props.theme.palette.grey[400]};
        border-radius: 20px;
      }
    }
  }
  @media screen and (max-width: 998px) {
    .progress_report_tab {
      justify-content: end;
    }
  }
  @media screen and (max-width: 768px) {
    .progress_report_tab {
      padding-bottom: 10px;
      width: 100%;
      ::-webkit-scrollbar {
        height: 0px;
      }
    }
  }
  .CardContainer {
  }
`;

export type SendProgressReportProps = {
  onClickStudentWise: () => void;
};
function SendProgressReport({ onClickStudentWise }: SendProgressReportProps) {
  const theme = useTheme();
  const { themeMode } = useSettings();

  return (
    <Page title="Send To Parents">
      <SendProgressReportRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <div className="card_top ">
            <div className="Card_title">
              <Typography variant="h6" fontSize={17}>
                Send To Parents
              </Typography>
            </div>
            <Stack className="progress_report_tab">
              <div style={{ flexShrink: 0 }}>
                <TabButton title="Send To Parents " variant="contained" />
                <TabButton title="Student Wise" variant="outlined" onClick={onClickStudentWise} />
              </div>
            </Stack>
          </div>
          <Divider />
          <div className="card-main-body">
            <form>
              <Grid className="CardContainer" container py={2} spacing={3}>
                <Grid item lg={6} xs={12}>
                  <Typography variant="subtitle1" fontSize={12} color="GrayText">
                    Select Year
                  </Typography>
                  <Autocomplete
                    options={YEAR_SELECT}
                    renderInput={(params) => <TextField {...params} placeholder="Select" />}
                  />
                </Grid>
                <Grid item lg={6} xs={12}>
                  <Typography variant="subtitle1" fontSize={12} color="GrayText">
                    Select Class
                  </Typography>
                  <Autocomplete
                    options={CLASS_SELECT}
                    renderInput={(params) => <TextField {...params} placeholder="Select" />}
                  />
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="subtitle1" fontSize={12} color="GrayText">
                    Select Exam
                  </Typography>
                  <Autocomplete
                    multiple
                    limitTags={5}
                    id="multiple-limit-tags"
                    options={['Annual', 'Quaterly', 'Half-Yearly', 'Jun Monthly', 'Feb Monthly']}
                    getOptionLabel={(option) => option}
                    renderInput={(params) => <TextField {...params} placeholder="Select" />}
                  />
                </Grid>
              </Grid>
            </form>
          </div>
          <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 3 }}>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="secondary" sx={{ fontFamily: typography.fontFamily }}>
                Cancel
              </Button>
              <Button variant="contained" color="primary">
                Send To Parents
              </Button>
            </Stack>
          </Box>
        </Card>
      </SendProgressReportRoot>
    </Page>
  );
}

export default SendProgressReport;
