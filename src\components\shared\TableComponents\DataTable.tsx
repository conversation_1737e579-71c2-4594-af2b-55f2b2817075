/* eslint-disable no-nested-ternary */
import { ReactNode, useCallback, useEffect, useRef } from 'react';
import {
  useTheme,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TablePagination,
  TableBody,
  Skeleton,
  Checkbox,
  TextField,
  Typography,
  Box,
  Stack,
  TableFooter,
} from '@mui/material';
import useSettings from '@/hooks/useSettings';
import { FetchStatus } from '@/types/Common';
import NoData from '@/assets/no-datas.png';
import SortableTableCell from './SortableTableCell';

export type DataTableColumn<D> = {
  name: string;
  headerLabel?: string;
  renderHeader?: () => ReactNode;
  sortable?: boolean;
  dataKey?: string;
  renderCell?: (row: D, rowIndex?: number) => ReactNode;
  width?: string;
  align?: string;
  renderFooter?: () => ReactNode;
  footerLabel?: string;
};

export type DataTablePaginationProps = {
  rowsPerPageOptions?: number[];
  totalRecords: number;
  pageNumber: number;
  pageSize: number;
  onPageChange: (event: React.MouseEvent<HTMLButtonElement> | null, page: number) => void;
  onRowsPerPageChange?: React.ChangeEventHandler<HTMLTextAreaElement | HTMLInputElement>;
  showPagination?: boolean;
  showFirstButton?: boolean;
  showLastButton?: boolean;
};

type ArrayStateProps = {
  rowIndex: number;
  columnIndex: number;
  value: string;
};

export type DataTableProps<D> = {
  columns: DataTableColumn<D>[];
  data: D[];
  allowPagination?: boolean;
  allowSorting?: boolean;
  sortColumn?: string;
  sortDirection?: 'asc' | 'desc';
  onSort?: (columnName: string) => void;
  headerClassName?: string;
  PaginationProps?: DataTablePaginationProps;
  fetchStatus?: FetchStatus;
  getRowKey: (item: D, index?: number) => React.Key | null | undefined;
  deletingRecords?: (string | number)[];
  onCheckboxClick?: any;
  ShowCheckBox?: boolean | undefined;
  setSelectedRows?: any;
  selectedRows?: any;
  RowSelected?: boolean;
  isCancell?: any;
  textAlign?: string;
  tableWidth?: number;
  tableStyles?: {} | undefined;
  tableCellStyles?: {} | undefined;
  respnseResults?: {} | undefined;
  tableRowSelect?: boolean;
  disabledCheckBox?: any[];
  isSubmitting?: boolean;
  footerColumn?: any;
  showHorizontalScroll?: boolean;
  tableCellSkeltonStyle?: {} | undefined;
  arrayState?: any;
  TableBodyHeight?: number | string;
  hoverDisable?: boolean;
  CheckboxHeaderStyle?: {} | undefined;
  CheckboxBodyStyle?: {} | undefined;
};

function DataTable<D>({
  columns,
  data,
  getRowKey,
  allowPagination = false,
  allowSorting = false,
  ShowCheckBox = false,
  sortColumn,
  sortDirection = 'asc',
  onSort,
  headerClassName,
  fetchStatus,
  PaginationProps,
  deletingRecords,
  setSelectedRows,
  selectedRows,
  RowSelected,
  textAlign,
  tableWidth,
  tableStyles,
  tableCellStyles,
  tableRowSelect,
  disabledCheckBox,
  isSubmitting,
  footerColumn,
  showHorizontalScroll,
  onCheckboxClick,
  tableCellSkeltonStyle,
  arrayState,
  TableBodyHeight,
  hoverDisable,
  CheckboxHeaderStyle,
  CheckboxBodyStyle,
}: DataTableProps<D>) {
  const tableRef = useRef<any>(null);
  const theme = useTheme();
  const isLight = useSettings().themeMode === 'light';
  // const [selected, setSelected] = useState<D[]>(selectedRows);
  const isSelected = (row: D) => selectedRows?.indexOf(row) !== -1;

  const handleRowClick = (row: D) => {
    // Check if the clicked row is disabled
    const rowIndex = data.findIndex((item) => item === row);
    const isRowDisabled = disabledCheckBox && disabledCheckBox[rowIndex];

    if (!isRowDisabled) {
      const selectedIndex = selectedRows?.indexOf(row);
      let newSelected: D[] = [];

      if (selectedIndex === -1) {
        newSelected = newSelected.concat(selectedRows, row);
        if (typeof arrayState === 'function') {
          arrayState((prev: ArrayStateProps[]) => prev.filter((cell: ArrayStateProps) => cell.rowIndex !== rowIndex));
        }
      } else if (selectedIndex === 0) {
        newSelected = newSelected.concat(selectedRows.slice(1));
      } else if (selectedIndex === selectedRows.length - 1) {
        newSelected = newSelected.concat(selectedRows.slice(0, -1));
      } else if (selectedIndex > 0) {
        newSelected = newSelected.concat(selectedRows.slice(0, selectedIndex), selectedRows.slice(selectedIndex + 1));
      }

      setSelectedRows(newSelected);
      if (onCheckboxClick) {
        onCheckboxClick(newSelected);
      }
    }
  };
  const handleRowAllClick = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      if (typeof arrayState === 'function') {
        arrayState([]);
      }
      // Filter out disabled rows before setting selected rows
      const enabledRows = data.filter((row, index) => !disabledCheckBox || !disabledCheckBox[index]);
      setSelectedRows(enabledRows);
      if (onCheckboxClick) {
        onCheckboxClick(enabledRows);
      }
    } else {
      setSelectedRows([]);
      if (onCheckboxClick) {
        onCheckboxClick([]);
      }
    }
  };
  localStorage.setItem('absenteesData', JSON.stringify(selectedRows));

  useEffect(() => {
    if (tableRef.current) {
      tableRef.current.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    }
  }, [PaginationProps?.pageNumber, PaginationProps?.pageSize]);

  const handleSort = useCallback(
    (col: string) => {
      onSort?.(col);
    },
    [onSort]
  );

  if (allowPagination && !PaginationProps) {
    throw new Error("PaginationProps must be passed when 'allowPagination' is set to true");
  }

  let tableBodyContent: ReactNode = null;

  if (fetchStatus === 'loading') {
    const rowCount = allowPagination ? PaginationProps?.pageSize : data.length;
    tableBodyContent = Array.from(Array(rowCount).keys()).map((index) => (
      <TableRow key={index}>
        {ShowCheckBox && (
          <TableCell padding="checkbox">
            <Skeleton variant="text" />
          </TableCell>
        )}
        {columns.map((column) => (
          <TableCell key={`Row${index}_${column.name}`}>
            <Skeleton variant="text" />
          </TableCell>
        ))}
      </TableRow>
    ));
  }

  if (fetchStatus === 'success') {
    tableBodyContent = data.map((item, rowIndex: number) => {
      const rowkey = getRowKey(item, rowIndex);
      let isDeleting = false;
      if (rowkey && deletingRecords && deletingRecords.length > 0) {
        isDeleting = deletingRecords.includes(rowkey);
      }
      const isRowSelected = isSelected(item);

      return (
        <TableRow
          hover={hoverDisable ?? true}
          role="checkbox"
          aria-checked={RowSelected && isRowSelected}
          tabIndex={-1}
          selected={RowSelected && isRowSelected}
          key={rowkey}
          sx={{
            opacity: isDeleting ? 0.5 : 1,
            cursor: 'pointer',
            '&:last-child td': {
              borderBottom: '0px',
            },
          }}
        >
          {ShowCheckBox && (
            <TableCell>
              <Checkbox
                sx={CheckboxBodyStyle}
                disabled={isSubmitting ? true : disabledCheckBox && disabledCheckBox[rowIndex]}
                color="primary"
                checked={isRowSelected}
                onClick={() => {
                  handleRowClick(item);
                }}
                inputProps={{ 'aria-labelledby': `checkbox-${item}` }}
              />
            </TableCell>
          )}
          {columns.map((column) => {
            let cellData = null;
            if (column.renderCell) {
              cellData = column.renderCell(item, rowIndex);
            } else if (column.dataKey === 'TextField') {
              cellData = <TextField disabled={!isRowSelected} placeholder={column.name} />;
            } else if (column.dataKey) {
              cellData = (item as Record<string, any>)[column.dataKey];
            }
            return (
              <TableCell
                onClick={
                  column.dataKey === 'TextField'
                    ? undefined
                    : column.dataKey === 'Button'
                    ? undefined
                    : () => tableRowSelect && handleRowClick(item)
                }
                className={textAlign}
                sx={{ textAlign: column.align || 'start' }}
                key={`Row_${rowkey}_${column.name}`}
                width={column.width ? column.width : 'auto'}
              >
                {cellData}
              </TableCell>
            );
          })}
        </TableRow>
      );
    });
  }

  return (
    <Paper className="card-table-container" sx={{ position: 'relative', display: 'flex', justifyContent: 'center' }}>
      {/* <button onClick={handleResponseClick}>handleResponseClick</button> */}
      {data.length !== 0 ? (
        <TableContainer
          sx={{
            '&::-webkit-scrollbar': {
              height: showHorizontalScroll ? '15px' : '',
            },
            '&::-webkit-scrollbar-thumb': {
              backgroundColor: isLight ? '' : theme.palette.common.white,
              borderRadius: '8px', // Optional: round the corners
            },
            '&::-webkit-scrollbar-track': {
              backgroundColor: isLight ? theme.palette.grey[50] : theme.palette.grey[700],
            },
          }}
        >
          <Stack
            ref={tableRef}
            // sx={{
            //   overflowY: 'auto',
            //   maxHeight: '100%',
            //   minWidth: tableWidth,
            //   '&::-webkit-scrollbar': {
            //     height: '15px',
            //   },
            // }}
          >
            <Table stickyHeader size="small" sx={tableStyles}>
              <TableHead className={headerClassName}>
                <TableRow>
                  {ShowCheckBox && (
                    <TableCell>
                      <Checkbox
                        sx={CheckboxHeaderStyle}
                        color="primary"
                        onChange={handleRowAllClick}
                        indeterminate={selectedRows?.length > 0 && selectedRows.length < data.length}
                        checked={selectedRows?.length > 0 && selectedRows.length === data.length}
                      />
                    </TableCell>
                  )}
                  {columns.map((column) => {
                    if (allowSorting && column.sortable) {
                      return (
                        <SortableTableCell
                          columnName={column.name}
                          active={sortColumn === column.name}
                          direction={sortDirection}
                          onClick={handleSort}
                          key={column.name}
                        >
                          {column.renderHeader ? column.renderHeader() : column.headerLabel}
                        </SortableTableCell>
                      );
                    }
                    return (
                      <TableCell
                        className={textAlign}
                        key={column.name}
                        // sx={{ backgroundColor: tableBgColor, tableStyles }}
                        sx={{ ...tableCellStyles, whiteSpace: 'nowrap' }}
                      >
                        {column.renderHeader ? column.renderHeader() : column.headerLabel}
                      </TableCell>
                    );
                  })}
                </TableRow>
              </TableHead>

              <TableBody sx={{ height: TableBodyHeight }}>{tableBodyContent}</TableBody>
              <TableFooter sx={{ height: '100%' }}>
                {/* <TableRow style={{ position: 'sticky', bottom: 0, backgroundColor: theme.palette.grey[300] }}>
                  <TableCell className={textAlign}> </TableCell>
                  <TableCell
                    // sx={{ backgroundColor: tableBgColor, tableStyles }}
                    sx={{ color: theme.palette.common.black }}
                  >
                    Monthly Total
                  </TableCell>
                  {footerColumn?.map((column) => {
                    return (
                      <TableCell
                        key={column.name}
                        // sx={{ backgroundColor: tableBgColor, tableStyles }}
                        sx={{ color: theme.palette.common.black }}

                      >
                        11
                      </TableCell>
                    );
                  })}
                  <TableCell
                    className={textAlign}
                    // sx={{ backgroundColor: tableBgColor, tableStyles }}
                    sx={tableCellStyles}
                  >
                    {' '}
                  </TableCell>
                </TableRow> */}
                {footerColumn}
              </TableFooter>
            </Table>
          </Stack>
        </TableContainer>
      ) : (
        <Box
          display="flex"
          alignItems="center"
          justifyContent="center"
          width="100%"
          height={{ xs: 'calc(100vh - 400px)', sm: 'calc(100vh - 400px)', lg: 'calc(100vh - 100px)' }}
        >
          <Stack direction="column" alignItems="center">
            <img src={NoData} width="150px" alt="" />
            <Typography variant="subtitle2" mt={2} color="GrayText">
              No data found !
            </Typography>
          </Stack>
        </Box>
      )}
      {!!PaginationProps && allowPagination && (
        <TablePagination
          rowsPerPageOptions={PaginationProps.rowsPerPageOptions}
          component="div"
          count={PaginationProps.totalRecords}
          rowsPerPage={PaginationProps.pageSize}
          page={PaginationProps.pageNumber}
          onPageChange={PaginationProps.onPageChange}
          onRowsPerPageChange={PaginationProps.onRowsPerPageChange}
          showFirstButton
          showLastButton
        />
      )}
    </Paper>
  );
}

export default DataTable;
