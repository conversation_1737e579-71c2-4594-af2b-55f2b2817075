/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  Collapse,
  FormControl,
  Avatar,
} from '@mui/material';
import { MdAdd } from 'react-icons/md';
import styled from 'styled-components';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import SearchIcon from '@mui/icons-material/Search';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import DeleteIcon from '@mui/icons-material/Delete';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import Excel from '@/assets/attendance/Excel.svg';
import Pdf from '@/assets/attendance/PDF.svg';
import { STATUS_SELECT } from '@/config/Selection';
import Popup from '@/components/shared/Popup/Popup';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { togglePopup } from '@/store/Layout/popup.slice';
import MembersList from './MembersList';

const StaffMembersRoot = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 40px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

export const data = [
  {
    slNo: 1,
    phoneNumber: '+91 9856773663',
    academicYear: '2023-2024',
    studentName: 'Alex',
    description: 'Welcome to PassDaily - eConnect for Schools and College',
    group: 'Welcome to PassDaily',
  },
  {
    slNo: 2,
    phoneNumber: '+91 9856773663',
    academicYear: '2023-2024',
    studentName: 'Mathew',
    description: 'Keep your password safe',
    group: 'Password',
  },
  {
    slNo: 3,
    phoneNumber: '+91 9856773663',
    academicYear: '2023-2024',
    studentName: 'Peter',
    description: 'PassDaily Team Welcomes you to the eConnect tool for Schools... - eConnect for Schools and College',
    group: 'Message',
  },
  {
    slNo: 4,
    phoneNumber: '+91 9856773663',
    academicYear: '2023-2024',
    studentName: 'Alex Mic',
    description: 'Welcome to PassDaily - eConnect for Schools and College',
    group: 'PassDaily',
  },
  {
    slNo: 5,
    phoneNumber: '+91 9856773663',
    academicYear: '2023-2024',
    studentName: 'john',
    description: 'Keep your password safe',
    group: 'Password',
  },
  {
    slNo: 6,
    phoneNumber: '+91 9856773663',
    academicYear: '2023-2024',
    studentName: 'Micheal',
    description: 'PassDaily Team Welcomes you to the eConnect tool for Schools... - eConnect for Schools and College',
    group: 'Message',
  },
  {
    slNo: 7,
    phoneNumber: '+91 9856773663',
    academicYear: '2023-2024',
    studentName: 'jack',
    description: 'Welcome to PassDaily - eConnect for Schools and College',
    group: 'Welcome',
  },
];

function StaffMembers() {
  const [showFilter, setShowFilter] = useState(false);
  const [popup, setPopup] = useState(false);

  const handlePopupOpen = () => {
    setPopup(true);
  };

  const getRowKey = useCallback((row: any) => row.examId, []);
  const AuthorizeKeywordsColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'slNo',
        dataKey: 'slNo',
        headerLabel: 'Sl.No',
        sortable: true,
      },
      {
        name: 'studentName',
        headerLabel: 'Student Name',
        renderCell: (row) => {
          return (
            <Stack direction="row" gap={1} alignItems="center">
              <Avatar alt="" src="/static/images/avatar/1.jpg" />
              <Typography variant="subtitle2">{row.studentName}</Typography>
            </Stack>
          );
        },
      },
      {
        name: 'phoneNumber',
        dataKey: 'phoneNumber',
        headerLabel: 'Phone Number',
      },
      {
        name: 'group',
        dataKey: 'group',
        headerLabel: 'Group',
      },

      {
        name: 'academicYear',
        dataKey: 'academicYear',
        headerLabel: 'AcademicYear',
      },

      {
        name: 'actions',
        headerLabel: 'Actions',
        renderCell: () => {
          return (
            <Stack direction="row" gap={1}>
              <IconButton size="small" color="error" sx={{ padding: 0.5 }}>
                <DeleteIcon />
              </IconButton>
            </Stack>
          );
        },
      },
    ],
    []
  );

  return (
    <Page title="List">
      <StaffMembersRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17} width="80%">
              Student Members
            </Typography>
            <Box pb={1} sx={{ flexShrink: 0 }}>
              <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                <SearchIcon />
              </IconButton>
              <Button onClick={handlePopupOpen} sx={{ borderRadius: '20px', mr: 2 }} variant="outlined" size="small">
                <MdAdd size="20px" /> Add
              </Button>
            </Box>
          </Stack>
          <Divider />

          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={3} pt={1} container spacing={3} alignItems="end">
                  <Grid item lg="auto" md={6} xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', lg: 250 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Group
                      </Typography>
                      <Autocomplete
                        options={STATUS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select" />}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item lg="auto" md={6} xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', lg: 250 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Autocomplete
                        options={STATUS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select" />}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item lg="auto" xs={12}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Paper className="card-table-container" sx={{ marginTop: '12px' }}>
              <DataTable columns={AuthorizeKeywordsColumns} data={data} getRowKey={getRowKey} fetchStatus="success" />
            </Paper>
          </div>
          <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 2 }}>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="error" sx={{ gap: 1 }} size="medium">
                <img className="icon" src={Pdf} />{' '}
                <Typography color="white" fontSize={12}>
                  Download
                </Typography>
              </Button>
              <Button variant="contained" color="success" sx={{ gap: 1 }} size="medium">
                <img className="icon" src={Excel} />{' '}
                <Typography color="white" fontSize={12}>
                  Download
                </Typography>
              </Button>
            </Stack>
          </Box>
        </Card>
      </StaffMembersRoot>
      <Popup size="lg" state={popup} onClose={() => setPopup(false)} popupContent={<MembersList />} />
    </Page>
  );
}

export default StaffMembers;
