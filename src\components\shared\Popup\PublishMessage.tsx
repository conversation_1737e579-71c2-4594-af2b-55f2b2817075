import { Box, Button, Stack, Typography } from '@mui/material';

export const PublishMessage = ({ message }: any) => {
  return (
    <Stack sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
      <Box
        sx={{
          height: 100,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          color: '#000',
        }}
      >
        <Typography gutterBottom fontWeight={550} textAlign="center" color="secondary" pt={2}>
          {message}
        </Typography>
      </Box>

      <Stack spacing={1} pb={2} direction="row">
        <Button variant="contained" color="secondary" size="medium" sx={{ width: 150, color: '#fff' }}>
          Unpublish
        </Button>
        <Button variant="contained" color="primary" size="medium" sx={{ width: 150 }}>
          Publish
        </Button>
      </Stack>
    </Stack>
  );
};
