import { useState } from 'react';
import {
  Button,
  TextField,
  Typography,
  Box,
  Stack,
  FormControl,
  IconButton,
  useTheme,
  Grid,
  Autocomplete,
} from '@mui/material';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { AlbumImageInfo } from '@/types/Album';
import PrgressUploadImage from '@/components/shared/Progress';
import { TbCloudUpload } from 'react-icons/tb';
import LoadingButton from '@mui/lab/LoadingButton';
import CloseIcon from '@mui/icons-material/Close';

// Remove unused imports: LoadingButton, TbCloudUpload

export type CreateEditStaffAccountFormProps = {
  onSave: (values: AlbumImageInfo) => void;
  onCancel: () => void;
  onClose: () => void;
  albumInfo?: AlbumImageInfo;
  isSubmitting: boolean;
};

function CreateEditStaffAccountForm({ onSave, onCancel, isSubmitting, albumInfo }: CreateEditStaffAccountFormProps) {
  return (
    <Box mt={1} padding="1.5rem">
      <form noValidate>
        <Grid container spacing={3}>
          <Grid item md={6} xs={12}>
            <Typography variant="h6" fontSize={14}>
              Select Staff
            </Typography>
            <Autocomplete
              options={['Male', 'Female']}
              renderInput={(params) => <TextField {...params} placeholder="Select name" />}
            />
          </Grid>
          <Grid item md={6} xs={12}>
            <Typography variant="h6" fontSize={14}>
              Login Name
            </Typography>
            <TextField fullWidth placeholder="Enter name" />
          </Grid>
          <Grid item md={6} xs={12}>
            <Typography variant="h6" fontSize={14}>
              Password
            </Typography>
            <TextField fullWidth placeholder="Enter password" />
          </Grid>
          <Grid item md={6} xs={12}>
            <Typography variant="h6" fontSize={14}>
              Confirm Password
            </Typography>
            <TextField fullWidth placeholder="Retype password" />
          </Grid>
          <Grid item md={6} xs={12}>
            <Typography variant="h6" fontSize={14}>
              Role
            </Typography>
            <Autocomplete
              options={['Male', 'Female']}
              renderInput={(params) => <TextField {...params} placeholder="Select role" />}
            />
          </Grid>
          <Grid item md={6} xs={12}>
            <Typography variant="h6" fontSize={14}>
              Status
            </Typography>
            <Autocomplete
              options={['']}
              renderInput={(params) => <TextField {...params} placeholder="Select status" />}
            />
          </Grid>
        </Grid>
        <Stack spacing={3} direction="row" mt={3} justifyContent="end">
          <Button onClick={onCancel} variant="contained" color="secondary" type="button">
            Cancel
          </Button>
          <LoadingButton
            loading={isSubmitting}
            variant="contained"
            color="primary"
            type="submit"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Saving...' : 'Save'}
          </LoadingButton>
        </Stack>
      </form>
    </Box>
  );
}

export default CreateEditStaffAccountForm;
