import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useAppSelector } from '@/hooks/useAppSelector';
import { fetchSignature } from '@/store/ZoomMeeting/meeting.thunk';
import { TextField, Button } from '@mui/material';
import { useState } from 'react';
import { ZoomMtg } from '@zoom/meetingsdk';

// 1) Point to the CDN for WASM and AV assets

function JoinMeeting() {
  const dispatch = useAppDispatch();
  const status = useAppSelector((s) => {
    const signature = s.meeting.signature as unknown as { status: string } | null;
    return signature && 'status' in signature ? signature.status : 'idle';
  });

  const [meetingNumber, setMeetingNumber] = useState('85273418355');
  const [userName, setUserName] = useState('Ansar');
  const [passWord, setPassWord] = useState('5pw2D8');
  const role = 0;

  // api/zoom.ts (INSECURE - FOR DEMO ONLY)
  const getZoomToken = async () => {
    const CLIENT_ID = 'GpeMTljxS7SHfPTxuY4UkA';
    const CLIENT_SECRET = 'I3h0YcBzvNfvvBRLes3VZMZGUuYCwaUt';
    const proxyUrl = 'https://cors-anywhere.herokuapp.com/'; // Public proxy
    const zoomUrl = 'https://zoom.us/oauth/token';

    const response = await fetch(proxyUrl + zoomUrl, {
      method: 'POST',
      headers: {
        Authorization: `Basic ${btoa(`${CLIENT_ID}:${CLIENT_SECRET}`)}`,
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        grant_type: 'account_credentials',
        account_id: '<EMAIL>',
      }),
    });

    const data = await response.json();
    console.log('Zoom Token:', data.access_token);
    return data.access_token;
  };

  const join = async () => {
    ZoomMtg.setZoomJSLib('https://source.zoom.us/3.13.0/lib', '/av');

    // 2) Preload and prepare the SDK
    ZoomMtg.preLoadWasm();
    ZoomMtg.prepareWebSDK();
    // 3) Load localization (optional)
    ZoomMtg.i18n.load('en-US');

    const signature = await getZoomToken();
    ZoomMtg.init({
      leaveUrl: window.location.origin,
      isSupportAV: true,
      success: () => {
        ZoomMtg.join({
          sdkKey: 'GpeMTljxS7SHfPTxuY4UkA',
          signature,
          meetingNumber,
          userName,
          passWord,
          success: () => console.log('Joined meeting'),
          error: (err) => console.error(err),
        });
      },
      error: (err) => console.error(err),
    });
  };

  return (
    <div style={{ padding: '20px', maxWidth: '400px', margin: 'auto' }}>
      <TextField
        label="Meeting Number"
        value={meetingNumber}
        onChange={(e) => setMeetingNumber(e.target.value)}
        fullWidth
        margin="normal"
      />
      <TextField
        label="User Name"
        value={userName}
        onChange={(e) => setUserName(e.target.value)}
        fullWidth
        margin="normal"
      />
      <TextField
        label="Password"
        value={passWord}
        onChange={(e) => setPassWord(e.target.value)}
        fullWidth
        margin="normal"
      />
      <Button variant="contained" color="primary" onClick={join} disabled={status === 'loading'}>
        {/* {status === 'loading' ? 'Joining…' : 'Join Meeting'} */}
        Join Meeting
      </Button>
    </div>
  );
}

export default JoinMeeting;
