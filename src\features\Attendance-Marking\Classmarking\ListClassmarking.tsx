import {
  Box,
  Stack,
  Typography,
  TableContainer,
  Table,
  TableRow,
  TableBody,
  TableHead,
  TableCell,
  Button,
  IconButton,
} from '@mui/material';
import notApprove from '@/assets/attendance/notApprove.svg';
import RemoveCircleOutlineIcon from '@mui/icons-material/RemoveCircleOutline';
import { StudentProps } from '@/types/AttendanceMarking';
import { useState } from 'react';

type ListProps = {
  absenteesList?: StudentProps[] | undefined;
  open: () => void;
  onClose: () => void;
};

export const ListClassmarking = ({ absenteesList, open, onClose }: ListProps) => {
  const [absentStudents, setAbsentStudent] = useState<StudentProps[] | undefined>(absenteesList);
  const handleRemoveStudent = (rollNumber: number) => {
    const updatedData = absentStudents?.filter((row) => row.rollNo !== rollNumber);
    setAbsentStudent(updatedData);
    console.log('absentStudents', absentStudents);
  };
  const StudentTableList = absentStudents?.map((row) => {
    return (
      <TableRow key={row.rollNo}>
        <TableCell component="th" scope="row">
          {row.rollNo}
        </TableCell>
        {/* <TableCell>{row.name}</TableCell> */}
        {/* <TableCell>{row.status}</TableCell> */}
        <TableCell align="center">
          <IconButton onClick={() => handleRemoveStudent(row.rollNo)}>
            <RemoveCircleOutlineIcon sx={{ color: 'red' }} />
          </IconButton>
        </TableCell>
      </TableRow>
    );
  });
  return (
    <Box>
      {absentStudents && absentStudents?.length > 0 && absentStudents ? (
        <TableContainer>
          <Table sx={{ width: '100%', marginTop: '30px' }} aria-label="customized table">
            <TableHead>
              <TableRow>
                <TableCell>Roll No</TableCell>
                <TableCell>Name</TableCell>
                <TableCell>Mark as</TableCell>
                <TableCell> </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>{StudentTableList}</TableBody>
          </Table>
        </TableContainer>
      ) : (
        <Box
          pt={10}
          sx={{
            width: '100%',
            minHeight: '80vh',
            display: 'flex',
            direction: 'column',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <Box>
            <img src={notApprove} alt="" className="Notsuccess_img_bg" />
            <Typography variant="h6" fontSize={15} pt={2} textAlign="center">
              No data founded.
            </Typography>
          </Box>
        </Box>
      )}

      {absentStudents && absentStudents?.length > 0 && (
        <Box sx={{ justifyContent: 'center', pt: 3, position: 'relative' }}>
          <Stack spacing={2} direction="row" sx={{}}>
            <Button onClick={onClose} fullWidth variant="contained" color="secondary">
              Cancel
            </Button>
            <Button
              onClick={open}
              // onKeyDown={toggleDrawer(false)}
              fullWidth
              variant="contained"
              color="primary"
            >
              Confiirm
            </Button>
          </Stack>
        </Box>
      )}
    </Box>
  );
};
