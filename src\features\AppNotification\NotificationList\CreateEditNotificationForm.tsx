import React, { useState } from 'react';
import {
  Box,
  Button,
  FormControl,
  MenuItem,
  Select,
  Stack,
  TextField,
  InputAdornment,
  Typography,
  Card,
  useTheme,
  CardMedia,
  Tooltip,
  IconButton,
} from '@mui/material';
import { STATUS_OPTIONS } from '@/config/Selection';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { ErrorIcon } from '@/theme/overrides/CustomIcons';
import LoadingButton from '@mui/lab/LoadingButton';
import TextareaField from '@/components/shared/Selections/TextareaField';
import { NotificationDataType } from '@/types/Notification';
import useSettings from '@/hooks/useSettings';
import FilesUpload from '@/components/shared/Selections/FilesUpload';
import Files from '@/assets/NotificationIcons/docsFile.png';
import doc from '@/assets/NotificationIcons/doc.png';
import pdf from '@/assets/NotificationIcons/pdf.png';
import xls from '@/assets/NotificationIcons/xls.png';
import ppt from '@/assets/NotificationIcons/ppt.png';
import CloseIcon from '@mui/icons-material/Close';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { v4 as uuidv4 } from 'uuid';
import { fileUpload } from '@/store/AppNotification/appNotification.thunks';
import SaveIcon from '@mui/icons-material/Save';

export type CreateEditNotificationFormProps = {
  onSave: (values: NotificationDataType, mode: 'create' | 'edit') => void;
  onCancel: (mode: 'create' | 'edit') => void;
  notificationDetails: NotificationDataType;
  isSubmitting: boolean;
  adminId: number | undefined;
};

const CreateEditNotificationTempValidationSchema = Yup.object({
  notificationTitle: Yup.string().required('Please enter notification title'),
  notificationContent: Yup.string().required('Please enter notification content'),
  notificationStatus: Yup.number().oneOf([0, 1], 'Please select status'),

  // .test('exists', 'Class name already used', async (val) => {
  //   try {
  //     if (val) {
  //       const existsResponse = await api.MessageBox.CreateNewMessage(val);
  //       return !existsResponse.data;
  //     }

  //     return true;
  //   } catch {
  //     return true;
  //   }
  // }),
});

export const CreateEditNotificationForm = ({
  notificationDetails,
  onCancel,
  isSubmitting,
  onSave,
  adminId,
}: CreateEditNotificationFormProps) => {
  const mode = notificationDetails.notificationId === 0 ? 'create' : 'edit';

  const theme = useTheme();
  const isLight = useSettings().themeMode === 'light';
  const [uploaded, setUploaded] = useState<File[]>([]);
  const dispatch = useAppDispatch();

  const {
    values: { notificationTitle, notificationContent, notificationStatus },
    handleChange,
    handleBlur,
    handleSubmit,
    setFieldValue,
    touched,
    errors,
  } = useFormik<NotificationDataType>({
    initialValues: {
      notificationId: notificationDetails.notificationId,
      notificationTitle: notificationDetails.notificationTitle,
      notificationContent: notificationDetails.notificationContent,
      notificationFile: '',
      adminId,
      fileCount: 0,
      createdDate: notificationDetails.createdDate,
      notificationStatus: notificationDetails.notificationStatus,
      accademicId: 10,
    },
    validationSchema: CreateEditNotificationTempValidationSchema,
    onSubmit: async (values) => {
      let uploadedFilesDetails = null;
      if (uploaded.length !== 0) {
        const formData = new FormData();
        uploaded.forEach((file) => {
          formData.append('files', file);
        });

        const uploadResponse = await dispatch(
          fileUpload({
            files: formData,
          })
        ).unwrap();

        uploadedFilesDetails = uploadResponse.details;
        console.log('uploadedFilesDetails', uploadedFilesDetails);
      }
      const rest = { ...values, notificationFile: uploadedFilesDetails };
      onSave(rest, mode);
      console.log('values', values);
    },
    validateOnBlur: false,
    // validate: (messageVals) => {
    //   const errorObj: any = {};
    //   messageVals.messageContent.forEach(async (classRow, rowIndex, arr) => {
    //     if (arr.some((x, i) => classRow.className !== '' && x.className === classRow.className && i !== rowIndex)) {
    //       if (!errorObj.classes) {
    //         errorObj.classes = [];
    //       }
    //       errorObj.classes[rowIndex] = {};
    //       errorObj.classes[rowIndex].className = 'Duplicate class name';
    //     }
    //   });
    //   return errorObj;
    // },
  });
  const limit = 3000;
  const renderFileIcon = (file: File) => {
    const fileName = file.name;
    const tooltipText = `${fileName}`;

    if (file.type.startsWith('image/')) {
      return (
        <Tooltip title={tooltipText} arrow>
          <CardMedia component="img" image={URL.createObjectURL(file)} alt="" />
        </Tooltip>
      );
      // eslint-disable-next-line no-else-return
    } else if (file.type.startsWith('video/')) {
      return (
        <Tooltip title={tooltipText} arrow>
          <CardMedia height="100%" component="video" controls src={URL.createObjectURL(file)} />
        </Tooltip>
      );
    } else if (file.name.toLowerCase().endsWith('.pdf')) {
      return (
        <Tooltip title={tooltipText} arrow>
          {/* <CardMedia component="img" width={10} image={pdf} alt="" /> */}
          <img src={pdf} alt="PDF" style={{ height: '100%', cursor: 'pointer' }} />
        </Tooltip>
      );
    } else if (file.name.toLowerCase().endsWith('.doc') || file.name.toLowerCase().endsWith('.docx')) {
      return (
        <Tooltip title={tooltipText} arrow>
          <img src={doc} alt="DOCX" style={{ height: '100%', cursor: 'pointer' }} />
        </Tooltip>
      );
    } else if (file.name.toLowerCase().endsWith('.xls') || file.name.toLowerCase().endsWith('.xlsx')) {
      return (
        <Tooltip title={tooltipText} arrow>
          <img src={xls} alt="XLSX" style={{ height: '100%', cursor: 'pointer' }} />
        </Tooltip>
      );
    } else if (file.name.toLowerCase().endsWith('.ppt') || file.name.toLowerCase().endsWith('.pptx')) {
      return (
        <Tooltip title={tooltipText} arrow>
          <img src={ppt} alt="PPT" style={{ height: '100%', cursor: 'pointer' }} />
        </Tooltip>
      );
    } else {
      return (
        <Tooltip title={tooltipText} arrow>
          <img src={Files} alt="FILES" style={{ height: '100%', cursor: 'pointer' }} />
        </Tooltip>
      );
    }
  };

  const handleRemoveFile = (index: number) => {
    setUploaded((prevFiles) => {
      const newFiles = [...prevFiles];
      newFiles.splice(index, 1);
      return newFiles;
    });
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  };
  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    handleFileChange(event);
  };

  return (
    <Box mt={3}>
      <form noValidate onSubmit={handleSubmit}>
        <Stack
          sx={{
            height: 'calc(100vh - 150px)',
            overflow: 'auto',
            // '&::-webkit-scrollbar': {
            //   width: 0,
            // },
          }}
          direction="column"
        >
          <FormControl>
            <Typography variant="subtitle1" fontSize={12}>
              Notification Title
            </Typography>
            <TextField
              placeholder="Enter title"
              name="notificationTitle"
              value={notificationTitle}
              onBlur={handleBlur}
              onChange={handleChange}
              error={touched.notificationTitle && !!errors.notificationTitle}
              helperText={errors.notificationTitle}
              disabled={isSubmitting}
              InputProps={{
                endAdornment: touched.notificationTitle && !!errors.notificationTitle && <ErrorIcon color="error" />,
              }}
            />
          </FormControl>

          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Notification content
            </Typography>

            <TextareaField
              limit={limit}
              // ShowCharectersCount={messageType}
              placeholder="Enter content..."
              name="notificationContent"
              value={notificationContent}
              onChange={handleChange}
              error={touched.notificationContent && !!errors.notificationContent}
              helperText={touched.notificationContent && errors.notificationContent}
              InputProps={{
                inputProps: {
                  style: { resize: 'vertical', minHeight: '60px', maxHeight: '150px' },
                },
                endAdornment: touched.notificationContent && !!errors.notificationContent && (
                  <InputAdornment position="end">
                    <ErrorIcon color="error" sx={{ mr: 2 }} />
                  </InputAdornment>
                ),
              }}
            />
          </FormControl>
          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              status
            </Typography>
            <Select
              name="notificationStatus"
              value={notificationStatus}
              onChange={handleChange}
              error={touched.notificationStatus && !!errors.notificationStatus}
              disabled={isSubmitting}
            >
              {STATUS_OPTIONS.map((opt) => (
                <MenuItem key={opt.id} value={opt.id}>
                  {opt.name}
                </MenuItem>
              ))}
            </Select>
            {touched.notificationStatus && !!errors.notificationStatus && (
              <Typography color="red" fontSize="12px" ml={2.5} mt={1} variant="subtitle1">
                {errors.notificationStatus}
              </Typography>
            )}
          </FormControl>
          <Box gap={5} mt={2}>
            <Stack width="100%">
              <FormControl fullWidth>
                <Typography variant="subtitle1" fontSize={12}>
                  Upload Files
                </Typography>
                <FilesUpload
                  height={150}
                  name="UploadFiles"
                  accept="/*"
                  multiple
                  setUploaded={setUploaded}
                  onChange={(event) => {
                    setFieldValue('UploadFiles', event.currentTarget.files?.[0]);
                  }}
                />
              </FormControl>
            </Stack>

            <Box mt={2} px={0.3} py={1} display="flex" flexWrap="wrap" gap={1} sx={{ width: '350px' }}>
              {uploaded.map((file, index) => (
                <Card
                  key={uuidv4()}
                  sx={{
                    boxShadow: 3,
                    position: 'relative',
                    height: 100,
                    width: file.type.startsWith('video/') || file.type.startsWith('image/') ? 150 : 'fit-content',
                    p: !file.type.startsWith('video/') && !file.type.startsWith('image/') ? 2 : 0,
                  }}
                >
                  <IconButton
                    size="small"
                    sx={{
                      position: 'absolute',
                      right: 5,
                      top: 5,
                      zIndex: 1,
                      p: 0,
                      border: `1px solid ${theme.palette.grey[300]}`,
                      backgroundColor: theme.palette.common.white,
                      '&:hover': {
                        backgroundColor: theme.palette.error.lighter,
                      },
                    }}
                    onClick={() => handleRemoveFile(index)}
                  >
                    <CloseIcon fontSize="small" sx={{ '&:hover': { color: 'red' } }} />
                  </IconButton>

                  {renderFileIcon(file)}
                </Card>
              ))}
            </Box>
          </Box>
        </Stack>
        <Box sx={{ justifyContent: 'center', pt: 3, position: 'relative' }}>
          <Stack spacing={2} direction="row" sx={{}}>
            <Button onClick={() => onCancel(mode)} fullWidth variant="contained" color="secondary" type="button">
              Cancel
            </Button>
            <LoadingButton
              loading={isSubmitting}
              fullWidth
              variant="contained"
              startIcon={<SaveIcon />}
              color="primary"
              type="submit"
              loadingPosition="start"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Saving...' : 'Save'}
            </LoadingButton>
          </Stack>
        </Box>
      </form>
    </Box>
  );
};
