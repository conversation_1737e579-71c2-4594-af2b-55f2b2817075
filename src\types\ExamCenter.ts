import { FetchStatus } from './Common';
import { PageRequestWithFilters, PagedData, PaginationInfo } from './Pagination';

// =================== Exam Timetable Types =========================

export type ExamTimetableInfo = {
  examId: number;
  accademicYear: string;
  className: string;
  examName: string;
  examSubject: string;
  examDate: string;
  examTime: string;
  status: number;
};
export type ExamListFilters = {
  examName?: string;
  examStatus?: number | null;
};

export type ExamListRequest = PageRequestWithFilters<ExamListFilters>;
export type ExamListPagedData = PagedData<ExamTimetableInfo>;

export type ExamCreateRequest = Omit<ExamTimetableInfo, 'examId'>;

export type ExamCenterState = {
  examList: {
    [key: string]: any;
    status: FetchStatus;
    data: ExamTimetableInfo[];
    error: string | null;
    pageInfo: PaginationInfo;
    sortColumn: string;
    sortDirection: 'asc' | 'desc';
  };
  markRegisterFilters: {
    status: FetchStatus;
    data: MarkRegisterFilterDataType;
    error: string | null;
  };
  markRegisterSubjectFilters: {
    status: FetchStatus;
    data: MarkRegisterFilterSubjectsType[];
    error: string | null;
  };
  markRegisterData: {
    status: FetchStatus;
    data: MarkRegisterDataType[];
    error: string | null;
  };
  markRegisterDataWithCE: {
    status: FetchStatus;
    data: MarkRegisterCEDataType[];
    error: string | null;
  };
  submitting: boolean;
  deletingRecords: Record<number, boolean>;
  error: string | null | undefined;
};

// ------ Exam Select Options Types ------
export type ExamSelect = {
  id: number;
  exam: string;
};

// =================== Student Wise Progress Report Types =========================
export type StuddentWiseProgrssReport = {
  slNo: number;
  subject: string;
  passMark: number;
  outOffMark: number;
  markScored: number;
  grade: string;
};

// =================== Topper Progress Report Types ===============================
export type TopperProgrssReport = {
  slNo: number;
  studentName: string;
  scored: number;
  outOff: number;
  percentage: number;
};
// =================== Grade Wise Progrss Report Types ===============================

export type GradeWiseProgrssReport = {
  slNo: number;
  subjectName: string;
  teacherName: string;
};

export type MarkRegisterFilterDataType = {
  currentAcademicId: number;
  yearList: [
    {
      academicId: number;
      academicTime: string;
    }
  ];
  classList: [
    {
      classId: number;
      className: string;
    }
  ];
  examList: [
    {
      examId: number;
      examName: string;
    }
  ];
};
export type MarkRegisterFilterSubjectsType = {
  subjectId: number;
  subjectName: string;
};

export type MarkRegisterDataType = {
  studentId: number;
  studentRollNo: number;
  studentImage: string;
  studentName: string;
  classId: number;
  className: string;
  academicId: number;
  academicTime: string;
  outoffMark: string;
  passMark: string;
  scoredMark: string;
  scoredGrade: string;
  examId: number;
};

export type MarkRegisterCBSEAddDataType = {
  adminId: number;
  academicId: number;
  classId: number;
  examId: number;
  subjectId: number;
  studentId: number;
  studentRollNo: number;
  outoffMark: string;
  passMark: string;
  markScored: string;
  scoredGrade: string;
  dbResult: string;
  id: number;
};

export type MarkRegisterCEDataType = {
  studentId: number;
  studentRollNo: number;
  studentImage: string;
  studentName: string;
  classId: number;
  className: string;
  academicId: number;
  academicTime: string;
  outoffMarkTE: string;
  passMarkTE: string;
  outoffMarkCE: string;
  passMarkCE: string;
  periodicTest: string;
  multipleAssesment: string;
  portFolio: string;
  subjectEnrichment: string;
  scoredMarkTheory: string;
  scoredGrade: string;
  examId: number;
  markId: number;
};

export type MarkRegisterCBSEwithCEAddDataType = {
  adminId: number;
  academicId: number;
  classId: number;
  examId: number;
  subjectId: number;
  studentId: number;
  studentRollNo: number;
  outoffMarkTE: string;
  passMarkTE: string;
  outoffMarkCE: string;
  passMarkCE: string;
  periodicTest: string | null;
  multipleAssesment: string | null;
  portFolio: string | null;
  subjectEnrichment: string | null;
  scoredMarkTheory: string;
  scoredGrade: string;
  dbResult: string;
  id: number;
};
