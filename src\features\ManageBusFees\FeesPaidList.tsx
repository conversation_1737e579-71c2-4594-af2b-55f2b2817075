/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  Collapse,
  FormControl,
} from '@mui/material';
import styled from 'styled-components';
import SearchIcon from '@mui/icons-material/Search';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { CLASS_SELECT, YEAR_SELECT } from '@/config/Selection';

const FeesPaidListRoot = styled.div`
  padding: 1rem;

  .receipt_btn {
    /* max-width: 10px; */
    padding: 0px 5px 0px 5px;
    font-size: 12px;
  }
  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: 2500px;
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;
const dummyData = [
  {
    slNo: 1,
    rollNo: 1,
    class: 1,
    student: 'Test Student',
    payment: 750,
    date: '04/06/2023',
  },
  {
    slNo: 2,
    rollNo: 2,
    class: 2,
    student: 'Test Student',
    payment: 750,
    date: '04/06/2023',
  },
  {
    slNo: 3,
    rollNo: 3,
    class: 3,
    student: 'Test Student',
    payment: 750,
    date: '04/06/2023',
  },
  {
    slNo: 4,
    rollNo: 4,
    class: 4,
    student: 'Test Student',
    payment: 750,
    date: '04/06/2023',
  },
  {
    slNo: 5,
    rollNo: 5,
    class: 5,
    student: 'Test Student',
    payment: 750,
    date: '04/06/2023',
  },
  {
    slNo: 1,
    rollNo: 1,
    class: 6,
    student: 'Test Student',
    payment: 750,
    date: '04/06/2023',
  },
  {
    slNo: 2,
    rollNo: 2,
    class: 7,
    student: 'Test Student',
    payment: 750,
    date: '04/06/2023',
  },
  {
    slNo: 3,
    rollNo: 3,
    class: 8,
    student: 'Test Student',
    payment: 750,
    date: '04/06/2023',
  },
  {
    slNo: 4,
    rollNo: 4,
    class: 9,
    student: 'Test Student',
    payment: 750,
    date: '04/06/2023',
  },
  {
    slNo: 5,
    rollNo: 5,
    class: 10,
    student: 'Test Student',
    payment: 750,
    date: '04/06/2023',
  },
];
function FeesPaidList() {
  const [showFilter, setShowFilter] = useState(false);

  const getRowKey = useCallback((row: any) => row.examId, []);
  const feesPaidListColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'slNo',
        dataKey: 'slNo',
        headerLabel: 'Sl.No',
        sortable: true,
      },
      {
        name: 'class',
        dataKey: 'class',
        headerLabel: 'Class',
      },
      {
        name: 'rollNo',
        dataKey: 'rollNo',
        headerLabel: 'Roll No.',
      },
      {
        name: 'student',
        dataKey: 'student',
        headerLabel: 'Student',
      },
      {
        name: 'payment',
        dataKey: 'payment',
        headerLabel: 'Payment',
      },
      {
        name: 'date',
        dataKey: 'date',
        headerLabel: 'Date',
      },
      {
        name: 'actions',
        headerLabel: 'Actions',
        renderCell: () => {
          return (
            <Stack direction="row" gap={1}>
              <Button className="receipt_btn" variant="outlined" color="secondary">
                Unpay
              </Button>
              <Button className="receipt_btn" variant="outlined" color="primary">
                Receipt
              </Button>
              <Button className="receipt_btn" variant="outlined" color="primary">
                Con.Receipt
              </Button>
            </Stack>
          );
        },
      },
    ],
    []
  );

  return (
    <Page title="List">
      <FeesPaidListRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17}>
              Bus Term Fees Mapped List
            </Typography>
            <Box pb={1} sx={{ flexShrink: 0 }}>
              <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                <SearchIcon />
              </IconButton>
            </Box>
          </Stack>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={3} alignItems="end">
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 180 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Autocomplete
                        options={YEAR_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select Year" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 180 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Autocomplete
                        options={CLASS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select Class" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 180 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Term
                      </Typography>
                      <Autocomplete
                        options={['Term - 1', 'Term - 2', 'Term - 3', 'Term - 4', 'Term - 5']}
                        renderInput={(params) => <TextField {...params} placeholder="Select Term" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 180 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Bus
                      </Typography>
                      <Autocomplete
                        options={['Bus No. - 1', 'Bus No. - 2', 'Bus No. - 3', 'Bus No. - 4', 'Bus No. - 5']}
                        renderInput={(params) => <TextField {...params} placeholder="Select Bus" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 180 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Bus Stop
                      </Typography>
                      <Autocomplete
                        options={['Palakkad', 'Thrissur', 'Kochi', 'Trivandrum', 'Kozhikkode']}
                        renderInput={(params) => <TextField {...params} placeholder="Select Stop" />}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item lg="auto" xs={12} sm={6}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Paper className="card-table-container" sx={{ marginTop: '12px' }}>
              <DataTable columns={feesPaidListColumns} data={dummyData} getRowKey={getRowKey} fetchStatus="success" />
            </Paper>
          </div>
        </Card>
      </FeesPaidListRoot>
    </Page>
  );
}

export default FeesPaidList;
