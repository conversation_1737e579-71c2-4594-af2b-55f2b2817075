/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  Collapse,
  FormControl,
  Checkbox,
  FormGroup,
  FormControlLabel,
} from '@mui/material';
import styled from 'styled-components';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import SearchIcon from '@mui/icons-material/Search';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import DeleteIcon from '@mui/icons-material/Delete';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import DateSelect from '@/components/shared/Selections/DateSelect';
import { STATUS_SELECT } from '@/config/Selection';
import typography from '@/theme/typography';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import MenuEditDeleteView from '@/components/shared/Selections/MenuEditDeleteView';
import { useTheme } from '@mui/material';
import { Chip } from '@mui/material';

const AuthorizeKeywordsRoot = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 240px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 50px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        /* border: 1px solid #e8e8e9; */
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

export const data = [
  {
    slNo: 1,
    MobileNumber: '100000000',
    Keywords: 'CLS,CONV,PARENTS,STAFF,PTA',
  },
  {
    slNo: 2,
    MobileNumber: '100000000',
    Keywords: 'CLS,CONV,PARENTS,STAFF,PTA',
  },
  {
    slNo: 3,
    MobileNumber: '100000000',
    Keywords: 'CLS,CONV,PARENTS,STAFF,PTA',
  },
  {
    slNo: 4,
    MobileNumber: '100000000',
    Keywords: 'CLS,CONV,PARENTS,STAFF,PTA',
  },
  {
    slNo: 5,
    MobileNumber: '100000000',
    Keywords: 'CLS,CONV,PARENTS,STAFF,PTA',
  },
];

function AuthorizeKeywords() {
  const [drawerOpen, setDrawerOpen] = React.useState<boolean>(false);
  const [showFilter, setShowFilter] = useState(false);
  const [showSend, setShowSend] = useState(true);
  const theme = useTheme();

  const handleOpen = () => {
    setDrawerOpen(true);
  };

  const toggleDrawerClose = useCallback(() => setDrawerOpen(false), []);

  const getRowKey = useCallback((row: any) => row.examId, []);
  const AuthorizeKeywordsColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'mobileNumber',
        dataKey: 'MobileNumber',
        headerLabel: 'Mobile Number',
      },
      {
        name: 'keywords',
        headerLabel: 'Keywords',
        renderCell: () => {
          return (
            <Box display="flex" justifyContent="space-between" flex={1}>
              <Chip size="small" label="CLS" />
              <Chip size="small" label="Conv" />
              <Chip size="small" label="Parent" />
              <Chip size="small" label="Staff" />
            </Box>
          );
        },
      },
      // {
      //   name: 'status',
      //   headerLabel: 'Status',
      //   renderCell: () => {
      //     return <Typography>Published</Typography>;
      //   },
      // },
    ],
    []
  );

  return (
    <Page title="List">
      <AuthorizeKeywordsRoot>
        <Card elevation={1} sx={{ px: { xs: 3, md: 5 }, py: 2, mb: 3 }}>
          <Stack direction="row" spacing={2} alignItems="center">
            <Typography variant="h6" fontSize={17}>
              SMS Keyword Authorization
            </Typography>
            <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowSend((x) => !x)}>
              {showSend ? <ExpandLessIcon /> : <KeyboardArrowRightIcon />}
            </IconButton>
          </Stack>
          <Divider />
          <Collapse in={showSend}>
            <form noValidate>
              <Grid pb={2} pt={1} container rowSpacing={1} columnSpacing={3}>
                <Grid item lg={8} xs={12}>
                  <FormControl sx={{ minWidth: { xs: '100%' } }}>
                    <Typography variant="h6" fontSize={14}>
                      Mobile Number
                    </Typography>
                    <TextField placeholder="Enter Number" />
                  </FormControl>
                </Grid>
                <Grid item lg={4} xs={12} mt="auto" pb="1px">
                  <FormControl sx={{ minWidth: { xs: '100%' } }}>
                    <Typography variant="h6" fontSize={14}>
                      Status
                    </Typography>
                    <Autocomplete
                      options={STATUS_SELECT}
                      renderInput={(params) => <TextField {...params} placeholder="Select Status" />}
                    />
                  </FormControl>
                </Grid>
                <Grid item xs={12} mt={3}>
                  <FormControl sx={{ minWidth: { xs: '100%' } }}>
                    <Typography variant="h6" fontSize={14}>
                      Select Keyword
                    </Typography>
                    <FormGroup>
                      <FormControlLabel control={<Checkbox />} label="CLS" />
                      <FormControlLabel control={<Checkbox />} label="CONV" />
                      <FormControlLabel control={<Checkbox />} label="PARENTS" />
                      <FormControlLabel control={<Checkbox />} label="PTA" />
                      <FormControlLabel control={<Checkbox />} label="STAFF" />
                    </FormGroup>
                  </FormControl>
                </Grid>
              </Grid>
            </form>
            <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' } }}>
              <Stack spacing={2} direction="row">
                <Button variant="contained" color="secondary" sx={{ fontFamily: typography.fontFamily }}>
                  Cancel
                </Button>
                <Button variant="contained" color="primary">
                  Save
                </Button>
              </Stack>
            </Box>
          </Collapse>
        </Card>

        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: 1 }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={19}>
              Keywords Authorized List
            </Typography>
            <Box sx={{ flexShrink: 0 }}>
              <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                <SearchIcon />
              </IconButton>
            </Box>
          </Stack>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pt={1} container spacing={3} alignItems="end" pb={showFilter ? 9 : 2}>
                  <Grid item lg="auto" md={6} xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', lg: 250 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Mobile Number
                      </Typography>
                      <TextField placeholder="Enter Subject" />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" md={6} xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', lg: 250 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        keyword
                      </Typography>
                      <Autocomplete
                        options={STATUS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select" />}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item lg="auto" xs={12}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            {/* <Paper className="card-table-container" sx={{ marginTop: 1 }}>
              <DataTable columns={AuthorizeKeywordsColumns} data={data} getRowKey={getRowKey} fetchStatus="success" />
            </Paper> */}
            <Paper className="card-table-container" sx={{ marginTop: 1 }}>
              <Grid container spacing={2}>
                {data.map((student, rowIndex) => (
                  <Grid item xl={4} lg={6} md={12} sm={6} xs={12} key={rowIndex}>
                    <Card
                      className="student_card"
                      sx={{ p: 2, boxShadow: 0, border: `1px solid ${theme.palette.grey[300]}` }}
                    >
                      <Box flexGrow={1}>
                        {AuthorizeKeywordsColumns.map((item, index) => (
                          <Stack direction="row" key={index}>
                            <Grid container>
                              <Grid item lg={4} xs={6}>
                                <Typography key={item.name} variant="subtitle1" color="GrayText" fontSize={13} mr={2}>
                                  {item.headerLabel}
                                </Typography>
                              </Grid>
                              {item.dataKey ? (
                                <Grid item lg={4} xs={6} mb={0.5} mt="auto">
                                  <Typography variant="h6" fontSize={13}>
                                    {/* {`: ${(student as { [key: string]: any })[item.dataKey ?? '']}`} */}
                                    {item.dataKey
                                      ? `:${' '}${(student as { [key: string]: any })[item.dataKey ?? '']}`
                                      : item && item.renderCell && item.renderCell(student, rowIndex)}
                                  </Typography>
                                </Grid>
                              ) : (
                                item && item.renderCell && item.renderCell(student, rowIndex)
                              )}
                            </Grid>
                          </Stack>
                        ))}
                      </Box>
                      <Stack position="absolute" top={10} right={10}>
                        <MenuEditDeleteView
                          Edit={() => {
                            return 0;
                          }}
                          // Delete={handleClickDelete}
                        />
                      </Stack>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Paper>
          </div>
          {/* <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 2 }}>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="secondary">
                Cancel
              </Button>
              <Button onClick={handleOpen} variant="contained" color="primary">
                Save
              </Button>
            </Stack>
          </Box> */}
        </Card>
      </AuthorizeKeywordsRoot>

      <TemporaryDrawer onClose={toggleDrawerClose} Title="Upload Details" state={drawerOpen} DrawerContent="" />
    </Page>
  );
}

export default AuthorizeKeywords;
