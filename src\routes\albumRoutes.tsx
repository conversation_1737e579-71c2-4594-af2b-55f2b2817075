import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import Loadable from '@/components/shared/Loadable';

const Album = Loadable(lazy(() => import('@/pages/Album')));
const List = Loadable(lazy(() => import('@/features/Album/List/List')));
const PhotoList = Loadable(lazy(() => import('@/features/Album/AlbumPhoto/Photo')));
const VideoList = Loadable(lazy(() => import('@/features/Album/AlbumVideo/Video')));

export const albumRoutes: RouteObject[] = [
  {
    path: '/album',
    element: <Album />,
    children: [
      {
        path: 'full-list',
        element: <List />,
      },
      {
        path: 'photo-list',
        element: <PhotoList />,
      },
      {
        path: 'video-list',
        element: <VideoList />,
      },
    ],
  },
];
