import * as React from 'react';
import Snackbar from '@mui/material/Snackbar';
import { Alert, Button } from '@mui/material';
import { IconButton } from '@mui/material';
import { CloseIcon } from '@/theme/overrides/CustomIcons';

type PositionedSnackbarPropsType = {
  open: boolean;
  onClose: () => void;
  content: string;
  anchorOrigin?: any;
  TransitionComponent?: any;
  action?: any;
  autoHideDuration?: number;
};
export default function PositionedSnackbar({
  open,
  onClose,
  content,
  anchorOrigin,
  TransitionComponent,
  action,
  autoHideDuration,
}: PositionedSnackbarPropsType) {
  return (
    <div>
      <Snackbar
        anchorOrigin={anchorOrigin}
        open={open}
        autoHideDuration={autoHideDuration}
        onClose={onClose}
        TransitionComponent={TransitionComponent}
        // key={vertical + horizontal}
        action={action}
      >
        <Alert severity="success" sx={{ width: '100%' }}>
          {content}
        </Alert>
      </Snackbar>
    </div>
  );
}
