import { breakPointsMinwidth } from '@/config/breakpoints';
import styled from 'styled-components';
import Page from '@/components/shared/Page';
import React, { useEffect, useState } from 'react';
import useAuth from '@/hooks/useAuth';
import ManageFee from '@/pages/ManageFee';
import error404 from '@/assets/404.json';
import { useNavigate } from 'react-router-dom'; // Import useNavigate
import ComponentLoading from '@/components/shared/ComponentLoading';
import Lottie from 'lottie-react';
import { Stack } from '@mui/material';
import api from '@/api';

const TestLoginRoot = styled.div`
  min-height: 100vh;
  padding: 0;
  padding-top: 1.25rem;
  display: flex;
  flex-direction: column;
  gap: 20px;
  justify-content: center;

  @media ${breakPointsMinwidth.lg} {
    padding: 2rem;
  }
`;

function TestLogin() {
  const [parentTestLogin, setParentTestLogin] = useState(false);
  const { loading, loginNew, logout, error, setParentMode, isInitialized, loginMode } = useAuth();
  const navigate = useNavigate(); // Initialize useNavigate

  const queryParams = new URLSearchParams(window.location.search);
  const adminId = queryParams.get('Aid');
  // const adminId = 2;

  useEffect(() => {
    api.Payment.checkLogin(adminId)
      .then((response) => {
        console.log('response::::----', response);
        const { username, password, schoolCode } = response.data;
        const performLogin = async () => {
          await logout();
          await loginNew({ username, password, schoolCode });
          navigate('/manage-fee');
        };

        performLogin();
      })
      .catch((error) => {
        window.location.href = 'http://holy.passdaily.in/';
        console.error(error);
      });
  }, [loginNew, navigate, adminId, logout]);

  return (
    <Page title="TestLogin">
      {loading && <ComponentLoading />}
      {error && (
        <Stack direction="row" alignItems="center" justifyContent="center">
          <Lottie animationData={error404} loop style={{ width: '100%', height: '100%' }} />
        </Stack>
      )}
    </Page>
  );
}

export default TestLogin;
