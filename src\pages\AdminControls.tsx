import React from 'react';
import { Link, useLocation, useOutletContext } from 'react-router-dom';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';
import styled from 'styled-components';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import ManageUpdates from '@/features/AdminControls/ManageUpdates';
import ParentLogin from '@/features/AdminControls/ParentLogin';
import AppDetailsList from '@/features/AdminControls/AppDetailsList';
import AppUpdateMessage from '@/features/AdminControls/AppUpdateMessage';
import StaffLoginList from '@/features/AdminControls/StaffLogin/StaffLoginList';
import Page from '@/components/shared/Page';
import MailTemplate from '@/features/AdminControls/MailTemplate/MailTemplate';
import { useDispatch } from 'react-redux';
import { flushStore } from '@/store/flush.slice';
import { OutletContextType } from '@/types/Common';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const AdminControlsRoot = styled.div`
  min-height: calc(100vh - ${TOP_BAR_HEIGHT});

  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};

  .tab {
    background-color: ${(props) => (props.theme.themeMode === 'light' ? '#fff' : props.theme.palette.grey[800])};
    @media screen and (min-width: 840px) {
      width: 100%;
    }
    @media screen and (max-width: 768px) {
      width: 100%;
    }
  }
  .Mui-selected {
    color: ${(props) => props.theme.palette.primary.main};
  }
`;

function TabPanel({ children, value, index }: TabPanelProps) {
  return (
    <Box
      sx={{ paddingTop: '3rem' }}
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
    >
      {value === index && <Box>{children}</Box>}
    </Box>
  );
}
function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`,
  };
}

function AdminControls() {
  const { topBarHeight } = useOutletContext<OutletContextType>();
  const location = useLocation();
  const [value, setValue] = React.useState(0);

  const dispatch = useDispatch();

  React.useEffect(() => {
    dispatch(flushStore());
    console.log('Flush Store');
  }, [dispatch, value]);

  React.useEffect(() => {
    const currentPath = location.pathname;
    const routePaths = [
      '/admin-controls/send-updates',
      '/admin-controls/staff-login',
      '/admin-controls/parent-login',
      '/admin-controls/app-details',
      '/admin-controls/app-update-message',
      '/admin-controls/mail-template',
    ];
    const index = routePaths.indexOf(currentPath);
    if (index !== -1) {
      setValue(index);
    }
  }, [location]);

  return (
    <Page title="">
      <AdminControlsRoot>
        <Tabs
          className="tab"
          variant="scrollable"
          scrollButtons
          allowScrollButtonsMobile
          value={value}
          aria-label="basic tabs"
          sx={{
            ml: '0px',
            borderBottom: 1,
            borderColor: 'divider',
            position: 'fixed',
             top: `${topBarHeight}px`,
            zIndex: 1,
          }}
        >
          <Tab {...a11yProps(0)} label="Send Updates" component={Link} to="/admin-controls/send-updates" />
          <Tab {...a11yProps(1)} label="Staff Login" component={Link} to="/admin-controls/staff-login" />
          <Tab {...a11yProps(2)} label="Parent Login" component={Link} to="/admin-controls/parent-login" />
          <Tab {...a11yProps(3)} label="App Details" component={Link} to="/admin-controls/app-details" />
          <Tab {...a11yProps(4)} label="App Update Message" component={Link} to="/admin-controls/app-update-message" />
          <Tab {...a11yProps(5)} label="Mail Template" component={Link} to="/admin-controls/mail-template" />
        </Tabs>

        <TabPanel value={value} index={0}>
          <ManageUpdates />
        </TabPanel>
        <TabPanel value={value} index={1}>
          <StaffLoginList />
        </TabPanel>
        <TabPanel value={value} index={2}>
          <ParentLogin />
        </TabPanel>
        <TabPanel value={value} index={3}>
          <AppDetailsList />
        </TabPanel>
        <TabPanel value={value} index={4}>
          <AppUpdateMessage />
        </TabPanel>
        <TabPanel value={value} index={5}>
          <MailTemplate />
        </TabPanel>
      </AdminControlsRoot>
    </Page>
  );
}

export default AdminControls;
