import React, { useMemo, useState } from 'react';
import { Box, Grid, Stack, Typography, Card, useTheme, Chip } from '@mui/material';
import Popup from '@/components/shared/Popup/Popup';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';

import useSettings from '@/hooks/useSettings';
import AutorenewOutlinedIcon from '@mui/icons-material/AutorenewOutlined';
import { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import MenuEditDeleteView from '@/components/shared/Selections/MenuEditDeleteView';
import ViewObjExam from './ViewObjExam';

export const data = [
  {
    SlNo: 1,
    Class: '10 A',
    Subject: 'COMPUTER',
    Exam: 'SECOND MID TERM EXAM',
    Type: 'Objective(MCQ) Type',
    StartTime: '11 Nov 2020 08:15 PM',
    EndTime: '11 Nov 2020 08:45 PM',
    AddedQuestions: '20 Questions',
    Staff: 'Passdaily Support',
    Status: 'Published',
  },
  {
    SlNo: 3,
    Class: '5 B',
    Subject: 'MALAYALAM',
    Exam: 'FIRST TERMINAL EXAMINATION 2020-21',
    Type: 'Objective(MCQ) Type',
    StartTime: '28 Aug 2020 08:15 PM',
    EndTime: '28 Aug 2020 10:00 PM',
    AddedQuestions: '15 Questions',
    Staff: 'Passdaily Support',
    Status: 'Published',
  },
  {
    SlNo: 4,
    Class: '10 C',
    Subject: 'ENGLISH',
    Exam: 'FIRST TERMINAL EXAMINATION 2020-21',
    Type: 'Objective(MCQ) Type',
    StartTime: '27 Aug 2020 08:15 PM',
    EndTime: '27 Aug 2020:10:00 PM',
    AddedQuestions: '15 Questions',
    Staff: 'Passdaily Support',
    Status: 'Published',
  },
  {
    SlNo: 6,
    Class: '7 C',
    Subject: 'COMPUTER',
    Exam: 'FIRST TERMINAL EXAMINATION 2020-21',
    Type: 'Objective(MCQ) Type',
    StartTime: '26 Aug 2020 08:15 PM',
    EndTime: '26 Aug 2020 09:45 PM',
    AddedQuestions: '30 Questions',
    Staff: 'Passdaily Support',
    Status: 'Published',
  },
  {
    SlNo: 9,
    Class: '9 B',
    Subject: 'MATHS',
    Exam: 'FIRST TERMINAL EXAMINATION 2020-21',
    Type: 'Objective(MCQ) Type',
    StartTime: '29 Aug 2020 12:00 PM',
    EndTime: '29 Aug 2020 01:00 PM',
    AddedQuestions: '15 Questions',
    Staff: 'Passdaily Support',
    Status: 'Published',
  },
  {
    SlNo: 11,
    Class: '10 C',
    Subject: 'GK',
    Exam: 'GK MOCK TEST 13',
    Type: 'Objective(MCQ) Type',
    StartTime: '11 Aug 2020 06:00 PM',
    EndTime: '11 Aug 2020 09:00 PM',
    AddedQuestions: '10 Questions',
    Staff: 'Passdaily Support',
    Status: 'Published',
  },
  {
    SlNo: 14,
    Class: '10 A',
    Subject: 'GK',
    Exam: 'GK MOCK TEST 10',
    Type: 'Objective(MCQ) Type',
    StartTime: '06 Aug 2020 06:00 PM',
    EndTime: '06 Aug 2020 09:00 PM',
    AddedQuestions: '10 Questions',
    Staff: 'Passdaily Support',
    Status: 'Published',
  },
];

function ObjExamList({ handleClickView, handleClickCloseView }: any) {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const [view, setView] = useState(false);
  const [Delete, setDelete] = useState(false);
  const [updatedData, setData] = useState(data);

  const handleStatusChange = (index: number) => {
    const copyData = [...updatedData];
    copyData[index].Status = copyData[index].Status === 'Published' ? 'Unpublished' : 'Published';
    setData(copyData);
  };

  // const handleClickView = () => setView(true);
  // const handleClickCloseView = () => setView(false);
  const handleClickDelete = () => setDelete(true);
  const handleClickCloseDelete = () => setDelete(false);

  const ObjExamListColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'Exam',
        headerLabel: 'Exam',
        renderCell: (row) => {
          return (
            <Chip
              size="small"
              label={row.Exam}
              variant="outlined"
              color={row.Status === 'Published' ? 'info' : 'error'}
              sx={{
                border: '0px',
                backgroundColor: isLight ? theme.palette.info.lighter : theme.palette.grey[700],
                color: isLight ? theme.palette.info.darker : theme.palette.grey[100],
              }}
            />
          );
        },
      },
      {
        name: 'Class',
        headerLabel: 'Class & Subject',
        renderCell: (row) => {
          return (
            <Typography variant="h6" fontSize={15}>
              : {row.Class} - {row.Subject}
            </Typography>
          );
        },
      },
      {
        name: 'StartTime',
        dataKey: 'StartTime',
        headerLabel: 'Start Time',
      },

      {
        name: 'EndTime',
        dataKey: 'EndTime',
        headerLabel: 'End Time',
      },
      {
        name: 'Exam',
        headerLabel: 'Exam Type',
        renderCell: (row) => {
          return (
            <Chip
              size="small"
              label={row.Type}
              variant="outlined"
              color="success"
              sx={{
                border: '0px',
                backgroundColor: isLight ? theme.palette.info.lighter : theme.palette.grey[700],
                color: isLight ? theme.palette.info.darker : theme.palette.grey[100],
              }}
            />
          );
        },
      },
      {
        name: 'AddedQuestions',
        dataKey: 'AddedQuestions',
        headerLabel: 'Added Questions',
      },
      {
        name: 'Staff',
        dataKey: 'Staff',
        headerLabel: 'Staff',
      },
    ],
    [isLight, theme]
  );

  return (
    <Box className="card-container" my={2}>
      <Grid container spacing={2}>
        {updatedData.map((student, rowIndex) => (
          <Grid item xl={4} lg={6} md={12} sm={6} xs={12}>
            <Card className="student_card" sx={{ p: 2, boxShadow: 0, border: `1px solid ${theme.palette.grey[300]}` }}>
              <Box display="flex" flexDirection="column">
                <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                  <Chip
                    icon={<AutorenewOutlinedIcon sx={{ transform: 'rotate(180deg)' }} />}
                    size="small"
                    label={student.Status === 'Published' ? 'Click to Unpublish' : 'Click to Publish'}
                    variant="outlined"
                    color="primary"
                    clickable
                    onClick={() => handleStatusChange(rowIndex)}
                  />

                  <Stack direction="row" alignItems="center" gap={1} ml={1}>
                    <Chip
                      size="small"
                      label={student.Status === 'Published' ? ' Published' : 'Unpublished'}
                      variant="outlined"
                      color={student.Status === 'Published' ? 'success' : 'error'}
                      sx={{ border: '0px' }}
                    />
                    <MenuEditDeleteView
                      Edit={() => {
                        return 0;
                      }}
                      Delete={handleClickDelete}
                      View={handleClickView}
                    />
                  </Stack>
                </Box>

                {ObjExamListColumns.map((item) => (
                  <Stack direction="row" ml={1}>
                    <Grid container>
                      <Grid item lg={5} xs={6} mt={1}>
                        <Typography key={item.name} variant="subtitle1" fontSize={13} mr={2}>
                          {item.headerLabel}
                        </Typography>
                      </Grid>
                      <Grid item lg={7} xs={6} mb={0} mt="auto">
                        <Typography
                          variant="h6"
                          fontSize={13}
                          color={item.dataKey === 'meetingLink' ? theme.palette.info.main : ''}
                        >
                          {item.dataKey
                            ? `:${' '}${(student as { [key: string]: any })[item.dataKey ?? '']}`
                            : item && item.renderCell && item.renderCell(student, rowIndex)}
                        </Typography>
                      </Grid>
                    </Grid>
                  </Stack>
                ))}
              </Box>
            </Card>
          </Grid>
        ))}
      </Grid>
      <Popup
        size="xs"
        state={Delete}
        onClose={handleClickCloseDelete}
        popupContent={<DeleteMessage message="Are you sure want to delete?" />}
      />
      {/* <Popup size="lg" state={view} popupContent={<ViewObjExam onClose={() => handleClickCloseView()} />} /> */}
    </Box>
  );
}

export default ObjExamList;
