/* eslint-disable no-prototype-builtins */
/* eslint-disable no-else-return */
/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/alt-text */
import React, { ChangeEvent, FormEvent, useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  Button,
  Typography,
  Card,
  Avatar,
  IconButton,
  Collapse,
  Select,
  MenuItem,
  FormControl,
  useTheme,
  SelectChangeEvent,
  Tooltip,
  Radio,
  Skeleton,
  TextField,
  InputAdornment,
  CircularProgress,
} from '@mui/material';
import NoData from '@/assets/no-datas.png';
import DeleteIcon from '@mui/icons-material/Delete';
import SaveIcon from '@mui/icons-material/Save';
import Success from '@/assets/ManageFee/Success.json';
import Updated from '@/assets/ManageFee/Updated.json';
import UpdateLoading from '@/assets/ManageFee/UpdateLoading.json';
import SaveLoading from '@/assets/ManageFee/SaveLoading.json';
import deleteBin from '@/assets/ManageFee/deleteBin.json';
import errorIcon from '@/assets/ManageFee/Error.json';
import deleteSuccess from '@/assets/ManageFee/deleteSuccess.json';
import studentSuccessIcon from '@/assets/ManageFee/studentSuccessJsonIcon.json';
import studentSelectedIcon from '@/assets/ManageFee/studentSelectedIcon.json';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import SearchIcon from '@mui/icons-material/Search';
import { IoIosArrowUp } from 'react-icons/io';
import styled from 'styled-components';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import useSettings from '@/hooks/useSettings';
import useAuth from '@/hooks/useAuth';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useAppSelector } from '@/hooks/useAppSelector';
import {
  getClassSectionsData,
  getDashboardYearSubmitting,
  getIndividualFeeSettingListData,
  getIndividualFeeSettingListStatus,
  getManageFeeclassListData,
  getYearData,
  getYearStatus,
} from '@/config/storeSelectors';
import { fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import {
  DeleteAllOptionalFee,
  DeleteOptionalFee,
  fetchClassList,
  fetchClassSections,
  fetchGetOptionalFeeSettingsIndividual,
  optionalFeeSettingsIndividual,
} from '@/store/ManageFee/manageFee.thunks';
import {
  BasicFeeMappedDeleteAllDataType,
  FeeMappedType,
  GetOptionalFeeSettingsDataType,
  GetOptionalFeeStudentMapDataType,
  OptionalFeeMappedDeleteAllType,
} from '@/types/ManageFee';
import { SuccessIcon } from '@/theme/overrides/CustomIcons';
import Lottie from 'lottie-react';
import LoadingButton from '@mui/lab/LoadingButton';
import PositionedSnackbar from '@/components/shared/Popup/SnackBar';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import { FEE_TYPE_ID_OPTIONS } from '@/config/Selection';
import DTVirtuoso from '@/components/shared/RND/DataTableVirtuoso';
import CurrencyRupeeIcon from '@mui/icons-material/CurrencyRupee';
import AddIcon from '@mui/icons-material/Add';
import StudentsPickerField from '@/components/shared/Selections/StudentsPicker';

const IndividualFeeSettingRoot = styled.div`
  padding: 1rem;
  @media screen and (max-width: 576px) {
    padding: 0.5rem;
  }
  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 576px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 50px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        /* border: 1px solid ${(props) => props.theme.palette.grey[200]}; */
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }

        .MuiTableCell-root {
          border: 1px solid ${(props) => props.theme.palette.grey[100]};
        }
        .MuiTableCell-root.MuiTableCell-body {
          border: 1px solid ${(props) => props.theme.palette.grey[100]};
        }
        .MuiTableCell-head {
          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? '#CFE1B9' : props.theme.palette.grey[900]};
        }
        @media screen and (min-width: 1200px) {
          .MuiTableCell-head:nth-child(1) {
            z-index: 11;
            position: sticky;
            left: 0;
            width: 50px;
          }
          .MuiTableCell-head:nth-child(2) {
            z-index: 11;
            position: sticky;
            left: 52.5px;
            width: 100px;
          }
          .MuiTableCell-head:nth-child(3) {
            z-index: 11;
            position: sticky;
            left: 164px;
            width: 200px;
          }
          .MuiTableCell-head:last-child {
            z-index: 11;
            position: sticky;
            right: 0px;
            width: 80px;
          }
          .MuiTableCell-root.MuiTableCell-body:nth-child(1) {
            position: sticky;
            left: 0;
            background-color: ${(props) =>
              props.theme.themeMode === 'light' ? '#E9F5DB' : props.theme.palette.grey[900]};
            width: 50px;
            z-index: 1;
          }
          .MuiTableCell-root.MuiTableCell-body:nth-child(2) {
            position: sticky;
            left: 52.5px;
            padding-left: 5px;
            background-color: ${(props) =>
              props.theme.themeMode === 'light' ? '#E9F5DB' : props.theme.palette.grey[900]};
            width: 100px;
            z-index: 1;
          }
          .MuiTableCell-root.MuiTableCell-body:nth-child(3) {
            position: sticky;
            left: 164px;
            background-color: ${(props) =>
              props.theme.themeMode === 'light' ? '#E9F5DB' : props.theme.palette.grey[900]};
            width: 200px;
            z-index: 1;
          }
          .MuiTableCell-root.MuiTableCell-body:last-child {
            position: sticky;
            right: 0px;
            background-color: ${(props) =>
              props.theme.themeMode === 'light' ? '#E9F5DB' : props.theme.palette.grey[800]};
            width: 80px;
            z-index: 1;
          }
        }
        .MuiTableCell-root.MuiTableCell-body {
          padding: 0px;
          height: 100%;
        }

        .MuiTableCell-root:first-child {
          padding-left: 10px;
        }
      }
    }
  }
`;

interface ExtendGetIndividualFeeSettingsDataType extends GetOptionalFeeSettingsDataType {
  studentId?: number;
}

export default function IndividualFeeSetting() {
  const theme = useTheme();
  const isLight = useSettings().themeMode === 'light';
  const { user } = useAuth();
  const { confirm } = useConfirm();
  const dispatch = useAppDispatch();
  const adminId: number = user ? user.accountId : 0;
  const academicId = 10;
  const [showFilter, setShowFilter] = useState(true);

  const YearData = useAppSelector(getYearData);
  const YearSubmitting = useAppSelector(getDashboardYearSubmitting);
  const YearStatus = useAppSelector(getYearStatus);
  const [classSectionsFilter, setClassSectionsFilter] = useState(0);
  const [classFilter, setClassFilter] = useState(0);
  const ClassSectionsData = useAppSelector(getClassSectionsData);
  const classListData = useAppSelector(getManageFeeclassListData);
  // const classListStatus = useAppSelector(getclassListStatus);

  const defaultYear = YearData[0]?.accademicId || 0;
  const [academicYearFilter, setAcademicYearFilter] = useState(defaultYear);
  const [feeTypeFilter, setFeeTypeFilter] = useState(0);

  const individualFeeSettingListData = useAppSelector(getIndividualFeeSettingListData);
  const individualFeeSettingListStatus = useAppSelector(getIndividualFeeSettingListStatus);

  const [optionalFeeData, setOptionalFeeData] = useState<any>([]);
  const [studentsData, setStudentsData] = useState<GetOptionalFeeStudentMapDataType[]>([]);
  const [clickedCells, setClickedCells] = useState<{ studentId: number; feeId: number; value: string }[]>([]);
  const [selectedCells, setSelectedCells] = useState<{ studentId: number; feeId: number }[]>([]);
  const [saving, setSaving] = useState(false);
  const [savingAll, setSavingAll] = useState(false);
  const [individualSaveLoading, setIndividualSaveLoading] = useState<Record<string, boolean>>({});
  const [succesResponse, setSuccesResponse] = useState('');
  const [showSuccessIcon, setShowSuccessIcon] = useState<{ [key: string]: boolean }>({});
  const [individualSaveButtonEnabled, setIndividualSaveButtonEnabled] = useState<any>([]);
  const [showErrorIcon, setShowErrorIcon] = useState<{ [key: string]: boolean }>({});
  const [checkedRows, setCheckedRows] = useState<{ [key: string]: boolean }>({});
  const [rowId, setRowId] = useState<{ [key: string]: number }>({});
  const [saveAllButtonDisabled, setSaveAllButtonDisabled] = useState<boolean>(true);
  const [snackBar, setSnackBar] = useState<boolean>(false);
  const [selectedRows, setSelectedRows] = useState<ExtendGetIndividualFeeSettingsDataType[]>([]);
  const [selectedStudentIds, setSelectedStudentIds] = useState<string>('');

  const initialOptionalFeeRequest = React.useMemo(
    () => ({
      adminId,
      academicId: academicYearFilter,
      feeTypeId: feeTypeFilter,
      sectionId: 0,
      classId: 0,
    }),
    [adminId, academicYearFilter, feeTypeFilter]
  );
  const currentOptionalFeeRequest = React.useMemo(
    () => ({
      adminId,
      academicId: academicYearFilter,
      feeTypeId: feeTypeFilter,
      sectionId: classSectionsFilter,
      classId: classFilter,
    }),
    [adminId, academicYearFilter, classSectionsFilter, classFilter, feeTypeFilter]
  );

  const loadIndividualFeeList = useCallback(
    async (request: { adminId: number; academicId: number; sectionId: number; classId: number; feeTypeId: number }) => {
      try {
        setSelectedCells([]);
        const data: any = await dispatch(fetchGetOptionalFeeSettingsIndividual(request)).unwrap();
        console.log('Individual fee data::::', data);
        if (data) {
          setOptionalFeeData(data);
          const studentsMap = data.studentsMapped
            ? data.studentsMapped.map((item: GetOptionalFeeStudentMapDataType) => ({ ...item }))
            : [];
          setStudentsData(studentsMap);
          console.log('studentsMap::::', studentsMap);
        }
      } catch (error) {
        console.error('Error loading Individual fee list:', error);
      }
    },
    [dispatch]
  );

  React.useEffect(() => {
    if (individualFeeSettingListStatus === 'idle') {
      loadIndividualFeeList(currentOptionalFeeRequest);
    }
    dispatch(fetchYearList(adminId));
    dispatch(fetchClassList({ adminId, academicId, sectionId: classSectionsFilter }));
    dispatch(fetchClassSections({ adminId, academicId }));

    setOptionalFeeData(individualFeeSettingListData);
    console.log('optionalFeeData::::----', optionalFeeData);
    console.log('selectedCells::::----', selectedCells);
    console.log('rowId::::----', rowId);
    console.log('individualSaveButtonEnabled::::----', individualSaveButtonEnabled);
  }, [
    dispatch,
    adminId,
    classSectionsFilter,
    selectedCells,
    optionalFeeData,
    individualFeeSettingListData,
    individualFeeSettingListStatus,
    academicYearFilter,
    rowId,
    individualSaveButtonEnabled,
    loadIndividualFeeList,
    currentOptionalFeeRequest,
  ]);

  const handleYearChange = (e: SelectChangeEvent) => {
    setAcademicYearFilter(YearData.filter((item) => item.accademicId === e.target.value)[0].accademicId);
    loadIndividualFeeList({ ...currentOptionalFeeRequest, academicId: parseInt(e.target.value, 10) });
    setClassFilter(0);
    setSelectedCells([]);
  };

  const handleFeeTypeChange = (e: SelectChangeEvent) => {
    const feeTypeId = parseInt(e.target.value, 10);
    setFeeTypeFilter(feeTypeId);
    loadIndividualFeeList({ ...currentOptionalFeeRequest, feeTypeId: parseInt(e.target.value, 10) });
  };

  const handleClassSectionChange = (e: SelectChangeEvent) => {
    setClassSectionsFilter(
      parseInt(ClassSectionsData.filter((item) => item.sectionId === e.target.value)[0].sectionId, 10)
    );
    loadIndividualFeeList({ ...currentOptionalFeeRequest, sectionId: parseInt(e.target.value, 10) });
    setClassFilter(0);
    setSelectedCells([]);
  };

  const handleClassChange = (e: SelectChangeEvent) => {
    setClassFilter(parseInt(classListData.filter((item) => item.classId === e.target.value)[0].classId, 10));
    loadIndividualFeeList({ ...currentOptionalFeeRequest, classId: parseInt(e.target.value, 10) });
    setSelectedCells([]);
  };

  const selectedIdsArray = String(selectedStudentIds || '')
    .split(',')
    .filter(Boolean)
    .map((id) => Number(id));

  const filteredData =
    selectedIdsArray.length > 0 ? studentsData.filter((row) => selectedIdsArray.includes(row.studentId)) : studentsData;

  const handleSave = useCallback(
    async (row: GetOptionalFeeStudentMapDataType) => {
      try {
        setSaving(true);
        // const feeId = row.feeId;
        const { studentId } = row;
        console.log('row::::----', row);

        setIndividualSaveLoading((prevMap) => ({ ...prevMap, [row.studentId]: true }));

        // Construct feeAmount array based on section amounts
        const FeeArray = Object.keys(rowId)
          .filter((key) => parseInt(key.split('_')[0], 10) === studentId) // Filter objects where feeId matches row.feeId
          .map((key) => ({
            feeId: parseInt(key.split('_')[1], 10),
            dbResult: 'string',
            optionalMapId: 0,
          }));

        const sendReq = [
          {
            adminId,
            accademicId: academicYearFilter,
            academicFeeId: 0,
            sectionId: classSectionsFilter,
            classId: classFilter,
            studentId,
            feeTypeId: feeTypeFilter,
            optionalFee: FeeArray,
          },
        ];
        const actionResult = await dispatch(optionalFeeSettingsIndividual(sendReq));

        if (actionResult && Array.isArray(actionResult.payload)) {
          const results = actionResult.payload;
          console.log('results::::----', results);
          // Filter out items without termAmount property
          const filteredResults = results.filter((f) => f.optionalFee);
          console.log('filteredResults::::----', filteredResults);

          // Extract termAmount arrays
          const optionalFeeArray = filteredResults.map((f) => f.optionalFee);

          // Flatten the array of termAmount arrays
          const optionalFeeArr = optionalFeeArray.flat();
          console.log('optionalFeeArr::::----', optionalFeeArr);

          // Find the item with dbResult === 'Success'
          const SuccessResult = optionalFeeArr.find((f) => f.dbResult === 'Success');
          console.log('SuccessResult::::----', SuccessResult);
          if (SuccessResult) {
            setSuccesResponse(SuccessResult.dbResult);
          }

          // Find the item with dbResult === 'Updated'
          const UpdateResult = optionalFeeArr.find((f) => f.dbResult === 'Updated');
          console.log('UpdateResult::::----', UpdateResult);
          if (UpdateResult) {
            setSuccesResponse(UpdateResult.dbResult);
          }
          setSaving(false);
          // setTermFeeDetails(response);
          setIndividualSaveLoading((prevMap) => ({ ...prevMap, [row.studentId]: false }));
          // setIndividualSaveButtonEnabled((prevEnabled) => ({
          //   ...prevEnabled,
          //   [studentId]: false, // Enable the button again after the operation completes
          // }));

          // // Update termFeeMapped for the current row
          // const updatedOptionalFeeData = optionalFeeData.map((item) => {
          //   if (item.studentId === studentId) {
          //     // Push each item of optionalFeeArr separately to feeMapped
          //     const updatedfeeMapped = [...item.feeMapped, ...optionalFeeArr];
          //     return { ...item, feeMapped: updatedfeeMapped };
          //   }
          //   return item;
          // });
          // setOptionalFeeData(updatedOptionalFeeData);
          // console.log('updatedOptionalFeeData::::----', updatedOptionalFeeData);

          // Update checkedRows when the row is unchecked
          // const updatedCheckedRows = { ...checkedRows };
          // if (checkedRows[row.feeId]) {
          //   delete updatedCheckedRows[row.feeId];
          // }
          // setCheckedRows(updatedCheckedRows);

          loadIndividualFeeList({
            ...currentOptionalFeeRequest,
            academicId: academicYearFilter,
            adminId,
            sectionId: classSectionsFilter,
            classId: classFilter,
          });
          setShowSuccessIcon((prevMap) => ({ ...prevMap, [row.studentId]: true }));
          setTimeout(() => {
            setShowSuccessIcon((prevMap) => ({ ...prevMap, [row.studentId]: false }));
          }, 5000);
        }
      } catch (error) {
        // Handle errors here
        // await showConfirmation(<ErrorMessage icon={ErrorMsg} message="Message content already created" />, '');
      }
    },
    [
      dispatch,
      adminId,
      academicYearFilter,
      classSectionsFilter,
      classFilter,
      rowId,
      currentOptionalFeeRequest,
      loadIndividualFeeList,
      feeTypeFilter,
    ]
  );
  const handleAllSave = useCallback(async () => {
    try {
      setSavingAll(true);

      const feeIdArray = Object.keys(rowId).map((key) => parseInt(key.split('_')[0], 10));

      // Ensure feeIdArray contains unique values
      const uniqueFeeIds = [...new Set(feeIdArray)];
      await Promise.all(
        uniqueFeeIds.map(async (studentId) => {
          // Construct feeAmount array based on section amounts
          const FeeArray = Object.keys(rowId)
            .filter((key) => parseInt(key.split('_')[0], 10) === studentId) // Filter objects where feeId matches row.feeId
            .map((key) => ({
              feeId: parseInt(key.split('_')[1], 10),
              dbResult: 'string',
              optionalMapId: 0,
            }));

          const sendReq = [
            {
              adminId,
              accademicId: academicYearFilter,
              academicFeeId: 0,
              sectionId: classSectionsFilter,
              classId: classFilter,
              studentId,
              feeTypeId: feeTypeFilter,
              optionalFee: FeeArray,
            },
          ];
          const actionResult = await dispatch(optionalFeeSettingsIndividual(sendReq));

          if (actionResult && Array.isArray(actionResult.payload)) {
            setSnackBar(true);
            const results = actionResult.payload;
            console.log('results::::----', results);
            // Filter out items without termAmount property
            const filteredResults = results.filter((f) => f.optionalFee);
            console.log('filteredResults::::----', filteredResults);

            // Extract termAmount arrays
            const optionalFeeArray = filteredResults.map((f) => f.optionalFee);

            // Flatten the array of termAmount arrays
            const optionalFeeArr = optionalFeeArray.flat();
            console.log('optionalFeeArr::::----', optionalFeeArr);

            // Find the item with dbResult === 'Success'
            const SuccessResult = optionalFeeArr.find((f) => f.dbResult === 'Success');
            console.log('SuccessResult::::----', SuccessResult);
            if (SuccessResult) {
              setSuccesResponse(SuccessResult.dbResult);
            }

            // Find the item with dbResult === 'Updated'
            const UpdateResult = optionalFeeArr.find((f) => f.dbResult === 'Updated');
            console.log('UpdateResult::::----', UpdateResult);
            if (UpdateResult) {
              setSuccesResponse(UpdateResult.dbResult);
            }
            setSavingAll(false);
            setIndividualSaveButtonEnabled([]);
            setSaveAllButtonDisabled(true);

            // // Update termFeeMapped for the current row
            // const updatedOptionalFeeData = optionalFeeData.map((item) => {
            //   if (item.studentId === studentId) {
            //     // Push each item of optionalFeeArr separately to feeMapped
            //     const updatedfeeMapped = [...item.feeMapped, ...optionalFeeArr];
            //     return { ...item, feeMapped: updatedfeeMapped };
            //   }
            //   return item;
            // });
            // setOptionalFeeData(updatedOptionalFeeData);
            // console.log('updatedOptionalFeeData::::----', updatedOptionalFeeData);

            // Update checkedRows when the row is unchecked
            // const updatedCheckedRows = { ...checkedRows };
            // if (checkedRows[row.feeId]) {
            //   delete updatedCheckedRows[row.feeId];
            // }
            // setCheckedRows(updatedCheckedRows);

            loadIndividualFeeList({
              ...currentOptionalFeeRequest,
              academicId: academicYearFilter,
              adminId,
              sectionId: classSectionsFilter,
              classId: classFilter,
            });
          }
        })
      );
    } catch (error) {
      // Handle errors here
      // await showConfirmation(<ErrorMessage icon={ErrorMsg} message="Message content already created" />, '');
    }
  }, [
    dispatch,
    adminId,
    academicYearFilter,
    classSectionsFilter,
    classFilter,
    rowId,
    currentOptionalFeeRequest,
    loadIndividualFeeList,
    feeTypeFilter,
  ]);
  type RowIds = {
    [key: string]: number;
  };
  type EnabledState = {
    [studentId: string]: string[];
  };
  // const handleCellClick = useCallback(
  //   async (studentId: number, feeId: number) => {
  //     const cellKey: any = `${studentId}_${feeId}`;

  //     // Check if cell is already selected
  //     const cellIndex = selectedCells.findIndex((cell) => cell.studentId === studentId && cell.feeId === feeId);

  //     if (cellIndex !== -1) {
  //       // Cell a lready selected, deselect it
  //       setSelectedCells((prevSelectedCells) =>
  //         prevSelectedCells.filter((cell) => !(cell.studentId === studentId && cell.feeId === feeId))
  //       );
  //       setRowId((prev) => {
  //         const updatedIds: RowIds = { ...prev };
  //         delete updatedIds[cellKey];
  //         return updatedIds;
  //       });
  //       setIndividualSaveButtonEnabled((prev: any) => {
  //         const updatedEnabled: EnabledState = { ...prev };
  //         if (updatedEnabled[studentId]) {
  //           updatedEnabled[studentId] = updatedEnabled[studentId].filter((key) => key !== cellKey);
  //           if (updatedEnabled[studentId].length === 0) {
  //             delete updatedEnabled[studentId]; // Remove studentId array if it becomes empty
  //           }
  //         }
  //         if (updatedEnabled) {
  //           const isSaveAllButtonDisabled = Object.keys(updatedEnabled).length === 0;
  //           setSaveAllButtonDisabled(isSaveAllButtonDisabled);
  //         }
  //         return updatedEnabled;
  //       });
  //     } else {
  //       setSelectedCells((prevSelectedCells) => [...prevSelectedCells, { studentId, feeId }]);
  //       // Cell not selected, select it
  //       setRowId((prev) => {
  //         const updatedIds: RowIds = { ...prev };
  //         updatedIds[cellKey] = cellKey;
  //         return updatedIds;
  //       });
  //       setIndividualSaveButtonEnabled((prevEnabled: any) => {
  //         const updatedEnabled = { ...prevEnabled };
  //         if (!updatedEnabled[studentId]) {
  //           updatedEnabled[studentId] = []; // Initialize as an empty array if not exists
  //         }
  //         if (!updatedEnabled[studentId].includes(cellKey)) {
  //           updatedEnabled[studentId].push(cellKey); // Add cellKey to the array
  //           if (updatedEnabled) {
  //             const isSaveAllButtonDisabled = Object.keys(updatedEnabled).length === 0;
  //             setSaveAllButtonDisabled(isSaveAllButtonDisabled);
  //           }
  //         }
  //         return updatedEnabled;
  //       });
  //       // Enable the Save All button
  //     }
  //     // Check if any cells are selected
  //   },
  //   [setSelectedCells, selectedCells]
  // );

  const handleCellClick = useCallback(
    (row: any, feeId: number) => {
      const { studentId, termFeeMapped, amount } = row;

      setClickedCells((prevClickedCells) => [...prevClickedCells, { studentId, feeId }]);
    },
    [setClickedCells] // Dependencies
  );

  const handleDeleteCell = useCallback(
    async (optionalMapId: number) => {
      const sendConfirmMessage = (
        <DeleteMessage
          jsonIcon={deleteBin}
          message={
            <div style={{ color: theme.palette.error.main }}>
              Are you sure you want to delete the cell &quot;{}&quot; ?
            </div>
          }
        />
      );

      if (await confirm(sendConfirmMessage, 'Delete Row?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
        const sendReq = [
          {
            adminId,
            accademicId: academicYearFilter,
            optionalMapId,
            dbResult: '',
            deletedId: 0,
          },
        ];

        console.log('sendReq', sendReq);

        const deleteResponse = await dispatch(DeleteOptionalFee(sendReq));
        console.log('deleteResponse::::----', deleteResponse);
        if (deleteResponse && Array.isArray(deleteResponse.payload)) {
          const results = deleteResponse.payload;
          const errorMessages = results.find((result) => result.dbResult === 'Failed');
          const successMessages = results.find((result) => result.dbResult === 'Deleted');

          if (!errorMessages) {
            const deleteSuccessMessage = (
              <SuccessMessage loop={false} jsonIcon={deleteSuccess} message="Delete successfully" />
            );
            await confirm(deleteSuccessMessage, 'Success', { okLabel: 'Ok', showOnlyOk: true });
            loadIndividualFeeList(currentOptionalFeeRequest);
          } else if (!successMessages) {
            const deleteErrorMessage = <DeleteMessage loop={false} jsonIcon={errorIcon} message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          } else {
            const deleteErrorMessage = <DeleteMessage loop={false} jsonIcon={errorIcon} message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          }
          loadIndividualFeeList(currentOptionalFeeRequest);
        }
      }
    },
    [confirm, dispatch, adminId, academicYearFilter, theme, loadIndividualFeeList, currentOptionalFeeRequest]
  );

  const handleDeleteRow = useCallback(
    async (row: GetOptionalFeeStudentMapDataType) => {
      const { studentId } = row;
      console.log('row::::----', row);
      // const fineMappedIds = row.filter((f) => f.feeId === term.feeId).map((m) => m.fineMappedId);

      const sendConfirmMessage = (
        <DeleteMessage
          jsonIcon={deleteBin}
          message={
            <div style={{ color: theme.palette.error.main }}>
              Are you sure you want to delete the row &quot;{}&quot; ?
            </div>
          }
        />
      );

      if (await confirm(sendConfirmMessage, 'Delete Row?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
        const sendReq = [
          {
            adminId,
            accademicId: academicYearFilter,
            classId: classFilter,
            studentId,
            dbResult: 'string',
            deletedId: 0,
          },
        ];

        console.log('sendReq', sendReq);

        const deleteResponse = await dispatch(DeleteAllOptionalFee(sendReq));
        console.log('deleteResponse::::----', deleteResponse);
        if (deleteResponse && Array.isArray(deleteResponse.payload)) {
          const results = deleteResponse.payload;
          const errorMessages = results.find((result) => result.dbResult === 'Failed');
          const successMessages = results.find((result) => result.dbResult === 'Success');

          if (!errorMessages) {
            const deleteSuccessMessage = (
              <SuccessMessage loop={false} jsonIcon={deleteSuccess} message="Delete successfully" />
            );
            await confirm(deleteSuccessMessage, 'Success', { okLabel: 'Ok', showOnlyOk: true });
            loadOptionalFeeList(currentOptionalFeeRequest);
          } else if (!successMessages) {
            const deleteErrorMessage = <DeleteMessage loop={false} jsonIcon={errorIcon} message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          } else {
            const deleteErrorMessage = <DeleteMessage loop={false} jsonIcon={errorIcon} message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          }
          loadOptionalFeeList(currentOptionalFeeRequest);
        }
      }
    },
    [confirm, dispatch, adminId, academicYearFilter, classFilter, theme, loadOptionalFeeList, currentOptionalFeeRequest]
  );

  const handleReset = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      loadIndividualFeeList(initialOptionalFeeRequest);
      setAcademicYearFilter(0);
      setClassSectionsFilter(0);
      setFeeTypeFilter(0);
      setClassFilter(0);
      setSelectedStudentIds('');
    },
    [initialOptionalFeeRequest, loadIndividualFeeList]
  );

  const handleDeleteMultiple = useCallback(async () => {
    const sendConfirmMessage = (
      <DeleteMessage jsonIcon={deleteBin} message={<div>Are you sure you want to delete all ?</div>} />
    );
    if (await confirm(sendConfirmMessage, 'Delete Basic Fee?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
      // const deleteResponse = await dispatch(deleteBasicFeeList(sendReq));
      const sendRequests: OptionalFeeMappedDeleteAllType[] = await Promise.all(
        selectedRows?.map(async (row) => {
          const sendReq = {
            adminId,
            accademicId: academicYearFilter,
            classId: classFilter,
            studentId: row.studentId,
            dbResult: '',
            deletedId: 0,
          };
          return sendReq;
        }) || []
      );
      const deleteResponse = await dispatch(DeleteAllOptionalFee(sendRequests));

      console.log('deleteResponse', deleteResponse);
      if (deleteResponse && Array.isArray(deleteResponse.payload)) {
        const results: OptionalFeeMappedDeleteAllType[] = deleteResponse.payload;
        const errorMessages = results.find((result) => result.dbResult === 'Failed');
        const successMessages = results.find((result) => result.dbResult === 'Success');

        if (!errorMessages) {
          const deleteSuccessMessage = (
            <SuccessMessage loop={false} jsonIcon={deleteSuccess} message="Delete All Successfully" />
          );
          await confirm(deleteSuccessMessage, 'Success', { okLabel: 'Ok', showOnlyOk: true });
          loadOptionalFeeList(currentOptionalFeeRequest);
        } else if (!successMessages) {
          const deleteErrorMessage = <DeleteMessage loop={false} jsonIcon={errorIcon} message="Delete Failed" />;
          await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
        } else {
          const deleteErrorMessage = (
            <DeleteMessage loop={false} jsonIcon={errorIcon} message="Something went wrong please try again" />
          );
          await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
        }
        loadOptionalFeeList(currentOptionalFeeRequest);
        // setSelectedRows([]);
        // setTermFeeDetails((prevDetails) => prevDetails.filter((item) => !selectedRows.includes(item)));
      }
    }
  }, [
    confirm,
    dispatch,
    adminId,
    selectedRows,
    academicYearFilter,
    classFilter,
    loadOptionalFeeList,
    currentOptionalFeeRequest,
  ]);

  const IndividualFeeSettingListColumns: DataTableColumn<GetOptionalFeeSettingsDataType>[] = useMemo(() => {
    const baseColumns: DataTableColumn<any>[] = [
      {
        name: 'slNo',
        renderHeader: () => (
          <Typography sx={{ width: 100 }} variant="subtitle2" fontSize={13}>
            Admission No
          </Typography>
        ),
        headerLabel: '',
        renderCell: (row) => {
          return (
            <Typography width={100} variant="subtitle1" fontSize={13}>
              {row.admissionNo}
            </Typography>
          );
        },
        sortable: true,
      },
      {
        name: 'studentName',
        renderHeader: () => (
          <Typography width={200} variant="subtitle2" fontSize={13}>
            Student Name
          </Typography>
        ),
        renderCell: (row) => {
          return (
            <Stack width={200} pl={1} direction="row" alignItems="center" gap={1}>
              <Avatar src="" />
              <Typography variant="subtitle2" fontSize={13}>
                {row.studentName}
              </Typography>
            </Stack>
          );
        },
      },
    ];

    const headerAndBodyCells = optionalFeeData.optionalFee
      ? optionalFeeData.optionalFee.map((item: { feeId: number; feeTitle: string }) => ({
          name: `${item.feeId}`,
          // width: '80px',
          renderHeader: () => (
            <Stack key={item.feeId} className="header-color" direction="row" justifyContent="center">
              <Typography whiteSpace="nowrap" variant="subtitle2" fontSize={12}>
                {item.feeTitle}
              </Typography>
            </Stack>
          ),
          renderCell: (row: GetOptionalFeeStudentMapDataType, rowIndex: number) => {
            const { studentId } = row;
            const { feeId } = item;
            const isCellClicked = clickedCells.some(
              (cell) => cell.studentId === row?.studentId && cell.feeId === item.feeId
            );
            const isSelectedRow = selectedRows.includes(row);
            const monthData = row?.termFeeMapped && row?.termFeeMapped.find((data) => data.feeId === item.feeId);
            // Summing up all amounts in row?.termFeeMapped
            const key = `${row?.studentId}`;
            // const enteredAmountsSum = rowSums[key] || 0; // Ensure a default value of 0 if rowSums[key] is undefined

            // Calculate the sum of `termFeeMapped` amounts
            // const termFeeAmountsSum = Array.isArray(row?.termFeeMapped)
            //   ? row?.termFeeMapped.reduce((total, i) => total + i.amount, 0)
            //   : 0; // Default to 0 if termFeeMapped is null or not an array

            // Add `enteredAmountsSum` to the calculated sum
            // const totalSum = termFeeAmountsSum + enteredAmountsSum;

            // console.log('Total Sum:', enteredAmountsSum);
            const id = 0;
            if (monthData) {
              const { amount, academicTermId } = monthData;
              return (
                <Stack
                  position="relative"
                  direction="row"
                  alignItems="center"
                  key={`cell_${row?.studentId}_${item.feeId}`}
                  // onClick={() => {
                  //   if (!isCellClicked) {
                  //     setClickedCells([...clickedCells, { rowIndex, columnIndex, value: '' }]);
                  //   }
                  // }}
                  // onClick={() => !isSelectedRow && handleCellClick(row, item.feeId, totalSum)}
                  className={isSelectedRow ? 'cellActive' : 'cellInActive'}
                  sx={{
                    borderLeft: 3,
                  }}
                  color="#000"
                  gap={2}
                  pl={1}
                  height="80px"
                  minWidth={110}
                >
                  {!isSelectedRow && (
                    <>
                      <Stack direction="row" alignItems="center">
                        <CurrencyRupeeIcon sx={{ fontSize: '14px' }} color="success" />
                        <Typography fontSize={12} variant="subtitle2">
                          {amount}
                        </Typography>
                      </Stack>
                      <Stack position="absolute" top={2} right={2}>
                        <IconButton onClick={() => handleDeleteCell(academicTermId)} size="small">
                          <DeleteIcon sx={{ fontSize: 16 }} />
                        </IconButton>
                      </Stack>
                    </>
                  )}
                  {isSelectedRow && (
                    <>
                      {/* <Typography position="absolute" top={0} right={2} variant="subtitle2" fontSize={10}>
                                Edit
                              </Typography> */}
                      <Stack direction="row" position="relative" alignItems="center" justifyContent="center">
                        <CurrencyRupeeIcon sx={{ fontSize: '14px', fontWeight: 600 }} color="warning" />
                        {/* <input
                                  style={{
                                    padding: '30px 1px',
                                    width: '100%',
                                    border: 0,
                                    backgroundColor: 'transparent',
                                    // fontFamily: ,
                                    color: theme.palette.warning.main,
                                    outline: 'none',
                                  }}
                                  type="text"
                                  onChange={(e) => handleAmountChange(e, row, item.feeId, id)}
                                  defaultValue={feeAmounts[`${row?.studentId}_${item.feeId}`] || amount}
                                /> */}
                        <TextField
                          type="number"
                          variant="outlined"
                          size="small"
                          sx={{
                            '& fieldset': { border: '1px' },
                          }}
                          inputProps={{
                            maxLength: 6,
                            style: {
                              padding: '20px 0px',
                              minWidth: 70,
                              // fontFamily: ,
                              fontSize: 12,
                              fontWeight: 600,
                              color: theme.palette.warning.main,
                            },
                            // '& input::placeholder': {
                            //   fontSize: '1px',
                            // },
                          }}
                          // placeholder="₹"
                          // onChange={(e) => handleAmountChange(e, row, item.feeId, id)}
                          // onChange={(e) => handleAmountChange(e, row, item.feeId, id)}
                          defaultValue={amount}
                          // value={feeAmounts[`${row?.studentId}_${item.feeId}`]}
                        />
                        {/* {!isSelectedRow && rowSums[`${row?.studentId}_${item.feeId}`] > row?.amount && (
                          <Typography
                            position="absolute"
                            bottom={0}
                            left={4}
                            minWidth={140}
                            variant="subtitle2"
                            fontSize={8}
                            color={theme.palette.error.main}
                          >
                            Enter valid amount
                          </Typography>
                        )} */}
                      </Stack>
                    </>
                  )}
                </Stack>
              );
            } else {
              // const monthData2 = row?.termFeeMapped && row?.termFeeMapped.find((data) => data.feeId === item.feeId);
              // if (monthData2) {
              //   const { amount } = monthData2;
              return (
                <Stack
                  alignItems="center"
                  justifyContent="center"
                  key={`cell_${row?.studentId}_${item.feeId}`}
                  // color={isSelectedRow ? theme.palette.grey[400] : theme.palette.grey[500]}
                  // bgcolor={isSelectedRow ? theme.palette.grey[50] : ''}
                  className={isSelectedRow ? 'inputCellActive' : 'inputCellInActive'}
                  gap={2}
                  minWidth={110}
                  height="100%"
                >
                  {isCellClicked ? (
                    <Stack pl={1} position="relative" direction="row" alignItems="center" justifyContent="center">
                      <CurrencyRupeeIcon sx={{ fontSize: '14px' }} />
                      <TextField
                        name=""
                        type="number"
                        variant="outlined"
                        size="small"
                        sx={{
                          '& fieldset': { border: '1px' },
                          '& .MuiOutlinedInput-root': {
                            '&:has(> input[data-com-onepassword-filled="light"])': {
                              backgroundColor: 'red',
                            },
                          },
                        }}
                        inputProps={{
                          maxLength: 6,
                          style: {
                            padding: '20px 0px',
                            minWidth: 70,
                            fontSize: 12,
                            fontWeight: 600,
                            // fontFamily: 'sans-serif',
                            // color: rowSums[`${row?.studentId}_${item.feeId}`] > row?.amount ? 'red' : '',
                          },
                          // '& input::placeholder': {
                          //   fontSize: '1px',
                          // },
                        }}
                        // placeholder="₹"
                        // autoFocus={isAutoFocus[`${row?.studentId}`] ?? true}
                        // inputRef={(el) => {
                        //   if (!inputRef.current[`${row?.studentId}_${item.feeId}`]) {
                        //     inputRef.current[`${row?.studentId}_${item.feeId}`] = el;
                        //   }
                        // }}
                        onKeyPress={(event) => {
                          if (event.key === 'Enter') {
                            event.preventDefault();

                            // Current cell identification
                            const currentKey = `${row?.studentId}_${item.feeId}`;
                            // const allKeys = Object.keys(inputRef.current);
                            // const currentIndex = allKeys.indexOf(currentKey);
                            // const [fId, tId] = nextKey.split('_').map(Number); // Convert strings to numbers

                            setClickedCells((prevClickedCells) => [
                              ...prevClickedCells,
                              { studentId, feeId: feeId + 1 },
                            ]);
                            // if (currentIndex !== -1 && currentIndex + 1 < allKeys.length) {
                            // const nextKey = allKeys[currentIndex + 1];
                            // Move focus to the next cell

                            // Move focus to the next cell
                            // inputRef.current[nextKey]?.focus();
                            console.log('c::::----', clickedCells);

                            // Add the next cell to the clickedCells array if it's not already present
                            // setClickedCells((prevClickedCells) => {
                            //   const isAlreadyClicked = prevClickedCells.some(
                            //     (cell) => cell.fId === fId && cell.tId === tId
                            //   );

                            //   if (!isAlreadyClicked) {
                            //     return [...prevClickedCells, { fId, tId }];
                            //   }
                            //   return prevClickedCells;
                            // });
                            // }
                          }
                        }}
                        // onChange={(e) => handleAmountChange(e, row, item.feeId, id)}
                        // onChange={(e) => debouncedHandleAmountChange(e, row, item.feeId, id)}
                        // value={feeAmounts[`${row?.studentId}_${item.feeId}`]}
                        disabled={isSelectedRow}
                      />

                      {/* {!!cellErrorMessages[`${row?.studentId}_${item.feeId}`] && (
                        <Typography
                          position="absolute"
                          bottom={0}
                          left={4}
                          minWidth={140}
                          variant="subtitle2"
                          fontSize={8}
                          color={theme.palette.error.main}
                        >
                          {cellErrorMessages[`${row?.studentId}_${item.feeId}`]}
                        </Typography>
                      )} */}
                    </Stack>
                  ) : (
                    !isCellClicked && (
                      <Stack
                        // onClick={() => {
                        //   const key = `${row?.studentId}_${item.feeId}`;
                        //   // Check conditions for setting clicked cells
                        //   if (sumRowAmounts < row?.amount) {
                        //     if (!isSelectedRow || rowSums[key] < row?.amount) {
                        //       setClickedCells((prevClickedCells) => [
                        //         ...prevClickedCells,
                        //         { rowIndex, columnIndex, value: '' },
                        //       ]);
                        //     }
                        //   }
                        // }}
                        onClick={() => !isSelectedRow && handleCellClick(row, item.feeId)}
                        sx={{
                          width: '100%',
                          height: '57px',
                          transform: 'scale(0.9)',
                          transition: '.1s',
                          '&:hover': {
                            color: !isSelectedRow ? theme.palette.success.main : '',
                            transform: !isSelectedRow ? 'scale(1.2)' : '',
                            transition: !isSelectedRow ? '.1s' : '',
                          },
                        }}
                        direction="row"
                        justifyContent="center"
                        alignItems="center"
                      >
                        <AddIcon />
                      </Stack>
                    )
                  )}
                </Stack>
              );
            }
          },
        }))
      : [];

    const baseColumns2: DataTableColumn<any>[] = [
      // {
      //   name: 'total',
      //   headerLabel: 'Total',
      //   renderCell: (row, index) => (
      //     <Typography px={1} textAlign="start" variant="subtitle1" fontSize={13}>
      //       {calculateTotals(row, index)}
      //     </Typography>
      //   ),
      // },
      {
        name: '',
        renderHeader: () => (
          <Typography width={80} textAlign="start" className="header-color" variant="subtitle2" fontSize={14}>
            Actions
          </Typography>
        ),
        renderCell: (row) => {
          return (
            <Stack width={80} direction="row" alignItems="center" justifyContent="center" gap={1}>
              {
                showSuccessIcon[row.studentId] === true ? (
                  <Stack direction="row" justifyContent="center" flexDirection="column" alignItems="center">
                    {succesResponse === 'Success' ? (
                      <>
                        <Lottie animationData={Success} loop={false} style={{ width: '30px' }} />
                        <Typography color={theme.palette.success.main} fontSize={7} variant="subtitle2">
                          Saved
                        </Typography>
                      </>
                    ) : (
                      <>
                        <Lottie animationData={Updated} loop={false} style={{ width: '30px' }} />
                        <Typography color={theme.palette.warning.main} fontSize={7} variant="subtitle2">
                          Updated
                        </Typography>
                      </>
                    )}
                  </Stack>
                ) : showErrorIcon[row.studentId] === true ? (
                  <Stack direction="row" justifyContent="center">
                    <Lottie animationData={errorIcon} loop={false} style={{ width: '30px' }} />
                  </Stack>
                ) : !individualSaveLoading[row.studentId] === true ? (
                  <IconButton
                    disabled={!individualSaveButtonEnabled.hasOwnProperty(row.studentId) || false}
                    size="small"
                    color={checkedRows[row.studentId] ? 'warning' : 'success'}
                    aria-label=""
                    onClick={() => handleSave(row)}
                  >
                    <SaveIcon fontSize="small" />
                  </IconButton>
                ) : (
                  <Stack direction="row" justifyContent="center" flexDirection="column" alignItems="center">
                    <>
                      <Lottie
                        animationData={checkedRows[row.studentId] ? UpdateLoading : SaveLoading}
                        loop
                        style={{ width: '20px' }}
                      />
                      <Typography
                        color={checkedRows[row.studentId] ? theme.palette.warning.main : theme.palette.success.main}
                        fontSize={7}
                        variant="subtitle2"
                      >
                        {checkedRows[row.studentId] ? 'Updating...' : 'Saving...'}
                      </Typography>
                    </>
                  </Stack>
                )
                // <LoadingButton
                //   loading={individualSaveLoading[row.feeId]}
                //   onClick={() => handleSave(row)}
                //   variant="contained"
                //   size="small"
                //   color="success"
                //   disabled={!individualSaveButtonEnabled[row.feeId] || saving}
                //   sx={{ py: 0.5, fontSize: '10px' }}
                // >
                //   {!individualSaveLoading[row.feeId] ? 'Save' : ''}
                // </LoadingButton>
              }

              <IconButton
                disabled={row.feeMapped === null}
                onClick={() => handleDeleteRow(row)}
                size="small"
                color="error"
                aria-label=""
              >
                <DeleteIcon fontSize="small" />
              </IconButton>
            </Stack>
          );
        },
      },
    ];

    return [...baseColumns, ...headerAndBodyCells, ...baseColumns2];
  }, [
    theme,
    handleCellClick,
    optionalFeeData,
    individualSaveButtonEnabled,
    showErrorIcon,
    checkedRows,
    handleSave,
    showSuccessIcon,
    individualSaveLoading,
    succesResponse,
    handleDeleteCell,
    handleDeleteRow,
    clickedCells,
    selectedRows,
  ]);
  // const feeCollectionListColumns: DataTableColumn<StudentFeeStatusDataType>[] = useMemo(
  //   () => [
  //     {
  //       name: 'slNo',
  //       headerLabel: 'Admission No',
  //       renderCell: (_, index) => {
  //         return (
  //           <Typography variant="subtitle1" fontSize={13}>
  //             100{index + 1}
  //           </Typography>
  //         );
  //       },
  //       sortable: true,
  //     },
  //     {
  //       name: 'studentName',
  //       headerLabel: 'Student Name',
  //       renderCell: (row) => {
  //         return (
  //           <Stack direction="row" alignItems="center" gap={1}>
  //             <Avatar src="" />
  //             <Typography variant="subtitle2" fontSize={13}>
  //               {row.studentName}
  //             </Typography>
  //           </Stack>
  //         );
  //       },
  //     },
  //     {
  //       name: 'admNo',
  //       headerLabel: 'Admission No.',
  //       dataKey: 'admissionNo',
  //     },
  //     {
  //       name: 'classDiv',
  //       dataKey: 'className',
  //       headerLabel: 'Class',
  //     },
  //     {
  //       name: 'prvYearPending',
  //       headerLabel: 'Prev Year Pending',
  //       dataKey: 'prvYearPending',
  //     },
  //     {
  //       name: 'totalFees',
  //       headerLabel: 'Total Fees',
  //       dataKey: 'totalFee',
  //     },
  //     {
  //       name: 'paid',
  //       headerLabel: 'Paid',
  //       renderCell: (row) => {
  //         return (
  //           <Typography color="success" sx={{ color: theme.palette.success.main }} variant="subtitle1">
  //             {row.paid}
  //           </Typography>
  //         );
  //       },
  //     },
  //     {
  //       name: 'balance',
  //       headerLabel: 'Balance',
  //       renderCell: (row) => {
  //         return (
  //           <Typography color="error" variant="subtitle1" fontSize={14}>
  //             {row.balance}
  //           </Typography>
  //         );
  //       },
  //     },
  //   ],
  //   [theme, handleShowPayCollections]
  // );

  // const getRowKey = useCallback(
  //   (row: GetOptionalFeeStudentMapDataType, index: number) => `${row.studentId}_${index}`,
  //   []
  // );

  const getRowKey = useCallback((row: GetOptionalFeeStudentMapDataType, index?: number) => {
    if (index === undefined) {
      return `${row.studentId}_undefined`;
    }
    return `${row.studentId}_${index}`;
  }, []);

  return (
    <Page title="Fees Collection">
      <IndividualFeeSettingRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17}>
              Individual Optional Fee Settings
            </Typography>
            <Box pb={1} sx={{ flexShrink: 0 }}>
              {selectedRows.length > 0 && (
                <Tooltip title="Delete">
                  <IconButton
                    sx={{ visibility: { xs: 'hidden', sm: 'visible' } }}
                    aria-label="delete"
                    color="error"
                    onClick={handleDeleteMultiple}
                  >
                    <DeleteIcon />
                  </IconButton>
                </Tooltip>
              )}
              {showFilter === false ? (
                <Tooltip title="Search">
                  <IconButton
                    aria-label="delete"
                    color="primary"
                    sx={{ mr: { xs: 0, sm: 1 } }}
                    onClick={() => setShowFilter((x) => !x)}
                  >
                    <SearchIcon />
                  </IconButton>
                </Tooltip>
              ) : (
                <IconButton
                  aria-label="delete"
                  color="primary"
                  sx={{ mr: { xs: 0, sm: 1 } }}
                  onClick={() => setShowFilter((x) => !x)}
                >
                  <IoIosArrowUp />
                </IconButton>
              )}
            </Box>
          </Stack>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate onReset={handleReset}>
                <Grid pb={4} container spacing={2} alignItems="end">
                  <Grid item xxl={2} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Select
                        labelId="academicYearFilter"
                        id="academicYearFilterSelect"
                        value={academicYearFilter?.toString()}
                        onChange={handleYearChange}
                        placeholder="Select Year"
                        disabled={YearStatus === 'loading'}
                        endAdornment={
                          YearStatus === 'loading' && (
                            <CircularProgress sx={{ position: 'relative', right: 15 }} size={24} />
                          )
                        }
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Year
                        </MenuItem>
                        {YearData.length !== 0 ? (
                          YearData?.map((opt) => (
                            <MenuItem key={opt.accademicId} value={opt.accademicId}>
                              {opt.accademicTime}
                            </MenuItem>
                          ))
                        ) : (
                          <MenuItem>No Data</MenuItem>
                        )}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Select Type
                      </Typography>
                      <Select
                        labelId="feeTypeFilter"
                        id="feeTypeFilterSelect"
                        value={feeTypeFilter.toString()}
                        onChange={handleFeeTypeChange}
                        placeholder="Select Year"
                      >
                        <MenuItem sx={{ display: 'none' }} value={0}>
                          Select Type
                        </MenuItem>
                        {FEE_TYPE_ID_OPTIONS.map((opt) => (
                          <MenuItem key={opt.id} value={opt.id}>
                            {opt.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Section
                      </Typography>
                      <Select
                        labelId="classSectionsFilter"
                        id="classSectionsFilterSelect"
                        value={classSectionsFilter?.toString()}
                        onChange={handleClassSectionChange}
                        placeholder="Select Section"
                        MenuProps={{
                          PaperProps: {
                            style: {
                              maxHeight: '270px', // Adjust the value to your desired height
                            },
                          },
                        }}
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Section
                        </MenuItem>
                        {ClassSectionsData.map((opt) => (
                          <MenuItem key={opt.sectionId} value={opt.sectionId}>
                            {opt.sectionName}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Select
                        disabled={classListData.length === 0}
                        labelId="classFilter"
                        id="classFilter"
                        value={classFilter?.toString()}
                        onChange={handleClassChange}
                        placeholder="Select Class"
                        sx={{
                          '& .MuiInputBase-input.Mui-disabled': {
                            backgroundColor: theme.palette.grey[200],
                          },
                        }}
                        MenuProps={{
                          PaperProps: {
                            style: {
                              maxHeight: '270px', // Adjust the value to your desired height
                            },
                          },
                        }}
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Class
                        </MenuItem>
                        {classListData.map((opt) => (
                          <MenuItem key={opt.classId} value={opt.classId}>
                            {opt.className}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Student
                      </Typography>
                      <StudentsPickerField
                        width="100%"
                        // setSelectedStudentData={setSelectedStudentData}
                        componentsPropsWidth={350}
                        loadRecentPaidList={loadOptionalFeeList}
                        currentRecentPaidListRequest={currentOptionalFeeRequest}
                        setSelectedStudentIds={setSelectedStudentIds}
                        classId={classFilter}
                        academicId={academicYearFilter}
                        // multiple
                      />
                    </FormControl>
                  </Grid>
                  {/* <Grid item lg={2} md={6} sm={6} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Date
                      </Typography>
                      <DatePickers name="messageDateFilter" value="" />
                    </FormControl>
                  </Grid> */}

                  <Grid item lg="auto">
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      {/* <Button type="submit" variant="contained" color="primary" fullWidth>
                        Search
                      </Button> */}
                      <Button type="reset" variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>
            {classFilter !== 0 && (
              <Box pr={1} mt={!showFilter ? 2 : 0} display="flex" justifyContent="end">
                <LoadingButton
                  loading={savingAll}
                  onClick={handleAllSave}
                  disabled={saveAllButtonDisabled}
                  color="success"
                  startIcon={<SaveIcon fontSize="small" />}
                  size="small"
                  variant="contained"
                  sx={{
                    //  py: 0.2, px: 1,
                    fontSize: 12,
                  }}
                >
                  {/* {switchToUpdateButton ? 'Update All ' : 'Save All'} */}
                  {savingAll ? 'Saving All' : 'Save All'}
                </LoadingButton>
              </Box>
            )}
            {classFilter !== 0 ? (
              <Paper
                className="card-table-container"
                sx={{
                  marginTop: '12px',
                  border: optionalFeeData.length !== 0 ? `2px solid ${theme.palette.grey[200]} ` : '',
                }}
              >
                <DTVirtuoso
                  // disabledCheckBox={studentsData.map((m) => m.feeMapped?.length === 0)}
                  tableStyles={{ minWidth: { xs: '1100px' } }}
                  showHorizontalScroll
                  ShowCheckBox
                  setSelectedRows={setSelectedRows}
                  selectedRows={selectedRows}
                  columns={IndividualFeeSettingListColumns}
                  data={filteredData}
                  getRowKey={getRowKey}
                  // fetchStatus={optionalFeeSettingListStatus}
                  fetchStatus="success"
                  showEmptyIconState={classFilter}
                />
              </Paper>
            ) : (
              <Box
                display="flex"
                alignItems="center"
                justifyContent="center"
                width="100%"
                height={{ xs: 'calc(100vh - 400px)', sm: 'calc(100vh - 400px)', lg: 'calc(100vh - 400px)' }}
              >
                <Stack direction="column" alignItems="center">
                  <img src={NoData} width="150px" alt="" />
                  <Typography variant="subtitle2" mt={2} color="GrayText">
                    No data found !
                  </Typography>
                </Stack>
              </Box>
            )}
          </div>
        </Card>
      </IndividualFeeSettingRoot>
      <PositionedSnackbar
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        content="Save All Successfully"
        open={snackBar}
        TransitionComponent="SlideTransition"
        onClose={() => setSnackBar(false)}
      />
    </Page>
  );
}
