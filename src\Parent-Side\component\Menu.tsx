import * as React from 'react';
import Button from '@mui/material/Button';
import ClickAwayListener from '@mui/material/ClickAwayListener';
import Grow from '@mui/material/Grow';
import Paper from '@mui/material/Paper';
import Popper from '@mui/material/Popper';
import MenuItem from '@mui/material/MenuItem';
import MenuList from '@mui/material/MenuList';
import Stack from '@mui/material/Stack';
import { SuccessIcon } from '@/theme/overrides/CustomIcons';
import { size } from 'lodash';
import TuneIcon from '@mui/icons-material/Tune';
import { Checkbox } from '@mui/material';
import { Box } from '@mui/material';

export default function MenuListComposition() {
  const [open, setOpen] = React.useState(false);
  const anchorRef = React.useRef<HTMLButtonElement>(null);

  const handleToggle = () => {
    setOpen((prevOpen) => !prevOpen);
  };

  const handleClose = (event: Event | React.SyntheticEvent) => {
    if (anchorRef.current && anchorRef.current.contains(event.target as HTMLElement)) {
      return;
    }

    setOpen(false);
  };

  function handleListKeyDown(event: React.KeyboardEvent) {
    if (event.key === 'Tab') {
      event.preventDefault();
      setOpen(false);
    } else if (event.key === 'Escape') {
      setOpen(false);
    }
  }

  // return focus to the button when we transitioned from !open -> open
  const prevOpen = React.useRef(open);
  React.useEffect(() => {
    if (prevOpen.current === true && open === false) {
      anchorRef.current!.focus();
    }

    prevOpen.current = open;
  }, [open]);

  return (
    <Stack direction="row" spacing={2}>
      <div>
        <Button
          ref={anchorRef}
          variant="outlined"
          id="composition-button"
          aria-controls={open ? 'composition-menu' : undefined}
          aria-expanded={open ? 'true' : undefined}
          aria-haspopup="true"
          onClick={handleToggle}
          startIcon={<TuneIcon />}
          size="small"
          color="secondary"
        >
          Filter
        </Button>
        <Popper
          sx={{ zIndex: 1, boxShadow: 5 }}
          open={open}
          anchorEl={anchorRef.current}
          role={undefined}
          placement="bottom-start"
          transition
          disablePortal
        >
          {({ TransitionProps, placement }) => (
            <Grow
              {...TransitionProps}
              style={{
                transformOrigin: placement === 'bottom-start' ? 'left top' : 'left bottom',
              }}
            >
              <Paper sx={{ width: '230px' }}>
                <ClickAwayListener onClickAway={handleClose}>
                  <MenuList
                    autoFocusItem={open}
                    id="composition-menu"
                    aria-labelledby="composition-button"
                    onKeyDown={handleListKeyDown}
                  >
                    <MenuItem onClick={handleClose} sx={{ fontSize: 14 }}>
                      <Checkbox /> Yesterday
                    </MenuItem>
                    <MenuItem onClick={handleClose} sx={{ fontSize: 14 }}>
                      <Checkbox /> Last Week
                    </MenuItem>
                    <MenuItem onClick={handleClose} sx={{ fontSize: 14 }}>
                      <Checkbox /> Last Month
                    </MenuItem>
                    <Box gap={2} mx={3} my={2} display="flex" justifyContent="space-between">
                      <Button fullWidth size="small" color="secondary" variant="outlined">
                        Clear
                      </Button>
                      <Button fullWidth size="small" color="success" variant="contained">
                        Apply
                      </Button>
                    </Box>
                  </MenuList>
                </ClickAwayListener>
              </Paper>
            </Grow>
          )}
        </Popper>
      </div>
    </Stack>
  );
}
