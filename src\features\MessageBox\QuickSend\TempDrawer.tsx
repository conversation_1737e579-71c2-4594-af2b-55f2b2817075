import * as React from 'react';
import { TableContainer, Table, TableBody, TableRow, TableCell, Checkbox, Stack, Box, Button } from '@mui/material';

export default function TempDrawer({ handleDrawerClose, drawerData }: any) {
  return (
    <Box
      mt={3}
      role="presentation"
      // onClick={toggleDrawer(anchor, false)}
      onKeyDown={handleDrawerClose}
    >
      <Box sx={{ height: 'calc(100vh - 150px)' }}>
        <hr />
        <TableContainer>
          <Table sx={{ width: '100%' }} aria-label="customized table">
            <TableBody>
              {drawerData &&
                drawerData.map((item: string) => (
                  <TableRow key={item}>
                    <TableCell>{item}</TableCell>
                    <TableCell align="right">
                      <Checkbox />
                    </TableCell>
                  </TableRow>
                ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>
      <Box sx={{ justifyContent: 'center', pt: 3 }}>
        <Stack spacing={2} direction="row">
          <Button onClick={handleDrawerClose} fullWidth variant="contained" color="secondary">
            Cancel
          </Button>
          <Button
            onClick={handleDrawerClose}
            onKeyDown={handleDrawerClose}
            fullWidth
            variant="contained"
            color="primary"
          >
            Confirm
          </Button>
        </Stack>
      </Box>
    </Box>
  );
}
