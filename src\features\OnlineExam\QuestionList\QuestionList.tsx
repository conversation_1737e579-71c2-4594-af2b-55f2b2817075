/* eslint-disable jsx-a11y/alt-text */
import React, { useState } from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  useTheme,
  IconButton,
  Collapse,
  Tooltip,
  FormControl,
  ToggleButtonGroup,
  Checkbox,
  ToggleButton,
} from '@mui/material';
import styled from 'styled-components';
import { CLASS_SELECT, STATUS_SELECT, SUBJECT_SELECT, YEAR_SELECT } from '@/config/Selection';
import SearchIcon from '@mui/icons-material/Search';
import Popup from '@/components/shared/Popup/Popup';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import { MdAdd } from 'react-icons/md';
import useSettings from '@/hooks/useSettings';
import DateSelect from '@/components/shared/Selections/DateSelect';
import CreateQuestions from './CreateQuestions';
import MapQuestions from './MapQuestions';
import DescQuestionList from '../DescriptiveExam/DescQuestionList/DescQuestionList';
import ObjQuestionList from '../ObjectiveExam/ObjQuestionList/ObjQuestionList';

const QuestionListRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  .question_card {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  }

  .Card {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 80px);
      flex-grow: 1;

      .card-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: auto;
        ::-webkit-scrollbar {
          width: 0px;
        }

        .MuiTableContainer-root {
          max-height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

function QuestionList() {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const [showFilter, setShowFilter] = useState(false);
  const [Delete, setDelete] = useState(false);

  const handleClickDelete = () => setDelete(true);
  const handleClickCloseDelete = () => setDelete(false);

  const [createPopup, setCreatePopup] = useState(false);
  const [mapPopup, setMapPopup] = useState(false);

  // const [changeView, setChangeView] = useState(false);
  // const [students, setStudents] = useState(StudentInfoData);
  // const [open, setOpen] = useState(false);
  // const [showNewMultiple, setShowNewMultiple] = useState(false);
  // const [selectedStudent, setSelectedStudent] = useState(null);

  // const handlePageChange = useCallback((event: unknown, newPage: number) => {
  //   // loadClassList({ ...currentClassListRequest, pageNumber: newPage + 1 });
  // }, []);

  // const handleChangeRowsPerPage = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
  //   const newPageSize = +event.target.value;
  //   // loadClassList({ ...currentClassListRequest, pageNumber: 1, pageSize: newPageSize });
  // }, []);

  // const pageProps = useMemo(
  //   () => ({
  //     rowsPerPageOptions: [10, 20, 30],
  //     pageNumber: 1,
  //     pageSize: 10,
  //     totalRecords: 100,
  //     onPageChange: handlePageChange,
  //     onRowsPerPageChange: handleChangeRowsPerPage,
  //   }),
  //   [handleChangeRowsPerPage, handlePageChange]
  // );

  // const handleOpen = (student: Student) => {
  //   setSelectedStudent(student);
  //   setOpen(true);
  // };

  // const handleClose = () => {
  //   setOpen(false);
  // };
  // const handleToggleNewMultiple = () => {
  //   setShowNewMultiple((prevState) => !prevState);
  // };

  // const handleToggle = () => {
  //   setChangeView((prevChangeView) => !prevChangeView);
  // };

  // const handleUpdate = (id: number, updatedData: Student) => {
  // const handleUpdate = (order: number, updatedData: Student) => {
  //   const updatedStudents = students.map((student) => (student.id === id ? { ...student, ...updatedData } : student));
  //   setStudents(updatedStudents);
  //   setOpen(false);
  // };

  // const getRowKey = useCallback((row: any) => row.examId, []);

  // const handleCreate = (newStudents) => {
  //   const updatedStudents = [...students, newStudents];
  //   setStudents(updatedStudents);
  //   setOpenNew(false);
  // };

  //   const renderContent = () => {
  //     return students.map((student, rowIndex) => (
  //       <Card key={student.id}>
  //         {QuestionListColumns.map((column) => (
  //           <Typography key={column.name}>{student[column.dataKey]}</Typography>
  //         ))}
  //       </Card>
  //     ));
  //   };

  // const content: ReactNode = data.map((item, rowIndex: number) => {
  //   let cellData: ReactNode = null;

  //   return (
  //     <Typography key={column.name} variant="subtitle1" mb={1} fontSize={13}>
  //       <b>{cellData}</b>
  //     </Typography>
  //   );
  // });

  // Content = content;
  const [examType, setExamType] = React.useState('obj');

  const handleChange = (event: React.MouseEvent<HTMLElement>, newExamType: string) => {
    setExamType(newExamType);
  };

  return (
    <Page title="Schedule List">
      <QuestionListRoot>
        <Card className="Card" elevation={1} sx={{ pb: { xs: 2, md: 3 } }}>
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
            sx={{ px: { xs: 3, md: 5 }, pt: { xs: 2, md: 3 } }}
          >
            <Typography variant="h6" fontSize={20}>
              Questions List
            </Typography>
            <ToggleButtonGroup value={examType} exclusive onChange={handleChange} aria-label="Platform" size="small">
              <ToggleButton value="obj" sx={{ fontWeight: 900, color: theme.palette.grey[500] }}>
                Objective(MCQ) Exams
              </ToggleButton>
              <ToggleButton value="desc" sx={{ fontWeight: 900, color: theme.palette.grey[500] }}>
                Descriptive Exams
              </ToggleButton>
            </ToggleButtonGroup>

            <Box sx={{ flexShrink: 0 }}>
              <Tooltip title="Search">
                <IconButton aria-label="" color="primary" sx={{ mr: 1 }} onClick={() => setShowFilter((x) => !x)}>
                  <SearchIcon />
                </IconButton>
              </Tooltip>
              <Button sx={{ borderRadius: '20px', mr: 1 }} size="small" variant="outlined">
                Order Questions
              </Button>
              <Button
                sx={{ borderRadius: '20px', borderWidth: 2 }}
                size="small"
                variant="contained"
                onClick={() => setCreatePopup(true)}
              >
                <MdAdd /> Create
              </Button>
            </Box>
          </Stack>
          <Stack direction="row" justifyContent="space-between" sx={{ px: { xs: 3, md: 5 }, pt: 1 }}>
            <Typography fontSize={15} color="GrayText" width="100%">
              <Checkbox />
              Select All
            </Typography>
            <Box sx={{ flexShrink: 0 }}>
              <Button
                variant="contained"
                size="small"
                color="success"
                sx={{
                  backgroundColor: isLight ? theme.palette.success.light : theme.palette.grey[700],
                  color: isLight ? theme.palette.success.darker : theme.palette.grey[100],
                  '&:hover': {
                    backgroundColor: isLight ? theme.palette.success.main : theme.palette.grey[800],
                    color: theme.palette.grey[100],
                  },
                  boxShadow: 0,
                  mr: 1,
                  borderRadius: '20px',
                }}
                onClick={() => setMapPopup(true)}
              >
                Map
              </Button>
              <Button
                variant="contained"
                size="small"
                color="secondary"
                sx={{
                  backgroundColor: isLight ? theme.palette.secondary.light : theme.palette.grey[700],
                  color: isLight ? theme.palette.secondary.darker : theme.palette.grey[100],
                  '&:hover': {
                    backgroundColor: isLight ? theme.palette.secondary.main : theme.palette.grey[800],
                    color: theme.palette.grey[100],
                  },
                  boxShadow: 0,
                  borderRadius: '20px',
                }}
                onClick={handleClickDelete}
              >
                Delete
              </Button>
            </Box>
          </Stack>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={2} sx={{ px: { xs: 3, md: 5 }, pb: { xs: 2, md: 5 } }}>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Autocomplete
                        options={YEAR_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select year" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Autocomplete
                        options={CLASS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select class" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Subject
                      </Typography>
                      <Autocomplete
                        options={SUBJECT_SELECT}
                        renderInput={(params) => <TextField {...params} fullWidth placeholder="Enter Subject" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Start Date
                      </Typography>
                      <DateSelect />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" md={6} xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', lg: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Status
                      </Typography>
                      <Autocomplete
                        options={STATUS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select" />}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item lg={1} xs={12}>
                    <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                      <Button variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                      {/* <Button variant="contained" color="primary" fullWidth>
                          Search
                        </Button> */}
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            {examType === 'obj' ? <ObjQuestionList /> : <DescQuestionList />}
          </div>
        </Card>
      </QuestionListRoot>
      <Popup
        size="xs"
        state={Delete}
        onClose={handleClickCloseDelete}
        popupContent={<DeleteMessage message="Are you sure want to delete?" />}
      />
      <Popup
        size="md"
        title="Add a new Question"
        state={createPopup}
        onClose={() => setCreatePopup(false)}
        popupContent={<CreateQuestions />}
      />
      <Popup
        size="xs"
        title="Map Questions To:"
        state={mapPopup}
        onClose={() => setMapPopup(false)}
        popupContent={<MapQuestions />}
      />
    </Page>
  );
}

export default QuestionList;
