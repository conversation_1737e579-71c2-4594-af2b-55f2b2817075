import ApiUrls from '@/config/ApiUrls';
import {
  DeleteMultipleNotificationListType,
  NotificationCreateRequest,
  NotificationDataType,
  NotificationRequest,
  SendToClassDivisionType,
  SendToClassSectionType,
  SendToParentsRequest,
  SendToParentsResponse,
  SendToStaffType,
  UplaodFilesType,
} from '@/types/Notification';
import { CreateResponse, DeleteResponse, SendResponse } from '@/types/Common';
import { APIResponse } from './base/types';
import { privateApi } from './base/api';

async function CreateNewNotification(request: NotificationCreateRequest): Promise<APIResponse<CreateResponse>> {
  const response = await privateApi.post<CreateResponse>(ApiUrls.CreateNotification, request);
  return response;
}

async function GetNotificationList(request: NotificationRequest): Promise<APIResponse<NotificationDataType[]>> {
  const response = await privateApi.post<NotificationDataType[]>(ApiUrls.GetNotificationList, request);
  console.log('response', response);
  console.log('request from Api', request);
  return response;
}

async function FileUpload(files: File[]): Promise<APIResponse<UplaodFilesType>> {
  try {
    console.log('files', files);
    const response = await privateApi.post<any>(ApiUrls.FileUpload, files, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    console.log(' response.data', response.data);
    return response.data;
  } catch (error) {
    throw new Error('Failed to upload files');
  }
}

async function DeleteNotificationList(
  adminId: number | undefined,
  notificationId: number
): Promise<APIResponse<DeleteResponse>> {
  if (adminId === undefined || notificationId === undefined) {
    throw new Error('adminId and notificationId are required');
  }
  const url = ApiUrls.DeleteNotificationList.replace('{adminId}', adminId.toString()).replace(
    '{notificationId}',
    notificationId.toString()
  );
  const response = await privateApi.get<DeleteResponse>(url);
  return response;
}

async function DeleteMultipleNotificationList(
  request: DeleteMultipleNotificationListType[]
): Promise<APIResponse<DeleteResponse>> {
  const response = await privateApi.post<DeleteResponse>(ApiUrls.DeleteMultipleNotificationList, request);
  return response;
}

async function SendToParentsList(request: SendToParentsRequest[]): Promise<APIResponse<SendToParentsResponse>> {
  const response = await privateApi.post<SendToParentsResponse>(ApiUrls.NotificationSendToParentsList, request);
  return response;
}
async function SendToStaffList(request: SendToStaffType[]): Promise<APIResponse<SendToStaffType>> {
  const response = await privateApi.post<SendToStaffType>(ApiUrls.NotificationSendToStaffList, request);
  return response;
}
async function SendToClassDivision(request: SendToClassDivisionType[]): Promise<APIResponse<SendToClassDivisionType>> {
  const response = await privateApi.post<SendToClassDivisionType>(ApiUrls.NotificationSendToClassDivision, request);
  return response;
}
async function SendToClassSection(request: SendToClassSectionType[]): Promise<APIResponse<SendToClassSectionType>> {
  const response = await privateApi.post<SendToClassSectionType>(ApiUrls.NotificationSendToClassSection, request);
  return response;
}
// ============================== Send To All ===================================
async function SendToAllParents(
  adminId: number | undefined,
  academicId: number | undefined,
  notificationId: number | undefined
): Promise<APIResponse<SendResponse>> {
  if (adminId === undefined || academicId === undefined || notificationId === undefined) {
    throw new Error('adminId, academicId, and notificationId are required');
  }

  const url = ApiUrls.NotificationSendToAllParents.replace('{adminId}', adminId.toString())
    .replace('{academicId}', academicId.toString())
    .replace('{notificationId}', notificationId.toString());

  const response = await privateApi.get<any>(url);
  return response;
}

async function SendToAllStaff(
  adminId: number | undefined,
  academicId: number | undefined,
  notificationId: number | undefined
): Promise<APIResponse<SendResponse>> {
  if (adminId === undefined || academicId === undefined || notificationId === undefined) {
    throw new Error('adminId, academicId, and notificationId are required');
  }

  const url = ApiUrls.NotificationSendToAllStaff.replace('{adminId}', adminId.toString())
    .replace('{academicId}', academicId.toString())
    .replace('{notificationId}', notificationId.toString());

  const response = await privateApi.get<any>(url);
  return response;
}
const methods = {
  CreateNewNotification,
  GetNotificationList,
  FileUpload,
  DeleteNotificationList,
  DeleteMultipleNotificationList,
  SendToParentsList,
  SendToStaffList,
  SendToClassDivision,
  SendToClassSection,
  SendToAllParents,
  SendToAllStaff,
};

export default methods;
