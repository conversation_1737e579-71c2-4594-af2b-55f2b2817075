/* eslint-disable no-else-return */
/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  Box,
  Grid,
  Stack,
  Table,
  TableContainer,
  TableRow,
  TableBody,
  Typography,
  Card,
  Checkbox,
  MenuItem,
  SelectChangeEvent,
  FormControl,
  Select,
  FormControlLabel,
  useTheme,
} from '@mui/material';
import styled from 'styled-components';
import RadioButtonUncheckedIcon from '@mui/icons-material/RadioButtonUnchecked';
import { SuccessIcon } from '@/theme/overrides/CustomIcons';
import { FEE_TYPE_ID_OPTIONS } from '@/config/Selection';
import CurrencyRupeeIcon from '@mui/icons-material/CurrencyRupee';
import useSettings from '@/hooks/useSettings';
import Popup from '@/components/shared/Popup/Popup';
import Lottie from 'lottie-react';
import PaySuccess from '@/Parent-Side/assets/payFee/PaySuccess.json';
import { FetchStatus } from '@/types/Common';
import {
  FeeDetailsType,
  PaymentOrderRequest,
  PaymentOrderResponse,
  StudentDetailsType,
  TermFeeDetailsType,
} from '@/types/Payment';
import { nanoid } from '@reduxjs/toolkit';
import api from '@/api';
import LoadingButton from '@mui/lab/LoadingButton';
import { useAppSelector } from '@/hooks/useAppSelector';
import { fetchParentPayFee } from '@/store/Payment/payment.thunk';
import { getParentPayFeeData, getParentPayFeeStatus } from '@/config/storeSelectors';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import PaymentLaunchForm, { PaymentLaunchFormHandle } from './PaymentLaunchForm';

const OnlinePaymentRoot = styled.div`
  /* padding: 1rem; */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.grey[200] : props.theme.palette.grey[900]};
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1111;
  @media screen and (max-width: 576px) {
    /* padding: 0.5rem; */
  }

  .Card {
    /* min-height: calc(100vh - 160px); */
    @media screen and (max-width: 996px) {
      /* height: calc(100% - 52px); */
      /* height: 100%; */
    }
  }
  .TableCard {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.grey[200] : props.theme.palette.grey[900]};
  }

  .fee-card {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.grey[100] : props.theme.palette.grey[900]};
  }
`;

function OnlinePayment() {
  const theme = useTheme();
  const isLight = useSettings().themeMode === 'light';
  const dispatch = useAppDispatch();
  const [feeTypeFilter, setFeeTypeFilter] = useState(1);
  const [open, setOpen] = useState(false);
  const [selectedFees, setSelectedFees] = useState<FeeDetailsType[]>([]);
  // const [parentPayFeeList, setPrentPayFeeList] = useState<GetParentPayFeeDataType[]>([]);
  const [studentDetails, setStudentDetails] = useState<StudentDetailsType | undefined>(undefined);
  const [feeDetailsList, setFeeDetailsList] = useState<FeeDetailsType[]>([]);
  const [termFeeDetailsList, setTermFeeDetailsList] = useState<TermFeeDetailsType[]>([]);
  const { academicId, sectionId, classId, studentId, feeTypeId } = studentDetails || {};

  const parentPayFee = useAppSelector(getParentPayFeeData);
  const parentPayFeeStatus = useAppSelector(getParentPayFeeStatus);

  const queryParams = new URLSearchParams(window.location.search);
  const term = queryParams.get('StudentId');

  const currentParentPayFeeRequest = React.useMemo(
    () => ({
      studentId: term,
      feeTypeId: feeTypeFilter,
    }),
    [feeTypeFilter, term]
  );

  const loadParentPayFeeList = useCallback(
    (request: any) => {
      dispatch(fetchParentPayFee(request));
    },
    [dispatch]
  );

  React.useEffect(() => {
    if (parentPayFeeStatus === 'idle') {
      loadParentPayFeeList(currentParentPayFeeRequest);
    }
    if ((parentPayFee && Array.isArray(parentPayFee.feeDetails)) || Array.isArray(parentPayFee.termFeeDetails)) {
      const feeDetails = parentPayFee.feeDetails.map((item: FeeDetailsType) => item);
      const termFeeDetails = parentPayFee.termFeeDetails.map((item: TermFeeDetailsType) => item);
      setFeeDetailsList(feeDetails);
      setTermFeeDetailsList(termFeeDetails);

      setStudentDetails(parentPayFee.studentDetails);
    } else {
      setTermFeeDetailsList([]);
    }
  }, [dispatch, parentPayFeeStatus, parentPayFee, loadParentPayFeeList, currentParentPayFeeRequest]);

  const paymentFormHandle = useRef<PaymentLaunchFormHandle>(null);
  const [createOrderStatus, setCreateOrderStatus] = useState<FetchStatus>('idle');
  const [orderResponse, setOrderResponse] = useState<PaymentOrderResponse | null>(null);
  const [readyToSubmit, setReadyToSubmit] = useState(false);

  const resetPaymentData = () => {
    setCreateOrderStatus('idle');
    setOrderResponse(null);
  };

  useEffect(() => {
    if (readyToSubmit && createOrderStatus === 'success' && orderResponse !== null && paymentFormHandle.current) {
      paymentFormHandle.current.submitForm();
      setReadyToSubmit(false);
    }
  }, [createOrderStatus, orderResponse, readyToSubmit]);

  useEffect(() => {
    return () => {
      resetPaymentData();
    };
  }, []);

  const handleFeeTypeChange = (e: SelectChangeEvent) => {
    const feeTypeIdNumber = parseInt(e.target.value, 10);
    setFeeTypeFilter(feeTypeIdNumber);
    loadParentPayFeeList({ ...currentParentPayFeeRequest, feeTypeId: e.target.value });
  };

  const handleCheckboxChange = (row: FeeDetailsType) => {
    setSelectedFees((prevSelected) => {
      if (prevSelected.includes(row)) {
        return prevSelected.filter((i) => i !== row);
      } else {
        return [...prevSelected, row];
      }
    });
  };

  const handleSelectAll = () => {
    if (selectedFees.length === feeDetailsList.length) {
      setSelectedFees([]);
    } else {
      setSelectedFees(feeDetailsList.map((row) => row.payableAmount !== 0 && row));
    }
  };

  const calculateTotal = useCallback(() => {
    return selectedFees.reduce((sum, row) => {
      const feeDetail = feeDetailsList.find((item) => item.particularId === row.particularId);
      return feeDetail ? sum + feeDetail.payableAmount : sum;
    }, 0);
  }, [selectedFees, feeDetailsList]);

  const handlePaymentClick = useCallback(async () => {
    console.log('Here');
    setCreateOrderStatus('loading');

    try {
      const termFeeDetailsArray = termFeeDetailsList
        .filter((item) => selectedFees.find((selected) => selected.particularId === item.particularId))
        .map((detail) => ({
          feeid: detail.feeId, // Assuming termFeeDetailsList items have these properties
          termid: detail.termId,
          totalamount: detail.totalAmount,
          scholarship: detail.scholarship || 0, // Use default value if not available
          discount: detail.discount || 0,
          fine: detail.fine || 0,
          payableamount: detail.payableAmount,
        }));
      console.log(termFeeDetailsArray);
      const testId = nanoid();
      const orderRequest: PaymentOrderRequest = {
        academicid: academicId,
        sectionid: sectionId,
        classid: classId,
        studentid: studentId,
        feetypeid: feeTypeId,
        devicetype: 'Web',
        totalamount: calculateTotal(),
        payableamount: calculateTotal(),
        items: termFeeDetailsArray,

        user_agent: window.navigator.userAgent,
        browser_tz: new Date().getTimezoneOffset().toString(),
        browser_color_depth: window.screen.colorDepth.toString(),
        browser_java_enabled: 'false',
        browser_screen_height: window.screen.height.toString(),
        browser_screen_width: window.screen.width.toString(),
        browser_language: window.navigator.language,
        additional_info1: `Fee payment Test - ${testId} `,
        additional_info2: `Academic fee paying - ${testId}`,
      };

      const createOrderRespose = await api.Payment.createOrder(orderRequest);
      if (createOrderRespose.status === 'success') {
        console.log(createOrderRespose);
        setOrderResponse(createOrderRespose.data);
        setCreateOrderStatus('success');
        setReadyToSubmit(true);
      } else {
        // Handle unsuccessful response
        console.error('Order creation failed:', createOrderRespose);
        setCreateOrderStatus('error');
      }
    } catch (error) {
      console.error('Error occurred during order creation:', error);
      setCreateOrderStatus('error');
    }
  }, [academicId, classId, feeTypeId, sectionId, selectedFees, calculateTotal, studentId, termFeeDetailsList]);

  return (
    <>
      <OnlinePaymentRoot>
        {/* <Box boxShadow={5} display="flex" alignItems="center" sx={{ backgroundColor: '#fff', height: 50 }}>
          <Typography variant="subtitle2" fontSize={20} pl={{ xs: 3.5, md: 5 }} color={theme.palette.success.main}>
            Online Payment
          </Typography>
        </Box> */}
        <Card className="Card" elevation={1} sx={{ m: '1rem', px: { xs: 3, md: 3 }, py: { xs: 2, md: 2 } }}>
          {/* <Typography variant="h6" fontSize={18}>
            Pay Fee
          </Typography>
          <Divider /> */}

          <Grid container spacing={10} direction="row" justifyContent="center">
            <Grid item lg={6} xs={12} direction="row" justifyContent="center">
              <Stack direction={{ xs: 'column', sm: 'row' }} alignItems="end" justifyContent="space-between" mb={1}>
                <FormControl sx={{ width: { xs: '100%', sm: '30%' }, mb: 1 }}>
                  {/* <Typography variant="subtitle1" mt={1} fontSize={14}>
                    Select Fee Type
                  </Typography> */}
                  <Select
                    labelId="feeTypeFilter"
                    id="feeTypeFilterSelect"
                    value={feeTypeFilter.toString()}
                    onChange={handleFeeTypeChange}
                    placeholder="Select Year"
                    // sx={{ mx: 0.3 }}
                  >
                    <MenuItem sx={{ display: 'none' }} value={0}>
                      Select Fee Type
                    </MenuItem>
                    {FEE_TYPE_ID_OPTIONS.map((opt) => (
                      <MenuItem key={opt.id} value={opt.id}>
                        {opt.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
                {/* <Button variant="outlined" color="success" sx={{ mb: 2 }}></Button> */}
                <FormControlLabel
                  control={
                    <Checkbox
                      icon={<RadioButtonUncheckedIcon />}
                      checkedIcon={<SuccessIcon />}
                      size="small"
                      color="success"
                      checked={selectedFees.length === feeDetailsList.length}
                      onClick={handleSelectAll}
                    />
                  }
                  label={selectedFees.length === feeDetailsList.length ? 'Deselect All' : 'Select All'}
                />
              </Stack>
              <Box sx={{ boxShadow: 0 }}>
                <TableContainer
                  sx={{
                    // height: { xs: 'calc(100vh - 335px)', sm: 'calc(100vh - 305px)' },
                    height: { xs: 'calc(100vh - 238px)', sm: 'calc(100vh - 208px)' },
                    overflow: 'auto',
                    '&::-webkit-scrollbar-thumb': {
                      backgroundColor: theme.palette.success.lighter,
                    },
                    // px: { xs: 0.5, md: 2 },
                  }}
                >
                  <Table stickyHeader aria-label="simple table">
                    <TableBody>
                      {feeDetailsList.map((row) => (
                        <TableRow key={row.particularId} sx={{ boxShadow: 0 }}>
                          <Card
                            className="fee-card"
                            sx={{
                              // border: '0.5px solid rgba(0, 0, 0, 0.12)',
                              mb: 1.5,
                              mx: 0.5,
                              py: 1,
                              pr: 2,
                              pl: 2.5,
                              boxShadow: 2,
                              display: 'flex',
                              justifyContent: 'space-between',
                            }}
                          >
                            <FormControlLabel
                              label={row.particularTitle}
                              control={
                                <Checkbox
                                  icon={<RadioButtonUncheckedIcon />}
                                  checkedIcon={<SuccessIcon />}
                                  size="small"
                                  disabled={row.payableAmount === 0}
                                  color="success"
                                  checked={selectedFees.includes(row)}
                                  onChange={() => handleCheckboxChange(row)}
                                />
                              }
                            />
                            {/* <Typography variant="subtitle1" fontSize={13}>
                                {row.feeName}
                              </Typography> */}
                            <Stack direction="row" alignItems="center">
                              <CurrencyRupeeIcon sx={{ fontSize: 14 }} />
                              <Typography fontSize={14} fontWeight={600} sx={{ display: 'flex', alignItems: 'center' }}>
                                {row.payableAmount}
                              </Typography>
                            </Stack>
                          </Card>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Box>
              <Box mx={2} pt={2} display="flex" alignItems="center" justifyContent="space-between">
                <Typography variant="subtitle2" fontSize={16} fontWeight={600}>
                  Total
                </Typography>
                <Stack direction="row" alignItems="center">
                  <CurrencyRupeeIcon sx={{ fontSize: 16 }} />
                  <Typography fontSize={16} fontWeight={600} sx={{ display: 'flex', alignItems: 'center' }}>
                    {calculateTotal()}
                  </Typography>
                </Stack>
              </Box>
              <LoadingButton
                fullWidth
                disabled={createOrderStatus === 'loading' || selectedFees.length === 0}
                loading={createOrderStatus === 'loading'}
                onClick={handlePaymentClick}
                variant="contained"
                color="info"
                sx={{
                  backgroundColor: '#06336a',
                  '&:hover': { backgroundColor: '#06336a' },
                  mt: 2,
                  py: 1.5,
                  fontSize: 20,
                }}
              >
                Pay Now
              </LoadingButton>
            </Grid>
          </Grid>
        </Card>
        {createOrderStatus === 'success' && orderResponse !== null && (
          <PaymentLaunchForm ref={paymentFormHandle} orderResponse={orderResponse} />
        )}
      </OnlinePaymentRoot>
      <Popup
        size="xs"
        state={open}
        onClose={() => setOpen(false)}
        popupContent={
          <Stack direction="column" alignItems="center" justifyContent="center">
            <Lottie animationData={PaySuccess} loop={false} />
            <Typography
              position="absolute"
              // color={theme.palette.success.main}
              top={{ xs: 310, sm: 380 }}
              fontSize={20}
              fontWeight={600}
              sx={{ pr: 1, display: 'flex', alignItems: 'center' }}
            >
              Payment Successful
            </Typography>
          </Stack>
        }
      />
      {/* <Popup size="md" state={popup2} onClose={handleClickCloseReceipt2} popupContent={<DetailedReceipt />} /> */}
    </>
  );
}

export default OnlinePayment;
