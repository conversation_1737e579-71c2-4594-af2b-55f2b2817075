/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Typography,
  Card,
} from '@mui/material';
import styled from 'styled-components';
import { STATUS_SELECT, YEAR_SELECT } from '@/config/Selection';
import Popup from '@/components/shared/Popup/Popup';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';

const PublishResultRoot = styled.div`
  padding: 1rem;

  .Card {
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }
  }
`;

interface ClassData {
  id: number;
  className: string;
  classDescription: string;
  classTeacher: string;
  status: string;
}

const dummyData: ClassData[] = [
  {
    id: 1,
    className: 'VIII-A',
    classDescription: '2022-2023',
    classTeacher: 'Passdaily Admin 1',
    status: 'Published',
  },
  {
    id: 2,
    className: 'VIII-B',
    classDescription: '2022-2023',
    classTeacher: 'Passdaily Admin 2',
    status: 'Unpublished',
  },
  {
    id: 3,
    className: 'VIII-C',
    classDescription: '2023-2024',
    classTeacher: 'Passdaily Admin 3',
    status: 'Published',
  },
  {
    id: 4,
    className: 'IX-A',
    classDescription: '2023-2024',
    classTeacher: 'Passdaily Admin 4',
    status: 'Unpublished',
  },
  {
    id: 5,
    className: 'IX-B',
    classDescription: '2023-2024',
    classTeacher: 'Passdaily Admin 5',
    status: 'Published',
  },
];

function ResultPublish() {
  const [popup, setPopup] = React.useState(false);

  const handleClickOpen = () => {
    setPopup(true);
  };

  const handleClickClose = () => setPopup(false);
  return (
    <Page title="Publish Result">
      <PublishResultRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17} width="80%">
              Publish Result
            </Typography>
          </Stack>
          <Divider />
          <Box>
            <Grid pb={4} pt={2} container spacing={3}>
              <Grid item lg={4} xs={12}>
                <Typography variant="h6" fontSize={14}>
                  Select Year
                </Typography>
                <Autocomplete
                  options={YEAR_SELECT}
                  renderInput={(params) => <TextField {...params} placeholder="Select" />}
                />
              </Grid>
              <Grid item lg={4} xs={12}>
                <Typography variant="h6" fontSize={14}>
                  Select Status
                </Typography>
                <Autocomplete
                  options={STATUS_SELECT}
                  renderInput={(params) => <TextField {...params} placeholder="Select" />}
                />
              </Grid>
              <Grid item lg={4} xs={12}>
                <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                  <Button variant="contained" color="secondary" fullWidth>
                    Reset
                  </Button>
                  <Button variant="contained" color="primary" fullWidth>
                    Search
                  </Button>
                </Stack>
              </Grid>
            </Grid>

            <Box>
              <Paper
                sx={{
                  border: `1px solid #e8e8e9`,
                  width: '100%',
                  overflow: 'auto',
                  '&::-webkit-scrollbar': {
                    width: 0,
                  },
                }}
              >
                <TableContainer
                  sx={{
                    width: { xs: '700px', md: '100%' },
                  }}
                >
                  <Table stickyHeader aria-label="simple table">
                    <TableHead>
                      <TableRow>
                        <TableCell>Sl.No</TableCell>
                        <TableCell>Class Name</TableCell>
                        <TableCell>Academic Year</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell>Action</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {dummyData.map((row) => (
                        <TableRow hover key={row.id}>
                          <TableCell>{row.id}</TableCell>
                          <TableCell>{row.className}</TableCell>
                          <TableCell>{row.classDescription}</TableCell>
                          <TableCell>
                            <Paper
                              sx={{
                                display: 'flex',
                                justifyContent: 'center',
                                p: 0.5,
                                maxWidth: 95,
                                borderRadius: '20px',
                                backgroundColor: '#F5FCF8',
                                color: 'green',
                                border: '1px solid green',
                              }}
                            >
                              <Typography fontSize={13}>{row.status}</Typography>
                            </Paper>
                          </TableCell>
                          <TableCell width="15%">
                            <Button
                              sx={{ minWidth: 100 }}
                              size="small"
                              variant={row.status === 'Published' ? 'contained' : 'outlined'}
                              onClick={handleClickOpen}
                            >
                              {row.status === 'Unpublished' ? 'Publish' : 'Unpublish'}
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Paper>
            </Box>
          </Box>
        </Card>
      </PublishResultRoot>
      <Popup
        size="xs"
        state={popup}
        onClose={handleClickClose}
        popupContent={<SuccessMessage message=" Updated Successfully" />}
      />
    </Page>
  );
}

export default ResultPublish;
