/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import {
  Autocomplete,
  Grid,
  Chip,
  Stack,
  <PERSON>lapse,
  TextField,
  Button,
  Typography,
  Box,
  FormControl,
  Radio,
  RadioGroup,
  FormControlLabel,
} from '@mui/material';
import styled from 'styled-components';
import { SUBJECT_SELECT, YEAR_SELECT } from '@/config/Selection';

const CreateQuestionsRoot = styled.div`
  width: 100%;
  padding: 0.5rem 1.5rem;
`;
const CLASS_SELECT = ['VIII A', 'VIII B', 'VIII C', 'IX A', 'X A'];

function CreateQuestions() {
  const [showMore, setShowMore] = React.useState(false);
  return (
    <CreateQuestionsRoot>
      <form noValidate>
        <Grid container spacing={3}>
          <Grid item md={4} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Academic Year
              </Typography>
              <Autocomplete
                options={YEAR_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select year" />}
              />
            </FormControl>
          </Grid>
          <Grid item md={4} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Class
              </Typography>
              <Autocomplete
                options={CLASS_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select Class" />}
              />
            </FormControl>
          </Grid>
          <Grid item md={4} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Subject
              </Typography>
              <Autocomplete
                options={SUBJECT_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select Subject" />}
              />
            </FormControl>
          </Grid>
          <Grid item md={6} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Exam
              </Typography>
              <Autocomplete
                options={['Annual Exam', 'Monthly Test 1', 'Monthly Test 2', 'Monthly Test 3']}
                renderInput={(params) => <TextField {...params} placeholder="Select Exam" />}
              />
            </FormControl>
          </Grid>
          <Grid item md={6} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Exam Type
              </Typography>
              <Autocomplete
                options={['Objective(MCQ) Type', 'Descriptive Type']}
                renderInput={(params) => <TextField {...params} placeholder="Select Type" />}
              />
            </FormControl>
          </Grid>
          <Grid item md={6} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Qusetion Type
              </Typography>
              <Autocomplete
                options={['Text Only', 'Text with Image', 'Text with Video']}
                renderInput={(params) => <TextField {...params} placeholder="Select File" />}
              />
            </FormControl>
          </Grid>
          <Grid item md={6} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Image/Video
              </Typography>
              <TextField type="file" inputProps={{ accept: 'image/*, video/*' }} placeholder="Select Type" />
            </FormControl>
          </Grid>
          <Grid item xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Question
              </Typography>
              <TextField multiline minRows={2} placeholder="Enter Question Here.." />
            </FormControl>
          </Grid>
        </Grid>

        <Box width="100%" display="flex" pt={2} justifyContent="flex-end">
          <Chip
            size="small"
            label="Add Answers"
            color="primary"
            variant="outlined"
            sx={{ border: 0, textDecorationLine: 'underline' }}
            clickable
            onClick={() => setShowMore(!showMore)}
          />
        </Box>
        <Box width="100%">
          <Collapse in={showMore}>
            <Grid container spacing={2}>
              <Grid item md={6} xs={12}>
                <FormControl fullWidth>
                  <Typography variant="subtitle2" fontSize={14} color="GrayText">
                    Option 1
                  </Typography>
                  <TextField fullWidth placeholder="Enter Here" />
                </FormControl>
              </Grid>
              <Grid item md={6} xs={12}>
                <FormControl fullWidth>
                  <Typography variant="subtitle2" fontSize={14} color="GrayText">
                    Option 2
                  </Typography>
                  <TextField fullWidth placeholder="Enter Here" />
                </FormControl>
              </Grid>
              <Grid item md={6} xs={12}>
                <FormControl fullWidth>
                  <Typography variant="subtitle2" fontSize={14} color="GrayText">
                    Option 3
                  </Typography>
                  <TextField fullWidth placeholder="Enter Here" />
                </FormControl>
              </Grid>
              <Grid item md={6} xs={12}>
                <FormControl fullWidth>
                  <Typography variant="subtitle2" fontSize={14} color="GrayText">
                    Option 4
                  </Typography>
                  <TextField fullWidth placeholder="Enter Here" />
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <FormControl>
                  <Stack direction="row" spacing={2} alignItems="center">
                    <Typography variant="subtitle2" fontSize={14} color="GrayText">
                      Correct Answer :
                    </Typography>
                    <RadioGroup row aria-labelledby="demo-row-radio-buttons-group-label" name="row-radio-buttons-group">
                      <FormControlLabel value="A" control={<Radio />} label="A" />
                      <FormControlLabel value="B" control={<Radio />} label="B" />
                      <FormControlLabel value="C" control={<Radio />} label="C" />
                      <FormControlLabel value="D" control={<Radio />} label="D" />
                    </RadioGroup>
                  </Stack>
                </FormControl>
              </Grid>
              <Grid item md={8} xs={12}>
                <FormControl fullWidth>
                  <Typography variant="subtitle2" fontSize={14} color="GrayText">
                    Answer Explaination
                  </Typography>
                  <TextField multiline placeholder="Enter Explaination Here.." />
                </FormControl>
              </Grid>
              <Grid item md={4} xs={12}>
                <FormControl fullWidth>
                  <Typography variant="subtitle2" fontSize={14} color="GrayText">
                    Image/Video
                  </Typography>
                  <TextField type="file" inputProps={{ accept: 'image/*, video/*' }} placeholder="Select Type" />
                </FormControl>
              </Grid>
            </Grid>
          </Collapse>
        </Box>
        <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 3 }}>
          <Stack spacing={2} direction="row">
            <Button variant="contained" color="secondary">
              Cancel
            </Button>
            <Button variant="contained" color="primary">
              Add
            </Button>
          </Stack>
        </Box>
      </form>
    </CreateQuestionsRoot>
  );
}

export default CreateQuestions;
