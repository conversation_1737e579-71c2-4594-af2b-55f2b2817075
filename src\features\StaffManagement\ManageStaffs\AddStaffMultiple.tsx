import { useEffect, useCallback, useRef, useState, FormEvent } from 'react';
import { GENDER_SELECT, STAFF_CATEGORY_SELECT, STATUS_OPTIONS } from '@/config/Selection';
import {
  Stack,
  Typography,
  Button,
  Table,
  TableHead,
  TableRow,
  Paper,
  TableBody,
  Grid,
  TableContainer,
  TextField,
  MenuItem,
  Select,
  IconButton,
  Card,
  Tooltip,
  Alert,
  Avatar,
  useTheme,
  SelectChangeEvent,
} from '@mui/material';
import { FormikErrors, useFormik } from 'formik';
import styled from 'styled-components';
import { v4 as uuidv4 } from 'uuid';
import CloseIcon from '@mui/icons-material/Close';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import Page from '@/components/shared/Page';
import ErrorIcon from '@mui/icons-material/Error';
import Success from '@/assets/ManageFee/Success.json';
import errorIcon from '@/assets/ManageFee/Error.json';
import errorFailedIcon from '@/assets/ManageFee/ErrorIcon.json';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import * as Yup from 'yup';
import api from '@/api';
import BackButton from '@/components/shared/BackButton';
import useSettings from '@/hooks/useSettings';
import { keyframes } from '@emotion/react';
import DatePickers from '@/components/shared/Selections/DatePicker';
import { StaffCreateRow, StaffListInfo } from '@/types/StaffManagement';
import useAuth from '@/hooks/useAuth';
import dayjs from 'dayjs';
import { getStaffSubmitting, getYearData } from '@/config/storeSelectors';
import { useAppSelector } from '@/hooks/useAppSelector';
import { addNewMultipleStaff } from '@/store/StaffMangement/StaffMangement/staffMangement.thunks';
import LoadingButton from '@mui/lab/LoadingButton';
import SaveIcon from '@mui/icons-material/Save';
import { ErrorMessage } from '@/components/shared/Popup/ErrorMessage';

export type AddStaffMultipleProps = {
  onBackClick: () => void;
  staffDetails: StaffListInfo;
  // onSave: (values: StaffListInfo, mode: 'create' | 'edit') => void;
};

export type AddStaffMultipleState = {
  staff: StaffCreateRow[];
};

const AddStaffMultipleRoot = styled.div`
  padding: 1rem;

  .Card {
    height: calc(100vh - 160px);
    display: flex;
    flex-direction: column;

    form {
      height: calc(100% - 40px);
      display: flex;
      flex-direction: column;

      .form-container {
        flex-grow: 1;
        height: calc(100% - 84px);

        .MuiTableContainer-root {
          height: 100%;
          overflow: auto;
          overflow-x: hidden;
        }
      }

      .button-container {
        border-top: 1px solid #ddd;
      }
    }
    .icon-wrapper {
      position: absolute;
      bottom: 0px;
      right: 0px;
    }
    .date-feild {
      .MuiStack-root {
        width: 100%;
      }
    }
    @media screen and (max-width: 380px) {
      form {
        height: calc(100% - 70px);
      }
      .staff_info_header {
        flex-wrap: wrap;
      }
    }
  }
`;

const formValidationSchema = Yup.object({
  staff: Yup.array().of(
    Yup.object({
      staffCode: Yup.string()
        .required('Please enter staff code')
        .test('exists', 'Staff code already used', async (val) => {
          try {
            if (val) {
              const existsResponse = await api.StaffManagement.StaffCodeExists(val);
              return !existsResponse.data;
            }
            return true;
          } catch {
            return true;
          }
        }),
      staffName: Yup.string()
        .required('First name is required')
        .min(2, 'First name must be at least 2 characters long'),
      staffGender: Yup.string().oneOf(['0', '1'], 'Please select a gender').required(),
      staffPhoneNumber: Yup.string()
        .required('Please enter a phone number')
        .matches(/^\d{10}$/, 'Phone number must be exactly 10 digits'),
      // staffEmailID: Yup.string().required('Please enter an email').email('Enter a valid email'),
      staffJoinDate: Yup.string().required('Please select the joining date'),
      staffDOB: Yup.string().required('Please select the date of birth'),
      staffBloodGroup: Yup.string().oneOf(
        ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'],
        'Please select a valid blood group'
      ),
      academicId: Yup.string().oneOf(['11', '10'], 'Please select a year').required(),
    })
  ),
});

const rotateAnimation = keyframes`
  0% {
    transform: rotate(360deg);
  }
  100% {
    transform: rotate(0deg);
  }
`;
function AddStaffMultiple({ onBackClick, staffDetails }: AddStaffMultipleProps) {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const { user } = useAuth();
  const adminId: number = user ? user.accountId : 0;
  const dispatch = useAppDispatch();
  const { confirm } = useConfirm();
  const YearData = useAppSelector(getYearData);
  const [academicYearFilter, setAcademicYearFilter] = useState(0);
  const [genderFilter, setGenderFilter] = useState(-1);

  const [error, setError] = useState<string | null>(null);
  const scrollContainerRef = useRef<any>(null);
  const textBoxRefs = useRef<(HTMLInputElement | null)[]>([]);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [isAnimating, setIsAnimating] = useState(false);
  const [isSolid, setIsSolid] = useState(false);
  const isSubmitting = useAppSelector(getStaffSubmitting);

  const initialRowKey = uuidv4();

  const getNewRow = useCallback(
    (): StaffCreateRow => ({ rowKey: uuidv4(), className: '', classDescription: '', classStatus: 1 }),
    []
  );

  const defaultRow: StaffListInfo = {
    rowKey: initialRowKey,
    // staffID: staffDetails.staffID,
    academicId: YearData[0]?.accademicId,
    staffCode: staffDetails.staffCode,
    staffJoinDate: staffDetails.staffJoinDate,
    staffName: staffDetails.staffName,
    staffGender: staffDetails.staffGender,
    staffDOB: staffDetails.staffDOB,
    staffBloodGroup: staffDetails.staffBloodGroup,
    staffBirthPlace: staffDetails.staffBirthPlace,
    staffNationality: staffDetails.staffNationality,
    staffMotherTongue: staffDetails.staffMotherTongue,
    staffReligion: staffDetails.staffReligion,
    staffCaste: staffDetails.staffCaste,
    staffFatherName: staffDetails.staffFatherName,
    staffMotherName: staffDetails.staffMotherName,
    staffFatherOccupation: staffDetails.staffFatherOccupation,
    staffJobExperience: staffDetails.staffJobExperience,
    staffJobRole: staffDetails.staffJobRole,
    staffJobDescription: staffDetails.staffJobDescription,
    staffPhoneNumber: staffDetails.staffPhoneNumber,
    staffPAddress: staffDetails.staffPAddress,
    staffCAddress: staffDetails.staffCAddress,
    staffEmailID: staffDetails.staffEmailID,
    staffImage: staffDetails.staffImage,
    staffCategory: staffDetails.staffCategory,
    createdBy: adminId,
    staffCastType: '',
    staffStatus: '',
  };

  const { values, handleChange, handleBlur, handleSubmit, setFieldValue, touched, errors, resetForm } =
    useFormik<AddStaffMultipleState>({
      initialValues: {
        staff: [defaultRow],
      },
      onSubmit: async (data) => {
        console.log('data::::----', data.staff);
        try {
          const response = await dispatch(addNewMultipleStaff(data.staff)).unwrap();

          if (response) {
            const successMessage = (
              <SuccessMessage
                loop={false}
                jsonIcon={Success}
                message={`${data.staff.length} Staffs created successfully`}
              />
            );
            await confirm(successMessage, 'Staffs Created', { okLabel: 'Ok', showOnlyOk: true });
            resetForm();
            setFieldValue('staff', [defaultRow]);
            // loadStaffList();
            // if (response.inserted) {
            if (response) {
              setError(null);
              onBackClick();
            } else {
              setError('Something went wrong in creating staff');
            }
          } else {
            const errorMessage = (
              <ErrorMessage loop={false} jsonIcon={errorFailedIcon} message="Staff created failed" />
            );
            await confirm(errorMessage, 'Staff Create', { okLabel: 'Ok', showOnlyOk: true });
          }
        } catch {
          const errorMessage = (
            <ErrorMessage loop={false} jsonIcon={errorFailedIcon} message="Something went wrong in creating staff" />
          );
          await confirm(errorMessage, 'Staff Create', { okLabel: 'Ok', showOnlyOk: true });
          setError('Something went wrong in creating staff');
        }
      },
      validateOnBlur: false,
      validationSchema: formValidationSchema,
      validate: (staffVals) => {
        const errorObj: any = {};
        staffVals.staff.forEach(async (staffRow, rowIndex, arr) => {
          if (arr.some((x, i) => staffRow.staffCode !== '' && x.staffCode === staffRow.staffCode && i !== rowIndex)) {
            if (!errorObj.staff) {
              errorObj.staff = [];
            }
            errorObj.staff[rowIndex] = {};
            errorObj.staff[rowIndex].staffCode = 'Duplicate Staff code';
          }
        });
        return errorObj;
      },
    });

  useEffect(() => {
    textBoxRefs.current[values.staff.length - 1]?.focus();
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollTop = scrollContainerRef.current.scrollHeight;
    }
  }, [values.staff.length, YearData]);

  const handleAddNewRow = () => {
    const newRow = {
      // staffID: 0,
      academicId: YearData[0]?.accademicId,
      staffCode: '',
      staffJoinDate: '',
      staffName: '',
      staffGender: -1,
      staffDOB: '',
      staffBloodGroup: '',
      staffBirthPlace: '',
      staffNationality: '',
      staffMotherTongue: '',
      staffReligion: '',
      staffCaste: '',
      staffFatherName: '',
      staffMotherName: '',
      staffFatherOccupation: '',
      staffJobExperience: '',
      staffJobRole: '',
      staffJobDescription: '',
      staffPhoneNumber: '',
      staffPAddress: '',
      staffCAddress: '',
      staffEmailID: '',
      staffImage: '',
      staffCategory: 0,
      createdBy: adminId,
      rowKey: uuidv4(), // Ensure each row has a unique key
    };
    setFieldValue('staff', [...values.staff, newRow]);
  };

  // Handle image change
  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>, rowIndex: number) => {
    if (event.target.files && event.target.files.length > 0) {
      setIsAnimating(true); // Start the animation
      const file = event.target.files[0];
      const imageUrl = URL.createObjectURL(file);
      setSelectedImage(imageUrl);
      setFieldValue(`staff[${rowIndex}].staffImage`, imageUrl);
      // Clear input value to allow re-uploading the same file
      event.target.value = '';
    }
  };

  const fileInputRefs = useRef<Array<HTMLInputElement | null>>([]);

  const setFileInputRef = useCallback((el: HTMLInputElement | null, rowIndex: number) => {
    fileInputRefs.current[rowIndex] = el;
  }, []);

  const handleAvatarClick = (rowIndex: number) => {
    fileInputRefs.current[rowIndex]?.click();
  };
  // const handleAvatarClick = () => {
  //   document.getElementById('imageInput')?.click();
  // };

  const handleRemoveImage = (rowIndex: number) => {
    setSelectedImage('');
    setFieldValue(`staff[${rowIndex}].staffImage`, '');
    setIsAnimating(false); // Stop the animation when image is removed
    setIsSolid(false);
  };

  const handleYearChange = (e: SelectChangeEvent, rowIndex: number) => {
    setAcademicYearFilter(parseInt(YearData.filter((item) => item.accademicId === e.target.value)[0].accademicId, 10));
    const selectedYearId = parseInt(e.target.value, 10);
    setFieldValue(`staff[${rowIndex}].academicId`, selectedYearId);
  };

  const handleGenderChange = (e: SelectChangeEvent, rowIndex: number) => {
    const selectedGenderId = parseInt(e.target.value, 10);
    // parseInt(GENDER_SELECT.filter((item) => item.id === e.target.value)[0].id, 10)
    setGenderFilter(selectedGenderId);

    setFieldValue(`staff[${rowIndex}].staffGender`, selectedGenderId);
  };

  const handleRowDelete = (rowkey: string) => {
    const updatedRows = values.staff.filter((x) => x.rowKey !== rowkey);
    setFieldValue('staff', updatedRows);
  };

  const hasStaffCodeFieldError = (rowIndex: number) => {
    if (touched.staff && touched.staff.length > 0 && errors.staff && errors.staff.length > 0) {
      return (
        !!touched.staff[rowIndex]?.staffCode && !!(errors.staff as FormikErrors<StaffCreateRow>[])[rowIndex]?.staffCode
      );
    }
    return false;
  };
  const getStaffCodeFieldError = (rowIndex: number) => {
    return (errors.staff as FormikErrors<StaffCreateRow>[])[rowIndex]?.staffCode;
  };
  const hasStaffNameFieldError = (rowIndex: number) => {
    if (touched.staff && touched.staff.length > 0 && errors.staff && errors.staff.length > 0) {
      return (
        !!touched.staff[rowIndex]?.staffName && !!(errors.staff as FormikErrors<StaffCreateRow>[])[rowIndex]?.staffName
      );
    }
    return false;
  };
  const getStaffNameFieldError = (rowIndex: number) => {
    return (errors.staff as FormikErrors<StaffCreateRow>[])[rowIndex]?.staffName;
  };
  const hasStaffGenderFieldError = (rowIndex: number) => {
    if (touched.staff && touched.staff.length > 0 && errors.staff && errors.staff.length > 0) {
      return (
        !!touched.staff[rowIndex]?.staffGender &&
        !!(errors.staff as FormikErrors<StaffCreateRow>[])[rowIndex]?.staffGender
      );
    }
    return false;
  };

  const getStaffGenderFieldError = (rowIndex: number) => {
    return (errors.staff as FormikErrors<StaffCreateRow>[])[rowIndex]?.staffGender;
  };

  const getStaffYearFieldError = (rowIndex: number) => {
    return (errors.staff as FormikErrors<StaffCreateRow>[])[rowIndex]?.academicId;
  };
  const hasStaffYearFieldError = (rowIndex: number) => {
    if (touched.staff && touched.staff.length > 0 && errors.staff && errors.staff.length > 0) {
      return (
        !!touched.staff[rowIndex]?.academicId &&
        !!(errors.staff as FormikErrors<StaffCreateRow>[])[rowIndex]?.academicId
      );
    }
    return false;
  };

  const hasStaffDOBFieldError = (rowIndex: number) => {
    if (touched.staff && touched.staff.length > 0 && errors.staff && errors.staff.length > 0) {
      return (
        !!touched.staff[rowIndex]?.staffDOB && !!(errors.staff as FormikErrors<StaffCreateRow>[])[rowIndex]?.staffDOB
      );
    }
    return false;
  };
  const getStaffDOBFieldError = (rowIndex: number) => {
    return (errors.staff as FormikErrors<StaffCreateRow>[])[rowIndex]?.staffDOB;
  };
  const hasStaffJoinDateFieldError = (rowIndex: number) => {
    if (touched.staff && touched.staff.length > 0 && errors.staff && errors.staff.length > 0) {
      return (
        !!touched.staff[rowIndex]?.staffJoinDate &&
        !!(errors.staff as FormikErrors<StaffCreateRow>[])[rowIndex]?.staffJoinDate
      );
    }

    return false;
  };
  const getStaffJoinDateFieldError = (rowIndex: number) => {
    return (errors.staff as FormikErrors<StaffCreateRow>[])[rowIndex]?.staffJoinDate;
  };
  const hasStaffPhoneNumberFieldError = (rowIndex: number) => {
    if (touched.staff && touched.staff.length > 0 && errors.staff && errors.staff.length > 0) {
      return (
        !!touched.staff[rowIndex]?.staffPhoneNumber &&
        !!(errors.staff as FormikErrors<StaffCreateRow>[])[rowIndex]?.staffPhoneNumber
      );
    }

    return false;
  };
  const getStaffPhoneNumberFieldError = (rowIndex: number) => {
    return (errors.staff as FormikErrors<StaffCreateRow>[])[rowIndex]?.staffPhoneNumber;
  };

  const handleReset = async () => {
    if (await confirm('Are you sure you want to reset form?', 'Reset?')) {
      resetForm();
      setFieldValue('staff', [defaultRow]);
    }
  };

  const handleResetForm = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    handleReset();
  };

  return (
    <Page title="Create Multiple Classes">
      <AddStaffMultipleRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack
            mb={2}
            direction="row"
            alignItems="center"
            justifyContent="space-between"
            className="staff_info_header"
            spacing={2}
          >
            <BackButton onBackClick={onBackClick} title="Information" />
            <Stack direction="row" justifyContent="end" width="100%">
              <Button
                type="button"
                variant="contained"
                size="small"
                sx={{ borderRadius: 10 }}
                startIcon={<AddIcon />}
                onClick={handleAddNewRow}
              >
                Add New Row
              </Button>
            </Stack>
          </Stack>
          <form noValidate onSubmit={handleSubmit} onReset={handleResetForm}>
            <div className="form-container">
              {!!error && (
                <Alert color="error" sx={{ marginBottom: '10px' }}>
                  {error}
                </Alert>
              )}

              <TableContainer component={Paper} ref={scrollContainerRef}>
                {values.staff.map((staffRow, rowIndex) => (
                  <Card
                    key={staffRow.rowKey}
                    sx={{
                      border: 1,
                      mb: 2,
                      p: 3,
                      boxShadow: 0,
                      bgcolor: isLight ? theme.palette.common.white : theme.palette.grey[900],
                      borderColor: isLight ? theme.palette.grey[300] : theme.palette.grey[900],
                    }}
                  >
                    <Grid
                      container
                      columnSpacing={2}
                      rowSpacing={1}
                      sx={rowIndex === values.staff.length - 1 ? { '& td': { border: 0 } } : null}
                    >
                      <Grid item xs={4} sm={6} md={6} lg={4} xl={1} textAlign="center">
                        <IconButton
                          color="primary"
                          sx={{
                            width: 'fit-content',
                            p: 0,
                            position: 'relative',
                          }}
                        >
                          <Avatar
                            sx={{
                              backgroundColor: isLight ? theme.palette.common.white : theme.palette.grey[900],
                              color: theme.palette.primary.main,
                              cursor: 'pointer',
                              width: '55px',
                              height: '55px',
                              position: 'relative',
                              '&::before': {
                                content: '""',
                                position: 'absolute',
                                top: '0px',
                                left: '0px',
                                right: '0px',
                                bottom: '0px',
                                border: `2px ${theme.palette.primary.main}`,
                                // borderStyle: selectedImage && isSolid ? 'solid' : 'dashed',
                                borderStyle: isAnimating ? 'solid' : 'dashed',
                                borderRadius: '50%',
                                animation: isAnimating ? `${rotateAnimation} .5s linear` : 'none',
                              },
                            }}
                            alt="Student Avatar"
                            src={staffRow.staffImage}
                            onClick={() => handleAvatarClick(rowIndex)} // Trigger file input on click
                          />
                          {staffRow.staffImage ? (
                            <Tooltip title="Remove Image">
                              <CloseIcon
                                className="icon-wrapper"
                                sx={{
                                  fontSize: '15px',
                                  cursor: 'pointer',
                                  bgcolor: theme.palette.error.main,
                                  color: theme.palette.common.white,
                                  borderRadius: '50px',
                                }}
                                onClick={() => handleRemoveImage(rowIndex)} // Handle image removal
                              />
                            </Tooltip>
                          ) : (
                            <AddIcon
                              className="icon-wrapper"
                              sx={{
                                fontSize: '15px',
                                cursor: 'pointer',
                                bgcolor: theme.palette.primary.main,
                                color: theme.palette.common.white,
                                borderRadius: '50px',
                              }}
                              onClick={() => handleAvatarClick(rowIndex)} // Trigger file input on click
                            />
                          )}
                          <input
                            id="imageInput"
                            type="file"
                            accept="image/*"
                            style={{ display: 'none' }}
                            ref={(el) => setFileInputRef(el, rowIndex)}
                            name={`staff[${rowIndex}].staffImage`}
                            onChange={(e) => handleImageChange(e, rowIndex)}
                          />
                        </IconButton>
                      </Grid>
                      <Grid item xs={8} sm={6} md={6} lg={4} xl={1.5}>
                        <Typography variant="subtitle2" fontSize={12}>
                          Staff Code
                        </Typography>
                        <TextField
                          disabled={isSubmitting}
                          name={`staff[${rowIndex}].staffCode`}
                          // label="Class Name"
                          value={staffRow.staffCode}
                          variant="outlined"
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Enter code"
                          fullWidth
                          error={hasStaffCodeFieldError(rowIndex)}
                          inputRef={(el) => {
                            textBoxRefs.current[rowIndex] = el;
                          }}
                          InputProps={{
                            endAdornment: hasStaffCodeFieldError(rowIndex) && (
                              <Tooltip title={getStaffCodeFieldError(rowIndex)} arrow>
                                <ErrorIcon color="error" />
                              </Tooltip>
                            ),
                          }}
                        />
                      </Grid>

                      <Grid item xs={12} sm={6} md={6} lg={4} xl={2.5}>
                        <Typography variant="subtitle2" fontSize={12}>
                          First Name
                        </Typography>
                        <TextField
                          disabled={isSubmitting}
                          name={`staff[${rowIndex}].staffName`}
                          // label="Class Description"
                          value={staffRow.staffName}
                          variant="outlined"
                          onChange={handleChange}
                          onBlur={handleBlur}
                          error={hasStaffNameFieldError(rowIndex)}
                          inputRef={(el) => {
                            textBoxRefs.current[rowIndex] = el;
                          }}
                          // helperText={
                          //   hasStaffNameFieldError(rowIndex) && (
                          //     <Typography color="red" fontSize="12px" ml={2.5} mt={1} variant="subtitle1">
                          //       {getStaffNameFieldError(rowIndex)}
                          //     </Typography>
                          //   )
                          // }
                          InputProps={{
                            endAdornment: hasStaffNameFieldError(rowIndex) && (
                              <Tooltip title={getStaffNameFieldError(rowIndex)} arrow>
                                <ErrorIcon color="error" />
                              </Tooltip>
                            ),
                          }}
                          placeholder="Enter name"
                          fullWidth
                        />
                      </Grid>
                      <Grid item xs={12} sm={6} md={6} lg={4} xl={1.5}>
                        <Typography variant="subtitle2" fontSize={12}>
                          Gender
                        </Typography>
                        <Select
                          disabled={isSubmitting}
                          fullWidth
                          name={`staff[${rowIndex}].staffGender`}
                          value={staffRow.staffGender}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Select gender"
                          error={hasStaffGenderFieldError(rowIndex)}
                          endAdornment={
                            hasStaffGenderFieldError(rowIndex) && (
                              <Tooltip title={getStaffGenderFieldError(rowIndex)} arrow>
                                <ErrorIcon color="error" style={{ marginLeft: 8 }} />
                              </Tooltip>
                            )
                          }
                        >
                          {GENDER_SELECT.map((opt) => (
                            <MenuItem key={opt.id} value={opt.id}>
                              {opt.gender}
                            </MenuItem>
                          ))}
                        </Select>
                      </Grid>
                      <Grid item xs={12} sm={6} md={6} lg={4} xl={2.5} className="date-feild">
                        <Typography variant="subtitle2" fontSize={12}>
                          Date of Birth
                        </Typography>
                        <DatePickers
                          name={`staff[${rowIndex}].staffDOB`}
                          value={dayjs(staffRow.staffDOB, 'DD/MM/YYYY')}
                          onChange={(e) => {
                            const formattedDate = e ? e.format('DD/MM/YYYY') : '';
                            setFieldValue(`staff[${rowIndex}].staffDOB`, formattedDate);
                            console.log('date::::', formattedDate);
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6} md={6} lg={4} xl={2.5} className="date-feild">
                        <Typography variant="subtitle2" fontSize={12}>
                          Joining Date
                        </Typography>
                        <DatePickers
                          name={`staff[${rowIndex}].staffJoinDate`}
                          value={dayjs(staffRow.staffJoinDate, 'DD/MM/YYYY')}
                          onChange={(e) => {
                            const formattedDate = e ? e.format('DD/MM/YYYY') : '';
                            setFieldValue(`staff[${rowIndex}].staffJoinDate`, formattedDate);
                            console.log('date::::', formattedDate);
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6} md={6} lg={4} xl={2.5} className="date-feild">
                        <Typography variant="subtitle2" fontSize={12}>
                          Academic Year
                        </Typography>
                        <Select
                          disabled={isSubmitting}
                          fullWidth
                          name={`staff[${rowIndex}].academicId`}
                          value={staffRow.academicId}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          error={hasStaffYearFieldError(rowIndex)}
                          endAdornment={
                            hasStaffYearFieldError(rowIndex) && (
                              <Tooltip title={getStaffYearFieldError(rowIndex)} arrow>
                                <ErrorIcon color="error" style={{ marginLeft: 8 }} />
                              </Tooltip>
                            )
                          }
                          placeholder="Select Year"
                        >
                          <MenuItem value={0} sx={{ display: 'none' }}>
                            Select Year
                          </MenuItem>
                          {YearData.map((opt) => (
                            <MenuItem key={opt.accademicId} value={opt.accademicId}>
                              {opt.accademicTime}
                            </MenuItem>
                          ))}
                        </Select>
                      </Grid>
                      <Grid item xs={12} sm={6} md={6} lg={4} xl={2.5}>
                        <Typography variant="subtitle2" fontSize={12}>
                          Birth Place
                        </Typography>
                        <TextField
                          disabled={isSubmitting}
                          name={`staff[${rowIndex}].staffBirthPlace`}
                          // label="Class Name"
                          value={staffRow.staffBirthPlace}
                          variant="outlined"
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Enter place"
                          fullWidth
                          inputRef={(el) => {
                            textBoxRefs.current[rowIndex] = el;
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6} md={6} lg={4} xl={1.5}>
                        <Typography variant="subtitle2" fontSize={12}>
                          Blood Group
                        </Typography>
                        <TextField
                          disabled={isSubmitting}
                          name={`staff[${rowIndex}].staffBloodGroup`}
                          // label="Class Name"
                          value={staffRow.staffBloodGroup}
                          variant="outlined"
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Blood Group"
                          fullWidth
                          inputRef={(el) => {
                            textBoxRefs.current[rowIndex] = el;
                          }}
                        />
                      </Grid>

                      <Grid item xs={12} sm={6} md={6} lg={4} xl={2.5}>
                        <Typography variant="subtitle2" fontSize={12}>
                          Nationality
                        </Typography>
                        <TextField
                          disabled={isSubmitting}
                          name={`staff[${rowIndex}].staffNationality`}
                          // label="Class Name"
                          value={staffRow.staffNationality}
                          variant="outlined"
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Enter nationality"
                          fullWidth
                          inputRef={(el) => {
                            textBoxRefs.current[rowIndex] = el;
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6} md={6} lg={4} xl={2.5}>
                        <Typography variant="subtitle2" fontSize={12}>
                          Mother Tongue
                        </Typography>
                        <TextField
                          disabled={isSubmitting}
                          name={`staff[${rowIndex}].staffMotherTongue`}
                          // label="Class Name"
                          value={staffRow.staffMotherTongue}
                          variant="outlined"
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Enter mother tongue"
                          fullWidth
                          inputRef={(el) => {
                            textBoxRefs.current[rowIndex] = el;
                          }}
                        />
                      </Grid>

                      <Grid item xs={12} sm={6} md={6} lg={4} xl={2.5}>
                        <Typography variant="subtitle2" fontSize={12}>
                          Father Name
                        </Typography>
                        <TextField
                          disabled={isSubmitting}
                          name={`staff[${rowIndex}].staffFatherName`}
                          // label="Class Name"
                          value={staffRow.staffFatherName}
                          variant="outlined"
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Enter father name"
                          fullWidth
                          inputRef={(el) => {
                            textBoxRefs.current[rowIndex] = el;
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6} md={6} lg={4} xl={2.5}>
                        <Typography variant="subtitle2" fontSize={12}>
                          Moher Name
                        </Typography>
                        <TextField
                          disabled={isSubmitting}
                          name={`staff[${rowIndex}].staffMotherName`}
                          // label="Class Name"
                          value={staffRow.staffMotherName}
                          variant="outlined"
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Enter mother name"
                          fullWidth
                          inputRef={(el) => {
                            textBoxRefs.current[rowIndex] = el;
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6} md={6} lg={4} xl={1.5}>
                        <Typography variant="subtitle2" fontSize={12}>
                          Caste
                        </Typography>
                        <TextField
                          disabled={isSubmitting}
                          name={`staff[${rowIndex}].staffCaste`}
                          // label="Class Name"
                          value={staffRow.staffCaste}
                          variant="outlined"
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Enter Caste"
                          fullWidth
                          inputRef={(el) => {
                            textBoxRefs.current[rowIndex] = el;
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6} md={6} lg={4} xl={2.5}>
                        <Typography variant="subtitle2" fontSize={12}>
                          Religion
                        </Typography>
                        <TextField
                          disabled={isSubmitting}
                          name={`staff[${rowIndex}].staffReligion`}
                          // label="Class Name"
                          value={staffRow.staffReligion}
                          variant="outlined"
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Enter Religion"
                          fullWidth
                          inputRef={(el) => {
                            textBoxRefs.current[rowIndex] = el;
                          }}
                        />
                      </Grid>

                      <Grid item xs={12} sm={6} md={6} lg={4} xl={2.5}>
                        <Typography variant="subtitle2" fontSize={12}>
                          Father Occupation
                        </Typography>
                        <TextField
                          disabled={isSubmitting}
                          name={`staff[${rowIndex}].staffFatherOccupation`}
                          // label="Class Name"
                          value={staffRow.staffFatherOccupation}
                          variant="outlined"
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Enter father occupation"
                          fullWidth
                          inputRef={(el) => {
                            textBoxRefs.current[rowIndex] = el;
                          }}
                        />
                      </Grid>

                      <Grid item xs={12} sm={6} md={6} lg={4} xl={2.5}>
                        <Typography variant="subtitle2" fontSize={12}>
                          job Role
                        </Typography>
                        <TextField
                          disabled={isSubmitting}
                          name={`staff[${rowIndex}].staffJobRole`}
                          // label="Class Name"
                          value={staffRow.staffJobRole}
                          variant="outlined"
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Enter job role"
                          fullWidth
                          inputRef={(el) => {
                            textBoxRefs.current[rowIndex] = el;
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6} md={6} lg={4} xl={2.5}>
                        <Typography variant="subtitle2" fontSize={12}>
                          Job Description
                        </Typography>
                        <TextField
                          disabled={isSubmitting}
                          name={`staff[${rowIndex}].staffJobDescription`}
                          // label="Class Name"
                          value={staffRow.staffJobDescription}
                          variant="outlined"
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Enter job description"
                          fullWidth
                          inputRef={(el) => {
                            textBoxRefs.current[rowIndex] = el;
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6} md={6} lg={4} xl={1.5}>
                        <Typography variant="subtitle2" fontSize={12}>
                          Job Experience
                        </Typography>
                        <TextField
                          disabled={isSubmitting}
                          name={`staff[${rowIndex}].staffJobExperience`}
                          // label="Class Name"
                          value={staffRow.staffJobExperience}
                          variant="outlined"
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Enter job experience"
                          fullWidth
                          inputRef={(el) => {
                            textBoxRefs.current[rowIndex] = el;
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6} md={6} lg={4} xl={2.5}>
                        <Typography variant="subtitle2" fontSize={12}>
                          Mobile Number
                        </Typography>
                        <TextField
                          disabled={isSubmitting}
                          name={`staff[${rowIndex}].staffPhoneNumber`}
                          // label="Class Name"
                          value={staffRow.staffPhoneNumber}
                          variant="outlined"
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Enter mobile number"
                          fullWidth
                          error={hasStaffPhoneNumberFieldError(rowIndex)}
                          inputRef={(el) => {
                            textBoxRefs.current[rowIndex] = el;
                          }}
                          InputProps={{
                            endAdornment: hasStaffPhoneNumberFieldError(rowIndex) && (
                              <Tooltip title={getStaffPhoneNumberFieldError(rowIndex)} arrow>
                                <ErrorIcon color="error" />
                              </Tooltip>
                            ),
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6} md={6} lg={4} xl={2.5}>
                        <Typography variant="subtitle2" fontSize={12}>
                          Job Email Id
                        </Typography>
                        <TextField
                          disabled={isSubmitting}
                          name={`staff[${rowIndex}].staffEmailID`}
                          // label="Class Name"
                          value={staffRow.staffEmailID}
                          variant="outlined"
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Enter email"
                          fullWidth
                          inputRef={(el) => {
                            textBoxRefs.current[rowIndex] = el;
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6} md={6} lg={4} xl={2.5}>
                        <Typography variant="subtitle2" fontSize={12}>
                          Category
                        </Typography>
                        <Select
                          disabled={isSubmitting}
                          fullWidth
                          name={`staff[${rowIndex}].staffCategory`}
                          value={staffRow.staffCategory}
                          onChange={handleChange}
                          placeholder="Select category"
                        >
                          <MenuItem value={0} sx={{ display: 'none' }}>
                            Select category
                          </MenuItem>
                          {STAFF_CATEGORY_SELECT.map((opt) => (
                            <MenuItem key={opt.id} value={opt.id}>
                              {opt.category}
                            </MenuItem>
                          ))}
                        </Select>
                      </Grid>
                      <Grid item xs={12} sm={6} md={6} lg={4} xl={4}>
                        <Typography variant="subtitle2" fontSize={12}>
                          Permanent Address
                        </Typography>
                        <TextField
                          disabled={isSubmitting}
                          name={`staff[${rowIndex}].staffPAddress`}
                          // label="Class Name"
                          value={staffRow.staffPAddress}
                          variant="outlined"
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Enter address"
                          fullWidth
                          inputRef={(el) => {
                            textBoxRefs.current[rowIndex] = el;
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6} md={6} lg={4} xl={5}>
                        <Typography variant="subtitle2" fontSize={12}>
                          Current Address
                        </Typography>
                        <TextField
                          disabled={isSubmitting}
                          name={`staff[${rowIndex}].staffCAddress`}
                          // label="Class Name"
                          value={staffRow.staffCAddress}
                          variant="outlined"
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Enter address"
                          fullWidth
                          inputRef={(el) => {
                            textBoxRefs.current[rowIndex] = el;
                          }}
                        />
                      </Grid>
                    </Grid>
                    <Stack position="absolute" right={3} top={3}>
                      <IconButton
                        aria-label="Delete Row"
                        color="error"
                        size="small"
                        onClick={() => handleRowDelete(staffRow.rowKey)}
                        disabled={values.staff.length === 1 || isSubmitting}
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </Stack>
                  </Card>
                ))}
              </TableContainer>
            </div>
            <Stack className="button-container" direction="row" justifyContent="end" gap={1} pt={1}>
              <Button
                disabled={isSubmitting}
                size="small"
                type="button"
                color="secondary"
                variant="contained"
                onClick={handleReset}
              >
                Reset
              </Button>
              <LoadingButton
                size="small"
                sx={{ width: 120 }}
                loadingPosition="start"
                disabled={isSubmitting}
                startIcon={<SaveIcon />}
                loading={isSubmitting}
                color="primary"
                variant="contained"
                type="submit"
              >
                {isSubmitting ? 'Saving' : 'Save'}
              </LoadingButton>
            </Stack>
          </form>
        </Card>
      </AddStaffMultipleRoot>
    </Page>
  );
}

export default AddStaffMultiple;
