import React from 'react';
import { Button } from '@mui/material';

type TabButtonProps = {
  onClick?: () => void;
  title: string;
  variant: 'outlined' | 'contained';
};

const TabButton = ({ onClick, title, variant }: TabButtonProps) => {
  return (
    <Button sx={{ borderRadius: '20px', mr: 2 }} size="small" variant={variant} onClick={onClick}>
      {title}
    </Button>
  );
};

export default TabButton;
