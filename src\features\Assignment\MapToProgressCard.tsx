/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Autocomplete,
  Box,
  Grid,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  useTheme,
  FormControl,
  FormControlLabel,
  RadioGroup,
  Radio,
} from '@mui/material';
import styled from 'styled-components';
import { CLASS_SELECT, SUBJECT_SELECT, YEAR_SELECT } from '@/config/Selection';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import useSettings from '@/hooks/useSettings';

const MapToProgressCardRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  .student_card {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  }

  .Card {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    min-height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }
  }
`;

function MapToProgressCard() {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';

  return (
    <Page title="Map To Progress Card">
      <MapToProgressCardRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17} width="100%">
              Map To Progress Card
            </Typography>
          </Stack>

          <form noValidate>
            <Grid pb={3} pt={4} container spacing={4}>
              {/* -------Assignment/HW Details-------- */}
              <Grid item lg={6} md={6} sm={6}>
                <Grid container spacing={2}>
                  <Grid item lg={10} xs={12}>
                    <Typography textAlign="center" variant="h6" fontSize={15}>
                      Assignment/HW Details
                    </Typography>
                  </Grid>

                  <Grid item lg={10} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={14}>
                        Academic Year
                      </Typography>
                      <Autocomplete
                        options={YEAR_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select Year" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg={10} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={14}>
                        Class
                      </Typography>
                      <Autocomplete
                        options={CLASS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select class" />}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item lg={10} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={14}>
                        Subject
                      </Typography>
                      <Autocomplete
                        options={SUBJECT_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select class" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg={10} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={14}>
                        Assignment/HW
                      </Typography>
                      <Autocomplete
                        options={SUBJECT_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select class" />}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item lg={10} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={14}>
                        Category
                      </Typography>
                      <Autocomplete
                        options={SUBJECT_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select class" />}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item lg={10} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={14} mb={1}>
                        Want Conversion
                      </Typography>
                      <RadioGroup
                        defaultValue="yes"
                        row
                        aria-labelledby="demo-row-radio-buttons-group-label"
                        name="row-radio-buttons-group"
                      >
                        <FormControlLabel
                          value="yes"
                          control={<Radio checkedIcon={<CheckCircleIcon />} />}
                          label="Yes"
                        />
                        <FormControlLabel value="no" control={<Radio checkedIcon={<CheckCircleIcon />} />} label="No" />
                      </RadioGroup>
                    </FormControl>
                  </Grid>
                </Grid>
              </Grid>
              {/* -------Progress Card Details-------- */}
              <Grid item lg={6} md={6} sm={6}>
                <Grid container spacing={2}>
                  <Grid item lg={10} xs={12}>
                    <Typography textAlign="center" variant="h6" fontSize={15}>
                      Progress Card Details
                    </Typography>
                  </Grid>
                  <Grid item lg={10} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={14}>
                        Academic Year
                      </Typography>
                      <Autocomplete
                        options={YEAR_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select Year" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg={10} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={14}>
                        Class
                      </Typography>
                      <Autocomplete
                        options={CLASS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select class" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg={10} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={14}>
                        Subject
                      </Typography>
                      <Autocomplete
                        options={SUBJECT_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select class" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg={10} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={14}>
                        Exam
                      </Typography>
                      <Autocomplete
                        options={SUBJECT_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select class" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg={10} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={14}>
                        Passmark
                      </Typography>
                      <Autocomplete
                        options={SUBJECT_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select class" />}
                      />
                    </FormControl>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </form>

          <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' } }}>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="primary">
                Map
              </Button>
            </Stack>
          </Box>
        </Card>
      </MapToProgressCardRoot>
    </Page>
  );
}

export default MapToProgressCard;
