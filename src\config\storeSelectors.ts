import { RootState } from '@/store';

// Common Section Dropdowns data selector functions
//
export const getClassStatus = (state: RootState) => state.dashboard.classList.status;
export const getClassData = (state: RootState) => state.dashboard.classList.data;
export const getClassError = (state: RootState) => state.dashboard.classList.error;
//
export const getYearStatus = (state: RootState) => state.dashboard.yearList.status;
export const getYearData = (state: RootState) => state.dashboard.yearList.data;
export const getYearError = (state: RootState) => state.dashboard.yearList.error;
export const getDashboardYearSubmitting = (state: RootState) => state.dashboard.submitting;


// AcademicMgmt data selector functions
//
//   --Class data selector functions
export const getClassListStatus = (state: RootState) => state.classManagement.classList.status;
export const getClassList = (state: RootState) => state.classManagement.classList.data;
export const getClassListData = (state: RootState) => state.classManagement.classList.data;
export const getClassListError = (state: RootState) => state.classManagement.classList.error;
export const getClassListPageInfo = (state: RootState) => state.classManagement.classList.pageInfo;
export const getSortColumn = (state: RootState) => state.classManagement.classList.sortColumn;
export const getSortDirection = (state: RootState) => state.classManagement.classList.sortDirection;
export const getSubmitting = (state: RootState) => state.classManagement.submitting;
export const getDeletingRecords = (state: RootState) => {
  const keys = Object.keys(state.classManagement.deletingRecords);
  return keys.length > 0 ? keys.map(Number) : [];
};
//   --Class sort data selector functions
export const getClassSortListStatus = (state: RootState) => state.classManagement.classSortList.status;
export const getClassSortListData = (state: RootState) => state.classManagement.classSortList.data;
export const getClassSortListError = (state: RootState) => state.classManagement.classSortList.error;
export const getClassSortListPageInfo = (state: RootState) => state.classManagement.classSortList.pageInfo;
export const getSortClassSortColumn = (state: RootState) => state.classManagement.classSortList.sortColumn;
export const getSortClassSortDirection = (state: RootState) => state.classManagement.classSortList.sortDirection;
export const getClassSortSubmitting = (state: RootState) => state.classManagement.submitting;
//
//   --Section data selector functions
export const getSectionListStatus = (state: RootState) => state.sectionManagement.sectionList.status;
export const getSectionList = (state: RootState) => state.sectionManagement.sectionList.data;
export const getSectionListData = (state: RootState) => state.sectionManagement.sectionList.data;
export const getSectionListError = (state: RootState) => state.sectionManagement.sectionList.error;
export const getSectionListPageInfo = (state: RootState) => state.sectionManagement.sectionList.pageInfo;
export const getSectionSortColumn = (state: RootState) => state.sectionManagement.sectionList.sortColumn;
export const getSectionSortDirection = (state: RootState) => state.sectionManagement.sectionList.sortDirection;
export const getSectionSubmitting = (state: RootState) => state.sectionManagement.submitting;
export const getSectionDeletingRecords = (state: RootState) => {
  const keys = Object.keys(state.sectionManagement.deletingRecords);
  return keys.length > 0 ? keys.map(Number) : [];
};
//
//   --Subject data selector functions
export const getSubjectListStatus = (state: RootState) => state.subjectManagement.subjectList.status;
export const getSubjectList = (state: RootState) => state.subjectManagement.subjectList.data;
export const getSubjectListData = (state: RootState) => state.subjectManagement.subjectList.data;
export const getSubjectListError = (state: RootState) => state.subjectManagement.subjectList.error;
export const getSubjectListPageInfo = (state: RootState) => state.subjectManagement.subjectList.pageInfo;
export const getSubjectSortColumn = (state: RootState) => state.subjectManagement.subjectList.sortColumn;
export const getSubjectSortDirection = (state: RootState) => state.subjectManagement.subjectList.sortDirection;
export const getSubjectSubmitting = (state: RootState) => state.subjectManagement.submitting;
export const getSubjectDeletingRecords = (state: RootState) => {
  const keys = Object.keys(state.subjectManagement.deletingRecords);
  return keys.length > 0 ? keys.map(Number) : [];
};
// ============= Manage Staff ============= //
//   --Staff data selector functions
export const getStaffDataListStatus = (state: RootState) => state.staffManagement.staffList.status;
export const getStaffList = (state: RootState) => state.staffManagement.staffList.data;
export const getStaffDataList = (state: RootState) => state.staffManagement.staffList.data;
export const getStaffErrorList = (state: RootState) => state.staffManagement.staffList.error;
export const getStaffListPageInfo = (state: RootState) => state.staffManagement.staffList.pageInfo;
export const getStaffSortColumn = (state: RootState) => state.staffManagement.staffList.sortColumn;
export const getStaffSortDirection = (state: RootState) => state.staffManagement.staffList.sortDirection;
export const getStaffSubmitting = (state: RootState) => state.staffManagement.submitting;
export const getStaffDeletingRecords = (state: RootState) => {
  const keys = Object.keys(state.staffManagement.deletingRecords);
  return keys.length > 0 ? keys.map(Number) : [];
};

//   -- CTS Filter List data selector functions
export const getCTSFilterListStatus = (state: RootState) => state.staffManagement.CTSFiltersList.status;
export const getCTSFilterListData = (state: RootState) => state.staffManagement.CTSFiltersList.data;
export const getCTSFilterListError = (state: RootState) => state.staffManagement.CTSFiltersList.error;
export const getCTSYearList = (state: RootState) => state.staffManagement.CTSFiltersList.yearList;
export const getCTSClassList = (state: RootState) => state.staffManagement.CTSFiltersList.classList;
export const getCTSSubjectList = (state: RootState) => state.staffManagement.CTSFiltersList.subjectList;
export const getCTSStaffList = (state: RootState) => state.staffManagement.CTSFiltersList.staffList;

//   -- CTS Details List data selector functions
export const getCTSDetailsListStatus = (state: RootState) => state.staffManagement.CTSDetailsList.status;
export const getCTSDetailsListData = (state: RootState) => state.staffManagement.CTSDetailsList.data;
export const getCTSDetailsListError = (state: RootState) => state.staffManagement.CTSDetailsList.error;
export const getCTSDetailsListPageInfo = (state: RootState) => state.staffManagement.CTSDetailsList.pageInfo;
export const getCTSSortColumn = (state: RootState) => state.staffManagement.CTSDetailsList.sortColumn;
export const getCTSSortDirection = (state: RootState) => state.staffManagement.CTSDetailsList.sortDirection;

//   --  CTS Update Details List data selector functions
export const getCTSUpdateDetailListStatus = (state: RootState) => state.staffManagement.CTSUpdateDetailList.status;
export const getCTSUpdateDetailListData = (state: RootState) => state.staffManagement.CTSUpdateDetailList.data;
export const getCTSUpdateDetailListError = (state: RootState) => state.staffManagement.CTSUpdateDetailList.error;

//   --  CTS Class Wise Mapping List data selector functions
export const getCTSMappingListStatus = (state: RootState) => state.staffManagement.CTSAllocationMapedList.status;
export const getCTSMappingListData = (state: RootState) => state.staffManagement.CTSAllocationMapedList.data;
export const getCTSStaffListData = (state: RootState) => state.staffManagement.CTSAllocationMapedList.staffList;
export const getCTSMappingListError = (state: RootState) => state.staffManagement.CTSAllocationMapedList.error;
export const getCTSDeletingRecords = (state: RootState) => state.staffManagement.deletingRecords;

//   --  CTS Teacher Wise Mapping List data selector functions
export const getCTSTeacherWiseMappingListStatus = (state: RootState) =>
  state.staffManagement.CTSAllocationTeacherWiseMapedList.status;
export const getCTSTeacherWiseMappingListData = (state: RootState) =>
  state.staffManagement.CTSAllocationTeacherWiseMapedList.data;
export const getCTSTeacherWiseSubjectListData = (state: RootState) =>
  state.staffManagement.CTSAllocationTeacherWiseMapedList.subjectList;
export const getCTSTeacherWiseClassListData = (state: RootState) =>
  state.staffManagement.CTSAllocationTeacherWiseMapedList.classList;
export const getCTSTeacherWiseMappingListError = (state: RootState) =>
  state.staffManagement.CTSAllocationTeacherWiseMapedList.error;

//   --Year data selector functions
export const getYearListStatus = (state: RootState) => state.yearManagement.yearList.status;
export const getYearList = (state: RootState) => state.yearManagement.yearList.data;
export const getYearListData = (state: RootState) => state.yearManagement.yearList.data;
export const getYearListError = (state: RootState) => state.yearManagement.yearList.error;
export const getYearListPageInfo = (state: RootState) => state.yearManagement.yearList.pageInfo;
export const getYearSortColumn = (state: RootState) => state.yearManagement.yearList.sortColumn;
export const getYearSortDirection = (state: RootState) => state.yearManagement.yearList.sortDirection;
export const getYearSubmitting = (state: RootState) => state.yearManagement.submitting;
export const getYearDeletingRecords = (state: RootState) => {
  const keys = Object.keys(state.yearManagement.deletingRecords);
  return keys.length > 0 ? keys.map(Number) : [];
};
//
//   --Student data selector functions
export const getStudentListStatus = (state: RootState) => state.studentManagement.studentList.status;
export const getStudentList = (state: RootState) => state.studentManagement.studentList.data;
export const getStudentListData = (state: RootState) => state.studentManagement.studentList.data;
export const getStudentListError = (state: RootState) => state.studentManagement.studentList.error;
export const getStudentListPageInfo = (state: RootState) => state.studentManagement.studentList.pageInfo;
export const getStudentSortColumn = (state: RootState) => state.studentManagement.studentList.sortColumn;
export const getStudentSortDirection = (state: RootState) => state.studentManagement.studentList.sortDirection;
export const getStudentSubmitting = (state: RootState) => state.studentManagement.submitting;
export const getStudentDeletingRecords = (state: RootState) => {
  const keys = Object.keys(state.studentManagement.deletingRecords);
  return keys.length > 0 ? keys.map(Number) : [];
};

//   --QuickUpdate Student data selector functions
export const getQucikUpdateStudentListStatus = (state: RootState) =>
  state.studentManagement.quickUpdateStudentList.status;
export const getQucikUpdateStudentListData = (state: RootState) => state.studentManagement.quickUpdateStudentList.data;
export const getQucikUpdateStudentListError = (state: RootState) =>
  state.studentManagement.quickUpdateStudentList.error;

// Dashboard data selector functions
//
export const getdashboardStatsListStatus = (state: RootState) => state.dashboard.dashboardStats.status;
export const getdashboardStatsListData = (state: RootState) => state.dashboard.dashboardStats.data;
export const getdashboardStatsListError = (state: RootState) => state.dashboard.dashboardStats.error;
//
export const getdashboardEventsListStatus = (state: RootState) => state.dashboard.dashboardEvents.status;
export const getdashboardEventsListData = (state: RootState) => state.dashboard.dashboardEvents.data;
export const getdashboardEventsListError = (state: RootState) => state.dashboard.dashboardEvents.error;
//
export const getdashboardBdayListStatus = (state: RootState) => state.dashboard.dashboardBday.status;
export const getdashboardBdayListData = (state: RootState) => state.dashboard.dashboardBday.data;
export const getdashboardBdayListError = (state: RootState) => state.dashboard.dashboardBday.error;
//
export const getdashboardFeeChartStatus = (state: RootState) => state.dashboard.dashboardFeeChart.status;
export const getdashboardFeeChartData = (state: RootState) => state.dashboard.dashboardFeeChart.data;
export const getdashboardFeeChartError = (state: RootState) => state.dashboard.dashboardFeeChart.error;
//
export const getTimeTableData = (state: RootState) => state.dashboard.dashboardTimetable.data;
export const getTimeTableStatus = (state: RootState) => state.dashboard.dashboardTimetable.status;
export const getTimeTableError = (state: RootState) => state.dashboard.dashboardTimetable.error;
//
export const getdashboardAttendanceData = (state: RootState) => state.dashboard.dashboardAttendance.data;
export const getdashboardAttendanceStatus = (state: RootState) => state.dashboard.dashboardAttendance.status;
export const getdashboardAttendanceError = (state: RootState) => state.dashboard.dashboardAttendance.error;
//
export const getdashboardVideosData = (state: RootState) => state.dashboard.dashboardVideos.data;
export const getdashboardVideosStatus = (state: RootState) => state.dashboard.dashboardVideos.status;
export const getdashboardVideosError = (state: RootState) => state.dashboard.dashboardVideos.error;

// AttendanceMarking data selector functions
export const getAttendanceListSubmitting = (state: RootState) => state.attendanceMarking.submitting;
export const getAttendanceListStatus = (state: RootState) => state.attendanceMarking.attendanceList.status;
export const getAttendanceListData = (state: RootState) => state.attendanceMarking.attendanceList.data;
export const getAttendanceListError = (state: RootState) => state.attendanceMarking.attendanceList.error;
//
// Leave Note List data selector functions
export const getLeaveNoteListStatus = (state: RootState) => state.attendanceMarking.leaveNoteList.status;
export const getLeaveNoteListData = (state: RootState) => state.attendanceMarking.leaveNoteList.data;
export const getLeaveNoteListError = (state: RootState) => state.attendanceMarking.leaveNoteList.error;
export const getLeaveNoteListPageInfo = (state: RootState) => state.attendanceMarking.leaveNoteList.pageInfo;
//
// Student Enquiry List data selector functions
export const getStudentEnquiryListStatus = (state: RootState) => state.attendanceMarking.studentEnquiryList.status;
export const getStudentEnquiryListData = (state: RootState) => state.attendanceMarking.studentEnquiryList.data;
export const getStudentEnquiryListError = (state: RootState) => state.attendanceMarking.studentEnquiryList.error;
export const getStudentEnquiryListSortColumn = (state: RootState) =>
  state.attendanceMarking.studentEnquiryList.sortColumn;
export const getStudentEnquiryListSortDirection = (state: RootState) =>
  state.attendanceMarking.studentEnquiryList.sortDirection;
export const getStudentEnquiryListPageInfo = (state: RootState) => state.attendanceMarking.studentEnquiryList.pageInfo;
//
// Attendance Calendar data selector functions
export const getAttendanceCalendarListStatus = (state: RootState) => state.attendanceMarking.attendanceCalendar.status;
export const getAttendanceCalendarListData = (state: RootState) => state.attendanceMarking.attendanceCalendar.data;
export const getAttendanceCalendarListError = (state: RootState) => state.attendanceMarking.attendanceCalendar.error;

// Attendance Summary Report data selector functions
export const getAttendanceSummaryReportStatus = (state: RootState) =>
  state.attendanceMarking.attendanceSummaryReport.status;
export const getAttendanceSummaryReportData = (state: RootState) =>
  state.attendanceMarking.attendanceSummaryReport.data;
export const getAttendanceSummaryReportError = (state: RootState) =>
  state.attendanceMarking.attendanceSummaryReport.error;

// MessageBox data selector functions
//
export const getMessageTempSubmitting = (state: RootState) => state.messageBox.submitting;
export const getMessadeTempListStatus = (state: RootState) => state.messageBox.messageTempList.status;
export const getMessadeTempListData = (state: RootState) => state.messageBox.messageTempList.data;
export const getMessadeTempListError = (state: RootState) => state.messageBox.messageTempList.error;
//
export const getMessadeTemplateStatus = (state: RootState) => state.messageBox.messageTemplate.status;
export const getMessadeTemplateData = (state: RootState) => state.messageBox.messageTemplate.data;
export const getMessadeTemplateError = (state: RootState) => state.messageBox.messageTemplate.error;
//
export const getParentsListStatus = (state: RootState) => state.messageBox.parentsList.status;
export const getParentsListData = (state: RootState) => state.messageBox.parentsList.data;
export const getParentsListError = (state: RootState) => state.messageBox.parentsList.error;
//
export const getStaffListStatus = (state: RootState) => state.messageBox.staffList.status;
export const getStaffListData = (state: RootState) => state.messageBox.staffList.data;
export const getStaffListError = (state: RootState) => state.messageBox.staffList.error;
//
export const getPtaListStatus = (state: RootState) => state.messageBox.ptaList.status;
export const getPtaListData = (state: RootState) => state.messageBox.ptaList.data;
export const getPtaListError = (state: RootState) => state.messageBox.ptaList.error;
//
export const getConveyorListStatus = (state: RootState) => state.messageBox.conveyorList.status;
export const getConveyorListData = (state: RootState) => state.messageBox.conveyorList.data;
export const getConveyorListError = (state: RootState) => state.messageBox.conveyorList.error;
//
export const getGroupListStatus = (state: RootState) => state.messageBox.groupList.status;
export const getGroupListData = (state: RootState) => state.messageBox.groupList.data;
export const getGroupListError = (state: RootState) => state.messageBox.groupList.error;
//
export const getGroupMembersListStatus = (state: RootState) => state.messageBox.groupMembersList.status;
export const getGroupMembersListData = (state: RootState) => state.messageBox.groupMembersList.data;
export const getGroupMembersListError = (state: RootState) => state.messageBox.groupMembersList.error;
//
export const getClassDivisionListStatus = (state: RootState) => state.messageBox.classDivisionList.status;
export const getClassDivisionListData = (state: RootState) => state.messageBox.classDivisionList.data;
export const getClassDivisionListError = (state: RootState) => state.messageBox.classDivisionList.error;
//
export const getClassSectionListStatus = (state: RootState) => state.messageBox.classSectionList.status;
export const getClassSectionListData = (state: RootState) => state.messageBox.classSectionList.data;
export const getClassSectionListError = (state: RootState) => state.messageBox.classSectionList.error;
//
export const getGroupWiseListStatus = (state: RootState) => state.messageBox.groupWiseList.status;
export const getGroupWiseListData = (state: RootState) => state.messageBox.groupWiseList.data;
export const getGroupWiseListError = (state: RootState) => state.messageBox.groupWiseList.error;
//
export const getPublicGroupListStatus = (state: RootState) => state.messageBox.publicGroupList.status;
export const getPublicGroupListData = (state: RootState) => state.messageBox.publicGroupList.data;
export const getPublicGroupListError = (state: RootState) => state.messageBox.publicGroupList.error;
//
export const getPublicGroupMembersListStatus = (state: RootState) => state.messageBox.publicGroupMembersList.status;
export const getPublicGroupMembersListData = (state: RootState) => state.messageBox.publicGroupMembersList.data;
export const getPublicGroupMembersListError = (state: RootState) => state.messageBox.publicGroupMembersList.error;
//
export const getPublicGroupWiseListStatus = (state: RootState) => state.messageBox.publicGroupWiseList.status;
export const getPublicGroupWiseListData = (state: RootState) => state.messageBox.publicGroupWiseList.data;
export const getPublicGroupWiseListError = (state: RootState) => state.messageBox.publicGroupWiseList.error;
//

// export const getProgressUploading = (state: RootState) => state.notification.filesUploadList.progress;

// Notification List data selector functions
export const getNotificationSubmitting = (state: RootState) => state.notification.submitting;
export const getNotificationStatus = (state: RootState) => state.notification.notificationList.status;
export const getNotificationData = (state: RootState) => state.notification.notificationList.data;
export const getNotificationError = (state: RootState) => state.notification.notificationList.error;
//
// Voice List data selector functions
export const getVoiceSubmitting = (state: RootState) => state.voiceMessage.submitting;
export const getVoiceStatus = (state: RootState) => state.voiceMessage.voiceList.status;
export const getVoiceData = (state: RootState) => state.voiceMessage.voiceList.data;
export const getVoiceError = (state: RootState) => state.voiceMessage.voiceList.error;

// Manage Fee
//
// Manage Fee Fee settings data selector functions
export const getManageFeeSubmitting = (state: RootState) => state.manageFee.submitting;
export const getTermFeeStatus = (state: RootState) => state.manageFee.feeSettingsList.status;
export const getTermFeeData = (state: RootState) => state.manageFee.feeSettingsList.data;
export const getTermFeeError = (state: RootState) => state.manageFee.feeSettingsList.error;
//
// Manage Fee Class Sections selector functions
export const getClassSectionsStatus = (state: RootState) => state.manageFee.classSectionsList.status;
export const getClassSectionsData = (state: RootState) => state.manageFee.classSectionsList.data;
export const getClassSectionsError = (state: RootState) => state.manageFee.classSectionsList.error;
//
// Manage Fee Fee Date Settings selector functions
export const getfeeDateSettingsStatus = (state: RootState) => state.manageFee.feeDateSettingsList.status;
export const getfeeDateSettingsData = (state: RootState) => state.manageFee.feeDateSettingsList.data;
export const getfeeDateSettingsError = (state: RootState) => state.manageFee.feeDateSettingsList.error;
//
// Manage Fee Term Fee Settings selector functions
export const getTermFeeSettingsListStatus = (state: RootState) => state.manageFee.termFeeSettingsList.status;
export const getTermFeeSettingsListData = (state: RootState) => state.manageFee.termFeeSettingsList.data;
export const getTermFeeSettingsListError = (state: RootState) => state.manageFee.termFeeSettingsList.error;
//
// Manage Fee Class List selector functions
export const getclassListStatus = (state: RootState) => state.manageFee.classList.status;
export const getManageFeeclassListData = (state: RootState) => state.manageFee.classList.data;
export const getclassListError = (state: RootState) => state.manageFee.classList.error;
//
// Manage Fee Class List selector functions
export const getstudentsFeeStatusListStatus = (state: RootState) => state.manageFee.studentsFeeStatusList.status;
export const getstudentsFeeStatusListData = (state: RootState) => state.manageFee.studentsFeeStatusList.data;
export const getstudentsFeeStatusListError = (state: RootState) => state.manageFee.studentsFeeStatusList.error;
//
// Manage Fee Student Term Fee Status selector functions
export const getstudentTermFeeStatusListStatus = (state: RootState) => state.manageFee.studentTermFeeStatusList.status;
export const getstudentTermFeeStatusListData = (state: RootState) => state.manageFee.studentTermFeeStatusList.data;
export const getstudentTermFeeStatusListError = (state: RootState) => state.manageFee.studentTermFeeStatusList.error;
//
// Manage Fee Student Term Fee New Status selector functions
export const getstudentTermFeeStatusNewListStatus = (state: RootState) =>
  state.manageFee.studentTermFeeStatusNewList.status;
export const getstudentTermFeeStatusNewListData = (state: RootState) =>
  state.manageFee.studentTermFeeStatusNewList.data;
export const getstudentTermFeeStatusNewListError = (state: RootState) =>
  state.manageFee.studentTermFeeStatusNewList.error;
//
// Manage Fee Create Term Fee Setting selector functions
export const getcreateTermFeeSettingListStatus = (state: RootState) => state.manageFee.createTermFeeSettingList.status;
export const getcreateTermFeeSettingListData = (state: RootState) => state.manageFee.createTermFeeSettingList.data;
export const getcreateTermFeeSettingListError = (state: RootState) => state.manageFee.createTermFeeSettingList.error;
//
// Manage Fee Scholarship Fee List selector functions
export const getScholarshipFeeListStatus = (state: RootState) => state.manageFee.ScholarshipFeeList.status;
export const getScholarshipFeeListData = (state: RootState) => state.manageFee.ScholarshipFeeList.data;
export const getScholarshipFeeListError = (state: RootState) => state.manageFee.ScholarshipFeeList.error;

// Manage Fee Optional Fee Setting selector functions
export const getOptionalFeeSettingListStatus = (state: RootState) => state.manageFee.optionalFeeSettingsList.status;
export const getOptionalFeeSettingListData = (state: RootState) => state.manageFee.optionalFeeSettingsList.data;
export const getOptionalFeeSettingListError = (state: RootState) => state.manageFee.optionalFeeSettingsList.error;

// Manage Fee Scholarship Setting selector functions
export const getScholarshipSettingsListStatus = (state: RootState) => state.manageFee.scholarshipSettingsList.status;
export const getScholarshipSettingsListData = (state: RootState) => state.manageFee.scholarshipSettingsList.data;
export const getScholarshipSettingsListError = (state: RootState) => state.manageFee.scholarshipSettingsList.error;

// Manage Fee Term Fee List selector functions
export const getTermFeeListStatus = (state: RootState) => state.manageFee.termFeeList.status;
export const getTermFeeListData = (state: RootState) => state.manageFee.termFeeList.data;
export const getTermFeeListError = (state: RootState) => state.manageFee.termFeeList.error;

// Manage Fee Basic Fee List selector functions
export const getBasicFeeListStatus = (state: RootState) => state.manageFee.basicFeeList.status;
export const getBasicFeeListData = (state: RootState) => state.manageFee.basicFeeList.data;
export const getBasicFeeListError = (state: RootState) => state.manageFee.basicFeeList.error;

// ============ Mange Fee Overview ============
// Manage Fee Overview Status List selector functions
export const getFeeOverviewStatusListStatus = (state: RootState) => state.manageFee.feeOverviewStatusList.status;
export const getFeeOverviewStatusListData = (state: RootState) => state.manageFee.feeOverviewStatusList.data;
export const getFeeOverviewStatusListError = (state: RootState) => state.manageFee.feeOverviewStatusList.error;

// Manage Fee Overview Chart List selector functions
export const getFeeOverviewChartListStatus = (state: RootState) => state.manageFee.feeOverviewChartList.status;
export const getFeeOverviewChartListData = (state: RootState) => state.manageFee.feeOverviewChartList.data;
export const getFeeOverviewChartListError = (state: RootState) => state.manageFee.feeOverviewChartList.error;

// Manage Fee Overview Paid List selector functions
export const getFeeOverviewPaidListStatus = (state: RootState) => state.manageFee.feeOverviewPaidList.status;
export const getFeeOverviewPaidListData = (state: RootState) => state.manageFee.feeOverviewPaidList.data;
export const getFeeOverviewPaidListError = (state: RootState) => state.manageFee.feeOverviewPaidList.error;

// Manage Fee Overview Mode List selector functions
export const getFeeOverviewModeListStatus = (state: RootState) => state.manageFee.feeOverviewModeList.status;
export const getFeeOverviewModeListData = (state: RootState) => state.manageFee.feeOverviewModeList.data;
export const getFeeOverviewModeListError = (state: RootState) => state.manageFee.feeOverviewModeList.error;

// Manage Fee  Receipt For Print List selector functions
export const getReceiptForPrintListStatus = (state: RootState) => state.manageFee.receiptForPrintList.status;
export const getReceiptForPrintListData = (state: RootState) => state.manageFee.receiptForPrintList.data;
export const getReceiptForPrintListError = (state: RootState) => state.manageFee.receiptForPrintList.error;

// Manage Fee  Get Fee Paid List selector functions
export const getFeePaidListStatus = (state: RootState) => state.manageFee.feePaidList.status;
export const getFeePaidListData = (state: RootState) => state.manageFee.feePaidList.data;
export const getFeePaidListError = (state: RootState) => state.manageFee.feePaidList.error;

// Manage Fee  Get Student Filter selector functions
export const getStudentFilterStatus = (state: RootState) => state.manageFee.studentsFilter.status;
export const getStudentFilterData = (state: RootState) => state.manageFee.studentsFilter.data;
export const getStudentFilterError = (state: RootState) => state.manageFee.studentsFilter.error;

// Manage Fee  Get Fee Paid Basic List selector functions
export const getFeePaidBasicListStatus = (state: RootState) => state.manageFee.feePaidBasicList.status;
export const getFeePaidBasicListData = (state: RootState) => state.manageFee.feePaidBasicList.data;
export const getFeePaidBasicListError = (state: RootState) => state.manageFee.feePaidBasicList.error;

// Manage Fee Get Basic Fee Filter selector functions
export const getBasicFeeFilterStatus = (state: RootState) => state.manageFee.basicFeeFilter.status;
export const getBasicFeeFilterData = (state: RootState) => state.manageFee.basicFeeFilter.data;
export const getBasicFeeFilterError = (state: RootState) => state.manageFee.basicFeeFilter.error;

// Manage Fee Get Fee Paid Term List selector functions
export const getFeePaidTermListStatus = (state: RootState) => state.manageFee.feePaidTermList.status;
export const getFeePaidTermListData = (state: RootState) => state.manageFee.feePaidTermList.data;
export const getFeePaidTermListError = (state: RootState) => state.manageFee.feePaidTermList.error;

// Manage Fee Get Term Fee Filter selector functions
export const getTermFeeFilterStatus = (state: RootState) => state.manageFee.termFeeFilter.status;
export const getTermFeeFilterData = (state: RootState) => state.manageFee.termFeeFilter.data;
export const getTermFeeFilterError = (state: RootState) => state.manageFee.termFeeFilter.error;

// Manage Fee Get Fee Pending List selector functions
export const getFeePendingListStatus = (state: RootState) => state.manageFee.feePendingList.status;
export const getFeePendingListData = (state: RootState) => state.manageFee.feePendingList.data;
export const getFeePendingListError = (state: RootState) => state.manageFee.feePendingList.error;

// Manage Fee Get Fee Pending Basic List selector functions
export const getFeePendingBasicListStatus = (state: RootState) => state.manageFee.feePendingBasicList.status;
export const getFeePendingBasicListData = (state: RootState) => state.manageFee.feePendingBasicList.data;
export const getFeePendingBasicListError = (state: RootState) => state.manageFee.feePendingBasicList.error;

// Manage Fee Get Fee Pending Term List selector functions
export const getFeePendingTermListStatus = (state: RootState) => state.manageFee.feePendingTermList.status;
export const getFeePendingTermListData = (state: RootState) => state.manageFee.feePendingTermList.data;
export const getFeePendingTermListError = (state: RootState) => state.manageFee.feePendingTermList.error;

// Manage Fee Get Fine List selector functions
export const getFineListStatus = (state: RootState) => state.manageFee.fineList.status;
export const getFineListData = (state: RootState) => state.manageFee.fineList.data;
export const getFineListError = (state: RootState) => state.manageFee.fineList.error;

// Manage Fee Get Fine Mapping List selector functions
export const getFineMappingListStatus = (state: RootState) => state.manageFee.fineMappingList.status;
export const getFineMappingListData = (state: RootState) => state.manageFee.fineMappingList.data;
export const getFineMappingListError = (state: RootState) => state.manageFee.fineMappingList.error;

// Manage Fee Check Receipt No selector functions
export const getCheckReceiptNoStatus = (state: RootState) => state.manageFee.checkReceiptNo.status;
export const getCheckReceiptNoData = (state: RootState) => state.manageFee.checkReceiptNo.data;
export const getCheckReceiptNoError = (state: RootState) => state.manageFee.checkReceiptNo.error;

// Manage Fee Get Stop Mapping Settings selector functions
export const getStopMappingSettingsStatus = (state: RootState) => state.manageFee.stopMappingSettingsList.status;
export const getStopMappingSettingsData = (state: RootState) => state.manageFee.stopMappingSettingsList.data;
export const getStopMappingSettingsError = (state: RootState) => state.manageFee.stopMappingSettingsList.error;

// Parent side Payment Get Parent Pay Fee
export const getParentPayFeeStatus = (state: RootState) => state.payment.parentPayFee.status;
export const getParentPayFeeData = (state: RootState) => state.payment.parentPayFee.data;
export const getParentPayFeeError = (state: RootState) => state.payment.parentPayFee.error;

// Parent side Payment Get Reciept Online
export const getRecieptOnlineStatus = (state: RootState) => state.payment.receiptOnlineList.status;
export const getRecieptOnlineData = (state: RootState) => state.payment.receiptOnlineList.data;
export const getRecieptOnlineError = (state: RootState) => state.payment.receiptOnlineList.error;

// Manage Fee Student Picker
export const getStudentPickerStatus = (state: RootState) => state.manageFee.studentPickerList.status;
export const getStudentPickerData = (state: RootState) => state.manageFee.studentPickerList.data;
export const getStudentPickerError = (state: RootState) => state.manageFee.studentPickerList.error;

// ======================================= Exam Center ===========================================
//
export const getExamCenterSubmitting = (state: RootState) => state.examCenter.submitting;
// Mark Register Filter data selector functions
export const getMarkRegisterFiltersStatus = (state: RootState) => state.examCenter.markRegisterFilters.status;
export const getMarkRegisterFiltersData = (state: RootState) => state.examCenter.markRegisterFilters.data;
export const getMarkRegisterFiltersError = (state: RootState) => state.examCenter.markRegisterFilters.error;
//
// Mark Register Subject Filter data selector functions
export const getMarkRegisterSubjectFiltersStatus = (state: RootState) =>
  state.examCenter.markRegisterSubjectFilters.status;
export const getMarkRegisterSubjectFiltersData = (state: RootState) => state.examCenter.markRegisterSubjectFilters.data;
export const getMarkRegisterSubjectFiltersError = (state: RootState) =>
  state.examCenter.markRegisterSubjectFilters.error;
//
// Mark Register Data data selector functions
export const getMarkRegisterDataStatus = (state: RootState) => state.examCenter.markRegisterData.status;
export const getMarkRegisterData = (state: RootState) => state.examCenter.markRegisterData.data;
export const getMarkRegisterDataError = (state: RootState) => state.examCenter.markRegisterData.error;
//
// Mark Register Data with CE selector functions
export const getMarkRegisterDataWithCEStatus = (state: RootState) => state.examCenter.markRegisterDataWithCE.status;
export const getMarkRegisterDataWithCE = (state: RootState) => state.examCenter.markRegisterDataWithCE.data;
export const getMarkRegisterDataWithCEerror = (state: RootState) => state.examCenter.markRegisterDataWithCE.error;
