/* eslint-disable jsx-a11y/alt-text */
import React, { useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  useTheme,
  IconButton,
  Collapse,
  Tooltip,
  FormControl,
  Checkbox,
  FormControlLabel,
  Radio,
  RadioGroup,
  Avatar,
} from '@mui/material';
import styled from 'styled-components';
import { CLASS_SELECT, STATUS_SELECT, YEAR_SELECT } from '@/config/Selection';
import SearchIcon from '@mui/icons-material/Search';

import useSettings from '@/hooks/useSettings';
import { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { BiLogoAndroid, BiLogoApple } from 'react-icons/bi';
import { RiComputerFill } from 'react-icons/ri';

export const data = [
  {
    studentName: '<PERSON>',
    class: 'VII-B',
    admission: '34765',
    academicYear: '2023-2024',
    status: 'Block',
    image:
      'https://images.unsplash.com/photo-1633332755192-727a05c4013d?q=80&w=1780&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    studentName: 'Peter Jack',
    class: 'VII-B',
    admission: '34765',
    academicYear: '2023-2024',
    status: 'Block',
  },
  {
    studentName: 'Peter Jack',
    class: 'VII-B',
    admission: '34765',
    academicYear: '2023-2024',
    status: 'Block',
  },
  {
    studentName: 'Peter Jack',
    class: 'VII-B',
    admission: '34765',
    academicYear: '2023-2024',
    status: 'Block',
  },
  {
    studentName: 'Peter Jack',
    class: 'VII-B',
    admission: '34765',
    academicYear: '2023-2024',
    status: 'Block',
  },
  {
    studentName: 'Peter Jack',
    class: 'VII-B',
    admission: '34765',
    academicYear: '2023-2024',
    status: 'Block',
  },
  {
    studentName: 'Peter Jack',
    class: 'VII-B',
    admission: '34765',
    academicYear: '2023-2024',
    status: 'Block',
  },
  {
    studentName: 'Peter Jack',
    class: 'VII-B',
    admission: '34765',
    academicYear: '2023-2024',
    status: 'Block',
  },
  {
    studentName: 'Peter Jack',
    class: 'VII-B',
    admission: '34765',
    academicYear: '2023-2024',
    status: 'Block',
  },
  {
    studentName: 'Peter Jack',
    class: 'VII-B',
    admission: '34765',
    academicYear: '2023-2024',
    status: 'Block',
  },
  {
    studentName: 'Peter Jack',
    class: 'VII-B',
    admission: '34765',
    academicYear: '2023-2024',
    status: 'Block',
  },
  {
    studentName: 'Peter Jack',
    class: 'VII-B',
    admission: '34765',
    academicYear: '2023-2024',
    status: 'Block',
  },
];

const StudentBlockUnblockRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;
  .Card {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    height: calc(100vh - 100px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 40px);
      flex-grow: 1;

      .card-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: auto;
        ::-webkit-scrollbar {
          width: 0px;
        }
      }
    }
  }
`;

function StudentBlockUnblock() {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const [showFilter, setShowFilter] = useState(false);

  const [status, setStatus] = React.useState('Unblock');

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setStatus((event.target as HTMLInputElement).value);
  };

  const StudentBlockUnblockColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'studentName',
        headerLabel: 'Student Name',
        dataKey: 'studentName',
        // renderCell: (row, itemIndex) => {
        //   return (
        //     <>
        //       <Avatar src={row.image} />
        //       {row.studentName}
        //     </>
        //   );
        // },
      },
      {
        name: 'admission',
        dataKey: 'admission',
        headerLabel: 'Admission',
      },
      {
        name: 'status',
        headerLabel: 'Status',
        // renderCell: (row, itemIndex) => {
        //   return (
        //     <FormControl>
        //       <RadioGroup
        //         row
        //         aria-labelledby="demo-row-radio-buttons-group-label"
        //         name="row-radio-buttons-group"
        //         value={value}
        //         onChange={handleChange}
        //       >
        //         <FormControlLabel value="Block" control={<Radio />} label="Block" />
        //         <FormControlLabel value="Unblock" control={<Radio />} label="Unblock" />
        //       </RadioGroup>
        //     </FormControl>
        //   );
        // },
      },
    ],
    []
  );

  return (
    <Page title="Student Block Unblock">
      <StudentBlockUnblockRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17} width="100%">
              Student Block Unblock
            </Typography>
            <Box pb={1} sx={{ flexShrink: 0 }}>
              <Tooltip title="Search">
                <IconButton aria-label="" color="primary" sx={{ mr: 1 }} onClick={() => setShowFilter((x) => !x)}>
                  <SearchIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Stack>
          <Stack>
            <Divider />
          </Stack>
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={5} pt={1} container spacing={2}>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 190 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Autocomplete
                        options={YEAR_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select year" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 190 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Autocomplete
                        options={CLASS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select class" />}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item lg={1} xs={12}>
                    <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                      <Button variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                      {/* <Button variant="contained" color="primary" fullWidth>
                          Search
                        </Button> */}
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Box className="card-container" mt={2}>
              <Grid container spacing={2}>
                {data.map((student, cardIndex) => (
                  <Grid item xl={4} lg={6} md={12} sm={6} xs={12}>
                    <Card
                      className="student_card"
                      sx={{ border: `1px solid ${theme.palette.grey[300]}`, p: 2, boxShadow: 0 }}
                    >
                      <Box display="flex" justifyContent="space-between" alignItems="center">
                        <Avatar src={student.image} />
                        <Checkbox value="" color="primary" />
                      </Box>
                      {StudentBlockUnblockColumns.map((item, itemIndex) => (
                        <Grid container>
                          <Grid item lg={5} xs={6} mb={1} color="GrayText">
                            <Typography key={item.name} variant="subtitle1" fontSize={13}>
                              {item.headerLabel}
                            </Typography>
                          </Grid>
                          <Grid item lg={6} xs={6}>
                            {item.dataKey ? (
                              <Typography
                                variant="h6"
                                fontSize={13}
                                color={item.dataKey === 'meetingLink' ? theme.palette.info.main : ''}
                              >
                                : {(student as { [key: string]: string })[item.dataKey ?? '']}
                              </Typography>
                            ) : (
                              // <Stack>{item.renderCell(student, itemIndex)}</Stack>
                              <FormControl>
                                <RadioGroup
                                  row
                                  aria-labelledby="demo-row-radio-buttons-group-label"
                                  name="row-radio-buttons-group"
                                  defaultValue={student.status}
                                  onChange={handleChange}
                                >
                                  <FormControlLabel value="Block" control={<Radio />} label="Block" />
                                  <FormControlLabel value="Unblock" control={<Radio />} label="Unblock" />
                                </RadioGroup>
                              </FormControl>
                            )}
                          </Grid>
                        </Grid>
                      ))}
                      <Typography my={2} variant="subtitle2">
                        Block Message
                      </Typography>
                      <TextField fullWidth placeholder="Enter Message" multiline maxRows={2} minRows={2} />
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Box>
            <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 3 }}>
              <Stack spacing={2} direction="row">
                <Button variant="contained" color="primary">
                  Update
                </Button>
              </Stack>
            </Box>
          </div>
        </Card>
      </StudentBlockUnblockRoot>
    </Page>
  );
}

export default StudentBlockUnblock;
