/* eslint-disable react/no-array-index-key */
/* eslint-disable jsx-a11y/alt-text */
import React, { FormEvent, useCallback, useState } from 'react';
import {
  Divider,
  Grid,
  Paper,
  Stack,
  Button,
  Typography,
  Box,
  FormControl,
  Select,
  MenuItem,
  SelectChangeEvent,
  useTheme,
  Tooltip,
  IconButton,
} from '@mui/material';
import styled from 'styled-components';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useAppSelector } from '@/hooks/useAppSelector';
import {
  getYearData,
  getMessageTempSubmitting,
  getPublicGroupListData,
  getPublicGroupMembersListData,
  getPublicGroupMembersListStatus,
} from '@/config/storeSelectors';
import useAuth from '@/hooks/useAuth';
import { fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import {
  fetchPublicGroupMembersList,
  fetchPublicGroupsList,
  messageSendToPublicGroupMembersList,
} from '@/store/MessageBox/messageBox.thunks';
import LoadingButton from '@mui/lab/LoadingButton';
import SendIcon from '@mui/icons-material/Send';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import LoadingMsg from '@/assets/MessageIcons/loading-message.gif';
import { SendToPublicGroupMembersType, PublicGroupMembersDataType } from '@/types/MessageBox';
import ErrorMsg from '@/assets/MessageIcons/error-message.gif';
import ErrorMsg1 from '@/assets/MessageIcons/error-message1.gif';
import ErrorMsg2 from '@/assets/MessageIcons/error-message2.gif';
import SuccessMsg from '@/assets/MessageIcons/message-success.gif';
import successIcon from '@/assets/MessageIcons/success.json';
import errorIcon from '@/assets/MessageIcons/error-message.json';
import Lottie from 'lottie-react';
import { breakPointsMaxwidth } from '@/config/breakpoints';
import { SuccessMessage } from '../Popup/SuccessMessage';
import { ErrorMessage } from '../Popup/ErrorMessage';
import { useConfirm } from '../Popup/Confirmation';
import LoadingPopup from '../Popup/LoadingPopup';
import { LoadingMessage } from '../Popup/LoadingMessage';
import { SendPopupProps } from '@/types/Common';
import { SendVoiceToPublicGroupRequest } from '@/types/VoiceMessage';
import { voiceMessageSendToPublicGroupMembersList } from '@/store/VoiceMessage/voiceMessage.thunk';

const GroupsRoot = styled.div`
  width: 100%;
  padding: 8px 24px;
  .delivery-report-btn {
    @media ${breakPointsMaxwidth.xs} {
      width: 100%;
    }
  }
  .send-btn {
    @media ${breakPointsMaxwidth.sm} {
      width: 50%;
    }
    @media ${breakPointsMaxwidth.xs} {
      width: 100%;
    }
  }
  .cancel-btn {
    @media ${breakPointsMaxwidth.xs} {
      width: 25%;
    }
    @media ${breakPointsMaxwidth.sm} {
      width: 50%;
    }
  }
`;

export type PublicGroupMembersListRequest = {
  adminId: number;
  academicId: number;
  gmemberId: number;
};
interface PublicGroupDataWithStatus extends PublicGroupMembersDataType {
  sendStatus: 'Success' | 'Failed' | '' | undefined;
}

function PublicGroups({ messageId, voiceId, isSubmitting, templateId }: SendPopupProps) {
  const { user } = useAuth();
  const theme = useTheme();
  const dispatch = useAppDispatch();
  const { confirm } = useConfirm();
  const defaultYearId: number | undefined = 10;
  const adminId: number = user ? user.accountId : 0;
  const [academicYearFilter, setAcademicYearFilter] = useState(defaultYearId);
  const [publicGroupFilter, setPublicGroupFilter] = useState(-1);
  const [view, setView] = useState<'main' | 'report'>('main');
  const [sendResults, setSendResults] = useState<PublicGroupDataWithStatus[]>([]);
  const [selectedRows, setSelectedRows] = useState<PublicGroupDataWithStatus[]>([]);
  const YearData = useAppSelector(getYearData);
  const PublicGroupsData = useAppSelector(getPublicGroupListData);
  const PublicGroupMembersData = useAppSelector(getPublicGroupMembersListData);
  const PublicGroupMembersStatus = useAppSelector(getPublicGroupMembersListStatus);
  const [selectedData, setSelectedData] = useState<PublicGroupDataWithStatus[]>([]);
  const [disabledCheckBoxes, setDisabledCheckBoxes] = useState<boolean[]>([]);
  const [errMsgTooltip, setErrMsgTooltip] = useState<React.ReactNode>(undefined);

  const initialPublicGroupListRequest: PublicGroupMembersListRequest = React.useMemo(
    () => ({
      adminId,
      academicId: defaultYearId,
      gmemberId: -1,
    }),
    [adminId, defaultYearId]
  );
  const currentPublicGroupListRequest: PublicGroupMembersListRequest = React.useMemo(
    () => ({
      adminId,
      academicId: academicYearFilter,
      gmemberId: publicGroupFilter,
    }),
    [adminId, academicYearFilter, publicGroupFilter]
  );

  const loadGroupMembersList = useCallback(
    async (request: PublicGroupMembersListRequest) => {
      try {
        const data = await dispatch(fetchPublicGroupMembersList(request)).unwrap();
        const dataWithStatus = data.map((item) => ({
          ...item,
          sendStatus: sendResults.find((i) => i.gmemberNumber === item.gmemberNumber)?.sendStatus,
        }));
        setSelectedData(dataWithStatus);
        setSelectedRows([]);
      } catch (error) {
        console.error('Error loading Group Members list:', error);
      }
    },
    [dispatch, setSelectedData, sendResults]
  );

  React.useEffect(() => {
    dispatch(fetchYearList(adminId));
    dispatch(fetchPublicGroupsList({ adminId, academicId: academicYearFilter }));
    if (PublicGroupMembersStatus === 'idle') {
      loadGroupMembersList(currentPublicGroupListRequest);
    }
  }, [
    dispatch,
    adminId,
    currentPublicGroupListRequest,
    PublicGroupMembersStatus,
    loadGroupMembersList,
    academicYearFilter,
  ]);

  const handleYearChange = (e: SelectChangeEvent) => {
    console.log('handleYearChange', e.target.value);
    setAcademicYearFilter(parseInt(YearData.filter((item) => item.accademicId === e.target.value)[0].accademicId, 10));
    loadGroupMembersList({ ...currentPublicGroupListRequest, academicId: parseInt(e.target.value, 10) });
    setSelectedRows([]);
  };
  const handleGroupChange = (e: SelectChangeEvent) => {
    setPublicGroupFilter(PublicGroupsData.filter((item) => item.groupId === parseInt(e.target.value, 10))[0].groupId);
    loadGroupMembersList({ ...currentPublicGroupListRequest, gmemberId: parseInt(e.target.value, 10) });
    setSelectedRows([]);
  };

  const handleReset = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      setAcademicYearFilter(parseInt(YearData[0].accademicId, 10));
      setPublicGroupFilter(-1);
      loadGroupMembersList(initialPublicGroupListRequest);
    },
    [YearData, loadGroupMembersList, initialPublicGroupListRequest]
  );
  const handleSubmit = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      loadGroupMembersList(currentPublicGroupListRequest);
      setSelectedRows([]);
    },
    [loadGroupMembersList, currentPublicGroupListRequest]
  );

  const handleCancel = useCallback(() => {
    setSelectedRows([]);
    loadGroupMembersList(currentPublicGroupListRequest);
    setDisabledCheckBoxes([]);
  }, [loadGroupMembersList, currentPublicGroupListRequest]);

  const handleBack = useCallback(() => {
    setView('main');
    // setSendResults(null);
    setSelectedRows([]);
  }, []);

  const handleSendMessage = useCallback(async () => {
    try {
      console.log(selectedRows);
      if (!selectedRows || selectedRows.length === 0) {
        const errorMessage = <ErrorMessage icon={ErrorMsg1} message="Please Select Atleast one Group Member to sent" />;
        await confirm(errorMessage, 'Staff not selected', { okLabel: 'Ok', showOnlyOk: true });
        return;
      }
      const sendRequests: SendToPublicGroupMembersType[] = await Promise.all(
        selectedRows?.map(async (item) => {
          const { accademicTime, ...rest } = item;
          const sendReq = { messageId, adminId, ...rest };
          return sendReq;
        }) || []
      );
      const actionResult = await dispatch(messageSendToPublicGroupMembersList(sendRequests));
      if (actionResult && Array.isArray(actionResult.payload)) {
        const results: PublicGroupDataWithStatus[] = actionResult.payload;

        const errorMessages = results.find((result) => result.sendStatus === 'Failed');
        const successMessages = results.find((result) => result.sendStatus === 'Success');
        if (!errorMessages) {
          const successMessage = (
            <SuccessMessage icon={SuccessMsg} message="Message sent to all selected Group Members Successfully" />
          );
          await confirm(successMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        } else if (!successMessages) {
          setErrMsgTooltip(
            <Typography variant="subtitle2" fontSize={12} style={{ color: theme.palette.error.main }}>
              No messages sent to any Group Members, Please check the numbers and Try again
            </Typography>
          );
          const errorMessage = (
            <ErrorMessage
              icon={ErrorMsg2}
              message="No messages sent to any Group Members, Please check the numbers and Try again"
            />
          );
          await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        } else {
          setErrMsgTooltip(
            <Typography variant="subtitle2" fontSize={12} style={{ color: theme.palette.error.main }}>
              Error sending messages to some Group Members, Please recheck and Try again
            </Typography>
          );
          const errorMessage = (
            <ErrorMessage
              icon={ErrorMsg}
              message="Error sending messages to some Group Members, Please recheck and Try again"
            />
          );
          await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        }
        setSendResults([...sendResults, ...results]);
        const dataResult = selectedData.map((item) => ({
          ...item,
          sendStatus:
            item.sendStatus !== 'Success' && item.sendStatus !== 'Failed'
              ? results.find((result) => result.gmemberNumber === item.gmemberNumber)?.sendStatus
              : item.sendStatus,
        }));

        // setSelectedRows([]);
        setSelectedData(dataResult);
      } else {
        const errorMessage = <ErrorMessage icon={ErrorMsg} message="Error while sending messages, Please Try again" />;
        await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
      }
      setSelectedRows([]);
      const updatedDisabledCheckBoxes = selectedData.map((selectedItem) =>
        selectedRows.includes(selectedItem)
          ? !disabledCheckBoxes[selectedData.indexOf(selectedItem)]
          : disabledCheckBoxes[selectedData.indexOf(selectedItem)]
      );

      setDisabledCheckBoxes(updatedDisabledCheckBoxes);
    } catch (error) {
      console.error('Error sending messages:', error);
    }
  }, [dispatch, confirm, messageId, selectedRows, adminId, selectedData, sendResults, theme, disabledCheckBoxes]);

  const handleSendVoiceMessage = useCallback(async () => {
    try {
      console.log(selectedRows);
      if (!selectedRows || selectedRows.length === 0) {
        const errorMessage = <ErrorMessage icon={ErrorMsg1} message="Please Select Atleast one Group Member to sent" />;
        await confirm(errorMessage, 'Staff not selected', { okLabel: 'Ok', showOnlyOk: true });
        return;
      }
      const sendRequests: SendVoiceToPublicGroupRequest[] = await Promise.all(
        selectedRows?.map(async (item) => {
          const { accademicTime, ...rest } = item;
          const sendReq = { voiceId, templateId, adminId, ...rest };
          return sendReq;
        }) || []
      );
      const actionResult = await dispatch(voiceMessageSendToPublicGroupMembersList(sendRequests));
      if (actionResult && Array.isArray(actionResult.payload)) {
        const results: PublicGroupDataWithStatus[] = actionResult.payload;

        const errorMessages = results.find((result) => result.sendStatus === 'Failed');
        const successMessages = results.find((result) => result.sendStatus === 'Success');
        if (!errorMessages) {
          const successMessage = (
            <SuccessMessage icon={SuccessMsg} message="Message sent to all selected Group Members Successfully" />
          );
          await confirm(successMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        } else if (!successMessages) {
          setErrMsgTooltip(
            <Typography variant="subtitle2" fontSize={12} style={{ color: theme.palette.error.main }}>
              No messages sent to any Group Members, Please check the numbers and Try again
            </Typography>
          );
          const errorMessage = (
            <ErrorMessage
              icon={ErrorMsg2}
              message="No messages sent to any Group Members, Please check the numbers and Try again"
            />
          );
          await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        } else {
          setErrMsgTooltip(
            <Typography variant="subtitle2" fontSize={12} style={{ color: theme.palette.error.main }}>
              Error sending messages to some Group Members, Please recheck and Try again
            </Typography>
          );
          const errorMessage = (
            <ErrorMessage
              icon={ErrorMsg}
              message="Error sending messages to some Group Members, Please recheck and Try again"
            />
          );
          await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        }
        setSendResults([...sendResults, ...results]);
        const dataResult = selectedData.map((item) => ({
          ...item,
          sendStatus:
            item.sendStatus !== 'Success' && item.sendStatus !== 'Failed'
              ? results.find((result) => result.gmemberNumber === item.gmemberNumber)?.sendStatus
              : item.sendStatus,
        }));

        // setSelectedRows([]);
        setSelectedData(dataResult);
      } else {
        const errorMessage = <ErrorMessage icon={ErrorMsg} message="Error while sending messages, Please Try again" />;
        await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
      }
      setSelectedRows([]);
      const updatedDisabledCheckBoxes = selectedData.map((selectedItem) =>
        selectedRows.includes(selectedItem)
          ? !disabledCheckBoxes[selectedData.indexOf(selectedItem)]
          : disabledCheckBoxes[selectedData.indexOf(selectedItem)]
      );

      setDisabledCheckBoxes(updatedDisabledCheckBoxes);
    } catch (error) {
      console.error('Error sending messages:', error);
    }
  }, [dispatch, confirm, messageId, selectedRows, adminId, selectedData, sendResults, theme, disabledCheckBoxes]);

  const getRowKey = useCallback((row: PublicGroupDataWithStatus) => row.gmemberId, []);

  const getStatusIcon = (row: PublicGroupDataWithStatus) => {
    let iconComponent;

    if (row.sendStatus === 'Failed') {
      iconComponent = (
        <Tooltip title={errMsgTooltip} placement="left">
          <Stack>
            <Lottie animationData={errorIcon} loop={false} style={{ width: '30px' }} />
          </Stack>
        </Tooltip>
      );
    } else if (row.sendStatus === 'Success') {
      iconComponent = <Lottie animationData={successIcon} loop={false} style={{ width: '30px' }} />;
    } else {
      iconComponent = null;
    }

    return <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>{iconComponent}</Box>;
  };

  // const Year = (row: PublicGroupDataWithStatus) => {
  //   let showYear;
  //   if(view === )
  // };
  const PublicGroupsListColumns: DataTableColumn<PublicGroupDataWithStatus>[] = [
    {
      name: 'status',
      renderCell: getStatusIcon,
    },
    {
      name: 'slNo',
      headerLabel: 'Sl.No',
      dataKey: 'gmemberId',
      sortable: true,
    },
    {
      name: 'groupName',
      dataKey: 'groupName',
      headerLabel: 'Group Name',
    },
    {
      name: 'year',
      dataKey: 'accademicTime',
      headerLabel: 'Accademic Year',
    },
    {
      name: 'gmemberName',
      dataKey: 'gmemberName',
      headerLabel: 'Member Name',
    },
    {
      name: 'mobileNumber',
      dataKey: 'gmemberNumber',
      headerLabel: 'Mobile Number',
    },
  ];
  return (
    <GroupsRoot>
      <Box>
        <Divider />
        {view === 'main' ? (
          <form noValidate onReset={handleReset} onSubmit={handleSubmit}>
            <Grid pb={2} pt={1} container spacing={3} alignItems="end">
              <Grid item lg={3} md={4} sm={4} xs={12}>
                <FormControl fullWidth>
                  <Typography variant="h6" fontSize={12} color="GrayText">
                    Academic Year
                  </Typography>
                  <Select
                    labelId="academicYearFilter"
                    id="academicYearFilterSelect"
                    value={academicYearFilter.toString()}
                    onChange={handleYearChange}
                    placeholder="Select Year"
                  >
                    {YearData.map((opt) => (
                      <MenuItem key={opt.accademicId} value={opt.accademicId}>
                        {opt.accademicTime}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item lg={3} md={4} sm={4} xs={12}>
                <FormControl fullWidth>
                  <Typography variant="subtitle2" fontSize={12} color="GrayText">
                    Group Name
                  </Typography>
                  <Select
                    labelId="publicGroupFilter"
                    id="publicGroupFilterSelect"
                    value={publicGroupFilter.toString()}
                    onChange={handleGroupChange}
                    placeholder="Select Group"
                  >
                    <MenuItem value="-1" sx={{ display: 'none' }}>
                      Select All
                    </MenuItem>
                    {PublicGroupsData.map((opt) => (
                      <MenuItem key={opt.groupId} value={opt.groupId}>
                        {opt.groupName}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item lg={3} md={4} sm={4} xs={12}>
                <FormControl fullWidth>
                  <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                    <Button variant="contained" color="secondary" type="reset" fullWidth>
                      Reset
                    </Button>
                    <Button variant="contained" color="primary" type="submit" fullWidth>
                      Search
                    </Button>
                  </Stack>
                </FormControl>
              </Grid>
            </Grid>
          </form>
        ) : (
          <Stack direction="row" alignItems="center" gap={1}>
            <IconButton onClick={handleBack} color="primary" size="small">
              <ArrowBackIcon fontSize="small" />
            </IconButton>
            <Typography variant="subtitle2" fontSize={17} py={2}>
              DELIVERY REPORT
            </Typography>
          </Stack>
        )}
        <Paper
          sx={{
            border:
              selectedData?.length === 0 && sendResults?.length === 0 ? `0px` : `1px solid ${theme.palette.grey[300]}`,
            width: '100%',
            overflow: 'auto',
          }}
        >
          <Box
            sx={{
              height: 'calc(100vh - 23.7rem)',
              width: { xs: selectedData?.length !== 0 ? '43.75rem' : '100%', md: '100%' },
            }}
          >
            {view === 'main' ? (
              <DataTable
                ShowCheckBox
                disabledCheckBox={disabledCheckBoxes}
                RowSelected
                setSelectedRows={setSelectedRows}
                selectedRows={selectedRows}
                columns={PublicGroupsListColumns}
                data={selectedData}
                getRowKey={getRowKey}
                fetchStatus={PublicGroupMembersStatus}
              />
            ) : (
              <DataTable
                setSelectedRows={setSelectedRows}
                selectedRows={selectedRows}
                columns={PublicGroupsListColumns}
                data={sendResults}
                getRowKey={getRowKey}
                fetchStatus={PublicGroupMembersStatus}
              />
            )}
          </Box>
        </Paper>
        <Box
          display="flex"
          sx={{
            justifyContent: {
              xs: 'right',
            },
            pr: {
              lg: '5',
            },
            pt: 3,
          }}
        >
          {view === 'main' && selectedData.length !== 0 && (
            <Stack
              spacing={2}
              direction={{ xs: 'column', sm: 'row' }}
              justifyContent={{ xs: 'ceneter', sm: 'space-between' }}
              width={sendResults.length > 0 ? '100%' : 'auto'}
            >
              {sendResults && sendResults.length > 0 && (
                <Button
                  className="delivery-report-btn"
                  sx={{ width: { xs: '48.5%', sm: 'auto' } }}
                  color="warning"
                  onClick={() => setView('report')}
                  variant="contained"
                >
                  Delivery Report
                </Button>
              )}
              <Stack spacing={2} direction="row">
                <Button
                  className="cancel-btn"
                  // sx={{ width: { xs: '25%', sm: 'auto' } }}
                  onClick={handleCancel}
                  variant="contained"
                  color="secondary"
                >
                  Cancel
                </Button>
                <LoadingButton
                  fullWidth
                  className="send-btn"
                  loadingPosition="start"
                  endIcon={<SendIcon />}
                  // sx={{ width: { sm: 'auto' } }}
                  onClick={voiceId && voiceId !== 0 ? handleSendVoiceMessage : handleSendMessage}
                  loading={isSubmitting}
                  variant="contained"
                  color="primary"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Sending...' : 'Send'}
                </LoadingButton>
              </Stack>
            </Stack>
          )}
        </Box>
      </Box>
      {isSubmitting ? (
        <LoadingPopup popupContent={<LoadingMessage icon={LoadingMsg} message="Sending messages please wait." />} />
      ) : null}
    </GroupsRoot>
  );
}

export default PublicGroups;
