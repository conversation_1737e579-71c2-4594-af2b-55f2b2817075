// eslint-disable-next-line @typescript-eslint/naming-convention
export type videoPlayerProps = {
  url: string;
  videothumbnail: string;
};
export const VideoPlayer: videoPlayerProps[] = [
  {
    url: 'https://youtu.be/bDJKs6r___g',
    videothumbnail:
      'https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80',
  },
  {
    url: 'https://youtu.be/dqtyVVj3Ykk',
    videothumbnail:
      'https://images.unsplash.com/photo-1542744173-8e7e53415bb0?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80',
  },
  {
    url: 'https://youtu.be/mkggXE5e2yk',
    videothumbnail:
      'https://images.unsplash.com/photo-1588072432836-e10032774350?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2072&q=80',
  },
];
