/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Autocomplete,
  Divider,
  Grid,
  Stack,
  TextField,
  Button,
  Table,
  TableContainer,
  TableCell,
  TableHead,
  TableRow,
  TableBody,
  Typography,
  Card,
  Paper,
} from '@mui/material';
import styled from 'styled-components';
import { CLASS_SELECT, YEAR_SELECT } from '@/config/Selection';
import Popup from '@/components/shared/Popup/Popup';
import { Reportlist } from './Reportlist';

const DeliveryReportRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  padding: 1rem;
  .icon {
    margin-right: 10px;
  }
  .Card {
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }
  }
  .MuiTableHead-root {
    .MuiTableCell-root {
      font-size: 13px;
      font-weight: 600;
    }
  }

  .MuiTableBody-root {
    .MuiTableCell-root {
      font-size: 11px;
      font-weight: 600;
    }
  }
`;

function DeliveryReport() {
  const [popupDelivery, setPopupDelivery] = React.useState(false);
  const handleClick = () => {
    setPopupDelivery(true);
  };
  const handleClickClose = () => setPopupDelivery(false);
  return (
    <Page title="Delivery Report">
      <DeliveryReportRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Typography variant="h6" fontSize={17}>
            Delivery Report
          </Typography>
          <Divider />
          <Grid py={2} container spacing={3} pt={2}>
            <Grid item lg={3} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Select Year
              </Typography>
              <Autocomplete
                options={YEAR_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select" />}
              />
            </Grid>
            <Grid item lg={3} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Select Class
              </Typography>
              <Autocomplete
                options={CLASS_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select" />}
              />
            </Grid>
            <Grid item lg={3} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Select Date
              </Typography>
              <TextField fullWidth type="date" placeholder="dd-mm-yyyy" />
            </Grid>
            <Grid item lg={3} xs={12}>
              <Stack spacing={2} sx={{ pt: { xs: 0, md: 3.79 } }} direction="row">
                <Button variant="contained" color="secondary" fullWidth>
                  Reset
                </Button>
                <Button variant="contained" color="primary" fullWidth>
                  Search
                </Button>
              </Stack>
            </Grid>
          </Grid>

          <Divider />

          <Paper
            sx={{ pt: 2, border: '1px solid #e8e8e9', height: 'calc(100vh - 320px)', width: '100%', overflow: 'auto' }}
          >
            <TableContainer className="tableContainer" sx={{ width: { xs: '900px', md: '100%' }, height: '100%' }}>
              <Table stickyHeader>
                <TableHead>
                  <TableRow>
                    <TableCell>Sl.No</TableCell>
                    <TableCell>Message ID</TableCell>
                    <TableCell>Sent Date</TableCell>
                    <TableCell>Message</TableCell>
                    <TableCell>Class</TableCell>
                    <TableCell>Number</TableCell>
                    <TableCell>Send To</TableCell>
                    <TableCell>Sent Count</TableCell>
                    <TableCell>Sent By</TableCell>
                    <TableCell align="center">Action</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {[1, 2, 3, 4, 5, 6, 7].map((row) => (
                    <TableRow key={row} sx={{ borderBottom: '1px solid  rgb(200, 200, 200)' }}>
                      <TableCell width={50}>01</TableCell>
                      <TableCell width={120}>000000001</TableCell>
                      <TableCell width={150}>02 Feb 2023, 10,15 am</TableCell>
                      <TableCell width={350}>
                        Lorem Ipsum is simply dummy text of the printing and typesetting industry.
                      </TableCell>
                      <TableCell width={50}>VIII-A</TableCell>
                      <TableCell width={150}>+91-9564554016</TableCell>
                      <TableCell width={150}>PTA</TableCell>
                      <TableCell width={180}>02</TableCell>
                      <TableCell width={100}>Passdaily</TableCell>
                      <TableCell width={50}>
                        <Button variant="outlined" size="small" color="secondary" onClick={handleClick}>
                          Report
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        </Card>
      </DeliveryReportRoot>
      <Popup size="md" title="Report" state={popupDelivery} onClose={handleClickClose} popupContent={<Reportlist />} />
    </Page>
  );
}

export default DeliveryReport;
