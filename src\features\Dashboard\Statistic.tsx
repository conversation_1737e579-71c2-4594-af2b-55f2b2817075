import BarChart from '@/components/Dashboard/BarChart';
import Typography from '@mui/material/Typography';
import { Box, Stack } from '@mui/material';
import typography from '@/theme/typography';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useAppSelector } from '@/hooks/useAppSelector';
import {
  getClassData,
  getClassStatus,
  getdashboardFeeChartData,
  getdashboardFeeChartStatus,
} from '@/config/storeSelectors';
import { fetchClassList, fetchDashboardFee<PERSON>hart } from '@/store/Dashboard/dashboard.thunks';
import { useEffect, useState } from 'react';
import useAuth from '@/hooks/useAuth';
import MenuItem from '@mui/material/MenuItem';
import Select, { SelectChangeEvent } from '@mui/material/Select';
import styled, { useTheme } from 'styled-components';
import { ClassListInfo } from '@/types/AcademicManagement';
import NodataGraphIcon from '@/assets/Graph.png';

const StatisticRoot = styled.div`
  padding: 1rem;
  width: 100%;
  .apexcharts-menu-icon {
    display: none;
  }
`;

function Statistic({ title }: any) {
  const { user } = useAuth();
  const theme = useTheme();
  const adminId: number | undefined = user?.accountId;
  const academicId = 10;
  const dispatch = useAppDispatch();
  // const dashboardFeeChartStatus = useAppSelector(getdashboardFeeChartStatus);
  const dashboardFeeChartData = useAppSelector(getdashboardFeeChartData);
  // const ClassStatus = useAppSelector(getClassStatus);
  const ClassData = useAppSelector(getClassData);
  const AllClassOption = {
    classId: -1,
    className: 'All Class',
    classDescription: 'string',
    classStatus: 1,
  };

  const classDataWithAllClass = [AllClassOption, ...ClassData];
  const [classOptions, setClassOptions] = useState(classDataWithAllClass[0]);
  const { classId, className } = classOptions || {};

  const handleChange = (event: SelectChangeEvent) => {
    const selectedClass = classDataWithAllClass.find((item) => item.className === event.target.value);
    if (selectedClass) {
      setClassOptions(selectedClass);
    }
  };
  useEffect(() => {
    dispatch(fetchClassList(adminId));
    dispatch(fetchDashboardFeeChart({ adminId, academicId, classId }));
  }, [dispatch, adminId, classId]);

  const totalFeeSum = dashboardFeeChartData.reduce((acc, currentMonth) => {
    return acc + parseFloat(currentMonth.totalFeeCollected);
  }, 0);
  console.log(totalFeeSum);
  const barChartDataFeeStatistic = [{ name: 'Fee', data: dashboardFeeChartData.map((item) => item.totalFeeCollected) }];

  const barChartOptionFeeStatistic = {
    chart: {
      foreColor: '#818594',
    },
    colors: ['#732ebe'],
    // stroke: ['100px'],
    noData: { style: {} },
    xaxis: {
      categories: dashboardFeeChartData.map((item) => item.monthName.substring(0, 3)),
      labels: {
        style: {
          fontWeight: 600,
          fontSize: '10',
        },
      },
      axisBorder: {
        show: true,
      },
      axisTicks: {
        show: false,
      },
    },
    yaxis: {
      labels: {
        style: {
          fontWeight: 600,
        },
      },
    },
    grid: {
      borderColor: 'rgba(163, 174, 208, 0.3)',
      show: true,
      yaxis: {
        lines: {
          show: false,
        },
      },
      row: {
        opacity: 0.5,
      },
      xaxis: {
        lines: {
          show: false,
        },
      },
    },
    fill: {
      type: 'gradient',
    },
    plotOptions: {
      bar: {
        borderRadius: 10,
        borderRadiusApplication: 0,
        columnWidth: '50%',
      },
    },
    tooltip: {
      style: {},
      theme: 'dark',
    },

    dataLabels: {
      enabled: false,
      style: {
        colors: ['#fff'],
        // fontSize: '8',
      },
    },
  };

  return (
    <StatisticRoot>
      <Stack direction="row" justifyContent="space-between">
        <Typography variant="h6" fontSize={20} sx={{ fontFamily: typography.fontFamily }}>
          {title}
        </Typography>
        <Stack direction={{ xs: 'column', sm: 'row' }}>
          {/* <SelectBox Selection_Options={YEAR_SELECT} placeholder="Year" /> */}
          <Select
            sx={{ backgroundColor: theme.palette.grey[100], color: theme.palette.primary.main, height: 30 }}
            value={className}
            onChange={handleChange}
            displayEmpty
            labelId="demo-dialog-select-label"
            id="demo-dialog-select"
            inputProps={{ 'aria-label': 'Without label' }}
            MenuProps={{
              PaperProps: {
                style: {
                  maxHeight: '250px', // Adjust the value to your desired height
                },
              },
            }}
          >
            {/* <MenuItem value={className} className="d-none">
              {className}
            </MenuItem> */}
            {classDataWithAllClass?.map((item: ClassListInfo) => (
              <MenuItem sx={{ fontSize: '13px' }} key={item.classId} value={item.className}>
                {item.className}
              </MenuItem>
            ))}
          </Select>
        </Stack>
      </Stack>
      <Box sx={{ overflowX: 'scroll', height: '400px' }}>
        {totalFeeSum === 0 ? (
          <Stack direction="column" justifyContent="center" alignItems="center" spacing={1} my={2}>
            <img width={255} src={NodataGraphIcon} alt="" style={{ opacity: 0.6 }} />
            <Typography variant="subtitle2" color="secondary">
              No Fees Data in this Class.
            </Typography>
          </Stack>
        ) : (
          <Box sx={{ width: { sm: '100%', xs: '550px' }, height: { sm: '250px', xs: '300px' } }}>
            <BarChart chartDatas={barChartDataFeeStatistic} chartOptions={barChartOptionFeeStatistic} height="100%" />
          </Box>
        )}
      </Box>
    </StatisticRoot>
  );
}

export default Statistic;
