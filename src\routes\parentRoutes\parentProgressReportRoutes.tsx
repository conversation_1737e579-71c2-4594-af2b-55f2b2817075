import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import Loadable from '@/components/shared/Loadable';

const Reports = Loadable(lazy(() => import('@/Parent-Side/pages/Reports')));
const ProgressReport = Loadable(lazy(() => import('@/Parent-Side/features/ProgressReport/ProgressReport')));
const ProgressReportCBSE = Loadable(lazy(() => import('@/Parent-Side/features/ProgressReport/ProgressReportCBSE')));
const ProgressReportLPUP = Loadable(lazy(() => import('@/Parent-Side/features/ProgressReport/ProgressReportLPUP')));
const AnnualReport = Loadable(lazy(() => import('@/Parent-Side/features/ProgressReport/AnnualReport')));

export const parentProgressReportRoutes: RouteObject[] = [
  {
    path: '/reports',
    element: <Reports />,
    children: [
      {
        path: 'progress-report',
        element: <ProgressReport />,
      },
      {
        path: 'progress-report-cbse',
        element: <ProgressReportCBSE />,
      },
      {
        path: 'progress-report-lp-up',
        element: <ProgressReportLPUP />,
      },
      {
        path: 'annual-report',
        element: <AnnualReport />,
      },
    ],
  },
];
