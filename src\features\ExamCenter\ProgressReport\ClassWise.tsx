import React, { useRef, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Box,
  Button,
  Divider,
  FormControl,
  Card,
  Stack,
  Collapse,
  Grid,
  IconButton,
  TextField,
  Typography,
  Select,
  MenuItem,
  Paper,
  useTheme,
  TableContainer,
  TableRow,
  TableCell,
  TableBody,
  Table,
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import { breakPointsMaxwidth } from '@/config/breakpoints';
import { useReactToPrint } from 'react-to-print';
import styled from 'styled-components';
import { EXAM_SELECT_OPTIONS } from '@/config/Selection';
import Excel from '@/assets/attendance/Excel.svg';
import TabButton from '@/components/shared/TabButton';

const SubjectDemo = ['SS', 'ISL', 'ENG', 'MAL', 'HIN', 'ARA', 'PHY', 'CHE', 'BIO', 'CS', 'MAT', 'BS'];

const ClassWiseRoot = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    min-height: calc(100vh - 100px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      height: calc(100% - 100px);
      flex-grow: 1;
      .certificate {
        /* border: 2px solid ${(props) => props.theme.palette.primary.main}; */
        /* padding: 0px 20px; */
        /* border-radius: 10px; */
      }
      .main-card-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid ${(props) => props.theme.palette.secondary.lighter};
        border-radius: 6px;
        overflow: hidden;
        .card-top {
          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.primary.lighter : props.theme.palette.grey[900]};
        }

        .card-table-container {
          flex-grow: 1;
          width: 100%;
          height: 100%;
          display: flex;
          flex-direction: column;
          /* border: 1px solid ${(props) => props.theme.palette.secondary.lighter}; */
          overflow: hidden;
          border-radius: 0px;

          .MuiTableContainer-root {
            height: 100%;
          }
          .MuiTable-root {
          }

          .MuiTablePagination-root {
            flex-grow: 1;
            flex-shrink: 0;
          }
          .MuiTableCell-root {
            border: 0.5px solid ${(props) => props.theme.palette.secondary.lighter};
            border-radius: 0px;
          }
          .subject_head .MuiTableCell-root {
            font-weight: ${(props) => props.theme.typography.fontWeightBold};
            color: ${(props) =>
              props.theme.themeMode === 'light' ? props.theme.palette.grey[600] : props.theme.palette.grey[500]};
          }
          .Table_head .MuiTableCell-root {
            font-weight: ${(props) => props.theme.typography.fontWeightBold};
            color: ${(props) =>
              props.theme.themeMode === 'light' ? props.theme.palette.grey[600] : props.theme.palette.grey[500]};
            background-color: ${(props) =>
              props.theme.themeMode === 'light' ? props.theme.palette.grey[50] : props.theme.palette.grey[900]};
          }
          .mark_head {
            font-weight: ${(props) => props.theme.typography.fontWeightBold};
            color: ${(props) =>
              props.theme.themeMode === 'light' ? props.theme.palette.grey[600] : props.theme.palette.grey[500]};
          }
        }
      }
    }
    .table_top {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-evenly;
      border: 1px solid ${(props) => props.theme.palette.secondary.lighter};
    }
    @media ${breakPointsMaxwidth.xl} {
      .MuiTableCell-root {
        font-size: 11px;
      }
      .MuiTableCell-root .MuiTypography-root {
        font-size: 11px;
      }
    }
    @media screen and (max-width: 1217px) {
      .MuiFormControl-root {
        width: 200px;
      }
      .select_box {
        width: 200px;
      }
    }
    @media screen and (max-width: 1160px) {
      .MuiTableContainer-root {
        /* width: 900px; */
      }
      .card-table-container {
        overflow: auto;
      }
    }
    .card_top {
      display: flex;
      justify-content: space-between;
      flex-direction: row;
    }
    .title_searchbar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex: 1;
    }
    .progress_report_tab {
      display: flex;
      align-items: center;
      flex-direction: row;
    }
    @media screen and (max-width: 1160px) {
      .card_top {
        display: flex;
        flex-direction: column;
      }
      .progress_report_tab {
        display: flex;
        justify-content: end;
        padding-bottom: 10px;
        overflow-x: scroll;
        ::-webkit-scrollbar {
          height: 10px;
        }
        ::-webkit-scrollbar-thumb {
          background-color: ${(props) => props.theme.palette.grey[400]};
          border-radius: 20px;
        }
      }
    }
    @media screen and (max-width: 998px) {
      .progress_report_tab {
        justify-content: start;
      }
      .table_top {
        /* flex-direction: column; */
      }
    }
    @media screen and (max-width: 768px) {
      .progress_report_tab {
        ::-webkit-scrollbar {
          height: 0px;
        }
      }
      .table_top {
      }
    }
  }
`;

export type ClassWiseProps = {
  onClickPromotionList: () => void;
  onClickStudentWise: () => void;
  onClickTopper: () => void;
  onClickGradeWise: () => void;
};
function ClassWise({ onClickPromotionList, onClickStudentWise, onClickTopper, onClickGradeWise }: ClassWiseProps) {
  const [showFilter, setShowFilter] = useState(false);
  const theme = useTheme();
  // const { themeMode } = useSettings();

  const componentRef = useRef<HTMLInputElement>(null);
  const handlePrint = useReactToPrint({
    content: () => componentRef.current,
    pageStyle: `
      @page {
        size: landscape;
      }
    `,
  });

  return (
    <Page title="Class Wise">
      <ClassWiseRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <div className="card_top ">
            <div className="title_searchbar">
              <Typography variant="h6" fontSize={17}>
                Class Wise
              </Typography>

              <Box sx={{ flexShrink: 0 }}>
                <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                  <SearchIcon />
                </IconButton>
              </Box>
            </div>
            <Stack className="progress_report_tab">
              <div style={{ flexShrink: 0 }}>
                <TabButton title="Student Individual" variant="outlined" onClick={onClickStudentWise} />
                <TabButton title="Student Promotion List" variant="outlined" onClick={onClickPromotionList} />
                <TabButton title="Class Wise" variant="contained" />
                <TabButton title="Topper" variant="outlined" onClick={onClickTopper} />
                <TabButton title="Grade Wise" variant="outlined" onClick={onClickGradeWise} />
              </div>
            </Stack>
          </div>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={3} alignItems="end">
                  <Grid item sm="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', xl: 240 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Year
                      </Typography>
                      <TextField name="year" sx={{ minWidth: { xs: '100%', xl: 240 } }} />
                    </FormControl>
                  </Grid>
                  <Grid item sm="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', xl: 240 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <TextField name="className" sx={{ minWidth: { xs: '100%', xl: 240 } }} />
                    </FormControl>
                  </Grid>
                  <Grid item sm="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', xl: 240 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Exam
                      </Typography>
                      <Select
                        sx={{ minWidth: { xs: '100%', xl: 240 } }}
                        className="select_box"
                        labelId="classStatusFilter"
                        id="classStatusFilterSelect"
                      >
                        <MenuItem value={-1}>All</MenuItem>
                        {EXAM_SELECT_OPTIONS.map((opt) => (
                          <MenuItem key={opt.id} value={opt.id}>
                            {opt.exam}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item sm="auto" xs={12}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button type="reset" variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>
            <Box ref={componentRef} className="main-card-container" sx={{ mt: 2 }}>
              <Box className="card-top text-center" sx={{ py: 2 }}>
                <Stack direction="column" gap={1}>
                  <Typography variant="h6" fontSize={17} sx={{ color: theme.palette.primary.main }}>
                    PUBLIC SCHOOL
                  </Typography>
                  <Typography variant="h6" fontSize={15}>
                    VII-A MONTHLY JANUARY
                  </Typography>
                  <Typography variant="h5" fontSize={14}>
                    CLASS INCHARGE : PASSDAILY STAFF
                  </Typography>
                </Stack>
              </Box>
              <Paper className="card-table-container">
                <Box className="table_top">
                  <Stack direction="row" flexWrap="wrap">
                    <Typography variant="subtitle1">NO OF FULL PASSES &nbsp;:&nbsp;</Typography>
                    <Typography variant="subtitle1" className="fw-bold">
                      10
                    </Typography>
                  </Stack>
                  <Stack direction="row" flexWrap="wrap">
                    <Typography variant="subtitle1">CLASS PERCENTAGE &nbsp;:&nbsp;</Typography>
                    <Typography variant="subtitle1" className="fw-bold">
                      65%
                    </Typography>
                  </Stack>
                </Box>
                <TableContainer>
                  <Table sx={{ overflow: 'auto', minWidth: { xs: '1200px' } }}>
                    <TableBody>
                      <TableRow className="subject_head">
                        <TableCell colSpan={2}>Subject</TableCell>
                        {SubjectDemo?.map((subject) => {
                          return (
                            <TableCell className="text-center" key={subject}>
                              {subject}
                            </TableCell>
                          );
                        })}
                        <TableCell width={40} rowSpan={6}>
                          FL
                        </TableCell>
                        <TableCell width={40} rowSpan={6}>
                          PAS
                        </TableCell>
                        <TableCell width={40} rowSpan={6}>
                          STA
                        </TableCell>
                        <TableCell width={40} rowSpan={6}>
                          TOT
                        </TableCell>
                        <TableCell width={40} rowSpan={6}>
                          PER
                        </TableCell>
                        <TableCell rowSpan={5} colSpan={2}>
                          No Of
                        </TableCell>
                      </TableRow>

                      <TableRow>
                        <TableCell colSpan={2} className="mark_head">
                          Max Marks
                        </TableCell>
                        {SubjectDemo?.map((subject) => {
                          return (
                            <TableCell key={subject} className="text-center">
                              100
                            </TableCell>
                          );
                        })}
                      </TableRow>
                      <TableRow>
                        <TableCell colSpan={2} className="mark_head">
                          Pass Marks
                        </TableCell>
                        {SubjectDemo?.map((subject) => {
                          return <TableCell className="text-center">30</TableCell>;
                        })}
                      </TableRow>
                      <TableRow>
                        <TableCell colSpan={2} className="mark_head">
                          Subject Wise Pass
                        </TableCell>
                        {SubjectDemo?.map((subject) => {
                          return <TableCell className="text-center">28</TableCell>;
                        })}
                      </TableRow>
                      <TableRow>
                        <TableCell colSpan={2} className="mark_head">
                          Subject Wise Pass Percentage
                        </TableCell>
                        {SubjectDemo?.map((subject) => {
                          return <TableCell className="text-center">75.10%</TableCell>;
                        })}
                      </TableRow>
                      <TableRow className="Table_head">
                        <TableCell>Sl.No</TableCell>
                        <TableCell>Name</TableCell>
                        {SubjectDemo?.map(() => {
                          return <TableCell className="text-center">M G</TableCell>;
                        })}

                        <TableCell>A</TableCell>
                        <TableCell>B</TableCell>
                      </TableRow>
                      {[1, 2, 3, 4].map(() => {
                        return (
                          <TableRow>
                            <TableCell>1</TableCell>
                            <TableCell>Alex Peter Micheal John</TableCell>
                            {SubjectDemo?.map(() => {
                              return <TableCell className="text-center">32,D</TableCell>;
                            })}
                            <TableCell>1</TableCell>
                            <TableCell>8</TableCell>
                            <TableCell>FL</TableCell>
                            <TableCell>FL</TableCell>
                            <TableCell>97%</TableCell>
                            <TableCell>2</TableCell>
                            <TableCell>9</TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Paper>
            </Box>
          </div>
          <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, mt: 3 }}>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="success" sx={{ gap: 1 }} size="medium">
                <img className="icon" alt="Excel" src={Excel} />
                <Typography color="white" fontSize={12}>
                  Download
                </Typography>
              </Button>
              <Button onClick={handlePrint} variant="contained" color="primary">
                Print
              </Button>
            </Stack>
          </Box>
        </Card>
      </ClassWiseRoot>
    </Page>
  );
}

export default ClassWise;
