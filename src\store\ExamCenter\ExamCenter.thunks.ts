import api from '@/api';
import { CreateResponse, DeleteResponse, UpdateResponse } from '@/types/Common';
import {
  ExamCreateRequest,
  ExamListPagedData,
  ExamListRequest,
  MarkRegisterFilterDataType,
  MarkRegisterFilterSubjectsType,
  MarkRegisterDataType,
  MarkRegisterCBSEAddDataType,
  MarkRegisterCEDataType,
  MarkRegisterCBSEwithCEAddDataType,
} from '@/types/ExamCenter';
import { createAsyncThunk } from '@reduxjs/toolkit';

// export const fetchExamList = createAsyncThunk<ExamListPagedData, ExamListRequest, { rejectValue: string }>(
//   'examCenter/list',
//   async (request, { rejectWithValue }) => {
//     try {
//       const response = await api.ExamCenter.GetExamList(request);
//       return response.data;
//     } catch {
//       return rejectWithValue('Something went wrong in fetching class List');
//     }
//   }
// );

// export const addNewExam = createAsyncThunk<CreateResponse, ExamCreateRequest, { rejectValue: string }>(
//   'examCenter/addnew',
//   async (request, { rejectWithValue }) => {
//     try {
//       const response = await api.ExamCenter.AddNewExam(request);

//       return response.data;
//     } catch {
//       return rejectWithValue('Something went wrong in adding new class');
//     }
//   }
// );

// export const updateExam = createAsyncThunk<UpdateResponse, ExamCenterInfo, { rejectValue: string }>(
//   'examCenter/update',
//   async (request, { rejectWithValue }) => {
//     try {
//       const response = await api.ExamCenter.UpdateExam(request);
//       return response.data;
//     } catch {
//       return rejectWithValue('Something went wrong in updating class');
//     }
//   }
// );

export const deleteExam = createAsyncThunk<DeleteResponse, number, { rejectValue: string }>(
  'examCenter/delete',
  async (examId, { rejectWithValue }) => {
    try {
      const response = await api.ExamCenter.DeleteExam(examId);

      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in deleting class');
    }
  }
);

export const fetchMarkRegisterFilter = createAsyncThunk<
  MarkRegisterFilterDataType,
  number | undefined,
  { rejectValue: string }
>('examCenter/markRegister/FilterData', async (request, { rejectWithValue }) => {
  try {
    const response = await api.ExamCenter.GetMarkRegisterFilter(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching mark register filter data');
  }
});

export const fetchMarkRegisterSubjectFilter = createAsyncThunk<
  MarkRegisterFilterSubjectsType[],
  { adminId: number | undefined; classId: number | undefined },
  { rejectValue: string }
>('examCenter/markRegister/SubjectFilterData', async (request, { rejectWithValue }) => {
  try {
    const response = await api.ExamCenter.GetMarkRegisterSubjectFilter(request.adminId, request.classId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching mark register subject filter data');
  }
});

export const fetchMarkRegisterDetails = createAsyncThunk<
  MarkRegisterDataType[],
  {
    adminId: number | undefined;
    academicId: number | undefined;
    classId: number | undefined;
    examId: number | undefined;
    subjectId: number | undefined;
  },
  { rejectValue: string }
>('examCenter/markRegister/Details', async (request, { rejectWithValue }) => {
  try {
    const response = await api.ExamCenter.GetMarkDetails(
      request.adminId,
      request.academicId,
      request.classId,
      request.examId,
      request.subjectId
    );
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching mark register details');
  }
});

export const addMarkRegisterCBSE = createAsyncThunk<
  MarkRegisterCBSEAddDataType[],
  MarkRegisterCBSEAddDataType[],
  { rejectValue: string }
>('examCenter/markRegister/add(CBSE)', async (request, { rejectWithValue }) => {
  try {
    const response = await api.ExamCenter.AddMarkRegisterCBSE(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in adding mark register data');
  }
});

export const fetchMarkRegisterDetailsWithCE = createAsyncThunk<
  MarkRegisterCEDataType[],
  {
    adminId: number | undefined;
    academicId: number | undefined;
    classId: number | undefined;
    examId: number | undefined;
    subjectId: number | undefined;
  },
  { rejectValue: string }
>('examCenter/markRegister/Details(With CE)', async (request, { rejectWithValue }) => {
  try {
    const response = await api.ExamCenter.GetMarkDetailsWithCE(
      request.adminId,
      request.academicId,
      request.classId,
      request.examId,
      request.subjectId
    );
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching mark register details with CE');
  }
});

export const addMarkRegisterCBSEwithCE = createAsyncThunk<
  MarkRegisterCBSEwithCEAddDataType[],
  MarkRegisterCBSEwithCEAddDataType[],
  { rejectValue: string }
>('examCenter/markRegister/add(CBSE with CE)', async (request, { rejectWithValue }) => {
  try {
    const response = await api.ExamCenter.AddMarkRegisterCBSEwithCE(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in adding mark register data with CE');
  }
});
