import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import Loadable from '@/components/shared/Loadable';

const OnlineExam = Loadable(lazy(() => import('@/pages/OnlineExam')));
const OnlineExamList = Loadable(lazy(() => import('@/features/OnlineExam/OnlineExamList/OnlineExamList')));
const QuestionList = Loadable(lazy(() => import('@/features/OnlineExam/QuestionList/QuestionList')));
const MaptoProgressCard = Loadable(lazy(() => import('@/features/ExamCenter/AddOnlineMarks')));

export const onlineExamRoutes: RouteObject[] = [
  {
    path: '/online-exam',
    element: <OnlineExam />,
    children: [
      {
        path: 'exam-details',
        element: <OnlineExamList />,
      },
      {
        path: 'questions-list',
        element: <QuestionList />,
      },
      {
        path: 'map-progress-card',
        element: <MaptoProgressCard />,
      },
    ],
  },
];
