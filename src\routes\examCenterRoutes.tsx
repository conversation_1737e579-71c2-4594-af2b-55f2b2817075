import Loadable from '@/components/shared/Loadable';
import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';

const ExamCenter = Loadable(lazy(() => import('@/pages/ExamCenter')));
const AddOnlineMarks = Loadable(lazy(() => import('@/features/ExamCenter/AddOnlineMarks')));
const ManageExam = Loadable(lazy(() => import('@/features/ExamCenter/ExamManage/ManageExam')));
const ExamTimetable = Loadable(lazy(() => import('@/features/ExamCenter/ExamTimetable/ExamTimetable')));
const SendTimetable = Loadable(lazy(() => import('@/features/ExamCenter/SendTimetable')));
const MarkRegister = Loadable(lazy(() => import('@/features/ExamCenter/MarkRegister/MarkRegister')));
const MarkRegisterCE = Loadable(lazy(() => import('@/features/ExamCenter/MarkRegisterCE/MarkRegisterCE')));
const DeleteMarks = Loadable(lazy(() => import('@/features/ExamCenter/DeleteMarks')));
const ProgressReport = Loadable(lazy(() => import('@/features/ExamCenter/ProgressReport/Index')));
const SendProgressReportIndex = Loadable(lazy(() => import('@/features/ExamCenter/SendProgressReport/Index')));

export const examCenterRoutes: RouteObject[] = [
  {
    path: '/exam-center',
    element: <ExamCenter />,
    children: [
      {
        path: 'online-marks',
        element: <AddOnlineMarks />,
      },
      {
        path: 'list',
        element: <ManageExam />,
      },
      {
        path: 'timetable',
        element: <ExamTimetable />,
      },
      {
        path: 'send-timetable',
        element: <SendTimetable />,
      },
      {
        path: 'mark-register',
        element: <MarkRegister />,
      },
      {
        path: 'mark-register-ce',
        element: <MarkRegisterCE />,
      },
      {
        path: 'delete-marks',
        element: <DeleteMarks />,
      },
      {
        path: 'progress-report',
        element: <ProgressReport />,
      },
      {
        path: 'send-progress-report',
        element: <SendProgressReportIndex />,
      },
    ],
  },
];
