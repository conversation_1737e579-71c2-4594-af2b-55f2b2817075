/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  Collapse,
  FormControl,
  Checkbox,
} from '@mui/material';
import deleteBin from '@/assets/ManageFee/deleteBin.json';
import styled from 'styled-components';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import DeleteIcon from '@mui/icons-material/Delete';
import SearchIcon from '@mui/icons-material/Search';
import { ExamTimetableInfo } from '@/types/ExamCenter';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { CLASS_SELECT_OPTIONS, SUBJECT_SELECT, YEAR_SELECT_OPTIONS } from '@/config/Selection';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import Popup from '@/components/shared/Popup/Popup';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import { deleteTermFeeList } from '@/store/ManageFee/manageFee.thunks';
import { useAppDispatch } from '@/hooks/useAppDispatch';

const DeleteMarksRoot = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      height: calc(100% - 45px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;
const dummyExamData: ExamTimetableInfo[] = [
  {
    examId: 1,
    accademicYear: '2023-2024',
    className: 'VII-A',
    examName: 'Malayalam',
    examSubject: 'Malayalam',
    examDate: '12-07-2023',
    examTime: '10:00AM to 12:30PM',
    status: 1,
  },
  {
    examId: 2,
    accademicYear: '2023-2024',
    className: 'VII-A',
    examName: 'English',
    examSubject: 'English',
    examDate: '12-07-2023',
    examTime: '10:00AM to 12:30PM',
    status: 1,
  },
  {
    examId: 3,
    accademicYear: '2023-2024',
    className: 'VII-A',
    examName: 'Hindi',
    examSubject: 'Malayalam',
    examDate: '12-07-2023',
    examTime: '10:00AM to 12:30PM',
    status: 1,
  },
  {
    examId: 0,
    accademicYear: '2023-2024',
    className: 'VII-A',
    examName: 'Malayalam',
    examSubject: 'Malayalam',
    examDate: '12-07-2023',
    examTime: '10:00AM to 12:30PM',
    status: 1,
  },

  {
    examId: 0,
    accademicYear: '2023-2024',
    className: 'VII-A',
    examName: 'Malayalam',
    examSubject: 'Malayalam',
    examDate: '12-07-2023',
    examTime: '10:00AM to 12:30PM',
    status: 1,
  },

  {
    examId: 0,
    accademicYear: '2023-2024',
    className: 'VII-A',
    examName: 'Malayalam',
    examSubject: 'Malayalam',
    examDate: '12-07-2023',
    examTime: '10:00AM to 12:30PM',
    status: 1,
  },
];

function DeleteMarks() {
  const { confirm } = useConfirm();
  const dispatch = useAppDispatch();

  const [drawerOpen, setDrawerOpen] = React.useState<boolean>(false);
  const [showFilter, setShowFilter] = useState(false);
  const [Delete, setDelete] = React.useState(false);

  const handleClickDelete = () => setDelete(true);
  const handleClickCloseDelete = () => setDelete(false);

  const handleOpen = () => {
    setDrawerOpen(true);
  };

  const handleDeleteTermFeeList = useCallback(
    async (termObj: any) => {
      const { termId } = termObj;
      const deleteConfirmMessage = (
        <DeleteMessage
          jsonIcon={deleteBin}
          message={
            <div>
              Are you sure you want to delete the Row <br />
              &quot;{termObj.termTitle}&quot; ?
            </div>
          }
        />
      );
      if (await confirm(deleteConfirmMessage, 'Delete Row?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
        const sendReq = [{ dbResult: 'string', deletedId: 0 }];

        console.log('sendReq', sendReq);

        const deleteResponse = await dispatch(deleteTermFeeList(sendReq));
        console.log('deleteResponse::::----', deleteResponse);
        if (deleteResponse && Array.isArray(deleteResponse.payload)) {
          const results: any[] = deleteResponse.payload;
          const errorMessages = results.find((result) => result.dbResult === 'Failed');
          const successMessages = results.find((result) => result.dbResult === 'Success');
          // Reload only the specific message template with the deleted messageId

          if (!errorMessages) {
            const deleteSuccessMessage = <SuccessMessage loop={false}  message="Delete successfully" />;
            await confirm(deleteSuccessMessage, 'Success', { okLabel: 'Ok', showOnlyOk: true });
          } else if (!successMessages) {
            const deleteErrorMessage = <DeleteMessage  message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          } else {
            const deleteErrorMessage = <DeleteMessage  message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          }
          // loadTermFeeList({ ...currentTermFeeListRequest });

          // setSnackBar(true);
          // setTermFeeData((prevDetails) => prevDetails.filter((item) => item.termId !== termId));
        }
      }
    },
    [confirm]
  );

  const toggleDrawerClose = useCallback(() => setDrawerOpen(false), []);

  // const handleSaveorEdit = useCallback(
  //   async (value: ExamTimetableInfo, mode: 'create' | 'edit') => {
  //     if (mode === 'create') {
  //       const { examId, ...rest } = value;
  //       const response = await dispatch(addNewExam(rest)).unwrap();

  //       if (response.id > 0) {
  //         setDrawerOpen(false);
  //         const successMessage = <SuccessMessage message="Exam created successfully" />;
  //         await confirm(successMessage, 'Exam Created', { okLabel: 'Ok', showOnlyOk: true });

  //         loadExamList({ ...currentExamListRequest, pageNumber: 1, sortColumn: 'examId', sortDirection: 'desc' });
  //       }
  //     } else {
  //       const response = await dispatch(updateExam(value)).unwrap();
  //       if (response.rowsAffected > 0) {
  //         setDrawerOpen(false);
  //         const successMessage = <SuccessMessage message="Exam updated successfully" />;
  //         await confirm(successMessage, 'Exam Updated', { okLabel: 'Ok', showOnlyOk: true });

  //         loadExamList(currentExamListRequest);
  //       }
  //     }
  //   },
  //   [confirm, currentExamListRequest, dispatch, loadExamList]
  // );

  // const handleStatusChange = (e: SelectChangeEvent) => {
  //   const statusVal = parseInt(e.target.value, 10);
  //   setExamStatusFilter(statusVal);
  //   loadExamList({
  //     ...currentExamListRequest,
  //     filters: { ...currentExamListRequest.filters, examStatus: statusVal },
  //   });
  // };

  const getRowKey = useCallback((row: ExamTimetableInfo) => row.examId, []);

  const examListColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'select',
        renderHeader: () => {
          return <Checkbox />;
        },
        renderCell: () => {
          return <Checkbox />;
        },
      },
      {
        name: 'studentName',
        dataKey: 'studentName',
        headerLabel: 'Student Name',
        sortable: true,
      },
      {
        name: 'class',
        dataKey: 'class',
        headerLabel: 'Class',
      },
      {
        name: 'rollNo',
        dataKey: 'rollNo',
        headerLabel: 'Roll No',
        sortable: true,
      },
      {
        name: 'mark',
        headerLabel: 'Marks Scored',
        renderCell: () => {
          return <TextField />;
        },
      },
      {
        name: 'grade',
        headerLabel: 'Grade',
        renderCell: () => {
          return <TextField disabled />;
        },
      },
      {
        name: 'actions',
        headerLabel: 'Actions',
        renderCell: () => {
          return (
            <IconButton onClick={handleDeleteTermFeeList} size="small" sx={{ padding: 0.5 }}>
              <DeleteIcon />
            </IconButton>
          );
        },
      },
    ],
    []
  );
  return (
    <Page title="List">
      <DeleteMarksRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17}>
              Delete Marks
            </Typography>
            <Box pb={1} sx={{ flexShrink: 0 }}>
              <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                <SearchIcon />
              </IconButton>
            </Box>
          </Stack>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={3} alignItems="end">
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 250 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Autocomplete
                        options={YEAR_SELECT_OPTIONS}
                        renderInput={(params) => <TextField {...params} placeholder="Select Status" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 250 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Exam
                      </Typography>
                      <Autocomplete
                        options={['Quaterly', 'Half-Yearly', 'Annual']}
                        renderInput={(params) => <TextField {...params} placeholder="Select Status" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 250 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Autocomplete
                        options={CLASS_SELECT_OPTIONS}
                        renderInput={(params) => <TextField {...params} placeholder="Select Status" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 250 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Subject
                      </Typography>
                      <Autocomplete
                        options={SUBJECT_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select Status" />}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item lg="auto" xs={12} sm={6}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Paper className="card-table-container" sx={{ marginTop: '12px' }}>
              <DataTable columns={examListColumns} data={dummyExamData} getRowKey={getRowKey} fetchStatus="success" />
            </Paper>
          </div>
          <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 2 }}>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="secondary">
                Cancel
              </Button>
              <Button onClick={handleOpen} variant="contained" color="primary">
                Delete
              </Button>
            </Stack>
          </Box>
        </Card>
      </DeleteMarksRoot>
      <Popup
        size="xs"
        state={Delete}
        onClose={handleClickCloseDelete}
        popupContent={<DeleteMessage jsonIcon={deleteBin} message="Are you sure want to delete?" />}
      />
      <TemporaryDrawer onClose={toggleDrawerClose} Title="Upload Details" state={drawerOpen} DrawerContent="" />
    </Page>
  );
}

export default DeleteMarks;
