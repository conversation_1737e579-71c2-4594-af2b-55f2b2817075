/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  Collapse,
  FormControl,
  Checkbox,
  FormGroup,
  FormControlLabel,
  useTheme,
  Chip,
  Avatar,
} from '@mui/material';
import styled from 'styled-components';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import { MdAdd, MdDelete } from 'react-icons/md';
import SearchIcon from '@mui/icons-material/Search';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import DeleteIcon from '@mui/icons-material/Delete';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import DateSelect from '@/components/shared/Selections/DateSelect';
import { STATUS_SELECT } from '@/config/Selection';
import typography from '@/theme/typography';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import MenuEditDeleteView from '@/components/shared/Selections/MenuEditDeleteView';
import Popup from '@/components/shared/Popup/Popup';
import CreateTcForm from '@/features/Tc&Cc/TcList/CreateTcForm';
import CreateSessionForm from '@/features/SchoolPlan/SessionPlanList/CreateSessionForm';

const DescriptiveExamRoot = styled.div``;

export const data = [
  {
    slNo: 1,
    studentName: 'Alex Peter',
    guardian: 'Jack',
    admissionNo: '12345',
  },
  {
    slNo: 2,
    studentName: 'Alex Peter',
    guardian: 'Jack',
    admissionNo: '12345',
  },
  {
    slNo: 3,
    studentName: 'Alex Peter',
    guardian: 'Jack',
    admissionNo: '12345',
  },
  {
    slNo: 4,
    studentName: 'Alex Peter',
    guardian: 'Jack',
    admissionNo: '12345',
  },
  {
    slNo: 5,
    studentName: 'Alex Peter',
    guardian: 'Jack',
    admissionNo: '12345',
  },
];

function DescriptiveExam() {
  const [popupOpen, setPopupOpen] = React.useState<boolean>(false);
  const [showFilter, setShowFilter] = useState(false);
  const [showSend, setShowSend] = useState(true);
  const theme = useTheme();

  const handleOpen = () => {
    setPopupOpen(true);
  };

  const togglePopupClose = useCallback(() => setPopupOpen(false), []);

  const getRowKey = useCallback((row: any) => row.examId, []);
  const DescriptiveExamColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'class',
        headerLabel: 'Class',
        dataKey: 'class',
      },
      {
        name: 'subject',
        headerLabel: 'Subject',
        dataKey: 'subject',
      },
      {
        name: 'examName',
        headerLabel: 'ExamName',
        renderCell: (row) => {
          return (
            <Box display="flex" justifyContent="space-between" flex={1}>
              <Chip size="small" sx={{ borderRadius: 1 }} color="info" label="First Year" />
            </Box>
          );
        },
      },
      {
        name: 'from',
        headerLabel: 'From',
        dataKey: 'from',
      },
      {
        name: 'to',
        headerLabel: 'To',
        dataKey: 'to',
      },
      {
        name: 'teacher',
        headerLabel: 'Teacher',
        dataKey: 'teacher',
      },
    ],
    []
  );

  return (
    <>
      <DescriptiveExamRoot>
        <div className="">
          {/* <Paper className="card-table-container" sx={{ marginTop: 1 }}>
              <DataTable columns={DescriptiveExamColumns} data={data} getRowKey={getRowKey} fetchStatus="success" />
            </Paper> */}
          <Paper className="">
            <Grid container spacing={2}>
              {data.map((student, rowIndex) => (
                <Grid item xl={3} lg={6} md={12} sm={6} xs={12} key={rowIndex}>
                  <Card
                    className="student_card"
                    sx={{ p: 2, boxShadow: 0, border: `1px solid ${theme.palette.grey[300]}` }}
                  >
                    <Box flexGrow={1}>
                      {DescriptiveExamColumns.map((item, index) => (
                        <Stack direction="row" key={index}>
                          <Grid container>
                            <Grid item lg={5} xs={6}>
                              <Typography key={item.name} variant="subtitle1" color="GrayText" fontSize={13} mr={2}>
                                {item.headerLabel}
                              </Typography>
                            </Grid>
                            <Grid item lg={7} xs={6} mb={0.5} mt="auto">
                              {item.dataKey ? (
                                <Typography variant="h6" fontSize={13}>
                                  {/* {`: ${(student as { [key: string]: any })[item.dataKey ?? '']}`} */}
                                  {item.dataKey
                                    ? `:${' '}${(student as { [key: string]: any })[item.dataKey ?? '']}`
                                    : item && item.renderCell && item.renderCell(student, rowIndex)}
                                </Typography>
                              ) : (
                                item && item.renderCell && item.renderCell(student, rowIndex)
                              )}
                            </Grid>
                          </Grid>
                        </Stack>
                      ))}
                    </Box>
                    <Stack position="absolute" top={10} right={10}>
                      {/* <MenuEditDeleteView
                          Edit={() => {
                            return 0;
                          }}
                          // Delete={handleClickDelete}
                        /> */}
                    </Stack>
                      <Button fullWidth variant="contained" size="small">
                        Details
                      </Button>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Paper>
        </div>
        {/* <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 2 }}>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="secondary">
                Cancel
              </Button>
              <Button onClick={handleOpen} variant="contained" color="primary">
                Save
              </Button>
            </Stack>
          </Box> */}
      </DescriptiveExamRoot>
      <Popup
        size="md"
        state={popupOpen}
        title="Create Session"
        onClose={togglePopupClose}
        popupContent={<CreateSessionForm />}
      />
      {/* <TemporaryDrawer onClose={toggleDrawerClose} Title="Upload Details" state={drawerOpen} DrawerContent="" /> */}
    </>
  );
}

export default DescriptiveExam;
