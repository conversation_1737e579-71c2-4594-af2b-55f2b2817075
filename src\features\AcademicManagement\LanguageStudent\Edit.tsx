import { CLASS_SELECT, STATUS_SELECT, YEAR_SELECT } from '@/config/Selection';
import { CreateProps } from '@/types/Common';
import { Button, Autocomplete, TextField, Typography, Box, Stack } from '@mui/material';
import React from 'react';

export const EditLanguage = ({ onClose, open }: CreateProps) => {
  return (
    <Box mt={3}>
      <Stack sx={{ height: 'calc(100vh - 150px)' }}>
        <Typography variant="h6" fontSize={14}>
          Select Year
        </Typography>
        <Autocomplete
          defaultValue=""
          fullWidth
          options={YEAR_SELECT}
          renderInput={(params) => <TextField {...params} placeholder="Select" />}
        />
        <Typography mt={2} variant="h6" fontSize={14}>
          Class
        </Typography>
        <Autocomplete
          defaultValue=""
          fullWidth
          options={CLASS_SELECT}
          renderInput={(params) => <TextField {...params} placeholder="Select" />}
        />
        <Typography mt={2} variant="h6" fontSize={14}>
          Subject Language
        </Typography>
        <Autocomplete
          defaultValue=""
          fullWidth
          options={YEAR_SELECT}
          renderInput={(params) => <TextField {...params} placeholder="Select" />}
        />

        <Typography mt={2} variant="h6" fontSize={14}>
          Select Student
        </Typography>
        <Autocomplete
          fullWidth
          value=""
          options={STATUS_SELECT}
          renderInput={(params) => <TextField {...params} placeholder="Select status" />}
        />
      </Stack>
      <Box sx={{ justifyContent: 'center', pt: 3, position: 'relative' }}>
        <Stack spacing={2} direction="row" sx={{}}>
          <Button onClick={onClose} fullWidth variant="contained" color="secondary">
            Cancel
          </Button>
          <Button onClick={open} fullWidth variant="contained" color="primary">
            Save
          </Button>
        </Stack>
      </Box>
    </Box>
  );
};
