import { useMemo } from 'react';
import { <PERSON>po<PERSON>, <PERSON>ack, Grid, IconButton, DialogTitle, Box, Dialog, Avatar, Button } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import styled from 'styled-components';
import { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import DialogContent from '@mui/material/DialogContent';
import Excel from '@/assets/attendance/Excel.svg';

export interface DialogTitleProps {
  id: string;
  children?: React.ReactNode;
  onClose: () => void;
}
const BootstrapDialog = styled(Dialog)(({ theme }) => ({
  '& .MuiDialogContent-root': {
    padding: theme.spacing(3, 3, 3, 3),
  },
  '& .MuiDialogActions-root': {
    padding: theme.spacing(1),
  },
  '& .close-button:hover': {
    // transition: '100ms',
    // transform: 'scale(1.1)',
  },
  // '& .MuiDialogTitle-root': {
  //   padding: theme.spacing(1, 3, 1, 3),
  // },
}));

const QuickReportRoot = styled.div``;

export function BootstrapDialogTitle(props: DialogTitleProps) {
  const { children, onClose, ...other } = props;
  return (
    <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }} {...other}>
      <div>{children}</div>
      {onClose ? (
        <IconButton
          className="close-button"
          aria-label="close"
          onClick={onClose}
          sx={{
            // position: 'absolute',
            // right: 8,
            // top: 8,
            color: (theme) => theme.palette.grey[500],
          }}
        >
          <CloseIcon />
        </IconButton>
      ) : null}
    </DialogTitle>
  );
}

export type QuickReportProps = {
  state: boolean;
  onClose?: () => void;
  size?: any;
};

function QuickReport({ onClose, state, size }: QuickReportProps) {
  const quickReportColumns: DataTableColumn<any>[] = useMemo(
    () => [
      //   {
      //     name: 'studentName',
      //     dataKey: 'studentName',
      //     headerLabel: 'Student Name',
      //   },
      //   {
      //     name: 'className',
      //     dataKey: 'className',
      //     headerLabel: 'Class',
      //   },
      //   {
      //     name: 'rollNo',
      //     dataKey: 'rollNo',
      //     headerLabel: 'Roll Number',
      //   },
      {
        name: 'admissionNumber',
        dataKey: 'admissionNumber',
        headerLabel: 'Admission No',
      },
      {
        name: 'admissionDate',
        dataKey: 'admissionDate',
        headerLabel: 'Admission Date',
      },

      {
        name: 'gender',
        dataKey: 'gender',
        headerLabel: 'Gender',
      },
      {
        name: 'dob',
        dataKey: 'dob',
        headerLabel: 'DOB',
      },
      {
        name: 'bloodGroup',
        dataKey: 'bloodGroup',
        headerLabel: 'Blood Group',
      },
      {
        name: 'religion',
        dataKey: 'religion',
        headerLabel: 'Religion',
      },
      {
        name: 'cast',
        dataKey: 'cast',
        headerLabel: 'Cast',
      },
      {
        name: 'nationality',
        dataKey: 'nationality',
        headerLabel: 'Nationality',
      },
      {
        name: 'fatherName',
        dataKey: 'fatherName',
        headerLabel: 'Father Name',
      },
      {
        name: 'fatherOccupation',
        dataKey: 'fatherOccupation',
        headerLabel: 'Father Occupation',
      },
      {
        name: 'fatherMobile',
        dataKey: 'fatherMobile',
        headerLabel: 'Father Mobile',
      },
      {
        name: 'motherName',
        dataKey: 'motherName',
        headerLabel: 'Mother Name',
      },
      {
        name: 'guardianName',
        dataKey: 'guardianName',
        headerLabel: 'Guardian Name',
      },
      {
        name: 'guardianNumber',
        dataKey: 'guardianNumber',
        headerLabel: 'Guardian Number',
      },
      {
        name: 'currentAddress',
        dataKey: 'currentAddress',
        headerLabel: 'Current Address',
      },
      {
        name: 'emailID',
        dataKey: 'emailID',
        headerLabel: 'Email ID',
      },
      {
        name: 'annualIncome',
        dataKey: 'annualIncome',
        headerLabel: 'Annual Income',
      },
      {
        name: 'studentLastStudied',
        dataKey: 'studentLastStudied',
        headerLabel: 'Student Last Studied',
      },
    ],
    []
  );

  return (
    <div>
      <BootstrapDialog
        onClose={onClose}
        fullWidth
        maxWidth={size}
        aria-labelledby="customized-dialog-title"
        open={state}
      >
        <DialogContent dividers sx={{ width: { xs: '550px', sm: '100%' } }}>
          <Box sx={{ border: '1px solid grey', p: 2 }}>
            <Stack direction="row" justifyContent="center">
              <Typography variant="h6" color="primary">
                STUDENT DETAILS
              </Typography>
            </Stack>
            <Stack my={4} direction="row" alignItems="center" justifyContent="space-between">
              <Grid container>
                <Grid item lg={4} xs={6}>
                  <Typography variant="subtitle1" fontSize={13} mb={1}>
                    Student Name
                  </Typography>
                  <Typography variant="subtitle1" fontSize={13} mb={1}>
                    Class
                  </Typography>
                  <Typography variant="subtitle1" fontSize={13} mb={1}>
                    Roll Number
                  </Typography>
                </Grid>
                <Grid item lg={8} xs={6}>
                  <Typography variant="subtitle1" fontSize={13} mb={1}>
                    :
                  </Typography>
                  <Typography variant="subtitle1" fontSize={13} mb={1}>
                    :
                  </Typography>
                  <Typography variant="subtitle1" fontSize={13} mb={1}>
                    :
                  </Typography>
                </Grid>
              </Grid>
              <Stack>
                <Avatar alt="" src="" variant="square" sx={{ width: '130px', height: '130px', borderRadius: 0.5 }} />
              </Stack>
            </Stack>
            {quickReportColumns.map((item, index) => (
              <Stack direction="row" key={index}>
                <Grid container mb={2}>
                  <Grid item lg={4} xs={5}>
                    <Typography key={item.name} variant="subtitle1" fontSize={13} mr={2}>
                      {item.headerLabel}
                    </Typography>
                  </Grid>
                  <Grid item lg={8} xs={7} mb={0.5} mt="auto">
                    <Typography variant="h6" fontSize={13}>
                      {item.dataKey
                        ? `:${' '}${(item as { [key: string]: any })[item.dataKey ?? '']}`
                        : item && item.renderCell && item.renderCell(item, index)}
                    </Typography>
                  </Grid>
                </Grid>
              </Stack>
            ))}
          </Box>
          <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, mt: 3 }}>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="success" sx={{ gap: 1 }} size="medium">
                <img className="icon" alt="Excel" src={Excel} />
                <Typography color="white" fontSize={12}>
                  Download
                </Typography>
              </Button>
              <Button variant="contained" color="primary">
                Print
              </Button>
            </Stack>
          </Box>
        </DialogContent>
      </BootstrapDialog>
    </div>
  );
}

export default QuickReport;
