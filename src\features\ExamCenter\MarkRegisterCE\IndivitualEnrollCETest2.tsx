/* eslint-disable react/self-closing-comp */
import styled from 'styled-components';
import Typography from '@mui/material/Typography';
import { Swiper, SwiperSlide } from 'swiper/react';
import KeyboardArrowLeftIcon from '@mui/icons-material/KeyboardArrowLeft';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import { Avatar, Stack, Box, Button, Chip, TextField } from '@mui/material';
import 'swiper/css';
import 'swiper/css/effect-coverflow';
import 'swiper/css/pagination';
import 'swiper/css/navigation';
import { Pagination, Navigation } from 'swiper';
import React, { useEffect, useRef } from 'react';
import { MarkRegisterCEDataType } from '@/types/ExamCenter';

const IndivitualEnrollCERoot = styled.div<{ theme: any }>`
  padding: 1;
  .swiper_container {
    position: relative;
    padding: 1rem;
  }
  .swiper-slide {
    position: relative;
    /* padding: 1rem 0rem; */
  }
  .swiper-button-next,
  .swiper-button-prev {
    position: absolute;
    top: var(--swiper-navigation-top-offset, 50%);
    margin-top: calc(0px - (var(--swiper-navigation-size) / 2));
    z-index: 10;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .events-card {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[800]};
    border: 1px solid
      ${(props) => (props.theme.themeMode === 'light' ? props.theme.palette.grey[300] : props.theme.palette.grey[900])};
  }
  .swiper-button-prev,
  .swiper-button-next {
    height: 30px;
    width: 30px;
    color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.primary.main : props.theme.palette.primary.main};
  }
`;

interface IndivitualEnrollCEProps {
  data?: MarkRegisterCEDataType[];
  setData: React.Dispatch<React.SetStateAction<MarkRegisterCEDataType[]>>;
  OnClose: () => void;
  sendMarkRegisterData: (data: any, mode: 'single' | 'multiple') => void;
  passMark?: string;
  outOfMark?: string;
  passMarkCE?: string;
  outOfMarkCE?: string;
}

const IndivitualEnrollCE = ({
  data,
  setData,
  OnClose,
  sendMarkRegisterData,
  passMark,
  outOfMark,
  passMarkCE,
  outOfMarkCE,
}: IndivitualEnrollCEProps) => {
  // useEffect(() => {
  //   if (dashboardEventsListStatus === 'idle') {
  //     dispatch(fetchDashboardEvents(adminId));
  //   }
  // }, [dispatch, dashboardEventsListStatus, adminId]);
  const swiperRef = useRef<import('swiper').Swiper | null>(null);
  const textFieldRefs = useRef<(HTMLInputElement | null)[][]>([]);

  useEffect(() => {
    textFieldRefs.current[0][0]?.focus();
  }, []);

  const handleKeyPress = (event: React.KeyboardEvent<HTMLDivElement>, index: number) => {
    if (event.key === 'Enter') {
      swiperRef.current?.slideNext();
      sendMarkRegisterData(data, 'single');
      setTimeout(() => {
        textFieldRefs.current[index + 1][0]?.focus();
      }, 700);
    }
  };

  return (
    <IndivitualEnrollCERoot>
      <Swiper
        spaceBetween={16}
        effect="coverflow"
        grabCursor
        breakpoints={{
          299: {
            slidesPerView: 1,
          },
          499: {
            slidesPerView: 1,
          },
          999: {
            slidesPerView: 1,
          },
          1599: {
            slidesPerView: 1,
          },
        }}
        coverflowEffect={{
          rotate: 0,
          depth: 0,
          stretch: 0,
        }}
        pagination={{ el: '.swiper-pagination', clickable: true }}
        navigation={{
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev',
          // clickable: true,
        }}
        modules={[Pagination, Navigation]}
        className="swiper_container "
        onSwiper={(swiper) => {
          swiperRef.current = swiper;
        }}
      >
        {/* // dashboardEventsListStatus === 'loading'
          //   ? [1, 2, 3].map((event) => (
          //       <SwiperSlide key={event} className="swiper-slide ">
          //         <Card sx={{ boxShadow: '0' }} className="events-card">
          //           <Skeleton sx={{ height: 170 }} animation="wave" variant="rectangular" />
          //           <CardContent>
          //             <Skeleton animation="wave" height={10} style={{ mb: 10, mt: 10 }} />
          //             <Skeleton animation="wave" height={10} width="80%" />
          //             <Stack direction="row" display="flex" alignItems="center" justifyContent="space-between">
          //               <Box display="flex" alignItems="center">
          //                 <Skeleton animation="wave" height={10} width="80%" />
          //               </Box>
          //               <Box display="flex" alignItems="center">
          //                 <Skeleton animation="wave" height={10} width="80%" />
          //               </Box>
          //             </Stack>
          //           </CardContent>
          //         </Card>
          //       </SwiperSlide>
          //     ))
          //   : */}
        {data?.map((row, index: number) => (
          <SwiperSlide key={row.studentId} className="swiper-slide ">
            <Box display="flex" flexDirection="column" textAlign="center" gap={1}>
              <Stack direction="row" justifyContent="center" gap={2} alignItems="center">
                <Box
                  // border={2}
                  borderRadius={10}
                  p={1.5}
                  sx={{
                    position: 'relative',
                    overflow: 'hidden',
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      borderRadius: 'inherit',
                      padding: '1.3px', // Adjust to control border thickness
                      WebkitMaskComposite: 'xor',
                      maskComposite: 'exclude',
                    },
                  }}
                >
                  <Avatar
                    sx={{
                      width: 100,
                      height: 100,
                      // boxShadow: '0px 0px 15px #fff '
                    }}
                    alt={row.studentName}
                    src={row.studentImage}
                  />
                </Box>
              </Stack>
              <Stack direction="column" alignItems="center" gap={1}>
                <Typography variant="h6" fontSize={20}>
                  {row.studentName}
                </Typography>

                <Stack direction="row" alignItems="center" gap={2}>
                  <Chip
                    size="small"
                    label={row.className}
                    // variant="contained"
                    color="success"
                  />
                  <Chip
                    size="small"
                    label="English"
                    // variant="contained"
                    color="info"
                  />
                </Stack>
                <Stack direction="row" alignItems="center" gap={2}>
                  <Typography variant="subtitle1" fontSize={13}>
                    Pass-Mark&nbsp;(CE&nbsp;): {passMarkCE === '' ? 'N/A' : passMarkCE}
                  </Typography>
                  <Typography variant="subtitle1" fontSize={13}>
                    Out-of-Mark(CE): {outOfMarkCE === '' ? 'N/A' : outOfMarkCE}
                  </Typography>
                </Stack>
                <Stack direction="row" alignItems="center" gap={2}>
                  <Typography variant="subtitle1" fontSize={13}>
                    Pass-Mark(TE): {passMark === '' ? 'N/A' : passMark}
                  </Typography>
                  <Typography variant="subtitle1" fontSize={13}>
                    Out-of-Mark(TE): {outOfMark === '' ? 'N/A' : outOfMark}
                  </Typography>
                </Stack>
                <Stack direction="row" alignItems="center" gap={2}>
                  <Box display="flex" alignItems="center" gap={2}>
                    <Typography variant="subtitle2" fontSize={12}>
                      PT:
                    </Typography>
                    <TextField
                      sx={{ maxWidth: '150px' }}
                      inputRef={(el) => {
                        if (!textFieldRefs.current[index]) {
                          textFieldRefs.current[index] = [];
                        }
                        textFieldRefs.current[index][0] = el;
                      }}
                      onKeyPress={(event) => {
                        if (event.key === 'Enter') {
                          event.preventDefault();
                          textFieldRefs.current[index][1]?.focus();
                        }
                      }}
                      value={row.periodicTest !== 'N' ? row.periodicTest : null}
                      onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                        const updatedData = data.map((item) => {
                          if (item.studentId === row.studentId) {
                            return { ...item, periodicTest: event.target.value };
                          }
                          return item;
                        });
                        setData(updatedData);
                      }}
                    />
                  </Box>
                  <Box display="flex" alignItems="center" gap={2}>
                    <Typography variant="subtitle2" fontSize={12}>
                      MA:
                    </Typography>
                    <TextField
                      sx={{ maxWidth: '150px' }}
                      inputRef={(el) => {
                        if (!textFieldRefs.current[index]) {
                          textFieldRefs.current[index] = [];
                        }
                        textFieldRefs.current[index][1] = el;
                      }}
                      onKeyPress={(event) => {
                        if (event.key === 'Enter') {
                          event.preventDefault();
                          textFieldRefs.current[index][2]?.focus();
                        }
                      }}
                      value={row.multipleAssesment !== 'N' ? row.multipleAssesment : null}
                      onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                        const updatedData = data.map((item) => {
                          if (item.studentId === row.studentId) {
                            return { ...item, multipleAssesment: event.target.value };
                          }
                          return item;
                        });
                        setData(updatedData);
                      }}
                    />
                  </Box>
                  <Box display="flex" alignItems="center" gap={2}>
                    <Typography variant="subtitle2" fontSize={12}>
                      PO:
                    </Typography>
                    <TextField
                      sx={{ maxWidth: '150px' }}
                      inputRef={(el) => {
                        if (!textFieldRefs.current[index]) {
                          textFieldRefs.current[index] = [];
                        }
                        textFieldRefs.current[index][2] = el;
                      }}
                      onKeyPress={(event) => {
                        if (event.key === 'Enter') {
                          event.preventDefault();
                          textFieldRefs.current[index][3]?.focus();
                        }
                      }}
                      value={row.portFolio !== 'N' ? row.portFolio : null}
                      onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                        const updatedData = data.map((item) => {
                          if (item.studentId === row.studentId) {
                            return { ...item, portFolio: event.target.value };
                          }
                          return item;
                        });
                        setData(updatedData);
                      }}
                    />
                  </Box>
                  <Box display="flex" alignItems="center" gap={2}>
                    <Typography variant="subtitle2" fontSize={12}>
                      SE:
                    </Typography>
                    <TextField
                      sx={{ maxWidth: '150px' }}
                      inputRef={(el) => {
                        if (!textFieldRefs.current[index]) {
                          textFieldRefs.current[index] = [];
                        }
                        textFieldRefs.current[index][3] = el;
                      }}
                      onKeyPress={(event) => {
                        if (event.key === 'Enter') {
                          event.preventDefault();
                          textFieldRefs.current[index][4]?.focus();
                        }
                      }}
                      value={row.subjectEnrichment !== 'N' ? row.subjectEnrichment : null}
                      onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                        const updatedData = data.map((item) => {
                          if (item.studentId === row.studentId) {
                            return { ...item, subjectEnrichment: event.target.value };
                          }
                          return item;
                        });
                        setData(updatedData);
                      }}
                    />
                  </Box>
                </Stack>
                <Box display="flex" alignItems="center" gap={2}>
                  <Typography variant="subtitle2" fontSize={12}>
                    Enter Mark :
                  </Typography>
                  <TextField
                    sx={{ maxWidth: '150px' }}
                    inputRef={(el) => {
                      if (!textFieldRefs.current[index]) {
                        textFieldRefs.current[index] = [];
                      }
                      textFieldRefs.current[index][4] = el;
                    }}
                    onKeyPress={(event) => handleKeyPress(event, index)}
                    value={row.scoredMarkTheory !== 'N' ? row.scoredMarkTheory : null}
                    onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                      const updatedData = data.map((item) => {
                        if (item.studentId === row.studentId) {
                          return { ...item, scoredMarkTheory: event.target.value };
                        }
                        return item;
                      });
                      setData(updatedData);
                    }}
                  />
                </Box>
                <Stack direction="row" alignItems="center" gap={2}>
                  <Button
                    variant="outlined"
                    color="primary"
                    onClick={() => {
                      OnClose();
                      sendMarkRegisterData(data, 'multiple');
                    }}
                  >
                    Update
                  </Button>
                  {index !== data.length - 1 && (
                    <Button
                      variant="contained"
                      color="primary"
                      className="slider-controler"
                      onClick={() => {
                        swiperRef.current?.slideNext();
                        sendMarkRegisterData(data, 'single');
                        setTimeout(() => {
                          textFieldRefs.current[index + 1][0]?.focus();
                        }, 500);
                      }}
                    >
                      Update & Next
                    </Button>
                  )}
                </Stack>
              </Stack>
            </Box>
          </SwiperSlide>
        ))}
        <div className="slider-controler d-none d-sm-flex">
          <div>
            <KeyboardArrowLeftIcon className="swiper-button-prev slider-arrow border-0 card shadow rounded-circle  " />
          </div>
          <div>
            <KeyboardArrowRightIcon className="swiper-button-next slider-arrow border-0 card shadow rounded-circle " />
          </div>
        </div>
      </Swiper>
    </IndivitualEnrollCERoot>
  );
};

export default IndivitualEnrollCE;
