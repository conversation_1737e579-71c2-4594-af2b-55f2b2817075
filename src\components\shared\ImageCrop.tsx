import React, { useState } from 'react';
import {
  Button,
  Grid,
  IconButton,
  Card,
  CardMedia,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
} from '@mui/material';
import { Edit as EditIcon } from '@mui/icons-material';
import AdvancedCropper from 'react-advanced-cropper';
import 'react-advanced-cropper/dist/style.css';

const ImageC = () => {
  const [images, setImages] = useState<any[]>([]);
  const [open, setOpen] = useState(false);
  const [currentImage, setCurrentImage] = useState<any>(null);
  const [croppedImage, setCroppedImage] = useState<string | null>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files[0]) {
      const file = files[0];
      const reader = new FileReader();
      reader.onload = (event) => {
        setCurrentImage({
          file,
          src: event.target?.result as string,
        });
        setOpen(true);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSaveCroppedImage = () => {
    if (croppedImage) {
      setImages([...images, { src: croppedImage }]);
      setOpen(false);
    }
  };

  const handleEditImage = (index: number) => {
    const imageToEdit = images[index];
    setCurrentImage({
      src: imageToEdit.src,
      file: null, // no need for file on edit
    });
    setOpen(true);
  };

  const handleCrop = (cropper: any) => {
    cropper.getCroppedCanvas().toBlob((blob: Blob) => {
      const url = URL.createObjectURL(blob);
      setCroppedImage(url);
    });
  };

  return (
    <div>
      {/* File Input */}
      <input accept="image/*" type="file" onChange={handleFileChange} style={{ marginBottom: '20px' }} />

      {/* Cropping Dialog */}
      <Dialog open={open} onClose={() => setOpen(false)}>
        <DialogTitle>Crop Image</DialogTitle>
        <DialogContent>
          {currentImage && <AdvancedCropper src={currentImage.src} onCrop={handleCrop} aspectRatio={1} />}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpen(false)} color="primary">
            Cancel
          </Button>
          <Button onClick={handleSaveCroppedImage} color="primary">
            Save
          </Button>
        </DialogActions>
      </Dialog>

      {/* Display Cropped Images Grid */}
      <Grid container spacing={2}>
        {images.map((image, index) => (
          <Grid item key={index} xs={4}>
            <Card>
              <CardMedia component="img" height="200" image={image.src} alt={`cropped-image-${index}`} />
              <IconButton
                color="primary"
                onClick={() => handleEditImage(index)}
                style={{ position: 'absolute', top: '10px', right: '10px' }}
              >
                <EditIcon />
              </IconButton>
            </Card>
          </Grid>
        ))}
      </Grid>
    </div>
  );
};

export default ImageC;
