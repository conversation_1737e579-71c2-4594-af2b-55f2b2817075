import React from 'react';
import { Link, useLocation, useOutletContext } from 'react-router-dom';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';
import styled from 'styled-components';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import Page from '@/components/shared/Page';
import Groups from '@/features/ManageGroups/Groups/Groups';
import StudentMembers from '@/features/ManageGroups/StudentMembers/StudentMembers';
import StaffMembers from '@/features/ManageGroups/StaffMember/StaffMember';
import PublicMembers from '@/features/ManageGroups/PublicMembers/PublicMembers';
import ScheduleList from '@/features/LiveClass/ScheduleList/ScheduleList';
import LiveClassList from '@/features/LiveClass/LiveClassList/LiveClassList';
import ZoomKeyList from '@/features/LiveClass/ZoomKeyList/ZoomKeyList';
import AssignmentList from '@/features/Assignment/AssignmentList/AssignmentList';
import MapToProgressCard from '@/features/Assignment/MapToProgressCard';
import { useDispatch } from 'react-redux';
import { flushStore } from '@/store/flush.slice';
import { OutletContextType } from '@/types/Common';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const AssignmentRoot = styled.div`
  min-height: calc(100vh - ${TOP_BAR_HEIGHT});

  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};

  .tab {
    background-color: ${(props) => (props.theme.themeMode === 'light' ? '#fff' : props.theme.palette.grey[800])};
    /* @media screen and (min-width: 1414px) {
      width: 100%;
    }
    @media screen and (max-width: 720px) {
    } */
    width: 100%;
  }
  .Mui-selected {
    color: ${(props) => props.theme.palette.primary.main};
  }
`;

function TabPanel({ children, value, index }: TabPanelProps) {
  return (
    <Box
      sx={{ paddingTop: '3rem' }}
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
    >
      {value === index && <Box>{children}</Box>}
    </Box>
  );
}
function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`,
  };
}

function Assignment() {
  const { topBarHeight } = useOutletContext<OutletContextType>();
  const location = useLocation();
  const [value, setValue] = React.useState(0);

  const dispatch = useDispatch();

  React.useEffect(() => {
    dispatch(flushStore());
    console.log('Flush Store');
  }, [dispatch, value]);

  React.useEffect(() => {
    const currentPath = location.pathname;
    const routePaths = ['/assignment/assignment-list', '/assignment/map-progress-card', '/live-class/zoom-key-list'];
    const index = routePaths.indexOf(currentPath);
    if (index !== -1) {
      setValue(index);
    }
  }, [location]);

  return (
    <Page title="">
      <AssignmentRoot>
        <Tabs
          className="tab"
          variant="scrollable"
          scrollButtons
          allowScrollButtonsMobile
          value={value}
          aria-label="basic tabs"
          sx={{
            ml: '0px',
            borderBottom: 1,
            borderColor: 'divider',
            position: 'fixed',
            top: `${topBarHeight}px`,
            zIndex: 1,
          }}
        >
          <Tab {...a11yProps(0)} label="Assignment List" component={Link} to="/assignment/assignment-list" />
          <Tab {...a11yProps(1)} label="Map To Progress Card" component={Link} to="/assignment/map-progress-card" />
          <Tab {...a11yProps(2)} label="" component={Link} to="/live-class/zoom-key-list" />
        </Tabs>

        <TabPanel value={value} index={0}>
          <AssignmentList />
        </TabPanel>
        <TabPanel value={value} index={1}>
          <MapToProgressCard />
        </TabPanel>
        <TabPanel value={value} index={2}>
          <ZoomKeyList />
        </TabPanel>
      </AssignmentRoot>
    </Page>
  );
}

export default Assignment;
