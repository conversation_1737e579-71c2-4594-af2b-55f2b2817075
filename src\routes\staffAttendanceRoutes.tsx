import Loadable from '@/components/shared/Loadable';
import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';

const StaffAttendance = Loadable(lazy(() => import('@/pages/StaffAttendance')));
const LeaveList = Loadable(lazy(() => import('@/features/StaffAttendance/LeaveList')));
const StaffWorkingDayList = Loadable(lazy(() => import('@/features/StaffAttendance/StaffWorkingDayList')));
const StaffPunchTimeSet = Loadable(lazy(() => import('@/features/StaffAttendance/StaffPunchTimeSet')));
const PunchList = Loadable(lazy(() => import('@/features/StaffAttendance/PunchList/PunchList')));
const PunchReport = Loadable(lazy(() => import('@/features/StaffAttendance/PunchReport')));

export const staffAttendanceRoutes: RouteObject[] = [
  {
    path: '/staff-attendance',
    element: <StaffAttendance />,
    children: [
      {
        path: 'leave-list',
        element: <LeaveList />,
      },
      {
        path: 'working-day-list',
        element: <StaffWorkingDayList />,
      },
      {
        path: 'punch-time-set',
        element: <StaffPunchTimeSet />,
      },
      {
        path: 'punch-list',
        element: <PunchList />,
      },
      {
        path: 'punch-report',
        element: <PunchReport />,
      },
    ],
  },
];
