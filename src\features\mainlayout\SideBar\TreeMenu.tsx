// import TreeItem from '@mui/lab/TreeItem';
// import TreeView from '@mui/lab/TreeView';
// import { Typography } from '@mui/material';
// import { ComponentProps, useCallback } from 'react';
// import styled from 'styled-components';
// import { TreeMenuItem } from '@/types/Layout';
// import { SideBarMenuData } from '@/config/sideBarMenu';
// import { Link } from 'react-router-dom';
// import { TreeIconMap } from '@/config/TreeIconMap';

// const CustomTreeItem = styled(TreeItem)`
//   margin-top: 2px;
//   .MuiTreeItem-content {
//     background-color: ${(props) =>
//       props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.grey[800]};
//     width: 100%;
//     padding: 0;
//     padding-left: 0.625rem;
//   }
//   .MuiTreeItem-content:hover {
//     color: #f85a00;
//   }
//   .Mui-selected {
//     color: #f85a00;
//     border-right: 3px solid #f85a00;
//     border-top-right-radius: 2px;
//     border-bottom-right-radius: 2px;
//   }

//   background-color: ${(props) =>
//     props.theme.themeMode === 'light' ? props.theme.palette.grey[200] : props.theme.palette.grey[900]};
// `;

// const CustomTreeLabel = styled.div`
//   display: flex;
//   align-items: center;

//   a {
//     display: flex;
//     align-items: center;
//     width: 100%;
//     color: inherit;
//     text-decoration: none;
//   }
//   a:hover {
//     color: #f85a00;
//   }

//   .labelIcon {
//     margin-right: ${(props) => props.theme.spacing(2)};
//     font-size: 25px;
//   }

//   .labelText {
//     font-weight: 600;
//     /* font-size: 13px; */
//     width: 100%;
//   }
// `;

// export type StyledTreeItemProps = ComponentProps<typeof TreeItem> & {
//   labelText: string;
//   labelIcon?: any;
//   link?: string;
//   onNodeClick: (node: TreeMenuItem) => void;
// };

// function StyledTreeItem(props: StyledTreeItemProps) {
//   const { nodeId, labelText, labelIcon: Icon, link, onNodeClick } = props;

//   return (
//     <CustomTreeItem
//       className=""
//       nodeId={nodeId}
//       label={
//         <CustomTreeLabel
//           onClick={() => {
//             onNodeClick({ id: nodeId, label: labelText, icon: Icon, link });
//           }}
//         >
//           {link ? (
//             <Link to={link} className="p-2">
//               {!!Icon && <Icon color="action" className="labelIcon  " />}
//               <Typography variant="body2" sx={{ fontSize: { xs: '15px', md: '13px' } }} className="labelText">
//                 {labelText}
//               </Typography>
//             </Link>
//           ) : null}
//         </CustomTreeLabel>
//       }
//     />
//   );
// }

// export type TreeBranchProps = {
//   node: TreeMenuItem;
//   onNodeClick: (node: TreeMenuItem) => void;
// };

// function TreeBranch({ node, onNodeClick }: TreeBranchProps) {
//   const labelIcon = node.icon ? TreeIconMap[node.icon] : null;
//   return (
//     <StyledTreeItem
//       key={node.id}
//       nodeId={node.id}
//       labelText={node.label}
//       link={node.link}
//       labelIcon={labelIcon}
//       onNodeClick={onNodeClick}
//     >
//       {Array.isArray(node.children)
//         ? node.children.map((n: TreeMenuItem) => <TreeBranch key={n.id} node={n} onNodeClick={onNodeClick} />)
//         : null}
//     </StyledTreeItem>
//   );
// }

// export type TreeMenuProps = {
//   onNodeClick: (node: TreeMenuItem) => void;
// };

// function TreeMenu({ onNodeClick }: TreeMenuProps) {
//   const handleLinkClick = useCallback(
//     (node: TreeMenuItem) => {
//       onNodeClick(node);
//     },
//     [onNodeClick]
//   );

//   return (
//     <TreeView defaultEndIcon={null}>
//       {SideBarMenuData.map((node) => (
//         <TreeBranch node={node} key={node.id} onNodeClick={handleLinkClick} />
//       ))}
//     </TreeView>
//   );
// }

// export default TreeMenu;
