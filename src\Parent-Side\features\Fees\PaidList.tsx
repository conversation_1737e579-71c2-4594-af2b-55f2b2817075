/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  Avatar,
  Chip,
} from '@mui/material';
import styled from 'styled-components';
import { CLASS_SELECT, YEAR_SELECT } from '@/config/Selection';
import Popup from '@/components/shared/Popup/Popup';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import CheckCircle from '@mui/icons-material/CheckCircle';
import { useTheme } from '@mui/material';
import useSettings from '@/hooks/useSettings';
import MenuListComposition from '@/Parent-Side/component/Menu';

const PaidListRoot = styled.div`
  padding: 1rem;
  @media screen and (max-width: 420px) {
    padding: 0rem;
  }

  .Card {
    min-height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }
  }
  .TableCard {
    margin-top: 1rem;
    padding: 0.5rem 1rem;
    /* background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.grey[200] : props.theme.palette.grey[900]}; */
  }
`;

export default function PaidList() {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const [Delete, setDelete] = React.useState(false);

  const handleClickCloseDelete = () => setDelete(false);

  return (
    <Page title="Paid List">
      <PaidListRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Box display="flex" justifyContent="space-between">
            <Typography variant="h6" fontSize={18}>
              Paid List
            </Typography>
            <MenuListComposition />
          </Box>
          <Divider />
          {[1, 2].map((item) => (
            <Card key={item} className="TableCard">
              <Stack direction="row" justifyContent="space-between">
                <Stack spacing={1}>
                  <Typography variant="h6" fontSize={16} sx={{ mt: 1 }}>
                    Carol Bradley
                  </Typography>
                  <Typography fontSize={14} sx={{ mt: 1, color: theme.palette.primary.light }}>
                    Payment Mode: Bank
                  </Typography>
                  <Typography color="error" fontSize={14} sx={{ mt: 1 }}>
                    Balance Amount : 8000
                  </Typography>
                </Stack>
                <Stack spacing={1} textAlign="right">
                  <Chip
                    label={`Roll No : ${item}`}
                    size="small"
                    variant="filled"
                    color="primary"
                    sx={{ width: 'fit-content', mr: 0, ml: 'auto' }}
                  />
                  <Typography variant="h6" fontSize={19} color="green" sx={{ mt: 1 }}>
                    ₹ 10, 000 <CheckCircle />
                  </Typography>
                  <Typography color="secondary" fontSize={12} sx={{ mt: 1 }}>
                    18 Mar 2024, 10:10 AM
                  </Typography>
                </Stack>
              </Stack>
            </Card>
          ))}
          <Typography variant="h6" color="GrayText" fontSize={16} sx={{ mt: 3, ml: 2 }}>
            Yesterday
          </Typography>
          <Divider />
          {[1, 2, 3, 4, 5, 6].map((item) => (
            <Card key={item} className="TableCard">
              <Stack direction="row" justifyContent="space-between">
                <Stack spacing={1}>
                  <Typography variant="h6" fontSize={16} sx={{ mt: 1 }}>
                    Carol Bradley
                  </Typography>
                  <Typography fontSize={14} sx={{ mt: 1 }}>
                    Payment Mode: Bank
                  </Typography>
                  <Typography color="GrayText" fontSize={14} sx={{ mt: 1 }}>
                    Balance Amount : Fully Paid
                  </Typography>
                </Stack>
                <Stack spacing={1} textAlign="right" mt={1}>
                  <Chip
                    label={`Roll No : ${item}`}
                    size="small"
                    color="secondary"
                    sx={{ width: '100px', mr: 0, ml: 'auto' }}
                  />
                  <Typography variant="h6" fontSize={19} color="green" sx={{ mt: 1 }}>
                    ₹ 10, 000 <CheckCircle />
                  </Typography>
                  <Typography color="secondary" fontSize={12} sx={{ mt: 1 }}>
                    18 Mar 2024, 10:10 AM
                  </Typography>
                </Stack>
              </Stack>
            </Card>
          ))}
        </Card>
      </PaidListRoot>

      {/* <Popup size="md" state={popup1} onClose={handleClickCloseReceipt1} popupContent={<Receipt />} /> */}
      {/* <Popup size="md" state={popup2} onClose={handleClickCloseReceipt2} popupContent={<ConReceipt />} /> */}
      <Popup
        size="xs"
        state={Delete}
        onClose={handleClickCloseDelete}
        popupContent={<DeleteMessage message="Are you sure want to delete?" />}
      />
    </Page>
  );
}
