/* eslint-disable prettier/prettier */
// function path(root: string, subPath: string) {
//   return `${root}${subPath}`;
// }

const API_BASE_URL = import.meta.env.VITE_API_BASEURL;

const urls = {
  BaseUrl: API_BASE_URL,
  LoginUrl: '/login',
  LoginUrlNew: '/login/loginnew',
  VerifyTokenUrl: '/token/verify',
  RenewAccessToken: '/token/renew',

  //  Dashboard Api
  DashboardStats: '/api/dashboard/{adminId}/stats',
  DashboardEvents: '/api/dashboard/{adminId}/events',
  DashboardBday: '/api/dashboard/{adminId}/birthday',
  DashboardFeeChart: '/api/dashboard/{adminId}/{academicId}/{classId}/feechart',
  DashboardTimetable: '/api/dashboard/{adminId}/{classId}/timetable',
  DashboardAttendance: '/api/dashboard/{adminId}/{classId}/attendance',
  DashboardVideos: '/api/dashboard/{adminId}/{classId}/videos',
  ClassList: '/api/dashboard/{adminId}/class',
  YearList: '/api/dashboard/{adminId}/year',

  // Attendance Api
  GetAttendanceList: '/api/attendance/attendancefilter',
  MarkAbsentees: '/api/attendance/markabsentees',
  MarkPresent: '/api/attendance/{adminId}/{classId}/{studentId}/{attendanceId}/markpresent',
  GetLeaveNoteList: '/api/attendance/studentleavelistpaged',
  ApproveLeave: '/api/attendance/leaveapprove',
  RejectLeave: '/api/attendance/leavereject',
  GetCalendarReport: '/api/attendance/attendancecalendarreport',

  //  Student Enquiry List
  GetStudentEnquiryList: '/api/attendance/studentenquirylistpaged',
  SendEnquiryReply: '/api/attendance/enquiryreply',
  GetAttendanceSummaryReport: '/api/attendance/attendancesummaryreport',

  //  MessageBox Api
  GetMessageTempList: '/api/message/list',
  FetchMessageTemplate: '/api/message/{messageId}/get',
  CreateMessageTemplate: '/api/message/new',
  EditMessageTemplate: '/api/message/update',
  DeleteMessageTemplate: '/api/message/{messageId}/delete',
  DeleteMultipleMessageTemplate: '/api/message/Delete',
  GetParentsList: '/api/message/{adminId}/{academicId}/{classId}/parent',
  SendToParents: '/api/message/SendToParents',
  SendToParentsList: '/api/message/ToParents',
  GetStaffList: '/api/message/staff',
  SendToStaffList: '/api/message/ToStaff',
  GetPtaList: '/api/message/pta',
  SendToPtaList: '/api/message/ToPta',
  GetConveyorList: '/api/message/{classId}/conveyor',
  SendToConveyorList: '/api/message/ToConveyor',
  GetGroupsList: '/api/message/{adminId}/{academicId}/groupfilter',
  GetGroupMembersList: '/api/message/{adminId}/{academicId}/{groupId}/groupStudents',
  SendToGroupMembersList: '/api/message/ToGroup',
  GetClassDivision: '/api/message/{adminId}/{academicId}/classdivision',
  SendToClassDivision: '/api/message/toclassdivision',
  GetClassSection: '/api/message/{adminId}/{academicId}/classsection',
  SendToClassSection: '/api/message/toclasssection',
  GetGroupWise: '/api/message/{adminId}/{academicId}/studentgroupwise',
  SendToGroupWise: '/api/message/tostudentgroupwise',
  GetPublicGroupList: '/api/message/{adminId}/{academicId}/publicgroupfilter',
  GetPublicGroupMembersList: '/api/message/{adminId}/{academicId}/{groupId}/publicgroupmembers',
  SendToPublicGroupMembersList: '/api/message/ToPublicGroupMembers',
  GetPublicGroupWiseList: '/api/message/{adminId}/{academicId}/publicgroupwise',
  SendToPublicGroupWiseList: '/api/message/topublicgroupwise',
  SendToAllParents: '/api/message/{adminId}/{academicId}/{messageId}/allparents',
  SendToAllStaff: '/api/message/{adminId}/{academicId}/{messageId}/allstaff',
  SendToAllPta: '/api/message/{adminId}/{academicId}/{messageId}/allpta',
  SendToAllConveyors: '/api/message/{adminId}/{academicId}/{messageId}/allconveyors',

  //  Notification App Api
  FileUpload: '/api/message/UploadFiles',
  CreateNotification: '/api/Notification/New',
  GetNotificationList: '/api/Notification/List',
  DeleteNotificationList: '/api/Notification/{adminId}/{notificationId}/Delete',
  DeleteMultipleNotificationList: '/api/Notification/Delete',
  NotificationSendToParentsList: '/api/Notification/ToParents',
  NotificationSendToStaffList: '/api/Notification/ToStaff',
  NotificationSendToClassDivision: '/api/Notification/toclassdivision',
  NotificationSendToClassSection: '/api/Notification/toclasssection',
  NotificationSendToAllParents: '/api/Notification/{adminId}/{academicId}/{notificationId}/allparents',
  NotificationSendToAllStaff: '/api/Notification/{adminId}/{academicId}/{notificationId}/allstaff',

  //  Voice Message Api
  VoiceFileUpload: '/api/Voice/UploadVoiceFile',
  CreateVoice: '/api/Voice/New',
  GetVoiceMessageList: '/api/Voice/List',
  DeleteVoiceMessageList: '/api/Voice/{adminId}/{voiceId}/Delete',
  DeleteMultipleVoiceMessageList: '/api/Voice/Delete',
  SendVoiceMessageToParents: '/api/Voice/ToParents',
  SendVoiceMessageToStaff: '/api/Voice/ToStaff',
  SendVoiceMessageToPTA: '/api/Voice/ToPta',
  SendVoiceMessageToConveyor: '/api/Voice/ToConveyor',
  SendVoiceMessageToGroup: '/api/Voice/ToGroup',
  SendVoiceMessageToPublicGroupMembers: '/api/Voice/ToPublicGroupMembers',
  SendVoiceMessageToClassDivision: '/api/Voice/toclassdivision',
  SendVoiceMessageToClassSection: '/api/Voice/toclasssection',
  SendVoiceMessageToGroupWise: '/api/Voice/tostudentgroupwise',
  SendVoiceMessageToPublicGroupWise: '/api/Voice/topublicgroupwise',
  SendVoiceMessageToAllParents: '/api/message/{adminId}/{academicId}/{voiceId}/allparents',
  SendVoiceMessageToAllStaff: '/api/message/{adminId}/{academicId}/{voiceId}/allstaff',
  SendVoiceMessageToAllPta: '/api/message/{adminId}/{academicId}/{voiceId}/allpta',
  SendVoiceMessageToAllConveyors: '/api/message/{adminId}/{academicId}/{voiceId}/allconveyors',

  //  Class Api
  GetClassList: '/classes/paged',
  AddEditClass: '/classes',
  DeleteClass: '/classes/{classId}',
  AddClasses: '/classes/multiple',
  ClassNameExists: '/classes/classname/exists',
  GetClassSortList: '/classes/takeclassforsort',
  UpdateClassSort: '/classes/classsortupdate',

  // Manage Fee Api
  GetTermFee: '/api/termfee/{adminId}/{academicId}/{feeTypeId}/GetBasicFeeSettings',
  GetClassSections: '/api/termfee/{adminId}/{academicId}/TakeSections',
  GetFeeDateSettings: '/api/termfee/{adminId}/{academicId}/{sectionId}/{feeTypeId}/GetTermFeeSettings',
  GetClassListData: '/api/termfee/{adminId}/{academicId}/{sectionId}/TakeClass',
  GetStudentsFeeStatus: '/api/termfee/{adminId}/{academicId}/{sectionId}/{classId}/SearchStudentsFeeStatus',
  GetStudentTermFeeStatus: '/api/termfee/{adminId}/{academicId}/{sectionId}/{classId}/{studentId}/StudentTermFeeStatus',
  GetStudentTermFeeStatusNew:
    '/api/termfee/{adminId}/{academicId}/{sectionId}/{classId}/{studentId}/{feeTypeId}/StudentTermFeeStatusType',
  StudentTermFeePay: '/api/termfee/termfeefinalpay',
  CreateBasicFeeSetting: '/api/termfee/basicfeeamountsetting',
  CreateTermFeeSetting: '/api/termfee/termfeeamountsetting',
  CreateBasicFeeSettingTitle: '/api/termfee/addbasicfeetitle',
  CreateTermFeeSettingTitle: '/api/termfee/addtermfeetitle',
  GetOptionalFeeSettings:
    '/api/termfee/{adminId}/{academicId}/{sectionId}/{classId}/{feeTypeId}/GetOptionalFeeSettings',
  OptionalFeeSettings: '/api/termfee/optionalfeesetting',
  GetScholarshipFeeList: '/api/termfee/{adminId}/{accademicId}/{feeTypeId}/scholarshiplistwithmapped',
  CreateScholarshipFeeSetting: '/api/termfee/scholarshipsetting',
  GetScholarshipSettings:
    '/api/termfee/{adminId}/{academicId}/{sectionId}/{classId}/{feeTypeId}/getscholarshipsettings',
  CreateScholarshipSettings: '/api/termfee/setscholarshipsettings',
  DeleteScholarship: '/api/termfee/scholarshipdrop',
  GetBasicFeeList: '/api/termfee/basicfeelist',
  DeleteBasicFeeList: '/api/termfee/basicfeedrop',
  UpdateBasicFeeList: '/api/termfee/basicfeeupdate',
  GetTermFeeList: '/api/termfee/termfeelist',
  DeleteTermFeeList: '/api/termfee/termfeedrop',
  UpdateTermFeeList: '/api/termfee/termfeeupdate',
  GetFeeOverviewStatus: '/api/termfee/{adminId}/{academicId}/{classId}/{feeTypeId}/feeoverviewstats',
  GetFeeOverviewChart: '/api/termfee/{adminId}/{academicId}/{classId}/{feeTypeId}/feeoverviewchart',
  GetFeeOverviewPaidList: '/api/termfee/feeoverviewpaidlist',
  GetFeeOverviewModeList: '/api/termfee/{adminId}/{academicId}/{classId}/{feeTypeId}/feeoverviewmodelist',
  GetReceiptForPrint: '/api/termfee/{adminId}/{receiptId}/takereceiptforprint',
  GetFeePaidList: '/api/termfee/takefeepaidlist',
  GetStudentsFilter: '/api/termfee/{adminId}/{academicId}/{sectionId}/{classId}/{feeTypeId}/takestudentslistforfilter',
  GetFeePaidBasicList: '/api/termfee/takefeepaidbasiclist',
  GetBasicFeeFilter: '/api/termfee/{adminId}/{academicId}/{feeTypeId}/takebasicfeelistforfilter',
  GetFeePaidTermList: '/api/termfee/takefeepaidtermlist',
  GetTermFeeFilter: '/api/termfee/{adminId}/{academicId}/{feeTypeId}/taketermfeelistforfilter',
  GetFeePendingList: '/api/termfee/takefeependinglist',
  GetFeePendingBasicList: '/api/termfee/takefeependingbasiclist',
  GetFeePendingTermList: '/api/termfee/takefeependingtermlist',
  GetFineList: '/api/termfee/takefinetypelist',
  CreateEditFineList: '/api/termfee/finetypeinsert',
  DeleteFineList: '/api/termfee/finetypedrop',
  GetFineMappingList: '/api/termfee/{adminId}/{academicId}/{feeTypeId}/takefinemappingsetup',
  FineMapInsert: '/api/termfee/finemapinsert',
  DeleteFineMap: '/api/termfee/finemappeddrop',
  DeleteBasicFee: '/api/termfee/basicfeemappeddrop',
  DeleteAllBasicFee: '/api/termfee/basicfeemappedalldrop',
  DeleteTermFee: '/api/termfee/termfeemappeddrop',
  DeleteAllTermFee: '/api/termfee/termfeemappedalldrop',
  ReceiptCancel: '/api/termfee/receiptcancel',
  CheckReceiptNo: '/api/termfee/{receiptNo}/{feeTypeId}/checkreceiptno',
  DeleteOptionalFee: '/api/termfee/optionalmappeddrop',
  DeleteAllOptionalFee: '/api/termfee/optionalmappedstudentdrop',
  DeleteScholarshipMapped: '/api/termfee/scholarshipmappeddrop',
  DeleteAllScholarshipMapped: '/api/termfee/scholarshipmappedstudentdrop',
  GetStopMappingSettings:
    '/api/termfee/{adminId}/{academicId}/{sectionId}/{classId}/{feeTypeId}/GetStopMappingSettings',
  CreateStopMapping: '/api/termfee/StopMapping',
  DeleteBusMapped: '/api/termfee/busmappeddrop',
  DeleteAllBusMappedStudent: '/api/termfee/busmappedstudentdrop',
  ReceiptApprove: '/api/termfee/receiptapprove',
  GetStudentPickerData: '/api/termfee/studentpicker',
  GetOptionalFeeSettingsIndividual:
    '/api/termfee/{adminId}/{academicId}/{sectionId}/{classId}/{feeTypeId}/GetOptionalFeeSettings',
  OptionalFeeSettingsIndividual: '/api/termfee/optionalfeesetting',

  GetExamList: '',
  AddEditExam: '',
  DeleteExam: '',

  // Payment - Parent side
  PaymentCreateOrder: '/api/payment/createorder',
  TransactionResponse: '/api/payment/transactionresponse',
  GetParentPayFee: '/api/payment/{studentId}/{feeTypeId}/parentpayfeeget',
  GetReceiptOnline: '/api/payment/{receiptId}/takereceiptforprint',
  checkLogin: '/api/payment/{adminId}/checklogin',

  //  Subjects Api
  GetSubjectList: '/subject/paged',
  GetSubject: '/subject/{subjectId}',
  UpdateSubject: '/subject/update',
  AddSubject: '/subject/addsinglesubject',
  AddMultipleSubjects: '/subject/addmultiplesubjects',
  DeleteSubject: '/subject/{subjectId}/deletesubject',
  SubjectNameExists: '/subject/subjectname/exists',

  //  Staff Api
  GetStaffDataList: 'api/staff/paged',
  GetStaff: 'api/staff/{staffId}',
  AddStaff: 'api/staff/addsinglestaff',
  AddMultipleStaff: 'api/staff/addmultiplestaff',
  UpdateStaff: 'api/staff/update',
  DeleteStaff: 'api/staff/{staffId}/deletestaff',
  StaffCodeExists: 'api/staff/staffcode/exists',
  GetCTSFilter: 'api/manageacademics/{adminId}/ctsfilter',
  GetCTSList: 'api/manageacademics/ctsDetailsPaged',
  AddCTSAllocationSingle: 'api/manageacademics/CtsAllocationSingle',
  UpdateCTSAllocationSingle: 'api/manageacademics/ctsdetailupdate',
  GetCTSUpdateDetail: 'api/manageacademics/{adminId}/{cteacherId}/takectsdetail',
  GetCTSMapping: 'api/manageacademics/{adminId}/{academicId}/{classId}/takectsmappingsetup',
  AddCTSAllocationMap: 'api/manageacademics/CtsAllocationMultiple',
  GetCTSTeacherWiseMapiing: 'api/manageacademics/{adminId}/{academicId}/{staffId}/takectsmappingsetupteacherwise',
  DeleteCTS: 'api/manageacademics/ctsdrop',

  //  Year Api
  GetYearList: '/api/year/paged',
  GetYear: '/api/year/{academicId}',
  UpdateYear: '/api/year/update',
  AddYear: '/api/year/addsingleyear',
  DeleteYear: '/api/year/{academicId}/deleteyear',
  YearNameExists: '/api/year/acatedmictime/exists',

  //  Section Api
  GetSectionList: '/api/section/paged',
  GetSection: '/api/section/{sectionId}',
  UpdateSection: '/api/section/update',
  AddSection: '/api/section/addsinglesection',
  DeleteSection: '/api/section/{sectionId}/deletesection',
  SectionNameExists: '/api/section/sectionname/exists',

  //  Student Api
  GetStudentList: '/api/student/paged',
  GetStudent: '/api/student/{studentId}',
  UpdateStudent: '/api/student/update',
  AddStudent: '/api/student/addsinglestudent',
  AddmultipleStudent: '/api/student/addmultiplestudent',
  DeleteStudent: '/api/student/{studentId}/deletestudent',
  StudentAdmNumberExists: '/api/student/admissionnumber/exists',
  GetQucickUpdateStudentList: '/api/student/{academicId}/{classId}/takestudentlist',
  QucickUpdate: '/api/student/quickupdate',

  // Exam Center Api
  GetMarkRegisterFilter: '/api/examcenter/{adminId}/markregisterfilter',
  GetMarkRegisterSubjectFilter: '/api/examcenter/{adminId}/{classId}/markregistersubjectfilter',
  GetMarkDetails: '/api/examcenter/{adminId}/{academicId}/{classId}/{examId}/{subjectId}/takemarkdetails',
  AddMarkRegisterCBSE: '/api/examcenter/cbsemarkadd',
  GetMarkDetailswithCE: '/api/examcenter/{adminId}/{academicId}/{classId}/{examId}/{subjectId}/takemarkdetailscbsece',
  AddMarkRegisterCBSEwithCE: '/api/examcenter/cbsecemarkadd',
};

export const AnonymousUrls = [urls.LoginUrl, urls.VerifyTokenUrl];

export default urls;
