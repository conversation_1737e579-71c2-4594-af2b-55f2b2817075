/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  Collapse,
  FormControl,
} from '@mui/material';
import styled from 'styled-components';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import SearchIcon from '@mui/icons-material/Search';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import DeleteIcon from '@mui/icons-material/Delete';
// import ModeEditIcon from '@mui/icons-material/ModeEdit';
import { CLASS_SELECT, YEAR_SELECT } from '@/config/Selection';

import DateSelect from '@/components/shared/Selections/DateSelect';

const SaleListRoot = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 50px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

export const data = [
  {
    SlNo: 1,
    ProductTitle: 'SCHOOL DIARY',
    ProductDescription: 'School Diary 2023-2024',
    LastUpdated: '6/23/2023 3:16:11 PM',
    Status: 1,
  },
  {
    SlNo: 2,
    ProductTitle: 'NOTEBOOK BIG UNRULED',
    ProductDescription: 'Unruled Notebook',
    LastUpdated: '6/23/2023 3:16:39 PM',
    Status: 0,
  },
  {
    SlNo: 3,
    ProductTitle: 'NOTEBOOK SMALL RULED',
    ProductDescription: 'Small Ruled Notebook',
    LastUpdated: '6/23/2023 3:17:10 PM',
    Status: 1,
  },
  {
    SlNo: 4,
    ProductTitle: 'SCHOOL SHOE',
    ProductDescription: 'Black & White Shoes',
    LastUpdated: '6/23/2023 3:17:40 PM',
    Status: 1,
  },
  {
    SlNo: 5,
    ProductTitle: 'TIE',
    ProductDescription: 'Uniform Tie',
    LastUpdated: '6/23/2023 3:17:54 PM',
    Status: 0,
  },
  {
    SlNo: 6,
    ProductTitle: 'SCHOOL UNIFORM',
    ProductDescription: 'School Uniform',
    LastUpdated: '6/23/2023 3:18:13 PM',
    Status: 1,
  },
  {
    SlNo: 7,
    ProductTitle: 'BELT',
    ProductDescription: 'Belt',
    LastUpdated: '6/23/2023 3:18:31 PM',
    Status: 0,
  },
  {
    SlNo: 8,
    ProductTitle: 'PENCIL CASE',
    ProductDescription: 'Hardtop Pencil Case',
    LastUpdated: '6/23/2023 3:19:00 PM',
    Status: 1,
  },
  {
    SlNo: 9,
    ProductTitle: 'SHARPENER',
    ProductDescription: 'Double Hole Pencil Sharpener',
    LastUpdated: '6/23/2023 3:19:20 PM',
    Status: 1,
  },
  {
    SlNo: 10,
    ProductTitle: 'ERASER',
    ProductDescription: 'White Eraser',
    LastUpdated: '6/23/2023 3:19:40 PM',
    Status: 1,
  },
  {
    SlNo: 11,
    ProductTitle: 'RULER',
    ProductDescription: '12 Inch Plastic Ruler',
    LastUpdated: '6/23/2023 3:20:00 PM',
    Status: 0,
  },
  {
    SlNo: 12,
    ProductTitle: 'SCISSORS',
    ProductDescription: '5 Inch Stainless Steel Scissors',
    LastUpdated: '6/23/2023 3:20:20 PM',
    Status: 1,
  },
  {
    SlNo: 13,
    ProductTitle: 'WATER BOTTLE',
    ProductDescription: '1 Liter Plastic Water Bottle',
    LastUpdated: '6/23/2023 3:20:40 PM',
    Status: 1,
  },
];

function SaleList() {
  const [drawerOpen, setDrawerOpen] = React.useState<boolean>(false);
  const [showFilter, setShowFilter] = useState(false);
  // const [showSend, setShowSend] = useState(false);

  const toggleDrawerOpen = () => {
    setDrawerOpen(true);
  };

  const toggleDrawerClose = useCallback(() => setDrawerOpen(false), []);

  const getRowKey = useCallback((row: any) => row.examId, []);
  const SaleListColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'slNo',
        dataKey: 'SlNo',
        headerLabel: 'Sl.No',
        sortable: true,
      },
      {
        name: 'year',
        dataKey: 'AcademicYear',
        headerLabel: 'Academic year',
        renderCell: () => {
          return <Typography>2023-24</Typography>;
        },
      },
      {
        name: 'name',
        dataKey: 'name',
        headerLabel: 'Student',
        renderCell: (item) => {
          return <Typography>Student {item.SlNo}</Typography>;
        },
      },
      {
        name: 'year',
        dataKey: 'year',
        headerLabel: 'Year',
        renderCell: () => {
          return <Typography>2023-24</Typography>;
        },
      },
      {
        name: 'Class',
        dataKey: 'Class',
        headerLabel: 'Class',
        renderCell: () => {
          return <Typography>Test-Class</Typography>;
        },
      },
      {
        name: 'rollNo',
        dataKey: 'SlNo',
        headerLabel: 'Roll No',
      },
      {
        name: 'payment',
        dataKey: 'payment',
        headerLabel: 'Total Payment(INR)',
        renderCell: (item: any) => {
          return <Typography>{1700 * item.SlNo}</Typography>;
        },
      },
      {
        name: 'date',
        dataKey: 'date',
        headerLabel: 'Payment Date',
        renderCell: () => {
          return <Typography>21 August 2023</Typography>;
        },
      },
      {
        name: 'actions',
        headerLabel: 'Options',
        renderCell: () => {
          return (
            <Stack direction="row" gap={1}>
              <IconButton size="small" onClick={toggleDrawerOpen} sx={{ padding: 0.5 }}>
                <DeleteIcon />
              </IconButton>
              <Button size="small" onClick={toggleDrawerOpen} variant="contained" sx={{ padding: 0.5 }}>
                Get Bill
              </Button>
            </Stack>
          );
        },
      },
    ],
    []
  );

  return (
    <Page title="List">
      <SaleListRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: 2 }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={19}>
              Product Sales List
            </Typography>
            <Box sx={{ flexShrink: 0 }}>
              <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                <SearchIcon />
              </IconButton>
            </Box>
          </Stack>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={3} alignItems="end">
                  <Grid item md={2} xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%' } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Autocomplete
                        options={YEAR_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select Year" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item md={2} xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%' } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Autocomplete
                        options={CLASS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select Class" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item md={2} xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%' } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Student
                      </Typography>
                      <Autocomplete
                        options={[]}
                        renderInput={(params) => <TextField {...params} placeholder="Choose a Student" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item md={2} xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%' } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        From Date
                      </Typography>
                      <DateSelect />
                    </FormControl>
                  </Grid>
                  <Grid item md={2} xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%' } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        To Date
                      </Typography>
                      <DateSelect />
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} md={1}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Paper className="card-table-container" sx={{ marginTop: '5px' }}>
              <DataTable columns={SaleListColumns} data={data} getRowKey={getRowKey} fetchStatus="success" />
            </Paper>
          </div>
          {/* <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 2 }}>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="secondary">
                Cancel
              </Button>
              <Button onClick={toggleDrawerOpen} variant="contained" color="primary">
                Save
              </Button>
            </Stack>
          </Box> */}
        </Card>
      </SaleListRoot>

      <TemporaryDrawer onClose={toggleDrawerClose} Title="Upload Details" state={drawerOpen} DrawerContent="" />
    </Page>
  );
}

export default SaleList;
