import { Button, <PERSON>Field, Typography, Box, Stack } from '@mui/material';
import React from 'react';

export const Create = ({ onClose, open }: any) => {
  return (
    <Box mt={3}>
      <Stack sx={{ height: 'calc(100vh - 150px)' }}>
        <Typography variant="h6" fontSize={14}>
          Enter Category Name
        </Typography>
        <TextField placeholder="Enter" />
      </Stack>
      <Box sx={{ justifyContent: 'center', pt: 3, position: 'relative' }}>
        <Stack spacing={2} direction="row" sx={{}}>
          <Button onClick={onClose} fullWidth variant="contained" color="secondary">
            Cancel
          </Button>
          <Button onClick={open} fullWidth variant="contained" color="primary">
            Save
          </Button>
        </Stack>
      </Box>
    </Box>
  );
};
