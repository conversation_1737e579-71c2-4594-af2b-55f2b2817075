import * as React from 'react';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons-material/Close';
import { Box, Dialog, Divider } from '@mui/material';
import styled from 'styled-components';

export interface DialogTitleProps {
  id: string;
  children?: React.ReactNode;
}
const BootstrapDialog = styled(Dialog)(({ theme }) => ({
  '& .MuiDialogContent-root': {
    padding: theme.spacing(0, 0, 3, 0),
  },
  '& .MuiDialogActions-root': {
    padding: theme.spacing(1),
  },
  '& .close-button:hover': {
    // transition: '100ms',
    // transform: 'scale(1.1)',
  },
  // '& .MuiDialogTitle-root': {
  //   padding: theme.spacing(1, 3, 1, 3),
  // },
}));
export function BootstrapDialogTitle(props: DialogTitleProps) {
  const { children, ...other } = props;
  return (
    <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }} {...other}>
      <div>{children}</div>
    </DialogTitle>
  );
}

export type PopupProps = {
  popupContent?: React.ReactNode;
  title?: string;
};

export default function LoadingPopup({ popupContent, title }: PopupProps) {
  return (
    <div>
      <BootstrapDialog
        // onClose={onClose}
        // fullWidth
        // width="100%"
        aria-labelledby="customized-dialog-title"
        open
      >
        <Box>
          <BootstrapDialogTitle id="customized-dialog-title">
            {title ?? title}
            {/* <Divider sx={{ mt: 1, width: '100%' }} /> */}
          </BootstrapDialogTitle>
        </Box>

        <DialogContent dividers>{popupContent}</DialogContent>
      </BootstrapDialog>
    </div>
  );
}
