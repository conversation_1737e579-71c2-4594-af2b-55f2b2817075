import { Box, Button, Dialog, DialogContent, DialogTitle, Stack, Theme } from '@mui/material';
import {
  JSXElementConstructor,
  ReactElement,
  ReactNode,
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useTheme } from 'styled-components';

type ConfirmChoiceCallback = (choice: boolean) => void;

export type CustomDialogRenderParams = (renderparams: { resolve: ConfirmChoiceCallback; theme: Theme }) => ReactNode;

export type ConfirmDialogParameters = {
  message: string | ReactElement<any, string | JSXElementConstructor<any>>;
  title: string;
  // isSubmitting?: boolean
};

export type ConfirmationDialogOptions = {
  okLabel?: string;
  cancelLabel?: string;
  actionName?: string;
  renderDialogUI?: CustomDialogRenderParams;
  showOnlyOk?: boolean;
};

type ConfirmationDialogInternalState = ConfirmDialogParameters & {
  isOpen: boolean;
  options?: ConfirmationDialogOptions;
};

export type ConfirmAPI = {
  confirm: (
    message: string | ReactElement<any, string | JSXElementConstructor<any>>,
    title: string,
    options?: ConfirmationDialogOptions
    // isSubmitting?: boolean
  ) => Promise<boolean>;
  isAsking: boolean;
  actionName?: string;
};

export type ConfirmationDialogProviderProps = {
  children: ReactNode;
};

const ConfirmationDialogContext = createContext<ConfirmAPI>({
  confirm: () => {},
  isOpen: false,
} as unknown as ConfirmAPI);

function ConfirmationDialogProider({ children }: ConfirmationDialogProviderProps) {
  const theme = useTheme();
  const [state, setState] = useState<ConfirmationDialogInternalState>({
    isOpen: false,
    title: 'Confirm',
    message: 'Are you sure?',
    options: {
      okLabel: 'Ok',
      cancelLabel: 'Cancel',
    },
  });
  const fn = useRef<ConfirmChoiceCallback>();
  const confirm = useCallback(
    (
      message: string | ReactElement<any, string | JSXElementConstructor<any>>,
      title: string,
      options?: ConfirmationDialogOptions
    ) =>
      new Promise<boolean>((resolve) => {
        fn.current = (choice: boolean) => {
          resolve(choice);
          setState((prevState) => ({ ...prevState, isOpen: false }));
        };
        setState({ message, title, options, isOpen: true });
      }),
    []
  );

  const { title, message } = state;
  const [countdown, setCountdown] = useState(4);

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (state.options?.showOnlyOk) {
      if (state.isOpen) {
        timer = setInterval(() => {
          setCountdown((prevCountdown) => prevCountdown - 1);
        }, 1000);
      }
    }
    return () => clearInterval(timer);
  }, [state.isOpen, state.options?.showOnlyOk]);

  useEffect(() => {
    let timer2: NodeJS.Timeout;
    if (state.options?.showOnlyOk) {
      if (countdown === 0) {
        fn?.current?.(true);
        setState((prevState) => ({ ...prevState, isOpen: false }));
      }
      if (state.isOpen === false) {
        timer2 = setInterval(() => {
          setCountdown(4);
        }, 1000);
      }
    }
    return () => clearInterval(timer2);
  }, [countdown, state.isOpen, state.options?.showOnlyOk]);

  const handleOKClick = () => {
    fn?.current?.(true);
    setState((prevState) => ({ ...prevState, isOpen: false }));
  };

  const handleCancelClick = () => {
    fn?.current?.(false);
  };

  const handleBackdropClick = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
  };

  const confirmAPIValue = useMemo<ConfirmAPI>(
    () => ({
      confirm,
      isAsking: state.isOpen,
      actionName: state.options?.actionName,
    }),
    [confirm, state.isOpen, state.options?.actionName]
  );

  return (
    <ConfirmationDialogContext.Provider value={confirmAPIValue}>
      {children}

      <Dialog open={state.isOpen} BackdropProps={{ onClick: handleBackdropClick }}>
        <DialogTitle fontSize={16}>{title}</DialogTitle>
        {!!state.options?.renderDialogUI && !!fn.current ? (
          state.options.renderDialogUI({ resolve: fn.current, theme })
        ) : (
          <DialogContent sx={{ padding: 0, px: 5, fontSize: 14, fontFamily: 'Poppins light' }} dividers>
            {message}
          </DialogContent>
        )}
        <Box sx={{ padding: '15px' }}>
          <Stack spacing={1} direction="row" justifyContent={state.options?.okLabel ? 'center' : 'end'}>
            {state.options?.showOnlyOk ? (
              <Button variant="contained" color="primary" size="small" onClick={handleOKClick}>
                {state.options?.okLabel || 'Ok'}&nbsp;({countdown}s)
              </Button>
            ) : (
              <>
                <Button
                  variant="contained"
                  color="secondary"
                  size="small"
                  sx={{ color: '#fff' }}
                  onClick={handleCancelClick}
                >
                  {state.options?.cancelLabel || 'Cancel'}
                </Button>
                <Button variant="contained" color="primary" size="small" onClick={handleOKClick}>
                  {state.options?.okLabel || 'Ok'}
                </Button>
              </>
            )}
          </Stack>
        </Box>
      </Dialog>
    </ConfirmationDialogContext.Provider>
  );
}

export function useConfirm() {
  return useContext(ConfirmationDialogContext);
}

export default ConfirmationDialogProider;
