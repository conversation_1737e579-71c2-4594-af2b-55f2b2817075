import React, { useState } from 'react';
import { <PERSON>ton, TextField, Typography, Box, Stack, FormControl, IconButton, useTheme, Card } from '@mui/material';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { AlbumVideoInfo } from '@/types/Album';
import PrgressUploadImage from '@/components/shared/Progress';
import { TbCloudUpload } from 'react-icons/tb';
import LoadingButton from '@mui/lab/LoadingButton';
import CloseIcon from '@mui/icons-material/Close';
import Videoplayer from '@/components/Dashboard/Videoplayer';
import ReactPlayer from 'react-player';
import ImageUpload from '@/components/shared/Selections/ImageUpload';
import useSettings from '@/hooks/useSettings';

// Remove unused imports: LoadingButton, TbCloudUpload

export type CreateEditClassFormProps = {
  onSave: (values: AlbumVideoInfo) => void;
  onCancel: () => void;
  onClose: () => void;
  albumInfo: AlbumVideoInfo;
  isSubmitting: boolean;
};

const CreateAlbumValidationSchema = Yup.object({
  albumName: Yup.string().required('Please enter Album Name'),
  videoTitle: Yup.string().required('Please enter Image Title'),
  // thumbnail: Yup.string().required('Please select an thumbnail image'),
  videoFile: Yup.string().required('Please select an Video'),
});

function CreateVideoForm({ onSave, onCancel, isSubmitting, albumInfo }: CreateEditClassFormProps) {
  const [uploadedVideo, setUploadedVideo] = useState('');
  const [uploadedImage, setUploadedImage] = useState('');
  const theme = useTheme();
  const isLight = useSettings().themeMode === 'light';

  const {
    values: { albumName, videoTitle, thumbnail, videoFile },
    handleChange,
    handleSubmit,
    touched,
    errors,
    setFieldValue,
  } = useFormik<AlbumVideoInfo>({
    initialValues: {
      albumId: albumInfo.albumId,
      albumName: albumInfo.albumName,
      videoTitle: albumInfo.videoTitle,
      thumbnail: albumInfo.thumbnail,
      videoFile: albumInfo.videoFile,
    },
    validationSchema: CreateAlbumValidationSchema,
    onSubmit: (values) => {
      // Call the onSave function with the updated values
      // const existingArray = [];

      const existingArray = JSON.parse(localStorage.getItem('myDummyVideoArray')) || [];
      const updatedArray = [...existingArray, values];
      localStorage.setItem('myDummyVideoArray', JSON.stringify(updatedArray));
      const newEntry = {
        albumId: Math.floor(Math.random() * 100000),
        albumName: values.albumName,
        videoTitle: values.videoTitle,
        videoThumbnail: uploadedImage,
        videoFile: uploadedVideo,
      };
      existingArray.push(newEntry);
      localStorage.setItem('myDummyVideoArray', JSON.stringify(existingArray));
      onSave(values);
    },
  });
  const [showProgress, setShowProgress] = useState(false);
  const [uploadedVideoSuccessFully, setUploadedVideoSuccessFully] = useState(false);

  // const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
  //   const files = event.target.files;
  //   if (files) {
  //     const videoUrl = URL.createObjectURL(files[0]);
  //     setUploadedVideo(videoUrl);
  //     setShowProgress(true);
  //     setTimeout(() => {
  //       setShowProgress(false);
  //       if (showProgress === false) setUploadedVideoSuccessFully(true);
  //     }, 2000);
  //   }
  // };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement> | React.DragEvent<HTMLDivElement>) => {
    let files;
    if ('dataTransfer' in event) {
      // Handle drag-and-drop
      event.preventDefault();
      files = event.dataTransfer.files;
    } else {
      // Handle file input change
      files = event.target.files;
    }

    if (files && files.length > 0) {
      const videoUrl = URL.createObjectURL(files[0]);
      setUploadedVideo(videoUrl);
      setShowProgress(true);

      setTimeout(() => {
        setShowProgress(false);
        if (!showProgress) setUploadedVideoSuccessFully(true);
      }, 2000);
    }
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    handleFileChange(event);
  };
  const handleFileChangeThumbnail = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files || null;
    if (files) {
      const imageUrls = URL.createObjectURL(files[0]);
      setUploadedImage(imageUrls);
      setFieldValue('images', imageUrls);
    }
  };
  return (
    <Box mt={3}>
      <form noValidate onSubmit={handleSubmit}>
        <Stack
          sx={{
            height: 'calc(100vh - 150px)',
            overflow: 'auto',
            '&::-webkit-scrollbar': {
              width: 0,
            },
          }}
          direction="column"
        >
          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Album Name
            </Typography>
            <TextField
              placeholder="Enter album name"
              name="albumName"
              value={albumName}
              onChange={handleChange}
              error={touched.albumName && !!errors.albumName}
              helperText={errors.albumName}
              disabled={isSubmitting}
            />
          </FormControl>
          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Video Title
            </Typography>
            <TextField
              placeholder="Enter video title"
              name="videoTitle"
              value={videoTitle}
              onChange={handleChange}
              error={touched.videoTitle && !!errors.videoTitle}
              helperText={errors.videoTitle}
              disabled={isSubmitting}
            />
          </FormControl>
          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Video Thumbnail
            </Typography>
            <ImageUpload state={uploadedImage} setState={setUploadedImage} onChange={handleFileChangeThumbnail} />

            {/* <TextField
              placeholder="Video Thumbnail"
              name="videoThumbnail"
              value={videoThumbnail}
              onChange={handleChange}
              error={touched.videoThumbnail && !!errors.videoThumbnail}
              helperText={errors.videoThumbnail}
              disabled={isSubmitting}
            /> */}
          </FormControl>
          {!uploadedVideoSuccessFully && (
            <Box>
              <Box
                sx={{
                  mt: 3,
                  height: 200,
                  border: '2px dashed #e8e8e9',
                  borderRadius: 1,
                  backgroundColor: isLight ? theme.palette.primary.lighter : theme.palette.grey[900],
                }}
              >
                <div
                  style={{
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    justifyContent: 'center',
                    flexDirection: 'column',
                  }}
                  onDragOver={handleDragOver}
                  onDrop={handleDrop}
                >
                  <Button
                    fullWidth
                    sx={{
                      '&.MuiButton-root:hover': {
                        backgroundColor: 'transparent',
                      },
                    }}
                    color="primary"
                    aria-label="upload video"
                    component="label"
                    disableTouchRipple
                  >
                    <input
                      type="file"
                      hidden
                      accept=".mov, .mp4"
                      onChange={(event) => {
                        setFieldValue('videoFile', event.currentTarget.files?.[0]);
                        handleFileChange(event);
                      }}
                      name="videoFile"
                    />
                    <Stack alignItems="center">
                      <TbCloudUpload fontSize={50} />
                      <Typography variant="h6" fontSize={12}>
                        Browse Video
                      </Typography>
                      <Typography variant="h6" fontSize={13} color="secondary">
                        or drag and drop
                      </Typography>
                    </Stack>
                  </Button>
                  <Stack mx={5}>{showProgress && <PrgressUploadImage />}</Stack>
                </div>
              </Box>
              <Typography variant="subtitle1" color="error" fontSize={12} mt={1} ml={2.5}>
                {touched.videoFile && errors.videoFile}
              </Typography>
              {/* <Typography variant="subtitle1" color="error" fontSize={12} ml={2.5}>
                {touched.imageFile && !!errors.imageFile}
                {errors.imageFile}
              </Typography> */}
            </Box>
          )}
          {uploadedVideoSuccessFully && (
            <Card sx={{ height: 200, mt: 2, position: 'relative', minWidth: 360 }}>
              <IconButton
                size="small"
                sx={{
                  position: 'absolute',
                  right: 5,
                  top: 5,
                  zIndex: 1,
                  border: `1px solid ${theme.palette.grey[300]}`,
                  backgroundColor: '#fff',
                  '&:hover': {
                    backgroundColor: theme.palette.grey[200],
                  },
                }}
                onClick={() => {
                  setUploadedVideoSuccessFully(false);
                  setUploadedVideo(''); // Clear the uploaded images
                }}
              >
                <CloseIcon fontSize="small" />
              </IconButton>
              {/* <video width="100%" controls src={uploadedVideo} /> */}
              {/* <ReactPlayer
                  controls
                  width="100%"
                  height="100%"
                  // playIcon={<BsFillPlayCircleFill color="white" fontSize={50} />}
                  light="https://images.unsplash.com/photo-1701352281550-4a7b283df099?q=80&w=1974&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                  url="https://www.youtube.com/watch?v=u1GSweKRC9U"
                  // playing={isPlaying}
                  muted
                /> */}
              <Videoplayer url={uploadedVideo} thumbnail={uploadedImage} />
            </Card>
          )}
        </Stack>
        <Box sx={{ justifyContent: 'center', pt: 3, position: 'relative' }}>
          <Stack spacing={2} direction="row" sx={{}}>
            <Button onClick={onCancel} fullWidth variant="contained" color="secondary" type="button">
              Cancel
            </Button>
            <LoadingButton
              loading={isSubmitting}
              fullWidth
              variant="contained"
              color="primary"
              type="submit"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Saving...' : 'Save'}
            </LoadingButton>
          </Stack>
        </Box>
      </form>
    </Box>
  );
}

export default CreateVideoForm;
