import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import Loadable from '@/components/shared/Loadable';

const GeneralSettings = Loadable(lazy(() => import('@/pages/GroupSettings')));
const Groups = Loadable(lazy(() => import('@/features/ManageGroups/Groups/Groups')));
const StudentMembers = Loadable(lazy(() => import('@/features/ManageGroups/StudentMembers/StudentMembers')));
const StaffMembers = Loadable(lazy(() => import('@/features/ManageGroups/StaffMember/StaffMember')));
const PublicMembers = Loadable(lazy(() => import('@/features/ManageGroups/PublicMembers/PublicMembers')));

export const groupSettingsRoutes: RouteObject[] = [
  {
    path: '/group-settings',
    element: <GeneralSettings />,
    children: [
      {
        path: 'groups',
        element: <Groups />,
      },
      {
        path: 'student-members',
        element: <StudentMembers />,
      },
      {
        path: 'staff-members',
        element: <StaffMembers />,
      },
      {
        path: 'public-members',
        element: <PublicMembers />,
      },
    ],
  },
];
