import Loadable from '@/components/shared/Loadable';
import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';

const TimeTable = Loadable(lazy(() => import('@/pages/TimeTable')));
const CreateNew = Loadable(lazy(() => import('@/features/TimeTable/CreateNew')));
const ListTimeTable = Loadable(lazy(() => import('@/features/TimeTable/ListTimeTable')));
const TimetableClass = Loadable(lazy(() => import('@/features/TimeTable/TimetableClass/TimetableClass')));
const TimetableStaff = Loadable(lazy(() => import('@/features/TimeTable/TimetableStaff/TimetableStaff')));
const StaffAttendance = Loadable(lazy(() => import('@/features/TimeTable/StaffAttendance')));
const Substitution = Loadable(lazy(() => import('@/features/TimeTable/Substitution')));
const TimetableGenerator = Loadable(lazy(() => import('@/features/TimeTable/TimetableGenerator/TimetableGenerator')));

export const timetableRoutes: RouteObject[] = [
  {
    path: '/time-table',
    element: <TimeTable />,
    children: [
      {
        path: 'new',
        element: <CreateNew />,
      },
      {
        path: 'list',
        element: <ListTimeTable />,
      },
      {
        path: 'class-wise',
        element: <TimetableClass />,
      },
      {
        path: 'staff-wise',
        element: <TimetableStaff />,
      },
      {
        path: 'staff-attendance',
        element: <StaffAttendance />,
      },
      {
        path: 'staff-substitution',
        element: <Substitution />,
      },
      {
        path: 'generator',
        element: <TimetableGenerator />,
      },
    ],
  },
];
