/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Paper,
  TextField,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Typography,
  Card,
  Checkbox,
  Stack,
  Button,
  Radio,
  FormControlLabel,
} from '@mui/material';
import styled from 'styled-components';
import { StudentMappedList } from '@/config/TableData';
import AddIcon from '@mui/icons-material/Add';
import { YEAR_SELECT } from '@/config/Selection';
// import { EditList } from './EditList';
// import { EditedTable } from './EditedTable';

const FineSetting3Root = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 16px;
`;

export default function FineSetting3() {
  return (
    <Page title="Fees Setting">
      <FineSetting3Root>
        <Card elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17}>
              Fine Setting
            </Typography>
            {/* <Box sx={{ display: 'flex', justifyContent: 'end', pb: 1 }}>
              <Button sx={{ borderRadius: '20px', mr: 1 }} variant="outlined">
                Fixed
              </Button>
              <Button sx={{ borderRadius: '20px' }} variant="outlined">
                Variable
              </Button>
            </Box> */}
          </Stack>
          <Divider />
          <Grid pb={4} pt={2} container spacing={3} justifyContent="space-between">
            <Grid item md={3} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Accademic Year
              </Typography>
              <Autocomplete
                options={YEAR_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select Year" />}
              />
            </Grid>
          </Grid>

          <Box display="flex" justifyContent="center">
            <Stack direction="column" gap={5} alignItems="center">
              <Button variant="contained" color="secondary" startIcon={<AddIcon />}>
                New Count
              </Button>
              <Typography variant="subtitle1" color="secondary">
                Please click save before add new fine counts
              </Typography>
              <Box display="flex" gap={5}>
                <Stack>
                  <Typography variant="subtitle2">Start After Days</Typography>
                  <TextField placeholder="00" size="small" sx={{ width: '70%' }} />
                </Stack>
                <Stack direction="row" alignItems="center" gap={3}>
                  In <FormControlLabel value="female" control={<Radio />} label="Amount" />
                </Stack>
                <Stack>
                  <Typography variant="subtitle2">Fine</Typography>
                  <TextField placeholder="00" size="small" sx={{ width: '70%' }} />
                </Stack>
              </Box>
            </Stack>
          </Box>
        </Card>
      </FineSetting3Root>
      {/* <TemporaryDrawer
        onClose={toggleDrawerClose}
        state={drawerOpen}
        DrawerContent={<EditList onClose={toggleDrawerClose} open={handleClickOpen} />}
      />
      <Popup size="md" state={popup} onClose={handleClickClose} popupContent={<EditedTable />} />
      <Popup
        size="xs"
        state={Delete}
        onClose={handleClickCloseDelete}
        popupContent={<DeleteMessage message="Are you sure want to delete?" />}
      /> */}
    </Page>
  );
}
