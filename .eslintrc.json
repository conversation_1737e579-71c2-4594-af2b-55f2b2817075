{
  "env": {
    "browser": true,
    "es2021": true,
    "jest": true
  },
  "extends": ["airbnb", "airbnb-typescript", "plugin:prettier/recommended", "plugin:storybook/recommended"],
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "ecmaFeatures": {
      "jsx": true
    },
    "ecmaVersion": "latest",
    "sourceType": "module",
    "project": "./tsconfig.json"
  },
  "plugins": ["react-hooks", "import", "prettier"],
  "rules": {
    "prettier/prettier": [
      "error",
      {
        "endOfLine": "auto"
      }
    ],
    "react/react-in-jsx-scope": 0,
    "react/jsx-props-no-spreading": 0,
    "import/prefer-default-export": 0,
    "no-console": 0,
    "react-hooks/rules-of-hooks": "error",
    "react-hooks/exhaustive-deps": "warn",
    "import/no-extraneous-dependencies": [
      "error",
      {
        "devDependencies": ["**/*.stories.*", "**/.storybook/**/*.*"],
        "peerDependencies": true
      }
    ],
    "react/function-component-definition": 0,
    "react/no-unused-prop-types": 0,
    "react/require-default-props": 0,
    "no-unused-vars": "off",
    "@typescript-eslint/no-unused-vars": "warn",
    "no-param-reassign": "off"
  },
  "overrides": [
    {
      // feel free to replace with your preferred file pattern - eg. 'src/**/*Slice.ts'
      "files": ["src/**/*.slice.ts"],
      // avoid state param assignment
      "rules": { "no-param-reassign": ["error", { "props": false }] }
    }
  ],
  "settings": {
    "import/resolver": {
      "alias": {
        "map": [["@", "./src"]],
        "extensions": [".ts", ".tsx", ".js", ".jsx", ".json", ".stories.tsx"]
      }
    }
  }
}
