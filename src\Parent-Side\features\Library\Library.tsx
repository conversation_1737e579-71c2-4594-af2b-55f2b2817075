/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  useTheme,
  IconButton,
  Collapse,
  Tooltip,
  FormControl,
  Chip,
  Snackbar,
  Alert,
} from '@mui/material';
import styled from 'styled-components';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import SearchIcon from '@mui/icons-material/Search';
import Popup from '@/components/shared/Popup/Popup';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import Physics from '@/Parent-Side/assets/liveclass/Icons/Physics.svg';
import { MdAdd, MdContentCopy } from 'react-icons/md';
import { CloseIcon } from '@/theme/overrides/CustomIcons';
import { Student, StudentInfoData } from '@/config/StudentDetails';
import useSettings from '@/hooks/useSettings';
import AutorenewOutlinedIcon from '@mui/icons-material/AutorenewOutlined';
import { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import DateSelect from '@/components/shared/Selections/DateSelect';
import MenuEditDelete from '@/components/shared/Selections/MenuEditDelete';
import CreateScheduleList from '@/features/LiveClass/ScheduleList/CreateScheduleList';
import InsertDriveFileIcon from '@mui/icons-material/InsertDriveFile';
import View from './View';
import { Paper } from '@mui/material';

export const data = [
  {
    title: 'The Mostly Dead Things',
    author: 'Passdaily',
    issuedOn: '12/02/2024',
    returnDate: '30/02/2024',
    bookId: '021',
    price: '200',
    fine: '50',
    status: 'Not Returned',
  },
  {
    title: 'The Mostly Dead Things2',
    author: 'Passdaily',
    issuedOn: '12/02/2024',
    returnDate: '30/02/2024',
    bookId: '021',
    price: '200',
    fine: '50',
    status: 'Returned',
  },
  {
    title: 'The Mostly Dead Things',
    author: 'Passdaily',
    issuedOn: '12/02/2024',
    returnDate: '30/02/2024',
    bookId: '021',
    price: '200',
    fine: '50',
    status: 'Not Returned',
  },
  {
    title: 'The Mostly Dead Things',
    author: 'Passdaily',
    issuedOn: '12/02/2024',
    returnDate: '30/02/2024',
    bookId: '021',
    price: '200',
    fine: '50',
    status: 'Returned',
  },
  {
    title: 'The Mostly Dead Things',
    author: 'Passdaily',
    issuedOn: '12/02/2024',
    returnDate: '30/02/2024',
    bookId: '021',
    price: '200',
    fine: '50',
    status: 'Not Returned',
  },
  {
    title: 'The Mostly Dead Things',
    author: 'Passdaily',
    issuedOn: '12/02/2024',
    returnDate: '30/02/2024',
    bookId: '021',
    price: '200',
    fine: '50',
    status: 'Returned',
  },
  {
    title: 'The Mostly Dead Things',
    author: 'Passdaily',
    issuedOn: '12/02/2024',
    returnDate: '30/02/2024',
    bookId: '021',
    price: '200',
    fine: '50',
    status: 'Not Returned',
  },
  {
    title: 'The Mostly Dead Things',
    author: 'Passdaily',
    issuedOn: '12/02/2024',
    returnDate: '30/02/2024',
    bookId: '021',
    price: '200',
    fine: '50',
    status: 'Not Returned',
  },
  {
    title: 'The Mostly Dead Things',
    author: 'Passdaily',
    issuedOn: '12/02/2024',
    returnDate: '30/02/2024',
    bookId: '021',
    price: '200',
    fine: '50',
    status: 'Not Returned',
  },
  {
    title: 'The Mostly Dead Things',
    author: 'Passdaily',
    issuedOn: '12/02/2024',
    returnDate: '30/02/2024',
    bookId: '021',
    price: '200',
    fine: '50',
    status: 'Not Returned',
  },
];

const LibraryRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  .student_card {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  }

  .Card {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    height: calc(100vh - 110px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 40px);
      flex-grow: 1;

      .card-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: auto;
        ::-webkit-scrollbar {
          width: 0px;
        }

        .MuiTableContainer-root {
          max-height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

function Library() {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const [changeView, setChangeView] = useState(false);
  const [showFilter, setShowFilter] = useState(false);
  const [Delete, setDelete] = useState(false);
  const [createPopup, setCreatePopup] = useState(false);

  const [students, setStudents] = useState(StudentInfoData);
  const [open, setOpen] = useState(false);
  const [openNew, setOpenNew] = useState(false);
  const [showNewMultiple, setShowNewMultiple] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [updatedData, setData] = useState(data);

  const LibraryColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'author',
        dataKey: 'author',
        headerLabel: 'Author',
      },
      {
        name: 'issuedOn',
        dataKey: 'issuedOn',
        headerLabel: 'Issued On',
      },
      {
        name: 'returnDate',
        dataKey: 'returnDate',
        headerLabel: 'Return Date',

        // renderCell: (row) => {
        //   return (
        //     <Stack direction="row" alignItems="center" maxWidth="90%">
        //       <Tooltip title={`${row.meetingLink}`} arrow>
        //         <Chip
        //           size="small"
        //           label="40"
        //           variant="outlined"
        //           sx={{
        //             border: '0px',
        //             mr: '1px',
        //             mb: 2,
        //             backgroundColor: isLight ? theme.palette.info.lighter : theme.palette.grey[700],
        //             color: isLight ? theme.palette.info.dark : theme.palette.grey[100],
        //             width: 'auto',
        //           }}
        //         />
        //       </Tooltip>
        //     </Stack>
        //   );
        // },
      },
      {
        name: 'bookId',
        dataKey: 'bookId',
        headerLabel: 'Book Id',
      },
    ],
    [theme, isLight]
  );

  return (
    <Page title="Schedule List">
      <LibraryRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 1, md: 2 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={20} width="100%">
              Library
            </Typography>
          </Stack>
          <Stack>
            <Divider />
          </Stack>
          <div className="card-main-body">
            <Box className="card-container" my={2}>
              <Grid container spacing={2}>
                {updatedData.map((data, rowIndex) => (
                  <Grid item xxl={3} xl={4} lg={6} md={12} sm={6} xs={12}>
                    <Card
                      className="student_card"
                      sx={{ p: 2, boxShadow: 0, border: `1px solid ${theme.palette.grey[300]}` }}
                    >
                      <Chip
                        sx={{
                          fontSize: 10,
                          position: 'absolute',
                          top: 10,
                          left: 0,
                          borderTopLeftRadius: 0,
                          borderBottomLeftRadius: 0,
                        }}
                        size="small"
                        label={data.status}
                        color={data.status === 'Not Returned' ? 'error' : 'success'}
                      />
                      {/* <Typography
                        variant="subtitle1"
                        position="absolute"
                        top={10}
                        left={0}
                        px={1}
                        sx={{ borderTopRightRadius: 10, borderBottomRightRadius: 10, }}
                        bgcolor={theme.palette.error.lighter}
                        color={data.status === 'Not Returned' ? 'error' : 'success'}
                        fontSize={11}
                      >
                        {data.status}
                      </Typography> */}
                      <Box flexGrow={1} mt={5} display="flex" gap={2}>
                        <Card sx={{ boxShadow: 5 }}>
                          <img width={100} src="https://m.media-amazon.com/images/I/61VY8n7LSNL._SY466_.jpg" alt="" />
                        </Card>
                        <Stack>
                          <Typography variant="subtitle2" fontSize={13}>
                            {data.title}
                          </Typography>
                          {LibraryColumns.map((item, index) => (
                            <Stack direction="row" key={index}>
                              <Grid container>
                                <Grid item lg={6} xs={6}>
                                  <Typography key={item.name} variant="subtitle1" color="GrayText" fontSize={11}>
                                    {item.headerLabel}
                                  </Typography>
                                </Grid>
                                <Grid item lg={6} xs={6} mb={0.5} mt="auto">
                                  {item.dataKey ? (
                                    <Typography variant="subtitle1" color="GrayText" fontSize={11}>
                                      {/* {`: ${(student as { [key: string]: any })[item.dataKey ?? '']}`} */}
                                      {item.dataKey
                                        ? `:${' '}${(data as { [key: string]: any })[item.dataKey ?? '']}`
                                        : item && item.renderCell && item.renderCell(data, rowIndex)}
                                    </Typography>
                                  ) : (
                                    item && item.renderCell && item.renderCell(data, rowIndex)
                                  )}
                                </Grid>
                              </Grid>
                            </Stack>
                          ))}
                          <Stack direction="row" justifyContent="space-between">
                            <Typography variant="subtitle2" fontSize={13}>
                              $ {data.price}
                            </Typography>
                            <Typography variant="subtitle1" color="error" fontSize={13}>
                              Fine: ${data.fine}
                            </Typography>
                          </Stack>
                        </Stack>
                      </Box>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Box>
          </div>
        </Card>
      </LibraryRoot>
      {/* <Popup
        size="xs"
        state={Delete}
        onClose={handleClickCloseDelete}
        popupContent={<DeleteMessage message="Are you sure want to delete?" />}
      /> */}
      {/* <Popup
        size="sm"
        title="Assignment Details"
        state={createPopup}
        onClose={() => setCreatePopup(false)}
        popupContent={<View />}
      /> */}
    </Page>
  );
}

export default Library;
