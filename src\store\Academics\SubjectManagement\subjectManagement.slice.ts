import { PayloadAction, createSlice } from '@reduxjs/toolkit';
import type { SubjectManagementState } from '@/types/AcademicManagement';
import { flushStore } from '@/store/flush.slice';
import { OutletContextType } from '@/types/Common';
import {
  fetchSubjectList,
  addNewSubject,
  updateSubject,
  deleteSubject,
  addNewSubjectMulti,
  // fetchSubjectLists,
} from './subjectManagement.thunks';

const initialState: SubjectManagementState = {
  subjectList: {
    status: 'idle',
    data: [],
    error: null,
    pageInfo: {
      totalrecords: 0,
      pagesize: 20,
      pagenumber: 1,
      totalpages: 0,
      remainingpages: 0,
    },
    sortColumn: 'subjectId',
    sortDirection: 'asc',
  },
  submitting: false,
  deletingRecords: {},
  error: null,
};

const subjectManagementSlice = createSlice({
  name: 'subjectManagement',
  initialState,
  reducers: {
    setSubjectListPage: (state, action: PayloadAction<number>) => {
      state.subjectList.pageInfo.pagenumber = action.payload;
    },
    setSubjectListPageSize: (state, action: PayloadAction<number>) => {
      state.subjectList.pageInfo.pagenumber = 1;
      state.subjectList.pageInfo.pagesize = action.payload;
    },
    setSortColumn: (state, action: PayloadAction<string>) => {
      state.subjectList.pageInfo.pagenumber = 1;
      state.subjectList.sortColumn = action.payload;
      state.subjectList.sortDirection = 'asc';
    },
    setSortDirection: (state, action: PayloadAction<'asc' | 'desc'>) => {
      state.subjectList.pageInfo.pagenumber = 1;
      state.subjectList.sortDirection = action.payload;
    },
    setSortColumnAndDirection: (state, action: PayloadAction<{ column: string; direction: 'asc' | 'desc' }>) => {
      state.subjectList.pageInfo.pagenumber = 1;
      state.subjectList.sortColumn = action.payload.column;
      state.subjectList.sortDirection = action.payload.direction;
    },
    setSubmitting: (state, action: PayloadAction<boolean>) => {
      state.submitting = action.payload;
    },
    setDeletingRecords: (state, action: PayloadAction<{ recordId: number }>) => {
      state.deletingRecords[action.payload.recordId] = true;
    },
    clearDeletingRecords: (state, action: PayloadAction<{ recordId: number }>) => {
      delete state.deletingRecords[action.payload.recordId];
    },
  },
  extraReducers: (builder) => {
    builder
      //   .addCase(fetchSubjectLists.pending, (state) => {
      //     state.subjectList.status = 'loading';
      //     state.subjectList.error = null;
      //   })
      //   .addCase(fetchSubjectLists.fulfilled, (state, action) => {
      //     const data = action.payload;
      //     state.subjectList.status = 'success';
      //     state.subjectList.data = data;
      //     state.subjectList.error = null;
      //     // state.subjectList.pageInfo = pageInfo;
      //   })
      //   .addCase(fetchSubjectLists.rejected, (state, action) => {
      //     state.subjectList.status = 'error';
      //     state.subjectList.error = action.payload || 'Unknown error in fetching subject list';
      //   })

      .addCase(fetchSubjectList.pending, (state) => {
        state.subjectList.status = 'loading';
        state.subjectList.error = null;
      })
      .addCase(fetchSubjectList.fulfilled, (state, action) => {
        const { data, ...pageInfo } = action.payload;
        state.subjectList.status = 'success';
        state.subjectList.data = data;
        state.subjectList.error = null;
        state.subjectList.pageInfo = pageInfo;
      })
      .addCase(fetchSubjectList.rejected, (state, action) => {
        state.subjectList.status = 'error';
        state.subjectList.error = action.payload || 'Unknown error in fetching subject list';
      })

      .addCase(addNewSubject.pending, (state) => {
        state.submitting = true;
        state.error = null;
      })
      .addCase(addNewSubject.fulfilled, (state) => {
        state.submitting = false;
        state.error = null;
      })
      .addCase(addNewSubject.rejected, (state, action) => {
        state.submitting = false;
        state.error = action.error.message;
      })

      .addCase(addNewSubjectMulti.pending, (state) => {
        state.submitting = true;
        state.error = null;
      })
      .addCase(addNewSubjectMulti.fulfilled, (state) => {
        state.submitting = false;
        state.error = null;
      })
      .addCase(addNewSubjectMulti.rejected, (state, action) => {
        state.submitting = false;
        state.error = action.error.message;
      })

      .addCase(updateSubject.pending, (state) => {
        state.submitting = true;
        state.error = null;
      })
      .addCase(updateSubject.fulfilled, (state, action) => {
        state.submitting = false;
        state.error = null;
        const dataIndex = state.subjectList.data.findIndex((x) => x.subjectId === action.meta.arg.subjectId);
        if (dataIndex > -1) {
          state.subjectList.data[dataIndex] = action.meta.arg;
        }
      })
      .addCase(updateSubject.rejected, (state, action) => {
        state.submitting = false;
        state.error = action.error.message;
      })

      .addCase(deleteSubject.pending, (state, action) => {
        state.deletingRecords[action.meta.arg] = true;
        state.error = null;
      })
      .addCase(deleteSubject.fulfilled, (state, action) => {
        if (action.payload.deleted) {
          const { [action.meta.arg]: deletedRecord, ...restDeletingRecords } = state.deletingRecords;
          state.deletingRecords = restDeletingRecords;
        }
        state.error = null;
      })
      .addCase(deleteSubject.rejected, (state, action) => {
        state.deletingRecords = { ...initialState.deletingRecords };
        state.error = action.error.message;
      })
      .addCase(flushStore, () => initialState);
  },
});

export const {
  setSubjectListPage,
  setSubjectListPageSize,
  setSortColumn,
  setSortDirection,
  setSortColumnAndDirection,
  setSubmitting,
  setDeletingRecords,
  clearDeletingRecords,
} = subjectManagementSlice.actions;

export default subjectManagementSlice.reducer;
