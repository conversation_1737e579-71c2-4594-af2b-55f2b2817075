/* eslint-disable prettier/prettier */
import * as React from 'react';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';
import Page from '@/components/shared/Page';
import styled from 'styled-components';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import NotificationCreate from '@/features/AppNotification/NotificationCreate';
import NotificationList from '@/features/AppNotification/NotificationList/NotificationList';
import NotificationReport from '@/features/AppNotification/NotificationReport/NotificationReport';
import NotificationReportStaff from '@/features/AppNotification/StaffNotification/StaffNotificationReport';
import { Link, useLocation, useOutletContext } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { flushStore } from '@/store/flush.slice';
import { OutletContextType } from '@/types/Common';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const AppNotificationRoot = styled.div`
  min-height: calc(100vh - ${TOP_BAR_HEIGHT});
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  .tab {
    background-color: ${(props) => (props.theme.themeMode === 'light' ? '#fff' : props.theme.palette.grey[800])};
  }
  .Mui-selected {
    color: ${(props) => props.theme.palette.primary.main};
  }
`;

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <Box
      sx={{ paddingTop: '3rem' }}
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </Box>
  );
}

function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`,
  };
}

export default function AppNotification() {
  const { topBarHeight } = useOutletContext<OutletContextType>();
  const location = useLocation();
  const [value, setValue] = React.useState(0);
  
  const dispatch = useDispatch();

  React.useEffect(() => {
    dispatch(flushStore());
    console.log('Flush Store');
  }, [dispatch, value]);

  React.useEffect(() => {
    const currentPath = location.pathname;
    const routePaths = [
      '/app-notification/create',
      '/app-notification/list',
      '/app-notification/report',
      '/app-notification/staff-report',
    ];
    const index = routePaths.indexOf(currentPath);
    if (index !== -1) {
      setValue(index);
    }
  }, [location]);

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  return (
    <Page title="App Notification">
      <AppNotificationRoot>
        <Tabs
          className="tab"
          variant="scrollable"
          scrollButtons
          allowScrollButtonsMobile
          value={value}
          onChange={handleChange}
          aria-label="basic tabs"
          sx={{
            ml: '0px',
            borderBottom: 1,
            borderColor: 'divider',
            position: 'fixed',
             top: `${topBarHeight}px`,
            zIndex: 1,
            width: '100%',
          }}
        >
          <Tab
            {...a11yProps(1)}
            label="Notification Create"
            component={Link}
            to="/app-notification/create"
            />
          <Tab
            {...a11yProps(1)}
            label="Notification List"
            component={Link}
            to="/app-notification/list"
            />
          <Tab
            {...a11yProps(2)}
            label="Notification Report"
            component={Link}
            to="/app-notification/report"
            />
          <Tab
            {...a11yProps(3)}
            label="Notification Report Staff"
            component={Link}
            to="/app-notification/staff-report"
            />
        </Tabs>
        <TabPanel value={value} index={0}>
          <NotificationCreate />
        </TabPanel>
        <TabPanel value={value} index={1}>
          <NotificationList />
        </TabPanel>
        <TabPanel value={value} index={2}>
          <NotificationReport />
        </TabPanel>
        <TabPanel value={value} index={3}>
          <NotificationReportStaff />
        </TabPanel>
      </AppNotificationRoot>
    </Page>
  );
}
