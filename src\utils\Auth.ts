import TokenStorage from '@/utils/TokenStorage';
import api from '@/api';
import { Account } from '@/types/Auth';

export async function GetActiveAccountFromToken(): Promise<Account | null> {
  try {
    const accessTokenInStorage = TokenStorage.getAccessToken();
    if (accessTokenInStorage) {
      try {
        const response = await api.Auth.VerifyAccessToken(accessTokenInStorage);
        if (response.status === 200) {
          if (response.data && response.data.valid && response.data.account) {
            return response.data.account;
          }
          return null;
        }
        return null;
      } catch {
        return null;
      }
    }
    return null;
  } catch {
    return null;
  }
}
