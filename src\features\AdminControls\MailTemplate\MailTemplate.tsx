/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  Collapse,
  FormControl,
  Autocomplete,
  TableHead,
  TableBody,
  useTheme,
  Table,
  TableRow,
  TableCell,
  Tooltip,
} from '@mui/material';
import styled from 'styled-components';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import SearchIcon from '@mui/icons-material/Search';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import DeleteIcon from '@mui/icons-material/Delete';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import { MdAdd } from 'react-icons/md';
import { YEAR_SELECT } from '@/config/Selection';
import { useAppSelector } from '@/hooks/useAppSelector';
import { getClassListData } from '@/config/storeSelectors';
import ClassSelect from '@/components/shared/Selections/ClassSelect';
import MenuEditDelete from '@/components/shared/Selections/MenuEditDelete';
import LoadingButton from '@mui/lab/LoadingButton';
import { CreateEditMessageForm } from '../../MessageBox/SmsTemplate/CreateEditMessageForm';
import { CreateEditMailTemplateForm } from './CreateEditMailTemplateForm';
import MenuEditDeleteView from '@/components/shared/Selections/MenuEditDeleteView';

const MailTemplateRoot = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 30px);
      flex-grow: 1;

      .card-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: auto;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
      .Student_Table .MuiTableCell-root {
        background-color: 'red';
      }
    }
  }
`;
export type SendButtonType = {
  name: string;
  component: string;
};
export type AllSendButtonsType = {
  id: number;
  name: string;
  action: any;
};
export const sendbuttons: SendButtonType[] = [
  { name: 'Parent', component: 'Parents' },
  { name: 'Staff', component: 'Staffs' },
  { name: 'Class Wise', component: 'ClassWise' },
  { name: 'Class Division', component: 'ClassDivision' },
];

const sendbuttons2: AllSendButtonsType[] = [
  { id: 1, name: 'All Parents', action: '' },
  { id: 2, name: 'All Staffs', action: '' },
];

export const data = [
  {
    SlNo: '1',
    GuardianName: 'Ansar',
    GuardianNumber: '9746980992',
    Students: [
      { id: 1, name: 'MANHA', class: 'LKG-A' },
      { id: 2, name: 'HAASINI M', class: 'I-A' },
    ],

    UserId: '9746980992',
    Password: '696451',
    CreatedDate: '24 Aug 2023 12:06 PM',
  },
  {
    SlNo: '2',
    GuardianName: 'ANWAR',
    GuardianNumber: '7558966668',
    Students: [
      { id: 1, name: 'MANHA', class: 'LKG-A' },
      { id: 2, name: 'HAASINI M', class: 'I-A' },
    ],
    UserId: '7558966668',
    Password: '325994',
    CreatedDate: '23 Aug 2023 04:23 PM',
  },
  {
    SlNo: '3',
    GuardianName: 'RUBY, PARENT',
    GuardianNumber: '9995436405',
    Students: [
      { id: 1, name: 'MANHA', class: 'LKG-A' },
      { id: 2, name: 'HAASINI M', class: 'I-A' },
    ],
    UserId: '9995436405',
    Password: '',
    CreatedDate: '23 Aug 2023 11:21 AM',
  },
  {
    SlNo: '4',
    GuardianName: 'FAVAS, FAVAS',
    GuardianNumber: '9946824752',
    Students: [
      { id: 1, name: 'MANHA', class: 'LKG-A' },
      { id: 2, name: 'HAASINI M', class: 'I-A' },
    ],
    UserId: '9946824752',
    Password: '486722',
    CreatedDate: '22 Aug 2023 02:09 PM',
  },
  {
    SlNo: '5',
    GuardianName: 'PARENT',
    GuardianNumber: '9061168913',
    Students: [
      { id: 1, name: 'MANHA', class: 'LKG-A' },
      { id: 2, name: 'HAASINI M', class: 'I-A' },
    ],
    UserId: '9061168913',
    Password: '642654',
    CreatedDate: '22 Aug 2023 01:03 PM',
  },
  {
    SlNo: '6',
    GuardianName: 'PARENT',
    GuardianNumber: '7092171154',
    Students: [
      { id: 1, name: 'MANHA', class: 'LKG-A' },
      { id: 2, name: 'HAASINI M', class: 'I-A' },
    ],
    UserId: '7092171154',
    Password: '878884',
    CreatedDate: '22 Aug 2023 12:24 PM',
  },
];

function MailTemplate() {
  const theme = useTheme();
  const classListData = useAppSelector(getClassListData);
  const classData = classListData.map((item) => item.className);
  console.log(classData);
  const [drawerOpen, setDrawerOpen] = React.useState<boolean>(false);
  const [showFilter, setShowFilter] = useState(false);
  const [enableDisable, setEnableDisable] = useState(false);

  const handleOpen = () => {
    setDrawerOpen(true);
  };

  const toggleDrawerClose = useCallback(() => setDrawerOpen(false), []);
  const pageProps = useMemo(
    () => ({
      rowsPerPageOptions: [10, 20, 30],
      pageNumber: 0,
      pageSize: 5,
      totalRecords: 50,
    }),
    []
  );

  const handleSaveorEdit = useCallback(async (value: any, mode: 'create' | 'edit') => {
    // try {
    //   if (mode === 'create') {
    //     // setDrawerOpen(false);
    //     // if (isSubmitting === false) {
    //     //   showConfirmation(
    //     //     <SuccessMessage icon={AddFile} message="Message template creatng..." />,
    //     //     'Message Creating'
    //     //   );
    //     // }
    //     const { ...rest } = value;
    //     const response = await dispatch(createMessageTempList(rest)).unwrap();
    //     loadMessageTempList({ ...currentMessageTempRequest });
    //     if (response.id > 0) {
    //       setDrawerOpen(false);
    //       await showConfirmation(<SuccessMessage icon={SaveFile} message="Message created successfully" />, '');
    //     }
    //     // const successMessage = <SuccessMessage icon={AddFile} message="Message created successfully" />;
    //     // await confirm(successMessage, 'Message Created', { okLabel: 'Ok', showOnlyOk: true });
    //   } else {
    //     // await showConfirmation(
    //     //   <SuccessMessage icon={SaveFile} message="Message created successfully" />,
    //     //   'Message Created'
    //     // );
    //     const response = await dispatch(EditMessageTemplate(value)).unwrap();
    //     setDrawerOpen(false);
    //     loadMessageTempList(currentMessageTempRequest);
    //     if (response.rowsAffected === 0) {
    //       await showConfirmation(<SuccessMessage icon={SaveFile} message="Message updated successfully" />, '');
    //       // const successMessage = <SuccessMessage icon={AddFile} message="Message updated successfully" />;
    //       // await confirm(successMessage, 'Message Updated', { okLabel: 'Ok', showOnlyOk: true });
    //       // loadMessageTempList(currentMessageTempRequest);
    //     }
    //   }
    // } catch (error) {
    //   // Handle errors here
    //   setDrawerOpen(false);
    //   await showConfirmation(<ErrorMessage icon={ErrorMsg} message="Message content already created" />, '');
    //   handleEditMessage(value);
    //   setDrawerOpen(true);
    //   console.error(error);
    // }
  }, []);
  const getRowKey = useCallback((row: any) => row.examId, []);
  const MailTemplateColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'Guardian Name',
        dataKey: 'GuardianName',
        headerLabel: 'Created',
      },
      {
        name: 'GuardianNumber',
        dataKey: 'GuardianNumber',
        headerLabel: 'Status',
      },
      // {
      //   name: 'Students',
      //   dataKey: 'concatenatedStudents',
      //   headerLabel: 'Students',
      // },
      // {
      //   name: 'Class',
      //   dataKey: 'Class',
      //   headerLabel: 'Class',
      // },
      {
        name: 'User Id',
        dataKey: 'UserId',
        headerLabel: 'Date',
      },

      // {
      //   name: 'actions',
      //   headerLabel: 'Actions',
      //   renderCell: () => {
      //     return (
      //       <Stack direction="row" gap={1}>
      //         <IconButton size="small" onClick={handleOpen} sx={{ padding: 0.5 }}>
      //           <ModeEditIcon />
      //         </IconButton>
      //         <IconButton size="small" color="error" onClick={handleOpen} sx={{ padding: 0.5 }}>
      //           <DeleteIcon />
      //         </IconButton>
      //       </Stack>
      //     );
      //   },
      // },
    ],
    []
  );

  return (
    <Page title="List">
      <MailTemplateRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17}>
              Mail Template
            </Typography>
            <Box pb={1} sx={{ flexShrink: 0 }}>
              <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                <SearchIcon />
              </IconButton>
              <Button onClick={handleOpen} sx={{ borderRadius: '20px' }} variant="outlined" size="small">
                <MdAdd size="20px" /> Create
              </Button>
            </Box>
          </Stack>
          <Divider sx={{ marginBottom: 1 }} />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={4} container spacing={3} alignItems="end">
                  <Grid item lg="auto" sm={4} xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', md: 150 } }}>
                      <Typography variant="h6" fontSize={14}>
                        Academic Year
                      </Typography>
                      <Autocomplete
                        options={YEAR_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select Year" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" sm={4} xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', md: 150 } }}>
                      <Typography variant="h6" fontSize={14}>
                        Class
                      </Typography>
                      {/* <Autocomplete
                        options={classData}
                        renderInput={(params) => <TextField {...params} placeholder="Select Class" />}
                      /> */}
                      <ClassSelect />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" sm={4} xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', md: 150 } }}>
                      <Typography variant="h6" fontSize={14}>
                        Student
                      </Typography>
                      <Autocomplete
                        options={['Student1', 'Student2', 'Student3']}
                        renderInput={(params) => <TextField {...params} placeholder="Select Student" />}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item lg="auto" sm={4} xs={12}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            {/* <Paper className="card-table-container" sx={{ marginTop: '12px' }}>
              <DataTable
                columns={MailTemplateColumns}
                data={data}
                getRowKey={getRowKey}
                fetchStatus="success"
                allowPagination
                PaginationProps={pageProps}
              />
            </Paper> */}
            <Box className="card-container" pb={3.5}>
              <Grid container spacing={2}>
                {data.map((student, rowIndex) => (
                  <Grid item xl={4} lg={6} md={12} sm={6} xs={12} key={rowIndex}>
                    <Card
                      className="student_card"
                      sx={{ p: 2, boxShadow: 0, border: `1px solid ${theme.palette.grey[300]}` }}
                    >
                      <Box flexGrow={1}>
                        {MailTemplateColumns.map((item, index) => (
                          <Stack direction="row" key={index}>
                            <Grid container>
                              <Grid item lg={6} xs={6}>
                                <Typography key={item.name} variant="subtitle1" color="GrayText" fontSize={13} mr={2}>
                                  {item.headerLabel}
                                </Typography>
                              </Grid>
                              {item.dataKey ? (
                                <Grid item lg={6} xs={6} mb={0.5} mt="auto">
                                  <Typography variant="h6" fontSize={13}>
                                    {`: ${(student as { [key: string]: any })[item.dataKey ?? '']}`}
                                    {/* {item.dataKey
                                      ? `:${' '}${(student as { [key: string]: any })[item.dataKey ?? '']}`
                                      : item && item.renderCell && item.renderCell(student, rowIndex)} */}
                                  </Typography>
                                </Grid>
                              ) : (
                                item && item.renderCell && item.renderCell(student, rowIndex)
                              )}
                            </Grid>
                          </Stack>
                        ))}
                      </Box>
                      <Stack position="absolute" top={10} right={10}>
                        <MenuEditDeleteView
                          Edit={() => {
                            return 0;
                          }}
                          // Delete={handleClickDelete}
                        />
                      </Stack>
                      <Typography variant="subtitle2">
                        <u>Testing</u>
                      </Typography>
                      <Typography variant="body2" color={theme.palette.grey[500]} fontSize={12}>
                        School is going to celebrate annual day celebate at scchool auditorium.
                      </Typography>

                      {sendbuttons.map((item) => (
                        <Button
                          sx={{ mr: 1, mb: 1.5, p: 0.5, fontWeight: 600, fontSize: 12 }}
                          size="small"
                          variant="outlined"
                          color="primary"
                          // onClick={() => handleClick(item, card)}
                        >
                          {item.name}
                        </Button>
                      ))}
                      {sendbuttons2.map((item) => (
                        <Tooltip key={item.id} title="">
                          <LoadingButton
                            sx={{ mr: 1, mb: 1.5, p: 0.5, fontWeight: 600, fontSize: 12 }}
                            size="small"
                            variant="outlined"
                          >
                            {item.name}
                          </LoadingButton>
                        </Tooltip>
                      ))}
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Box>
          </div>
          {/* <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 2 }}>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="secondary">
                Cancel
              </Button>
              <Button onClick={handleOpen} variant="contained" color="primary">
                Save
              </Button>
            </Stack>
          </Box> */}
        </Card>
      </MailTemplateRoot>
      <TemporaryDrawer
        onClose={toggleDrawerClose}
        Title="Create"
        state={drawerOpen}
        DrawerContent={
          <CreateEditMailTemplateForm
            adminId={8}
            isSubmitting={false}
            messageDetails={null}
            onSave={handleSaveorEdit}
            onCancel={toggleDrawerClose}
          />
        }
      />

      {/* <TemporaryDrawer onClose={toggleDrawerClose} Title="Upload Details" state={drawerOpen} DrawerContent="" /> */}
    </Page>
  );
}

export default MailTemplate;
