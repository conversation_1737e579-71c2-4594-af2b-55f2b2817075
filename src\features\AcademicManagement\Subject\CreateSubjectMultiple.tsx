import { useEffect, useCallback, useRef, useState, FormEvent } from 'react';
import { STATUS_OPTIONS } from '@/config/Selection';
import { SubjectCreateRow } from '@/types/AcademicManagement';
import {
  Stack,
  Typography,
  Button,
  Table,
  TableHead,
  TableRow,
  Paper,
  TableBody,
  TableCell,
  TableContainer,
  TextField,
  MenuItem,
  Select,
  IconButton,
  Card,
  Tooltip,
  Alert,
} from '@mui/material';
import { FormikErrors, useFormik } from 'formik';
import { MdArrowBack } from 'react-icons/md';
import styled from 'styled-components';
import { v4 as uuidv4 } from 'uuid';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import Page from '@/components/shared/Page';
import ErrorIcon from '@mui/icons-material/Error';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import { addNewSubjectMulti } from '@/store/Academics/SubjectManagement/subjectManagement.thunks';
import * as Yup from 'yup';
import api from '@/api';

export type CreateSubjectMultipleProps = {
  onBackClick: () => void;
};

export type CreateSubjectMultipleState = {
  subjects: SubjectCreateRow[];
};

const CreateSubjectMultipleRoot = styled.div`
  padding: 1rem;

  .Card {
    height: calc(100vh - 160px);
    display: flex;
    flex-direction: column;

    form {
      height: calc(100% - 40px);
      display: flex;
      flex-direction: column;

      .form-container {
        flex-grow: 1;
        height: calc(100% - 84px);

        .MuiTableContainer-root {
          height: 100%;
          overflow: auto;
          overflow-x: hidden;
        }
      }

      .button-container {
        border-top: 1px solid #ddd;
      }
    }
  }
`;

const formValidationSchema = Yup.object({
  subjects: Yup.array().of(
    Yup.object({
      subjectName: Yup.string()
        .required('Please enter Subject name')
        .test('exists', 'Subject name already used', async (val) => {
          try {
            if (val) {
              const existsResponse = await api.SubjectManagement.SubjectNameExists(val);
              return !existsResponse.data;
            }

            return true;
          } catch {
            return true;
          }
        }),
    })
  ),
});

function CreateSubjectMultiple({ onBackClick }: CreateSubjectMultipleProps) {
  const dispatch = useAppDispatch();
  const { confirm } = useConfirm();
  const [error, setError] = useState<string | null>(null);
  const scrollContainerRef = useRef<any>(null);
  const textBoxRefs = useRef<(HTMLInputElement | null)[]>([]);

  const initialRowKey = uuidv4();

  const getNewRow = useCallback(
    (): SubjectCreateRow => ({ rowKey: uuidv4(), subjectName: '', subjectDescription: '', subjectStatus: 1 }),
    []
  );

  const defaultRow: SubjectCreateRow = {
    rowKey: initialRowKey,
    subjectName: '',
    subjectDescription: '',
    subjectStatus: 1,
  };

  const { values, handleChange, handleBlur, handleSubmit, setFieldValue, touched, errors, resetForm } =
    useFormik<CreateSubjectMultipleState>({
      initialValues: {
        subjects: [defaultRow],
      },
      onSubmit: async (data) => {
        try {
          const response = await dispatch(addNewSubjectMulti(data.subjects)).unwrap();

          if (response.inserted) {
            const successMessage = <SuccessMessage message={`${data.subjects.length} subjects created successfully`} />;
            await confirm(successMessage, 'subjects Created', { okLabel: 'Ok', showOnlyOk: true });
            resetForm();
            setFieldValue('subjects', []);
            if (response.inserted) {
              setError(null);
              onBackClick();
            } else {
              setError('Something went wrong in creating subjects');
            }
          }
        } catch {
          setError('Something went wrong in creating subjects');
        }
      },
      validateOnBlur: false,
      validationSchema: formValidationSchema,
      validate: (subjectVals) => {
        const errorObj: any = {};
        subjectVals.subjects.forEach(async (subjectRow, rowIndex, arr) => {
          if (
            arr.some(
              (x, i) => subjectRow.subjectName !== '' && x.subjectName === subjectRow.subjectName && i !== rowIndex
            )
          ) {
            if (!errorObj.subjects) {
              errorObj.subjects = [];
            }
            errorObj.subjects[rowIndex] = {};
            errorObj.subjects[rowIndex].subjectName = 'Duplicate Subject name';
          }
        });
        return errorObj;
      },
    });

  useEffect(() => {
    textBoxRefs.current[values.subjects.length - 1]?.focus();
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollTop = scrollContainerRef.current.scrollHeight;
    }
  }, [values.subjects.length]);

  const handleAddNewRow = () => {
    const newRow = getNewRow();
    values.subjects = [...values.subjects, newRow];
    setFieldValue('subjects', values.subjects);
  };

  const handleRowDelete = (rowkey: string) => {
    const updatedRows = values.subjects.filter((x) => x.rowKey !== rowkey);
    setFieldValue('subjects', updatedRows);
  };

  const hasSubjectNameFieldError = (rowIndex: number) => {
    if (touched.subjects && touched.subjects.length > 0 && errors.subjects && errors.subjects.length > 0) {
      return (
        !!touched.subjects[rowIndex]?.subjectName &&
        !!(errors.subjects as FormikErrors<SubjectCreateRow>[])[rowIndex]?.subjectName
      );
    }

    return false;
  };

  const getSubjectNameFieldError = (rowIndex: number) => {
    return (errors.subjects as FormikErrors<SubjectCreateRow>[])[rowIndex]?.subjectName;
  };

  const handleReset = async () => {
    if (await confirm('Are you sure you want to reset form?', 'Reset?')) {
      resetForm();
      setFieldValue('subjects', [defaultRow]);
    }
  };

  const handleResetForm = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    handleReset();
  };

  return (
    <Page title="Create Multiple subjects">
      <CreateSubjectMultipleRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" alignItems="center" spacing={2}>
            <Typography variant="h6">Add Multiple Subject</Typography>
            <Button variant="outlined" size="small" onClick={onBackClick}>
              <MdArrowBack /> Back
            </Button>
          </Stack>
          <form noValidate onSubmit={handleSubmit} onReset={handleResetForm}>
            <div className="form-container">
              {!!error && (
                <Alert color="error" sx={{ marginBottom: '10px' }}>
                  {error}
                </Alert>
              )}
              <TableContainer component={Paper} ref={scrollContainerRef}>
                <Table sx={{ minWidth: 650 }} aria-label="simple table" stickyHeader>
                  <TableHead>
                    <TableRow>
                      <TableCell>Subject Name</TableCell>
                      <TableCell>Subject Desc.</TableCell>
                      <TableCell>Subject Status</TableCell>
                      <TableCell>&nbsp;</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {values.subjects.map((subjectRow, rowIndex) => (
                      <TableRow
                        key={subjectRow.rowKey}
                        sx={rowIndex === values.subjects.length - 1 ? { '& td': { border: 0 } } : null}
                      >
                        <TableCell>
                          <TextField
                            name={`subjects[${rowIndex}].subjectName`}
                            // label="Subject Name"
                            value={subjectRow.subjectName}
                            variant="outlined"
                            onChange={handleChange}
                            onBlur={handleBlur}
                            placeholder="Subject name"
                            fullWidth
                            error={hasSubjectNameFieldError(rowIndex)}
                            inputRef={(el) => {
                              textBoxRefs.current[rowIndex] = el;
                            }}
                            InputProps={{
                              endAdornment: hasSubjectNameFieldError(rowIndex) && (
                                <Tooltip title={getSubjectNameFieldError(rowIndex)} arrow>
                                  <ErrorIcon color="error" />
                                </Tooltip>
                              ),
                            }}
                          />
                        </TableCell>
                        <TableCell>
                          <TextField
                            name={`subjects[${rowIndex}].subjectDescription`}
                            // label="Subject Description"
                            value={subjectRow.subjectDescription}
                            variant="outlined"
                            onChange={handleChange}
                            onBlur={handleBlur}
                            placeholder="Subject description"
                            fullWidth
                          />
                        </TableCell>
                        <TableCell>
                          <Select
                            name={`subjects[${rowIndex}].subjectStatus`}
                            value={subjectRow.subjectStatus}
                            onChange={handleChange}
                            fullWidth
                          >
                            {STATUS_OPTIONS.map((opt) => (
                              <MenuItem key={opt.id} value={opt.id}>
                                {opt.name}
                              </MenuItem>
                            ))}
                          </Select>
                        </TableCell>
                        <TableCell>
                          <IconButton
                            aria-label="Delete Row"
                            onClick={() => handleRowDelete(subjectRow.rowKey)}
                            disabled={values.subjects.length === 1}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                    <TableRow sx={{ '&:last-child td, &:last-child th': { border: 0 } }}>
                      <TableCell colSpan={4}>
                        <Button type="button" variant="outlined" startIcon={<AddIcon />} onClick={handleAddNewRow}>
                          Add New Row
                        </Button>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            </div>
            <Stack className="button-container" direction="row" justifyContent="end" gap={1} padding={2}>
              <Button type="button" color="secondary" variant="contained" onClick={handleReset}>
                Reset
              </Button>
              <Button color="primary" variant="contained" type="submit">
                Save
              </Button>
            </Stack>
          </form>
        </Card>
      </CreateSubjectMultipleRoot>
    </Page>
  );
}

export default CreateSubjectMultiple;
