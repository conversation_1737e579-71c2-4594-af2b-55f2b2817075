/* eslint-disable react/no-array-index-key */
import React, { useContext, useState } from 'react';
import { Button, Paper, Typography, TextField, Grid, Chip, Stack, ChipProps } from '@mui/material';
import CalendarContext from '@/contexts/CalendarContext';

export const labelsClasses = [
  { name: 'Event', color: 'info' },
  { name: 'Holiday', color: 'error' },
  { name: 'Exam', color: 'success' },
  { name: 'Online Class', color: 'warning' },
  { name: 'Other', color: 'secondary' },
];

const EventModal = () => {
  const { setShowEventModal, daySelected, dispatchCalEvent, selectedEvent } = useContext(CalendarContext);

  const [title, setTitle] = useState(selectedEvent ? selectedEvent.title : '');
  const [description, setDescription] = useState(selectedEvent ? selectedEvent.description : '');
  const [selectedLabel, setSelectedLabel] = useState(
    selectedEvent ? labelsClasses.find((lbl) => lbl.color === selectedEvent.label) : labelsClasses[0]
  );
  const [selectedName, setSelectedName] = useState(
    selectedEvent ? labelsClasses.find((lbl) => lbl.name === selectedEvent.type) : labelsClasses[0]
  );

  const handleSubmit = (e: any) => {
    e.preventDefault();
    const calendarEvent = {
      title,
      description,
      label: selectedLabel ? selectedLabel.color : labelsClasses[0].color,
      day: daySelected?.valueOf(),
      id: selectedEvent ? selectedEvent.id : Date.now(),
      type: selectedName ? selectedName.name : labelsClasses[0].name,
    };
    if (selectedEvent) {
      dispatchCalEvent({ type: 'update', payload: calendarEvent });
    } else {
      dispatchCalEvent({ type: 'push', payload: calendarEvent });
    }

    setShowEventModal(false);
  };

  return (
    // <Box
    //   sx={{
    //     height: '100vh',
    //     width: '100%',
    //     position: 'fixed',
    //     left: 0,
    //     top: 0,
    //     display: 'flex',
    //     justifyContent: 'center',
    //     alignItems: 'center',
    //     backgroundColor: 'rgba(0,0,0,0.5)',
    //     zIndex: 100,
    //   }}
    // >
    <Paper
      sx={{
        borderRadius: 'lg',
        boxShadow: '2xl',
        minWidth: 300,
        padding: '2px',
        maxWidth: '100%',
      }}
    >
      <form onSubmit={handleSubmit}>
        <Grid container spacing={3} sx={{ p: 3 }}>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Event Title"
              variant="standard"
              value={title}
              required
              onChange={(e) => setTitle(e.target.value)}
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Event Description"
              variant="standard"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
            />
          </Grid>
          <Grid item xs={12}>
            <Typography variant="body2">{daySelected?.format('dddd, MMMM DD')}</Typography>
          </Grid>
          <Grid item xs={12}>
            <div style={{ display: 'flex', gap: '4px' }}>
              <Grid container spacing={1} rowSpacing={1.5}>
                {labelsClasses.map((lblClass, i) => (
                  <Grid item xs="auto">
                    <Button
                      size="small"
                      key={i}
                      onClick={() => {
                        setSelectedLabel(lblClass);
                        setSelectedName(lblClass);
                      }}
                      variant={selectedLabel?.color === lblClass.color ? 'contained' : 'outlined'}
                      color={lblClass.color as ChipProps['color']}
                      sx={{
                        // border: selectedLabel?.color === lblClass.color ? '3px solid grey' : null,
                        // color: selectedLabel?.color !== lblClass.color ? '#fff' : '#000',
                        cursor: 'pointer',
                      }}
                    >
                      {lblClass.name}
                    </Button>
                  </Grid>
                ))}
              </Grid>
            </div>
          </Grid>
        </Grid>
        <Stack spacing={2} direction="row" justifyContent="flex-end" sx={{ pr: 3, py: 2 }}>
          {selectedEvent && (
            <Button
              variant="contained"
              color="info"
              onClick={() => {
                dispatchCalEvent({
                  type: 'delete',
                  payload: selectedEvent,
                });
                setShowEventModal(false);
              }}
            >
              Delete
            </Button>
          )}
          <Button type="submit" variant="contained" color="primary">
            Save
          </Button>
        </Stack>
      </form>
    </Paper>
    // </Box>
  );
};

export default EventModal;
