/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  FormControl,
  FormControlLabel,
  Radio,
  RadioGroup,
} from '@mui/material';
import styled from 'styled-components';
import typography from '@/theme/typography';
import Popup from '@/components/shared/Popup/Popup';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';

const EnrollBooksRoot = styled.div`
  min-height: calc(100vh - ${TOP_BAR_HEIGHT});
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  h6 {
    font-weight: 600;
  }
  .MuiTextField-root:hover .MuiInputBase-input {
    cursor: text;
  }
`;

function EnrollBooks() {
  const [popup, setPopup] = React.useState(false);

  const handleClickOpen = () => setPopup(true);
  const handleClickClose = () => setPopup(false);

  return (
    <Page title="Create Login">
      <EnrollBooksRoot>
        <Card elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Typography variant="h6" fontSize={17}>
            Add New Book
          </Typography>
          <Divider />
          <form noValidate>
            <Grid pb={4} pt={2} container rowSpacing={3}>
              <Grid item md={6} xs={12}>
                <FormControl sx={{ minWidth: { xs: '100%', sm: '90%' } }}>
                  <Typography variant="h6" fontSize={14}>
                    Book Code
                  </Typography>
                  <TextField placeholder="Enter Book Code" />
                </FormControl>
              </Grid>
              <Grid item md={6} xs={12}>
                <FormControl sx={{ minWidth: { xs: '100%', sm: '90%' } }}>
                  <Typography variant="h6" fontSize={14}>
                    Title of the Book
                  </Typography>
                  <TextField placeholder="Enter Book Title" />
                </FormControl>
              </Grid>
              <Grid item md={6} xs={12}>
                <FormControl sx={{ minWidth: { xs: '100%', sm: '90%' } }}>
                  <Typography variant="h6" fontSize={14}>
                    Author
                  </Typography>
                  <TextField placeholder="Enter Author Name" />
                </FormControl>
              </Grid>
              <Grid item md={6} xs={12}>
                <FormControl sx={{ minWidth: { xs: '100%', sm: '90%' } }}>
                  <Typography variant="h6" fontSize={14}>
                    Year of Publication
                  </Typography>
                  <TextField placeholder="Enter Year" />
                </FormControl>
              </Grid>
              <Grid item md={6} xs={12}>
                <FormControl sx={{ minWidth: { xs: '100%', sm: '90%' } }}>
                  <Typography variant="h6" fontSize={14}>
                    Price
                  </Typography>
                  <TextField placeholder="Enter Price" />
                </FormControl>
              </Grid>
              <Grid item md={6} xs={12}>
                <FormControl>
                  <Typography variant="h6" fontSize={14}>
                    Status
                  </Typography>
                  <RadioGroup row aria-labelledby="demo-row-radio-buttons-group-label" name="row-radio-buttons-group">
                    <FormControlLabel value="v-good" control={<Radio />} label="V.Good" />
                    <FormControlLabel value="good" control={<Radio />} label="Good" />
                    <FormControlLabel value="avg" control={<Radio />} label="Avg" />
                    <FormControlLabel value="bad" control={<Radio />} label="Bad" />
                    <FormControlLabel value="v-bad" control={<Radio />} label="V.Bad" />
                  </RadioGroup>
                </FormControl>
              </Grid>
              <Grid item md={6} xs={12}>
                <FormControl sx={{ minWidth: { xs: '100%', sm: '90%' } }}>
                  <Typography variant="h6" fontSize={14}>
                    Category
                  </Typography>
                  <Autocomplete
                    options={[]}
                    renderInput={(params) => <TextField {...params} placeholder="Select Category" />}
                  />
                </FormControl>
              </Grid>
              <Grid item md={6} xs={12}>
                <FormControl sx={{ minWidth: { xs: '100%', sm: '90%' } }}>
                  <Typography variant="h6" fontSize={14}>
                    Book Location
                  </Typography>
                  <Autocomplete
                    options={[]}
                    renderInput={(params) => <TextField {...params} placeholder="Select Book Location" />}
                  />
                </FormControl>
              </Grid>
              <Grid item md={6} xs={12}>
                <FormControl sx={{ minWidth: { xs: '100%', sm: '90%' } }}>
                  <Typography variant="h6" fontSize={14}>
                    About Book
                  </Typography>
                  <TextField placeholder="Enter Book Summary" multiline minRows={3} />
                </FormControl>
              </Grid>
              <Grid item md={6} xs={12}>
                <FormControl sx={{ minWidth: { xs: '100%', sm: '90%' } }}>
                  <Typography variant="h6" fontSize={14}>
                    Remarks
                  </Typography>
                  <TextField placeholder="Enter Book Remarks" multiline minRows={3} />
                </FormControl>
              </Grid>
            </Grid>
          </form>
          <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' } }}>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="secondary" sx={{ fontFamily: typography.fontFamily }}>
                Cancel
              </Button>
              <Button onClick={handleClickOpen} variant="contained" color="primary">
                Save
              </Button>
            </Stack>
          </Box>
        </Card>
        <Popup
          size="xs"
          state={popup}
          onClose={handleClickClose}
          popupContent={<SuccessMessage message="TimeTable Updated Successfully" />}
        />
      </EnrollBooksRoot>
    </Page>
  );
}

export default EnrollBooks;
