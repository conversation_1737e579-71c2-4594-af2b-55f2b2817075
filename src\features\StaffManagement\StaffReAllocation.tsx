/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Typography,
  Card,
  Checkbox,
} from '@mui/material';
import styled from 'styled-components';
import { YEAR_SELECT } from '@/config/Selection';
import Popup from '@/components/shared/Popup/Popup';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import { teacherData } from '@/config/TableData';

const StaffReAllocationRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  h6 {
    font-weight: 600;
  }
  .MuiTextField-root:hover .MuiInputBase-input {
    cursor: text;
  }
`;

function StaffReAllocation() {
  const [popup, setPopup] = React.useState(false);

  const handleClickOpen = () => setPopup(true);
  const handleClickClose = () => setPopup(false);

  return (
    <Page title="Manage Year">
      <StaffReAllocationRoot>
        <Card elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Typography variant="h6" fontSize={17}>
            Staff Re-Allocation
          </Typography>
          <Divider />
          <Grid pb={4} pt={2} container spacing={3}>
            <Grid item lg={8} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Select Year
              </Typography>
              <Autocomplete
                options={YEAR_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select" />}
              />
            </Grid>
            <Grid item lg={4} xs={12}>
              <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                <Button variant="contained" color="secondary" fullWidth>
                  Reset
                </Button>
                <Button variant="contained" color="primary" fullWidth>
                  Search
                </Button>
              </Stack>
            </Grid>
          </Grid>
          <Box mb={1}>
            <Paper
              sx={{
                border: `1px solid #e8e8e9`,
                width: '100%',
                overflow: 'auto',
                '&::-webkit-scrollbar': {
                  width: 0,
                },
              }}
            >
              <TableContainer
                sx={{
                  maxHeight: 410,
                  width: { xs: '700px', md: '100%' },
                  '&::-webkit-scrollbar': {
                    width: 0,
                  },
                }}
              >
                <Table stickyHeader aria-label="simple table">
                  <TableHead>
                    <TableRow>
                      <TableCell>
                        <Checkbox />
                      </TableCell>
                      <TableCell>Sl.No</TableCell>
                      <TableCell>Staff Name</TableCell>
                      <TableCell>Subjects</TableCell>
                      <TableCell>Academic Year</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {teacherData.map((r) => (
                      <TableRow hover key={r.id}>
                        <TableCell>
                          <Checkbox />
                        </TableCell>
                        <TableCell>{r.id}</TableCell>
                        <TableCell>{r.name}</TableCell>
                        <TableCell>{r.subjects}</TableCell>
                        <TableCell>2022-2023</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Paper>
          </Box>
          <Typography variant="h6" fontSize={14}>
            Re-Allocate To :
          </Typography>
          <Stack
            sx={{ flexDirection: { md: 'row', xs: 'column' } }}
            direction="row"
            alignItems="center"
            justifyContent="space-between"
          >
            <Box sx={{ width: { md: '30%', xs: '100%' } }}>
              <Autocomplete
                options={YEAR_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select" />}
              />
            </Box>
            <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pt: { xs: 3, md: 0 } }}>
              <Stack spacing={2} direction="row">
                <Button variant="contained" color="secondary">
                  Cancel
                </Button>
                <Button onClick={handleClickOpen} variant="contained" color="primary">
                  Allocate Staff
                </Button>
              </Stack>
            </Box>
          </Stack>
        </Card>
        <Popup
          size="xs"
          state={popup}
          onClose={handleClickClose}
          popupContent={<SuccessMessage message="Allocated Successfully" />}
        />
      </StaffReAllocationRoot>
    </Page>
  );
}

export default StaffReAllocation;
