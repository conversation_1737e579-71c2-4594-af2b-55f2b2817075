/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  useTheme,
  IconButton,
  Collapse,
  Tooltip,
  FormControl,
  Chip,
  Snackbar,
  Alert,
} from '@mui/material';
import styled from 'styled-components';
import DirectionsBusRoundedIcon from '@mui/icons-material/DirectionsBusRounded';
import useSettings from '@/hooks/useSettings';
import AutorenewOutlinedIcon from '@mui/icons-material/AutorenewOutlined';
import { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import DateSelect from '@/components/shared/Selections/DateSelect';
import MenuEditDelete from '@/components/shared/Selections/MenuEditDelete';
import CreateScheduleList from '@/features/LiveClass/ScheduleList/CreateScheduleList';
import InsertDriveFileIcon from '@mui/icons-material/InsertDriveFile';
import Map from '@/features/VehicleTracking/VehicleList/Map';

const VehiclekRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  .student_card {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  }

  .Card {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    height: calc(100vh - 110px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 40px);
      flex-grow: 1;

      .card-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: auto;
        ::-webkit-scrollbar {
          width: 0px;
        }

        .MuiTableContainer-root {
          max-height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

function VehicleTrack() {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';

  return (
    <Page title="Schedule List">
      <VehiclekRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 1, md: 2 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={20}>
              Track
            </Typography>
          </Stack>
          <Stack>
            <Divider />
          </Stack>
          <div className="card-main-body">
            <Box className="card-container" my={2}>
              <Box display="flex" gap={2}>
                <Stack direction="column" gap={2}>
                  {[1, 2, 3, 4, 5, 6].map((m) => (
                    <Card
                      className="student_card"
                      sx={{ p: 2, boxShadow: 0, width: 250, border: `1px solid ${theme.palette.grey[300]}` }}
                    >
                      <Box display="flex" alignItems="start" gap={2} mb={2}>
                        <Stack sx={{ backgroundColor: theme.palette.success.lighter, borderRadius: 50, p: 0.5 }}>
                          <DirectionsBusRoundedIcon color="success" />
                        </Stack>
                        <Stack>
                          <Typography variant="subtitle2">Physics</Typography>
                          <Typography variant="subtitle1" fontSize={11} color="GrayText">
                            KL 09 PKD 4587
                          </Typography>
                          <Typography variant="subtitle1" fontSize={11} color="GrayText">
                            Palakkad to Ottapalam
                          </Typography>
                        </Stack>
                      </Box>

                      <Button fullWidth variant="contained" color="success" size="small">
                        Track
                      </Button>
                    </Card>
                  ))}
                </Stack>
                <Map />
              </Box>
            </Box>
          </div>
        </Card>
      </VehiclekRoot>
    </Page>
  );
}

export default VehicleTrack;
