import React, { useCallback, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  useTheme,
  FormControl,
  TextField,
  Box,
  Typography,
  Stack,
  Button,
  Card,
  Grid,
  IconButton,
  Divider,
} from '@mui/material';
import styled from 'styled-components';
import AudioPlayer from 'react-h5-audio-player';
import CloseIcon from '@mui/icons-material/Close';
import ErrorVoice1 from '@/assets/MessageIcons/error-message.gif';
import ErrorVoice2 from '@/assets/MessageIcons/error-msg-template.gif';
import SaveFile from '@/assets/MessageIcons/save-file.gif';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import { createVoice, fileUpload } from '@/store/VoiceMessage/voiceMessage.thunk';
import { VoiceCreateRequest } from '@/types/VoiceMessage';
import useAuth from '@/hooks/useAuth';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import useSettings from '@/hooks/useSettings';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import { getVoiceSubmitting } from '@/config/storeSelectors';
import { useAppSelector } from '@/hooks/useAppSelector';
import { ErrorMessage } from '@/components/shared/Popup/ErrorMessage';
import { ErrorIcon } from '@/theme/overrides/CustomIcons';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import FilesUpload from '@/components/shared/Selections/FilesUpload';
import LoadingButton from '@mui/lab/LoadingButton';
// import { UplodedeFile } from './UplodedFile';
import SaveIcon from '@mui/icons-material/Save';

const CreateVoiceMessageRoot = styled.div`
  padding: 1rem;

  li {
    padding-bottom: 5px;
  }
  .sub-heading {
    font-size: 90%;
    color: ${(props) => props.theme.palette.grey[600]};
  }
  .Card {
    min-height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }
  }
  .MuiIconButton-root:hover {
    background-color: transparent;
  }
`;

function CreateVoiceMessage() {
  const { user } = useAuth();
  const adminId: number | undefined = user?.accountId;
  const dispatch = useAppDispatch();
  const theme = useTheme();
  const isLight = useSettings().themeMode === 'light';
  const { confirm } = useConfirm();
  const [uploaded, setUploaded] = useState<File[]>([]);
  const isSubmitting = useAppSelector(getVoiceSubmitting);
  // const [image, setImage] = useState();
  // const [fileName, setFileName] = useState<:string>([{ name: '' }]);

  const handleSave = useCallback(
    async (value: VoiceCreateRequest) => {
      console.log('value', value);
      try {
        let uploadedFilesDetails = null;

        // Upload files if any
        if (uploaded.length !== 0) {
          const formData = new FormData();
          uploaded.forEach((file) => {
            formData.append('files', file);
          });

          const uploadResponse = await dispatch(
            fileUpload({
              files: formData,
            })
          ).unwrap();

          uploadedFilesDetails = uploadResponse.voiceFile;
          console.log('uploadedFilesDetails', uploadedFilesDetails);
        }
        // Add uploaded file details to the value
        const rest = { ...value, voiceFile: uploadedFilesDetails };
        console.log('rest::::', rest);

        // Create notification
        const response = await dispatch(createVoice(rest)).unwrap();
        console.log('response', response);

        const Success = response.result === 'SUCCESS';
        const Exist = response.result === 'EXIST';

        if (response.id > 0 || Success) {
          const successMessage = <SuccessMessage icon={SaveFile} message="Voice Message created successfully" />;
          await confirm(successMessage, '', { okLabel: 'Ok', showOnlyOk: true });
          setUploaded([]);
        } else if (Exist) {
          const errorMessage = <ErrorMessage icon={ErrorVoice1} message="Voice Message already created" />;
          await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        }
      } catch (error) {
        const errorMessage = <ErrorMessage icon={ErrorVoice2} message="Something went wrong, please try again." />;
        await confirm(errorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
        console.error(error);
      }
    },
    [confirm, dispatch, uploaded]
  );
  const handleRemoveFile = (index: number) => {
    setUploaded((prevFiles) => {
      const newFiles = [...prevFiles];
      newFiles.splice(index, 1);
      return newFiles;
    });
  };
  const CreateEditVoiceValidationSchema = Yup.object({
    voiceTitle: Yup.string().required('Please enter title'),
    voiceFile: Yup.mixed()
      .test('fileCount', 'Please upload only one file', (value) => !value || uploaded.length <= 1)
      .test(
        'fileSize',
        'File size is too large (maximum size is 5 MB)',
        (value) => !value || value.size <= 5 * 1024 * 1024 // 5 MB
      )
      .required('Please upload a voice file'),
    // notificationStatus: Yup.number().oneOf([0, 1], 'Please select status'),
  });

  const {
    values: { voiceTitle },
    handleChange,
    handleBlur,
    handleSubmit,
    handleReset,
    setFieldValue,
    touched,
    errors,
  } = useFormik<VoiceCreateRequest>({
    initialValues: {
      voiceTitle: '',
      voiceFile: '',
      adminId,
      templateId: '',
    },
    validationSchema: CreateEditVoiceValidationSchema,
    onSubmit: (values) => {
      handleSave(values);
      console.log('values', values);
    },
    validateOnBlur: false,
  });
  return (
    <Page title="Voice Message Create">
      <CreateVoiceMessageRoot>
        <Card className="Card" elevation={5} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Typography variant="h6" fontSize={17}>
            Voice Message Create
          </Typography>
          <Divider />
          <form noValidate onSubmit={handleSubmit} onReset={handleReset}>
            <Stack py={2}>
              <Grid container>
                <Grid item xs={12} lg={6}>
                  <FormControl fullWidth>
                    <Typography variant="subtitle1" fontSize={12}>
                      Voice Mail Title
                    </Typography>
                    <TextField
                      placeholder="Enter title"
                      name="voiceTitle"
                      value={voiceTitle}
                      onBlur={handleBlur}
                      onChange={handleChange}
                      error={touched.voiceTitle && !!errors.voiceTitle}
                      helperText={touched.voiceTitle && errors.voiceTitle}
                      disabled={isSubmitting}
                      InputProps={{
                        endAdornment: touched.voiceTitle && !!errors.voiceTitle && <ErrorIcon color="error" />,
                      }}
                    />
                  </FormControl>
                </Grid>
              </Grid>
            </Stack>
            <Box gap={5}>
              <Stack width={{ xs: '100%', lg: '50%' }}>
                <FormControl fullWidth>
                  <Typography variant="subtitle1" fontSize={12}>
                    Upload Files
                  </Typography>
                  <FilesUpload
                    height={200}
                    title="
                  Voice call can send between 09:10 am to 08:30 pm File format should be .mp3 and .Wav format file
                  size should not exceed 5 MB."
                    name="voiceFile"
                    accept=".mp3,audio/*"
                    multiple={false}
                    setUploaded={setUploaded}
                    onChange={(event) => {
                      setFieldValue('voiceFile', event.currentTarget.files?.[0]);
                    }}
                    error={touched.voiceFile && !!errors.voiceFile}
                    helperText={touched.voiceFile && errors.voiceFile}
                  />
                </FormControl>
              </Stack>

              <Box mt={2} display="flex" flexWrap="wrap" gap={1}>
                {uploaded.map((file, index: number) => (
                  <Box
                    // key={uuidv4()}
                    sx={{
                      position: 'relative',
                      height: 100,
                      width: { xs: '100%', lg: '50%' },
                      px: 0.09,
                    }}
                  >
                    <IconButton
                      size="small"
                      sx={{
                        position: 'absolute',
                        right: 5,
                        top: 5,
                        zIndex: 1,
                        p: 0,
                        border: `1px solid ${theme.palette.grey[300]}`,
                        backgroundColor: theme.palette.common.white,
                        '&:hover': {
                          backgroundColor: theme.palette.error.lighter,
                        },
                      }}
                      onClick={() => handleRemoveFile(index)}
                    >
                      <CloseIcon fontSize="small" sx={{ '&:hover': { color: 'red' } }} />
                    </IconButton>

                    <AudioPlayer
                      // autoPlay
                      muted
                      style={{
                        borderRadius: 10,
                        backgroundColor: isLight ? theme.palette.grey[300] : theme.palette.grey[800],
                      }}
                      src={URL.createObjectURL(file)}
                      onPlay={(e) => console.log('onPlay')}
                      // other props here
                    />
                  </Box>
                ))}
              </Box>
            </Box>

            <Box display="flex" sx={{ justifyContent: { xs: 'right', md: 'right' }, pr: { lg: '5' }, pt: 3 }}>
              <Stack spacing={2} direction="row">
                <Button disabled={isSubmitting} variant="contained" color="secondary">
                  Cancel
                </Button>
                <LoadingButton
                  fullWidth
                  loadingPosition="start"
                  loading={isSubmitting}
                  startIcon={<SaveIcon />} //
                  variant="contained"
                  color="primary"
                  type="submit"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Saving...' : 'Save'}
                </LoadingButton>
              </Stack>
            </Box>
          </form>
        </Card>
      </CreateVoiceMessageRoot>
      {/* <TemporaryDrawer onclickdrwer={onclick} open="right" /> */}
    </Page>
  );
}

export default CreateVoiceMessage;
