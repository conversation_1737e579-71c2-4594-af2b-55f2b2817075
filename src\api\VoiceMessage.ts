import {
  DeleteMultipleVoiceMessageType,
  SendToParentsResponse,
  SendVoiceMessageToGroupWiseRequest,
  SendVoiceMessageToPublicGroupWiseRequest,
  SendVoiceToClassDivisionRequest,
  SendVoiceToClassSectionRequest,
  SendVoiceToConveyorRequest,
  SendVoiceToGroupRequest,
  SendVoiceToParentsRequest,
  SendVoiceToPtaRequest,
  SendVoiceToPublicGroupRequest,
  SendVoiceToStaffRequest,
  UplaodFilesType,
  VoiceCreateRequest,
  VoiceDataType,
  VoiceMessageFilter,
} from '@/types/VoiceMessage';
import { CreateResponse, DeleteResponse, SendResponse } from '@/types/Common';
import ApiUrls from '@/config/ApiUrls';
import { APIResponse } from './base/types';
import { privateApi } from './base/api';

async function CreateNewVoice(request: VoiceCreateRequest): Promise<APIResponse<CreateResponse>> {
  const response = await privateApi.post<CreateResponse>(ApiUrls.CreateVoice, request);
  return response;
}

async function FileUpload(files: File): Promise<APIResponse<UplaodFilesType>> {
  try {
    console.log('files', files);
    const response = await privateApi.post<any>(ApiUrls.VoiceFileUpload, files, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    console.log(' response.data', response.data);
    return response.data;
  } catch (error) {
    throw new Error('Failed to upload files');
  }
}

async function GetVoiceMessageList(request: VoiceMessageFilter): Promise<APIResponse<VoiceDataType[]>> {
  const response = await privateApi.post<VoiceDataType[]>(ApiUrls.GetVoiceMessageList, request);
  console.log('response', response);
  console.log('request from Api', request);
  return response;
}

async function DeleteVoiceList(adminId: number | undefined, voiceId: number): Promise<APIResponse<DeleteResponse>> {
  if (adminId === undefined || voiceId === undefined) {
    throw new Error('adminId and VoiceId are required');
  }
  const url = ApiUrls.DeleteVoiceMessageList.replace('{adminId}', adminId.toString()).replace(
    '{voiceId}',
    voiceId.toString()
  );
  const response = await privateApi.get<DeleteResponse>(url);
  return response;
}

async function DeleteMultipleVoiceList(
  request: DeleteMultipleVoiceMessageType[]
): Promise<APIResponse<DeleteResponse>> {
  const response = await privateApi.post<DeleteResponse>(ApiUrls.DeleteMultipleVoiceMessageList, request);
  return response;
}

//  Send to parent
async function SendVoiceMessageToParents(
  request: SendVoiceToParentsRequest
): Promise<APIResponse<SendToParentsResponse>> {
  const response = await privateApi.post<SendToParentsResponse>(ApiUrls.SendVoiceMessageToParents, request);
  return response;
}

//  Send to Staff
async function SendVoiceMessageToStaff(
  request: SendVoiceToStaffRequest[]
): Promise<APIResponse<SendVoiceToStaffRequest>> {
  const response = await privateApi.post<SendVoiceToStaffRequest>(ApiUrls.SendVoiceMessageToStaff, request);
  return response;
}

// Send to Pta
async function SendVoiceMessageToPTA(request: SendVoiceToPtaRequest[]): Promise<APIResponse<SendVoiceToPtaRequest>> {
  const response = await privateApi.post<SendVoiceToPtaRequest>(ApiUrls.SendVoiceMessageToPTA, request);
  return response;
}

// Send to Conveyor
async function SendVoiceMessageToConveyor(
  request: SendVoiceToConveyorRequest[]
): Promise<APIResponse<SendVoiceToConveyorRequest>> {
  const response = await privateApi.post<SendVoiceToConveyorRequest>(ApiUrls.SendVoiceMessageToConveyor, request);
  return response;
}

// Send to Group
async function SendVoiceMessageToGroup(
  request: SendVoiceToGroupRequest[]
): Promise<APIResponse<SendVoiceToGroupRequest>> {
  const response = await privateApi.post<SendVoiceToGroupRequest>(ApiUrls.SendVoiceMessageToGroup, request);
  return response;
}
// Send to Public Group
async function SendVoiceMessageToPublicGroupMembers(
  request: SendVoiceToPublicGroupRequest[]
): Promise<APIResponse<SendVoiceToPublicGroupRequest>> {
  const response = await privateApi.post<SendVoiceToPublicGroupRequest>(
    ApiUrls.SendVoiceMessageToPublicGroupMembers,
    request
  );
  return response;
}
// Send to Class Division
async function SendVoiceMessageToClassDivision(
  request: SendVoiceToClassDivisionRequest[]
): Promise<APIResponse<SendVoiceToClassDivisionRequest>> {
  const response = await privateApi.post<SendVoiceToClassDivisionRequest>(
    ApiUrls.SendVoiceMessageToClassDivision,
    request
  );
  return response;
}
// Send to Class Section
async function SendVoiceMessageToClassSection(
  request: SendVoiceToClassSectionRequest[]
): Promise<APIResponse<SendVoiceToClassSectionRequest>> {
  const response = await privateApi.post<SendVoiceToClassSectionRequest>(
    ApiUrls.SendVoiceMessageToClassSection,
    request
  );
  return response;
}
// Send to group wise
async function SendVoiceMessageToGroupWise(
  request: SendVoiceMessageToGroupWiseRequest[]
): Promise<APIResponse<SendVoiceMessageToGroupWiseRequest>> {
  const response = await privateApi.post<SendVoiceMessageToGroupWiseRequest>(
    ApiUrls.SendVoiceMessageToGroupWise,
    request
  );
  return response;
}
// Send to Public Group Wise
async function SendVoiceMessageToPublicGroupWise(
  request: SendVoiceMessageToPublicGroupWiseRequest[]
): Promise<APIResponse<SendVoiceMessageToPublicGroupWiseRequest>> {
  const response = await privateApi.post<SendVoiceMessageToPublicGroupWiseRequest>(
    ApiUrls.SendVoiceMessageToPublicGroupWise,
    request
  );
  return response;
}

// ============================== Send To All ===================================
//

async function SendVoiceMessageToAllParents(
  adminId: number | undefined,
  academicId: number | undefined,
  voiceId: number | undefined
): Promise<APIResponse<SendResponse>> {
  if (adminId === undefined || academicId === undefined || voiceId === undefined) {
    throw new Error('adminId, academicId, and voiceId are required');
  }

  const url = ApiUrls.SendVoiceMessageToAllParents.replace('{adminId}', adminId.toString())
    .replace('{academicId}', academicId.toString())
    .replace('{voiceId}', voiceId.toString());

  const response = await privateApi.get<any>(url);
  return response;
}

async function SendVoiceMessageToAllStaff(
  adminId: number | undefined,
  academicId: number | undefined,
  voiceId: number | undefined
): Promise<APIResponse<SendResponse>> {
  if (adminId === undefined || academicId === undefined || voiceId === undefined) {
    throw new Error('adminId, academicId, and voiceId are required');
  }

  const url = ApiUrls.SendVoiceMessageToAllStaff.replace('{adminId}', adminId.toString())
    .replace('{academicId}', academicId.toString())
    .replace('{voiceId}', voiceId.toString());

  const response = await privateApi.get<any>(url);
  return response;
}

async function SendVoiceMessageToAllPta(
  adminId: number | undefined,
  academicId: number | undefined,
  voiceId: number | undefined
): Promise<APIResponse<SendResponse>> {
  if (adminId === undefined || academicId === undefined || voiceId === undefined) {
    throw new Error('adminId, academicId, and voiceId are required');
  }

  const url = ApiUrls.SendVoiceMessageToAllPta.replace('{adminId}', adminId.toString())
    .replace('{academicId}', academicId.toString())
    .replace('{voiceId}', voiceId.toString());

  const response = await privateApi.get<any>(url);
  return response;
}

async function SendVoiceMessageToAllConveyors(
  adminId: number | undefined,
  academicId: number | undefined,
  voiceId: number | undefined
): Promise<APIResponse<SendResponse>> {
  if (adminId === undefined || academicId === undefined || voiceId === undefined) {
    throw new Error('adminId, academicId, and voiceId are required');
  }

  const url = ApiUrls.SendVoiceMessageToAllConveyors.replace('{adminId}', adminId.toString())
    .replace('{academicId}', academicId.toString())
    .replace('{voiceId}', voiceId.toString());

  const response = await privateApi.get<any>(url);
  return response;
}

const methods = {
  CreateNewVoice,
  FileUpload,
  GetVoiceMessageList,
  DeleteVoiceList,
  DeleteMultipleVoiceList,
  SendVoiceMessageToParents,
  SendVoiceMessageToStaff,
  SendVoiceMessageToPTA,
  SendVoiceMessageToConveyor,
  SendVoiceMessageToGroup,
  SendVoiceMessageToPublicGroupMembers,
  SendVoiceMessageToClassDivision,
  SendVoiceMessageToClassSection,
  SendVoiceMessageToGroupWise,
  SendVoiceMessageToPublicGroupWise,
  SendVoiceMessageToAllParents,
  SendVoiceMessageToAllStaff,
  SendVoiceMessageToAllPta,
  SendVoiceMessageToAllConveyors,
};

export default methods;
