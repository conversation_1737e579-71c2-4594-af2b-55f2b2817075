import { ComponentProps } from 'react';
import { TableCell, TableSortLabel } from '@mui/material';

export type SortableTableCellProps = Omit<ComponentProps<typeof TableCell>, 'onClick'> & {
  columnName: string;
  direction: 'asc' | 'desc';
  active: boolean;
  onClick: (columnName: string) => void;
};

function SortableTableCell({ children, active, direction, columnName, onClick, ...rest }: SortableTableCellProps) {
  return (
    <TableCell
      onClick={() => {
        onClick(columnName);
      }}
      {...rest}
    >
      <TableSortLabel active={active} direction={direction} sx={{ mr: 2 }}>
        {children}
      </TableSortLabel>
    </TableCell>
  );
}

export default SortableTableCell;
