/* eslint-disable no-nested-ternary */
/* eslint-disable no-plusplus */
/* eslint-disable react/jsx-no-useless-fragment */
/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Grid,
  TextField,
  Box,
  Typography,
  useTheme,
  Stack,
  Button,
  Card,
  Divider,
  InputAdornment,
  Select,
  MenuItem,
  FormControl,
} from '@mui/material';

import styled from 'styled-components';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import Popup from '@/components/shared/Popup/Popup';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import MuiSwitch from '@/components/shared/Selections/MuiSwitch';
import { sendToAll, quicksendlinks1, quicksendlinks2, SendToAllTypes, QuickSendlinksTypes } from '@/config/smsData';
import { MESSAGE_TYPE_OPTIONS } from '@/config/Selection';
import ErrorMsg from '@/assets/MessageIcons/error-message.gif';
import ErrorMsg1 from '@/assets/MessageIcons/error-message1.gif';
import SuccessMsg from '@/assets/MessageIcons/message-success.gif';
import SaveFile from '@/assets/MessageIcons/save-file.gif';
import * as Yup from 'yup';
import { useFormik } from 'formik';
import { MessageTempDataType } from '@/types/MessageBox';
import { ErrorIcon, SuccessIcon } from '@/theme/overrides/CustomIcons';
import { createMessageTempList, fetchMessageTempList } from '@/store/MessageBox/messageBox.thunks';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import useAuth from '@/hooks/useAuth';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import { ErrorMessage } from '@/components/shared/Popup/ErrorMessage';
import { LoadingMessage } from '@/components/shared/Popup/LoadingMessage';
import AddFile from '@/assets/MessageIcons/add-file.gif';
import { getMessadeTempListData, getMessadeTempListStatus, getMessageTempSubmitting } from '@/config/storeSelectors';
import { useAppSelector } from '@/hooks/useAppSelector';
import LoadingPopup from '@/components/shared/Popup/LoadingPopup';
import LoadingMsg from '@/assets/MessageIcons/loading-message.gif';
import LoadingButton from '@mui/lab/LoadingButton';
import TextareaField from '@/components/shared/Selections/TextareaField';
import QuickSendMessage from './QuickSendMessage';
import SendMessage from '../SmsTemplate/SendMessage';
import SaveIcon from '@mui/icons-material/Save';

const QuickSendRoot = styled.div`
  padding: 1rem;
  li {
    padding-bottom: 5px;
  }
  .sub-heading {
    font-size: 90%;
    color: ${(props) => props.theme.palette.grey[600]};
  }
  .Card {
  }
  .message {
    font-size: 13px;
    padding-left: 1rem;
    color: ${(props) => props.theme.palette.grey[600]};
  }
  .MuiTableCell-root {
    border-bottom: 0px;
  }
`;

const getDataForSwitch = (switchId: string) => {
  const switchDataMap: any = {
    '1': ['Class : VIII-A', 'Class : VIII-B', 'Class : VIII-C', 'Class : VIII-D', 'Class : VIII-E'],
    '2': ['Class : VI', 'Class : VII', 'Class : VIII', 'Class : XI', 'Class : X'],
    '3': ['Bus No: 1', 'Bus No: 2', 'Bus No: 3', 'Sports', 'Arts'],
    '4': ['Alex : Bus-1', 'John : Bus-2', 'David : Bus-3', 'Joe : Bus-4', 'Abraham : Bus-5'],
    '5': ['Football Camp', 'Sports Test', 'Onam Celebration', 'Karate'],
    '6': null,
  };

  return switchDataMap[switchId] || null;
};
const DefaultMessageTemplateInfo: MessageTempDataType = {
  messageId: 0,
  messageTitle: '',
  messageContent: '',
  messageDate: '',
  messageCreatedBy: '',
  messageStatus: 1,
  messageTemplateId: '',
  messageType: 1,
};
const CreateEditMessageTempValidationSchema = Yup.object({
  messageTitle: Yup.string().required('Please enter Message Title'),
  messageType: Yup.number().oneOf([1, 2, 3], 'Please select Message Type'),
  // messageTemplateId: Yup.string().required('Please enter Template Id'),
  messageContent: Yup.string().required('Please enter Message Content'),
});

function QuickSendNew() {
  const dispatch = useAppDispatch();
  const { confirm } = useConfirm();
  const theme = useTheme();
  // const isLight = useSettings().themeMode === 'light';
  const { user } = useAuth();
  const admId: number | undefined = user?.accountId;
  const [drawerOpen, setDrawerOpen] = React.useState(false);
  const [switches, setSwitches] = React.useState('');
  const [active, setActive] = React.useState<string | null>('');
  const [allSendToggle, setAllSendToggle] = React.useState<string | null>('');
  // const [drawerData, setDrawerData] = React.useState(false);
  const [content, setContent] = React.useState('');
  const [option, setOption] = React.useState('');
  const [msgId, setMsgId] = React.useState<number>(0);
  const [popupView, setPopupView] = React.useState(false);
  const isSubmitting = useAppSelector(getMessageTempSubmitting);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isLoadingSendAll, setIsLoadingSendAll] = useState<boolean>(false);
  const [messageSave, setMessageSave] = useState<MessageTempDataType | undefined>(undefined);
  const MessageTempListData = useAppSelector(getMessadeTempListData);
  const [messageTempListDatas, setMessageTempListDatas] = useState<MessageTempDataType[]>([]);
  const MessageTempListStatus = useAppSelector(getMessadeTempListStatus);
  const [show, setShow] = React.useState(true);

  // const [messageTemplate, setMessageTemplate] = useState<MessageTempDataType>(DefaultMessageTemplateInfo);

  // const handleClick = useCallback((item: SendButtonType, row: MessageTempDataType) => {
  //   setMessageTemplate(row);
  //   setPopupView(true);
  //   setOption(item.name);
  //   setContent(item.component);
  // }, []);

  const initialMessageTempRequest = React.useMemo(
    () => ({
      messageTitle: '',
      messageDate: '',
      messageType: '-1',
    }),
    []
  );

  const handleSave = useCallback(
    async (value: MessageTempDataType) => {
      setIsLoading(true);
      console.log('value', value);
      // // setMessageSave(value);
      // console.log('messageSave', messageSave);
      try {
        const { ...rest } = value;
        const response = await dispatch(createMessageTempList(rest)).unwrap();
        console.log('response', response);
        setMsgId(response.id);

        if (response.id > 0) {
          const successMessage = <SuccessMessage icon={SaveFile} message="Message created successfully" />;
          await confirm(successMessage, '', { okLabel: 'Ok', showOnlyOk: true });
          setShow((s) => !s);
          // Find the newly created message in messageTempListDatas
          const newMessage = messageTempListDatas.find((f) => f.messageId === response.id);
          if (newMessage) {
            setMessageSave(newMessage);
            console.log('messageSave', newMessage);
          } else {
            console.log('Newly created message not found in messageTempListDatas');
          }
        }

        // const successMessage = <SuccessMessage icon={AddFile} message="Message created successfully" />;
        // await confirm(successMessage, 'Message Created', { okLabel: 'Ok', showOnlyOk: true });
      } catch (error) {
        const errorMessage = <ErrorMessage icon={ErrorMsg} message="Message content already created" />;
        await confirm(errorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
        console.error(error);
      }
      setIsLoading(false);
    },
    [confirm, dispatch, messageSave, MessageTempListData]
  );
  const {
    values: { messageTitle, messageContent, messageType, messageTemplateId },
    handleChange,
    handleBlur,
    handleSubmit,
    handleReset,
    touched,
    errors,
  } = useFormik<MessageTempDataType>({
    initialValues: {
      messageId: 0,
      messageTitle: '',
      messageContent: '',
      messageDate: '',
      messageCreatedBy: admId,
      messageStatus: 0,
      messageTemplateId: '',
      messageType: 1,
    },
    validationSchema: CreateEditMessageTempValidationSchema,
    onSubmit: (values) => {
      handleSave(values);
      console.log('values::::', values);
    },
    validateOnBlur: false,
  });
  const limit = messageType === 1 ? 30 : 1000;
  const showTemplateIdField = messageType === 2;

  React.useEffect(() => {
    if (MessageTempListStatus === 'idle') {
      dispatch(fetchMessageTempList(initialMessageTempRequest));
    }
    setMessageTempListDatas(MessageTempListData);
    // setMessageSave(messageTempListDatas.find((f) => f.messageId === msgId));
    console.log('MessageTempListDatas', messageTempListDatas);
    // console.log('messageSave', messageSave);
  }, [
    initialMessageTempRequest,
    MessageTempListStatus,
    dispatch,
    MessageTempListData,
    messageTempListDatas,
    messageSave,
    msgId,
  ]);

  const handleFileChange = () => {};

  const handleDrawerClose = () => {
    setDrawerOpen(false);
  };

  const [popup, setPopup] = React.useState(false);

  const handleClickdrawerOpen = () => {
    setPopup(true);
  };

  const handleClickClose = () => {
    setPopupView(false);
    setDrawerOpen(false);
  };

  // setMsgId(localStorage.setItem('messageId', response.id));

  const handleSwitchChange = (row: QuickSendlinksTypes) => {
    // if (row.id === '6') {
    //   setActive('');
    //   if (switches === '6') {
    //     setSwitches('');
    //   } else {
    //     setSwitches(row.id);
    //   }
    //   return;
    // }

    if (row.name === 'Student Individual' || row.name === 'Class Division Wise') {
      setDrawerOpen(true);
      setPopupView(false);
    } else {
      setDrawerOpen(false);
      setPopupView(true);
    }
    if (row.id === switches) {
      setDrawerOpen(false);
      // setDrawerData(null);
    } else {
      setIsLoading(false);
      setIsLoadingSendAll(false);
      // setMessageTemplate(row);
      // setPopupView(true);
      setOption(row.name);
      setContent(row.component);
      // setDrawerOpen(true);
    }
  };
  const handleSwitch = (row: QuickSendlinksTypes) => {
    if (row.name === 'Student Individual') {
      setDrawerOpen(true);
      setPopupView(false);
    } else {
      setDrawerOpen(false);
      // setDrawerData(null);
      setIsLoading(false);
      setIsLoadingSendAll(false);
      // setMessageTemplate(row);
      setPopupView(true);
      setOption(row.name);
      setContent(row.component);
      // setDrawerOpen(true);
      // setDrawerData(true);
    }
  };

  const handleSendAll = useCallback(
    async (row: SendToAllTypes, id: number) => {
      setIsLoadingSendAll(true);

      const sendConfirmMessage = (
        <LoadingMessage
          icon={isSubmitting ? LoadingMsg : LoadingMsg}
          message={
            <div>
              Are you sure you want to send to <br />
              <span style={{ color: theme.palette.primary.main }}>&quot;{row.name}&quot; ?</span>
            </div>
          }
        />
      );
      if (await confirm(sendConfirmMessage, 'Send Message?', { okLabel: 'Send', cancelLabel: 'Cancel' })) {
        const sendResponse = await dispatch(row.action({ adminId: admId, academicId: 10, messageId: id })).unwrap();
        // setButtonLoadingStatus(card.messageId, row.name, sendResponse.result);
        // setSuccessResult(sendResponse.result);
        console.log('sendResponse::::', sendResponse.result);
        setIsLoadingSendAll(false);

        if (sendResponse.result === 'Success') {
          const sendDoneMessage = <SuccessMessage icon={SuccessMsg} message={`Message sent to all ${row.name}.`} />;
          await confirm(sendDoneMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        } else if (sendResponse.result === 'Failed') {
          const sendDoneMessage = (
            <ErrorMessage
              icon={ErrorMsg}
              message="No messages sent to any Members, Please check the numbers and Try again."
            />
          );
          await confirm(sendDoneMessage, '', { okLabel: 'Ok', showOnlyOk: true });
          // const buttonKey = `${card.messageId}_${row.id}`;
        } else {
          const sendDoneMessage = <ErrorMessage icon={ErrorMsg1} message="Something Went Wrong Please try later." />;
          await confirm(sendDoneMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        }
        // console.log('individualSendLoadingMap', individualSendLoadingMap);
      }
      setIsLoadingSendAll(false);
    },
    [dispatch, confirm, admId, isSubmitting, theme]
  );

  const handleSendSwitch1 = (row: QuickSendlinksTypes) => {
    if (msgId > 0) {
      handleSwitchChange(row);
    }
  };

  const handleSendSwitch2 = (row: QuickSendlinksTypes) => {
    if (msgId > 0) {
      handleSwitch(row);
    }
  };

  const handleSendSwitch3 = (row: SendToAllTypes) => {
    if (msgId > 0) {
      handleSendAll(row, msgId);
    }
  };

  return (
    <Page title="Quick Send">
      <QuickSendRoot>
        <Card className="Card" elevation={5} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Typography variant="h6" fontSize={17}>
            Quick Send
          </Typography>
          <Divider />
          <form noValidate onSubmit={handleSubmit} onReset={handleReset}>
            <Stack py={2}>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <Typography variant="subtitle1" fontSize={12}>
                      Message Title
                    </Typography>
                    <TextField
                      placeholder="Enter message title"
                      name="messageTitle"
                      value={messageTitle}
                      onBlur={handleBlur}
                      onChange={(e) => {
                        handleChange(e);
                        if (msgId > 0) {
                          setShow(true);
                        }
                      }}
                      error={touched.messageTitle && !!errors.messageTitle}
                      helperText={touched.messageTitle && errors.messageTitle}
                      // disabled={isSubmitting}
                      InputProps={{
                        endAdornment: (
                          <>
                            {msgId > 0 && <SuccessIcon color="success" />}
                            {touched.messageTitle && !!errors.messageTitle && <ErrorIcon color="error" />}
                          </>
                        ),
                      }}
                    />
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <Typography variant="subtitle1" fontSize={12}>
                      Type
                    </Typography>
                    <Select
                      name="messageType"
                      value={messageType}
                      onChange={(e) => {
                        handleChange(e);
                        if (msgId > 0) {
                          setShow(true);
                        }
                      }}
                      error={touched.messageType && !!errors.messageType}
                      // disabled={isSubmitting}
                    >
                      {MESSAGE_TYPE_OPTIONS.map((opt) => (
                        <MenuItem key={opt.id} value={opt.id}>
                          {opt.name}
                        </MenuItem>
                      ))}
                    </Select>
                    {touched.messageType && !!errors.messageType && (
                      <Typography color="red" fontSize="12px" ml={2.5} mt={1} variant="subtitle1">
                        {errors.messageType}
                      </Typography>
                    )}
                  </FormControl>
                </Grid>

                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <Typography variant="subtitle1" fontSize={12}>
                      Message content
                    </Typography>
                    <TextareaField
                      limit={limit}
                      ShowCharectersCount={messageType}
                      placeholder="Enter content..."
                      name="messageContent"
                      value={messageContent}
                      onChange={(e) => {
                        handleChange(e);
                        if (msgId > 0) {
                          setShow(true);
                        }
                      }}
                      error={touched.messageContent && !!errors.messageContent}
                      helperText={touched.messageContent && errors.messageContent}
                      InputProps={{
                        inputProps: {
                          style: { resize: 'vertical', minHeight: '60px', maxHeight: '100px' },
                          maxLength: limit,
                        },
                        endAdornment: (
                          <>
                            {msgId > 0 && <SuccessIcon color="success" sx={{ mr: 2 }} />}
                            {touched.messageContent && !!errors.messageContent && (
                              <InputAdornment position="end">
                                <ErrorIcon color="error" sx={{ mr: 2 }} />
                              </InputAdornment>
                            )}
                          </>
                        ),
                      }}
                    />
                  </FormControl>
                </Grid>
                {showTemplateIdField && (
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12}>
                        Template Id
                      </Typography>
                      <TextField
                        placeholder="Enter template id"
                        name="messageTemplateId"
                        value={messageTemplateId}
                        onChange={handleChange}
                        error={touched.messageTemplateId && !!errors.messageTemplateId}
                        helperText={errors.messageTemplateId}
                        disabled={isSubmitting}
                      />
                    </FormControl>
                  </Grid>
                )}
              </Grid>
            </Stack>

            <Box pt={3} sx={{ display: 'flex', gap: { xl: 0, xxl: 5 }, flexWrap: 'wrap' }}>
              <ul className="message" style={{ marginRight: 20 }}>
                <li>Please do not copy paste message from Word, Excel, Notepad etc.</li>
                <li>If copied, Please ensure the special character like @ ,# ,$ ,& ,&quot; ,etc are not included.</li>
              </ul>
              <ul className="message">
                <li>Or please remove special characters then retype from the keyboard.</li>
                <li>Kindly avoid unwanted space and next line for better delivery.</li>
              </ul>
            </Box>
            <Box display={{ sm: 'flex' }} sx={{ justifyContent: { sm: 'right' }, pr: { lg: '5' } }}>
              <Stack spacing={2} direction="row">
                <Button disabled={isSubmitting} fullWidth type="reset" variant="contained" color="secondary">
                  Cancel
                </Button>
                {/* <LoadingButton
                  fullWidth
                  loadingPosition="start"
                  loading={isSubmitting}
                  variant="contained"
                  color="primary"
                  type="submit"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Saving...' : 'Save'}
                </LoadingButton> */}

                <LoadingButton
                  fullWidth
                  loadingPosition="start"
                  startIcon={<SaveIcon />}
                  loading={isSubmitting}
                  variant="contained"
                  color="primary"
                  type="submit"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Saving...' : 'Save'}
                </LoadingButton>
              </Stack>
            </Box>
            <Divider sx={{ my: 2 }} />
            <Box>
              <Typography variant="h5" fontSize={15}>
                Send To
              </Typography>
            </Box>
            {/* <Box width={{ xl: '50%', lg: '70%', md: '100%', sm: '50%', xs: '100%' }}> */}
            <Box mt={2}>
              {quicksendlinks1?.map((row: QuickSendlinksTypes) => (
                <Button
                  key={row.id}
                  sx={{ mr: 1, mb: 1.5, p: 0.5, fontWeight: 600, fontSize: 12 }}
                  size="small"
                  variant="outlined"
                  disabled={msgId === 0 && show}
                  color="primary"
                  onClick={() => handleSendSwitch1(row)}
                >
                  {row.name}
                </Button>
              ))}
              {quicksendlinks2?.map((row: QuickSendlinksTypes) => (
                <Button
                  key={row.id}
                  sx={{ mr: 1, mb: 1.5, p: 0.5, fontWeight: 600, fontSize: 12 }}
                  size="small"
                  variant="outlined"
                  disabled={msgId === 0 && show}
                  color="primary"
                  onClick={() => handleSendSwitch2(row)}
                >
                  {row.name}
                </Button>
              ))}
              {sendToAll?.map((row: SendToAllTypes) => (
                <Button
                  key={row.id}
                  sx={{ mr: 1, mb: 1.5, p: 0.5, fontWeight: 600, fontSize: 12 }}
                  size="small"
                  variant="outlined"
                  disabled={msgId === 0 && show}
                  color="primary"
                  onClick={() => handleSendSwitch3(row)}
                >
                  {row.name}
                </Button>
              ))}

              {/* {quicksendlinks1?.map((row: QuickSendlinksTypes) => (
                      <TableContainer>
                        <Table key={row.id}>
                          <TableBody>
                            <TableCell>
                              <Typography
                                sx={{ width: 'fit-content', cursor: 'pointer' }}
                                onClick={() => handleSendSwitch1(row)}
                                variant="subtitle2"
                              >
                                {row.name}
                              </Typography>
                            </TableCell>
                            <TableCell
                              sx={{
                                width: '2.5%',
                                minWidth: '30px',
                                display: 'table-cell',
                              }}
                            >
                            <MuiSwitch
                              checked={switches === row.id}
                              disabled={msgId === 0}
                              onChange={() => handleSendSwitch1(row)}
                            />
                          </TableCell>
                        </TableBody>
                      </Table>
                    </TableContainer>
                  ))} */}

              {/* {quicksendlinks2?.map((row) => (
                    <TableContainer>
                      <Table key={row.id}>
                        <TableBody>
                          <TableCell>
                            <Typography
                              sx={{ width: 'fit-content', cursor: 'pointer' }}
                              onClick={() => handleSendSwitch2(row)}
                              variant="subtitle2"
                            >
                              {row.name}
                            </Typography>
                          </TableCell>
                          <TableCell
                            sx={{
                              width: '2.5%',
                              minWidth: '30px',
                              display: 'table-cell',
                            }}
                          >
                            <MuiSwitch
                              checked={active === row.id}
                              disabled={msgId === 0}
                              onChange={() => handleSendSwitch2(row)}
                            />
                          </TableCell>
                        </TableBody>
                      </Table>
                    </TableContainer>
                  ))}
                  {sendToAll?.map((row) => (
                    <TableContainer>
                      <Table key={row.id}>
                        <TableBody>
                          <TableCell>
                            <Typography
                              sx={{ width: 'fit-content', cursor: 'pointer' }}
                              onClick={() => handleSendSwitch3(row)}
                              variant="subtitle2"
                            >
                              {row.name}
                            </Typography>
                          </TableCell>
                          <TableCell
                            sx={{
                              width: '2.5%',
                              minWidth: '30px',
                              display: 'table-cell',
                            }}
                          >
                            <MuiSwitch
                              checked={allSendToggle === row.id}
                              disabled={msgId === 0}
                              onChange={() => handleSendSwitch3(row)}
                            />
                          </TableCell>
                        </TableBody>
                      </Table>
                    </TableContainer>
                  ))} */}
            </Box>
            {/* </Box> */}
          </form>
        </Card>
      </QuickSendRoot>
      {isSubmitting && isLoadingSendAll ? (
        <LoadingPopup popupContent={<LoadingMessage icon={LoadingMsg} message="Message creating please wait..." />} />
      ) : isSubmitting && isLoading ? (
        <LoadingPopup popupContent={<LoadingMessage icon={AddFile} message="Message creating please wait..." />} />
      ) : null}
      {/* <TempDrawer
        handleDrawerdrawerOpen={handleSwitchChange}
        handleDrawerClose={handleDrawerClose}
        state={drawerOpen}
        drawerData={drawerData}
        title="Select"
      /> */}
      <TemporaryDrawer
        onClose={handleClickClose}
        Title={`Send to ${option}`}
        state={drawerOpen}
        DrawerContent={<QuickSendMessage content={content} messageId={msgId} />}
      />
      <Popup
        size="xl"
        title={`Send to ${option}`}
        state={popupView}
        onClose={handleClickClose}
        popupContent={<SendMessage content={content} messageId={msgId} />}
      />
      <Popup
        size="xs"
        state={popup}
        onClose={handleClickClose}
        popupContent={<SuccessMessage message="Message Sent Successfully" />}
      />
    </Page>
  );
}

export default QuickSendNew;
