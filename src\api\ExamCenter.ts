import ApiUrls from '@/config/ApiUrls';
import {
  ExamCreateRequest,
  ExamListPagedData,
  ExamListRequest,
  ExamTimetableInfo,
  MarkRegisterCBSEAddDataType,
  MarkRegisterCBSEwithCEAddDataType,
  MarkRegisterCEDataType,
  MarkRegisterDataType,
  MarkRegisterFilterDataType,
  MarkRegisterFilterSubjectsType,
} from '@/types/ExamCenter';
import { CreateResponse, DeleteResponse, UpdateResponse } from '@/types/Common';
import { APIResponse } from './base/types';
import { privateApi } from './base/api';

async function GetExamList(request: ExamListRequest): Promise<APIResponse<ExamListPagedData>> {
  const response = await privateApi.post<ExamListPagedData>(ApiUrls.GetExamList, request);
  return response;
}
async function AddNewExam(request: ExamCreateRequest): Promise<APIResponse<CreateResponse>> {
  const response = await privateApi.post<CreateResponse>(ApiUrls.AddEditExam, request);
  return response;
}

async function UpdateExam(request: ExamTimetableInfo): Promise<APIResponse<UpdateResponse>> {
  const response = await privateApi.put<UpdateResponse>(ApiUrls.AddEditExam, request);
  return response;
}

async function DeleteExam(examId: number): Promise<APIResponse<DeleteResponse>> {
  const url = ApiUrls.DeleteExam.replace('{examId}', examId.toString());
  const response = await privateApi.delete<DeleteResponse>(url);
  return response;
}

async function GetMarkRegisterFilter(adminId: number | undefined): Promise<APIResponse<MarkRegisterFilterDataType>> {
  if (adminId === undefined) {
    throw new Error('adminId required');
  }

  const url = ApiUrls.GetMarkRegisterFilter.replace('{adminId}', adminId.toString());

  const response = await privateApi.get<any>(url);
  return response;
}

async function GetMarkRegisterSubjectFilter(
  adminId: number | undefined,
  classId: number | undefined
): Promise<APIResponse<MarkRegisterFilterSubjectsType[]>> {
  if (adminId === undefined || classId === undefined) {
    throw new Error('adminId and classId required');
  }

  const url = ApiUrls.GetMarkRegisterSubjectFilter.replace('{adminId}', adminId.toString()).replace(
    '{classId}',
    classId.toString()
  );

  const response = await privateApi.get<any>(url);
  return response;
}

async function GetMarkDetails(
  adminId: number | undefined,
  academicId: number | undefined,
  classId: number | undefined,
  examId: number | undefined,
  subjectId: number | undefined
): Promise<APIResponse<MarkRegisterDataType[]>> {
  if (
    adminId === undefined ||
    academicId === undefined ||
    classId === undefined ||
    examId === undefined ||
    subjectId === undefined
  ) {
    throw new Error('adminId, academicId, classId, examId and subjectId are required');
  }

  const url = ApiUrls.GetMarkDetails.replace('{adminId}', adminId.toString())
    .replace('{academicId}', academicId.toString())
    .replace('{classId}', classId.toString())
    .replace('{examId}', examId.toString())
    .replace('{subjectId}', subjectId.toString());

  const response = await privateApi.get<any>(url);
  return response;
}

async function AddMarkRegisterCBSE(
  request: MarkRegisterCBSEAddDataType[]
): Promise<APIResponse<MarkRegisterCBSEAddDataType[]>> {
  const response = await privateApi.post<MarkRegisterCBSEAddDataType[]>(ApiUrls.AddMarkRegisterCBSE, request);
  return response;
}

async function GetMarkDetailsWithCE(
  adminId: number | undefined,
  academicId: number | undefined,
  classId: number | undefined,
  examId: number | undefined,
  subjectId: number | undefined
): Promise<APIResponse<MarkRegisterCEDataType[]>> {
  if (
    adminId === undefined ||
    academicId === undefined ||
    classId === undefined ||
    examId === undefined ||
    subjectId === undefined
  ) {
    throw new Error('adminId, academicId, classId, examId and subjectId are required');
  }

  const url = ApiUrls.GetMarkDetailswithCE.replace('{adminId}', adminId.toString())
    .replace('{academicId}', academicId.toString())
    .replace('{classId}', classId.toString())
    .replace('{examId}', examId.toString())
    .replace('{subjectId}', subjectId.toString());

  const response = await privateApi.get<any>(url);
  return response;
}

async function AddMarkRegisterCBSEwithCE(
  request: MarkRegisterCBSEwithCEAddDataType[]
): Promise<APIResponse<MarkRegisterCBSEwithCEAddDataType[]>> {
  const response = await privateApi.post<MarkRegisterCBSEwithCEAddDataType[]>(
    ApiUrls.AddMarkRegisterCBSEwithCE,
    request
  );
  return response;
}

const methods = {
  GetExamList,
  AddNewExam,
  UpdateExam,
  DeleteExam,
  GetMarkRegisterFilter,
  GetMarkRegisterSubjectFilter,
  GetMarkDetails,
  AddMarkRegisterCBSE,
  GetMarkDetailsWithCE,
  AddMarkRegisterCBSEwithCE,
};

export default methods;
