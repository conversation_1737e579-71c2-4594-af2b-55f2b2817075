import * as React from 'react';
import Box from '@mui/material/Box';
import Drawer from '@mui/material/Drawer';
import Button from '@mui/material/Button';
import { Stack, useTheme } from '@mui/material';
import birthdayIconDown from '@/assets/birthday/birthday.png';
import Birthday from './Birthday/Birthday';
import DownloadLinkCard from './DownloadLinkCard';

type Anchor = 'bottom';

export default function BottomBirthdayDrawer() {
  const theme = useTheme();
  const [state, setState] = React.useState({
    bottom: false,
  });

  const toggleDrawer = (anchor: Anchor, open: boolean) => (event: React.KeyboardEvent | React.MouseEvent) => {
    if (
      event.type === 'keydown' &&
      ((event as React.KeyboardEvent).key === 'Tab' || (event as React.KeyboardEvent).key === 'Shift')
    ) {
      return;
    }

    setState({ ...state, [anchor]: open });
  };

  const list = () => (
    <Box
      sx={{ width: '100%', height: '100%' }}
      role="presentation"
      // onClick={toggleDrawer('bottom', false)}
      // onKeyDown={toggleDrawer('bottom', false)}
    >
      <Birthday closeClick={toggleDrawer('bottom', false)} />
      <DownloadLinkCard />
    </Box>
  );

  return (
    <div className="w-100">
      <React.Fragment key="bottom">
        <Button onClick={toggleDrawer('bottom', true)} className="rounded-circle">
          <Stack sx={{ backgroundColor: theme.palette.info.lighter, p: 2, borderRadius: 50, boxShadow: 5 }}>
            <img src={birthdayIconDown} alt="birthday-icon" width={45} height={45} />
          </Stack>
        </Button>
        <Drawer anchor="bottom" open={state.bottom} className="" onClose={toggleDrawer('bottom', false)}>
          {list()}
        </Drawer>
      </React.Fragment>
    </div>
  );
}
