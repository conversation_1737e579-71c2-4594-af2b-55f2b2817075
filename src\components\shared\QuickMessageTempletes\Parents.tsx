/* eslint-disable jsx-a11y/alt-text */
import React, { FormEvent, useCallback, useState } from 'react';
import {
  Divider,
  Grid,
  Paper,
  Stack,
  Button,
  Typography,
  Box,
  Avatar,
  FormControl,
  Select,
  MenuItem,
  SelectChangeEvent,
  useTheme,
} from '@mui/material';
import styled from 'styled-components';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useAppSelector } from '@/hooks/useAppSelector';
import {
  getClassData,
  getParentsListData,
  getParentsListStatus,
  getYearData,
  getMessageTempSubmitting,
} from '@/config/storeSelectors';
import useAuth from '@/hooks/useAuth';
import { fetchClassList, fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import { fetchParentsList, messageSendToParentsList } from '@/store/MessageBox/messageBox.thunks';
// import useSettings from '@/hooks/useSettings';
import LoadingButton from '@mui/lab/LoadingButton';
import SendIcon from '@mui/icons-material/Send';
import ErrorMsg from '@/assets/MessageIcons/error-message.gif';
import ErrorMsg1 from '@/assets/MessageIcons/error-message1.gif';
import ErrorMsg2 from '@/assets/MessageIcons/error-message2.gif';
import SuccessMsg from '@/assets/MessageIcons/message-success.gif';
import successIcon from '@/assets/MessageIcons/success.json';
import errorIcon from '@/assets/MessageIcons/error-message.json';
import Lottie from 'lottie-react';
import { ParentsDataType, SendToParentsRequest, SendToParentsResponse } from '@/types/MessageBox';
import { SuccessMessage } from '../Popup/SuccessMessage';
import { useConfirm } from '../Popup/Confirmation';
import { ErrorMessage } from '../Popup/ErrorMessage';
import LoadingPopup from '../Popup/LoadingPopup';
import { LoadingMessage } from '../Popup/LoadingMessage';

const ParentsRoot = styled.div`
  width: 100%;
`;

interface ParentsDataWithStatus extends ParentsDataType {
  sendStatus: 'Success' | 'Failed';
  status?: string;
}

function Parents({ messageId }: { messageId: number }) {
  const { user } = useAuth();
  const theme = useTheme();
  // const isLight = useSettings().themeMode === 'light';
  const dispatch = useAppDispatch();
  const { confirm } = useConfirm();
  const adminId: number = user ? user.accountId : 0;
  // const [view, setView] = useState<'main' | 'report'>('main');
  const [classNameFilter, setClassNameFilter] = useState(-2);
  const [academicYearFilter, setAcademicYearFilter] = useState(0);
  // const [sendResults, setSendResults] = useState<SendToParentsResponse[] | null>([]);
  const [selectedRows, setSelectedRows] = useState<ParentsDataWithStatus[]>([]);
  const YearData = useAppSelector(getYearData);
  const ClassData = useAppSelector(getClassData);
  const ParentsListData = useAppSelector(getParentsListData);
  const ParentsListStatus = useAppSelector(getParentsListStatus);
  const isSubmitting = useAppSelector(getMessageTempSubmitting);
  const [selectedData, setSelectedData] = useState<ParentsDataWithStatus[]>([]);
  const [errorTooltip, setErrorTooltip] = useState<React.ReactNode>(undefined);

  interface ParentListRequest {
    adminId: number;
    academicId: number;
    classId: number;
  }
  const initialParentListRequest = React.useMemo(
    () => ({
      adminId,
      academicId: 10,
      classId: -2,
    }),
    [adminId]
  );
  const currentParentListRequest: ParentListRequest = React.useMemo(
    () => ({
      adminId,
      academicId: academicYearFilter,
      classId: classNameFilter,
    }),
    [academicYearFilter, classNameFilter, adminId]
  );

  const loadParentsList = useCallback(
    async (request: ParentListRequest) => {
      try {
        const data = await dispatch(fetchParentsList(request)).unwrap();
        setSelectedData(data);
        setSelectedRows([]);
      } catch (error) {
        console.error('Error loading parents list:', error);
      }
    },
    [dispatch, setSelectedData]
  );

  React.useEffect(() => {
    dispatch(fetchClassList(adminId));
    dispatch(fetchYearList(adminId));
    dispatch(fetchParentsList(currentParentListRequest));
    loadParentsList(currentParentListRequest);
  }, [dispatch, adminId, loadParentsList, currentParentListRequest]);

  const handleYearChange = (e: SelectChangeEvent) => {
    setAcademicYearFilter(parseInt(YearData.filter((item) => item.accademicId === e.target.value)[0].accademicId, 10));
    loadParentsList({ ...currentParentListRequest, academicId: parseInt(e.target.value, 10) });
    setSelectedRows([]);
  };
  const handleClassChange = (e: SelectChangeEvent) => {
    setClassNameFilter(ClassData.filter((item) => item.classId === parseInt(e.target.value, 10))[0].classId);
    loadParentsList({ ...currentParentListRequest, classId: parseInt(e.target.value, 10) });
    setSelectedRows([]);
  };

  const handleReset = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      setAcademicYearFilter(parseInt(YearData[0].accademicId, 10));
      setClassNameFilter(-2);
      setSelectedRows([]);
      loadParentsList(initialParentListRequest);
    },
    [YearData, loadParentsList, initialParentListRequest]
  );

  const handleSubmit = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      loadParentsList(currentParentListRequest);
      setSelectedRows([]);
    },
    [loadParentsList, currentParentListRequest]
  );

  // const handleBack = useCallback(() => {
  //   // setView('main');
  //   setSendResults(sendResults);
  //   setSelectedRows([]);
  //   loadParentsList(currentParentListRequest);
  // }, [loadParentsList, currentParentListRequest]);

  const handleSend = useCallback(async () => {
    try {
      if (!selectedRows || selectedRows.length === 0) {
        const errorMessage = <ErrorMessage icon={ErrorMsg1} message="Please Select Atleast one parent to sent" />;

        await confirm(errorMessage, 'Parent not selected', { okLabel: 'Ok', showOnlyOk: true });
        return;
      }
      const sendRequests: SendToParentsRequest[] = await Promise.all(
        selectedRows?.map(async (item) => {
          const { admissionNo, ...rest } = item;
          const sendReq = { messageId, adminId, ...rest } as SendToParentsRequest;
          return sendReq;
        }) || []
      );
      const actionResult = await dispatch(messageSendToParentsList(sendRequests));

      if (actionResult && Array.isArray(actionResult.payload)) {
        const results: SendToParentsResponse[] = actionResult.payload;
        // setSendResults(results);
        console.log('results', results);

        const errorMessages = results.find((result) => result.sendStatus === 'Failed');
        const successMessages = results.find((result) => result.sendStatus === 'Success');

        if (!errorMessages) {
          const successMessage = (
            <SuccessMessage icon={SuccessMsg} message="Message sent to all selected parents Successfully" />
          );
          await confirm(successMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        } else if (!successMessages) {
          const errorMessage = (
            <ErrorMessage
              icon={ErrorMsg2}
              message="No messages sent to any parents, Please check the numbers and Try again"
            />
          );

          await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        } else {
          const errorMessage = (
            <ErrorMessage
              icon={ErrorMsg}
              message="Error sending messages to some parents, Please recheck and Try again"
            />
          );

          await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        }
        const updatedSelectedData = selectedData.map((rowData) => {
          const selectedRow = selectedRows.find((sr) => sr.studentId === rowData.studentId);
          console.log('selectedRow::::', selectedRow);
          if (selectedRow) {
            const result = results.find((r) => r.studentId === selectedRow.studentId);

            if (result) {
              return { ...rowData, status: result.sendStatus || '' };
            }
          }
          return rowData;
        });
        setSelectedRows([]);
        setSelectedData(updatedSelectedData);
      } else {
        const errorMessage = <ErrorMessage message="Error while sending messages, Please Try again" />;

        await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
      }
      // loadParentsList(currentParentListRequest);
    } catch (error) {
      console.error('Error sending messages:', error);
    }
  }, [confirm, dispatch, selectedRows, selectedData, messageId, adminId]);

  // const handleSendIndivitually = useCallback(async () => {
  //   try {
  //     if (!selectedRows || selectedRows.length === 0) {
  //       const errorMessage = <ErrorMessage message="Please Select Atleat one parent to sent" />;

  //       await confirm(errorMessage, 'Parent not Selected', { okLabel: 'Ok', showOnlyOk: true });
  //       return;
  //     }

  //     const sendRequests = selectedRows?.map(async (item) => {
  //       const { admissionNo, ...rest } = item;
  //       const sendReq = { messageId, adminId, ...rest };
  //       const response = await dispatch(messageSendToParents(sendReq)).unwrap();

  //       return response;
  //     });
  //     setSelectedRows([]);
  //     console.log(sendRequests);
  //     const results = await Promise.all(sendRequests);
  //     const errorMessages = results.find(({ result }) => result === 'Failed');
  //     const successMessages = results.find(({ result }) => result === 'Success');

  //     // // Display success messages
  //     // if (successMessages.length > 0) {
  //     //   const successMessage = <SuccessMessage message={successMessages.join('\n')} />;
  //     //   await confirm(successMessage, 'Messages Created', { okLabel: 'Ok', showOnlyOk: true });
  //     // }
  //     if (!errorMessages) {
  //       const successMessage = <SuccessMessage message="Message sent to all selected parents Successfully" />;
  //       await confirm(successMessage, '', { okLabel: 'Ok', showOnlyOk: true });
  //     } else if (!successMessages) {
  //       const errorMessage = (
  //         <ErrorMessage message="No messages sent to any parents, Please check the numbers and Try again" />
  //       );

  //       await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
  //     } else {
  //       const errorMessage = (
  //         <ErrorMessage message="Error sending messages to some parents, Please recheck and Try again" />
  //       );

  //       await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
  //     }
  //     loadParentsList(currentParentListRequest);
  //   } catch (error) {
  //     console.error('Error sending messages:', error);
  //   }
  // }, [confirm, dispatch, selectedRows, messageId, adminId, loadParentsList, currentParentListRequest]);

  const getRowKey = useCallback((row: ParentsDataWithStatus) => row.studentId, []);

  const getStatusIcon = (row: ParentsDataWithStatus) => {
    let iconComponent;

    if (row.status === 'Failed') {
      iconComponent = (
        <Stack>
          <Lottie animationData={errorIcon} loop={false} style={{ width: '30px' }} />
        </Stack>
      );
    } else if (row.status === 'Success') {
      iconComponent = (
        <Stack>
          <Lottie animationData={successIcon} loop={false} style={{ width: '30px' }} />
        </Stack>
      );
    } else if (row.status === 'Null') {
      iconComponent = 'empty';
    } else {
      iconComponent = null;
    }

    return <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>{iconComponent}</Box>;
  };

  // const rowBgColor = (row: ParentsDataWithStatus) => {
  //   let backgroundColor;
  //   if (row.status === 'Failed') {
  //     backgroundColor = isLight ? 'red' : 'blue';
  //   } else if (row.status === 'Success') {
  //     backgroundColor = isLight ? 'green' : 'lightgreen';
  //   } else {
  //     backgroundColor = 'inherit';
  //   }
  //   return { backgroundColor };
  // };

  const parentsListColumns: DataTableColumn<ParentsDataWithStatus>[] = [
    {
      name: 'studentName',
      headerLabel: 'Student Name',
      renderCell: (row) => (
        <Stack direction="row" gap={1} alignItems="center">
          <Avatar />
          <Typography variant="subtitle2">{row.studentName}</Typography>
        </Stack>
      ),
    },
    {
      name: 'className',
      dataKey: 'className',
      headerLabel: 'Class',
    },
    {
      name: 'status',
      renderCell: getStatusIcon,
    },
  ];

  return (
    <ParentsRoot>
      <Box>
        <Divider />
        <form noValidate onReset={handleReset} onSubmit={handleSubmit}>
          <Grid pb={2} pt={1} container spacing={3} alignItems="end">
            <Grid item xs={6}>
              <FormControl fullWidth>
                <Typography variant="subtitle2" fontSize={12} color="GrayText">
                  Select Year
                </Typography>
                <Select
                  labelId="academicYearFilter"
                  id="academicYearFilterSelect"
                  value={academicYearFilter.toString()}
                  onChange={handleYearChange}
                  placeholder="Select Year"
                >
                  <MenuItem value={0}>Select Year</MenuItem>
                  {YearData.map((opt) => (
                    <MenuItem key={opt.accademicId} value={opt.accademicId}>
                      {opt.accademicTime}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={6}>
              <FormControl fullWidth>
                <Typography variant="subtitle2" fontSize={12} color="GrayText">
                  Select Class
                </Typography>
                <Select
                  labelId="classNameFilter"
                  id="classNameFilterSelect"
                  value={classNameFilter.toString()}
                  onChange={handleClassChange}
                  placeholder="Select Class"
                >
                  <MenuItem value={-2}>Select Class</MenuItem>
                  {ClassData.map((opt) => (
                    <MenuItem key={opt.classId} value={opt.classId}>
                      {opt.className}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            {/* <Grid item lg={3} md={4} sm={4} xs={12}>
              <FormControl fullWidth>
                <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                  <Button variant="contained" color="secondary" type="reset" fullWidth>
                    Reset
                  </Button>
                  <Button variant="contained" color="primary" type="submit" fullWidth>
                    Search
                  </Button>
                </Stack>
              </FormControl>
            </Grid> */}
          </Grid>
        </form>
        <Paper
          sx={{
            border: selectedData?.length === 0 ? `0px` : `1px solid ${theme.palette.grey[300]}`,
            width: '100%',
            overflow: 'auto',
          }}
        >
          <Box
            sx={{
              height: 'calc(100vh - 12.75rem)',
              width: { xs: '100%', md: '100%' },
            }}
          >
            <DataTable
              ShowCheckBox
              RowSelected
              setSelectedRows={setSelectedRows}
              selectedRows={selectedRows}
              columns={parentsListColumns}
              data={selectedData}
              getRowKey={getRowKey}
              // getRowKey={(row: ParentsDataType) => row.studentId}
              fetchStatus={ParentsListStatus}
            />
          </Box>
        </Paper>

        <Box pt={3}>
          {selectedData?.length !== 0 ? (
            <Stack spacing={2} direction="row">
              <Button
                fullWidth
                onClick={() => loadParentsList(currentParentListRequest)}
                variant="contained"
                color="secondary"
              >
                Cancel
              </Button>
              <LoadingButton
                fullWidth
                endIcon={<SendIcon />}
                loadingPosition="start"
                // onClick={handleSendIndivitually}
                onClick={handleSend}
                loading={isSubmitting}
                variant="contained"
                color="primary"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Sending...' : 'Send'}
              </LoadingButton>
            </Stack>
          ) : null}
        </Box>
      </Box>
      {/* {isSubmitting ? (
        <LoadingPopup popupContent={<LoadingMessage icon={LoadingMsg} message="Sending messages please wait." />} />
      ) : null} */}
    </ParentsRoot>
  );
}

export default Parents;
