import React, { useState } from 'react';
import TextField, { TextFieldProps } from '@mui/material/TextField';

type CustomTextFieldProps = TextFieldProps & {
  name: string;
  label: string;
  value: string | undefined;
  variant: 'standard' | 'outlined' | 'filled';
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void | any;
  disabled?: boolean;
};

const CustomTextField = ({ name, label, value, variant, onChange, disabled }: CustomTextFieldProps) => {
  const [isFocused, setIsFocused] = useState(false);

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
  };

  const labelStyles = {
    fontSize: 14,
    marginTop: value || isFocused ? 0 : -7,
  };

  return (
    <TextField
      name={name}
      label={label}
      value={value}
      variant={variant}
      onChange={onChange}
      disabled={disabled}
      onFocus={handleFocus}
      onBlur={handleBlur}
      InputLabelProps={{
        style: labelStyles,
      }}
      fullWidth
    />
  );
};

export default CustomTextField;
