import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import Loadable from '@/components/shared/Loadable';

const PtaForum = Loadable(lazy(() => import('@/pages/PtaForum')));
const List = Loadable(lazy(() => import('@/features/PtaForum/MembersList')));
const CoreCommittee = Loadable(lazy(() => import('@/features/PtaForum/CoreCommittee')));
const TrustCommittee = Loadable(lazy(() => import('@/features/PtaForum/TrustCommittee')));

export const ptaForumRoutes: RouteObject[] = [
  {
    path: '/pta-forum',
    element: <PtaForum />,
    children: [
      {
        path: 'members-list',
        element: <List />,
      },
      {
        path: 'core-committee',
        element: <CoreCommittee />,
      },
      {
        path: 'trust-committee',
        element: <TrustCommittee />,
      },
    ],
  },
];
