/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import { Box, Divider, Grid, Stack, TextField, Button, Typography, Card, FormControl } from '@mui/material';
import styled from 'styled-components';
import typography from '@/theme/typography';
import Popup from '@/components/shared/Popup/Popup';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';

const ContactUsRoot = styled.div`
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  .MuiTextField-root:hover .MuiInputBase-input {
    cursor: text;
  }
`;

function ContactUs() {
  const [popup, setPopup] = React.useState(false);

  const handleClickOpen = () => setPopup(true);
  const handleClickClose = () => setPopup(false);

  return (
    <Page title="Create Login">
      <ContactUsRoot>
        <Card elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Typography variant="h6" fontSize={17}>
            Add New Login
          </Typography>
          <Divider />
          <form noValidate>
            <Grid py={4} container spacing={5}>
              <Grid item xs={12}>
                <FormControl sx={{ minWidth: { xs: '100%', xxl: '85%', md: '70%' } }}>
                  <Typography variant="subtitle2" color="GrayText" fontSize={14}>
                    Name & Address
                  </Typography>
                  <TextField placeholder="Enter School Name and Address" multiline minRows={3} />
                </FormControl>
              </Grid>
              <Grid item lg={6} xs={12}>
                <FormControl sx={{ minWidth: { xs: '100%', xl: 330, md: 300 } }}>
                  <Typography variant="subtitle2" color="GrayText" fontSize={14}>
                    Contact Number
                  </Typography>
                  <TextField placeholder="Enter Number" />
                </FormControl>
              </Grid>
              <Grid item lg={6} xs={12}>
                <FormControl sx={{ minWidth: { xs: '100%', xl: 330, md: 300 } }}>
                  <Typography variant="subtitle2" color="GrayText" fontSize={14}>
                    Email ID
                  </Typography>
                  <TextField placeholder="Enter email-id" />
                </FormControl>
              </Grid>
              <Grid item lg={6} xs={12}>
                <FormControl sx={{ minWidth: { xs: '100%', xl: 330, md: 300 } }}>
                  <Typography variant="subtitle2" color="GrayText" fontSize={14}>
                    Google Map Lattitude
                  </Typography>
                  <TextField placeholder="Enter Google Map Lattitude" />
                </FormControl>
              </Grid>
              <Grid item lg={6} xs={12}>
                <FormControl sx={{ minWidth: { xs: '100%', xl: 330, md: 300 } }}>
                  <Typography variant="subtitle2" color="GrayText" fontSize={14}>
                    Google Map Longitude
                  </Typography>
                  <TextField placeholder="Enter Google Map Longitude" />
                </FormControl>
              </Grid>
            </Grid>
          </form>
          <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { xxl: '15%' } }}>
            <Stack spacing={2} direction="row" pt={5}>
              <Button variant="contained" color="secondary" sx={{ fontFamily: typography.fontFamily }}>
                Cancel
              </Button>
              <Button onClick={handleClickOpen} variant="contained" color="primary">
                Save
              </Button>
            </Stack>
          </Box>
        </Card>
        <Popup
          size="xs"
          state={popup}
          onClose={handleClickClose}
          popupContent={<SuccessMessage message="TimeTable Updated Successfully" />}
        />
      </ContactUsRoot>
    </Page>
  );
}

export default ContactUs;
