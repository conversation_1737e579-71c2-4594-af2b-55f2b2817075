import { DemoContainer, DemoItem } from '@mui/x-date-pickers/internals/demo';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import dayjs from 'dayjs';

export default function SelectDateTimePicker({ disablePast }: { disablePast?: boolean }) {
  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <DemoContainer sx={{ p: 0 }} components={['DatePicker', 'TimePicker', 'DateTimePicker', 'DateRangePicker']}>
        <DemoItem label="">
          <DateTimePicker disablePast={disablePast} defaultValue={dayjs(new Date())} />
        </DemoItem>
      </DemoContainer>
    </LocalizationProvider>
  );
}
