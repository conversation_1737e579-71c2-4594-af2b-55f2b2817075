import api from '@/api';
import {
  SubjectCreateRequest,
  SubjectCreateRow,
  SubjectListInfo,
  SubjectListPagedData,
  SubjectListRequest,
} from '@/types/AcademicManagement';
import { CreateResponse, CreateResponseMulti, DeleteResponse, UpdateResponse } from '@/types/Common';
import { createAsyncThunk } from '@reduxjs/toolkit';

export const fetchSubjectList = createAsyncThunk<SubjectListPagedData, SubjectListRequest, { rejectValue: string }>(
  'SubjectManagement/list',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.SubjectManagement.GetSubjectList(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in fetching Subject List');
    }
  }
);
export const fetchSubjectLists = createAsyncThunk<any, any, { rejectValue: string }>(
  'SubjectManagement/lists',
  async (adminId, { rejectWithValue }) => {
    try {
      const response = await api.SubjectManagement.SubjectList(adminId);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in fetching Subject List');
    }
  }
);

export const addNewSubject = createAsyncThunk<CreateResponse, SubjectCreateRequest, { rejectValue: string }>(
  'SubjectManagement/addnew',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.SubjectManagement.AddNewSubject(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in adding new Subject');
    }
  }
);

export const addNewSubjectMulti = createAsyncThunk<CreateResponseMulti, SubjectCreateRow[], { rejectValue: string }>(
  'SubjectManagement/addnewmulti',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.SubjectManagement.AddMultipleSubjects(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in adding multipe Subjects');
    }
  }
);

export const updateSubject = createAsyncThunk<UpdateResponse, SubjectListInfo, { rejectValue: string }>(
  'SubjectManagement/update',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.SubjectManagement.UpdateSubject(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in updating Subject');
    }
  }
);

export const deleteSubject = createAsyncThunk<DeleteResponse, number, { rejectValue: string }>(
  'SubjectManagement/delete',
  async (SubjectId, { rejectWithValue }) => {
    try {
      const response = await api.SubjectManagement.DeleteSubject(SubjectId);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in deleting Subject');
    }
  }
);
