/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Avatar,
  Autocomplete,
  Box,
  Divider,
  Grid,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  useTheme,
  IconButton,
  Collapse,
  Tooltip,
  FormControl,
  Chip,
  Snackbar,
  Alert,
} from '@mui/material';
import styled from 'styled-components';
import { CLASS_SELECT, YEAR_SELECT } from '@/config/Selection';
import SearchIcon from '@mui/icons-material/Search';
import Popup from '@/components/shared/Popup/Popup';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import { CloseIcon } from '@/theme/overrides/CustomIcons';
import useSettings from '@/hooks/useSettings';
import { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import DateSelect from '@/components/shared/Selections/DateSelect';
import MenuEditDelete from '@/components/shared/Selections/MenuEditDelete';
import { purplePreset } from '@/utils/Colors';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useAppSelector } from '@/hooks/useAppSelector';
import { getdashboardBdayListData, getdashboardBdayListStatus } from '@/config/storeSelectors';
import { fetchDashboardBday } from '@/store/Dashboard/dashboard.thunks';
import useAuth from '@/hooks/useAuth';
import birthday from '@/assets/birthday/birthday-cake.png';

export const data = [
  {
    class: 'VII-B',
    name: 'Ajesh',
    academicYear: '2023-2024',
    adm_date: '12/06/2005',
    g_name: 'Parent',
    g_number: 64106464513,
    status: 'Published',
  },
  {
    class: 'VI-C',
    name: 'David',
    meetingID: 'IDGHDGB',
    academicYear: '2023-2024',
    adm_date: '12/06/2005',
    g_name: 'Parent',
    g_number: 64106464513,
    status: 'Published',
  },
  {
    class: 'XII-B',
    name: 'Peter',
    meetingID: 'IDGHDGB',
    academicYear: '2023-2024',
    adm_date: '12/06/2005',
    g_name: 'Parent',
    g_number: 64106464513,
    status: 'Published',
  },
  {
    class: 'V-A',
    name: 'Christopher',
    meetingID: 'IDGHDGB',
    academicYear: '2023-2024',
    adm_date: '12/06/2005',
    g_name: 'Parent',
    g_number: 64106464513,
    status: 'Published',
  },
  {
    class: 'II-B',
    name: 'john',
    meetingID: 'IDGHDGB',
    academicYear: '2023-2024',
    adm_date: '12/06/2005',
    g_name: 'Parent',
    g_number: 64106464513,
    status: 'Published',
  },
  {
    class: 'XII-C',
    name: 'Micheal',
    meetingID: 'IDGHDGB',
    academicYear: '2023-2024',
    adm_date: '12/06/2005',
    g_name: 'Parent',
    g_number: 64106464513,
    status: 'Published',
  },
  {
    class: 'I-B',
    name: 'jack',
    meetingID: 'IDGHDGB',
    academicYear: '2023-2024',
    adm_date: '12/06/2005',
    g_name: 'Parent',
    g_number: 64106464513,
    status: 'Published',
  },
  {
    class: 'VII-A',
    name: 'Ajesh',
    meetingID: 'IDGHDGB',
    academicYear: '2023-2024',
    adm_date: '12/06/2005',
    g_name: 'Parent',
    g_number: 64106464513,
    status: 'Published',
  },
  {
    class: 'VI-C',
    name: 'Ajesh',
    meetingID: 'IDGHDGB',
    academicYear: '2023-2024',
    adm_date: '12/06/2005',
    g_name: 'Parent',
    g_number: 64106464513,
    status: 'Published',
  },
  {
    class: 'X-B',
    name: 'Ajesh',
    meetingID: 'IDGHDGB',
    academicYear: '2023-2024',
    adm_date: '12/06/2005',
    g_name: 'Parent',
    g_number: 64106464513,
    status: 'Published',
  },
];

const StudentBdayRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  .student_card {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  }

  .Card {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 40px);
      flex-grow: 1;

      .card-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: auto;
        ::-webkit-scrollbar {
          width: 0px;
        }
      }
    }
  }
`;

function StudentBday() {
  const { user } = useAuth();
  const adminId: number | undefined = user?.accountId;
  const dispatch = useAppDispatch();
  const dashboardBdayListStatus = useAppSelector(getdashboardBdayListStatus);
  const dashboardBdayListData = useAppSelector(getdashboardBdayListData);

  React.useEffect(() => {
    if (dashboardBdayListStatus === 'idle') {
      dispatch(fetchDashboardBday(adminId));
    }
  }, [dispatch, dashboardBdayListStatus, adminId]);
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const [changeView, setChangeView] = useState(false);
  const [showFilter, setShowFilter] = useState(true);
  const [Delete, setDelete] = useState(false);

  const [open, setOpen] = useState(false);
  const [openNew, setOpenNew] = useState(false);
  const [showNewMultiple, setShowNewMultiple] = useState(false);

  const handleToggle = () => {
    setChangeView((prevChangeView) => !prevChangeView);
  };

  const handleClickDelete = () => setDelete(true);
  const handleClickCloseDelete = () => setDelete(false);
  const handlePageChange = useCallback((event: unknown, newPage: number) => {
    // loadClassList({ ...currentClassListRequest, pageNumber: newPage + 1 });
  }, []);

  const handleChangeRowsPerPage = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const newPageSize = +event.target.value;
    // loadClassList({ ...currentClassListRequest, pageNumber: 1, pageSize: newPageSize });
  }, []);

  const handleClose = () => {
    setOpen(false);
  };
  const handleOpenNew = () => {
    setOpenNew(true);
  };

  const handleCloseNew = () => {
    setOpenNew(false);
  };
  const handleToggleNewMultiple = () => {
    setShowNewMultiple((prevState) => !prevState);
  };

  const [isSnackbarOpen, setIsSnackbarOpen] = useState(false);

  const handleCopyClick = (text: string) => {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        setIsSnackbarOpen(true);
      })
      .catch((err) => {
        console.error('Unable to copy link to clipboard', err);
      });
  };

  const handleCloseSnackbar = () => {
    setIsSnackbarOpen(false);
  };

  const StudentBdayColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'studentName',
        headerLabel: 'Student Name',
        renderCell: (row) => {
          return (
            <Stack direction="row" gap={0.5} alignItems="center">
              <Typography variant="subtitle2">:</Typography>
              <Avatar alt="" src="/static/images/avatar/1.jpg" />
              <Typography variant="subtitle2">{row.studentName}</Typography>
            </Stack>
          );
        },
      },
      {
        name: 'dob',
        headerLabel: 'Date of Birth',
        renderCell: (row) => {
          return <Typography variant="subtitle2">: {row.studentDob.split('T')[0]}</Typography>;
        },
      },
      {
        name: 'class',
        headerLabel: 'Class',
        renderCell: (row) => {
          return (
            <Stack direction="row" alignItems="center" maxWidth="90%" gap={0.5}>
              <Typography variant="subtitle2">:</Typography>
              <Chip
                size="small"
                label={row.className}
                variant="outlined"
                sx={{
                  border: '0px',
                  backgroundColor: isLight ? purplePreset.lighter : '',
                  color: isLight ? purplePreset.main : '',
                  width: 'auto',
                }}
              />
            </Stack>
          );
        },
      },
    ],
    [isLight]
  );

  return (
    <Page title="Student Details">
      <StudentBdayRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17} width="100%">
              Student B'day List
            </Typography>
            <Box pb={1} sx={{ flexShrink: 0 }}>
              <Tooltip title="Search">
                <IconButton aria-label="" color="primary" sx={{ mr: 1 }} onClick={() => setShowFilter((x) => !x)}>
                  <SearchIcon />
                </IconButton>
              </Tooltip>
              {/* <Button
                sx={{ borderRadius: '20px' }}
                size="small"
                variant="outlined"
                onClick={() => setCreatePopup(true)}
              >
                <MdAdd size="20px" /> Create
              </Button> */}
            </Box>
          </Stack>
          <Stack>
            <Divider />
          </Stack>
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={2}>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Autocomplete
                        options={YEAR_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select year" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Autocomplete
                        options={CLASS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select class" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Select Date
                      </Typography>
                      <DateSelect />
                    </FormControl>
                  </Grid>

                  <Grid item lg={1} xs={12}>
                    <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                      <Button variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                      {/* <Button variant="contained" color="primary" fullWidth>
                          Search
                        </Button> */}
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Box className="card-container" my={2}>
              {dashboardBdayListData.length !== 0 ? (
                <Grid container spacing={2}>
                  {dashboardBdayListData.map((student, rowIndex) => (
                    <Grid item xl={4} lg={6} md={12} sm={6} xs={12}>
                      <Card
                        className="student_card"
                        sx={{ p: 2, boxShadow: 0, border: `1px solid ${theme.palette.grey[300]}` }}
                      >
                        <Stack direction="row" alignItems="top" justifyContent="space-between">
                          <Box flexGrow={1}>
                            {StudentBdayColumns.map((item) => (
                              <Stack direction="row">
                                <Grid container>
                                  <Grid item lg={5} xs={6}>
                                    <Typography key={item.name} variant="subtitle1" fontSize={13} mr={2}>
                                      {item.headerLabel}
                                    </Typography>
                                  </Grid>
                                  <Grid item lg={7} xs={6} mb={0} mt="auto">
                                    <Typography variant="h6" fontSize={13}>
                                      {item.dataKey
                                        ? `:${' '}${(student as { [key: string]: any })[item.dataKey ?? '']}`
                                        : item && item.renderCell && item.renderCell(student, rowIndex)}
                                    </Typography>
                                  </Grid>
                                </Grid>
                              </Stack>
                            ))}
                          </Box>
                          <MenuEditDelete
                            Edit={() => {
                              return 0;
                            }}
                            Delete={handleClickDelete}
                          />
                        </Stack>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              ) : (
                <Box sx={{ height: '100%' }}>
                  <Stack alignItems="center" justifyContent="center" height="100%" spacing={3}>
                    <img src={birthday} alt="" height={200} />
                    <Typography variant="subtitle1">No Birthdays on selected Date</Typography>
                  </Stack>
                </Box>
              )}
            </Box>
            <Snackbar
              color=""
              anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
              open={isSnackbarOpen}
              autoHideDuration={3000}
              onClose={(event, reason) => {
                if (reason === 'timeout' || reason === 'clickaway') {
                  setIsSnackbarOpen(false);
                }
              }}
              action={
                <IconButton aria-label="close" color="inherit" sx={{ p: 0.5 }} onClick={handleCloseSnackbar}>
                  <CloseIcon />
                </IconButton>
              }
            >
              <Alert onClose={handleCloseSnackbar} severity="success" sx={{ width: '100%' }}>
                Link copied to clipboard!
              </Alert>
            </Snackbar>
          </div>
        </Card>
      </StudentBdayRoot>
      <Popup
        size="xs"
        state={Delete}
        onClose={handleClickCloseDelete}
        popupContent={<DeleteMessage message="Are you sure want to delete?" />}
      />
    </Page>
  );
}

export default StudentBday;
