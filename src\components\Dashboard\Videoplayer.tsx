/* eslint-disable no-unneeded-ternary */
import React, { useState } from 'react';
import ReactPlayer from 'react-player';
import { BsFillPlayCircleFill } from 'react-icons/bs';
import videoDummy from '@/assets/video-dummy.jpg';

type VideoPlayerProps = {
  url: string;
  thumbnail?: string;
  onPlay?: () => void;
  onEnd?: () => void;
};

const VideoPlayer = ({ url, thumbnail, onPlay, onEnd }: VideoPlayerProps) => {
  const [isPlaying, setIsPlaying] = useState(false);

  const handlePlay = () => {
    setIsPlaying(true);
    if (onPlay) {
      onPlay();
    }
  };

  const handleEnd = () => {
    setIsPlaying(false);
    if (onEnd) {
      onEnd();
    }
  };

  return (
    <ReactPlayer
      controls
      width="100%"
      height="100%"
      playIcon={thumbnail && <BsFillPlayCircleFill color="white" fontSize={50} />}
      light={thumbnail ? thumbnail : ''}
      url={url}
      playing={isPlaying}
      muted
      onPlay={handlePlay}
      onEnded={handleEnd}
      onBuffer={handlePlay}
    />
  );
};

export default VideoPlayer;
