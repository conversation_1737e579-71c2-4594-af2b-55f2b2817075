/* eslint-disable no-useless-catch */
import axios, { AxiosError, AxiosInstance, AxiosResponse } from 'axios';
import { Api, APIResponse } from '@/api/base/types';
import ApiUrls, { AnonymousUrls } from '@/config/ApiUrls';
import { TokenStoragekey } from '@/config/Constants';
import TokenStorage from '@/utils/TokenStorage';

const publicApiClient = axios.create({
  baseURL: ApiUrls.BaseUrl,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true,
});

const privateApiClient = axios.create({
  baseURL: ApiUrls.BaseUrl,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true,
});

const refreshInstance = axios.create({
  baseURL: ApiUrls.BaseUrl,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true,
});

privateApiClient.interceptors.request.use(
  (config) => {
    if (config.url) {
      const url = config.url.toLowerCase();
      if (!AnonymousUrls.some((x) => url.startsWith(x.toLowerCase()))) {
        const token = localStorage.getItem(TokenStoragekey);
        const { headers } = config;
        if (token) {
          headers.Authorization = `Bearer ${token}`;
        }
      }
    }
    return config;
  },
  (error) => {
    Promise.reject(error);
  }
);

privateApiClient.interceptors.response.use(
  (response) => response,
  async (error: any) => {
    const originalRequest = error.config;

    if (error.response?.status === 401) {
      try {
        // Attempt to refresh the access token
        const accessTokenInStorage = TokenStorage.getAccessToken();
        if (accessTokenInStorage) {
          const response = await refreshInstance.post(
            ApiUrls.RenewAccessToken,
            { accessToken: accessTokenInStorage },
            { withCredentials: true }
          );
          const { accessToken } = response.data;

          // Update the access and refresh tokens in local storage
          TokenStorage.setAccessToken(accessToken);

          // Retry the original request with the new access token
          originalRequest.headers.Authorization = `Bearer ${accessToken}`;
        }

        return await refreshInstance(originalRequest);
      } catch (e) {
        // If refreshing the access token fails, log the user out and redirect to the login page
        TokenStorage.clearAccessToken();
        window.location.reload();
      }
    }

    return Promise.reject(error);
  }
);

const logErrors = (error: AxiosError) => {
  if (error.response) {
    // The request was made and the server responded with a status code
    // that falls out of the range of 2xx
    console.log(error.response.data);
    console.log(error.response.status);
    console.log(error.response.headers);
  } else if (error.request) {
    // The request was made but no response was received
    // `error.request` is an instance of XMLHttpRequest in the browser and an instance of
    // http.ClientRequest in node.js
    console.log(error.request);
  } else {
    // Something happened in setting up the request that triggered an Error
    console.log('Error', error.message);
  }
  console.log(error.config);
};

function ApiFactory(axiosInstance: AxiosInstance) {
  const api: Api = {
    get: async <T = any>(url: string, config?: any): Promise<APIResponse<T>> => {
      try {
        const response = await axiosInstance.get<T>(url, config);
        return { status: response.status, data: response.data };
      } catch (error: unknown) {
        logErrors(error as AxiosError);
        throw error;
      }
    },
    post: async <T = any, D = any>(url: string, data: D, config?: any): Promise<APIResponse<T>> => {
      try {
        const response = await axiosInstance.post<T, AxiosResponse<T>, D>(url, data, config);
        return { status: response.status, data: response.data };
      } catch (error: unknown) {
        // logErrors(error as AxiosError);
        throw error;
      }
    },
    put: async <T = any, D = any>(url: string, data: D, config?: any): Promise<APIResponse<T>> => {
      try {
        const response = await axiosInstance.put<T, AxiosResponse<T>, D>(url, data, config);
        return { status: response.status, data: response.data };
      } catch (error: unknown) {
        logErrors(error as AxiosError);
        throw error;
      }
    },
    patch: async <T = any, D = any>(url: string, data: D, config?: any): Promise<APIResponse<T>> => {
      try {
        const response = await axiosInstance.patch<T, AxiosResponse<T>, D>(url, data, config);
        return { status: response.status, data: response.data };
      } catch (error: unknown) {
        logErrors(error as AxiosError);
        throw error;
      }
    },
    delete: async <T = any>(url: string, config?: any): Promise<APIResponse<T>> => {
      try {
        const response = await axiosInstance.delete<T>(url, config);
        return { status: response.status, data: response.data };
      } catch (error: unknown) {
        logErrors(error as AxiosError);
        throw error;
      }
    },
  };
  return api;
}

export const publicApi = ApiFactory(publicApiClient);
export const privateApi = ApiFactory(privateApiClient);
