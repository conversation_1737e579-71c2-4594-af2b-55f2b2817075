/* eslint-disable no-nested-ternary */
import React, { useState, useRef } from 'react';
import {
  Button,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  Typography,
  Box,
  Avatar,
  Stack,
  useTheme,
} from '@mui/material';
import { PhotoCamera } from '@mui/icons-material';
import AvatarEditor from 'react-avatar-editor';
import EditRoundedIcon from '@mui/icons-material/EditRounded';
import AddIcon from '@mui/icons-material/Add';
import LoadingButton from '@mui/lab/LoadingButton';
import { motion, AnimatePresence } from 'framer-motion';
import useSettings from '@/hooks/useSettings';

const backdropVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1 },
};

const popupVariants = {
  hidden: { scale: 0.5, opacity: 0 },
  visible: {
    scale: 1,
    opacity: 1,
    transition: {
      type: 'spring',
      stiffness: 200,
      damping: 20,
    },
  },
};

type ProfileImageProps = {
  selectedImage: string | null;
  setFieldValue: any;
  width: string;
  height: string;
};

const ProfileImage = ({ selectedImage, setFieldValue, width, height }: ProfileImageProps) => {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const [open, setOpen] = useState(false);
  const [image, setImage] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [croppedImage, setCroppedImage] = useState<string | null>(null);
  const editorRef = useRef<AvatarEditor | null>(null);

  // Open dialog to upload and edit image
  const handleClickOpen = () => {
    setOpen(true);
  };

  // Close the dialog
  const handleClose = () => {
    setOpen(false);
  };

  // Handle image file selection
  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setImage(reader.result as string); // Set selected image
        setCroppedImage(null); // Clear previous cropped image
        setOpen(true);
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle saving the cropped image
  // const handleSave = () => {
  //   setLoading(true);
  //   if (editorRef.current) {
  //     const canvas = editorRef.current.getImage();
  //     const dataUrl = canvas.toDataURL(); // Get cropped image as base64
  //     setCroppedImage(dataUrl); // Set the cropped image
  //     setTimeout(() => {
  //       setLoading(false);
  //       setOpen(false); // Close the dialog
  //     }, 1500);
  //   }
  // };

  const handleSave = () => {
    setLoading(true);
    if (editorRef.current) {
      requestAnimationFrame(() => {
        editorRef.current.getImage().toBlob(
          (blob: any) => {
            if (blob) {
              const reader = new FileReader();
              reader.onloadend = () => {
                setCroppedImage(reader.result as string);
                const imgUrl = URL.createObjectURL(blob); // Create a URL from the blob
                console.log('imgUrl', imgUrl);
                console.log('blob', blob);
                setFieldValue('studentImage', imgUrl); // Set it in Formik's state
                setLoading(false);
                setOpen(false);
                // Cleanup the URL after usage (optional, based on your use case)
                // setTimeout(() => {
                //   URL.revokeObjectURL(imgUrl);
                // }, 10000);
              };
              reader.readAsDataURL(blob);
            }
          },
          'image/png',
          0.9
        ); // Adjust quality for better performance if needed
      });
    }
  };
  return (
    <Box sx={{ textAlign: 'center' }}>
      <Stack direction="row" justifyContent="center">
        {selectedImage ? (
          <Box position="relative">
            <Avatar
              sx={{ width, height }}
              src={selectedImage ?? ''}
              // onClick={handleClickOpen}
            />

            <IconButton
              onClick={handleClickOpen}
              color="primary"
              component="label"
              size="small"
              sx={{
                position: 'absolute',
                bottom: -5,
                right: -5,
                bgcolor: 'rgba(0, 0, 0, 0.6)',
                color: 'white',
                '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.8)' },
              }}
            >
              <EditRoundedIcon fontSize="small" />
            </IconButton>
          </Box>
        ) : (
          <IconButton color="primary" component="label" sx={{ position: 'relative', p: 0.5 }}>
            <Avatar
              sx={{
                backgroundColor: isLight ? theme.palette.common.white : theme.palette.grey[900],
                color: theme.palette.primary.main,
                cursor: 'pointer',
                position: 'relative',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: '0px',
                  left: '0px',
                  right: '0px',
                  bottom: '0px',
                  border: `2px ${theme.palette.primary.main}`,
                  borderStyle: selectedImage || image ? 'solid' : 'dashed',
                  // borderStyle: isAnimating ? 'solid' : 'dashed',
                  borderRadius: '50%',
                  // animation: isAnimating ? `${rotateAnimation} .5s linear` : 'none',
                },
                width,
                height,
              }}
              src={croppedImage ?? ''}
            />
            {!croppedImage && (
              <input type="file" accept="image/*" style={{ display: 'none' }} onChange={handleImageChange} />
            )}

            {!croppedImage ? (
              <AddIcon
                sx={{
                  position: 'absolute',
                  bottom: 3,
                  right: 3,
                  fontSize: '15px',
                  cursor: 'pointer',
                  bgcolor: theme.palette.primary.main,
                  color: theme.palette.common.white,
                  borderRadius: '50px',
                }}
              />
            ) : (
              <IconButton
                onClick={handleClickOpen}
                color="primary"
                component="label"
                size="small"
                sx={{
                  position: 'absolute',
                  bottom: 0,
                  right: 0,
                  bgcolor: 'rgba(0, 0, 0, 0.6)',
                  color: 'white',
                  '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.8)' },
                }}
              >
                <EditRoundedIcon sx={{ fontSize: '15px' }} />
              </IconButton>
            )}
          </IconButton>
        )}
      </Stack>

      <AnimatePresence>
        {open && (
          <motion.div
            className="popup-backdrop"
            variants={backdropVariants}
            initial="hidden"
            animate="visible"
            exit="hidden"
            onClick={() => setOpen(false)}
            style={{
              position: 'fixed',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              background: 'rgba(0, 0, 0, 0.5)',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              //   zIndex: theme.zIndex.appBar,
              zIndex: 1,
            }}
          >
            <motion.div
              variants={popupVariants}
              initial="hidden"
              animate="visible"
              exit="hidden"
              className="popup"
              style={{
                background: isLight ? 'white' : theme.palette.grey[800],
                // paddingTop: '10px',
                paddingBottom: '10px',
                borderRadius: '10px',
                textAlign: 'start',
                maxWidth: '400px',
              }}
              onClick={(e) => e.stopPropagation()}
            >
              <DialogTitle sx={{ color: !isLight ? 'white' : theme.palette.grey[800] }}>Edit Avatar</DialogTitle>
              {/* <Typography variant="h5" mb={2}>
              Edit Avatar
              </Typography> */}
              <DialogContent>
                <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                  <Button
                    size="small"
                    component="label"
                    color="primary"
                    variant="outlined"
                    // bgcolor={theme.palette.primary.main}
                    // px={1.5}
                    // py={0}
                    // borderRadius={10}
                    // direction="row"
                    // alignItems="center"
                    // gap={1}
                    sx={{ mb: 2, borderRadius: 10 }}
                  >
                    <Typography variant="subtitle2" mr={1}>
                      Upload new photo
                    </Typography>

                    <PhotoCamera />
                    <input type="file" accept="image/*" style={{ display: 'none' }} onChange={handleImageChange} />
                  </Button>
                  {selectedImage ? (
                    <AvatarEditor
                      ref={editorRef}
                      image={selectedImage}
                      width={250}
                      height={250}
                      border={50}
                      borderRadius={125}
                      scale={1.2}
                    />
                  ) : image ? (
                    <AvatarEditor
                      ref={editorRef}
                      image={image}
                      width={250}
                      height={250}
                      border={50}
                      borderRadius={125}
                      scale={1.2}
                    />
                  ) : null}
                </div>
              </DialogContent>
              <DialogActions>
                <Button disabled={loading} onClick={handleClose} color="secondary">
                  Cancel
                </Button>
                <LoadingButton
                  sx={{ width: loading ? '130px' : '', mr: '10px' }}
                  loading={loading}
                  loadingPosition="start"
                  onClick={handleSave}
                  color="primary"
                  disabled={loading}
                  variant={loading ? 'outlined' : 'text'}
                >
                  {loading ? 'Saving...' : 'Save'}
                </LoadingButton>
              </DialogActions>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </Box>
  );
};

export default ProfileImage;
