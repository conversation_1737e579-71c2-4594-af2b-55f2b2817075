import Loadable from '@/components/shared/Loadable';
import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';

const ManageBusFees = Loadable(lazy(() => import('@/pages/ManageBusFees')));
const BusList = Loadable(lazy(() => import('@/features/ManageBusFees/BusList')));
const FeesList = Loadable(lazy(() => import('@/features/ManageFee/FeesList/BasicFeeList')));
const FeeCollection = Loadable(lazy(() => import('@/features/ManageFee/FeeCollection/FeeCollection')));
const PayFee = Loadable(lazy(() => import('@/features/ManageFee/PayFee/PayFee')));
const PaidList = Loadable(lazy(() => import('@/features/ManageFee/PaidList/TotalFeePaidList')));
const DailyReport = Loadable(lazy(() => import('@/features/ManageFee/Unused/DailyReport')));
const PendingList = Loadable(lazy(() => import('@/features/ManageFee/PendingList/TotalFeePendingList')));
const FeeSetting = Loadable(lazy(() => import('@/features/ManageFee/BasicFeeSetting/FeeSetting')));
const FineSetting = Loadable(lazy(() => import('@/features/ManageFee/Unused/FineSetting')));
const OptionalFee = Loadable(lazy(() => import('@/features/ManageFee/OptionalFee/OptionalFee')));

export const manageBusFeesRoutes: RouteObject[] = [
  {
    path: '/manage-bus-fees',
    element: <ManageBusFees />,
    children: [
      {
        path: 'bus-list',
        element: <FeesList />,
      },
      {
        path: 'bus-stop-map',
        element: <FeeCollection />,
      },
      {
        path: 'terms-list',
        element: <BusList />,
      },
      {
        path: 'term-fees-map',
        element: <PayFee />,
      },
      {
        path: 'term-student-map',
        element: <PaidList />,
      },
      {
        path: 'pay-bus-fees',
        element: <DailyReport />,
      },
      {
        path: 'paid-list',
        element: <PendingList />,
      },
      {
        path: 'pending-list',
        element: <FeeSetting />,
      },
      {
        path: 'fine-setting',
        element: <FineSetting />,
      },
      {
        path: 'optional',
        element: <OptionalFee />,
      },
    ],
  },
];
