/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Autocomplete,
  Avatar,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Typography,
  Card,
  Checkbox,
} from '@mui/material';
import styled from 'styled-components';
import { CLASS_SELECT, YEAR_SELECT } from '@/config/Selection';
import { MdDelete, MdEdit } from 'react-icons/md';
import { Students } from '@/config/TableData';

const GaurdianQuickUpdateRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  h6 {
    font-weight: 600;
  }
  .MuiTextField-root:hover .MuiInputBase-input {
    cursor: text;
  }
`;

function GaurdianQuickUpdate() {
  return (
    <Page title="Student Parent Info">
      <GaurdianQuickUpdateRoot>
        <Card elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Typography variant="h6" fontSize={17}>
            Student Parent Info
          </Typography>
          <Divider />
          <Grid pb={4} pt={2} container spacing={3}>
            <Grid item lg={4} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Academic Year
              </Typography>
              <Autocomplete
                options={YEAR_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select" />}
              />
            </Grid>
            <Grid item lg={4} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Class
              </Typography>
              <Autocomplete
                options={CLASS_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select" />}
              />
            </Grid>
            <Grid item lg={4} xs={12}>
              <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                <Button variant="contained" color="secondary" fullWidth>
                  Reset
                </Button>
                <Button variant="contained" color="primary" fullWidth>
                  Search
                </Button>
              </Stack>
            </Grid>
          </Grid>
          <Box>
            <Paper
              sx={{
                border: `1px solid #e8e8e9`,
                width: '100%',
                overflow: 'auto',
                '&::-webkit-scrollbar': {
                  width: 0,
                },
              }}
            >
              <TableContainer
                sx={{
                  maxHeight: 410,
                  width: { xs: '700px', md: '100%' },
                  '&::-webkit-scrollbar': {
                    width: 0,
                  },
                }}
              >
                <Table stickyHeader aria-label="simple table">
                  <TableHead>
                    <TableRow>
                      <TableCell>
                        <Checkbox />
                      </TableCell>
                      <TableCell>SL No</TableCell>
                      <TableCell>Adm No</TableCell>
                      <TableCell>Student Name</TableCell>
                      <TableCell>Gaurdian Name</TableCell>
                      <TableCell>Gaurdian Number</TableCell>
                      <TableCell>Action</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {Students.map((lnrow) => (
                      <TableRow hover key={lnrow.rollNo}>
                        <TableCell>
                          <Checkbox />
                        </TableCell>
                        <TableCell>{lnrow.rollNo}</TableCell>
                        <TableCell>45678</TableCell>
                        <TableCell>
                          <Stack direction="row">
                            <Avatar src={lnrow.image} sx={{ mr: 2 }} />
                            <Typography pt={0.7} fontSize={15}>
                              {lnrow.name}
                            </Typography>
                          </Stack>
                        </TableCell>
                        <TableCell>Passdaily</TableCell>
                        <TableCell>7558966668</TableCell>
                        <TableCell>
                          <MdEdit size="20px" />
                          <MdDelete size="20px" />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Paper>
          </Box>
        </Card>
      </GaurdianQuickUpdateRoot>
    </Page>
  );
}

export default GaurdianQuickUpdate;
