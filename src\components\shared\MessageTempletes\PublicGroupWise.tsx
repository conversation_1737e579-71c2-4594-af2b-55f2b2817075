/* eslint-disable no-nested-ternary */
/* eslint-disable react/no-array-index-key */
/* eslint-disable jsx-a11y/alt-text */
import React, { FormEvent, useCallback, useState } from 'react';
import {
  Divider,
  Grid,
  Paper,
  Stack,
  Button,
  Typography,
  Box,
  FormControl,
  Select,
  MenuItem,
  SelectChangeEvent,
  useTheme,
  Tooltip,
} from '@mui/material';
import styled from 'styled-components';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useAppSelector } from '@/hooks/useAppSelector';
import {
  getYearData,
  getMessageTempSubmitting,
  getPublicGroupWiseListStatus,
  getPublicGroupListData,
} from '@/config/storeSelectors';
import useAuth from '@/hooks/useAuth';
import { fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import { fetchPublicGroupWiseList, messageSendToPublicGroupWiseList } from '@/store/MessageBox/messageBox.thunks';
import LoadingButton from '@mui/lab/LoadingButton';
import Lottie from 'lottie-react';
import successIcon from '@/assets/MessageIcons/success.json';
import errorIcon from '@/assets/MessageIcons/error-message.json';
import { SendToPublicGroupWiseType, PublicGroupWiseDataType } from '@/types/MessageBox';
import ErrorMsg from '@/assets/MessageIcons/error-message.gif';
import ErrorMsg1 from '@/assets/MessageIcons/error-message1.gif';
import ErrorMsg2 from '@/assets/MessageIcons/error-message2.gif';
import SuccessMsg from '@/assets/MessageIcons/message-success.gif';
import SendIcon from '@mui/icons-material/Send';
import { SuccessIcon } from '@/theme/overrides/CustomIcons';
import { SuccessMessage } from '../Popup/SuccessMessage';
import { ErrorMessage } from '../Popup/ErrorMessage';
import { useConfirm } from '../Popup/Confirmation';
import { SendPopupProps } from '@/types/Common';
import { SendVoiceMessageToPublicGroupWiseRequest } from '@/types/VoiceMessage';
import { voiceMessageSendToPublicGroupWise } from '@/store/VoiceMessage/voiceMessage.thunk';

const GroupsRoot = styled.div`
  width: 100%;
  padding: 8px 24px;
`;

// export type PublicGroupWiseListRequest = {
//   adminId: number;
//   academicId: number;
//   groupId: number;
// };
interface PublicGroupWiseDataWithStatus extends PublicGroupWiseDataType {
  sendStatus: 'Success' | 'Failed' | '' | undefined;
}

function PublicGroupWise({ messageId, voiceId, templateId, isSubmitting }: SendPopupProps) {
  const { user } = useAuth();
  const theme = useTheme();
  const dispatch = useAppDispatch();
  const { confirm } = useConfirm();
  const defaultYearId: number | undefined = 10;
  const adminId: number = user ? user.accountId : 0;
  const [academicYearFilter, setAcademicYearFilter] = useState(defaultYearId);
  const [publicGroupFilter, setPublicGroupFilter] = useState(-1);
  const [selectedRows, setSelectedRows] = useState<PublicGroupWiseDataWithStatus[]>([]);
  const [sendResults, setSendResults] = useState<PublicGroupWiseDataWithStatus[]>([]);
  const YearData = useAppSelector(getYearData);
  const PublicGroupsData = useAppSelector(getPublicGroupListData);
  const PublicGroupWiseStatus = useAppSelector(getPublicGroupWiseListStatus);
  const [selectedData, setSelectedData] = useState<PublicGroupWiseDataWithStatus[]>([]);
  const [individualSendLoadingMap, setIndividualSendLoadingMap] = useState<Record<string, boolean>>({});
  const [isLoading, setIsloading] = useState<boolean>(false);
  const [errMsgTooltip, setErrMsgTooltip] = useState('');
  const [showSuccessMap, setShowSuccessMap] = useState<{ [key: string]: boolean }>({});
  const [disabledCheckBoxes, setDisabledCheckBoxes] = useState<boolean[]>([]);

  console.log('selectedData', selectedData);

  const initialPublicGroupListRequest = React.useMemo(
    () => ({
      adminId,
      academicId: defaultYearId,
      groupId: -1,
    }),
    [adminId, defaultYearId]
  );
  const currentPublicGroupListRequest = React.useMemo(
    () => ({
      adminId,
      academicId: academicYearFilter,
      groupId: publicGroupFilter,
    }),
    [adminId, academicYearFilter, publicGroupFilter]
  );

  const loadPublicGroupWiseList = useCallback(
    async (request: { adminId: number; academicId: number; groupId: number }) => {
      try {
        const data = await dispatch(fetchPublicGroupWiseList(request)).unwrap();
        const dataWithStatus = data.map((item) => ({
          ...item,
          sendStatus: sendResults.find((i) => i.groupId === item.groupId)?.sendStatus,
        }));
        setSelectedData(dataWithStatus);
        setSelectedRows([]);
      } catch (error) {
        console.error('Error loading Public group wise list:', error);
      }
    },
    [dispatch, sendResults]
  );

  React.useEffect(() => {
    dispatch(fetchYearList(adminId));
    // dispatch(fetchPublicGroupsList({ adminId, academicId: academicYearFilter }));
    // loadPublicGroupWiseList(initialPublicGroupListRequest);
  }, [dispatch, adminId, loadPublicGroupWiseList, initialPublicGroupListRequest]);

  const handleYearChange = (e: SelectChangeEvent) => {
    console.log('handleYearChange', e.target.value);
    setAcademicYearFilter(parseInt(YearData.filter((item) => item.accademicId === e.target.value)[0].accademicId, 10));
    loadPublicGroupWiseList({ ...currentPublicGroupListRequest, academicId: parseInt(e.target.value, 10) });
    setSelectedRows([]);
  };
  const handleGroupChange = (e: SelectChangeEvent) => {
    setPublicGroupFilter(PublicGroupsData.filter((item) => item.groupId === parseInt(e.target.value, 10))[0].groupId);
    loadPublicGroupWiseList({ ...currentPublicGroupListRequest, groupId: parseInt(e.target.value, 10) });
    setSelectedRows([]);
  };

  const handleReset = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      setAcademicYearFilter(parseInt(YearData[0].accademicId, 10));
      setPublicGroupFilter(-1);
      loadPublicGroupWiseList(initialPublicGroupListRequest);
      setDisabledCheckBoxes([]);
    },
    [YearData, loadPublicGroupWiseList, initialPublicGroupListRequest]
  );
  const handleSubmit = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      loadPublicGroupWiseList(currentPublicGroupListRequest);
      setSelectedRows([]);
    },
    [loadPublicGroupWiseList, currentPublicGroupListRequest]
  );

  const handleCancel = () => {
    loadPublicGroupWiseList(currentPublicGroupListRequest);
    setDisabledCheckBoxes([]);
  };

  const handleSendMessage = useCallback(async () => {
    setIsloading(true);
    try {
      console.log(selectedRows);
      if (!selectedRows || selectedRows.length === 0) {
        const errorMessage = <ErrorMessage icon={ErrorMsg1} message="Please Select Atleast one Group Member to sent" />;
        await confirm(errorMessage, 'Public group not selected', { okLabel: 'Ok', showOnlyOk: true });
        return;
      }
      const sendRequests: SendToPublicGroupWiseType[] = await Promise.all(
        selectedRows?.map(async (item) => {
          const { accademicTime, ...rest } = item;
          const sendReq = { messageId, adminId, ...rest };
          return sendReq;
        }) || []
      );
      const messageSendCount = sendRequests.length;

      sendRequests.map((row) => setIndividualSendLoadingMap((prevMap) => ({ ...prevMap, [row.groupId]: true })));
      const actionResult = await dispatch(messageSendToPublicGroupWiseList(sendRequests));
      sendRequests.map((row) => setIndividualSendLoadingMap((prevMap) => ({ ...prevMap, [row.groupId]: false })));

      if (actionResult && Array.isArray(actionResult.payload)) {
        const results: PublicGroupWiseDataWithStatus[] = actionResult.payload;

        const errorMessages = results.find((result) => result.sendStatus === 'Failed');
        const successMessages = results.find((result) => result.sendStatus === 'Success');
        if (!errorMessages) {
          const successMessage = (
            <SuccessMessage icon={SuccessMsg} message={`Message sent to ${messageSendCount} Recipient.`} />
          );
          await confirm(successMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        } else if (!successMessages) {
          const errorMessage = (
            <ErrorMessage
              icon={ErrorMsg2}
              message="No messages sent to any Group Members, Please check the numbers and Try again"
            />
          );
          await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        } else {
          const errorMessage = (
            <ErrorMessage
              icon={ErrorMsg}
              message="Error sending messages to some Group Members, Please recheck and Try again"
            />
          );
          await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        }
        const updatedSelectedData = selectedData.map((rowData) => {
          const selectedRow = selectedRows.find((sr) => sr.groupId === rowData.groupId);
          console.log('selectedRow::::', selectedRow);
          if (selectedRow) {
            const result = results.find((r) => r.groupId === selectedRow.groupId);

            if (result) {
              return { ...rowData, status: result.sendStatus || '' };
            }
          }
          return rowData;
        });
        // setSelectedRows([]);
        setSelectedData(updatedSelectedData);
      } else {
        const errorMessage = <ErrorMessage message="Error while sending messages, Please Try again" />;
        await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
      }
      setSelectedRows([]);

      sendRequests.map((row) => setShowSuccessMap((prevMap) => ({ ...prevMap, [row.groupId]: true })));
      // Update disabled state for each selected row
      const updatedDisabledCheckBoxes = selectedData.map((selectedItem) =>
        selectedRows.includes(selectedItem)
          ? !disabledCheckBoxes[selectedData.indexOf(selectedItem)]
          : disabledCheckBoxes[selectedData.indexOf(selectedItem)]
      );

      setDisabledCheckBoxes(updatedDisabledCheckBoxes);

      setTimeout(() => {
        sendRequests.map((row) => setShowSuccessMap((prevMap) => ({ ...prevMap, [row.groupId]: false })));
      }, 5000);
    } catch (error) {
      console.error('Error sending messages:', error);
    }
  }, [dispatch, confirm, messageId, selectedRows, adminId, selectedData, setSelectedRows, disabledCheckBoxes]);

  const handleSendMessageIndivitual = useCallback(
    async (row: PublicGroupWiseDataWithStatus, rowIndex: number) => {
      setIsloading(false);
      try {
        const { accademicTime, ...rest } = row;

        // Check if the row is already loading, if yes, return
        if (individualSendLoadingMap[row.groupId]) {
          return;
        }

        // Set loading state for the specific row
        setIndividualSendLoadingMap((prevMap) => ({ ...prevMap, [row.groupId]: true }));

        const updatedDisabledCheckBoxes = [...disabledCheckBoxes];
        updatedDisabledCheckBoxes[rowIndex] = !updatedDisabledCheckBoxes[rowIndex];
        setDisabledCheckBoxes(updatedDisabledCheckBoxes);

        const sendRequests: SendToPublicGroupWiseType[] = [{ messageId, adminId, ...rest }];

        const actionResult = await dispatch(messageSendToPublicGroupWiseList(sendRequests));

        // Reset loading state after the action is complete
        setIndividualSendLoadingMap((prevMap) => ({ ...prevMap, [row.groupId]: false }));

        if (actionResult && Array.isArray(actionResult.payload)) {
          const results: PublicGroupWiseDataWithStatus[] = actionResult.payload;

          // Update the sendResults state based on the previous state
          setSendResults((prevResults) => [...prevResults, ...results]);

          // Update the selectedData state with the new status based on the previous state
          setSelectedData((prevResults) =>
            prevResults.map((item) => {
              const resultForItem = results.find((result) => result.groupId === item.groupId);

              if (resultForItem) {
                return {
                  ...item,
                  sendStatus:
                    item.sendStatus !== 'Success' && item.sendStatus !== 'Failed'
                      ? resultForItem.sendStatus
                      : item.sendStatus,
                };
              }

              return item;
            })
          );
          setSelectedRows([]);

          const errorMessages = results.find((result) => result.sendStatus === 'Failed');
          if (errorMessages) {
            setErrMsgTooltip('No messages sent to any Class Sections, Please check the numbers and Try again');
          } else {
            setErrMsgTooltip('Error sending messages to some Class Sections, Please recheck and Try again');
          }
          setSelectedRows([]);
        } else {
          setErrMsgTooltip('Error while sending messages, Please Try again');
        }
        setShowSuccessMap((prevMap) => ({ ...prevMap, [row.groupId]: true }));

        setTimeout(() => {
          setShowSuccessMap((prevMap) => ({ ...prevMap, [row.groupId]: false }));
          // setIsButtonDisabledMap((prevMap) => ({ ...prevMap, [row.classSectionName]: true }));
        }, 5000);
      } catch (error) {
        console.error('Error sending messages:', error);
      }
    },
    [dispatch, messageId, adminId, setSendResults, individualSendLoadingMap, disabledCheckBoxes]
  );

  const handleSendVoiceMessage = useCallback(async () => {
    setIsloading(true);
    try {
      console.log(selectedRows);
      if (!selectedRows || selectedRows.length === 0) {
        const errorMessage = <ErrorMessage icon={ErrorMsg1} message="Please Select Atleast one Group Member to sent" />;
        await confirm(errorMessage, 'Public group not selected', { okLabel: 'Ok', showOnlyOk: true });
        return;
      }
      const sendRequests: SendVoiceMessageToPublicGroupWiseRequest[] = await Promise.all(
        selectedRows?.map(async (item) => {
          const { accademicTime, ...rest } = item;
          const sendReq = { voiceId, templateId, adminId, ...rest };
          return sendReq;
        }) || []
      );
      const messageSendCount = sendRequests.length;

      sendRequests.map((row) => setIndividualSendLoadingMap((prevMap) => ({ ...prevMap, [row.groupId]: true })));
      const actionResult = await dispatch(voiceMessageSendToPublicGroupWise(sendRequests));
      sendRequests.map((row) => setIndividualSendLoadingMap((prevMap) => ({ ...prevMap, [row.groupId]: false })));

      if (actionResult && Array.isArray(actionResult.payload)) {
        const results: PublicGroupWiseDataWithStatus[] = actionResult.payload;

        const errorMessages = results.find((result) => result.sendStatus === 'Failed');
        const successMessages = results.find((result) => result.sendStatus === 'Success');
        if (!errorMessages) {
          const successMessage = (
            <SuccessMessage icon={SuccessMsg} message={`Message sent to ${messageSendCount} Recipient.`} />
          );
          await confirm(successMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        } else if (!successMessages) {
          const errorMessage = (
            <ErrorMessage
              icon={ErrorMsg2}
              message="No messages sent to any Group Members, Please check the numbers and Try again"
            />
          );
          await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        } else {
          const errorMessage = (
            <ErrorMessage
              icon={ErrorMsg}
              message="Error sending messages to some Group Members, Please recheck and Try again"
            />
          );
          await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        }
        const updatedSelectedData = selectedData.map((rowData) => {
          const selectedRow = selectedRows.find((sr) => sr.groupId === rowData.groupId);
          console.log('selectedRow::::', selectedRow);
          if (selectedRow) {
            const result = results.find((r) => r.groupId === selectedRow.groupId);

            if (result) {
              return { ...rowData, status: result.sendStatus || '' };
            }
          }
          return rowData;
        });
        // setSelectedRows([]);
        setSelectedData(updatedSelectedData);
      } else {
        const errorMessage = <ErrorMessage message="Error while sending messages, Please Try again" />;
        await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
      }
      setSelectedRows([]);

      sendRequests.map((row) => setShowSuccessMap((prevMap) => ({ ...prevMap, [row.groupId]: true })));
      // Update disabled state for each selected row
      const updatedDisabledCheckBoxes = selectedData.map((selectedItem) =>
        selectedRows.includes(selectedItem)
          ? !disabledCheckBoxes[selectedData.indexOf(selectedItem)]
          : disabledCheckBoxes[selectedData.indexOf(selectedItem)]
      );

      setDisabledCheckBoxes(updatedDisabledCheckBoxes);

      setTimeout(() => {
        sendRequests.map((row) => setShowSuccessMap((prevMap) => ({ ...prevMap, [row.groupId]: false })));
      }, 5000);
    } catch (error) {
      console.error('Error sending messages:', error);
    }
  }, [
    dispatch,
    confirm,
    voiceId,
    templateId,
    selectedRows,
    adminId,
    selectedData,
    setSelectedRows,
    disabledCheckBoxes,
  ]);

  const handleSendVoiceMessageIndivitual = useCallback(
    async (row: PublicGroupWiseDataWithStatus, rowIndex: number) => {
      setIsloading(false);
      try {
        const { accademicTime, ...rest } = row;

        // Check if the row is already loading, if yes, return
        if (individualSendLoadingMap[row.groupId]) {
          return;
        }

        // Set loading state for the specific row
        setIndividualSendLoadingMap((prevMap) => ({ ...prevMap, [row.groupId]: true }));

        const updatedDisabledCheckBoxes = [...disabledCheckBoxes];
        updatedDisabledCheckBoxes[rowIndex] = !updatedDisabledCheckBoxes[rowIndex];
        setDisabledCheckBoxes(updatedDisabledCheckBoxes);

        const sendRequests: SendVoiceMessageToPublicGroupWiseRequest[] = [{ voiceId, templateId, adminId, ...rest }];

        const actionResult = await dispatch(voiceMessageSendToPublicGroupWise(sendRequests));

        // Reset loading state after the action is complete
        setIndividualSendLoadingMap((prevMap) => ({ ...prevMap, [row.groupId]: false }));

        if (actionResult && Array.isArray(actionResult.payload)) {
          const results: PublicGroupWiseDataWithStatus[] = actionResult.payload;

          // Update the sendResults state based on the previous state
          setSendResults((prevResults) => [...prevResults, ...results]);

          // Update the selectedData state with the new status based on the previous state
          setSelectedData((prevResults) =>
            prevResults.map((item) => {
              const resultForItem = results.find((result) => result.groupId === item.groupId);

              if (resultForItem) {
                return {
                  ...item,
                  sendStatus:
                    item.sendStatus !== 'Success' && item.sendStatus !== 'Failed'
                      ? resultForItem.sendStatus
                      : item.sendStatus,
                };
              }

              return item;
            })
          );
          setSelectedRows([]);

          const errorMessages = results.find((result) => result.sendStatus === 'Failed');
          if (errorMessages) {
            setErrMsgTooltip('No messages sent to any Class Sections, Please check the numbers and Try again');
          } else {
            setErrMsgTooltip('Error sending messages to some Class Sections, Please recheck and Try again');
          }
          setSelectedRows([]);
        } else {
          setErrMsgTooltip('Error while sending messages, Please Try again');
        }
        setShowSuccessMap((prevMap) => ({ ...prevMap, [row.groupId]: true }));

        setTimeout(() => {
          setShowSuccessMap((prevMap) => ({ ...prevMap, [row.groupId]: false }));
          // setIsButtonDisabledMap((prevMap) => ({ ...prevMap, [row.classSectionName]: true }));
        }, 5000);
      } catch (error) {
        console.error('Error sending messages:', error);
      }
    },
    [dispatch, voiceId, templateId, adminId, setSendResults, individualSendLoadingMap, disabledCheckBoxes]
  );
  const getRowKey = useCallback((row: PublicGroupWiseDataWithStatus) => row.groupId, []);

  const getStatusButton = (row: PublicGroupWiseDataWithStatus, rowIndex: number) => (
    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
      {row.sendStatus === 'Failed' ? (
        <Tooltip title={errMsgTooltip} placement="left">
          <Stack>
            <Lottie animationData={errorIcon} loop={false} style={{ width: '30px' }} />
          </Stack>
        </Tooltip>
      ) : row.sendStatus === 'Success' ? (
        showSuccessMap[row.groupId] === true ? (
          <Stack>
            <Lottie animationData={successIcon} loop={false} style={{ width: '30px' }} />
          </Stack>
        ) : (
          <LoadingButton
            endIcon={<SuccessIcon color="success" />}
            variant="outlined"
            size="small"
            color="primary"
            disabled
            // disabled={isButtonDisabledMap[row.groupId]}
          >
            Sent
          </LoadingButton>
        )
      ) : !selectedRows.includes(row) ? (
        <LoadingButton
          endIcon={<SendIcon />}
          onClick={() => {
            if (messageId && messageId !== 0) {
              handleSendMessageIndivitual(row, rowIndex);
            } else {
              handleSendVoiceMessageIndivitual(row, rowIndex);
            }
          }}
          variant="outlined"
          size="small"
          color="primary"
          disabled={isSubmitting && isLoading}
          loading={individualSendLoadingMap[row.groupId]}
        >
          Send
        </LoadingButton>
      ) : (
        <LoadingButton
          endIcon={<SendIcon />}
          variant="outlined"
          size="small"
          color="primary"
          disabled
          loading={individualSendLoadingMap[row.groupId]}
        >
          Send
        </LoadingButton>
      )}
    </Box>
  );

  // const getStatusIcon = (row: PublicGroupWiseDataWithStatus) => {
  //   let iconComponent;

  //   if (row.status === 'Failed') {
  //     iconComponent = <FcCancel style={{ height: 20, width: 20 }} />;
  //   } else if (row.status === 'Success') {
  //     iconComponent = <FcOk style={{ height: 20, width: 20 }} />;
  //   } else if (row.status === 'Null') {
  //     iconComponent = 'empty';
  //   } else {
  //     iconComponent = null;
  //   }

  //   return <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>{iconComponent}</Box>;
  // };

  const PublicGroupWiseListColumns: DataTableColumn<PublicGroupWiseDataWithStatus>[] = [
    {
      name: 'slNo',
      headerLabel: 'Sl.No',
      dataKey: 'groupId',
      sortable: true,
    },
    {
      name: 'groupName',
      dataKey: 'groupName',
      headerLabel: 'Group Name',
    },
    {
      name: 'year',
      dataKey: 'accademicTime',
      headerLabel: 'Accademic Year',
    },
    {
      name: 'status',
      renderCell: getStatusButton,
    },
  ];
  return (
    <GroupsRoot>
      <Box>
        <Divider />
        <form noValidate onReset={handleReset} onSubmit={handleSubmit}>
          <Grid pb={2} pt={1} container spacing={3} alignItems="end">
            <Grid item lg={3} md={4} sm={4} xs={12}>
              <FormControl fullWidth>
                <Typography variant="h6" fontSize={12} color="GrayText">
                  Academic Year
                </Typography>
                <Select
                  labelId="academicYearFilter"
                  id="academicYearFilterSelect"
                  value={academicYearFilter.toString()}
                  onChange={handleYearChange}
                  placeholder="Select Year"
                >
                  {YearData.map((opt) => (
                    <MenuItem key={opt.accademicId} value={opt.accademicId}>
                      {opt.accademicTime}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item lg={3} md={4} sm={4} xs={12}>
              <FormControl fullWidth>
                <Typography variant="subtitle2" fontSize={12} color="GrayText">
                  Group Name
                </Typography>
                <Select
                  labelId="publicGroupFilter"
                  id="publicGroupFilterSelect"
                  value={publicGroupFilter.toString()}
                  onChange={handleGroupChange}
                  placeholder="Select Group"
                >
                  <MenuItem value="-1" sx={{ display: 'none' }}>
                    Select All
                  </MenuItem>
                  {PublicGroupsData.map((opt) => (
                    <MenuItem key={opt.groupId} value={opt.groupId}>
                      {opt.groupName}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item lg={3} md={4} sm={4} xs={12}>
              <FormControl fullWidth>
                <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                  <Button variant="contained" color="secondary" type="reset" fullWidth>
                    Reset
                  </Button>
                  <Button variant="contained" color="primary" type="submit" fullWidth>
                    Search
                  </Button>
                </Stack>
              </FormControl>
            </Grid>
          </Grid>
        </form>
        <Paper
          sx={{
            border:
              selectedData?.length === 0 && sendResults?.length === 0 ? `0px` : `1px solid ${theme.palette.grey[300]}`,
            width: '100%',
            overflow: 'auto',
          }}
        >
          <Box
            sx={{
              height: 'calc(100vh - 23.7rem)',
              width: { xs: selectedData?.length !== 0 ? '43.75rem' : '100%', md: '100%' },
            }}
          >
            <DataTable
              ShowCheckBox
              isSubmitting={isSubmitting}
              disabledCheckBox={disabledCheckBoxes}
              // tableRowSelect={disabledCheckBoxes}
              setSelectedRows={setSelectedRows}
              selectedRows={selectedRows}
              columns={PublicGroupWiseListColumns}
              data={selectedData}
              getRowKey={getRowKey}
              fetchStatus={PublicGroupWiseStatus}
            />
          </Box>
        </Paper>
        <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 3 }}>
          {selectedData.length !== 0 && (
            <Stack spacing={2} direction="row">
              <Button disabled={isSubmitting} onClick={handleCancel} variant="contained" color="secondary">
                Cancel
              </Button>
              <LoadingButton
                loadingPosition="start"
                endIcon={<SendIcon />}
                fullWidth
                onClick={messageId && messageId !== 0 ? handleSendMessage : handleSendVoiceMessage}
                loading={isLoading && isSubmitting}
                variant="contained"
                color="primary"
                disabled={isSubmitting}
              >
                {isSubmitting && isLoading ? 'Sending...' : 'Send'}
              </LoadingButton>
            </Stack>
          )}
        </Box>
      </Box>
    </GroupsRoot>
  );
}

export default PublicGroupWise;
