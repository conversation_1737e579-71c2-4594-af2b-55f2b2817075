/* eslint-disable jsx-a11y/alt-text */
import React, { useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  useTheme,
  IconButton,
  Collapse,
  Tooltip,
  FormControl,
  Chip,
} from '@mui/material';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import styled from 'styled-components';
import { CLASS_SELECT, YEAR_SELECT } from '@/config/Selection';
import { MdAdd } from 'react-icons/md';
import SearchIcon from '@mui/icons-material/Search';
import AutorenewOutlinedIcon from '@mui/icons-material/AutorenewOutlined';
import useSettings from '@/hooks/useSettings';
import { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import Videoplayer from '@/components/Dashboard/Videoplayer';
import MenuEditDelete from '@/components/shared/Selections/MenuEditDelete';
import Popup from '@/components/shared/Popup/Popup';
import { SuccessIcon } from '@/theme/overrides/CustomIcons';

export const data = [
  {
    class: 'VII-B',
    academicYear: '2023-2024',
    subject: 'English',
    chapter: '03',
    status: 'Published',
    title: 'The Framers',
    description:
      'Farming Industry Lorem ipsum dolor sit, amet consectetur adipisicing elit. Esse iure dicta inventore officia, optio eum voluptates ipsum ipsa laborum odit recusandae sunt soluta deserunt in non fuga debitis accusamus illo',
    date: '10-12-2023',
    created: 'Passdaily',
    video: 'https://youtu.be/gHESEeS1kGk',
    thumbnail:
      'https://images.unsplash.com/photo-1635192592106-77a5aacbe1a3?q=80&w=1969&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    class: 'VII-B',
    academicYear: '2023-2024',
    subject: 'English',
    chapter: '03',
    status: 'Published',
    title: 'The Framers',
    description: 'Farming Industry...',
    date: '10-12-2023',
    created: 'Passdaily',
    video: 'https://youtu.be/havBDwFgW24',
    thumbnail:
      'https://images.unsplash.com/photo-1592843997881-cab3860b1067?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    class: 'VII-B',
    academicYear: '2023-2024',
    subject: 'English',
    chapter: '03',
    status: 'Published',
    title: 'The Framers',
    description: 'Farming Industry...',
    date: '10-12-2023',
    created: 'Passdaily',
    video: 'https://youtu.be/DlHC7I9dBNU',
    thumbnail:
      'https://images.unsplash.com/photo-1482517967863-00e15c9b44be?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    class: 'VII-B',
    academicYear: '2023-2024',
    subject: 'English',
    chapter: '03',
    status: 'Published',
    title: 'The Framers',
    description: 'Farming Industry...',
    date: '10-12-2023',
    created: 'Passdaily',
    video: 'https://youtu.be/gHESEeS1kGk',
    thumbnail:
      'https://images.unsplash.com/photo-1635192592106-77a5aacbe1a3?q=80&w=1969&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    class: 'VII-B',
    academicYear: '2023-2024',
    subject: 'English',
    chapter: '03',
    status: 'Published',
    title: 'The Framers',
    description: 'Farming Industry...',
    date: '10-12-2023',
    created: 'Passdaily',
    video: 'https://youtu.be/havBDwFgW24',
    thumbnail:
      'https://images.unsplash.com/photo-1592843997881-cab3860b1067?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    class: 'VII-B',
    academicYear: '2023-2024',
    subject: 'English',
    chapter: '03',
    status: 'Published',
    title: 'The Framers',
    description: 'Farming Industry...',
    date: '10-12-2023',
    created: 'Passdaily',
    video: 'https://youtu.be/DlHC7I9dBNU',
    thumbnail:
      'https://images.unsplash.com/photo-1482517967863-00e15c9b44be?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    class: 'VII-B',
    academicYear: '2023-2024',
    subject: 'English',
    chapter: '03',
    status: 'Published',
    title: 'The Framers',
    description: 'Farming Industry...',
    date: '10-12-2023',
    created: 'Passdaily',
    video: 'https://youtu.be/gHESEeS1kGk',
    thumbnail:
      'https://images.unsplash.com/photo-1635192592106-77a5aacbe1a3?q=80&w=1969&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    class: 'VII-B',
    academicYear: '2023-2024',
    subject: 'English',
    chapter: '03',
    status: 'Published',
    title: 'The Framers',
    description: 'Farming Industry...',
    date: '10-12-2023',
    created: 'Passdaily',
    video: 'https://youtu.be/havBDwFgW24',
    thumbnail:
      'https://images.unsplash.com/photo-1592843997881-cab3860b1067?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    class: 'VII-B',
    academicYear: '2023-2024',
    subject: 'English',
    chapter: '03',
    status: 'Published',
    title: 'The Framers',
    description: 'Farming Industry...',
    date: '10-12-2023',
    created: 'Passdaily',
    video: 'https://youtu.be/DlHC7I9dBNU',
    thumbnail:
      'https://images.unsplash.com/photo-1482517967863-00e15c9b44be?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
];

const OnlineVideoClassRoot = styled.div``;

function OnlineVideoClass() {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const [showFilter, setShowFilter] = useState(false);
  const [createPopup, setCreatePopup] = useState(false);
  const [mapOGFilePopup, setMapOGFilePopup] = useState(false);
  const [updatedData, setData] = useState(data);
  const [view, setView] = useState(false);
  const [Delete, setDelete] = useState(false);

  const handleClickView = () => setView(true);
  const handleClickCloseView = () => setView(false);
  const handleClickDelete = () => setDelete(true);
  const handleClickCloseDelete = () => setDelete(false);

  const handleStatusChange = (index) => {
    const copyData = [...updatedData];
    copyData[index].status = copyData[index].status === 'Published' ? 'Unpublished' : 'Published';
    setData(copyData);
  };

  const OnlineVideoClassColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'studentName',
        dataKey: 'studentName',
        headerLabel: 'Student Name',
      },

      {
        name: 'class',
        dataKey: 'class',
        headerLabel: 'Class',
      },
      {
        name: 'admission',
        dataKey: 'admission',
        headerLabel: 'Admission',
      },
      {
        name: 'academicYear',
        dataKey: 'academicYear',
        headerLabel: 'Academic',
      },
    ],
    []
  );

  return (
    <OnlineVideoClassRoot>
      <Grid container spacing={2}>
        {data.map((student, cardIndex) => (
          <Grid item xl={12} lg={12} md={12} sm={12} xs={12}>
            <Card sx={{ p: 1, m: 1, bgcolor: isLight ? theme.palette.common.white : theme.palette.grey[900] }}>
              <Grid container spacing={2}>
                <Grid item lg={2} md={6} xs={6}>
                  <Card sx={{ height: '120px', boxShadow: '0' }}>
                    <Videoplayer url={student.video} thumbnail={student.thumbnail} />
                  </Card>
                </Grid>
                <Grid item lg={10} md={6} xs={12}>
                  <Grid container mt={0.5} spacing={1.5}>
                    <Grid item lg={1.5} xs={6}>
                      <Typography variant="subtitle1" color="GrayText" fontSize={13} mt={1}>
                        Class
                      </Typography>
                      <Typography variant="subtitle1" color="GrayText" fontSize={13} mt={1}>
                        Subject
                      </Typography>
                      <Typography variant="subtitle1" color="GrayText" fontSize={13} mt={1}>
                        Chapter
                      </Typography>
                    </Grid>
                    <Grid item lg={2} xs={6} borderRight={2} borderColor={theme.palette.grey[300]}>
                      <Typography variant="h6" fontSize={13} mt={1.5}>
                        : {student.class}
                      </Typography>
                      <Typography variant="h6" fontSize={13} mt={1}>
                        : {student.subject}
                      </Typography>
                      <Typography variant="h6" fontSize={13} mt={1}>
                        : {student.chapter}
                      </Typography>
                    </Grid>
                    <Grid item lg={1.5} xs={6}>
                      <Typography variant="subtitle1" color="GrayText" fontSize={13} mt={1}>
                        Title
                      </Typography>
                      <Typography variant="subtitle1" color="GrayText" fontSize={13} mt={1}>
                        Description
                      </Typography>
                    </Grid>
                    <Grid item lg={3.5} xs={6} borderRight={2} borderColor={theme.palette.grey[300]}>
                      <Typography variant="h6" fontSize={13} mt={1}>
                        : {student.title}
                      </Typography>
                      <Typography
                        variant="h6"
                        fontSize={13}
                        mt={1}
                        sx={{
                          display: '-webkit-box',
                          WebkitBoxOrient: 'vertical',
                          WebkitLineClamp: 3, // Set the number of lines to display
                          textOverflow: 'ellipsis',
                        }}
                      >
                        : {student.description}
                      </Typography>
                    </Grid>
                    <Grid item lg={1.5} xs={6}>
                      <Typography variant="subtitle1" color="GrayText" fontSize={13} mt={1}>
                        Status
                      </Typography>
                      <Typography variant="subtitle1" color="GrayText" fontSize={13} mt={1}>
                        Date
                      </Typography>
                      <Typography variant="subtitle1" color="GrayText" fontSize={13} mt={1}>
                        Created
                      </Typography>
                    </Grid>
                    <Grid item lg={2} xs={6}>
                      <Typography variant="h6" fontSize={13} mt={1}>
                        :
                        <Chip
                          icon={<SuccessIcon />}
                          sx={{
                            ml: 0.5,
                            borderRadius: 1,
                          }}
                          color="success"
                          size="small"
                          label="Publish"
                          variant="filled"
                        />
                      </Typography>
                      <Typography variant="h6" fontSize={13} mt={1}>
                        :{' '}
                        <span>
                          <CalendarTodayIcon fontSize="small" color="secondary" sx={{ marginRight: '5px' }} />
                        </span>
                        <span>{student.date}</span>
                      </Typography>
                      <Typography variant="h6" fontSize={13} mt={1}>
                        : {student.created}
                      </Typography>
                    </Grid>
                  </Grid>
                </Grid>

                {/* <Grid item lg={3.1} xs={6} mt={4.5} borderRight={1} borderColor={theme.palette.grey[300]}>
                          {OnlineVideoClassColumns.map((item) => (
                            <Grid container mt={2}>
                              <Grid item lg={6.1} xs={6}>
                                <Typography key={item.name} variant="subtitle1" color="GrayText" fontSize={13} mr={2}>
                                  {item.headerLabel}
                                </Typography>
                              </Grid>
                              <Grid item lg={5} xs={6} mb={0} mt="auto">
                                <Typography
                                  variant="h6"
                                  fontSize={13}
                                  color={item.dataKey === 'meetingLink' ? theme.palette.info.main : ''}
                                >
                                  : {(student as { [key: string]: string })[item.dataKey ?? '']}
                                </Typography>
                              </Grid>
                            </Grid>
                          ))}
                        </Grid>
                        <Grid item lg={3.1} xs={6} mt={4.5} borderRight={1} borderColor={theme.palette.grey[300]}>
                          {OnlineVideoClassColumns.map((item) => (
                            <Grid container mt={2}>
                              <Grid item lg={6.1} xs={6}>
                                <Typography key={item.name} variant="subtitle1" color="GrayText" fontSize={13} mr={2}>
                                  {item.headerLabel}
                                </Typography>
                              </Grid>
                              <Grid item lg={5} xs={6} mb={0} mt="auto">
                                <Typography
                                  variant="h6"
                                  fontSize={13}
                                  color={item.dataKey === 'meetingLink' ? theme.palette.info.main : ''}
                                >
                                  : {(student as { [key: string]: string })[item.dataKey ?? '']}
                                </Typography>
                              </Grid>
                            </Grid>
                          ))}
                        </Grid>
                        <Grid item lg={3.1} xs={6} mt={4.5}>
                          {OnlineVideoClassColumns.map((item) => (
                            <Grid container mt={2}>
                              <Grid item lg={6.1} xs={6}>
                                <Typography key={item.name} variant="subtitle1" color="GrayText" fontSize={13} mr={2}>
                                  {item.headerLabel}
                                </Typography>
                              </Grid>
                              <Grid item lg={5} xs={6} mb={0} mt="auto">
                                <Typography
                                  variant="h6"
                                  fontSize={13}
                                  color={item.dataKey === 'meetingLink' ? theme.palette.info.main : ''}
                                >
                                  : {(student as { [key: string]: string })[item.dataKey ?? '']}
                                </Typography>
                              </Grid>
                            </Grid>
                          ))}
                        </Grid> */}
              </Grid>
            </Card>
          </Grid>
        ))}
      </Grid>
    </OnlineVideoClassRoot>
  );
}

export default OnlineVideoClass;
