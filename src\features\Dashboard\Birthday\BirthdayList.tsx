import { Box, List, ListItem, Avatar, <PERSON>ItemAvatar, Stack, Typography } from '@mui/material';
// import List from '@/theme/overrides/List';
import birthdayIcon from '@/assets/birthday/birthday.png';
import birthday from '@/assets/birthday/birthday-cake.png';
import styled from 'styled-components';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useAppSelector } from '@/hooks/useAppSelector';
import { getdashboardBdayListData, getdashboardBdayListStatus } from '@/config/storeSelectors';
import { fetchDashboardBday } from '@/store/Dashboard/dashboard.thunks';
import { useEffect } from 'react';
import useAuth from '@/hooks/useAuth';

// const dummyArray = [
//   {
//     id: '01',
//     label: '<PERSON>',
//     class: 'VIII-B',
//     rollNo: '1',
//     image:
//       'https://images.unsplash.com/photo-1633332755192-727a05c4013d?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=580&q=80',
//   },
//   {
//     id: '02',
//     label: 'Sara Mary Lynch',
//     class: 'VIII-B',
//     rollNo: '1',
//     image: '',
//   },
// ];

const BirthdayListRoot = styled.div`
  height: 100%;
  .text {
    font-size: 10px;
    font-weight: 600;
  }
  .list {
    overflow-y: scroll;
  }
  .list::-webkit-scrollbar {
    display: none;
  }
  .List-item {
    border-bottom: 1px solid
      ${(props) => (props.theme.themeMode === 'light' ? props.theme.palette.grey[200] : props.theme.palette.grey[800])};
    padding: 0.59rem 0;
  }
  .List-item:last-child {
    border-bottom: none;
  }
  .bday_image {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.info.lighter : props.theme.palette.grey[700]};
  }
`;

function BirthdayList() {
  const { user } = useAuth();
  const adminId: number | undefined = user?.accountId;
  const dispatch = useAppDispatch();
  const dashboardBdayListStatus = useAppSelector(getdashboardBdayListStatus);
  const dashboardBdayListData = useAppSelector(getdashboardBdayListData);

  useEffect(() => {
    if (dashboardBdayListStatus === 'idle') {
      dispatch(fetchDashboardBday(adminId));
    }
  }, [dispatch, dashboardBdayListStatus, adminId]);
  return (
    <BirthdayListRoot>
      {dashboardBdayListData.length !== 0 ? (
        <List dense className="list" sx={{ minHeight: '30vh' }}>
          {dashboardBdayListData?.map((list) => {
            return (
              <ListItem key={list.studentId} className="List-item" disablePadding>
                {/* <ListItemButton> */}
                <ListItemAvatar>
                  <Avatar alt="" src={list.studentImage} />
                </ListItemAvatar>
                <Stack direction="column" flexGrow={1}>
                  <Typography
                    sx={{
                      maxWidth: '130px',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      fontFamily: 'Poppins medium',
                      fontSize: '12px',
                      fontWeight: '550',
                    }}
                  >
                    {list.studentName}
                  </Typography>
                  <Stack direction="row" spacing={2} sx={{ display: 'flex', alignItems: 'center' }}>
                    {/* <CalendarTodayIcon color="secondary" sx={{ fontSize: '9px', marginRight: '8px' }} /> */}
                    <Typography
                      sx={{ fontFamily: 'Poppins medium', fontWeight: '500', fontSize: '9px', color: 'gray' }}
                    >
                      Class: <b>{list.className}</b>
                    </Typography>
                    <Typography
                      sx={{ fontFamily: 'Poppins medium', fontWeight: '500', fontSize: '9px', color: 'gray' }}
                    >
                      Age: <b>{list.studentAge}</b>
                    </Typography>
                  </Stack>
                </Stack>
                {/* <Stack display="flex" justifyContent="center"> */}
                {/* <ListItemIcon> */}
                <Stack className="bday_image" sx={{ p: 1, borderRadius: 50 }}>
                  <img width={20} src={birthdayIcon} alt="" />
                </Stack>
                {/* </ListItemIcon> */}
                {/* </Stack> */}
                {/* </ListItemButton> */}
              </ListItem>
            );
          })}
        </List>
      ) : (
        <Box
          sx={{
            minHeight: 'calc(100vh - 600px)',
            // minHeight: '30vh',
            // overflow: 'auto',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            flexGrow: 1,
            '&::-webkit-scrollbar': {
              width: '0px', // Adjust the width of the scrollbar as per your requirement
            },
          }}
        >
          <Stack alignItems="center" justifyContent="center" height="100%" spacing={1}>
            <img src={birthday} alt="" height={100} style={{ opacity: 0.6 }} />
            <Typography variant="subtitle2" color="GrayText">
              No Birthdays Today
            </Typography>
          </Stack>
        </Box>
      )}
    </BirthdayListRoot>
  );
}

export default BirthdayList;
