import { GridColDef } from '@mui/x-data-grid';
import absent from '@/assets/attendance/absent.svg';
import { EnquiryDataProps } from '@/types/Timetable';
import { PromoteStudent } from '@/types/ManageStudent';
import present from '@/assets/attendance/present.svg';
import { TeacherDataTypes } from '@/types/ManageStaff';
import { AbsenteesProps, HeadCell } from '../types/AttendanceMarking';

export const columns: GridColDef[] = [
  { field: 'id', headerName: 'Roll No', width: 200 },
  { field: 'name', headerName: 'Student name', width: 400 },
  { field: 'status', headerName: 'Status', width: 170 },
];

export const dmrows = [
  { id: 1, name: '<PERSON>onna Grand', status: 'Absent' },
  { id: 2, name: '<PERSON>', status: 'Absent' },
  { id: 3, name: 'Lannister', status: 'Absent' },
  { id: 4, name: '<PERSON>', status: 'Absent' },
  { id: 5, name: '<PERSON><PERSON><PERSON>', status: 'Absent' },
  { id: 6, name: '<PERSON><PERSON>', status: 'Absent' },
  { id: 7, name: '<PERSON>', status: 'Absent' },
  { id: 8, name: 'Frances', status: 'Absent' },
  { id: 9, name: 'Jack Felix', status: 'Absent' },
  { id: 10, name: 'Fionna Grand', status: 'Absent' },
  { id: 11, name: 'Alex', status: 'Absent' },
];

export const sessionDetailedHead: readonly HeadCell[] = [
  {
    id: 'rollNo',
    numeric: true,
    disablePadding: true,
    label: 'Roll No',
  },
  {
    id: 'name',
    numeric: false,
    disablePadding: false,
    label: 'Student Name',
  },
  {
    id: 'status',
    numeric: false,
    disablePadding: false,
    label: 'Status',
  },
];

export type StaffHead = {
  id: string;
  numeric: boolean;
  disablePadding: boolean;
  label: string;
};

export const staffDataHead: readonly StaffHead[] = [
  {
    id: 'id',
    numeric: false,
    disablePadding: true,
    label: 'ID',
  },
  {
    id: 'name',
    numeric: false,
    disablePadding: false,
    label: 'Staff Name',
  },
  {
    id: 'phone',
    numeric: false,
    disablePadding: false,
    label: 'Phone Number',
  },
  {
    id: 'status',
    numeric: false,
    disablePadding: false,
    label: 'Status',
  },
];

export const sessionDetaildData = [
  {
    id: 1,
    rollNo: '01',
    name: 'Fionna Grand',
    status: 'Absent',
    image:
      'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80',
  },
  {
    id: 2,
    rollNo: '02',
    name: 'Alex',
    status: 'Absent',
    image:
      'https://images.unsplash.com/photo-1633332755192-727a05c4013d?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=580&q=80',
  },
  {
    id: 3,
    rollNo: '03',
    name: 'Micheal',
    status: 'Absent',
    image:
      'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=580&q=80',
  },
  { id: 4, rollNo: '04', name: 'John', status: 'Absent' },
  { id: 5, rollNo: '05', name: 'Zack', status: 'Absent' },
  { id: 6, rollNo: '06', name: 'Johny', status: 'Absent' },
  { id: 8, rollNo: '07', name: 'Johnson', status: 'Absent' },
  { id: 9, rollNo: '08', name: 'Martha', status: 'Absent' },
  { id: 10, rollNo: '09', name: 'Kante', status: 'Absent' },
];
export const qmrows = [
  { id: 1, class: 'XII-A' },
  { id: 2, class: 'VI-B' },
  { id: 3, class: 'VII-B' },
  { id: 4, class: 'II-C' },
  { id: 5, class: 'VII-A' },
  { id: 6, class: 'VII-A' },
  { id: 7, class: 'VII-A' },
  { id: 8, class: 'VII-A' },
];

type Lnrows = {
  id: number;
  name: string;
  class: string;
  subject: string;
  description: string;
};
export const lnrows: Lnrows[] = [
  {
    id: 1,
    name: 'Fionna Grand',
    class: 'XII-A',
    subject: 'Tour Leave',
    description: 'Going banglore',
  },
  {
    id: 2,
    name: 'Fionna Grand',
    class: 'VI-B',
    subject: 'Health Issue',
    description: 'Due to Fever',
  },
  {
    id: 3,
    name: 'Fionna Grand',
    class: 'VII-B',
    subject: 'Hospitalised',
    description: 'Covid',
  },
  {
    id: 4,
    name: 'Fionna Grand',
    class: 'II-C',
    subject: 'Tour Leave',
    description: 'Going banglore',
  },
  {
    id: 5,
    name: 'Fionna Grand',
    class: 'VII-A',
    subject: 'Sick Leave',
    description: 'Cold',
  },
  {
    id: 6,
    name: 'Fionna Grand',
    class: 'VII-A',
    subject: 'Sick Leave',
    description: 'Cold',
  },
  {
    id: 7,
    name: 'Fionna Grand',
    class: 'VII-A',
    subject: 'Sick Leave',
    description: 'Cold',
  },
  {
    id: 8,
    name: 'Fionna Grand',
    class: 'VII-A',
    subject: 'Sick Leave',
    description: 'Cold',
  },
  {
    id: 9,
    name: 'Fionna Grand',
    class: 'VII-A',
    subject: 'Sick Leave',
    description: 'Cold',
  },
  {
    id: 10,
    name: 'Fionna Grand',
    class: 'VII-A',
    subject: 'Sick Leave',
    description: 'Cold',
  },
  {
    id: 11,
    name: 'Fionna Grand',
    class: 'VII-A',
    subject: 'Sick Leave',
    description: 'Cold',
  },
];

export const strows1 = [
  { days: 'Mon', date: 1, status: 'Absent', color: '#FEF2F2', icon: absent },
  { days: 'Tue', date: 2, status: 'Present', color: '#F0FDF4', icon: present },
  { days: 'Wed', date: 3, status: 'Not Taken', color: '#F5F3FF' },
  { days: 'Thu', date: 4, status: 'Present', color: '#F0FDF4', icon: present },
  { days: 'Fri', date: 5, status: 'Absent', color: '#FEF2F2', icon: absent },
  { days: 'Sat', date: 6, status: 'Present', color: '#F0FDF4', icon: present },
  { days: 'Sun', date: 7, status: 'Not Taken', color: '#F5F3FF' },
  { days: 'Mon', date: 8, status: 'Absent', color: '#FEF2F2', icon: absent },
  { days: 'Tue', date: 9, status: 'Not Taken', color: '#F5F3FF' },
  { days: 'Wed', date: 10, status: 'Present', color: '#F0FDF4', icon: present },
  { days: 'Thu', date: 11, status: 'Not Taken', color: '#F5F3FF' },
  { days: 'Fri', date: 12, status: 'Present', color: '#F0FDF4', icon: present },
  { days: 'Sat', date: 13, status: 'Not Taken', color: '#F5F3FF' },
  { days: 'Sun', date: 14, status: 'Present', color: '#F0FDF4', icon: present },
  { days: 'Mon', date: 15, status: 'Present', color: '#F0FDF4', icon: present },
  { days: 'Tue', date: 16, status: 'Absent', color: '#FEF2F2', icon: absent },
  { days: 'Wed', date: 17, status: 'Absent', color: '#FEF2F2', icon: absent },
  { days: 'Thu', date: 18, status: 'Not Taken', color: '#F5F3FF' },
  { days: 'Fri', date: 19, status: 'Present', color: '#F0FDF4', icon: present },
  { days: 'Sat', date: 20, status: 'Present', color: '#F0FDF4', icon: present },
  { days: 'Sun', date: 21, status: 'Not Taken', color: '#F5F3FF' },
  { days: 'Mon', date: 22, status: 'Present', color: '#F0FDF4', icon: present },
  { days: 'Tue', date: 23, status: 'Not Taken', color: '#F5F3FF' },
  { days: 'Wed', date: 24, status: 'Absent', color: '#FEF2F2', icon: absent },
  { days: 'Thu', date: 25, status: 'Not Taken', color: '#F5F3FF' },
  { days: 'Fri', date: 26, status: 'Present', color: '#F0FDF4', icon: present },
  { days: 'Sat', date: 27, status: 'Present', color: '#F0FDF4', icon: present },
  { days: 'Sun', date: 28, status: 'Not Taken', color: '#F5F3FF' },
  { days: 'Mon', date: 29, status: 'Not Taken', color: '#F5F3FF' },
  { days: 'Tue', date: 30, status: 'Present', color: '#F0FDF4', icon: present },
  { days: 'Wed', date: 31, status: 'Present', color: '#F0FDF4', icon: present },
];

export const strows2 = [
  {
    rollNo: '01',
    name: 'Fionna Grand',
    work: '31',
    attendance: '10',
    absent: '0',
    percentage: '100',
    image:
      'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80',
  },
  {
    rollNo: '02',
    name: 'Alex',
    work: '31',
    attendance: '10',
    absent: '5',
    percentage: '50',
    image:
      'https://images.unsplash.com/photo-1568602471122-7832951cc4c5?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80',
  },
  {
    rollNo: '03',
    name: 'Micheal',
    work: '31',
    attendance: '10',
    absent: '1',
    percentage: '90',
    image:
      'https://images.unsplash.com/photo-1633332755192-727a05c4013d?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=580&q=80',
  },
  {
    rollNo: '04',
    name: 'John',
    work: '31',
    attendance: '10',
    absent: '6',
    percentage: '40',
    image:
      'https://images.unsplash.com/photo-1570295999919-56ceb5ecca61?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=580&q=80',
  },
  {
    rollNo: '05',
    name: 'Jack Felix',
    work: '31',
    attendance: '10',
    absent: '4',
    percentage: '60',
    image:
      'https://images.unsplash.com/photo-1565464027194-7957a2295fb7?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=580&q=80',
  },
  {
    rollNo: '06',
    name: 'Johny',
    work: '31',
    attendance: '10',
    absent: '8',
    percentage: '20',
    image:
      'https://images.unsplash.com/photo-1639149888905-fb39731f2e6c?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=464&q=80',
  },
  {
    rollNo: '07',
    name: 'Micheal',
    work: '31',
    attendance: '10',
    absent: '8',
    percentage: '20',
    image:
      'https://images.unsplash.com/photo-1633332755192-727a05c4013d?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=580&q=80',
  },
  {
    rollNo: '08',
    name: 'Jack Felix',
    work: '31',
    attendance: '10',
    absent: '8',
    percentage: '20',
    image:
      'https://images.unsplash.com/photo-1568602471122-7832951cc4c5?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80',
  },
  {
    rollNo: '09',
    name: 'Alex',
    work: '31',
    attendance: '10',
    absent: '8',
    percentage: '20',
    image:
      'https://images.unsplash.com/photo-1565464027194-7957a2295fb7?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=580&q=80',
  },
];

export const FeePendingList = [
  { slNo: 1, name: 'Fionna Grand', amount: 10000, status: 'Not paid' },
  { slNo: 2, name: 'Fionna Grand', amount: 10000, status: 'Not paid' },
  { slNo: 3, name: 'Fionna Grand', amount: 10000, status: 'Not paid' },
  { slNo: 4, name: 'Fionna Grand', amount: 10000, status: 'Not paid' },
  { slNo: 5, name: 'Fionna Grand', amount: 10000, status: 'Not paid' },
  { slNo: 6, name: 'Fionna Grand', amount: 10000, status: 'Not paid' },
  { slNo: 7, name: 'Fionna Grand', amount: 10000, status: 'Not paid' },
  { slNo: 8, name: 'Fionna Grand', amount: 10000, status: 'Not paid' },
  { slNo: 9, name: 'Fionna Grand', amount: 10000, status: 'Not paid' },
];

export const Students = [
  {
    id: 1,
    rollNo: '01',
    name: 'Fionna Grand',
    status: 'Absent',
    image:
      'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80',
  },
  {
    id: 2,
    rollNo: '02',
    name: 'Alex',
    status: 'Absent',
    image:
      'https://images.unsplash.com/photo-1633332755192-727a05c4013d?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=580&q=80',
  },
  {
    id: 3,
    rollNo: '03',
    name: 'Micheal',
    status: 'Absent',
    image:
      'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=580&q=80',
  },
];

export const StudentFeeList = [
  { slNo: 1, title: 'Exam Fee', description: 'January Exam Fee', updateDate: '12/01/2023', status: 'Publish' },
  { slNo: 2, title: 'Exam Fee', description: 'January Exam Fee', updateDate: '12/01/2023', status: 'Publish' },
  { slNo: 3, title: 'Exam Fee', description: 'January Exam Fee', updateDate: '12/01/2023', status: 'Publish' },
  { slNo: 4, title: 'Exam Fee', description: 'January Exam Fee', updateDate: '12/01/2023', status: 'Publish' },
  { slNo: 5, title: 'Exam Fee', description: 'January Exam Fee', updateDate: '12/01/2023', status: 'Publish' },
  { slNo: 6, title: 'Exam Fee', description: 'January Exam Fee', updateDate: '12/01/2023', status: 'Publish' },
  { slNo: 7, title: 'Exam Fee', description: 'January Exam Fee', updateDate: '12/01/2023', status: 'Publish' },
  { slNo: 8, title: 'Exam Fee', description: 'January Exam Fee', updateDate: '12/01/2023', status: 'Publish' },
  { slNo: 9, title: 'Exam Fee', description: 'January Exam Fee', updateDate: '12/01/2023', status: 'Publish' },
  { slNo: 10, title: 'Exam Fee', description: 'January Exam Fee', updateDate: '12/01/2023', status: 'Publish' },
  { slNo: 11, title: 'Exam Fee', description: 'January Exam Fee', updateDate: '12/01/2023', status: 'Publish' },
  { slNo: 12, title: 'Exam Fee', description: 'January Exam Fee', updateDate: '12/01/2023', status: 'Publish' },
  { slNo: 13, title: 'Exam Fee', description: 'January Exam Fee', updateDate: '12/01/2023', status: 'Publish' },
  { slNo: 14, title: 'Exam Fee', description: 'January Exam Fee', updateDate: '12/01/2023', status: 'Publish' },
  { slNo: 15, title: 'Exam Fee', description: 'January Exam Fee', updateDate: '12/01/2023', status: 'Publish' },
];
export const StudentMappedList = [
  { slNo: 1, title: 'Tution Fee', Class: 'VII A', year: '2023-2024', amount: 15600 },
  { slNo: 2, title: 'Admission Fee', Class: 'VII A', year: '2023-2024', amount: 2400 },
  { slNo: 3, title: 'Exam Fee', Class: 'VII A', year: '2023-2024', amount: 900 },
  { slNo: 4, title: 'Lab Fee', Class: 'VII A', year: '2023-2024', amount: 1200 },
  { slNo: 5, title: 'Bus Fee', Class: 'VII A', year: '2023-2024', amount: 3100 },
  { slNo: 6, title: 'Misc. Fee', Class: 'VII A', year: '2023-2024', amount: 400 },
  { slNo: 7, title: 'ID Card', Class: 'VII A', year: '2023-2024', amount: 150 },
  { slNo: 8, title: 'E-Learning', Class: 'VII A', year: '2023-2024', amount: 1700 },
  { slNo: 9, title: 'Books Fee', Class: 'VII A', year: '2023-2024', amount: 2300 },
];

export const absenteesList: AbsenteesProps[] = [
  { rollno: 1, name: 'Fionna Grand', mark: 'Absent' },
  { rollno: 2, name: 'Alex', mark: 'Absent' },
  { rollno: 3, name: 'Peter', mark: 'Absent' },
];

export const teacherData: TeacherDataTypes[] = [
  {
    id: 1,
    name: 'John Smith',
    staffCode: 1001,
    experience: '2 Year',
    mobileNumber: '1234567890',
    emailId: '<EMAIL>',
    role: 'Class Teacher',
  },
  {
    id: 2,
    name: 'Jane Doe',
    staffCode: 1001,
    experience: '2 Year',
    mobileNumber: '9876543210',
    emailId: '<EMAIL>',
    role: 'Subject Teacher',
  },
  {
    id: 3,
    name: 'Michael Johnson',
    staffCode: 1001,
    experience: '2 Year',
    mobileNumber: '4567891230',
    emailId: '<EMAIL>',
    role: 'Subject Teacher',
  },
  {
    id: 4,
    name: 'Emily Wilson',
    staffCode: 1001,
    experience: '2 Year',
    mobileNumber: '7890123456',
    emailId: '<EMAIL>',
    role: 'Subject Teacher',
  },
  {
    id: 5,
    name: 'David Thompson',
    staffCode: 1001,
    experience: '2 Year',
    mobileNumber: '5678901234',
    emailId: '<EMAIL>',
    role: 'Class Teacher',
  },
];

export const conveyorsData = [
  {
    id: 1,
    idNo: '001',
    driverName: 'John Doe',
    driverNumber: '1234567890',
    lastUpdated: '2023-05-18',
  },
  {
    id: 2,
    idNo: '002',
    driverName: 'Jane Smith',
    driverNumber: '9876543210',
    lastUpdated: '2023-05-17',
  },
  {
    id: 3,
    idNo: '003',
    driverName: 'Michael Johnson',
    driverNumber: '4567891230',
    lastUpdated: '2023-05-16',
  },
  {
    id: 4,
    idNo: '004',
    driverName: 'Emily Wilson',
    driverNumber: '7890123456',
    lastUpdated: '2023-05-15',
  },
  {
    id: 5,
    idNo: '005',
    driverName: 'David Thompson',
    driverNumber: '5678901234',
    lastUpdated: '2023-05-14',
  },
];

export const enquiryData: EnquiryDataProps[] = [
  {
    SlNo: 1,
    SubmittedDate: '23-05-23',
    Subject: 'History',
    StudentName: 'John',
    RollNo: 'A003',
    Class: '11th',
    RepliedDate: null,
    Reply: null,
    Query: 'Is there any Homework Today?',
    Status: 'Submitted',
  },
  {
    SlNo: 2,
    SubmittedDate: '23-05-20',
    Subject: 'Enquiry',
    StudentName: 'John Doe',
    RollNo: 'A001',
    Class: '10th',
    RepliedDate: '23-05-22',
    Reply: 'Sports day is scheduled for June 15th.',
    Query: 'When is sports day?',
    Status: 'Replied',
  },
  {
    SlNo: 3,
    SubmittedDate: '23-05-20',
    Subject: 'Mathematics',
    StudentName: 'John Doe',
    RollNo: 'A001',
    Class: '10th',
    RepliedDate: '23-05-22',
    Reply: 'Your query has been resolved.',
    Query: 'I need help with a math problem.',
    Status: 'Replied',
  },
  {
    SlNo: 4,
    SubmittedDate: '23-05-19',
    Subject: 'Enquiry',
    StudentName: 'Emily Davis',
    RollNo: 'A004',
    Class: '8th',
    RepliedDate: '23-05-23',
    Reply: 'The science fair is scheduled for July 10th. Please prepare your project accordingly.',
    Query: 'When is the science fair?',
    Status: 'Replied',
  },
  {
    SlNo: 5,
    SubmittedDate: '23-05-19',
    Subject: 'English',
    StudentName: 'Emily Davis',
    RollNo: 'A004',
    Class: '8th',
    RepliedDate: '23-05-23',
    Reply: 'Please refer to page 50 of your textbook for the answer.',
    Query: 'Where can I find the definition of a specific word?',
    Status: 'Replied',
  },
  {
    SlNo: 6,
    SubmittedDate: '23-05-18',
    Subject: 'Science',
    StudentName: 'Jane Smith',
    RollNo: 'A002',
    Class: '9th',
    RepliedDate: '23-05-21',
    Reply: 'Please provide more information about your question.',
    Query: 'Can you explain the concept of photosynthesis?',
    Status: 'Replied',
  },
  {
    SlNo: 7,
    SubmittedDate: '23-05-18',
    Subject: 'Enquiry',
    StudentName: 'Jane Smith',
    RollNo: 'A002',
    Class: '9th',
    RepliedDate: '23-05-21',
    Reply: 'The exam dates will be announced shortly. Please stay tuned for updates.',
    Query: 'Exam date?',
    Status: 'Replied',
  },
  {
    SlNo: 8,
    SubmittedDate: '23-05-17',
    Subject: 'Geography',
    StudentName: 'Michael Wilson',
    RollNo: 'A005',
    Class: '10th',
    RepliedDate: '23-05-20',
    Reply: 'Here is a detailed explanation of the concept you asked for.',
    Query: 'What are the major rivers in South America?',
    Status: 'Replied',
  },
  {
    SlNo: 9,
    SubmittedDate: '23-05-17',
    Subject: 'Enquiry',
    StudentName: 'Robert Johnson',
    RollNo: 'A003',
    Class: '11th',
    RepliedDate: null,
    Reply: null,
    Query: 'Is there a school trip planned for this semester?',
    Status: 'Submitted',
  },
  {
    SlNo: 10,
    SubmittedDate: '23-05-17',
    Subject: 'Enquiry',
    StudentName: 'Michael Wilson',
    RollNo: 'A005',
    Class: '10th',
    RepliedDate: '23-05-20',
    Reply: 'The next parent-teacher meeting is on June 5th. We look forward to seeing you there.',
    Query: 'When is the next parent-teacher meeting?',
    Status: 'Replied',
  },
];

export const ClassSortStudents = [
  { slNo: 1, class: 'X-A', section: 'TEST CLASS' },
  { slNo: 2, class: 'XI-A', section: 'TEST CLASS' },
  { slNo: 3, class: 'VII-B', section: 'TEST CLASS' },
  { slNo: 4, class: 'VI-C', section: 'TEST CLASS' },
  { slNo: 5, class: 'VII-A', section: 'TEST CLASS' },
  { slNo: 6, class: 'VI-D', section: 'TEST CLASS' },
  { slNo: 7, class: 'V-B', section: 'TEST CLASS' },
  { slNo: 8, class: 'I-B', section: 'TEST CLASS' },
  { slNo: 9, class: 'I-B', section: 'TEST CLASS' },
  { slNo: 10, class: 'I-B', section: 'TEST CLASS' },
  { slNo: 11, class: 'II-B', section: 'TEST CLASS' },
  { slNo: 12, class: 'II-A', section: 'TEST CLASS' },
];
export type TimeTableDataProps = {
  id: number;
  subject: string;
  academicYear: string;
  class: string;
  day: string;
  period: number;
};

export const TIME_TABLE_DATA: TimeTableDataProps[] = [
  { id: 1, subject: 'Hindi', academicYear: '2022-2023', class: 'VIII-B', day: 'Tue', period: 1 },
  { id: 2, subject: 'English', academicYear: '2022-2023', class: 'VIII-B', day: 'Tue', period: 2 },
  { id: 3, subject: 'Maths', academicYear: '2022-2023', class: 'VIII-B', day: 'Tue', period: 3 },
  { id: 4, subject: 'Science', academicYear: '2022-2023', class: 'VIII-B', day: 'Tue', period: 4 },
  { id: 5, subject: 'Social', academicYear: '2022-2023', class: 'VIII-B', day: 'Tue', period: 5 },
  { id: 6, subject: 'Arabic', academicYear: '2022-2023', class: 'VIII-B', day: 'Tue', period: 6 },
  { id: 7, subject: 'Music', academicYear: '2022-2023', class: 'VIII-B', day: 'Tue', period: 7 },
  { id: 8, subject: 'P.E.T', academicYear: '2022-2023', class: 'VIII-B', day: 'Tue', period: 8 },
  { id: 9, subject: 'Hindi', academicYear: '2023-2024', class: 'VIII-A', day: 'Mon', period: 1 },
  { id: 10, subject: 'English', academicYear: '2023-2024', class: 'VIII-A', day: 'Mon', period: 2 },
  { id: 11, subject: 'Maths', academicYear: '2023-2024', class: 'VIII-A', day: 'Mon', period: 3 },
  { id: 12, subject: 'Science', academicYear: '2023-2024', class: 'VIII-A', day: 'Mon', period: 4 },
  { id: 13, subject: 'Social', academicYear: '2023-2024', class: 'VIII-A', day: 'Mon', period: 5 },
  { id: 14, subject: 'Arabic', academicYear: '2023-2024', class: 'VIII-A', day: 'Mon', period: 6 },
  { id: 15, subject: 'Music', academicYear: '2023-2024', class: 'VIII-A', day: 'Mon', period: 7 },
  { id: 16, subject: 'P.E.T', academicYear: '2023-2024', class: 'VIII-A', day: 'Mon', period: 8 },
];

export type StaffDataProps = {
  id: string;
  name: string;
  phone: string;
  year: string;
  status: string;
};

export const STAFF_DATA: StaffDataProps[] = [
  {
    id: '01',
    name: 'Krishika',
    phone: '+91-9564213204',
    year: '2023-2024',
    status: 'Absent',
  },
  {
    id: '02',
    name: 'Nikhil',
    phone: '+91-8454401346',
    year: '2023-2024',
    status: 'Absent',
  },
  {
    id: '03',
    name: 'Bhaskar',
    phone: '+91-9031645478',
    year: '2023-2024',
    status: 'Absent',
  },
  {
    id: '04',
    name: 'Thanmayi',
    phone: '+91-6214587981',
    year: '2023-2024',
    status: 'Absent',
  },
  {
    id: '05',
    name: 'Aboli',
    phone: '+91-7800645803',
    year: '2023-2024',
    status: 'Absent',
  },
  {
    id: '06',
    name: 'Chandresh',
    phone: '+91-8687046318',
    year: '2023-2024',
    status: 'Absent',
  },
  {
    id: '07',
    name: 'Karthav',
    phone: '+91-8846276940',
    year: '2023-2024',
    status: 'Absent',
  },
  {
    id: '08',
    name: 'Aayana',
    phone: '+91-6452069763',
    year: '2023-2024',
    status: 'Absent',
  },
  {
    id: '09',
    name: 'Balan',
    phone: '+91-7865163240',
    year: '2023-2024',
    status: 'Absent',
  },
  {
    id: '10',
    name: 'Divya',
    phone: '+91-9046790103',
    year: '2023-2024',
    status: 'Absent',
  },
];

export const PromoteStudentArray: PromoteStudent[] = [
  {
    id: 1,
    name: 'Fionna Grand',
    class: 'VIII-A',
    slNo: 1,
    admission_no: 'A123',
    year: '2022-2023',
  },
  {
    id: 2,
    name: 'John Doe',
    class: 'IX-B',
    slNo: 2,
    admission_no: 'B456',
    year: '2022-2023',
  },
  {
    id: 3,
    name: 'Emma Smith',
    class: 'VII-C',
    slNo: 3,
    admission_no: 'C789',
    year: '2022-2023',
  },
  {
    id: 4,
    name: 'Michael Johnson',
    class: 'X-A',
    slNo: 4,
    admission_no: 'D012',
    year: '2022-2023',
  },
];

export type ManageExamType = {
  id: number;
  slNo: number;
  name: string;
  description: string;
  staffStatus: string;
  parentStatus: string;
};

export const ManageExamData: ManageExamType[] = [
  {
    id: 1,
    slNo: 1,
    name: 'Monthly January',
    description: 'January Exam ',
    parentStatus: 'Published',
    staffStatus: 'Published',
  },
  {
    id: 2,
    slNo: 2,
    name: 'Monthly January',
    description: 'January Exam',
    parentStatus: 'Published',
    staffStatus: 'Published',
  },
  {
    id: 3,
    slNo: 3,
    name: 'Monthly January',
    description: 'January Exam',
    parentStatus: 'Published',
    staffStatus: 'Published',
  },
  {
    id: 4,
    slNo: 4,
    name: 'Monthly January',
    description: 'January Exam',
    parentStatus: 'Unpublished',
    staffStatus: 'Published',
  },
  {
    id: 5,
    slNo: 5,
    name: 'Monthly January',
    description: 'January Exam',
    parentStatus: 'Unpublished',
    staffStatus: 'Published',
  },
  {
    id: 6,
    slNo: 6,
    name: 'Monthly January',
    description: 'January Exam',
    parentStatus: 'Published',
    staffStatus: 'Published',
  },
  {
    id: 7,
    slNo: 7,
    name: 'Monthly January',
    description: 'January Exam',
    parentStatus: 'Unpublished',
    staffStatus: 'Published',
  },
  {
    id: 8,
    slNo: 8,
    name: 'Monthly January',
    description: 'January Exam',
    parentStatus: 'Unpublished',
    staffStatus: 'Published',
  },
  {
    id: 9,
    slNo: 9,
    name: 'Monthly January',
    description: 'January Exam',
    parentStatus: 'Unpublished',
    staffStatus: 'Published',
  },
  {
    id: 10,
    slNo: 10,
    name: 'Monthly January',
    description: 'January Exam',
    parentStatus: 'Published',
    staffStatus: 'Published',
  },
];
