/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Box,
  Checkbox,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  Collapse,
  FormControl,
} from '@mui/material';
import styled from 'styled-components';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import SearchIcon from '@mui/icons-material/Search';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { MdAdd } from 'react-icons/md';
import { CLASS_SELECT } from '@/config/Selection';
import DeleteIcon from '@mui/icons-material/Delete';
import ModeEditIcon from '@mui/icons-material/ModeEdit';

const LandingImagesRoot = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      height: calc(100% - 40px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

export const data = [
  {
    slNo: 1,
    title: 'Annual Day',
    description:
      'We Design and develop Websites,Applications,Mobile Solutions. Lorem Ipsum is a sample text generated to use for web designing.',
    imageFile:
      'https://images.unsplash.com/photo-1544456203-0af5a69f5789?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1974&q=80',
  },
  {
    slNo: 2,
    title: 'Independance Day',
    description:
      'We are a full Services Digital Agency Lorem Ipsum is a sample text generated to use for web designing purposes.',
    imageFile:
      'https://images.unsplash.com/photo-1588075592405-d3d4f0846961?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2072&q=80',
  },
  {
    slNo: 3,
    title: 'Annual Day',
    description:
      'We Design and develop Websites,Applications,Mobile Solutions Lorem Ipsum is a sample text generated to use for web designing.',
    imageFile:
      'https://images.unsplash.com/photo-1503676685182-2531a01b5b5c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1776&q=80 ',
  },
  {
    slNo: 4,
    title: 'Independance Day',
    description:
      'We are a full Services Digital Agency Lorem Ipsum is a sample text generated to use for web designing purposes.',
    imageFile:
      'https://images.unsplash.com/photo-1534643960519-11ad79bc19df?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
  },
  {
    slNo: 5,
    title: 'Annual Day',
    description:
      'We Design and develop Websites,Applications,Mobile Solutions Lorem Ipsum is a sample text generated to use for web designing.',
    imageFile:
      'https://images.unsplash.com/photo-1636202339022-7d67f7447e3a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2071&q=80 ',
  },
  {
    slNo: 6,
    title: 'Independance Day',
    description:
      'We are a full Services Digital Agency Lorem Ipsum is a sample text generated to use for web designing purposes.',
    imageFile:
      'https://images.unsplash.com/photo-1588072432836-e10032774350?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2072&q=80',
  },
];

function LandingImages() {
  const [drawerOpen, setDrawerOpen] = React.useState<boolean>(false);
  const [showFilter, setShowFilter] = useState(false);

  const handleOpen = () => {
    setDrawerOpen(true);
  };

  const toggleDrawerClose = useCallback(() => setDrawerOpen(false), []);

  const getRowKey = useCallback((row: any) => row.examId, []);

  const LandingImagesColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'select',
        renderHeader: () => {
          return <Checkbox />;
        },
        renderCell: () => {
          return <Checkbox />;
        },
      },
      {
        name: 'SlNo',
        dataKey: 'slNo',
        headerLabel: 'Sl.No',
        sortable: true,
      },
      {
        name: 'title',
        dataKey: 'title',
        headerLabel: 'Title',
      },
      {
        name: 'description',
        dataKey: 'description',
        headerLabel: 'Description',
        width: '40%',
        renderCell: (row) => {
          return <Typography>{row.description}</Typography>;
        },
      },
      {
        name: 'image',
        dataKey: 'image',
        headerLabel: 'Image File',
        renderCell: (row) => {
          return <img src={row.imageFile} alt="" width={200} style={{ borderRadius: '10px' }} />;
        },
      },
      {
        name: 'actions',
        headerLabel: 'Actions',
        renderCell: () => {
          return (
            <Stack direction="row">
              <IconButton size="small" onClick={handleOpen} sx={{ padding: 0.5 }}>
                <ModeEditIcon />
              </IconButton>
              <IconButton size="small" color="error" onClick={handleOpen} sx={{ padding: 0.5 }}>
                <DeleteIcon />
              </IconButton>
            </Stack>
          );
        },
      },
    ],
    []
  );

  return (
    <Page title="List">
      <LandingImagesRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17}>
              Send AppUpdate Message
            </Typography>
            <Box pb={1} sx={{ flexShrink: 0 }}>
              <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                <SearchIcon />
              </IconButton>
              <Button onClick={handleOpen} sx={{ borderRadius: '20px' }} variant="outlined" size="small">
                <MdAdd size="20px" /> Create
              </Button>
            </Box>
          </Stack>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={3} alignItems="end">
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Autocomplete
                        options={CLASS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Autocomplete
                        options={CLASS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Student Name
                      </Typography>
                      <TextField placeholder="Enter Name" />
                    </FormControl>
                  </Grid>

                  <Grid item lg="auto" xs={12} sm={6}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Paper className="card-table-container" sx={{ marginTop: '12px' }}>
              <DataTable columns={LandingImagesColumns} data={data} getRowKey={getRowKey} fetchStatus="success" />
            </Paper>
          </div>
          {/* <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 2 }}>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="secondary">
                Cancel
              </Button>
              <Button onClick={handleOpen} variant="contained" color="primary">
                Save
              </Button>
            </Stack>
          </Box> */}
        </Card>
      </LandingImagesRoot>

      <TemporaryDrawer onClose={toggleDrawerClose} Title="Upload Details" state={drawerOpen} DrawerContent="" />
    </Page>
  );
}

export default LandingImages;
