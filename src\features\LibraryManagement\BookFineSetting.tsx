/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import Page from '@/components/shared/Page';
import { Divider, Grid, Stack, Typography, Card, useTheme } from '@mui/material';
import styled from 'styled-components';
import useSettings from '@/hooks/useSettings';

const BookFineSettingRoot = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 576px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      height: calc(100% - 40px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

function BookFineSetting() {
  const theme = useTheme();
  const isLight = useSettings().themeMode === 'light';

  return (
    <Page title="List">
      <BookFineSettingRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Typography whiteSpace="nowrap" variant="h6" fontSize={17}>
            Fine Setting
          </Typography>
          <Divider />
          <Stack alignItems="center" mt={5}>
            <Card
              sx={{
                minWidth: '50%',
                boxShadow: 0,
                bgcolor: isLight ? theme.palette.primary.lighter : theme.palette.grey[900],
              }}
            >
              <Typography textAlign="center" variant="h6" py={2}>
                Fine Calculation Matrix
              </Typography>
              <Divider />
              <Grid container columnSpacing={15} mt={5} px={10}>
                <Grid item lg={6}>
                  <Stack direction="row" justifyContent="space-between" gap={2} mb={5}>
                    <Typography whiteSpace="nowrap" variant="subtitle2">
                      Return Date
                    </Typography>
                    <Typography whiteSpace="nowrap" variant="subtitle2">
                      :
                    </Typography>
                    <Typography whiteSpace="nowrap" variant="subtitle2">
                      10
                    </Typography>
                  </Stack>
                  <Stack direction="row" justifyContent="space-between" gap={2} mb={5}>
                    <Typography whiteSpace="nowrap" variant="subtitle2">
                      Return Date
                    </Typography>
                    <Typography whiteSpace="nowrap" variant="subtitle2">
                      :
                    </Typography>
                    <Typography whiteSpace="nowrap" variant="subtitle2">
                      20
                    </Typography>
                  </Stack>
                  <Stack direction="row" justifyContent="space-between" gap={2} mb={5}>
                    <Typography whiteSpace="nowrap" variant="subtitle2">
                      Return Date
                    </Typography>
                    <Typography whiteSpace="nowrap" variant="subtitle2">
                      :
                    </Typography>
                    <Typography whiteSpace="nowrap" variant="subtitle2">
                      30
                    </Typography>
                  </Stack>
                  <Stack direction="row" justifyContent="space-between" gap={2} mb={5}>
                    <Typography whiteSpace="nowrap" variant="subtitle2">
                      Return Date
                    </Typography>
                    <Typography whiteSpace="nowrap" variant="subtitle2">
                      :
                    </Typography>
                    <Typography whiteSpace="nowrap" variant="subtitle2">
                      40
                    </Typography>
                  </Stack>
                  <Stack direction="row" justifyContent="space-between" gap={2} mb={5}>
                    <Typography whiteSpace="nowrap" variant="subtitle2">
                      Return Date
                    </Typography>
                    <Typography whiteSpace="nowrap" variant="subtitle2">
                      :
                    </Typography>
                    <Typography whiteSpace="nowrap" variant="subtitle2">
                      50
                    </Typography>
                  </Stack>
                </Grid>
                <Grid item lg={6}>
                  <Stack direction="row" justifyContent="space-between" gap={2} mb={5}>
                    <Typography whiteSpace="nowrap" variant="subtitle2">
                      Fine/Day
                    </Typography>
                    <Typography whiteSpace="nowrap" variant="subtitle2">
                      :
                    </Typography>
                    <Typography whiteSpace="nowrap" variant="subtitle2">
                      1
                    </Typography>
                  </Stack>
                  <Stack direction="row" justifyContent="space-between" gap={2} mb={5}>
                    <Typography whiteSpace="nowrap" variant="subtitle2">
                      Fine/Day
                    </Typography>
                    <Typography whiteSpace="nowrap" variant="subtitle2">
                      :
                    </Typography>
                    <Typography whiteSpace="nowrap" variant="subtitle2">
                      2
                    </Typography>
                  </Stack>
                  <Stack direction="row" justifyContent="space-between" gap={2} mb={5}>
                    <Typography whiteSpace="nowrap" variant="subtitle2">
                      Fine/Day
                    </Typography>
                    <Typography whiteSpace="nowrap" variant="subtitle2">
                      :
                    </Typography>
                    <Typography whiteSpace="nowrap" variant="subtitle2">
                      3
                    </Typography>
                  </Stack>
                  <Stack direction="row" justifyContent="space-between" gap={2} mb={5}>
                    <Typography whiteSpace="nowrap" variant="subtitle2">
                      Fine/Day
                    </Typography>
                    <Typography whiteSpace="nowrap" variant="subtitle2">
                      :
                    </Typography>
                    <Typography whiteSpace="nowrap" variant="subtitle2">
                      4
                    </Typography>
                  </Stack>
                  <Stack direction="row" justifyContent="space-between" gap={2} mb={5}>
                    <Typography whiteSpace="nowrap" variant="subtitle2">
                      Fine/Day
                    </Typography>
                    <Typography whiteSpace="nowrap" variant="subtitle2">
                      :
                    </Typography>
                    <Typography whiteSpace="nowrap" variant="subtitle2">
                      5
                    </Typography>
                  </Stack>
                </Grid>
              </Grid>
            </Card>
          </Stack>
        </Card>
      </BookFineSettingRoot>
    </Page>
  );
}

export default BookFineSetting;
