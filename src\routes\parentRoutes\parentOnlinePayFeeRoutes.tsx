import Loadable from '@/components/shared/Loadable';
import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';

const OnlinePayment = Loadable(lazy(() => import('@/Parent-Side/features/Fees/OnlinePayment')));
const OnlinePayment2 = Loadable(lazy(() => import('@/Parent-Side/features/Fees/OnlinePayment2')));
const TransactionResponse = Loadable(lazy(() => import('@/Parent-Side/features/Fees/TransactionResponse')));
const OnlineReciept = Loadable(lazy(() => import('@/Parent-Side/features/Fees/OnlineReciept')));
const JoinMeeting = Loadable(lazy(() => import('@/features/ZoomMeeting/Meeting')));

export const parentOnlinePayFeeRoutes: RouteObject[] = [
  {
    path: '/parent/pay-fee',
    element: <OnlinePayment />,
  },
  {
    path: '/parent/pay-fee-new',
    element: <OnlinePayment2 />,
  },
  {
    path: '/parent/reciept',
    element: <OnlineReciept />,
  },
  {
    path: '/parent/downloadreceipt',
    element: <OnlineReciept />,
  },
  {
    path: '/parent/pay-response',
    element: <TransactionResponse />,
  },
  {
    path: '/parent/pay-response',
    element: <TransactionResponse />,
  },
  {
    path: '/join-meeting',
    element: <JoinMeeting />,
  },
];
