/* eslint-disable jsx-a11y/alt-text */
import React, { useMemo, useState } from 'react';
import { Grid, Stack, Button, Typography, Box, AvatarGroup, Avatar, Chip } from '@mui/material';
import styled, { useTheme } from 'styled-components';
import useSettings from '@/hooks/useSettings';
import { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import ReportViewTabs from './ReportViewTabs';

const ReportViewRoot = styled.div`
  width: 100%;
  padding: 1.5rem;
`;

export const data = {
  studentName: 'Alex',
  image:
    'https://images.unsplash.com/photo-1633332755192-727a05c4013d?q=80&w=1780&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  assignment: 'English Poets',
  class: 'VII-D',
  description: 'Williams Hills',
  uploadFiles: '',
  outOffMark: '50',
};

function ReportView({ onClose }: any) {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';

  const ScheduleListColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'studentName',
        headerLabel: 'Student',
        renderCell: (row) => {
          return (
            <Stack direction="row" alignItems="center" gap={1}>
              <Avatar src={row.image} />
              <Typography variant="subtitle2">{row.studentName}</Typography>
            </Stack>
          );
        },
      },
      {
        name: 'assignment',
        dataKey: 'assignment',
        headerLabel: 'Assignment',
      },
      {
        name: 'class',
        dataKey: 'class',
        headerLabel: 'Class',
      },
      {
        name: 'description',
        dataKey: 'description',
        headerLabel: 'Description',
      },
      {
        name: 'uploadFiles',
        headerLabel: 'Upload Files',
        renderCell: (row) => {
          return (
            <Stack direction="row" alignItems="center">
              <AvatarGroup>
                <Avatar
                  alt="Remy Sharp"
                  src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?q=80&w=1780&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                />
                <Avatar alt="Travis Howard" src="/static/images/avatar/2.jpg" />
                <Avatar alt="Cindy Baker" src="/static/images/avatar/3.jpg" />
                <Avatar alt="Agnes Walker" src="/static/images/avatar/4.jpg" />
                <Avatar alt="Trevor Henderson" src="/static/images/avatar/5.jpg" />
              </AvatarGroup>
            </Stack>
          );
        },
      },

      {
        name: 'outOffMark',
        headerLabel: 'Out Off Mark',
        renderCell: (row) => {
          return (
            <Chip
              size="small"
              label={row.outOffMark}
              variant="outlined"
              // color="success"
              sx={{
                border: '0px',
                borderRadius: '5px',
                bgcolor: theme.palette.info.lighter,
                color: theme.palette.info.main,
              }}
            />
          );
        },
      },
    ],
    []
  );
  return (
    <ReportViewRoot>
      <form noValidate>
        {ScheduleListColumns.map((item) => (
          <Grid container direction="row" alignItems="center">
            <Grid item lg={3} xs={6} mb={1}>
              <Typography key={item.name} variant="subtitle1" fontSize={13} mr={2}>
                {item.headerLabel}
              </Typography>
            </Grid>
            <Grid item lg={9} xs={6} mb={1}>
              <Typography
                variant="h6"
                fontSize={13}
                color={item.dataKey === 'meetingLink' ? theme.palette.info.main : ''}
              >
                {item.dataKey
                  ? `:${' '}${(data as { [key: string]: any })[item.dataKey ?? '']}`
                  : item && item.renderCell && item.renderCell(data)}
              </Typography>
            </Grid>
          </Grid>
        ))}
        <ReportViewTabs />

        <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 3 }}>
          <Stack direction="row">
            <Button variant="contained" color="primary">
              Add Mark & Comment
            </Button>
          </Stack>
        </Box>
      </form>
    </ReportViewRoot>
  );
}

export default ReportView;
