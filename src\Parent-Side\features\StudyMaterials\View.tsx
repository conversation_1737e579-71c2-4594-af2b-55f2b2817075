/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import {
  Autocomplete,
  Grid,
  Stack,
  TextField,
  Button,
  Typography,
  Box,
  FormControl,
  Select,
  MenuItem,
  Card,
  IconButton,
} from '@mui/material';
import styled from 'styled-components';
import { CLASS_SELECT, STATUS_OPTIONS, YEAR_SELECT } from '@/config/Selection';
import SelectDateTimePicker from '@/components/shared/Selections/TimePicker';
import { SuccessIcon } from '@/theme/overrides/CustomIcons';
import XLIcon from '@/Parent-Side/assets/XLIcon.svg';
import TextIcon from '@/Parent-Side/assets/TextIcon.svg';
import WordIcon from '@/Parent-Side/assets/WordIcon.svg';
import PowerpointIcon from '@/Parent-Side/assets/PowerpointIcon.svg';
import DownloadForOfflineIcon from '@mui/icons-material/DownloadForOffline';

const ViewRoot = styled.div`
  width: 100%;
  padding: 1.5rem;
`;
const dummy = [
  { id: 2, fileIcon: TextIcon },
  { id: 3, fileIcon: WordIcon },
  { id: 4, fileIcon: PowerpointIcon },
];

function View() {
  return (
    <ViewRoot>
      <Box>
        <Typography variant="subtitle2" mb={1} fontSize={18}>
          Physics
        </Typography>
        <Typography variant="subtitle1" mb={2}>
          Lorem ipsum dolor sit amet, consectetur adipisicing elit. Quam quis, voluptatem minus placeat facere fugit
          quibusdam dolorem.
        </Typography>
        <Box>
          <Card sx={{ p: 2, mb: 2 }}>
            <Box display="flex" alignItems="center" justifyContent="space-between">
              <Stack direction="row" alignItems="center" gap={3}>
                <div>
                  <img src={XLIcon} alt="" />
                </div>
                <Typography variant="body2">File Name , Sept 05 : 2021 , 10 : 00 am</Typography>
              </Stack>
              <SuccessIcon color="success" />
            </Box>
          </Card>
          {dummy.map((item) => (
            <Card sx={{ p: 2, mb: 2 }} key={item.id}>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Stack direction="row" alignItems="center" gap={3}>
                  <div>
                    <img src={item.fileIcon} alt="" />
                  </div>
                  <Typography variant="body2">File Name , Sept 05 : 2021 , 10 : 00 am</Typography>
                </Stack>
                <IconButton size="small">
                  <DownloadForOfflineIcon color="success" />
                </IconButton>
              </Box>
            </Card>
          ))}
        </Box>
      </Box>
    </ViewRoot>
  );
}

export default View;
