/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  Collapse,
  FormControl,
  Checkbox,
  FormGroup,
  FormControlLabel,
  useTheme,
  Chip,
  Avatar,
} from '@mui/material';
import styled from 'styled-components';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import { MdAdd, MdDelete } from 'react-icons/md';
import SearchIcon from '@mui/icons-material/Search';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import DeleteIcon from '@mui/icons-material/Delete';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import DateSelect from '@/components/shared/Selections/DateSelect';
import { STATUS_SELECT } from '@/config/Selection';
import typography from '@/theme/typography';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import MenuEditDeleteView from '@/components/shared/Selections/MenuEditDeleteView';
import Popup from '@/components/shared/Popup/Popup';
import CreateTcForm from './CreateTcForm';

const TcListRoot = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 240px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 50px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        /* border: 1px solid #e8e8e9; */
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

export const data = [
  {
    slNo: 1,
    studentName: 'Alex Peter',
    guardian: 'Jack',
    admissionNo: '12345',
  },
  {
    slNo: 2,
    studentName: 'Alex Peter',
    guardian: 'Jack',
    admissionNo: '12345',
  },
  {
    slNo: 3,
    studentName: 'Alex Peter',
    guardian: 'Jack',
    admissionNo: '12345',
  },
  {
    slNo: 4,
    studentName: 'Alex Peter',
    guardian: 'Jack',
    admissionNo: '12345',
  },
  {
    slNo: 5,
    studentName: 'Alex Peter',
    guardian: 'Jack',
    admissionNo: '12345',
  },
];

function TcList() {
  const [popupOpen, setPopupOpen] = React.useState<boolean>(false);
  const [showFilter, setShowFilter] = useState(false);
  const [showSend, setShowSend] = useState(true);
  const theme = useTheme();

  const handleOpen = () => {
    setPopupOpen(true);
  };

  const togglePopupClose = useCallback(() => setPopupOpen(false), []);

  const getRowKey = useCallback((row: any) => row.examId, []);
  const TcListColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'studentName',
        headerLabel: 'Student Name',
        renderCell: (row) => {
          return (
            <Stack direction="row" spacing={1} alignItems="center">
              <Avatar alt="Remy Sharp" src="/static/images/avatar/1.jpg" />
              <Typography variant="subtitle2">{row.studentName}</Typography>
            </Stack>
          );
        },
      },
      {
        name: 'guardian',
        headerLabel: 'Guardian',
        dataKey: 'guardian',
      },
      {
        name: 'admissionNo',
        headerLabel: 'Admission No',
        renderCell: (row) => {
          return (
            <Box display="flex" justifyContent="space-between" flex={1}>
              <Chip size="small" sx={{ borderRadius: 1 }} color="info" label={row.admissionNo} />
            </Box>
          );
        },
      },
      // {
      //   name: 'status',
      //   headerLabel: 'Status',
      //   renderCell: () => {
      //     return <Typography>Published</Typography>;
      //   },
      // },
    ],
    []
  );

  return (
    <Page title="List">
      <TcListRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: 1 }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={19}>
              Tc List
            </Typography>
            <Box sx={{ flexShrink: 0 }}>
              <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                <SearchIcon />
              </IconButton>
              <Button sx={{ borderRadius: '20px' }} variant="contained" size="small" onClick={handleOpen}>
                <MdAdd size="20px" />
                Create
              </Button>
            </Box>
          </Stack>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pt={1} container spacing={3} alignItems="end" pb={showFilter ? 9 : 2}>
                  <Grid item lg="auto" md={6} xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', lg: 250 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        SL.No
                      </Typography>
                      <TextField placeholder="Enter number" />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" md={6} xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', lg: 250 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Admission No
                      </Typography>
                      <TextField placeholder="Enter number" />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" md={6} xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', lg: 250 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Student Name
                      </Typography>
                      <TextField placeholder="Enter name" />
                    </FormControl>
                  </Grid>

                  <Grid item lg="auto" xs={12}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            {/* <Paper className="card-table-container" sx={{ marginTop: 1 }}>
              <DataTable columns={TcListColumns} data={data} getRowKey={getRowKey} fetchStatus="success" />
            </Paper> */}
            <Paper className="card-table-container" sx={{ marginTop: 1 }}>
              <Grid container spacing={2}>
                {data.map((student, rowIndex) => (
                  <Grid item xl={3} lg={6} md={12} sm={6} xs={12} key={rowIndex}>
                    <Card
                      className="student_card"
                      sx={{ p: 2, boxShadow: 0, border: `1px solid ${theme.palette.grey[300]}` }}
                    >
                      <Box flexGrow={1}>
                        {TcListColumns.map((item, index) => (
                          <Stack direction="row" key={index}>
                            <Grid container>
                              <Grid item lg={5} xs={6}>
                                <Typography key={item.name} variant="subtitle1" color="GrayText" fontSize={13} mr={2}>
                                  {item.headerLabel}
                                </Typography>
                              </Grid>
                              <Grid item lg={5} xs={6} mb={0.5} mt="auto">
                                {item.dataKey ? (
                                  <Typography variant="h6" fontSize={13}>
                                    {/* {`: ${(student as { [key: string]: any })[item.dataKey ?? '']}`} */}
                                    {item.dataKey
                                      ? `:${' '}${(student as { [key: string]: any })[item.dataKey ?? '']}`
                                      : item && item.renderCell && item.renderCell(student, rowIndex)}
                                  </Typography>
                                ) : (
                                  item && item.renderCell && item.renderCell(student, rowIndex)
                                )}
                              </Grid>
                            </Grid>
                          </Stack>
                        ))}
                      </Box>
                      <Stack position="absolute" top={10} right={10}>
                        <MenuEditDeleteView
                          Edit={() => {
                            return 0;
                          }}
                          // Delete={handleClickDelete}
                        />
                      </Stack>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Paper>
          </div>
          {/* <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 2 }}>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="secondary">
                Cancel
              </Button>
              <Button onClick={handleOpen} variant="contained" color="primary">
                Save
              </Button>
            </Stack>
          </Box> */}
        </Card>
      </TcListRoot>
      <Popup
        size="md"
        state={popupOpen}
        title="TC Information Create"
        onClose={togglePopupClose}
        popupContent={<CreateTcForm />}
      />
      {/* <TemporaryDrawer onClose={toggleDrawerClose} Title="Upload Details" state={drawerOpen} DrawerContent="" /> */}
    </Page>
  );
}

export default TcList;
