import * as React from 'react';
import dayjs from 'dayjs';
import { DemoContainer } from '@mui/x-date-pickers/internals/demo';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { MobileDatePicker } from '@mui/x-date-pickers/MobileDatePicker';

type DateSelectProps = {
  value?: dayjs.Dayjs | string | null | undefined;
  onChange?: (newValue: dayjs.Dayjs | null) => void;
};

export default function DateSelect({ value, onChange }: DateSelectProps) {
  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <DemoContainer
        sx={{ p: 0 }}
        components={['DatePicker', 'MobileDatePicker', 'DesktopDatePicker', 'StaticDatePicker']}
      >
        <MobileDatePicker format="DD/MM/YYYY" defaultValue={dayjs(new Date())} value={value} onChange={onChange} />
      </DemoContainer>
    </LocalizationProvider>
  );
}
