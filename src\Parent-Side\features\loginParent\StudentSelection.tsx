import SlideImage2 from '@/assets/loginslider/SlideImage2.svg';
import Slider, { Settings } from 'react-slick';
import styled from 'styled-components';
import CarouselArrow from '@/components/Dashboard/CarouselArrow';
import Typography from '@mui/material/Typography';
import { Box, CardActionArea, CardContent, CardMedia, Grid, Stack, useTheme } from '@mui/material';
import { Card } from '@mui/material';
import { Button, Radio } from '@mui/material'; // Import Radio component
import useSettings from '@/hooks/useSettings';
import React, { useState } from 'react';
import { SuccessIcon } from '@/theme/overrides/CustomIcons';
import LoadingButton from '@mui/lab/LoadingButton';

const settings: Settings = {
  dots: false,
  infinite: true,
  speed: 500,
  slidesToShow: 1,
  slidesToScroll: 1,
  nextArrow: <CarouselArrow direction="next" />,
  prevArrow: <CarouselArrow direction="previous" />,
  autoplay: true,
  autoplaySpeed: 10000,
};

const StudentSelectionRoot = styled.div`
  /* padding: 4rem; */
  /* position: relative; */
`;
const studentsArray = [
  {
    id: 1,
    studentName: 'Alex',
    class: 'VII-A',
    image:
      'https://images.unsplash.com/photo-1633332755192-727a05c4013d?q=80&w=1780&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    id: 2,
    studentName: 'John',
    class: 'VII-B',
    image:
      'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?q=80&w=1780&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    id: 3,
    studentName: 'Emma',
    class: 'VII-C',
    image:
      'https://images.unsplash.com/photo-1628157588553-5eeea00af15c?q=80&w=1780&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    id: 4,
    studentName: 'Sophia',
    class: 'VII-D',
    image:
      'https://images.unsplash.com/photo-1628157588553-5eeea00af15c?q=80&w=1780&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    id: 5,
    studentName: 'Liam',
    class: 'VII-A',
    image:
      'https://images.unsplash.com/photo-1628157588553-5eeea00af15c?q=80&w=1780&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    id: 6,
    studentName: 'Olivia',
    class: 'VII-B',
    image:
      'https://images.unsplash.com/photo-1633332755192-727a05c4013d?q=80&w=1780&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    id: 7,
    studentName: 'Noah',
    class: 'VII-C',
    image:
      'https://images.unsplash.com/photo-1633332755192-727a05c4013d?q=80&w=1780&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    id: 8,
    studentName: 'Ava',
    class: 'VII-D',
    image:
      'https://images.unsplash.com/photo-1633332755192-727a05c4013d?q=80&w=1780&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    id: 9,
    studentName: 'William',
    class: 'VII-A',
    image:
      'https://images.unsplash.com/photo-1633332755192-727a05c4013d?q=80&w=1780&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    id: 10,
    studentName: 'Isabella',
    class: 'VII-B',
    image:
      'https://images.unsplash.com/photo-1633332755192-727a05c4013d?q=80&w=1780&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
];

console.log(studentsArray);

function StudentSelection({ loginParent, loading, login, setParentMode }: any) {
  const theme = useTheme();
  const isLight = useSettings().themeMode === 'light';

  // State to keep track of the selected student
  const [selectedStudent, setSelectedStudent] = useState(null);

  // Function to handle radio button selection
  const handleRadioChange = (studentId) => {
    setSelectedStudent(studentId);
  };

  return (
    <StudentSelectionRoot>
      <Grid container spacing={10} padding="4rem">
        {studentsArray.map((student) => (
          <Grid item xl={3} lg={3} md={4} sm={6} xs={12} key={student.id}>
            <Card
              sx={{
                boxShadow: 0,
                position: 'relative',
                border: 2,
                borderColor: selectedStudent === student.id ? theme.palette.success.main : theme.palette.grey[100], // Change border color if selected
                backgroundColor: selectedStudent === student.id ? theme.palette.success.lighter : 'transparent', // Change background color if selected
              }}
            >
              <CardActionArea onClick={() => handleRadioChange(student.id)}>
                <Box display="flex" justifyContent="center" mt={3}>
                  <img style={{ borderRadius: '50%' }} width={100} src={student.image} alt="" />
                </Box>
                <CardContent sx={{ textAlign: 'center' }}>
                  <Typography variant="h5" fontSize={15} fontWeight={600}>
                    {student.studentName}
                  </Typography>
                  <Typography variant="h5" fontSize={12}>
                    {student.class}
                  </Typography>
                  {/* Radio button */}
                  <Stack position="absolute" top={10} right={10}>
                    <Radio
                      checked={selectedStudent === student.id}
                      disableFocusRipple
                      disableTouchRipple
                      disableRipple
                      color="success"
                      checkedIcon={selectedStudent === student.id ? <SuccessIcon /> : undefined}
                      onChange={(e) => e.stopPropagation()}
                    />
                  </Stack>
                </CardContent>
              </CardActionArea>
            </Card>
          </Grid>
        ))}
      </Grid>
      <Stack direction="row" justifyContent="center" position="absolute" bottom={20} width="100%">
        <LoadingButton
          loading={loading}
          loadingPosition="start"
          sx={{ width: 250 }}
          variant="contained"
          color="success"
          onClick={() => {
            login(loginParent);
            setParentMode(true);
          }}
        >
          {loading ? 'Signing in...' : 'Continue'}
        </LoadingButton>
      </Stack>
    </StudentSelectionRoot>
  );
}

export default StudentSelection;
