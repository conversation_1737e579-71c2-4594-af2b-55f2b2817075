/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/alt-text */
import React, { FormEvent, useCallback, useEffect, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Autocomplete,
  Box,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  SnackbarOrigin,
  Divider,
  Collapse,
  Avatar,
  FormControl,
  MenuItem,
  Select,
  SelectChangeEvent,
  useTheme,
} from '@mui/material';
import styled from 'styled-components';
import SearchIcon from '@mui/icons-material/Search';
import VisibilityIcon from '@mui/icons-material/Visibility';
import { MONTH_SELECT, YEAR_SELECT } from '@/config/Selection';
import { MdAdd } from 'react-icons/md';
import DeleteIcon from '@mui/icons-material/Delete';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import { TeacherDataTypes } from '@/types/ManageStaff';
import Popup from '@/components/shared/Popup/Popup';
import { teacherData } from '@/config/TableData';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import PositionedSnackbar from '@/components/shared/Popup/SnackBar';
import deleteBin from '@/assets/ManageFee/deleteBin.json';
import deleteSuccess from '@/assets/ManageFee/deleteSuccess.json';
import Success from '@/assets/ManageFee/Success.json';
import errorIcon from '@/assets/ManageFee/Error.json';
import errorFailedIcon from '@/assets/ManageFee/ErrorIcon.json';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import { IoIosArrowUp } from 'react-icons/io';
import dayjs, { Dayjs } from 'dayjs';

import { useConfirm } from '@/components/shared/Popup/Confirmation';
import { StaffListInfo, StaffListRequest } from '@/types/StaffManagement';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import useAuth from '@/hooks/useAuth';
import { useAppSelector } from '@/hooks/useAppSelector';
import {
  getStaffDataList,
  getStaffDataListStatus,
  getStaffDeletingRecords,
  getStaffListPageInfo,
  getStaffSortColumn,
  getStaffSortDirection,
  getStaffSubmitting,
  getYearData,
  getYearStatus,
} from '@/config/storeSelectors';
import {
  addNewStaff,
  deleteStaff,
  fetchStaffDataList,
  updateStaff,
} from '@/store/StaffMangement/StaffMangement/staffMangement.thunks';
import { setSortColumn, setSortDirection } from '@/store/StaffMangement/StaffMangement/staffMangement.slice';
import { fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import { ErrorMessage } from '@/components/shared/Popup/ErrorMessage';
import useSettings from '@/hooks/useSettings';
import { Tooltip } from '@mui/material';
import DatePickers from '@/components/shared/Selections/DatePicker';

export interface State extends SnackbarOrigin {
  open: boolean;
}

const DetailReportRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */

  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 50px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

const DefaultStaffInfo: StaffListInfo = {
  staffID: 0,
  academicId: 0,
  staffCode: '',
  staffJoinDate: '',
  staffName: '',
  staffGender: -1,
  staffDOB: '',
  staffBloodGroup: '',
  staffBirthPlace: '',
  staffNationality: '',
  staffMotherTongue: '',
  staffReligion: '',
  staffCaste: '',
  staffCastType: '',
  staffFatherName: '',
  staffMotherName: '',
  staffFatherOccupation: '',
  staffJobExperience: '',
  staffJobRole: '',
  staffJobDescription: '',
  staffPhoneNumber: '',
  staffPAddress: '',
  staffCAddress: '',
  staffEmailID: '',
  staffImage: '',
  staffStatus: '',
  staffCategory: 0,
  createdBy: 0,
};

function DetailReport() {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const { confirm } = useConfirm();
  const dispatch = useAppDispatch();
  const { user } = useAuth();
  const adminId: number = user ? user.accountId : 0;
  const YearData = useAppSelector(getYearData);
  const YearStatus = useAppSelector(getYearStatus);
  const defualtYear = YearData[0]?.accademicId || 0;
  const [selectedStaffDetail, setSelectedStaffDetail] = useState<StaffListInfo>(DefaultStaffInfo);
  const [academicYearFilter, setAcademicYearFilter] = useState(11);
  const [staffNameFilter, setStaffNameFilter] = useState('');
  const [staffCodeFilter, setStaffCodeFilter] = useState('');
  const [staffMobileNumberFilter, setStaffMobileNumberFilter] = useState('');

  const [Delete, setDelete] = React.useState(false);
  const [addWorkingDay, setAddWorkingDay] = React.useState(false);
  const [staffView, setStaffView] = React.useState(false); // New state variable
  const [showFilter, setShowFilter] = useState(true);
  const [monthFilter, setMonthFilter] = useState(12);

  const handleClickDelete = () => setDelete(true);
  const handleClickCloseDelete = () => setDelete(false);

  const [staff, setStaffs] = React.useState(teacherData);
  const [createorEditOpen, setCreateorEditOpen] = React.useState(false);
  const [selectedStaff, setSelectedStaff] = React.useState(null);
  const [mapStaffOpen, setMapStaffOpen] = React.useState(false);
  const [showAddStaff, setShowAddStaff] = React.useState<'individual' | 'multiple' | 'manageStaff'>('manageStaff');

  const StaffDataListStatus = useAppSelector(getStaffDataListStatus);
  const StaffListData = useAppSelector(getStaffDataList);
  // const StaffListError = useAppSelector(getStaffListError);
  const paginationInfo = useAppSelector(getStaffListPageInfo);
  const sortColumn = useAppSelector(getStaffSortColumn);
  const sortDirection = useAppSelector(getStaffSortDirection);
  const isSubmitting = useAppSelector(getStaffSubmitting);

  const { pagenumber, pagesize, totalrecords } = paginationInfo;

  const currentStaffListRequest = useMemo(
    () => ({
      pageNumber: 1,
      pageSize: 10,
      sortColumn,
      sortDirection,
      filters: {
        adminId,
        academicId: 11,
        staffCode: staffCodeFilter,
        staffName: '',
        staffPhoneNumber: '',
      },
    }),
    [adminId, staffCodeFilter, sortColumn, sortDirection]
  );

  const loadStaffList = useCallback(
    (request: StaffListRequest) => {
      dispatch(fetchStaffDataList(request));
    },
    [dispatch]
  );

  useEffect(() => {
    if (YearStatus === 'idle') {
      setAcademicYearFilter(defualtYear);
    }
    if (StaffDataListStatus === 'idle') {
      dispatch(fetchYearList(adminId));
      loadStaffList(currentStaffListRequest);
    }
    // console.log('datass::', StaffListData);
  }, [loadStaffList, StaffDataListStatus, defualtYear, YearStatus, currentStaffListRequest, dispatch, adminId]);

  const handleYearChange = (e: SelectChangeEvent) => {
    const selectedAcademicId = e.target.value;
    setAcademicYearFilter(parseInt(selectedAcademicId, 10));
    loadStaffList({
      ...currentStaffListRequest,
      filters: {
        ...currentStaffListRequest.filters,
        academicId: selectedAcademicId,
      },
    });
  };

  const handleMonthChange = (e: SelectChangeEvent) => {
    const selectedMonth = parseInt(e.target.value, 10);
    if (selectedMonth) {
      setMonthFilter(selectedMonth);
    }
    // loadAttendanceCalendar({
    //   ...currentAttendanceCalendarRequest,
    //   month: selectedMonth || 0,
    // });
  };

  const handlePageChange = useCallback(
    (event: unknown, newPage: number) => {
      loadStaffList({ ...currentStaffListRequest, pageNumber: newPage + 1 });
    },
    [currentStaffListRequest, loadStaffList]
  );

  const handleChangeRowsPerPage = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const newPageSize = +event.target.value;
      loadStaffList({ ...currentStaffListRequest, pageNumber: 1, pageSize: newPageSize });
    },
    [currentStaffListRequest, loadStaffList]
  );

  const handleSort = useCallback(
    (column: string) => {
      const newReq = { ...currentStaffListRequest };
      if (column === sortColumn) {
        console.log('Toggling direction');
        const sortDirectionNew = sortDirection === 'asc' ? 'desc' : 'asc';
        newReq.pageNumber = 1;
        newReq.sortDirection = sortDirectionNew;
        dispatch(setSortDirection(sortDirectionNew));
      } else {
        newReq.pageNumber = 1;
        newReq.sortColumn = column;
        dispatch(setSortColumn(column));
      }

      loadStaffList(newReq);
    },
    [currentStaffListRequest, dispatch, loadStaffList, sortColumn, sortDirection]
  );

  const pageProps = useMemo(
    () => ({
      rowsPerPageOptions: [10, 20, 30, 50, 100, 200, 300, 500],
      pageNumber: pagenumber - 1,
      pageSize: pagesize,
      totalRecords: totalrecords,
      onPageChange: handlePageChange,
      onRowsPerPageChange: handleChangeRowsPerPage,
    }),
    [handleChangeRowsPerPage, handlePageChange, pagenumber, pagesize, totalrecords]
  );

  const handleReset = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      setAcademicYearFilter(0);
      setStaffNameFilter('');
      setStaffCodeFilter('');
      setStaffMobileNumberFilter('');
      loadStaffList({
        ...currentStaffListRequest,
        pageNumber: 1,
        pageSize: 20,
        sortColumn: 'staffID',
        sortDirection: 'desc',
        filters: {
          adminId,
          academicId: 0,
          staffCode: '',
          staffName: '',
          staffPhoneNumber: '',
        },
      });
    },
    [currentStaffListRequest, loadStaffList, adminId]
  );

  const handleEditSubject = useCallback((SubjectObj: StaffListInfo) => {
    setSelectedStaffDetail(SubjectObj);
    setCreateorEditOpen(true);
  }, []);

  const handleSaveorEdit = useCallback(
    async (value: StaffListInfo, mode: 'create' | 'edit') => {
      try {
        if (mode === 'create') {
          const { staffID, ...rest } = value;
          const response = await dispatch(addNewStaff(rest)).unwrap();

          if (response.id > 0) {
            const successMessage = (
              <SuccessMessage loop={false} jsonIcon={Success} message="Staff created successfully" />
            );
            await confirm(successMessage, 'Staff Created', { okLabel: 'Ok', showOnlyOk: true });
            setShowAddStaff('manageStaff');
            loadStaffList({
              ...currentStaffListRequest,
              pageNumber: 1,
              sortColumn: 'staffID',
              sortDirection: 'desc',
            });
          } else {
            const errorMessage = (
              <ErrorMessage loop={false} jsonIcon={errorFailedIcon} message="Staff created failed" />
            );
            await confirm(errorMessage, 'Staff Create', { okLabel: 'Ok', showOnlyOk: true });
          }
        } else {
          const { staffDOB, staffJoinDate } = value;

          const isDateFormatted = (date: string) => {
            const dateRegex = /^\d{2}\/\d{2}\/\d{4}$/;
            return dateRegex.test(date);
          };

          // Function to format dates
          const formatDate = (date: Dayjs | string | number | null): string | null => {
            if (date === null) return null;
            if (dayjs.isDayjs(date)) return date.format('DD/MM/YYYY');
            if (typeof date === 'string' && isDateFormatted(date)) return date; // Already formatted
            if (typeof date === 'string' || typeof date === 'number') return dayjs(date).format('DD/MM/YYYY');
            return null; // Fallback for any unexpected types
          };

          // Convert startDate and endDate to DD/MM/YYYY format
          const formattedStaffDOB = formatDate(staffDOB);
          const formattedStaffJoinDate = formatDate(staffJoinDate);
          const updateReq = {
            ...value,
            staffDOB: formattedStaffDOB,
            staffJoinDate: formattedStaffJoinDate,
            dbResult: '',
          };
          const response = await dispatch(updateStaff(updateReq)).unwrap();
          if (response) {
            loadStaffList({
              ...currentStaffListRequest,
            });
            setCreateorEditOpen(false);
            const successMessage = (
              <SuccessMessage loop={false} jsonIcon={Success} message="Staff updated successfully" />
            );
            await confirm(successMessage, 'Staff Updated', { okLabel: 'Ok', showOnlyOk: true });
          } else {
            const errorMessage = (
              <ErrorMessage loop={false} jsonIcon={errorFailedIcon} message="Staff created failed" />
            );
            await confirm(errorMessage, 'Staff Create', { okLabel: 'Ok', showOnlyOk: true });
          }
        }
      } catch {
        const errorMessage = <ErrorMessage loop={false} jsonIcon={errorFailedIcon} message="Staff created failed" />;
        await confirm(errorMessage, 'Staff Create', { okLabel: 'Ok', showOnlyOk: true });
      }
    },
    [confirm, currentStaffListRequest, dispatch, loadStaffList]
  );

  const currentItemCount = StaffListData.length;

  const handleDeleteStaff = useCallback(
    async (staffObj: StaffListInfo) => {
      try {
        const { staffID, staffName } = staffObj;

        const deleteConfirmMessage = (
          <DeleteMessage
            jsonIcon={deleteBin}
            message={
              <div>
                Are you sure you want to delete the Staff <br />
                <span style={{ color: theme.palette.error.main }}>&quot;{staffName}&quot;</span> ?
              </div>
            }
          />
        );
        if (await confirm(deleteConfirmMessage, 'Delete Staff?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
          const deleteResponse = await dispatch(deleteStaff(staffID)).unwrap();
          if (deleteResponse.deleted) {
            loadStaffList(currentStaffListRequest);
            const deleteDoneMessage = (
              <DeleteMessage loop={false} jsonIcon={deleteSuccess} message="Staff deleted successfully." />
            );
            await confirm(deleteDoneMessage, 'Deleted', { okLabel: 'Ok', showOnlyOk: true });
            let pageNumberToMove = pagenumber;
            if (paginationInfo.remainingpages === 0 && pagenumber > 1 && currentItemCount === 1) {
              pageNumberToMove = pagenumber - 1;
              loadStaffList({ ...currentStaffListRequest, pageNumber: pageNumberToMove });
            } else {
              // loadStaffList(currentStaffListRequest);
            }
          } else {
            const errorMessage = (
              <ErrorMessage loop={false} jsonIcon={errorFailedIcon} message="Staff deleted failed" />
            );
            await confirm(errorMessage, 'Staff Delete', { okLabel: 'Ok', showOnlyOk: true });
          }
        }
      } catch {
        const errorMessage = (
          <ErrorMessage loop={false} jsonIcon={errorFailedIcon} message="Something went wrong please try again." />
        );
        await confirm(errorMessage, 'Staff Delete', { okLabel: 'Ok', showOnlyOk: true });
      }
    },
    [
      confirm,
      currentStaffListRequest,
      currentItemCount,
      dispatch,
      loadStaffList,
      pagenumber,
      paginationInfo.remainingpages,
      theme,
    ]
  );

  const getRowKey = useCallback((row: StaffListInfo) => row.staffID, []);

  const handleCreateStaff = () => {
    setCreateorEditOpen(true);
  };

  const handleClose = () => {
    setCreateorEditOpen(false);
  };

  const handleUpdate = (updatedData: any, id: number) => {
    const updatedStaffs = staff.map((staffs) => (staffs.id === id ? { ...staffs, ...updatedData } : staffs));
    setStaffs(updatedStaffs);
    console.log(updatedStaffs);
    setCreateorEditOpen(false);
  };

  const handleCreate = (newStaff: any) => {
    const updatedStaffs = [...staff, newStaff];
    setStaffs(updatedStaffs);
    console.log(updatedStaffs);
    // setCreateOpen(false);
  };

  const [openSnackBar, setOpenSnackBar] = React.useState<boolean>(false);

  const handleOpenSnackBar = () => setOpenSnackBar(true);

  const handleCloseSnackBar = () => {
    setOpenSnackBar(false);
  };

  const ManageStaffColumns: DataTableColumn<StaffListInfo>[] = useMemo(
    () => [
      {
        name: 'staffID',
        dataKey: 'staffID',
        headerLabel: 'Sl No.',
        sortable: true,
      },
      {
        name: 'Selfie',
        headerLabel: 'Selfie',
        renderCell: (row) => {
          return (
            <Stack direction="row" gap={1} alignItems="center">
              <Avatar alt="" src={row.staffImage} />
            </Stack>
          );
        },
      },
      {
        name: 'punchIn',
        dataKey: 'punchIn',
        headerLabel: 'Punch In',
      },
      {
        name: 'month',
        dataKey: 'month',
        headerLabel: 'Month',
      },
      {
        name: 'location',
        dataKey: 'location',
        headerLabel: 'Location',
      },
      {
        name: 'outReason',
        dataKey: 'outReason',
        headerLabel: 'Out Reason',
      },
      {
        name: 'lateReason',
        dataKey: 'lateReason',
        headerLabel: 'Late Reason',
      },
      {
        name: 'punchOut',
        dataKey: 'punchOut',
        headerLabel: 'Punch Out',
      },
      {
        name: 'selfie',
        dataKey: 'selfie',
        headerLabel: 'Selfie',
      },
      {
        name: 'location',
        dataKey: 'location',
        headerLabel: 'Location',
      },
      {
        name: 'earlyReason',
        dataKey: 'earlyReason',
        headerLabel: 'Early Reason',
      },
    ],
    [handleDeleteStaff, handleEditSubject]
  );

  return (
    <Box>
      <DetailReportRoot>
        <Paper sx={{ m: 3, border: 1, borderColor: theme.palette.grey[300] }}>
          <DataTable
            columns={ManageStaffColumns}
            data={StaffListData}
            getRowKey={getRowKey}
            fetchStatus={StaffDataListStatus}
            allowSorting
            sortColumn={sortColumn}
            sortDirection={sortDirection}
            onSort={handleSort}
          />
        </Paper>
      </DetailReportRoot>
    </Box>
  );
}

export default DetailReport;
