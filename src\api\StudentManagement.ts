import ApiUrls from '@/config/ApiUrls';
import {
  QuickUpdateStudentListInfo,
  StudentCreateRequest,
  StudentCreateRow,
  StudentListInfo,
  StudentListPagedData,
  StudentListRequest,
} from '@/types/StudentManagement';
import { CreateResponse, CreateResponseMulti, DeleteResponse, UpdateResponse } from '@/types/Common';
import { privateApi } from '@/api/base/api';
import { APIResponse } from '@/api/base/types';

async function GetStudentList(request: StudentListRequest): Promise<APIResponse<StudentListPagedData>> {
  const response = await privateApi.post<StudentListPagedData>(ApiUrls.GetStudentList, request);
  return response;
}

async function StudentList(studentId: number | undefined): Promise<APIResponse<StudentListPagedData[]>> {
  if (studentId === undefined) {
    throw new Error('studentId is required');
  }

  const url = ApiUrls.GetStudent.replace('{studentId}', studentId.toString());
  const response = await privateApi.get<any>(url);
  return response;
}

async function AddNewStudent(request: StudentCreateRequest): Promise<APIResponse<CreateResponse>> {
  const response = await privateApi.post<CreateResponse>(ApiUrls.AddStudent, request);
  return response;
}

async function AddNewMultipleStudent(request: StudentCreateRow[]): Promise<APIResponse<CreateResponseMulti>> {
  const response = await privateApi.post<CreateResponseMulti>(ApiUrls.AddMultipleStaff, request);
  return response;
}

async function UpdateStudent(request: StudentListInfo): Promise<APIResponse<UpdateResponse>> {
  const response = await privateApi.post<UpdateResponse>(ApiUrls.UpdateStudent, request);
  return response;
}

async function DeleteStudent(studentId: number): Promise<APIResponse<DeleteResponse>> {
  const url = ApiUrls.DeleteStudent.replace('{studentId}', studentId.toString());
  const response = await privateApi.get<DeleteResponse>(url);
  return response;
}

async function StudentAdmNumberExists(admissionNumber: string): Promise<APIResponse<boolean>> {
  const url = `${ApiUrls.StudentAdmNumberExists}?q=${admissionNumber}`;
  const response = await privateApi.get<boolean>(url);
  return response;
}

async function GetQucickUpdateStudentList(
  academicId: number | undefined,
  classId: number | undefined
): Promise<APIResponse<QuickUpdateStudentListInfo[]>> {
  if (academicId === undefined || classId === undefined) {
    throw new Error('academicId and classId is required');
  }

  const url = ApiUrls.GetQucickUpdateStudentList.replace('{academicId}', academicId.toString()).replace(
    '{classId}',
    classId.toString()
  );
  const response = await privateApi.get<any>(url);
  return response;
}

async function QucickUpdate(request: QuickUpdateStudentListInfo): Promise<APIResponse<UpdateResponse>> {
  const response = await privateApi.post<UpdateResponse>(ApiUrls.QucickUpdate, request);
  return response;
}

const methods = {
  GetStudentList,
  AddNewStudent,
  AddNewMultipleStudent,
  UpdateStudent,
  DeleteStudent,
  StudentAdmNumberExists,
  StudentList,
  GetQucickUpdateStudentList,
  QucickUpdate,
};

export default methods;
