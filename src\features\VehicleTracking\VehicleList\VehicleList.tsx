/* eslint-disable jsx-a11y/alt-text */
import React, { FormEvent, useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Box,
  Stack,
  Button,
  Typography,
  Card,
  useTheme,
  Grid,
  MenuItem,
  TextField,
  Tooltip,
  IconButton,
  Chip,
  Collapse,
  FormControl,
  Select,
} from '@mui/material';
import styled from 'styled-components';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import { MdAdd } from 'react-icons/md';
import Popup from '@/components/shared/Popup/Popup';
import { enquiryData } from '@/config/TableData';
import useSettings from '@/hooks/useSettings';
import SearchIcon from '@mui/icons-material/Search';
import { STATUS_OPTIONS } from '@/config/Selection';
import MenuEditDeleteView from '@/components/shared/Selections/MenuEditDeleteView';
import { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import Map from './Map';
import CreateVehicle from './Create';
import VehicleDetailedView from './VehicleDetailedView';

const VehicleListRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  h6 {
    font-weight: 600;
  }
  .MuiTextField-root:hover .MuiInputBase-input {
    cursor: text;
  }
  .Card {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      /* max-height: calc(100% - 40px); */
      flex-grow: 1;

      .card-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: auto;
        ::-webkit-scrollbar {
          width: 0px;
        }
      }
    }
    .vehicle-card {
      border: 2px solid ${(props) => props.theme.palette.grey[300]};
      transition: border-color 0.3s;
      &:hover {
        border: 2px solid ${(props) => props.theme.palette.grey[400]};
      }
      &.active {
        border: 2px solid ${(props) => props.theme.palette.primary.main};
      }
    }
  }
`;

export const data = [
  {
    id: 1,
    vehicle: 'Bus',
    regNo: 'KL-09-5474',
    sim: '478897',
    exeName: 'Justin Holland 10',
    driverName: 'Chris Scheider',
    vehicleRoot: 'Palakkad to Coibatore',
    zoom: 12,
  },
  {
    id: 3,
    vehicle: 'Bus',
    regNo: 'KL-09-5474',
    sim: '478897',
    exeName: 'Justin Holland 10',
    driverName: 'Chris Scheider',
    vehicleRoot: 'Palakkad to Coibatore',
    zoom: 8,
  },
  {
    id: 4,
    vehicle: 'Bus',
    regNo: 'KL-09-5474',
    sim: '478897',
    exeName: 'Justin Holland 10',
    driverName: 'Chris Scheider',
    vehicleRoot: 'Palakkad to Coibatore',
    zoom: 16,
  },
  {
    id: 6,
    vehicle: 'Bus',
    regNo: 'KL-09-5474',
    sim: '478897',
    exeName: 'Justin Holland 10',
    driverName: 'Chris Scheider',
    vehicleRoot: 'Palakkad to Coibatore',
    zoom: 20,
  },
  {
    id: 9,
    vehicle: 'Bus',
    regNo: 'KL-09-5474',
    sim: '478897',
    exeName: 'Justin Holland 10',
    driverName: 'Chris Scheider',
    vehicleRoot: 'Palakkad to Coibatore',
    zoom: 2,
  },
  {
    id: 11,
    vehicle: 'Bus',
    regNo: 'KL-09-5474',
    sim: '478897',
    exeName: 'Justin Holland 13',
    driverName: 'Chris Scheider',
    vehicleRoot: 'Palakkad to Coibatore',
    zoom: 6,
  },
  {
    id: 14,
    vehicle: 'Bus',
    regNo: 'KL-09-5474',
    sim: '478897',
    exeName: 'Justin Holland 10',
    driverName: 'Chris Scheider',
    vehicleRoot: 'Palakkad to Coibatore',
    zoom: 4,
  },
];

function VehicleList() {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const [showFilter, setShowFilter] = useState(true);
  const [popup, setPopup] = React.useState(false);
  const [popup2, setPopup2] = React.useState(false);
  const [students, setStudents] = React.useState(enquiryData);
  const [createPopup, setCreatePopup] = React.useState(false);
  const [detailedViewPopup, setDetailedViewPopup] = React.useState(false);
  const [selectedStudent, setSelectedStudent] = React.useState(null);

  const [activeCard, setActiveCard] = useState<number | null>(null);
  const [zoomLevel, setZoomLevel] = useState<number>(10); // Track zoom level

  const handleCardClick = (id: number, zoom: number) => {
    setActiveCard((prev) => (prev === id ? null : id)); // Toggle active state
    setZoomLevel(zoom); // Update zoom level based on clicked card
  };
  const handleClickOpen = () => {
    setPopup(true);
    setCreatePopup(false);
  };
  const handleClickClose = () => {
    setPopup(false);
  };

  const handleMapOpen = () => {
    setPopup2(true);
    setCreatePopup(false);
  };
  const handleMapClose = () => {
    setPopup2(false);
  };

  const handleOpen = (student: any) => {
    setSelectedStudent(student);
    setCreatePopup(true);
  };

  const handleClose = () => {
    setCreatePopup(false);
  };

  const handleUpdate = (SlNo: number, updatedData: any) => {
    const updatedStudents = students.map((student) =>
      student.SlNo === SlNo ? { ...student, ...updatedData } : student
    );
    setStudents(updatedStudents);
    setCreatePopup(false);
  };

  const handleReset = useCallback((e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    // setAcademicYearFilter(defaultYear);
    // setFeeTypeFilter(0);
    // setTermTitleFilter('');
    // loadTermFeeList({
    //   adminId,
    //   accademicId: academicYearFilter,
    //   termTitle: termTitleFilter,
    //   feeTypeId: feeTypeFilter,
    // });
  }, []);

  const ObjExamListColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'vehicle',
        headerLabel: 'Vehicle',
        dataKey: 'vehicle',
      },
      {
        name: 'regNo',
        dataKey: 'regNo',
        headerLabel: 'Reg No',
      },

      {
        name: 'sim',
        headerLabel: 'Sim',
        renderCell: (row) => {
          return (
            <Chip
              size="small"
              label="788555"
              variant="outlined"
              color="info"
              sx={{
                border: '0px',
                backgroundColor: isLight ? theme.palette.info.lighter : theme.palette.grey[700],
                color: isLight ? theme.palette.info.darker : theme.palette.grey[100],
              }}
            />
          );
        },
      },
      {
        name: 'exeName',
        dataKey: 'exeName',
        headerLabel: 'Exe Name',
      },
      {
        name: 'driverName',
        dataKey: 'driverName',
        headerLabel: 'Driver Name',
      },
      {
        name: 'vehicleRoot',
        dataKey: 'vehicleRoot',
        headerLabel: 'Vehicle Root',
      },
    ],
    [isLight, theme]
  );

  return (
    <Page title="Vehicle List">
      <VehicleListRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 1, md: 2 } }}>
          <Box display="flex" pb={0.5} justifyContent="space-between">
            <Typography variant="h6" fontSize={20}>
              Track
            </Typography>
            <Stack direction="row" alignItems="center" gap={1}>
              <Tooltip title="Search">
                <IconButton aria-label="delete" color="primary" onClick={() => setShowFilter((x) => !x)}>
                  <SearchIcon />
                </IconButton>
              </Tooltip>
              <Button
                sx={{ borderRadius: '20px' }}
                variant="outlined"
                size="small"
                onClick={() => setCreatePopup(true)}
              >
                <MdAdd size="20px" /> Create
              </Button>
            </Stack>
          </Box>

          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate onReset={handleReset}>
                <Grid pb={2} pt={1} container spacing={3} alignItems="end">
                  <Grid item xxl={2} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Vehicle Reg No
                      </Typography>
                      <TextField placeholder="Enter" name="" />
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Vehicle Name
                      </Typography>
                      <TextField placeholder="Enter" name="" />
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Sim Number
                      </Typography>
                      <TextField placeholder="Enter" name="" />
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Status
                      </Typography>
                      <Select
                        name=""
                        // value={vehicleStatus}
                        // onChange={handleChange}
                        fullWidth
                      >
                        {STATUS_OPTIONS.map((opt) => (
                          <MenuItem key={opt.id} value={opt.id}>
                            {opt.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto">
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button type="reset" variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>
            {/* calc(100vh - 250px) */}
            <Box className="card-container" my={2}>
              <Grid container spacing={5}>
                <Grid item lg={4} xs={6} py={2} sx={{ height: 'calc(100vh - 250px)', overflow: 'scroll' }}>
                  {data.map((vehicle) => (
                    <Card
                      key={vehicle.id}
                      className={`vehicle-card ${activeCard === vehicle.id ? 'active' : ''}`}
                      sx={{ mb: 2, p: 2 }}
                    >
                      <Stack direction="row" position="absolute" right={5} alignItems="center" gap={1} ml={1}>
                        <MenuEditDeleteView
                          Edit={() => {
                            return 0;
                          }}
                          Delete={() => {
                            return 0;
                          }}
                          View={() => setDetailedViewPopup(true)}
                        />
                      </Stack>

                      {ObjExamListColumns.map((item) => (
                        <Stack direction="row" ml={1}>
                          <Grid container>
                            <Grid item lg={5} xs={6} mt={1}>
                              <Typography key={item.name} variant="subtitle1" fontSize={13} mr={2}>
                                {item.headerLabel}
                              </Typography>
                            </Grid>
                            <Grid item lg={7} xs={6} mb={0} mt="auto">
                              <Typography
                                variant="h6"
                                fontSize={13}
                                color={item.dataKey === 'meetingLink' ? theme.palette.info.main : ''}
                              >
                                {item.dataKey
                                  ? `:${' '}${(vehicle as { [key: string]: any })[item.dataKey ?? '']}`
                                  : item && item.renderCell && item.renderCell(data, vehicle.id)}
                              </Typography>
                            </Grid>
                          </Grid>
                        </Stack>
                      ))}
                      <Button
                        type="reset"
                        variant="contained"
                        color="primary"
                        onClick={() => handleCardClick(vehicle.id, vehicle.zoom)}
                        sx={{
                          mt: 1,
                          // backgroundColor: theme.palette.chart.violet[0],
                          // '&:hover': {
                          //   backgroundColor: theme.palette.chart.violet[1],
                          // },
                        }}
                        fullWidth
                      >
                        Track
                      </Button>
                    </Card>
                  ))}
                </Grid>
                <Grid item lg={8} xs={6}>
                  <Map zoom={zoomLevel} />
                </Grid>
              </Grid>
            </Box>
          </div>
        </Card>
        <Popup
          size="xs"
          state={popup}
          onClose={handleClickClose}
          popupContent={<SuccessMessage message="Updated Successfully" />}
        />
        <Popup
          size="sm"
          title="Detailed View"
          state={detailedViewPopup}
          onClose={() => setDetailedViewPopup(false)}
          popupContent={<VehicleDetailedView onClose={() => setDetailedViewPopup(false)} />}
        />
        <Popup
          size="sm"
          title="Create Vehicle"
          state={createPopup}
          onClose={handleClose}
          popupContent={<CreateVehicle onClose={handleClose} />}
        />
      </VehicleListRoot>
    </Page>
  );
}

export default VehicleList;
