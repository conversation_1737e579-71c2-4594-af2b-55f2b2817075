import React, { ChangeEvent, FormEvent, useCallback, useEffect, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Divider,
  Paper,
  TextField,
  Typography,
  Card,
  IconButton,
  Box,
  Stack,
  Select,
  MenuItem,
  Button,
  Collapse,
  useTheme,
  Grid,
  FormControl,
  SelectChangeEvent,
  Tooltip,
} from '@mui/material';
import styled from 'styled-components';
import { useAppSelector } from '@/hooks/useAppSelector';
import {
  getClassSectionsData,
  getClassSortListData,
  getClassSortListPageInfo,
  getClassSortListStatus,
  getClassSortSubmitting,
  getManageFeeclassListData,
  getSortClassSortColumn,
  getSortClassSortDirection,
  getYearData,
  getYearStatus,
} from '@/config/storeSelectors';
import SearchIcon from '@mui/icons-material/Search';
import ErrorIcon from '@/assets/ManageFee/ErrorIcon.json';
import Success from '@/assets/ManageFee/Success.json';
import SaveIcon from '@mui/icons-material/Save';
import { setSortColumn, setSortDirection } from '@/store/Academics/ClassManagement/classManagement.slice';
import { fetchClassSortList, updateClassSort } from '@/store/Academics/ClassManagement/classManagement.thunks';
import { ClassSortListInfo, ClassSortListRequest } from '@/types/AcademicManagement';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import useAuth from '@/hooks/useAuth';
import { fetchClassList, fetchClassSections } from '@/store/ManageFee/manageFee.thunks';
import useSettings from '@/hooks/useSettings';
import { fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import { ErrorMessage } from '@/components/shared/Popup/ErrorMessage';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import LoadingButton from '@mui/lab/LoadingButton';
import { Checkbox } from '@mui/material';

const ClassSortRoot = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      height: calc(100% - 40px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

function ClassSort() {
  // const [forceDispatch, setForceDispatch] = useState(false);
  const theme = useTheme();
  const isLight = useSettings().themeMode === 'light';
  const { confirm } = useConfirm();
  const dispatch = useAppDispatch();
  const { user } = useAuth();
  const adminId: number = user ? user.accountId : 0;
  const YearData = useAppSelector(getYearData);
  const ClassSectionsData = useAppSelector(getClassSectionsData);
  const classListData = useAppSelector(getManageFeeclassListData);
  const YearStatus = useAppSelector(getYearStatus);
  const defualtYear = YearData[0]?.accademicId || 0;

  const classSortListStatus = useAppSelector(getClassSortListStatus);
  const classSortListData = useAppSelector(getClassSortListData);
  const paginationInfo = useAppSelector(getClassSortListPageInfo);
  const sortColumn = useAppSelector(getSortClassSortColumn);
  const sortDirection = useAppSelector(getSortClassSortDirection);
  const isSubmitting = useAppSelector(getClassSortSubmitting);
  // const deletingRecords = useAppSelector(getDeletingRecords);
  const [showFilter, setShowFilter] = useState(true);
  const [academicYearFilter, setAcademicYearFilter] = useState(defualtYear);
  const [classFilter, setClassFilter] = useState(-1);
  const [classSectionsFilter, setClassSectionsFilter] = useState(-1);
  const [selectedRows, setSelectedRows] = useState<Record<number, boolean>>({});
  const [editedRows, setEditedRows] = useState<ClassSortListInfo[]>([]);
  const [isAllSelected, setIsAllSelected] = useState<boolean>(false);

  const { pagenumber, pagesize, totalrecords } = paginationInfo;

  const currentClassSortListRequest = useMemo(
    () => ({
      pageNumber: pagenumber,
      pageSize: pagesize,
      sortColumn,
      sortDirection,
      filters: {
        adminId,
        academicId: academicYearFilter,
        sectionId: classSectionsFilter,
        classId: classFilter,
      },
    }),
    [pagenumber, pagesize, adminId, sortColumn, sortDirection, classSectionsFilter, classFilter, academicYearFilter]
  );

  const loadClassSortList = useCallback(
    (request: ClassSortListRequest) => {
      dispatch(fetchClassSortList(request));
    },
    [dispatch]
  );

  useEffect(() => {
    dispatch(fetchYearList(adminId));
    dispatch(fetchClassSections({ adminId, academicId: Number(academicYearFilter) }));
    dispatch(fetchClassList({ adminId, academicId: Number(academicYearFilter), sectionId: classSectionsFilter }));
    if (classSortListStatus === 'idle') {
      loadClassSortList(currentClassSortListRequest);
    }
  }, [
    loadClassSortList,
    classSortListStatus,
    currentClassSortListRequest,
    classSectionsFilter,
    academicYearFilter,
    adminId,
    dispatch,
  ]);

  const handleYearChange = (e: SelectChangeEvent) => {
    const selectedAcademicId = parseInt(e.target.value, 10);
    setAcademicYearFilter(selectedAcademicId);
    loadClassSortList({
      ...currentClassSortListRequest,
      filters: {
        ...currentClassSortListRequest.filters,
        academicId: selectedAcademicId,
      },
    });
  };

  const handleClassSectionChange = (e: SelectChangeEvent) => {
    const selectedSectionId = parseInt(e.target.value, 10);
    setClassSectionsFilter(selectedSectionId);
    loadClassSortList({
      ...currentClassSortListRequest,
      filters: {
        ...currentClassSortListRequest.filters,
        sectionId: selectedSectionId,
      },
    });
  };

  const handleClassChange = (e: SelectChangeEvent) => {
    const selectedClassId = parseInt(e.target.value, 10);
    setClassFilter(selectedClassId);
    loadClassSortList({
      ...currentClassSortListRequest,
      filters: {
        ...currentClassSortListRequest.filters,
        classId: selectedClassId,
      },
    });
  };
  const getRowKey = useCallback((row: ClassSortListInfo) => row.classId, []);

  const handlePageChange = useCallback(
    (event: unknown, newPage: number) => {
      loadClassSortList({ ...currentClassSortListRequest, pageNumber: newPage + 1 });
    },
    [currentClassSortListRequest, loadClassSortList]
  );

  const handleChangeRowsPerPage = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const newPageSize = +event.target.value;
      loadClassSortList({ ...currentClassSortListRequest, pageNumber: 1, pageSize: newPageSize });
    },
    [currentClassSortListRequest, loadClassSortList]
  );

  const handleSort = useCallback(
    (column: string) => {
      const newReq = { ...currentClassSortListRequest };
      if (column === sortColumn) {
        console.log('Toggling direction');
        const sortDirectionNew = sortDirection === 'asc' ? 'desc' : 'asc';
        newReq.pageNumber = 1;
        newReq.sortDirection = sortDirectionNew;
        dispatch(setSortDirection(sortDirectionNew));
      } else {
        newReq.pageNumber = 1;
        newReq.sortColumn = column;
        dispatch(setSortColumn(column));
      }

      loadClassSortList(newReq);
    },
    [currentClassSortListRequest, dispatch, loadClassSortList, sortColumn, sortDirection]
  );

  const handleReset = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      setAcademicYearFilter(YearData[0]?.accademicId);
      setClassFilter(0);
      setClassSectionsFilter(0);
      loadClassSortList({
        ...currentClassSortListRequest,
        filters: {
          ...currentClassSortListRequest.filters,
          academicId: YearData[0]?.accademicId,
          classId: 0,
          sectionId: 0,
        },
      });
    },
    [loadClassSortList, YearData, currentClassSortListRequest]
  );

  const classListUpdateColumns: DataTableColumn<ClassSortListInfo>[] = useMemo(
    () => [
      {
        name: 'classId',
        headerLabel: 'Old Id',
        dataKey: 'classId',
        sortable: true,
      },
      {
        name: 'className',
        dataKey: 'className',
        headerLabel: 'Class Name',
      },
      {
        name: 'classSection',
        dataKey: 'classSection',
        headerLabel: 'Class Section',
      },
      {
        name: 'sortOrder',
        dataKey: 'sortOrder',
        headerLabel: 'Sort Order',
      },
    ],
    []
  );

  const handleUpdateAll = useCallback(async () => {
    if (editedRows.length < 1) return; // No rows to update

    try {
      const deleteConfirmMessage = (
        <SuccessMessage
          message={
            <Card sx={{ my: 1, boxShadow: 0, width: 500, border: 1, borderColor: theme.palette.grey[300] }}>
              <DataTable
                columns={classListUpdateColumns}
                data={editedRows}
                getRowKey={getRowKey}
                fetchStatus={classSortListStatus}
              />
            </Card>
          }
        />
      );

      if (await confirm(deleteConfirmMessage, 'Update Rows?', { okLabel: 'Update', cancelLabel: 'Cancel' })) {
        const promises = editedRows.map(async (row) => {
          const updateReq = [
            {
              ...row,
              createdBy: adminId,
              dbResult: 'string',
            },
          ];
          console.log('Update request:', updateReq);
          const response = await dispatch(updateClassSort(updateReq)).unwrap();
          return response;
        });

        const responses = await Promise.all(promises);

        const successfulResponses = responses.filter((response) => response !== null);
        const isSuccess = successfulResponses.length === responses.length && successfulResponses.every(Boolean);

        if (isSuccess) {
          setIsAllSelected(false);
          setSelectedRows([]);
          setEditedRows([]);
          loadClassSortList({ ...currentClassSortListRequest });
          await confirm(
            <SuccessMessage loop={false} jsonIcon={Success} message="Class Sort updated successfully" />,
            'Class Sort Updated',
            { okLabel: 'Ok', showOnlyOk: true }
          );
        } else {
          await confirm(
            <ErrorMessage loop={false} jsonIcon={ErrorIcon} message="Some updates failed." />,
            'Class Sort Update',
            { okLabel: 'Ok', showOnlyOk: true }
          );
        }
      }
    } catch (error) {
      await confirm(
        <ErrorMessage loop={false} jsonIcon={ErrorIcon} message="Something went wrong. Please try again." />,
        'Class Sort Update',
        { okLabel: 'Ok', showOnlyOk: true }
      );
      console.error('Error updating class sort:', error);
    }
  }, [
    dispatch,
    confirm,
    loadClassSortList,
    classSortListStatus,
    classListUpdateColumns,
    currentClassSortListRequest,
    adminId,
    editedRows,
    getRowKey,
    theme,
  ]);

  const pageProps = useMemo(
    () => ({
      rowsPerPageOptions: [10, 20, 30, 50, 100, 200, 300, 500],
      pageNumber: pagenumber - 1,
      pageSize: pagesize,
      totalRecords: totalrecords,
      onPageChange: handlePageChange,
      onRowsPerPageChange: handleChangeRowsPerPage,
    }),
    [handleChangeRowsPerPage, handlePageChange, pagenumber, pagesize, totalrecords]
  );

  // const publishedClasses = classSortListData.filter((data) => {
  //   return data.classStatus === 1;
  // });

  // const handleRowAllClick = useCallback(
  //   (event: React.ChangeEvent<HTMLInputElement>) => {
  //     const isChecked = event.target.checked;

  //     // Use functional updates to ensure state is updated based on previous state
  //     setSelectedRows((prev) => {
  //       const newSelectedRows = isChecked
  //         ? classSortListData.reduce((acc, item) => ({ ...acc, [item.classId]: true }), {})
  //         : {};

  //       // Log the new selected rows for debugging
  //       console.log('New selected rows:', newSelectedRows);
  //       console.log('selectedRows  rows:', selectedRows);
  //       return newSelectedRows;
  //     });

  //     // Update edited rows based on selection
  //     setEditedRows(isChecked ? classSortListData : []);
  //   },
  //   [classSortListData, selectedRows]
  // );
  const handleRowAllClick = useCallback(
    (event: ChangeEvent<HTMLInputElement>) => {
      const isChecked = event.target.checked;
      setIsAllSelected(isChecked);
      setSelectedRows(classSortListData.reduce((acc, item) => ({ ...acc, [item.classId]: isChecked }), {}));
      if (isChecked) {
        setEditedRows(classSortListData);
      } else {
        setSelectedRows({});
        setEditedRows([]);
      }
    },
    [classSortListData]
  );

  const handleRowClick = (event: React.ChangeEvent<HTMLInputElement>, row: ClassSortListInfo) => {
    const { checked } = event.target;

    setSelectedRows((prev) => ({
      ...prev,
      [row.classId]: checked,
    }));

    setEditedRows((prev) => {
      if (checked) {
        const existingRow = prev.find((item) => item.classId === row.classId);
        return existingRow ? prev : [...prev, row];
      }
      return prev.filter((item) => item.classId !== row.classId);
    });
  };

  const classListColumns: DataTableColumn<ClassSortListInfo>[] = useMemo(
    () => [
      {
        name: 'checkBox',
        renderHeader: () => {
          return (
            <Checkbox
              disabled={isSubmitting}
              color="primary"
              onChange={handleRowAllClick}
              indeterminate={
                classSortListData.length > 0 && Object.values(selectedRows).some((checked) => checked) && !isAllSelected
              }
              // indeterminate={
              //   Object.keys(selectedRows).length > 0 && Object.keys(selectedRows).length < classSortListData.length
              // }
              checked={isAllSelected}
            />
          );
        },
        renderCell: (row) => {
          const isRowSelected = selectedRows[row.classId] || false;
          return (
            <Checkbox
              disabled={isSubmitting}
              color="primary"
              checked={isRowSelected}
              onChange={(e) => {
                handleRowClick(e, row);
              }}
              inputProps={{ 'aria-labelledby': `checkbox-${row}` }}
            />
          );
        },
      },
      {
        name: 'classId',
        headerLabel: 'Old Id',
        dataKey: 'classId',
        sortable: true,
      },
      {
        name: 'className',
        headerLabel: 'Class Name',
        renderCell: (row) => {
          // const isSelected = selectedRows.some((selectedRow) => selectedRow.classId === row.classId);
          const isRowSelected = selectedRows[row.classId] || false;

          return !isRowSelected ? (
            <Typography variant="subtitle1">{row.className}</Typography>
          ) : (
            <TextField
              disabled={isSubmitting}
              defaultValue={row.className}
              // onChange={(e) => {
              //   const updatedRows = classSortListData.map((item) => {
              //     if (item.classId === row.classId) {
              //       return { ...item, className: e.target.value };
              //     }
              //     return item;
              //   });
              //   setEditedRows(updatedRows);
              // }}
              onChange={(e) => {
                setEditedRows((prev) =>
                  prev.map((item) => (item.classId === row.classId ? { ...item, className: e.target.value } : item))
                );
              }}
              placeholder="Enter class"
            />
          );
        },
        sortable: true,
      },
      {
        name: 'classSection',
        headerLabel: 'Class Section',
        renderCell: (row) => {
          const id = `${row.classId}_${row.sectionId}`;
          // const isSelected = selectedRows.some((selectedRow) => selectedRow.classId === row.classId);
          const isRowSelected = selectedRows[row.classId] || false;
          return !isRowSelected ? (
            <Typography variant="subtitle1">{row.classSection}</Typography>
          ) : (
            <TextField
              disabled={isSubmitting}
              defaultValue={row.classSection}
              // onChange={(e) => {
              //   const updatedRows = classSortListData.map((item) => {
              //     if (item.classId === row.classId) {
              //       return { ...item, classSection: e.target.value };
              //     }
              //     return item;
              //   });
              //   setEditedRows(updatedRows);
              // }}
              onChange={(e) => {
                setEditedRows((prev) =>
                  prev.map((item) => (item.classId === row.classId ? { ...item, classSection: e.target.value } : item))
                );
              }}
              placeholder="Enter section "
            />
          );
        },
      },
      {
        name: 'sortOrder',
        headerLabel: 'Sort Order',
        renderCell: (row) => {
          // const isSelected = selectedRows.some((selectedRow) => selectedRow.classId === row.classId);
          const isRowSelected = selectedRows[row.classId] || false;
          return !isRowSelected ? (
            <Typography variant="subtitle1">{row.sortOrder}</Typography>
          ) : (
            <TextField
              disabled={isSubmitting}
              defaultValue={row.sortOrder}
              // onChange={(e) => {
              //   const updatedRows = classSortListData.map((item) => {
              //     if (item.classId === row.classId) {
              //       return { ...item, sortOrder: e.target.value };
              //     }
              //     return item;
              //   });
              //   setEditedRows(updatedRows);
              // }}
              onChange={(e) => {
                setEditedRows((prev) =>
                  prev.map((item) => (item.classId === row.classId ? { ...item, sortOrder: e.target.value } : item))
                );
              }}
              placeholder="New Id "
            />
          );
        },
      },
    ],
    [selectedRows, isAllSelected, isSubmitting, classSortListData, handleRowAllClick]
  );

  return (
    <Page title="Class Sort">
      <ClassSortRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" pb={1} justifyContent="space-between" alignItems="center" flexWrap="wrap">
            <Stack direction="row" alignItems="center" justifyContent="space-between" flex={10} whiteSpace="nowrap">
              <Typography variant="h6" fontSize={17}>
                Manage Class
              </Typography>
              <Tooltip title="Search">
                <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                  <SearchIcon />
                </IconButton>
              </Tooltip>
            </Stack>
            <Box
              display="flex"
              justifyContent="end"
              alignItems="center"
              columnGap={2}
              sx={{
                flexShrink: 0,
                flex: 1,
                '@media (max-width: 340px)': {
                  flexWrap: 'wrap',
                  rowGap: 1,
                },
              }}
            >
              <LoadingButton
                loading={isSubmitting}
                loadingPosition="start"
                disabled={editedRows.length === 0 || isSubmitting}
                // disabled={
                //   selectedRows.every((selectedRow) => classSortListData.some((row) => row === selectedRow)) ||
                //   isSubmitting
                // }
                variant="contained"
                startIcon={<SaveIcon />}
                size="small"
                sx={{
                  whiteSpace: 'nowrap',
                  width: 'fit-content',
                  '@media (max-width: 340px)': {
                    width: '100%',
                  },
                }}
                onClick={handleUpdateAll}
              >
                {isSubmitting ? 'Updating...' : 'Update All'}
              </LoadingButton>
            </Box>
          </Stack>

          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate onReset={handleReset}>
                <Grid pb={2} pt={1} container spacing={3} alignItems="end">
                  <Grid item xxl={2} xl={2.5} lg={3} md={3} sm={3.3} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Select
                        disabled={isSubmitting}
                        labelId="academicYearFilter"
                        id="academicYearFilterSelect"
                        value={academicYearFilter?.toString()}
                        // defaultValue={YearData[0]?.accademicId}
                        onChange={handleYearChange}
                        placeholder="Select Year"
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Year
                        </MenuItem>
                        {YearData.map((opt) => (
                          <MenuItem key={opt.accademicId} value={opt.accademicId}>
                            {opt.accademicTime}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={3} md={3} sm={3.3} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class Section
                      </Typography>
                      <Select
                        disabled={isSubmitting}
                        labelId="classStatusFilter"
                        id="classStatusFilterSelect"
                        value={classSectionsFilter?.toString()}
                        onChange={handleClassSectionChange}
                      >
                        <MenuItem value={-1}>Select All</MenuItem>
                        {ClassSectionsData.map((opt) => (
                          <MenuItem key={opt.sectionId} value={opt.sectionId}>
                            {opt.sectionName}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={3} md={3} sm={3.3} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Select
                        disabled={classListData.length === 0 || isSubmitting}
                        labelId="classFilter"
                        id="classFilter"
                        value={classFilter?.toString()}
                        onChange={handleClassChange}
                        placeholder="Select Class"
                        sx={{
                          '& .MuiInputBase-input.Mui-disabled': {
                            backgroundColor: theme.palette.grey[200],
                          },
                        }}
                        MenuProps={{
                          PaperProps: {
                            style: {
                              maxHeight: '270px', // Adjust the value to your desired height
                            },
                          },
                        }}
                      >
                        <MenuItem value={-1}>Select All</MenuItem>
                        {classListData.map((opt) => (
                          <MenuItem key={opt.classId} value={opt.classId}>
                            {opt.className}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12} sm={6}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      {/* <Button type="submit" variant="contained" color="primary" fullWidth>
                        Search
                      </Button> */}
                      <Button disabled={isSubmitting} type="reset" variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>
            <Paper className="card-table-container" sx={{ marginTop: '12px' }}>
              <DataTable
                tableStyles={{ minWidth: { xs: '700px', lg: '1200px', xl: '100% ' } }}
                // ShowCheckBox
                // setSelectedRows={setSelectedRows}
                // selectedRows={selectedRows}
                columns={classListColumns}
                data={classSortListData}
                getRowKey={getRowKey}
                fetchStatus={classSortListStatus}
                allowPagination
                allowSorting
                PaginationProps={pageProps}
                sortColumn={sortColumn}
                sortDirection={sortDirection}
                onSort={handleSort}
              />
            </Paper>
          </div>
        </Card>
      </ClassSortRoot>
    </Page>
  );
}

export default ClassSort;
