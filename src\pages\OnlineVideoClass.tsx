import React from 'react';
import { Link, useLocation, useOutletContext } from 'react-router-dom';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';
import styled from 'styled-components';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import Page from '@/components/shared/Page';
import StudentsDeviceDetails from '@/features/OnlineVideoClass/StudentsDeviceDetails';
import StudentBlockUnblock from '@/features/OnlineVideoClass/StudentBlockUnblock';
import StudentBlockList from '@/features/OnlineVideoClass/StudentBlockList';
import StudentBlockReport from '@/features/OnlineVideoClass/StudentBlockReport';
import SubjectChapterDetails from '@/features/OnlineVideoClass/SubjectChapterDetails';
import VideoDetail from '@/features/OnlineVideoClass/VideoDetail/VideoDetail';
import { useDispatch } from 'react-redux';
import { flushStore } from '@/store/flush.slice';
import { OutletContextType } from '@/types/Common';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const OnlineVideoClassRoot = styled.div`
  min-height: calc(100vh - ${TOP_BAR_HEIGHT});

  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};

  .tab {
    background-color: ${(props) => (props.theme.themeMode === 'light' ? '#fff' : props.theme.palette.grey[800])};
    /* @media screen and (min-width: 1414px) {
      width: 100%;
    }
    @media screen and (max-width: 720px) {
    } */
    width: 100%;
  }
  .Mui-selected {
    color: ${(props) => props.theme.palette.primary.main};
  }
`;

function TabPanel({ children, value, index }: TabPanelProps) {
  return (
    <Box
      sx={{ paddingTop: '3rem' }}
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
    >
      {value === index && <Box>{children}</Box>}
    </Box>
  );
}
function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`,
  };
}

function OnlineVideoClass() {
  const { topBarHeight } = useOutletContext<OutletContextType>();
  const location = useLocation();
  const [value, setValue] = React.useState(0);

  const dispatch = useDispatch();

  React.useEffect(() => {
    dispatch(flushStore());
    console.log('Flush Store');
  }, [dispatch, value]);

  React.useEffect(() => {
    const currentPath = location.pathname;
    const routePaths = [
      '/online-video-class/student-device-details',
      '/online-video-class/student-block-unblock',
      '/online-video-class/student-block-list',
      '/online-video-class/student-block-report',
      '/online-video-class/subject-chapter-details',
      '/online-video-class/video-detail',
    ];
    const index = routePaths.indexOf(currentPath);
    if (index !== -1) {
      setValue(index);
    }
  }, [location]);

  return (
    <Page title="">
      <OnlineVideoClassRoot>
        <Tabs
          className="tab"
          variant="scrollable"
          scrollButtons
          allowScrollButtonsMobile
          value={value}
          aria-label="basic tabs"
          sx={{
            ml: '0px',
            borderBottom: 1,
            borderColor: 'divider',
            position: 'fixed',
             top: `${topBarHeight}px`,
            zIndex: 1,
            width: '100%',
          }}
        >
          <Tab
            {...a11yProps(0)}
            label="Student Device Details"
            component={Link}
            to="/online-video-class/student-device-details"
          />
          <Tab
            {...a11yProps(1)}
            label="Student Block/Unblock"
            component={Link}
            to="/online-video-class/student-block-unblock"
          />
          <Tab
            {...a11yProps(2)}
            label="Student Block List"
            component={Link}
            to="/online-video-class/student-block-list"
          />
          <Tab
            {...a11yProps(3)}
            label="Student Block Report"
            component={Link}
            to="/online-video-class/student-block-report"
          />
          <Tab
            {...a11yProps(4)}
            label="Subject Chapter Details"
            component={Link}
            to="/online-video-class/subject-chapter-details"
          />
          <Tab {...a11yProps(5)} label="Video Detail" component={Link} to="/online-video-class/video-detail" />
        </Tabs>
        <TabPanel value={value} index={0}>
          <StudentsDeviceDetails />
        </TabPanel>
        <TabPanel value={value} index={1}>
          <StudentBlockUnblock />
        </TabPanel>
        <TabPanel value={value} index={2}>
          <StudentBlockList />
        </TabPanel>
        <TabPanel value={value} index={3}>
          <StudentBlockReport />
        </TabPanel>
        <TabPanel value={value} index={4}>
          <SubjectChapterDetails />
        </TabPanel>
        <TabPanel value={value} index={5}>
          <VideoDetail />
        </TabPanel>
      </OnlineVideoClassRoot>
    </Page>
  );
}

export default OnlineVideoClass;
