/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  useTheme,
  IconButton,
  Collapse,
  Tooltip,
  FormControl,
  Chip,
  Snackbar,
  Alert,
} from '@mui/material';
import styled from 'styled-components';
import AccessTimeRoundedIcon from '@mui/icons-material/AccessTimeRounded';
import CheckCircleOutlineRoundedIcon from '@mui/icons-material/CheckCircleOutlineRounded';
import HelpOutlineRoundedIcon from '@mui/icons-material/HelpOutlineRounded';
import PauseCircleOutlineRoundedIcon from '@mui/icons-material/PauseCircleOutlineRounded';
import AlarmRoundedIcon from '@mui/icons-material/AlarmRounded';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import SearchIcon from '@mui/icons-material/Search';
import Popup from '@/components/shared/Popup/Popup';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import empty from '@/Parent-Side/assets/descriptiveExam/emptystate.png';
import { MdAdd, MdContentCopy } from 'react-icons/md';
import { CloseIcon } from '@/theme/overrides/CustomIcons';
import { Student, StudentInfoData } from '@/config/StudentDetails';
import useSettings from '@/hooks/useSettings';
import AutorenewOutlinedIcon from '@mui/icons-material/AutorenewOutlined';
import { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import DateSelect from '@/components/shared/Selections/DateSelect';
import MenuEditDelete from '@/components/shared/Selections/MenuEditDelete';
import CreateScheduleList from '@/features/LiveClass/ScheduleList/CreateScheduleList';
import InsertDriveFileIcon from '@mui/icons-material/InsertDriveFile';
import View from '../StudyMaterials/View';
import BackButton from '@/components/shared/BackButton';
import Exam from './Exam';
import ViewResult from './ViewResult';

const DetailsExamRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  .student_card {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  }

  .Card {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    min-height: calc(100vh - 110px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 40px);
      flex-grow: 1;

      .card-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: auto;
        ::-webkit-scrollbar {
          width: 0px;
        }

        .MuiTableContainer-root {
          max-height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

function DetailsExam({ onBackClick }: any) {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const [changeView, setChangeView] = useState<'DetailsPage' | 'ExamPage' | 'ViewResult'>('DetailsPage');
  const [showDetails, setShowDetails] = useState<'DetailsPage' | ''>('');
  const [resultView, setResultView] = useState<boolean>(false);
  const [showFilter, setShowFilter] = useState(false);
  const [Delete, setDelete] = useState(false);
  const [createPopup, setCreatePopup] = useState(false);

  const [students, setStudents] = useState(StudentInfoData);
  const [open, setOpen] = useState(false);
  const [openNew, setOpenNew] = useState(false);
  const [showNewMultiple, setShowNewMultiple] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState(null);

  const handleToggle = () => {
    setChangeView((prevChangeView) => !prevChangeView);
  };

  const handleClickDelete = () => setDelete(true);
  const handleClickCloseDelete = () => setDelete(false);
  const handlePageChange = useCallback((event: unknown, newPage: number) => {
    // loadClassList({ ...currentClassListRequest, pageNumber: newPage + 1 });
  }, []);

  const handleChangeRowsPerPage = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const newPageSize = +event.target.value;
    // loadClassList({ ...currentClassListRequest, pageNumber: 1, pageSize: newPageSize });
  }, []);

  const pageProps = useMemo(
    () => ({
      rowsPerPageOptions: [10, 20, 30],
      pageNumber: 1,
      pageSize: 10,
      totalRecords: 100,
      onPageChange: handlePageChange,
      onRowsPerPageChange: handleChangeRowsPerPage,
    }),
    [handleChangeRowsPerPage, handlePageChange]
  );

  const handleClose = () => {
    setOpen(false);
  };
  const handleOpenNew = () => {
    setOpenNew(true);
  };

  const handleCloseNew = () => {
    setOpenNew(false);
  };
  const handleToggleNewMultiple = () => {
    setShowNewMultiple((prevState) => !prevState);
  };

  const handleUpdate = (id: number, updatedData: Student) => {
    const updatedStudents = students.map((student) => (student.id === id ? { ...student, ...updatedData } : student));
    setStudents(updatedStudents);
    setOpen(false);
  };

  const getRowKey = useCallback((row: any) => row.examId, []);

  const [isSnackbarOpen, setIsSnackbarOpen] = useState(false);

  const handleCopyClick = (text: string) => {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        setIsSnackbarOpen(true);
      })
      .catch((err) => {
        console.error('Unable to copy link to clipboard', err);
      });
  };

  const handleCloseSnackbar = () => {
    setIsSnackbarOpen(false);
  };

  return changeView === 'DetailsPage' ? (
    <Page title="Schedule List">
      <DetailsExamRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 1, md: 2 } }}>
          <BackButton
            onBackClick={onBackClick}
            title={
              <div>
                <span style={{ color: 'grey' }}>Descriptive Exam</span>
                <ChevronRightIcon fontSize="small" />
                Details
              </div>
            }
          />
          {showDetails === '' ? (
            <div className="card-main-body">
              <Box
                width="100%"
                height="100%"
                className="card-container"
                display="flex"
                justifyContent="center"
                alignItems="center"
                gap={5}
              >
                <Stack>
                  <img width={200} src={empty} alt="" />
                </Stack>
                <Typography variant="subtitle1" color="secondary">
                  Take Your Test
                </Typography>
                <Button onClick={() => setShowDetails('DetailsPage')} variant="contained" color="success">
                  Take Test
                </Button>
              </Box>
            </div>
          ) : (
            <>
              <Box mt={2}>
                <Stack>
                  <Typography variant="h6" fontSize={18}>
                    Half Yearly Examination
                  </Typography>
                  <Typography variant="body2" fontSize={16} color="secondary">
                    Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum is simply
                    dummy text of the printing and typesetting industry. Lorem Ipsum is simply dummy text of the
                    printing and typesetting industry. Lorem Ipsum is simply dummy text of the printing and typesetting
                    industry. Lorem Ipsum is simply dummy text of the printing and typesetting industry.
                  </Typography>
                </Stack>
                <Grid container mt={5} rowGap={5}>
                  <Grid item lg={4} md={6} sm={6} xs={12}>
                    <Box display="flex" gap={2} alignItems="start">
                      <Stack
                        p={0.5}
                        display="flex"
                        alignItems="center"
                        justifyContent="center"
                        sx={{ backgroundColor: '#ffe8ee', borderRadius: 5 }}
                      >
                        <AccessTimeRoundedIcon color="error" sx={{ fontSize: '25px' }} />
                      </Stack>
                      <Stack>
                        <Typography variant="subtitle2" fontSize={16}>
                          Durations
                        </Typography>
                        <Typography variant="body1" fontSize={13} color="secondary">
                          5 Minutes
                        </Typography>
                      </Stack>
                    </Box>
                  </Grid>
                  <Grid item lg={4} md={6} sm={6} xs={12}>
                    <Box display="flex" gap={2} alignItems="start">
                      <Stack
                        p={0.5}
                        display="flex"
                        alignItems="center"
                        justifyContent="center"
                        sx={{ backgroundColor: '#f0f9e8', borderRadius: 5 }}
                      >
                        <CheckCircleOutlineRoundedIcon sx={{ fontSize: '25px', color: '#73bf2b' }} />
                      </Stack>
                      <Stack>
                        <Typography variant="subtitle2" fontSize={16}>
                          Total Mark
                        </Typography>
                        <Typography variant="body1" fontSize={13} color="secondary">
                          10 Marks
                        </Typography>
                      </Stack>
                    </Box>
                  </Grid>
                  <Grid item lg={4} md={6} sm={6} xs={12}>
                    <Box display="flex" gap={2} alignItems="start">
                      <Stack
                        p={0.5}
                        display="flex"
                        alignItems="center"
                        justifyContent="center"
                        sx={{ backgroundColor: '#e7f8ee', borderRadius: 5 }}
                      >
                        <HelpOutlineRoundedIcon sx={{ fontSize: '25px', color: '#37a76c' }} />
                      </Stack>
                      <Stack>
                        <Typography variant="subtitle2" fontSize={16}>
                          Total Questions
                        </Typography>
                        <Typography variant="body1" fontSize={13} color="secondary">
                          5 Questions
                        </Typography>
                      </Stack>
                    </Box>
                  </Grid>
                  <Grid item lg={4} md={6} sm={6} xs={12}>
                    <Box display="flex" gap={2} alignItems="start">
                      <Stack
                        p={0.5}
                        display="flex"
                        alignItems="center"
                        justifyContent="center"
                        sx={{ backgroundColor: '#f3eafb', borderRadius: 5 }}
                      >
                        <PauseCircleOutlineRoundedIcon sx={{ fontSize: '25px', color: '#875ebe' }} />
                      </Stack>
                      <Stack>
                        <Typography variant="subtitle2" fontSize={16}>
                          Pause Allowed
                        </Typography>
                        <Typography variant="body1" fontSize={13} color="secondary">
                          05 Times
                        </Typography>
                      </Stack>
                    </Box>
                  </Grid>
                  <Grid item lg={4} md={6} sm={6} xs={12}>
                    <Box display="flex" gap={2} alignItems="start">
                      <Stack
                        p={0.5}
                        display="flex"
                        alignItems="center"
                        justifyContent="center"
                        sx={{ backgroundColor: '#fef4ea', borderRadius: 5 }}
                      >
                        <AlarmRoundedIcon sx={{ fontSize: '25px', color: '#fe9c43' }} />
                      </Stack>
                      <Stack>
                        <Typography variant="subtitle2" fontSize={16}>
                          Start Date
                        </Typography>
                        <Typography variant="body1" fontSize={13} color="secondary">
                          02 Feb 24, 2024, 10:00 AM
                        </Typography>
                      </Stack>
                    </Box>
                  </Grid>
                  <Grid item lg={4} md={6} sm={6} xs={12}>
                    <Box display="flex" gap={2} alignItems="start">
                      <Stack
                        p={0.5}
                        display="flex"
                        alignItems="center"
                        justifyContent="center"
                        sx={{ backgroundColor: theme.palette.error.lighter, borderRadius: 5 }}
                      >
                        <AlarmRoundedIcon color="error" sx={{ fontSize: '25px' }} />
                      </Stack>
                      <Stack>
                        <Typography variant="subtitle2" fontSize={16}>
                          End Date
                        </Typography>
                        <Typography variant="body1" fontSize={13} color="secondary">
                          02 Feb 24, 2024, 10:00 AM
                        </Typography>
                      </Stack>
                    </Box>
                  </Grid>
                </Grid>
                <Box
                  p={3}
                  borderRadius={1}
                  display="flex"
                  // flexWrap="wrap"
                  mt={5}
                  bgcolor={isLight ? theme.palette.grey[200] : theme.palette.grey[900]}
                >
                  <Grid container columnSpacing={30} rowGap={3}>
                    <Grid item lg={6} xs={12}>
                      <Stack direction="row" alignItems="center" justifyContent="space-between">
                        <Typography variant="subtitle1" color="secondary">
                          Attempted
                        </Typography>
                        <Typography variant="subtitle2" color="error">
                          Not Yet
                        </Typography>
                      </Stack>
                    </Grid>
                    <Grid item lg={6} xs={12} >
                      <Stack direction="row" alignItems="center" justifyContent="space-between">
                        <Typography variant="subtitle1" color="secondary">
                          Answered Question
                        </Typography>
                        <Typography variant="subtitle2">0</Typography>
                      </Stack>
                    </Grid>
                    <Grid item lg={6} xs={12} >
                      <Stack direction="row" alignItems="center" justifyContent="space-between">
                        <Typography variant="subtitle1" color="secondary">
                          Pause Count
                        </Typography>
                        <Typography variant="subtitle2">0</Typography>
                      </Stack>
                    </Grid>
                    <Grid item lg={6} xs={12} >
                      <Stack direction="row" alignItems="center" justifyContent="space-between">
                        <Typography variant="subtitle1" color="secondary">
                          Status
                        </Typography>
                        <Typography variant="subtitle2">On Progress</Typography>
                      </Stack>
                    </Grid>
                  </Grid>
                </Box>
              </Box>
              <Stack mt={15} direction="row" justifyContent="center">
                <Button
                  onClick={() => (resultView ? setChangeView('ViewResult') : setChangeView('ExamPage'))}
                  variant="contained"
                  color="success"
                >
                  {resultView ? 'View Result' : 'Continue'}
                </Button>
              </Stack>
            </>
          )}
        </Card>
      </DetailsExamRoot>
      <Popup
        size="xs"
        state={Delete}
        onClose={handleClickCloseDelete}
        popupContent={<DeleteMessage message="Are you sure want to delete?" />}
      />
      <Popup
        size="sm"
        title="Assignment Details"
        state={createPopup}
        onClose={() => setCreatePopup(false)}
        popupContent={<View />}
      />
    </Page> 
  ) : changeView === 'ViewResult' ? (
    <ViewResult resultView={resultView} onBackClick={() => setChangeView('DetailsPage')}/>
  ) : (
    <Exam
      onBackClick={() => setChangeView('DetailsPage')}
      FinishExam={() => setResultView(true)}
      resultView={resultView}
    />
  );
}

export default DetailsExam;
