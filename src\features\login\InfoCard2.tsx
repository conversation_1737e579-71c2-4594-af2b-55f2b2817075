import Anchor from '@/components/Dashboard/Anchor';
import { Card, Typography } from '@mui/material';
import styled from 'styled-components';

const InfoCardRoot = styled(Card)`
  padding: 0.9375rem;
  height: 180px;

  .content {
    display: flex;
    flex-direction: column;

    a {
      align-self: flex-end;
      text-decoration: none;
    }
  }
`;

export type InfoCardProps = {
  content: string;
  url: string;
};

function InfoCard2({ content, url }: InfoCardProps) {
  return (
    <InfoCardRoot className="infoCard" elevation={0}>
      <div className="content">
        <Typography variant="body2">Our MISSION:</Typography>
        <Typography variant="body2">
          To create an educational world in which children of all types and from all corners of the society have an
          opportunity for Qualitative education .
        </Typography>
        <Anchor href={url}>View more</Anchor>
      </div>
    </InfoCardRoot>
  );
}

export default InfoCard2;
