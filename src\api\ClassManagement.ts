import ApiUrls from '@/config/ApiUrls';
import {
  ClassCreateRequest,
  ClassCreateRow,
  ClassListInfo,
  ClassListPagedData,
  ClassListRequest,
  ClassSortListInfo,
  ClassSortListPagedData,
  ClassSortListRequest,
} from '@/types/AcademicManagement';
import { CreateResponse, CreateResponseMulti, DeleteResponse, UpdateResponse } from '@/types/Common';
import { privateApi } from '@/api/base/api';
import { APIResponse } from '@/api/base/types';

async function GetClassList(request: ClassListRequest): Promise<APIResponse<ClassListPagedData>> {
  const response = await privateApi.post<ClassListPagedData>(ApiUrls.GetClassList, request);
  return response;
}
// async function ClassList(request: ClassListRequest): Promise<APIResponse<ClassListPagedData>> {
//   const response = await privateApi.post<ClassListPagedData>(ApiUrls.ClassList, request);
//   return response;
// }

async function ClassList(adminId: number | undefined): Promise<APIResponse<ClassListPagedData[]>> {
  if (adminId === undefined) {
    throw new Error('adminId is required');
  }

  const url = ApiUrls.ClassList.replace('{adminId}', adminId.toString());
  const response = await privateApi.get<any>(url);
  return response;
}

async function AddNewClass(request: ClassCreateRequest): Promise<APIResponse<CreateResponse>> {
  const response = await privateApi.post<CreateResponse>(ApiUrls.AddEditClass, request);
  return response;
}

async function AddNewClasses(request: ClassCreateRow[]): Promise<APIResponse<CreateResponseMulti>> {
  const response = await privateApi.post<CreateResponseMulti>(ApiUrls.AddClasses, request);
  return response;
}

async function UpdateClass(request: ClassListInfo): Promise<APIResponse<UpdateResponse>> {
  const response = await privateApi.post<UpdateResponse>(ApiUrls.AddEditClass, request);
  return response;
}

async function DeleteClass(classId: number): Promise<APIResponse<DeleteResponse>> {
  const url = ApiUrls.DeleteClass.replace('{classId}', classId.toString());
  const response = await privateApi.get<DeleteResponse>(url);
  return response;
}

async function ClassNameExists(className: string): Promise<APIResponse<boolean>> {
  const url = `${ApiUrls.ClassNameExists}?q=${className}`;
  const response = await privateApi.get<boolean>(url);
  return response;
}

async function GetClassSortList(request: ClassSortListRequest): Promise<APIResponse<ClassSortListPagedData>> {
  const response = await privateApi.post<ClassSortListPagedData>(ApiUrls.GetClassSortList, request);
  return response;
}

async function UpdateClassSort(request: ClassSortListInfo): Promise<APIResponse<UpdateResponse>> {
  const response = await privateApi.post<UpdateResponse>(ApiUrls.UpdateClassSort, request);
  return response;
}

const methods = {
  GetClassList,
  ClassList,
  AddNewClass,
  AddNewClasses,
  UpdateClass,
  DeleteClass,
  ClassNameExists,
  GetClassSortList,
  UpdateClassSort,
};

export default methods;
