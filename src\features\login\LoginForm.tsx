import PasswordField from '@/components/shared/Selections/PasswordField';
import { breakPointsMinwidth } from '@/config/breakpoints';
import useAuth from '@/hooks/useAuth';
import { LoginRequest } from '@/types/Auth';
import Button from '@mui/lab/LoadingButton';
import { Alert, Card, TextField, Typography, useTheme } from '@mui/material';
import styled from 'styled-components';
import { useFormik } from 'formik';
import * as yup from 'yup';
import { useSchool } from '@/contexts/SchoolContext';
import passdailyLogo from '@/assets/SchoolLogos/logo-small.svg';
import holyLogo from '@/assets/SchoolLogos/HolyLogo.jpeg';
import carmelLogo from '@/assets/SchoolLogos/CarmelLogo.png';
import thereseLogo from '@/assets/SchoolLogos/StthereseLogo.png';
import thomasLogo from '@/assets/SchoolLogos/StThomasLogo.png';
import nirmalaLogo from '@/assets/SchoolLogos/NirmalaLogo.png';
import MIMLogo from '@/assets/SchoolLogos/MIMLogo.png';
import alFitrahLogo from '@/assets/SchoolLogos/alFitrahLogo.png';

const LoginFormRoot = styled(Card)`
  padding: 0.9375rem;
  @media ${breakPointsMinwidth.lg} {
    padding: 1.875rem;
  }
  .logo {
    display: flex;
    justify-content: center;
  }
  .logo img {
    /* width: 50px; */
  }
  .title {
    font-size: 1.7rem;
    font-family: 'Poppins Medium';
    font-weight: 500;
    text-align: center;
    letter-spacing: 0px;
  }

  .subtitle {
    font-size: 1.6rem;
    font-family: 'Poppins Medium';
    text-align: center;
    letter-spacing: 0px;
  }

  .greet {
    text-align: center;
    p {
      font-size: 0.75rem;
      font-family: 'Poppins Regular';
      font-weight: 400;
      color: ${(props) => props.theme.palette.secondary.main};
    }
  }
  /* .MuiFormHelperText-root.Mui-error {
    color: blue;
  } */
`;

const validationSchema = yup.object({
  username: yup.string().required('Enter username'),
  password: yup.string().required('Password is required'),
});

function LoginForm() {
  const theme = useTheme();
  const { selectedSchool } = useSchool();
  const { loading, login, error, setParentMode } = useAuth();
  const {
    values: { username, password },
    handleSubmit,
    handleChange,
    touched,
    errors,
  } = useFormik<LoginRequest>({
    initialValues: {
      username: '',
      password: '',
    },
    validationSchema,
    onSubmit: (values) => {
      login(values);
      setParentMode(false);
    },
  });

  return (
    <LoginFormRoot className="loginCard" elevation={0}>
      <div className="logo">
        <img
          width={selectedSchool?.schoolId === '8' ? 80 : 50}
          src={selectedSchool?.schoolLogo || passdailyLogo}
          alt={selectedSchool?.schoolLogo || passdailyLogo}
        />
        {/* <img width={50} src={holyLogo} alt="holyLogo" /> */}
        {/* <img width={50} src={carmelLogo} alt="carmelLogo" /> */}
        {/* <img width={50} src={thereseLogo} alt="thereseLogo" /> */}
        {/* <img width={50} src={thomasLogo} alt="thomasLogo" /> */}
        {/* <img width={50} src={nirmalaLogo} alt="nirmalaLogo" /> */}
        {/* <img width={100} src={MIMLogo} alt="MIMLogo" /> */}
      </div>
      <Typography variant="h3" className="title mb-3">
        Welcome to {selectedSchool?.schoolName || 'Passdaily'}
        {/* Welcome to Holy Angels School */}
        {/* Welcome to Carmel Convent School */}
        {/* Welcome to St.Therese Convent School */}
        {/* Welcome St.Thomas */}
        {/* Welcome to Nirmal Convent School Pune */}
        {/* Welcome to MIM High School */}
      </Typography>
      <div className="greet mb-3">
        <Typography variant="body2">
          ERP by <b>Passdaily</b>
        </Typography>
        <Typography variant="body2">empowered by </Typography>
        <Typography variant="subtitle2">Mindgoal solutions</Typography>
      </div>
      <Typography variant="h3" className="subtitle mb-3">
        Sign In
      </Typography>
      <form noValidate onSubmit={handleSubmit}>
        {!!error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}
        <div className="w-100 mb-3">
          <TextField
            id="username"
            name="username"
            variant="outlined"
            color="primary"
            placeholder="Enter username"
            fullWidth
            autoComplete="off"
            value={username}
            onChange={handleChange}
            error={touched.username && Boolean(errors.username)}
            helperText={touched.username && errors.username && <span style={{ color: 'red' }}>{errors.username}</span>}
          />
        </div>
        <div className="w-100 mb-4">
          <PasswordField
            id="password"
            name="password"
            color="primary"
            placeholder="Enter password"
            passwordViewable
            fullWidth
            value={password}
            onChange={handleChange}
            error={touched.password && Boolean(errors.password)}
            helperText={touched.password && errors.password}
          />
        </div>
        <div className="w-100 mb-3">
          <Button
            loading={loading}
            loadingPosition="start"
            startIcon={<span />}
            fullWidth
            type="submit"
            variant="contained"
            color="primary"
          >
            {loading ? 'Signing in...' : 'Login'}
          </Button>
        </div>
        <div className="w-100">
          <Button fullWidth type="button" variant="contained" color="secondary">
            Create Parent Account
          </Button>
        </div>
      </form>
    </LoginFormRoot>
  );
}

export default LoginForm;
