import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import Loadable from '@/components/shared/Loadable';

const AdminControls = Loadable(lazy(() => import('@/pages/AdminControls')));
const ManageUpdates = Loadable(lazy(() => import('@/features/AdminControls/ManageUpdates')));
const StaffLoginList = Loadable(lazy(() => import('@/features/AdminControls/StaffLogin/StaffLoginList')));
const ParentLogin = Loadable(lazy(() => import('@/features/AdminControls/ParentLogin')));
const AppDetailsList = Loadable(lazy(() => import('@/features/AdminControls/AppDetailsList')));
const AppUpdateMessage = Loadable(lazy(() => import('@/features/AdminControls/AppUpdateMessage')));
const MailTemplate = Loadable(lazy(() => import('@/features/AdminControls/MailTemplate/MailTemplate')));

export const adminControlsRoutes: RouteObject[] = [
  {
    path: '/admin-controls',
    element: <AdminControls />,
    children: [
      {
        path: 'send-updates',
        element: <ManageUpdates />,
      },
      {
        path: 'staff-login',
        element: <StaffLoginList />,
      },
      {
        path: 'parent-login',
        element: <ParentLogin />,
      },
      {
        path: 'app-details',
        element: <AppDetailsList />,
      },
      {
        path: 'app-update-message',
        element: <AppUpdateMessage />,
      },
      {
        path: 'mail-template',
        element: <MailTemplate />,
      },
    ],
  },
];
