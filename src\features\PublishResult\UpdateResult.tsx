/* eslint-disable prettier/prettier */
/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Autocomplete,
  Divider,
  Grid,
  Stack,
  TextField,
  Button,
  Box,
  Typography,
  Card,
  Radio,
  TableContainer,
} from '@mui/material';
import styled from 'styled-components';
import { CLASS_SELECT, YEAR_SELECT } from '@/config/Selection';
import Popup from '@/components/shared/Popup/Popup';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import { StudentInfoData } from '@/config/StudentDetails';
import PTA from '@/components/shared/MessageTempletes/PTA';
import Conveyors from '@/components/shared/MessageTempletes/Conveyors';
import Parents from '@/components/shared/MessageTempletes/Parents';
import Staffs from '@/components/shared/MessageTempletes/Staffs';
import GroupWise from '@/components/shared/MessageTempletes/GroupWise';
import PublicGroups from '@/components/shared/MessageTempletes/PublicGroups';
import ClassWise from '@/components/shared/MessageTempletes/ClassWise';
import ClassDivision from '@/components/shared/MessageTempletes/ClassDivision';
import Groups from '@/components/shared/MessageTempletes/Groups';
import PublicGroupWise from '@/components/shared/MessageTempletes/PublicGroupWise';

const UpdateResultRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  padding: 1rem;

  h6 {
    font-weight: 600;
  }
  .icon {
    margin-right: 10px;
  }
  .Card-Top {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  /* .Card {
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }
  } */
  .ListCard {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.grey[100] : props.theme.palette.grey[900]};
    border: 0px solid
      ${(props) => (props.theme.themeMode === 'light' ? props.theme.palette.grey[400] : props.theme.palette.grey[900])};
  }
  .radio {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.primary.lighter : props.theme.palette.grey[800]};
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
  }
  .subtitile {
    font-family: 'Poppins semibold';
    font-weight: 500;
  }
`;

export const sendbuttons = [
  { name: 'PTA', component: <PTA /> },
  { name: 'Conveyors', component: <Conveyors /> },
  { name: 'Groups', component: <Groups /> },
  { name: 'Parents', component: <Parents /> },
  { name: 'Staffs', component: <Staffs /> },
  { name: 'Group Wise', component: <GroupWise /> },
  { name: 'Public Group', component: <PublicGroups /> },
  { name: 'Public Group Wise', component: <PublicGroupWise /> },
  { name: 'Class Wise', component: <ClassWise /> },
  { name: 'Class Division', component: <ClassDivision /> },
];

export const sendbuttons2 = [
  { name: 'All Parents' },
  { name: 'All Staffs' },
  { name: 'All PTA' },
  { name: 'All Conveyors' },
  { name: ' Staff App' },
];

function UpdateResult() {
  const [Delete, setDelete] = React.useState(false);
  const [popupSuccess, setPopupSuccess] = React.useState(false);

  const handleClickCloseDelete = () => setDelete(false);

  const handleOpenPopup = () => setPopupSuccess(true);
  const handleClosePopup = () => setPopupSuccess(false);

  const [selectedOption, setSelectedOption] = React.useState<string[]>([]);

  const handleOptionChange = (event: any, rollNo: number) => {
    setSelectedOption((prevState) => {
      const updatedState = [...prevState];
      updatedState[rollNo] = event.target.value;
      return updatedState;
    });
  };

  return (
    <Page title="Update Result">
      <UpdateResultRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Typography variant="h6" fontSize={17}>
            Update Result
          </Typography>
          <Divider />
          <Grid pb={4} container spacing={3} pt={2}>
            <Grid item lg={4} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Select Year
              </Typography>
              <Autocomplete
                options={YEAR_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select" />}
              />
            </Grid>
            <Grid item lg={4} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Select Class
              </Typography>
              <Autocomplete
                options={CLASS_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select" />}
              />
            </Grid>
            <Grid item lg={4} xs={12}>
              <Stack spacing={2} sx={{ pt: { xs: 0, md: 3.79 } }} direction="row">
                <Button variant="contained" color="secondary" fullWidth>
                  Reset
                </Button>
                <Button variant="contained" color="primary" fullWidth>
                  Search
                </Button>
              </Stack>
            </Grid>
          </Grid>

          <TableContainer sx={{ height: 'calc(100vh - 320px)' }}>
            <Grid container spacing={4}>
              {StudentInfoData.map((card) => (
                <Grid item xl={6} lg={6} md={12} sm={6} xs={12} key={card.rollNo}>
                  <Card className="ListCard" sx={{ p: 2.5, boxShadow: 0 }}>
                    <Box className="Card-Top">
                      <Stack direction="column" spacing={1} width="100%">
                        <Typography variant="h6" fontSize={16}>
                          Student Name : {card.name}
                        </Typography>
                        <Stack direction="row" spacing={5} alignItems="center" sx={{ ml: { xs: 1.5 }, mr: 1 }}>
                          <Typography fontSize={13} color="secondary" className="subtitile">
                            Academic Year : {card.academic}
                          </Typography>
                          <Typography fontSize={13} color="secondary" className="subtitile">
                            Class : {card.className}
                          </Typography>
                        </Stack>
                        <Typography variant="h6" fontSize={13} color="secondary">
                          Result Status :
                        </Typography>
                        <Stack direction="row" spacing={1} alignItems="center" sx={{ ml: { xs: 1.5 } }}>
                          <Typography className="radio" pr={2}>
                            <Radio
                              value="pass"
                              size="small"
                              checked={selectedOption[card.rollNo] === 'pass'}
                              onChange={(event) => handleOptionChange(event, card.rollNo)}
                            />
                            Pass
                          </Typography>
                          <Typography className="radio" pr={2}>
                            <Radio
                              size="small"
                              color="info"
                              value="withheld"
                              checked={selectedOption[card.rollNo] === 'withheld'}
                              onChange={(event) => handleOptionChange(event, card.rollNo)}
                            />
                            WithHeld
                          </Typography>

                          <Typography className="radio" pr={2}>
                            <Radio
                              size="small"
                              color="secondary"
                              value="noResult"
                              checked={selectedOption[card.rollNo] === 'noResult'}
                              onChange={(event) => handleOptionChange(event, card.rollNo)}
                            />
                            No Result
                          </Typography>
                        </Stack>
                        <TextField placeholder="Remarks" fullWidth />
                      </Stack>
                    </Box>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </TableContainer>
          <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 3 }}>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="secondary">
                Cancel
              </Button>
              <Button onClick={handleOpenPopup} variant="contained" color="primary">
                Update
              </Button>
            </Stack>
          </Box>
        </Card>
      </UpdateResultRoot>
      <Popup
        size="xs"
        state={Delete}
        onClose={handleClickCloseDelete}
        popupContent={<DeleteMessage message="Are you sure want to delete?" />}
      />
      <Popup
        size="xs"
        state={popupSuccess}
        onClose={handleClosePopup}
        popupContent={<SuccessMessage message="Result Updated Successfully" />}
      />
    </Page>
  );
}

export default UpdateResult;
