/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import {
  Autocomplete,
  Grid,
  Stack,
  TextField,
  Button,
  Typography,
  Box,
  FormControl,
  Select,
  MenuItem,
  RadioGroup,
  FormControlLabel,
  Radio,
} from '@mui/material';
import styled from 'styled-components';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import { CLASS_SELECT, STATUS_OPTIONS, SUBJECT_SELECT, YEAR_SELECT } from '@/config/Selection';
import SelectDateTimePicker from '@/components/shared/Selections/TimePicker';
import InputFileUpload from '@/components/shared/Selections/FilesUpload';
import DateSelect from '@/components/shared/Selections/DateSelect';

const CreateAssignmentRoot = styled.div`
  width: 100%;
  padding: 1.5rem;
`;

function CreateAssignment({ onClose }: any) {
  return (
    <CreateAssignmentRoot>
      <form noValidate>
        <Grid container spacing={3} direction="row" justifyContent="space-between">
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Academic Year
              </Typography>
              <Autocomplete
                options={YEAR_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select year" />}
              />
            </FormControl>
          </Grid>
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Class
              </Typography>
              <Autocomplete
                options={CLASS_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select class" />}
              />
            </FormControl>
          </Grid>
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Subject
              </Typography>
              <Autocomplete
                options={SUBJECT_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select class" />}
              />
            </FormControl>
          </Grid>
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Start Date
              </Typography>
              <DateSelect />
            </FormControl>
          </Grid>
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Assignment Name
              </Typography>
              <TextField placeholder="Enter Year " name="" />
            </FormControl>
          </Grid>
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                End Date
              </Typography>
              <DateSelect />
            </FormControl>
          </Grid>
          <Grid item md={12} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Assignment Description
              </Typography>
              <TextField
                multiline
                fullWidth
                minRows={2}
                InputProps={{ inputProps: { style: { resize: 'vertical' } } }}
                placeholder="Enter description..."
                name="classDescription"
                value=""
              />
            </FormControl>
          </Grid>
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Out Off Mark
              </Typography>
              <TextField placeholder="Enter Meeting Title " name="" />
            </FormControl>
          </Grid>
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Status
              </Typography>
              <Select
                name=""
                value=""
                //   error={touched.classStatus && !!errors.classStatus}
              >
                {STATUS_OPTIONS.map((opt) => (
                  <MenuItem key={opt.id} value={opt.id}>
                    {opt.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Upload File
              </Typography>
              <InputFileUpload />
            </FormControl>
          </Grid>
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="h6" fontSize={14} mb={1}>
                Send Notification
              </Typography>
              <RadioGroup
                defaultValue="yes"
                row
                aria-labelledby="demo-row-radio-buttons-group-label"
                name="row-radio-buttons-group"
              >
                <FormControlLabel value="yes" control={<Radio checkedIcon={<CheckCircleIcon />} />} label="Yes" />
                <FormControlLabel value="no" control={<Radio checkedIcon={<CheckCircleIcon />} />} label="No" />
              </RadioGroup>
            </FormControl>
          </Grid>
        </Grid>
        <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 3 }}>
          <Stack spacing={2} direction="row">
            <Button variant="contained" color="secondary">
              Cancel
            </Button>
            <Button variant="contained" color="primary">
              Create
            </Button>
          </Stack>
        </Box>
      </form>
    </CreateAssignmentRoot>
  );
}

export default CreateAssignment;
