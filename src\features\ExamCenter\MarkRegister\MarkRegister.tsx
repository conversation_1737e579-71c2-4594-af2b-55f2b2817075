/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  Collapse,
  FormControl,
  Tooltip,
  Select,
  MenuItem,
  SelectChangeEvent,
  Avatar,
} from '@mui/material';
import styled from 'styled-components';
import SearchIcon from '@mui/icons-material/Search';

import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { useTheme } from '@mui/material/styles';
import useSettings from '@/hooks/useSettings';
import Popup from '@/components/shared/Popup/Popup';
import IndivitualEnroll from '@/features/ExamCenter/MarkRegister/IndivitualEnroll';
import useAuth from '@/hooks/useAuth';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useAppSelector } from '@/hooks/useAppSelector';
import {
  getMarkRegisterData,
  getMarkRegisterDataStatus,
  getMarkRegisterFiltersData,
  getMarkRegisterFiltersStatus,
  getMarkRegisterSubjectFiltersData,
  getMarkRegisterSubjectFiltersStatus,
} from '@/config/storeSelectors';
import {
  addMarkRegisterCBSE,
  fetchMarkRegisterDetails,
  fetchMarkRegisterFilter,
  fetchMarkRegisterSubjectFilter,
} from '@/store/ExamCenter/examCenter.thunks';
import NoData from '@/assets/no-datas.png';
import { MarkRegisterDataType } from '@/types/ExamCenter';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import successIcon from '@/assets/MessageIcons/success.json';
import errorIcon from '@/assets/MessageIcons/error-message.json';
import { useConfirm } from '@/components/shared/Popup/Confirmation';

const MarkRegisterRoot = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      height: calc(100% - 100px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

function MarkRegister() {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const { user } = useAuth();
  const dispatch = useAppDispatch();
  const { confirm } = useConfirm();
  const adminId: number = user ? user.accountId : 0;
  const [showFilter, setShowFilter] = useState(true);
  const [popup, setPopup] = useState(false);
  const MarkRegisterFilters = useAppSelector(getMarkRegisterFiltersData);
  const MarkRegisterFilterStatus = useAppSelector(getMarkRegisterFiltersStatus);
  const MarkRegisterFilterSubject = useAppSelector(getMarkRegisterSubjectFiltersData);
  const MarkRegisterFilterSubjectStatus = useAppSelector(getMarkRegisterSubjectFiltersStatus);
  const MarkRegisterData = useAppSelector(getMarkRegisterData);
  const MarkRegisterDataStatus = useAppSelector(getMarkRegisterDataStatus);

  const [academicYearFilter, setAcademicYearFilter] = useState(-2);
  const [examFilter, setExamFilter] = useState(-2);
  const [classFilter, setClassFilter] = useState(-2);
  const [subjectFilter, setSubjectFilter] = useState(-2);
  const [data, setData] = useState<MarkRegisterDataType[]>(MarkRegisterData);
  const [outOfMarkState, setOutOfMarkState] = useState(MarkRegisterData[0]?.outoffMark);
  const [passMarkState, setPassMarkState] = useState(MarkRegisterData[0]?.passMark);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [hasError, setHasError] = useState(false);

  const currentMarkRegisterRequest = useMemo(
    () => ({
      adminId,
      academicId: academicYearFilter,
      classId: classFilter,
      examId: examFilter,
      subjectId: subjectFilter,
    }),
    [adminId, academicYearFilter, classFilter, subjectFilter, examFilter]
  );

  const loadMarkRegisterData = useCallback(
    async (request: { adminId: number; academicId: number; classId: number; examId: number; subjectId: number }) => {
      try {
        const result = await dispatch(fetchMarkRegisterDetails(request)).unwrap();
        setData(result);
        setOutOfMarkState(result[0]?.outoffMark === 'N' ? '' : result[0]?.outoffMark);
        setPassMarkState(result[0]?.passMark === 'N' ? '' : result[0]?.passMark);
      } catch (error) {
        console.error('Error loading mark register list:', error);
      }
    },
    [dispatch]
  );

  const loadMarkRegisterFilters = useCallback(async () => {
    try {
      dispatch(fetchMarkRegisterFilter(adminId)).unwrap();
    } catch (error) {
      console.error('Error loading mark register filter:', error);
    }
  }, [adminId, dispatch]);

  const loadMarkRegisterFilterSubjects = useCallback(
    async (request: { adminId: number; classId: number }) => {
      try {
        dispatch(fetchMarkRegisterSubjectFilter(request)).unwrap();
      } catch (error) {
        console.error('Error loading mark register filter subjects:', error);
      }
    },
    [dispatch]
  );

  React.useEffect(() => {
    if (MarkRegisterFilterStatus === 'idle') {
      loadMarkRegisterFilters();
    }
    if (MarkRegisterFilterSubjectStatus === 'idle') {
      loadMarkRegisterFilterSubjects({ adminId, classId: classFilter });
    }
    if (MarkRegisterDataStatus === 'idle') {
      loadMarkRegisterData(currentMarkRegisterRequest);
    }
  }, [
    MarkRegisterDataStatus,
    MarkRegisterFilterStatus,
    MarkRegisterFilterSubjectStatus,
    adminId,
    classFilter,
    currentMarkRegisterRequest,
    dispatch,
    loadMarkRegisterData,
    loadMarkRegisterFilterSubjects,
    loadMarkRegisterFilters,
  ]);

  const handleYearChange = (e: SelectChangeEvent) => {
    const selectedAcademicId = parseInt(e.target.value, 10);
    setAcademicYearFilter(selectedAcademicId);
    loadMarkRegisterData({ ...currentMarkRegisterRequest, examId: selectedAcademicId });
  };

  const handleClassChange = (e: SelectChangeEvent) => {
    const selectedClass = parseInt(e.target.value, 10);
    if (selectedClass) {
      setClassFilter(selectedClass);
    }
    loadMarkRegisterFilterSubjects({
      adminId,
      classId: selectedClass,
    });
    // loadMarkRegisterData({ ...currentMarkRegisterRequest, examId: selectedClass });
    setSubjectFilter(-2);
  };

  const handleExamChange = (e: SelectChangeEvent) => {
    const selectedExam = parseInt(e.target.value, 10);
    if (selectedExam) {
      setExamFilter(selectedExam);
    }
    loadMarkRegisterData({ ...currentMarkRegisterRequest, examId: selectedExam });
  };

  const handleSubjectChange = (e: SelectChangeEvent) => {
    const selectedSubject = parseInt(e.target.value, 10);
    if (selectedSubject) {
      setSubjectFilter(selectedSubject);
    }
    loadMarkRegisterData({ ...currentMarkRegisterRequest, subjectId: selectedSubject });
  };

  const handleReset = React.useCallback(
    (e: any) => {
      e.preventDefault();
      setClassFilter(-2);
      setAcademicYearFilter(-2);
      setExamFilter(-2);
      setSubjectFilter(-2);
      setOutOfMarkState('');
      setPassMarkState('');
      loadMarkRegisterData({ adminId, academicId: -2, classId: -2, examId: -2, subjectId: -2 });
    },
    [loadMarkRegisterData, adminId]
  );

  const sendMarkRegisterData = useCallback(
    async (rows: MarkRegisterDataType[], mode: 'single' | 'multiple') => {
      if (passMarkState === '' || outOfMarkState === '') {
        const sendDoneMessage = (
          <SuccessMessage jsonIcon={errorIcon} loop={false} message="Please Enter Pass Mark and Out of Mark first." />
        );
        await confirm(sendDoneMessage, '', { okLabel: 'Ok', showOnlyOk: true });
      }
      const sendData = rows
        .filter((row) => row.scoredMark !== 'N')
        .map((item: MarkRegisterDataType) => {
          const { studentId, studentRollNo, outoffMark, passMark, scoredMark, scoredGrade } = item;
          return {
            adminId,
            academicId: academicYearFilter,
            classId: classFilter,
            examId: examFilter,
            subjectId: subjectFilter,
            studentId,
            studentRollNo,
            outoffMark: outoffMark === 'N' ? outOfMarkState : outoffMark,
            passMark: passMark === 'N' ? passMarkState : passMark,
            markScored: scoredMark,
            scoredGrade: scoredGrade === 'N' ? '' : scoredGrade,
            dbResult: '',
            id: 0,
          };
        });
      const sendResponse = await dispatch(addMarkRegisterCBSE(sendData));
      loadMarkRegisterData(currentMarkRegisterRequest);
      if (mode === 'multiple') {
        if (
          sendResponse?.payload &&
          typeof sendResponse.payload[0] !== 'string' &&
          sendResponse.payload[0]?.dbResult === 'Success'
        ) {
          const sendDoneMessage = (
            <SuccessMessage jsonIcon={successIcon} loop={false} message="Marks added successfully." />
          );
          await confirm(sendDoneMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        } else {
          const sendDoneMessage = <SuccessMessage jsonIcon={errorIcon} loop={false} message="Failed to add marks." />;
          await confirm(sendDoneMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        }
      }
    },
    [
      academicYearFilter,
      adminId,
      classFilter,
      confirm,
      currentMarkRegisterRequest,
      dispatch,
      examFilter,
      loadMarkRegisterData,
      outOfMarkState,
      passMarkState,
      subjectFilter,
    ]
  );

  const getRowKey = useCallback((row: MarkRegisterDataType) => row.studentId, []);

  const handleStudentClick = useCallback(
    async (studentId: number) => {
      if (passMarkState === '' || outOfMarkState === '') {
        await sendMarkRegisterData(data, 'single');
      } else {
        setCurrentIndex(data.findIndex((x) => x.studentId === studentId));
        setPopup(true);
      }
    },
    [data, outOfMarkState, passMarkState, sendMarkRegisterData]
  );

  const handleMarksChange = useCallback(
    (studentId: number, field: string, value: string) => {
      const regex = /^[0-9]*\.?[0-9]*$|^[absABS]*$/;
      if (value !== '' && value !== 'ABS' && !regex.test(value)) {
        return;
      }
      const updatedData = data.map((item) => {
        if (item.studentId === studentId) {
          return { ...item, [field]: value };
        }
        return item;
      });
      setData(updatedData);

      const error = updatedData.some((item) => {
        return parseInt(item.scoredMark, 10) > parseInt(outOfMarkState, 10);
      });
      setHasError(error);
    },
    [data, outOfMarkState]
  );

  const markListColumns: DataTableColumn<MarkRegisterDataType>[] = useMemo(
    () => [
      {
        name: 'class',
        dataKey: 'className',
        headerLabel: 'Class',
      },
      {
        name: 'rollNo',
        dataKey: 'studentRollNo',
        headerLabel: 'Roll No',
        sortable: true,
      },
      {
        name: 'studentName',
        headerLabel: 'Student Name',
        renderCell: (row) => {
          return (
            <Stack
              direction="row"
              gap={1}
              alignItems="center"
              sx={{ cursor: 'pointer', ':hover': { color: theme.palette.primary.light } }}
              onClick={() => handleStudentClick(row.studentId)}
            >
              <Avatar alt="" src={row.studentImage} />
              <Typography variant="subtitle2">{row.studentName}</Typography>
            </Stack>
          );
        },
      },
      {
        name: 'mark',
        headerLabel: 'Mark',
        renderCell: (row) => {
          return (
            <TextField
              type="text"
              error={row.scoredMark !== 'N' && parseInt(row.scoredMark, 10) > parseInt(outOfMarkState, 10)}
              helperText={
                row.scoredMark !== 'N' && parseInt(row.scoredMark, 10) > parseInt(outOfMarkState, 10)
                  ? 'Enter valid mark'
                  : ''
              }
              value={row.scoredMark !== 'N' ? row.scoredMark : ''}
              onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                handleMarksChange(row.studentId, 'scoredMark', event.target.value);
              }}
              sx={{ maxWidth: '130px' }}
            />
          );
        },
      },
      {
        name: 'grade',
        headerLabel: 'Grade',
        renderCell: (row) => {
          return (
            <TextField value={row.scoredGrade !== 'N' ? row.scoredGrade : ''} disabled sx={{ maxWidth: '100px' }} />
          );
        },
      },
      {
        name: 'abs',
        renderCell: (row) => {
          return (
            <Button
              variant="outlined"
              size="small"
              onClick={() => {
                handleMarksChange(row.studentId, 'scoredMark', 'ABS');
              }}
            >
              Mark Absent
            </Button>
          );
        },
      },
    ],
    [theme.palette.primary.light, handleStudentClick, outOfMarkState, handleMarksChange]
  );

  const handleNext = () => {
    if (currentIndex < data.length - 1) {
      setCurrentIndex((prev) => prev + 1);
      console.log('NextIndex:', currentIndex);
    }
  };

  const handlePrev = () => {
    if (currentIndex > 0) {
      setCurrentIndex((prev) => prev - 1);
      console.log('PrevIndex:', currentIndex);
    }
  };

  const handleClearAll = () => {
    const clearedData = data.map((item) => ({
      ...item,
      scoredMark: item.scoredMark === 'N' ? 'N' : '0',
    }));
    setData(clearedData);
  };

  return (
    <Page title="List">
      <MarkRegisterRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack pb={1} direction="row" justifyContent="space-between" alignItems="center" flexWrap="wrap">
            <Stack direction="row" alignItems="center" justifyContent="space-between" flex={1} whiteSpace="nowrap">
              <Stack direction="row" gap={2} alignItems="center">
                <Typography variant="h6" fontSize={17} width="100%">
                  Mark Register
                </Typography>
              </Stack>

              <Tooltip title="Search">
                <IconButton aria-label="" color="primary" sx={{ mr: 1 }} onClick={() => setShowFilter((x) => !x)}>
                  <SearchIcon />
                </IconButton>
              </Tooltip>
            </Stack>
            <Box
              display="flex"
              justifyContent="end"
              alignItems="center"
              columnGap={2}
              sx={{
                flexShrink: 0,

                '@media (max-width: 340px)': {
                  flexWrap: 'wrap',
                  rowGap: 1,
                },
                '@media (max-width: 280px)': {
                  flex: 1,
                },
              }}
            >
              {MarkRegisterData.length !== 0 && (
                <Button
                  onClick={async () => {
                    if (passMarkState === '' || outOfMarkState === '') {
                      await sendMarkRegisterData(data, 'single');
                    } else {
                      setPopup(true);
                    }
                  }}
                  sx={{
                    borderRadius: '20px',
                    whiteSpace: 'nowrap',
                    '@media (max-width: 500px)': {
                      width: '100%',
                    },
                  }}
                  size="small"
                  variant="outlined"
                >
                  Enroll Indivitual
                </Button>
              )}
            </Box>
          </Stack>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={showFilter ? 7 : 2} pt={1} container spacing={3} alignItems="end">
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 250 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Select
                        labelId="academicYearFilter"
                        id="academicYearFilterSelect"
                        value={academicYearFilter?.toString()}
                        // defaultValue={YearData[0]?.accademicId}
                        onChange={handleYearChange}
                        placeholder="Select Year"
                      >
                        <MenuItem value={-2}>Select</MenuItem>
                        {MarkRegisterFilters?.yearList.map((opt) => (
                          <MenuItem key={opt.academicId} value={opt.academicId}>
                            {opt.academicTime}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 250 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Exam
                      </Typography>
                      <Select
                        labelId="examFilter"
                        id="examFilterSelect"
                        value={examFilter?.toString()}
                        // defaultValue={YearData[0]?.accademicId}
                        onChange={handleExamChange}
                        placeholder="Select Exam"
                      >
                        <MenuItem value={-2}>Select</MenuItem>
                        {MarkRegisterFilters?.examList.map((opt) => (
                          <MenuItem key={opt.examId} value={opt.examId}>
                            {opt.examName}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 250 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Select
                        labelId="classFilter"
                        id="classFilterSelect"
                        value={classFilter?.toString()}
                        // defaultValue={YearData[0]?.accademicId}
                        onChange={handleClassChange}
                        placeholder="Select Class"
                      >
                        <MenuItem value={-2}>Select</MenuItem>
                        {MarkRegisterFilters?.classList.map((opt) => (
                          <MenuItem key={opt.classId} value={opt.classId}>
                            {opt.className}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 250 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Subject
                      </Typography>
                      <Select
                        labelId="subjectFilter"
                        id="subjectFilterSelect"
                        value={subjectFilter?.toString()}
                        // defaultValue={YearData[0]?.accademicId}
                        onChange={handleSubjectChange}
                        placeholder="Select Subject"
                      >
                        <MenuItem value={-2}>Select</MenuItem>
                        {MarkRegisterFilterSubject?.map((opt) => (
                          <MenuItem key={opt.subjectId} value={opt.subjectId}>
                            {opt.subjectName}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item lg="auto" xs={12} sm={6}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button variant="contained" color="secondary" fullWidth onClick={handleReset}>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Paper className="card-table-container" sx={{ marginTop: '12px' }}>
              <Stack direction="row" justifyContent="space-evenly" alignItems="center" spacing={2} py={2}>
                <Stack direction="row" alignItems="center" gap={2}>
                  <Typography className="fw-bold" variant="subtitle2" color="GrayText">
                    Pass-Mark
                  </Typography>
                  <TextField
                    value={passMarkState}
                    type="number"
                    placeholder="00.00"
                    error={parseInt(passMarkState, 10) > parseInt(outOfMarkState, 10)}
                    helperText={parseInt(passMarkState, 10) > parseInt(outOfMarkState, 10) ? 'Invalid' : ''}
                    FormHelperTextProps={{ sx: { fontSize: '9px' } }}
                    onChange={(e) => setPassMarkState(e.target.value)}
                    sx={{ maxWidth: '80px' }}
                  />
                </Stack>
                <Stack direction="row" alignItems="center" gap={2}>
                  <Typography className="fw-bold" variant="subtitle2" color="GrayText">
                    Out-of-Marks
                  </Typography>
                  <TextField
                    value={outOfMarkState}
                    type="number"
                    placeholder="00.00"
                    onChange={(e) => setOutOfMarkState(e.target.value)}
                    sx={{ maxWidth: '90px' }}
                  />
                </Stack>
              </Stack>
              {MarkRegisterData?.length !== 0 ? (
                <DataTable data={data} columns={markListColumns} getRowKey={getRowKey} fetchStatus="success" />
              ) : (
                <Box
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                  width="100%"
                  height={{ xs: 'calc(100vh - 400px)', sm: 'calc(100vh - 400px)', lg: 'calc(100vh - 284px)' }}
                >
                  <Stack direction="column" alignItems="center">
                    <img src={NoData} width="150px" alt="" />
                    <Typography variant="subtitle2" mt={2} color="GrayText">
                      No data found !
                    </Typography>
                  </Stack>
                </Box>
              )}
            </Paper>
          </div>
          <Box display="flex" justifyContent="space-between" sx={{ pr: { lg: '5' }, pt: 2 }}>
            <Button onClick={handleClearAll} variant="outlined" color={themeMode === 'light' ? 'secondary' : 'warning'}>
              Clear All
            </Button>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="secondary">
                Cancel
              </Button>
              <Button
                disabled={
                  data.filter((row) => row.scoredMark === 'N' || row.scoredMark === '').length ===
                    MarkRegisterData.length ||
                  passMarkState === '' ||
                  outOfMarkState === '' ||
                  hasError ||
                  data === MarkRegisterData
                }
                onClick={() => sendMarkRegisterData(data, 'multiple')}
                variant="contained"
                color="primary"
              >
                Update
              </Button>
            </Stack>
          </Box>
        </Card>
      </MarkRegisterRoot>

      <Popup
        title="Enroll Indivitual Marks"
        size="xs"
        state={popup}
        onClose={() => {
          setPopup(false);
          loadMarkRegisterData(currentMarkRegisterRequest);
        }}
        popupContent={
          <IndivitualEnroll
            data={data[currentIndex]}
            setData={setData}
            handleNext={handleNext}
            handlePrev={handlePrev}
            sendMarkRegisterData={sendMarkRegisterData}
            OnClose={() => {
              setPopup(false);
              loadMarkRegisterData(currentMarkRegisterRequest);
            }}
            passMark={passMarkState}
            outOfMark={outOfMarkState}
            dataCount={data.length - 1}
            currentIndex={currentIndex}
            handleMarksChange={handleMarksChange}
          />
        }
      />
    </Page>
  );
}

export default MarkRegister;
