import { Card, Grid, Box, Stack, Skeleton, Typography, IconButton } from '@mui/material';
import styled from 'styled-components';
import MenuListComposition from '@/components/shared/Selections/Menu';
import { getdashboardStatsListStatus, getdashboardStatsListData } from '@/config/storeSelectors';
import { useAppSelector } from '@/hooks/useAppSelector';
import { fetchDashboardStats } from '@/store/Dashboard/dashboard.thunks';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useEffect } from 'react';
import LoadingButton from '@mui/lab/LoadingButton';
import { BiRefresh } from 'react-icons/bi';

const StatusCardRoot = styled.div`
  padding: 0rem 1rem 2.5rem 1rem;
  /* margin: 15px; */
  background-color: ${(props) => (props.theme.themeMode === 'light' ? '' : props.theme.palette.grey[900])};
  .Card {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[800]};
    padding: 0.5rem 0.5rem 0.5rem 1rem;
    border: 1px solid
      ${(props) => (props.theme.themeMode === 'light' ? props.theme.palette.grey[300] : props.theme.palette.grey[800])};
  }
`;
interface StatusCardProps {
  adminId?: number;
}

function StatusCard({ adminId }: StatusCardProps): JSX.Element {
  const dispatch = useAppDispatch();
  const dashboardStatsListStatus = useAppSelector(getdashboardStatsListStatus);
  const dashboardStatsListData = useAppSelector(getdashboardStatsListData);

  useEffect(() => {
    if (dashboardStatsListStatus === 'idle') {
      dispatch(fetchDashboardStats(adminId));
    }
  }, [dispatch, dashboardStatsListStatus, adminId]);

  return (
    <StatusCardRoot>
      <Box sx={{ width: '100%', height: '100%' }}>
        <Box display="flex">
          {dashboardStatsListStatus === 'loading' ? (
            <LoadingButton sx={{ my: 3, mr: 0, ml: 'auto' }} loading />
          ) : (
            <IconButton sx={{ my: 1, mr: 0, ml: 'auto' }} onClick={() => dispatch(fetchDashboardStats(adminId))}>
              <BiRefresh fontSize="inherit" />
            </IconButton>
          )}
        </Box>
        {dashboardStatsListStatus === 'loading' ? (
          <Grid container spacing={3} rowSpacing={{ xs: 3, md: 5 }}>
            {[1, 2, 3, 4, 5, 6, 7, 8].map((data) => (
              <Grid key={data} item xl={3} xs={6}>
                <Card sx={{ boxShadow: '0' }} className="Card">
                  <Stack direction="row" justifyContent="space-between" alignItems="center">
                    <Typography variant="h6">
                      <Skeleton variant="text" width={50} />
                    </Typography>
                    <Box>
                      <Skeleton variant="text" width={20} />
                    </Box>
                  </Stack>
                  <Typography
                    sx={{ fontFamily: 'Poppins Regular', fontSize: '12px', fontWeight: '600', marginTop: '10px' }}
                  >
                    <Skeleton variant="text" height={30} />
                  </Typography>
                </Card>
              </Grid>
            ))}
          </Grid>
        ) : (
          <Grid container spacing={{ sm: 3, xs: 1 }} rowSpacing={{ xs: 2, md: 4 }}>
            {dashboardStatsListData.map((data, index) => (
              <Grid key={index} item xl={3} xs={6}>
                <Card sx={{ boxShadow: '0' }} className="Card">
                  <Stack direction="row" justifyContent="space-between" alignItems="center">
                    {data.total === 0 && data.name !== 'Leave Note' ? (
                      <Typography variant="h6">{data.count}</Typography>
                    ) : (
                      <Typography variant="h6">
                        {data.count}/{data.total}
                      </Typography>
                    )}
                    <Box>
                      <MenuListComposition />
                    </Box>
                  </Stack>
                  <Typography
                    sx={{
                      fontFamily: 'Poppins Regular',
                      fontSize: { sm: '80%', xs: '60%' },
                      fontWeight: '600',
                      marginTop: '10px',
                    }}
                  >
                    {data.name}
                  </Typography>
                  {/* <Typography
                    sx={{
                      fontFamily: 'Poppins Regular',
                      fontSize: '50%',
                      fontWeight: '600',
                      marginTop: '10px',
                      display: { sm: 'none', xs: 'block' },
                    }}
                  >
                    {data.name}
                  </Typography> */}
                </Card>
              </Grid>
            ))}
          </Grid>
        )}
      </Box>
    </StatusCardRoot>
  );
}

export default StatusCard;
