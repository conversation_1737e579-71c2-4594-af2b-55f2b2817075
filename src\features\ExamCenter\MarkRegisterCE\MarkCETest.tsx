import React, { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
} from '@mui/material';

interface Student {
  id: number;
  name: string;
  marks: number | string;
}

const MarkCETEST: React.FC = () => {
  const [students, setStudents] = useState<Student[]>([
    { id: 1, name: 'Alice', marks: '' },
    { id: 2, name: '<PERSON>', marks: '' },
    { id: 3, name: '<PERSON>', marks: '' },
  ]);

  const [open, setOpen] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);

  const handleDialogOpen = () => {
    setCurrentIndex(0); // Start with the first student
    setOpen(true);
  };

  const handleDialogClose = () => {
    setOpen(false);
  };

  const handleNext = () => {
    if (currentIndex < students.length - 1) {
      setCurrentIndex((prev) => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentIndex > 0) {
      setCurrentIndex((prev) => prev - 1);
    }
  };

  const handleMarksChange = (id: number, value: string) => {
    setStudents((prev) => prev.map((student) => (student.id === id ? { ...student, marks: value } : student)));
  };

  return (
    <div>
      <Button variant="contained" color="primary" onClick={handleDialogOpen}>
        Enroll Individual
      </Button>

      <TableContainer style={{ marginTop: '20px' }}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Name</TableCell>
              <TableCell>Marks</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {students.map((student) => (
              <TableRow key={student.id}>
                <TableCell>{student.name}</TableCell>
                <TableCell>
                  <TextField
                    type="number"
                    value={student.marks}
                    onChange={(e) => handleMarksChange(student.id, e.target.value)}
                    fullWidth
                  />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Dialog */}
      <Dialog open={open} onClose={handleDialogClose} maxWidth="sm" fullWidth>
        <DialogTitle>Student Details</DialogTitle>
        <DialogContent>
          <p>
            <strong>Name:</strong> {students[currentIndex].name}
          </p>
          <p>
            <strong>Marks:</strong>
            <TextField
              type="number"
              value={students[currentIndex].marks}
              onChange={(e) => handleMarksChange(students[currentIndex].id, e.target.value)}
              fullWidth
            />
          </p>
        </DialogContent>
        <DialogActions>
          <Button onClick={handlePrevious} disabled={currentIndex === 0} variant="outlined">
            Previous
          </Button>
          <Button onClick={handleNext} disabled={currentIndex === students.length - 1} variant="outlined">
            Next
          </Button>
          <Button onClick={handleDialogClose} variant="contained" color="secondary">
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
};

export default MarkCETEST;
