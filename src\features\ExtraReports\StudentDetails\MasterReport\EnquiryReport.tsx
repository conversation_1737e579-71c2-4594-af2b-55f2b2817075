/* eslint-disable jsx-a11y/alt-text */
import { useCallback, useMemo } from 'react';
import { Box, Grid, Card, useTheme, Chip, Paper } from '@mui/material';
import styled from 'styled-components';
import useSettings from '@/hooks/useSettings';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { EnquiryReportType } from '@/types/Reports';

const EnquiryReportData = [
  {
    id: 1,
    subject: 'Parent Meeting',
    query: 'Lorem Ipsum is simply dummy text of the printing and typesetting industry.',
    reply: '13 Dec, 1:00 PM Conform',
    status: 'Submitted',
  },
  {
    id: 2,
    subject: 'Parent Meeting',
    query: 'Lorem Ipsum is simply dummy text of the printing and typesetting industry.',
    reply: '13 Dec, 1:00 PM Conform',
    status: 'Rejected',
  },
  {
    id: 3,
    subject: 'Parent Meeting',
    query: 'Lorem Ipsum is simply dummy text of the printing and typesetting industry.',
    reply: '13 Dec, 1:00 PM Conform',
    status: 'Submitted',
  },

  {
    id: 4,
    subject: 'Parent Meeting',
    query: 'Lorem Ipsum is simply dummy text of the printing and typesetting industry.',
    reply: '13 Dec, 1:00 PM Conform',
    status: 'Submitted',
  },
  {
    id: 5,
    subject: 'Parent Meeting',
    query: 'Lorem Ipsum is simply dummy text of the printing and typesetting industry.',
    reply: '13 Dec, 1:00 PM Conform',
    status: 'Submitted',
  },
  {
    id: 6,
    subject: 'Parent Meeting',
    query: 'Lorem Ipsum is simply dummy text of the printing and typesetting industry.',
    reply: '13 Dec, 1:00 PM Conform',
    status: 'Rejected',
  },
  {
    id: 7,
    subject: 'Parent Meeting',
    query: 'Lorem Ipsum is simply dummy text of the printing and typesetting industry.',
    reply: 'Dec 26 to Jan 01,2023',
    status: 'Submitted',
  },
];

const EnquiryReportRoot = styled.div`
  .ReportCard {
    display: flex;
    flex-direction: column;
    height: 350px;
    background-color: ${(props) => (props.theme.themeMode === 'light' ? '#f6fbf7' : props.theme.palette.grey[900])};
    /* border: 1px solid ${(props) => props.theme.palette.grey[300]}; */

    .card-main-body {
      display: flex;
      flex-direction: column;
      min-height: calc(100% - 5px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }
    }
  }
`;

function EnquiryReport() {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const tableStyles = { backgroundColor: isLight ? '#f6fbf7' : theme.palette.grey[900] };
  const getRowKeyEnquiryReport = useCallback((row: EnquiryReportType) => row.id, []);

  const EnquiryColumns: DataTableColumn<EnquiryReportType>[] = useMemo(
    () => [
      {
        name: 'subject',
        dataKey: 'subject',
        headerLabel: 'Subject',
      },
      {
        name: 'query',
        dataKey: 'query',
        headerLabel: 'Query',
      },
      {
        name: 'reply',
        dataKey: 'reply',
        headerLabel: 'Reply',
      },
      {
        name: 'status',
        headerLabel: 'Status',
        renderCell: (row) => {
          return (
            <Chip
              variant="outlined"
              sx={{ height: 25 }}
              label={row.status}
              color={row.status === 'Submitted' ? 'success' : 'error'}
            />
          );
        },
      },
    ],
    []
  );

  return (
    <EnquiryReportRoot>
      <Grid container spacing={3}>
        <Grid item xl={12} lg={12} xs={12}>
          <Card className="ReportCard" sx={{ mt: 3, boxShadow: 0, px: { xs: 3, md: 2 }, py: { xs: 2, md: 2 } }}>
            <div className="card-main-body">
              <Box>
                <Chip
                  label="Enquiry"
                  variant="filled"
                  sx={{
                    height: 25,
                    background: isLight ? theme.palette.common.black : theme.palette.common.white,
                    color: isLight ? theme.palette.common.white : theme.palette.common.black,
                  }}
                />
              </Box>
              <Paper className="card-table-container">
                <DataTable
                  columns={EnquiryColumns}
                  data={EnquiryReportData}
                  getRowKey={getRowKeyEnquiryReport}
                  fetchStatus="success"
                  tableStyles={tableStyles}
                />
              </Paper>
            </div>
          </Card>
        </Grid>
      </Grid>
    </EnquiryReportRoot>
  );
}

export default EnquiryReport;
