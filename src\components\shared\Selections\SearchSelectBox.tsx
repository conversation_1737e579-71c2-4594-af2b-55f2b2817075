import * as React from 'react';
import Autocomplete from '@mui/material/Autocomplete';
import TextField from '@mui/material/TextField';
import Stack from '@mui/material/Stack';
import styled from 'styled-components';

const SearchSelectBoxRoot = styled.div`
  .MuiAutocomplete-tag {
    margin: 1px;
  }
`;

type SearchSelectBoxFieldProps = {
  multiple?: boolean;
  options: any[];
  placeholder?: string;
  getOptionLabel?: (option: any) => string;
  renderTags?: (value: any[], getTagProps: any) => React.ReactNode;
};

export default function SearchSelectBoxField({
  multiple = false,
  options,
  getOptionLabel = (option) => option.studentName,
  renderTags,
  placeholder = 'Select option',
}: SearchSelectBoxFieldProps) {
  return (
    <SearchSelectBoxRoot>
      <Stack spacing={3} sx={{ width: 350 }}>
        <Autocomplete
          multiple={multiple}
          options={options}
          getOptionLabel={getOptionLabel}
          renderTags={renderTags}
          renderInput={(params) => (
            <TextField {...params} variant="outlined" placeholder={placeholder} label="Search" />
          )}
          isOptionEqualToValue={(option, value) => option.studentId === value.studentId}
          filterSelectedOptions
          sx={{ height: 30, width: 350 }}
        />
      </Stack>
    </SearchSelectBoxRoot>
  );
}
