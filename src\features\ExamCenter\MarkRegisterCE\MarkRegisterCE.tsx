/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  Collapse,
  FormControl,
  Tooltip,
  Select,
  MenuItem,
  SelectChangeEvent,
  Avatar,
  Chip,
} from '@mui/material';
import styled from 'styled-components';
import SearchIcon from '@mui/icons-material/Search';

import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { useTheme } from '@mui/material/styles';
import useSettings from '@/hooks/useSettings';
import Popup from '@/components/shared/Popup/Popup';
import useAuth from '@/hooks/useAuth';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useAppSelector } from '@/hooks/useAppSelector';
import {
  getExamCenterSubmitting,
  getMarkRegisterDataWithCE,
  getMarkRegisterDataWithCEStatus,
  getMarkRegisterFiltersData,
  getMarkRegisterFiltersStatus,
  getMarkRegisterSubjectFiltersData,
  getMarkRegisterSubjectFiltersStatus,
} from '@/config/storeSelectors';
import {
  addMarkRegisterCBSEwithCE,
  fetchMarkRegisterDetailsWithCE,
  fetchMarkRegisterFilter,
  fetchMarkRegisterSubjectFilter,
} from '@/store/ExamCenter/examCenter.thunks';
import NoData from '@/assets/no-datas.png';
import { MarkRegisterCEDataType } from '@/types/ExamCenter';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import successIcon from '@/assets/MessageIcons/success.json';
import errorIcon from '@/assets/MessageIcons/error-message.json';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import LoadingButton from '@mui/lab/LoadingButton';
import IndivitualEnrollCE from './IndivitualEnrollCE';

const MarkRegisterRoot = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      height: calc(100% - 100px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

function MarkRegisterCE() {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const { user } = useAuth();
  const dispatch = useAppDispatch();
  const { confirm } = useConfirm();
  const adminId: number = user ? user.accountId : 0;
  const [showFilter, setShowFilter] = useState(true);
  const [popup, setPopup] = useState(false);
  const MarkRegisterFilters = useAppSelector(getMarkRegisterFiltersData);
  const MarkRegisterFilterStatus = useAppSelector(getMarkRegisterFiltersStatus);
  const MarkRegisterFilterSubject = useAppSelector(getMarkRegisterSubjectFiltersData);
  const MarkRegisterFilterSubjectStatus = useAppSelector(getMarkRegisterSubjectFiltersStatus);
  const MarkRegisterDataWithCE = useAppSelector(getMarkRegisterDataWithCE);
  const MarkRegisterDataWithCEStatus = useAppSelector(getMarkRegisterDataWithCEStatus);
  const isSubmitting = useAppSelector(getExamCenterSubmitting);
  const [hasError, setHasError] = useState(false);

  const [academicYearFilter, setAcademicYearFilter] = useState(-2);
  const [examFilter, setExamFilter] = useState(-2);
  const [classFilter, setClassFilter] = useState(-2);
  const [subjectFilter, setSubjectFilter] = useState(-2);
  const [dataWithCE, setDataWithCE] = useState<MarkRegisterCEDataType[]>(MarkRegisterDataWithCE);
  const [outOfMarkState, setOutOfMarkState] = useState('');
  const [passMarkState, setPassMarkState] = useState('');
  const [outOfMarkStateCE, setOutOfMarkStateCE] = useState('');
  const [passMarkStateCE, setPassMarkStateCE] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);

  const handleNext = () => {
    if (currentIndex < dataWithCE.length - 1) {
      setCurrentIndex((prev) => prev + 1);
      console.log('NextIndex:', currentIndex);
    }
  };

  const handlePrev = () => {
    if (currentIndex > 0) {
      setCurrentIndex((prev) => prev - 1);
      console.log('PrevIndex:', currentIndex);
    }
  };

  const currentMarkRegisterRequest = useMemo(
    () => ({
      adminId,
      academicId: academicYearFilter,
      classId: classFilter,
      examId: examFilter,
      subjectId: subjectFilter,
    }),
    [adminId, academicYearFilter, classFilter, subjectFilter, examFilter]
  );

  const loadMarkRegisterData = useCallback(
    async (request: { adminId: number; academicId: number; classId: number; examId: number; subjectId: number }) => {
      try {
        const result = await dispatch(fetchMarkRegisterDetailsWithCE(request)).unwrap();
        setDataWithCE(result);
        setOutOfMarkState(result[0]?.outoffMarkTE === 'N' ? '' : result[0]?.outoffMarkTE);
        setPassMarkState(result[0]?.passMarkTE === 'N' ? '' : result[0]?.passMarkTE);
        setOutOfMarkStateCE(result[0]?.outoffMarkCE === 'N' ? '' : result[0]?.outoffMarkCE);
        setPassMarkStateCE(result[0]?.passMarkCE === 'N' ? '' : result[0]?.passMarkCE);
      } catch (error) {
        console.error('Error loading mark register list:', error);
      }
    },
    [dispatch]
  );

  const loadMarkRegisterFilters = useCallback(async () => {
    try {
      dispatch(fetchMarkRegisterFilter(adminId)).unwrap();
    } catch (error) {
      console.error('Error loading mark register filter:', error);
    }
  }, [adminId, dispatch]);

  const loadMarkRegisterFilterSubjects = useCallback(
    async (request: { adminId: number; classId: number }) => {
      try {
        dispatch(fetchMarkRegisterSubjectFilter(request)).unwrap();
      } catch (error) {
        console.error('Error loading mark register filter subjects:', error);
      }
    },
    [dispatch]
  );

  React.useEffect(() => {
    if (MarkRegisterFilterStatus === 'idle') {
      loadMarkRegisterFilters();
    }
    if (MarkRegisterFilterSubjectStatus === 'idle') {
      loadMarkRegisterFilterSubjects({ adminId, classId: classFilter });
    }
    if (MarkRegisterDataWithCEStatus === 'idle') {
      loadMarkRegisterData(currentMarkRegisterRequest);
    }
  }, [
    MarkRegisterFilterStatus,
    MarkRegisterFilterSubjectStatus,
    adminId,
    classFilter,
    currentMarkRegisterRequest,
    dispatch,
    loadMarkRegisterData,
    loadMarkRegisterFilterSubjects,
    loadMarkRegisterFilters,
    MarkRegisterDataWithCEStatus,
  ]);

  const handleYearChange = (e: SelectChangeEvent) => {
    const selectedAcademicId = parseInt(e.target.value, 10);
    setAcademicYearFilter(selectedAcademicId);
    loadMarkRegisterData({ ...currentMarkRegisterRequest, examId: selectedAcademicId });
  };

  const handleClassChange = (e: SelectChangeEvent) => {
    const selectedClass = parseInt(e.target.value, 10);
    if (selectedClass) {
      setClassFilter(selectedClass);
    }
    loadMarkRegisterFilterSubjects({
      adminId,
      classId: selectedClass,
    });
    // loadMarkRegisterData({ ...currentMarkRegisterRequest, examId: selectedClass });
    setSubjectFilter(-2);
  };

  const handleExamChange = (e: SelectChangeEvent) => {
    const selectedExam = parseInt(e.target.value, 10);
    if (selectedExam) {
      setExamFilter(selectedExam);
    }
    loadMarkRegisterData({ ...currentMarkRegisterRequest, examId: selectedExam });
  };

  const handleSubjectChange = (e: SelectChangeEvent) => {
    const selectedSubject = parseInt(e.target.value, 10);
    if (selectedSubject) {
      setSubjectFilter(selectedSubject);
    }
    loadMarkRegisterData({ ...currentMarkRegisterRequest, subjectId: selectedSubject });
  };

  const handleReset = React.useCallback(
    (e: any) => {
      e.preventDefault();
      setClassFilter(-2);
      setAcademicYearFilter(-2);
      setExamFilter(-2);
      setSubjectFilter(-2);
      setOutOfMarkState('');
      setPassMarkState('');
      loadMarkRegisterData({ adminId, academicId: -2, classId: -2, examId: -2, subjectId: -2 });
    },
    [loadMarkRegisterData, adminId]
  );

  const sendMarkRegisterDataWithCE = useCallback(
    async (rows: MarkRegisterCEDataType[], mode: 'single' | 'multiple') => {
      if (passMarkState === '' || outOfMarkState === '' || passMarkStateCE === '' || outOfMarkStateCE === '') {
        const sendDoneMessage = (
          <SuccessMessage jsonIcon={errorIcon} loop={false} message="Please Enter Pass Mark and Out of Mark first." />
        );
        await confirm(sendDoneMessage, '', { okLabel: 'Ok', showOnlyOk: true });
      }
      // const sendConfirmMessage = (
      //   <DeleteMessage
      //     message={<div style={{ color: theme.palette.error.main }}>Are you sure you want to update marks ?</div>}
      //   />
      // );
      // else if (
      //   await confirm('Are you sure you want to update marks ?', '', {
      //     okLabel: 'Yes',
      //     cancelLabel: 'No',
      //   })
      // ) {
      const sendData = rows
        .filter(
          (row) =>
            row.scoredMarkTheory !== 'N' ||
            row.periodicTest !== 'N' ||
            row.multipleAssesment !== 'N' ||
            row.portFolio !== 'N' ||
            row.subjectEnrichment !== 'N'
        )
        .map((item) => {
          const {
            studentId,
            studentRollNo,
            outoffMarkTE,
            passMarkTE,
            outoffMarkCE,
            passMarkCE,
            scoredMarkTheory,
            scoredGrade,
            periodicTest,
            multipleAssesment,
            portFolio,
            subjectEnrichment,
          } = item;
          return {
            adminId,
            academicId: academicYearFilter,
            classId: classFilter,
            examId: examFilter,
            subjectId: subjectFilter,
            studentId,
            studentRollNo,
            outoffMarkTE: outoffMarkTE === 'N' ? outOfMarkState : outoffMarkTE,
            passMarkTE: passMarkTE === 'N' ? passMarkState : passMarkTE,
            outoffMarkCE: outoffMarkCE === 'N' ? outOfMarkStateCE : outoffMarkCE,
            passMarkCE: passMarkCE === 'N' ? passMarkStateCE : passMarkCE,
            scoredMarkTheory,
            scoredGrade: scoredGrade === 'N' ? '' : scoredGrade,
            periodicTest: periodicTest === 'N' ? null : periodicTest,
            multipleAssesment: multipleAssesment === 'N' ? null : multipleAssesment,
            portFolio: portFolio === 'N' ? null : portFolio,
            subjectEnrichment: subjectEnrichment === 'N' ? null : subjectEnrichment,
            dbResult: '',
            id: 0,
          };
        });
      const sendResponse = await dispatch(addMarkRegisterCBSEwithCE(sendData));
      loadMarkRegisterData(currentMarkRegisterRequest);
      if (mode === 'multiple') {
        if (
          sendResponse?.payload &&
          typeof sendResponse.payload[0] !== 'string' &&
          sendResponse.payload[0]?.dbResult === 'Success'
        ) {
          const sendDoneMessage = (
            <SuccessMessage jsonIcon={successIcon} loop={false} message="Marks Added successfully." />
          );
          await confirm(sendDoneMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        } else if (
          sendResponse?.payload &&
          typeof sendResponse.payload[0] !== 'string' &&
          sendResponse.payload[0]?.dbResult === 'Rejected'
        ) {
          const sendDoneMessage = <SuccessMessage jsonIcon={errorIcon} loop={false} message="Marks Adding Failed." />;
          await confirm(sendDoneMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        }
      }
    },
    [
      academicYearFilter,
      adminId,
      classFilter,
      confirm,
      currentMarkRegisterRequest,
      dispatch,
      examFilter,
      loadMarkRegisterData,
      outOfMarkState,
      outOfMarkStateCE,
      passMarkState,
      passMarkStateCE,
      subjectFilter,
    ]
  );

  const handleCancel = async () => {
    if (await confirm('Are you sure you want to reset form?', 'Reset?')) {
      loadMarkRegisterData(currentMarkRegisterRequest);
    }
  };

  const handleMarksChange = useCallback(
    (studentId: number, field: string, value: string) => {
      const regex = /^[0-9]*\.?[0-9]*$|^[absABS]*$/;
      if (value !== '' && value !== 'ABS' && !regex.test(value)) {
        return;
      }
      const updatedData = dataWithCE.map((item) => {
        if (item.studentId === studentId) {
          return { ...item, [field]: value };
        }
        return item;
      });
      setDataWithCE(updatedData);

      const error = updatedData.some((item) => {
        return (
          parseInt(item.periodicTest, 10) > parseInt(outOfMarkStateCE, 10) / 4 ||
          parseInt(item.multipleAssesment, 10) > parseInt(outOfMarkStateCE, 10) / 4 ||
          parseInt(item.portFolio, 10) > parseInt(outOfMarkStateCE, 10) / 4 ||
          parseInt(item.subjectEnrichment, 10) > parseInt(outOfMarkStateCE, 10) / 4 ||
          parseInt(item.scoredMarkTheory, 10) > parseInt(outOfMarkState, 10)
        );
      });
      setHasError(error);
    },
    [dataWithCE, outOfMarkState, outOfMarkStateCE]
  );

  const getRowKeyCE = useCallback((row: MarkRegisterCEDataType) => row.studentId, []);
  const markListColumnsCE: DataTableColumn<MarkRegisterCEDataType>[] = useMemo(
    () => [
      {
        name: 'class',
        dataKey: 'className',
        headerLabel: 'Class',
      },
      {
        name: 'rollNo',
        dataKey: 'studentRollNo',
        headerLabel: 'Roll No',
        sortable: true,
      },
      {
        name: 'studentName',
        headerLabel: 'Student Name',
        renderCell: (row) => {
          return (
            <Stack
              direction="row"
              gap={1}
              alignItems="center"
              sx={{ cursor: 'pointer', ':hover': { color: theme.palette.primary.light } }}
              onClick={async () => {
                if (
                  passMarkState === '' ||
                  outOfMarkState === '' ||
                  passMarkStateCE === '' ||
                  outOfMarkStateCE === ''
                ) {
                  await sendMarkRegisterDataWithCE(dataWithCE, 'single');
                } else {
                  setCurrentIndex(dataWithCE.findIndex((x) => x.studentId === row.studentId));
                  setPopup(true);
                }
              }}
            >
              <Avatar alt="" src={row.studentImage} />
              <Typography variant="subtitle2">{row.studentName}</Typography>
            </Stack>
          );
        },
      },
      {
        name: 'periodicTest',
        headerLabel: 'Periodic Test',
        renderCell: (row) => {
          return (
            <TextField
              error={parseInt(row.periodicTest, 10) > parseInt(outOfMarkStateCE, 10) / 4}
              helperText={parseInt(row.periodicTest, 10) > parseInt(outOfMarkStateCE, 10) / 4 ? 'Invalid' : ''}
              FormHelperTextProps={{ sx: { fontSize: '9px' } }}
              value={row.periodicTest !== 'N' ? row.periodicTest : ''}
              onChange={(event: React.ChangeEvent<HTMLInputElement>) =>
                handleMarksChange(row.studentId, 'periodicTest', event.target.value)
              }
              sx={{
                maxWidth: '85px',
                '& .MuiInputBase-root': {
                  height: '30px', // Adjust the height
                },
                '& .MuiOutlinedInput-input': {
                  padding: '5px', // Reduce padding for the input text
                },
              }}
            />
          );
        },
      },
      {
        name: 'multipleAssesment',
        headerLabel: 'Multiple Assesment',
        renderCell: (row) => {
          return (
            <TextField
              value={row.multipleAssesment !== 'N' ? row.multipleAssesment : ''}
              error={parseInt(row.multipleAssesment, 10) > parseInt(outOfMarkStateCE, 10) / 4}
              helperText={parseInt(row.multipleAssesment, 10) > parseInt(outOfMarkStateCE, 10) / 4 ? 'Invalid' : ''}
              FormHelperTextProps={{ sx: { fontSize: '9px' } }}
              onChange={(event: React.ChangeEvent<HTMLInputElement>) =>
                handleMarksChange(row.studentId, 'multipleAssesment', event.target.value)
              }
              sx={{
                maxWidth: '85px',
                '& .MuiInputBase-root': {
                  height: '30px', // Adjust the height
                },
                '& .MuiOutlinedInput-input': {
                  padding: '5px', // Reduce padding for the input text
                },
              }}
            />
          );
        },
      },
      {
        name: 'portFolio',
        headerLabel: 'PortFolio',
        renderCell: (row) => {
          return (
            <TextField
              value={row.portFolio !== 'N' ? row.portFolio : ''}
              error={parseInt(row.portFolio, 10) > parseInt(outOfMarkStateCE, 10) / 4}
              helperText={parseInt(row.portFolio, 10) > parseInt(outOfMarkStateCE, 10) / 4 ? 'Invalid' : ''}
              FormHelperTextProps={{ sx: { fontSize: '9px' } }}
              onChange={(event: React.ChangeEvent<HTMLInputElement>) =>
                handleMarksChange(row.studentId, 'portFolio', event.target.value)
              }
              sx={{
                maxWidth: '85px',
                '& .MuiInputBase-root': {
                  height: '30px', // Adjust the height
                },
                '& .MuiOutlinedInput-input': {
                  padding: '5px', // Reduce padding for the input text
                },
              }}
            />
          );
        },
      },
      {
        name: 'subjectEnrichment',
        headerLabel: 'Subject Enrichment',
        renderCell: (row) => {
          return (
            <TextField
              value={row.subjectEnrichment !== 'N' ? row.subjectEnrichment : ''}
              error={parseInt(row.subjectEnrichment, 10) > parseInt(outOfMarkStateCE, 10) / 4}
              helperText={parseInt(row.subjectEnrichment, 10) > parseInt(outOfMarkStateCE, 10) / 4 ? 'Invalid' : ''}
              FormHelperTextProps={{ sx: { fontSize: '9px' } }}
              onChange={(event: React.ChangeEvent<HTMLInputElement>) =>
                handleMarksChange(row.studentId, 'subjectEnrichment', event.target.value)
              }
              sx={{
                maxWidth: '85px',
                '& .MuiInputBase-root': {
                  height: '30px',
                },
                '& .MuiOutlinedInput-input': {
                  padding: '5px',
                },
              }}
            />
          );
        },
      },
      {
        name: 'mark',
        headerLabel: 'Theory Mark',
        renderCell: (row) => {
          return (
            <TextField
              value={row.scoredMarkTheory !== 'N' ? row.scoredMarkTheory : ''}
              error={parseInt(row.scoredMarkTheory, 10) > parseInt(outOfMarkState, 10)}
              helperText={parseInt(row.scoredMarkTheory, 10) > parseInt(outOfMarkState, 10) ? 'Invalid' : ''}
              FormHelperTextProps={{ sx: { fontSize: '9px' } }}
              onChange={(event: React.ChangeEvent<HTMLInputElement>) =>
                handleMarksChange(row.studentId, 'scoredMarkTheory', event.target.value)
              }
              sx={{
                maxWidth: '110px',
                '& .MuiInputBase-root': {
                  height: '30px',
                },
                '& .MuiOutlinedInput-input': {
                  padding: '5px',
                },
              }}
            />
          );
        },
      },
      {
        name: 'grade',
        headerLabel: 'Grade',
        renderCell: (row) => {
          return row.scoredGrade === '' ? (
            ''
          ) : (
            <Chip
              size="small"
              label={row.scoredGrade !== '' ? row.scoredGrade : ''}
              sx={{ fontWeight: 'bold' }}
              // variant="contained"
              color={row.scoredGrade !== 'E' ? 'success' : 'error'}
            />
          );
          // <TextField
          //   value={row.scoredGrade !== 'N' ? row.scoredGrade : ''}
          //   disabled
          //   sx={{
          //     maxWidth: '85px',
          //     '& .MuiInputBase-root': {
          //       height: '30px', // Adjust the height
          //     },
          //     '& .MuiOutlinedInput-input': {
          //       padding: '5px', // Reduce padding for the input text
          //     },
          //   }}
          // />
        },
      },
      {
        name: 'abs',
        renderCell: (row) => {
          return (
            <Button
              variant="outlined"
              size="small"
              onClick={() => {
                const updatedData = dataWithCE.map((item) => {
                  if (item.studentId === row.studentId) {
                    return {
                      ...item,
                      scoredMarkTheory: 'ABS',
                    };
                  }
                  return item;
                });
                setDataWithCE(updatedData);
              }}
            >
              Mark Absent
            </Button>
          );
        },
      },
    ],
    [
      dataWithCE,
      handleMarksChange,
      outOfMarkState,
      outOfMarkStateCE,
      passMarkState,
      passMarkStateCE,
      sendMarkRegisterDataWithCE,
      theme.palette.primary.light,
    ]
  );

  const handleClearAll = () => {
    const clearedData = dataWithCE.map((item) => ({
      ...item,
      periodicTest: item.periodicTest === 'N' ? 'N' : '0',
      multipleAssesment: item.multipleAssesment === 'N' ? 'N' : '0',
      portFolio: item.portFolio === 'N' ? 'N' : '0',
      subjectEnrichment: item.subjectEnrichment === 'N' ? 'N' : '0',
      scoredMarkTheory: item.scoredMarkTheory === 'N' ? 'N' : '0',
    }));
    setDataWithCE(clearedData);
  };

  return (
    <Page title="List">
      {/* <MarkCETEST /> */}
      <MarkRegisterRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack pb={1} direction="row" justifyContent="space-between" alignItems="center" flexWrap="wrap">
            <Stack direction="row" alignItems="center" justifyContent="space-between" flex={1} whiteSpace="nowrap">
              <Stack direction="row" gap={2} alignItems="center">
                <Typography variant="h6" fontSize={17} width="100%">
                  Mark Register CE
                </Typography>
              </Stack>

              <Tooltip title="Search">
                <IconButton aria-label="" color="primary" sx={{ mr: 1 }} onClick={() => setShowFilter((x) => !x)}>
                  <SearchIcon />
                </IconButton>
              </Tooltip>
            </Stack>
            <Box
              display="flex"
              justifyContent="end"
              alignItems="center"
              columnGap={2}
              sx={{
                flexShrink: 0,

                '@media (max-width: 340px)': {
                  flexWrap: 'wrap',
                  rowGap: 1,
                },
                '@media (max-width: 280px)': {
                  flex: 1,
                },
              }}
            >
              <Button
                onClick={async () => {
                  if (
                    passMarkState === '' ||
                    outOfMarkState === '' ||
                    passMarkStateCE === '' ||
                    outOfMarkStateCE === ''
                  ) {
                    await sendMarkRegisterDataWithCE(dataWithCE, 'single');
                  } else {
                    setCurrentIndex(0);
                    setPopup(true);
                  }
                }}
                sx={{
                  borderRadius: '20px',
                  whiteSpace: 'nowrap',
                  '@media (max-width: 500px)': {
                    width: '100%',
                  },
                }}
                size="small"
                variant="outlined"
                disabled={MarkRegisterDataWithCE?.length === 0}
              >
                Enroll Indivitual
              </Button>
            </Box>
          </Stack>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={showFilter ? 7 : 2} pt={1} container spacing={3} alignItems="end">
                  <Grid item xl={2.5} lg={2.5} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Select
                        labelId="academicYearFilter"
                        id="academicYearFilterSelect"
                        value={academicYearFilter?.toString()}
                        // defaultValue={YearData[0]?.accademicId}
                        onChange={handleYearChange}
                        placeholder="Select Year"
                      >
                        <MenuItem value={-2}>Select</MenuItem>
                        {MarkRegisterFilters?.yearList.map((opt) => (
                          <MenuItem key={opt.academicId} value={opt.academicId}>
                            {opt.academicTime}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xl={2.5} lg={2.5} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Exam
                      </Typography>
                      <Select
                        labelId="examFilter"
                        id="examFilterSelect"
                        value={examFilter?.toString()}
                        // defaultValue={YearData[0]?.accademicId}
                        onChange={handleExamChange}
                        placeholder="Select Exam"
                      >
                        <MenuItem value={-2}>Select</MenuItem>
                        {MarkRegisterFilters?.examList.map((opt) => (
                          <MenuItem key={opt.examId} value={opt.examId}>
                            {opt.examName}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xl={2.5} lg={2.5} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Select
                        labelId="classFilter"
                        id="classFilterSelect"
                        value={classFilter?.toString()}
                        // defaultValue={YearData[0]?.accademicId}
                        onChange={handleClassChange}
                        placeholder="Select Class"
                      >
                        <MenuItem value={-2}>Select</MenuItem>
                        {MarkRegisterFilters?.classList.map((opt) => (
                          <MenuItem key={opt.classId} value={opt.classId}>
                            {opt.className}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xl={2.5} lg={2.5} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Subject
                      </Typography>
                      <Select
                        labelId="subjectFilter"
                        id="subjectFilterSelect"
                        value={subjectFilter?.toString()}
                        // defaultValue={YearData[0]?.accademicId}
                        onChange={handleSubjectChange}
                        placeholder="Select Subject"
                      >
                        <MenuItem value={-2}>Select</MenuItem>
                        {MarkRegisterFilterSubject?.map((opt) => (
                          <MenuItem key={opt.subjectId} value={opt.subjectId}>
                            {opt.subjectName}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item sm={1}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button variant="contained" color="secondary" fullWidth onClick={handleReset}>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Paper className="card-table-container" sx={{ marginTop: '12px' }}>
              <Stack direction="row" justifyContent="space-evenly" alignItems="center" py={2}>
                <Stack direction="row" alignItems="center" gap={2}>
                  <Typography className="fw-bold" variant="subtitle2" color="GrayText">
                    Pass-Mark(CE)
                  </Typography>
                  <TextField
                    type="number"
                    placeholder="00.00"
                    error={parseInt(passMarkStateCE, 10) > parseInt(outOfMarkStateCE, 10)}
                    helperText={parseInt(passMarkStateCE, 10) > parseInt(outOfMarkStateCE, 10) ? 'Invalid' : ''}
                    FormHelperTextProps={{ sx: { fontSize: '9px' } }}
                    value={passMarkStateCE}
                    onChange={(e) => setPassMarkStateCE(e.target.value)}
                    sx={{
                      maxWidth: '70px',
                      '& .MuiInputBase-root': {
                        height: '30px',
                      },
                      '& .MuiOutlinedInput-input': {
                        padding: '5px',
                      },
                    }}
                  />
                </Stack>
                <Stack direction="row" alignItems="center" gap={2}>
                  <Typography className="fw-bold" variant="subtitle2" color="GrayText">
                    Out-of-Marks(CE)
                  </Typography>
                  <TextField
                    type="number"
                    placeholder="00.00"
                    value={outOfMarkStateCE}
                    onChange={(e) => setOutOfMarkStateCE(e.target.value)}
                    sx={{
                      maxWidth: '70px',
                      '& .MuiInputBase-root': {
                        height: '30px',
                      },
                      '& .MuiOutlinedInput-input': {
                        padding: '5px',
                      },
                    }}
                  />
                </Stack>
                <Divider orientation="vertical" sx={{ borderWidth: 3, borderStyle: 'solid' }} />
                <Stack direction="row" alignItems="center" gap={2}>
                  <Typography className="fw-bold" variant="subtitle2" color="GrayText">
                    Pass-Mark
                  </Typography>
                  <TextField
                    type="number"
                    placeholder="00.00"
                    error={parseInt(passMarkState, 10) > parseInt(outOfMarkState, 10)}
                    helperText={parseInt(passMarkState, 10) > parseInt(outOfMarkState, 10) ? 'Invalid' : ''}
                    FormHelperTextProps={{ sx: { fontSize: '9px' } }}
                    value={passMarkState}
                    onChange={(e) => setPassMarkState(e.target.value)}
                    sx={{
                      maxWidth: '70px',
                      '& .MuiInputBase-root': {
                        height: '30px',
                      },
                      '& .MuiOutlinedInput-input': {
                        padding: '5px',
                      },
                    }}
                  />
                </Stack>
                <Stack direction="row" alignItems="center" gap={2}>
                  <Typography className="fw-bold" variant="subtitle2" color="GrayText">
                    Out-of-Marks
                  </Typography>
                  <TextField
                    type="number"
                    placeholder="00.00"
                    value={outOfMarkState}
                    onChange={(e) => setOutOfMarkState(e.target.value)}
                    sx={{
                      maxWidth: '70px',
                      '& .MuiInputBase-root': {
                        height: '30px',
                      },
                      '& .MuiOutlinedInput-input': {
                        padding: '5px',
                      },
                    }}
                  />
                </Stack>
              </Stack>
              <Divider sx={{ borderWidth: 2, borderStyle: 'solid' }} />
              {MarkRegisterDataWithCE?.length !== 0 ? (
                <DataTable
                  showHorizontalScroll
                  tableStyles={{ minWidth: { xs: '1000px', lg: '1100px' } }}
                  data={dataWithCE}
                  columns={markListColumnsCE}
                  getRowKey={getRowKeyCE}
                  fetchStatus="success"
                />
              ) : (
                <Box
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                  width="100%"
                  height={{ xs: 'calc(100vh - 400px)', sm: 'calc(100vh - 400px)', lg: 'calc(100vh - 284px)' }}
                >
                  <Stack direction="column" alignItems="center">
                    <img src={NoData} width="150px" alt="" />
                    <Typography variant="subtitle2" mt={2} color="GrayText">
                      No data found !
                    </Typography>
                  </Stack>
                </Box>
              )}
            </Paper>
            <Box display="flex" justifyContent="space-between" sx={{ pr: { lg: '5' }, pt: 2 }}>
              <Button
                onClick={handleClearAll}
                variant="outlined"
                color={themeMode === 'light' ? 'secondary' : 'warning'}
              >
                Clear All
              </Button>
              <Stack spacing={2} direction="row">
                <Button onClick={handleCancel} disabled={isSubmitting} variant="contained" color="secondary">
                  Cancel
                </Button>
                <LoadingButton
                  loadingPosition="start"
                  loading={isSubmitting}
                  disabled={dataWithCE.length === 0 || hasError}
                  onClick={() => sendMarkRegisterDataWithCE(dataWithCE, 'multiple')}
                  variant="contained"
                  color="primary"
                  sx={{ width: 120 }}
                >
                  Update
                </LoadingButton>
              </Stack>
            </Box>
          </div>
        </Card>
      </MarkRegisterRoot>

      <Popup
        title="Enroll Indivitual Marks"
        size="xs"
        state={popup}
        onClose={() => {
          setPopup(false);
          loadMarkRegisterData(currentMarkRegisterRequest);
        }}
        popupContent={
          <IndivitualEnrollCE
            data={dataWithCE[currentIndex]}
            setData={setDataWithCE}
            handleNext={handleNext}
            handlePrev={handlePrev}
            sendMarkRegisterData={sendMarkRegisterDataWithCE}
            OnClose={() => {
              setPopup(false);
              loadMarkRegisterData(currentMarkRegisterRequest);
            }}
            passMark={passMarkState}
            outOfMark={outOfMarkState}
            passMarkCE={passMarkStateCE}
            outOfMarkCE={outOfMarkStateCE}
            dataCount={dataWithCE.length - 1}
            currentIndex={currentIndex}
            handleMarksChange={handleMarksChange}
            hasError={hasError}
          />
        }
      />
    </Page>
  );
}

export default MarkRegisterCE;
