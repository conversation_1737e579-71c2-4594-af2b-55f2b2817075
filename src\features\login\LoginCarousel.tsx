// import { useState } from 'react';
// import Slider, { Settings } from 'react-slick';
// import styled from 'styled-components';
// import CarouselArrow from '@/components/Dashboard/CarouselArrow';
// import slideImg1 from '@/assets/loginslider/SlideImage1.svg';
// // import slideImg2 from '@/assets/passdailyLoginSliderImg/loginslideImg2.jpg';
// // import slideImg3 from '@/assets/passdailyLoginSliderImg/loginslideImg3.jpg';
// // import slideImg4 from '@/assets/passdailyLoginSliderImg/loginslideImg4.jpg';
// // import slideImg5 from '@/assets/passdailyLoginSliderImg/loginslideImg5.jpg';
// // import slideImg6 from '@/assets/passdailyLoginSliderImg/loginslideImg6.jpg';
// // import slideImg7 from '@/assets/passdailyLoginSliderImg/loginslideImg7.jpg';
// // import slideImg8 from '@/assets/passdailyLoginSliderImg/loginslideImg8.jpg';
// // import slideImg9 from '@/assets/passdailyLoginSliderImg/loginslideImg9.jpg';
// // import slideImg10 from '@/assets/passdailyLoginSliderImg/loginslideImg10.jpg';
// import { Card } from '@mui/material';

// const LoginCarouselRoot = styled.div`
//   max-width: 100%;
//   padding: 0px;
//   .styleSliderSettings {
//     border-radius: 5px;
//     overflow: hidden;
//   }
// `;

// const SlideImage = styled.img`
//   width: 100%;
//   height: auto;
//   position: relative;
//   border-radius: 5px;
// `;

// // Revolution-style effects using keyframes
// const getSlideAnimation = (index: number) => {
//   const baseStyle = {
//     animation: '',
//   };

//   switch (index) {
//     case 0:
//       return { ...baseStyle, animation: 'slideEffect1 1.5s ease-in-out' };
//     case 1:
//       return { ...baseStyle, animation: 'slideEffect2 1.5s ease-in-out' };
//     case 2:
//       return { ...baseStyle, animation: 'slideEffect3 1.5s ease-in-out' };
//     case 3:
//       return { ...baseStyle, animation: 'slideEffect4 1.5s ease-in-out' };
//     case 4:
//       return { ...baseStyle, animation: 'slideEffect5 1.5s ease-in-out' };
//     case 5:
//       return { ...baseStyle, animation: 'slideEffect6 1.5s ease-in-out' };
//     case 6:
//       return { ...baseStyle, animation: 'slideEffect7 1.5s ease-in-out' };
//     case 7:
//       return { ...baseStyle, animation: 'slideEffect8 1.5s ease-in-out' };
//     case 8:
//       return { ...baseStyle, animation: 'slideEffect9 1.5s ease-in-out' };
//     case 9:
//       return { ...baseStyle, animation: 'slideEffect10 1.5s ease-in-out' };
//     default:
//       return baseStyle;
//   }
// };

// // Define keyframe animations
// const GlobalStyles = styled.div`
//   @keyframes slideEffect1 {
//     0% {
//       transform: scale(0.9) translateX(-50%);
//       opacity: 0;
//     }
//     50% {
//       transform: scale(1.1) translateX(0);
//       opacity: 1;
//     }
//     100% {
//       transform: scale(1) translateX(0);
//       opacity: 1;
//     }
//   }

//   @keyframes slideEffect2 {
//     0% {
//       transform: scale(1.1) rotate(10deg);
//       opacity: 0;
//     }
//     50% {
//       transform: scale(1.05) rotate(0deg);
//       opacity: 1;
//     }
//     100% {
//       transform: scale(1) rotate(0deg);
//       opacity: 1;
//     }
//   }

//   @keyframes slideEffect3 {
//     0% {
//       transform: translateY(-50%) scale(1.2);
//       opacity: 0;
//     }
//     50% {
//       transform: translateY(-10%) scale(1.1);
//       opacity: 1;
//     }
//     100% {
//       transform: translateY(0) scale(1);
//       opacity: 1;
//     }
//   }

//   @keyframes slideEffect4 {
//     0% {
//       transform: skewX(-10deg) scale(0.9);
//       opacity: 0;
//     }
//     50% {
//       transform: skewX(5deg) scale(1.05);
//       opacity: 1;
//     }
//     100% {
//       transform: skewX(0deg) scale(1);
//       opacity: 1;
//     }
//   }

//   @keyframes slideEffect5 {
//     0% {
//       transform: scale(1.3) translateX(50%);
//       opacity: 0;
//     }
//     50% {
//       transform: scale(1.1) translateX(-10%);
//       opacity: 1;
//     }
//     100% {
//       transform: scale(1) translateX(0);
//       opacity: 1;
//     }
//   }

//   @keyframes slideEffect6 {
//     0% {
//       transform: rotateY(90deg);
//       opacity: 0;
//     }
//     50% {
//       transform: rotateY(45deg);
//       opacity: 1;
//     }
//     100% {
//       transform: rotateY(0deg);
//       opacity: 1;
//     }
//   }

//   @keyframes slideEffect7 {
//     0% {
//       transform: scale(0.8) translateY(50%);
//       opacity: 0;
//     }
//     50% {
//       transform: scale(1.1) translateY(-10%);
//       opacity: 1;
//     }
//     100% {
//       transform: scale(1) translateY(0);
//       opacity: 1;
//     }
//   }

//   @keyframes slideEffect8 {
//     0% {
//       transform: translateX(-100%) scale(1);
//       opacity: 0;
//     }
//     50% {
//       transform: translateX(-20%) scale(1.1);
//       opacity: 1;
//     }
//     100% {
//       transform: translateX(0) scale(1);
//       opacity: 1;
//     }
//   }

//   @keyframes slideEffect9 {
//     0% {
//       transform: scale(1.5) rotate(-10deg);
//       opacity: 0;
//     }
//     50% {
//       transform: scale(1.2) rotate(-5deg);
//       opacity: 1;
//     }
//     100% {
//       transform: scale(1) rotate(0deg);
//       opacity: 1;
//     }
//   }

//   @keyframes slideEffect10 {
//     0% {
//       transform: translateY(-100%) scale(0.9);
//       opacity: 0;
//     }
//     50% {
//       transform: translateY(-20%) scale(1.1);
//       opacity: 1;
//     }
//     100% {
//       transform: translateY(0) scale(1);
//       opacity: 1;
//     }
//   }
// `;

// function LoginCarousel() {
//   const [currentSlide, setCurrentSlide] = useState(0);

//   const handleSlideChange = (oldIndex: number, newIndex: number) => {
//     setCurrentSlide(newIndex);
//   };

//   const sliderSettings: Settings = {
//     className: 'styleSliderSettings',
//     dots: false,
//     infinite: true,
//     speed: 500,
//     slidesToShow: 1,
//     slidesToScroll: 1,
//     nextArrow: <CarouselArrow direction="next" />,
//     prevArrow: <CarouselArrow direction="previous" />,
//     autoplay: true,
//     autoplaySpeed: 3000,
//     draggable: true,
//     arrows: true,
//     fade: true,
//     cssEase: 'ease-in-out',
//     beforeChange: handleSlideChange,
//   };

//   return (
//     <Card sx={{ height: 495, display: 'flex', alignItems: 'center', px: 0.5 }}>
//       <GlobalStyles />
//       <LoginCarouselRoot>
//         <Slider {...sliderSettings}>
//           {[slideImg1, slideImg1].map((img, index) => (
//             <div key={index}>
//               <SlideImage
//                 src={img}
//                 alt={`Slide ${index + 1}`}
//                 style={getSlideAnimation(currentSlide === index ? index : -1)}
//               />
//             </div>
//           ))}
//         </Slider>
//       </LoginCarouselRoot>
//     </Card>
//   );
// }

// export default LoginCarousel;

import SlideImage1 from '@/assets/loginslider/SlideImage1.svg';
import Slider, { Settings } from 'react-slick';
import styled from 'styled-components';
import CarouselArrow from '@/components/Dashboard/CarouselArrow';

const settings: Settings = {
  dots: false,
  infinite: true,
  speed: 500,
  slidesToShow: 1,
  slidesToScroll: 1,
  nextArrow: <CarouselArrow direction="next" />,
  prevArrow: <CarouselArrow direction="previous" />,
  autoplay: true,
  autoplaySpeed: 10000,
};

const LoginCarouselRoot = styled.div`
  max-width: 100%;
  padding: 40px;
`;

function LoginCarousel() {
  return (
    <LoginCarouselRoot>
      <Slider {...settings}>
        <img src={SlideImage1} alt="One" />
        <img src={SlideImage1} alt="Two" />
      </Slider>
    </LoginCarouselRoot>
  );
}

export default LoginCarousel;
