import ClassDivision from '@/components/shared/QuickMessageTempletes/ClassDivision';
import Parents from '@/components/shared/QuickMessageTempletes/Parents';
import React from 'react';

const QuickSendMessage = ({ content, messageId }: { content: string; messageId: number }) => {
  switch (content) {
    case 'Parents':
      return <Parents messageId={messageId} />;
    case 'ClassDivision':
      return <ClassDivision messageId={messageId} />;
    default:
      return <div> </div>;
  }
};

export default QuickSendMessage;
