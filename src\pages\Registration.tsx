import LoginCarousel from '@/features/login/LoginCarousel';
import { breakPointsMinwidth } from '@/config/breakpoints';
import styled from 'styled-components';
import Page from '@/components/shared/Page';
import LoginForm from '@/features/login/LoginForm';
import InfoCard from '@/features/login/InfoCard';
import logo from '@/assets/SchoolLogos/HolyLogo.jpeg';
import Pdaily<PERSON>ogo from '@/assets/SchoolLogos/logo-small.svg';
import bgPattern from '@/assets/bg-pattern.jpeg';
import React, { ReactElement, useCallback, useState } from 'react';
import PlaceIcon from '@mui/icons-material/Place';
import LanguageRoundedIcon from '@mui/icons-material/LanguageRounded';
import EmailRoundedIcon from '@mui/icons-material/EmailRounded';
import PhoneRoundedIcon from '@mui/icons-material/PhoneRounded';
import InfoIcon from '@mui/icons-material/Info';
import ParentLogin from '@/Parent-Side/pages/ParentLogin';
import {
  Switch,
  FormControlLabel,
  Grid,
  Stack,
  useTheme,
  Card,
  TextField,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Typography,
  Box,
  Divider,
  FormControl,
  IconButton,
  Collapse,
  useMediaQuery,
} from '@mui/material';
import { colorPresets, purplePreset } from '@/utils/Colors';
import DateSelect from '@/components/shared/Selections/DateSelect';
import DatePickers from '@/components/shared/Selections/DatePicker';
import Success from '@/assets/Registration/success.gif';
import Fail from '@/assets/Registration/fail.gif';
import Loading from '@/assets/Registration/loading.gif';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { ErrorIcon } from '@/theme/overrides/CustomIcons';
import axios from 'axios';
import Popup from '@/components/shared/Popup/Popup';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import { ErrorMessage } from '@/components/shared/Popup/ErrorMessage';
import LoadingPopup from '@/components/shared/Popup/LoadingPopup';
import { LoadingMessage } from '@/components/shared/Popup/LoadingMessage';
import dayjs, { Dayjs } from 'dayjs';

const RegistrationRoot = styled.div`
  /* min-height: calc(100vh - 160px); */
  padding: 1rem;

  .Card {
    min-height: calc(100vh - 35px);
  }
  /* @media ${breakPointsMinwidth.lg} {
    padding: 2rem;
  } */
`;

const DummyContent =
  ' Lorem Ipsum is simply dummy text of the printing and typesetting industry has been the industry&apos;s standard dummy text ever a type specimen book.';

function Registration() {
  const theme = useTheme();
  const { confirm } = useConfirm();
  //   const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [expanded, setExpanded] = React.useState(false);
  const [popup, setPopup] = React.useState(false);
  const [date, setDate] = React.useState<Dayjs | string>('');
  const maxDate = dayjs(new Date());

  const handleExpandClick = () => {
    setExpanded(!expanded);
  };

  const [submitting, setSubmitting] = useState(false);

  const showConfirmation = useCallback(
    async (successMessage: ReactElement, title: string) => {
      const confirmation = confirm(successMessage, title, {
        okLabel: 'Ok',
        showOnlyOk: true,
      });

      return confirmation;
    },
    [confirm]
  );
  const onSave = async (values) => {
    // console.error('values::::', values);
    try {
      // Construct the value object including the Dob field
      const value = {
        ...values,
        Dob: date ? dayjs(date).format('YYYY-MM-DD') : '', // Format the date before assigning
      };
      setSubmitting(true);
      console.log('values::::', value);
      const response = await axios.post('https://holyregapi.pasdaily.in/ElixirApi/StudentSet/OnlineAdmission', value);
      console.log('response.data::::', response.data);
      const successMessages = response.data.result === 'Success';
      const errorMessages = response.data.result === 'Failed';
      setSubmitting(false);
      if (!errorMessages) {
        await showConfirmation(<SuccessMessage icon={Success} message="Enquiry Submitted SuccessFully" />, '');
      } else if (errorMessages) {
        await showConfirmation(<ErrorMessage icon={Fail} message="Enquiry Submission Failed, Please try later" />, '');
      } else {
        await showConfirmation(<ErrorMessage icon={Fail} message="Enquiry Submission Failed, Please try later" />, '');
      }
    } catch (error) {
      setSubmitting(false);
      await showConfirmation(<ErrorMessage icon={Fail} message="Enquiry Submission Failed, Please try later" />, '');
      console.error('Error submitting form:', error);
    } finally {
      setSubmitting(false);
    }
  };
  const CreateEditMessageTempValidationSchema = Yup.object({
    AcademicYear: Yup.string().required('Kindly select the Academic Year.'),
    Class: Yup.string().required('Kindly select the Class.'),
    StudentName: Yup.string().required('Kindly insert the Name.'),
    MotherName: Yup.string().required('Kindly insert the Mother Name.'),
    FatherName: Yup.string().required('Kindly insert the Father Name.'),
    // Dob: Yup.string().required('Kindly insert the Date of Birth.'),
    MobileNumber: Yup.string().required('Kindly insert the Mobile No.'),
    Gender: Yup.string().required('Kindly select the Gender.'),
    SourceOfInformation: Yup.string().required('Kindly select the Source Of Information.'),
  });

  const {
    values: {
      AcademicYear,
      Class,
      StudentName,
      lastName,
      midleName,
      MotherName,
      FatherName,
      FatherOccupation,
      FatherQualification,
      Dob,
      PlaceOfBirth,
      MobileNumber,
      EmailId,
      MotherTongue,
      MotherQualification,
      Gender,
      Address,
      Cast,
      SubCast,
      EmergencyContact,
      AnnualIncome,
      PreviousSchool,
      SourceOfInformation,
      ReasonOfInterest,
    },
    handleChange,
    handleBlur,
    handleSubmit,
    touched,
    errors,
  } = useFormik<any>({
    initialValues: {
      AcademicYear: '',
      Class: '',
      StudentName: '',
      MotherName: '',
      FatherName: '',
      FatherOccupation: '',
      FatherQualification: '',
      Dob: '',
      PlaceOfBirth: '',
      MobileNumber: '',
      EmailId: '',
      MotherTongue: '',
      MotherQualification: '',
      Gender: '',
      Address: '',
      Cast: '',
      SubCast: '',
      EmergencyContact: '',
      AnnualIncome: '',
      PreviousSchool: '',
      SourceOfInformation: '',
      ReasonOfInterest: '',
    },
    validationSchema: CreateEditMessageTempValidationSchema,
    onSubmit: (values) => {
      onSave(values);
    },
    validateOnBlur: false,
    // validate: (messageVals) => {
    //   const errorObj: any = {};
    //   messageVals.messageContent.forEach(async (classRow, rowIndex, arr) => {
    //     if (arr.some((x, i) => classRow.Class !== '' && x.Class === classRow.Class && i !== rowIndex)) {
    //       if (!errorObj.classes) {
    //         errorObj.classes = [];
    //       }
    //       errorObj.classes[rowIndex] = {};
    //       errorObj.classes[rowIndex].Class = 'Duplicate class name';
    //     }
    //   });
    //   return errorObj;
    // },
  });
  const [parentLogin, setParentLogin] = useState(false);
  return (
    <Page title="Registration">
      <RegistrationRoot className="container-fluid">
        <Card className="Card">
          <Grid container>
            <Grid
              item
              xl={3}
              lg={5}
              md={5}
              xs={12}
              position="relative"
              sx={{
                background: ` linear-gradient(90deg, rgba(5,51,106,1) 0%, rgba(5,51,106,0.9809173669467787) 0%),url(${bgPattern})`,
                backgroundSize: 'cover', // Optional: Adjust as needed
                backgroundPosition: 'center', // Optional: Adjust as needed
                position: 'relative',
              }}
            >
              <Box
                p={2}
                display="flex"
                flexDirection="column"
                justifyContent="space-between"
                height={{ xs: '100%', md: '100vh' }}
              >
                <Box>
                  <Stack pt={4} direction="row" justifyContent="center" alignItems="start">
                    <img src={logo} style={{ width: '50%' }} alt="" />
                  </Stack>
                  <Stack mt={{ xs: 2, md: 5 }}>
                    <Typography variant="subtitle1" color={theme.palette.common.white} fontSize={20}>
                      Angels&apos; Paradise
                    </Typography>
                    <Typography variant="subtitle1" color="#fafafa99" fontSize={15}>
                      Enquiry Form 2025-26
                    </Typography>
                    <Divider
                      sx={{
                        backgroundColor: theme.palette.common.white,
                        height: 2,
                        width: 30,
                        mt: 1,
                      }}
                    />
                    <Box sx={{ display: { xs: 'none', md: 'block' } }} color={theme.palette.common.white}>
                      <ol style={{ listStyleType: 'none', padding: 0, paddingTop: 20, color: '#fafafa99' }}>
                        <li style={{ display: 'flex', paddingBottom: 15, gap: 10 }}>
                          <PlaceIcon fontSize="small" />
                          <Typography variant="subtitle1" fontSize={13}>
                            Behind P & T Colony, Gandhi Nagar, Manpada , Dombivli East, Mumbai, Maharashtra 421204
                          </Typography>
                        </li>
                        <li style={{ display: 'flex', paddingBottom: 15, gap: 10 }}>
                          <LanguageRoundedIcon fontSize="small" />
                          <Typography variant="subtitle1" fontSize={14}>
                            www.holyangels.ac.in
                          </Typography>
                        </li>
                        <li style={{ display: 'flex', paddingBottom: 15, gap: 10 }}>
                          <EmailRoundedIcon fontSize="small" />
                          <Typography variant="subtitle1" fontSize={14}>
                            <EMAIL>
                          </Typography>
                        </li>
                        <li style={{ display: 'flex', paddingBottom: 15, gap: 10 }}>
                          <PhoneRoundedIcon fontSize="small" />
                          <Typography variant="subtitle1" fontSize={14}>
                            86520 29999
                          </Typography>
                        </li>
                        <li style={{ display: 'flex', paddingBottom: 15, gap: 10 }}>
                          <PhoneRoundedIcon fontSize="small" />
                          <Typography variant="subtitle1" fontSize={14}>
                            8652019999 (admin office)
                          </Typography>
                        </li>
                      </ol>
                    </Box>
                    {/*  */}
                    <Collapse sx={{ display: { md: 'none', xs: 'block' } }} in={expanded} timeout="auto" unmountOnExit>
                      <Box color={theme.palette.common.white}>
                        <ol style={{ listStyleType: 'none', padding: 0, paddingTop: 20, color: '#fafafa99' }}>
                          <li style={{ display: 'flex', paddingBottom: 15, gap: 10 }}>
                            <PlaceIcon fontSize="small" />
                            <Typography variant="subtitle1" fontSize={13}>
                              Behind P & T Colony, Gandhi Nagar, Manpada , Dombivli East, Mumbai, Maharashtra 421204
                            </Typography>
                          </li>
                          <li style={{ display: 'flex', paddingBottom: 15, gap: 10 }}>
                            <LanguageRoundedIcon fontSize="small" />
                            <Typography variant="subtitle1" fontSize={14}>
                              www.holyangels.ac.in
                            </Typography>
                          </li>
                          <li style={{ display: 'flex', paddingBottom: 15, gap: 10 }}>
                            <EmailRoundedIcon fontSize="small" />
                            <Typography variant="subtitle1" fontSize={14}>
                              <EMAIL>
                            </Typography>
                          </li>
                          <li style={{ display: 'flex', paddingBottom: 15, gap: 10 }}>
                            <PhoneRoundedIcon fontSize="small" />
                            <Typography variant="subtitle1" fontSize={14}>
                              86520 29999
                            </Typography>
                          </li>
                          <li style={{ display: 'flex', paddingBottom: 15, gap: 10 }}>
                            <PhoneRoundedIcon fontSize="small" />
                            <Typography variant="subtitle1" fontSize={14}>
                              8652019999 (admin office)
                            </Typography>
                          </li>
                        </ol>
                      </Box>
                    </Collapse>
                  </Stack>
                </Box>
                <Stack
                  sx={{ visibility: { xs: 'hidden', md: 'visible' } }}
                  direction="row"
                  justifyContent="start"
                  alignItems="center"
                  gap={1}
                >
                  <Typography color={theme.palette.common.white} variant="subtitle1" fontSize={14}>
                    Powered By
                  </Typography>
                  <img src={PdailyLogo} alt="" />
                  <Typography color={theme.palette.common.white} variant="subtitle1" fontSize={20}>
                    Passdaily
                  </Typography>
                </Stack>
                <Collapse sx={{ display: { md: 'none', xs: 'block' } }} in={expanded} timeout="auto" unmountOnExit>
                  <Stack direction="row" justifyContent="start" alignItems="center" gap={1}>
                    <Typography color={theme.palette.common.white} variant="subtitle1" fontSize={14}>
                      Powered By
                    </Typography>
                    <img src={PdailyLogo} alt="" />
                    <Typography color={theme.palette.common.white} variant="subtitle1" fontSize={20}>
                      Passdaily
                    </Typography>
                  </Stack>
                </Collapse>
              </Box>
              <Stack
                onClick={handleExpandClick}
                visibility={{ sm: 'visible', md: 'hidden' }}
                color="#fff"
                position="absolute"
                top={10}
                right={10}
              >
                <InfoIcon />
              </Stack>
            </Grid>
            <Grid item xl={9} lg={7} md={7} xs={12}>
              <Box p={2}>
                <Stack textAlign="center" mt={3}>
                  <Typography variant="subtitle2" fontSize={24}>
                    Enquiry Form
                  </Typography>
                  <Typography variant="subtitle1" fontSize={13}>
                    Please furnish the below mentioned details and complete the enquiry
                  </Typography>
                </Stack>
                <Stack bgcolor={theme.palette.grey[300]} mt={4} py={1} px={2}>
                  <Typography variant="subtitle1" fontSize={15}>
                    Student Details:
                  </Typography>
                </Stack>
                <form onSubmit={handleSubmit} noValidate style={{ marginTop: 30 }}>
                  <Grid container spacing={5}>
                    <Grid item xl={4} lg={6} md={12} xs={12}>
                      <FormControl fullWidth variant="filled">
                        <InputLabel id="demo-simple-select-standard-label">Academic Year *</InputLabel>
                        <Select
                          size="small"
                          disabled={submitting}
                          labelId="demo-simple-select-standard-label"
                          id="demo-simple-select-standard"
                          label="Academic Year *"
                          name="AcademicYear"
                          required
                          value={AcademicYear}
                          onChange={handleChange}
                          error={touched.AcademicYear && !!errors.AcademicYear}
                        >
                          <MenuItem value="2024-2025">2025-2026</MenuItem>
                        </Select>
                        {touched.AcademicYear && !!errors.AcademicYear && (
                          <Typography color="red" fontSize="12px" mt={1} variant="subtitle1">
                            {errors.AcademicYear}
                          </Typography>
                        )}
                      </FormControl>
                    </Grid>
                    <Grid item xl={4} lg={6} md={12} xs={12}>
                      <FormControl fullWidth variant="filled">
                        <InputLabel id="demo-simple-select-standard-label">Class *</InputLabel>
                        <Select
                          size="small"
                          disabled={submitting}
                          labelId="demo-simple-select-standard-label"
                          id="demo-simple-select-standard"
                          label="Class *"
                          required
                          name="Class"
                          value={Class}
                          onChange={handleChange}
                          error={touched.Class && !!errors.Class}
                        >
                          <MenuItem value="NURSERY">NURSERY</MenuItem>
                          <MenuItem value="JRKG">JRKG</MenuItem>
                          <MenuItem value="SRKG">SRKG </MenuItem>
                        </Select>
                        {touched.Class && !!errors.Class && (
                          <Typography color="red" fontSize="12px" mt={1} variant="subtitle1">
                            {errors.Class}
                          </Typography>
                        )}
                      </FormControl>
                    </Grid>
                    <Grid item xl={4} lg={6} md={12} xs={12}>
                      <FormControl fullWidth variant="filled">
                        <TextField
                          size="small"
                          disabled={submitting}
                          id="standard-basic"
                          label="Student Full Name"
                          color="primary"
                          required
                          variant="filled"
                          name="StudentName"
                          value={StudentName}
                          onBlur={handleBlur}
                          onChange={handleChange}
                          error={touched.StudentName && !!errors.StudentName}
                          helperText={errors.StudentName}
                          InputProps={{
                            endAdornment: touched.StudentName && !!errors.StudentName && <ErrorIcon color="error" />,
                          }}
                        />
                      </FormControl>
                    </Grid>
                    {/* <Grid item xl={4} lg={6} md={12} xs={12}>
                      <FormControl fullWidth variant="filled">
                        <TextField
                          size="small"
                          disabled={submitting}
                          id="standard-basic"
                          label="Middle Name"
                          color="primary"
                          value={midleName}
                          onChange={handleChange}
                          name="midleName"
                          variant="filled"
                        />
                      </FormControl>
                    </Grid>
                    <Grid item xl={4} lg={6} md={12} xs={12}>
                      <FormControl fullWidth variant="filled">
                        <TextField
                          size="small"
                          disabled={submitting}
                          id="standard-basic"
                          label="Last Name"
                          color="primary"
                          required
                          variant="filled"
                          name="lastName"
                          value={lastName}
                          onBlur={handleBlur}
                          onChange={handleChange}
                          error={touched.lastName && !!errors.lastName}
                          helperText={errors.lastName}
                          InputProps={{
                            endAdornment: touched.lastName && !!errors.lastName && <ErrorIcon color="error" />,
                          }}
                        />
                      </FormControl>
                    </Grid> */}
                    <Grid item xl={4} lg={6} md={12} xs={12}>
                      <FormControl fullWidth variant="filled">
                        <TextField
                          size="small"
                          disabled={submitting}
                          id="standard-basic"
                          label="Mother Name"
                          color="primary"
                          required
                          name="MotherName"
                          variant="filled"
                          value={MotherName}
                          onBlur={handleBlur}
                          onChange={handleChange}
                          error={touched.MotherName && !!errors.MotherName}
                          helperText={errors.MotherName}
                          InputProps={{
                            endAdornment: touched.MotherName && !!errors.MotherName && <ErrorIcon color="error" />,
                          }}
                        />
                      </FormControl>
                    </Grid>
                    <Grid item xl={4} lg={6} md={12} xs={12}>
                      <FormControl fullWidth variant="filled">
                        <TextField
                          size="small"
                          disabled={submitting}
                          id="standard-basic"
                          label="Father Name"
                          color="primary"
                          required
                          name="FatherName"
                          variant="filled"
                          value={FatherName}
                          onBlur={handleBlur}
                          onChange={handleChange}
                          error={touched.FatherName && !!errors.FatherName}
                          helperText={errors.FatherName}
                          InputProps={{
                            endAdornment: touched.FatherName && !!errors.FatherName && <ErrorIcon color="error" />,
                          }}
                        />
                      </FormControl>
                    </Grid>
                    <Grid item xl={4} lg={6} md={12} xs={12}>
                      <FormControl fullWidth variant="filled">
                        <TextField
                          size="small"
                          disabled={submitting}
                          id="standard-basic"
                          label="Father Occupation"
                          color="primary"
                          variant="filled"
                          onChange={handleChange}
                          name="FatherOccupation"
                          value={FatherOccupation}
                        />
                      </FormControl>
                    </Grid>
                    <Grid item xl={4} lg={6} md={12} xs={12}>
                      <FormControl fullWidth variant="filled">
                        <TextField
                          size="small"
                          disabled={submitting}
                          id="standard-basic"
                          label="Father Qualification"
                          color="primary"
                          variant="filled"
                          onChange={handleChange}
                          name="FatherQualification"
                          value={FatherQualification}
                        />
                      </FormControl>
                    </Grid>
                    <Grid item xl={4} lg={6} md={12} xs={12} position="relative">
                      <Typography variant="subtitle1" fontSize={12} position="absolute" top={20}>
                        Date Of Birth
                      </Typography>
                      <FormControl fullWidth>
                        <DatePickers
                          fullWidth="21.7vw"
                          padding={2.5}
                          name="Dob"
                          value={dayjs(date, 'YYYY-MM-DD')} // Format the date before passing it
                          onChange={(e) => {
                            const selectedDate = e ? dayjs(e) : ''; // Convert the selected date to dayjs object
                            setDate(selectedDate); // Store the selected date in the state
                          }}
                          maxDate={maxDate}
                          variant={{ textField: { variant: 'filled', fullWidth: true } }}
                        />
                      </FormControl>
                    </Grid>
                    <Grid item xl={4} lg={6} md={12} xs={12}>
                      <FormControl fullWidth variant="filled">
                        <TextField
                          size="small"
                          disabled={submitting}
                          id="standard-basic"
                          label="Place of Birth"
                          color="primary"
                          name="PlaceOfBirth"
                          onChange={handleChange}
                          value={PlaceOfBirth}
                          variant="filled"
                        />
                      </FormControl>
                    </Grid>
                    <Grid item xl={4} lg={6} md={12} xs={12}>
                      <FormControl fullWidth variant="filled">
                        <TextField
                          size="small"
                          disabled={submitting}
                          id="standard-basic"
                          label="Mobile No."
                          color="primary"
                          required
                          type="number"
                          name="MobileNumber"
                          variant="filled"
                          value={MobileNumber}
                          onBlur={handleBlur}
                          onChange={handleChange}
                          error={touched.MobileNumber && !!errors.MobileNumber}
                          helperText={errors.MobileNumber}
                          InputProps={{
                            endAdornment: touched.MobileNumber && !!errors.MobileNumber && <ErrorIcon color="error" />,
                          }}
                        />
                      </FormControl>
                    </Grid>
                    <Grid item xl={4} lg={6} md={12} xs={12}>
                      <FormControl fullWidth variant="filled">
                        <TextField
                          size="small"
                          disabled={submitting}
                          id="standard-basic"
                          label="Email Id"
                          color="primary"
                          variant="filled"
                          value={EmailId}
                          onChange={handleChange}
                          name="EmailId"
                        />
                      </FormControl>
                    </Grid>
                    <Grid item xl={4} lg={6} md={12} xs={12}>
                      <FormControl fullWidth variant="filled">
                        <TextField
                          size="small"
                          disabled={submitting}
                          id="standard-basic"
                          label="Mother Tongue"
                          color="primary"
                          value={MotherTongue}
                          onChange={handleChange}
                          name="MotherTongue"
                          variant="filled"
                        />
                      </FormControl>
                    </Grid>
                    <Grid item xl={4} lg={6} md={12} xs={12}>
                      <FormControl fullWidth variant="filled">
                        <TextField
                          size="small"
                          disabled={submitting}
                          id="standard-basic"
                          label="Mother Qualification"
                          color="primary"
                          name="MotherQualification"
                          variant="filled"
                          value={MotherQualification}
                          onBlur={handleBlur}
                          onChange={handleChange}
                          error={touched.MotherQualification && !!errors.MotherQualification}
                          helperText={errors.MotherQualification}
                          InputProps={{
                            endAdornment: touched.MotherQualification && !!errors.MotherQualification && (
                              <ErrorIcon color="error" />
                            ),
                          }}
                        />
                      </FormControl>
                    </Grid>
                    <Grid item xl={4} lg={6} md={12} xs={12}>
                      <FormControl fullWidth variant="filled">
                        <InputLabel id="demo-simple-select-standard-label">Gender *</InputLabel>
                        <Select
                          size="small"
                          disabled={submitting}
                          labelId="demo-simple-select-standard-label"
                          id="demo-simple-select-standard"
                          label="Gender *"
                          name="Gender"
                          required
                          value={Gender}
                          onChange={handleChange}
                          error={touched.Gender && !!errors.Gender}
                        >
                          <MenuItem value="Male">Male</MenuItem>
                          <MenuItem value="Female">Female</MenuItem>
                          <MenuItem value="Transgender">Transgender</MenuItem>
                        </Select>
                        {touched.Gender && !!errors.Gender && (
                          <Typography color="red" fontSize="12px" mt={1} variant="subtitle1">
                            {errors.Gender}
                          </Typography>
                        )}
                      </FormControl>
                    </Grid>
                    <Grid item xl={4} lg={6} md={12} xs={12}>
                      <FormControl fullWidth variant="filled">
                        <TextField
                          size="small"
                          disabled={submitting}
                          id="standard-basic"
                          label="Address"
                          color="primary"
                          value={Address}
                          name="Address"
                          onChange={handleChange}
                          variant="filled"
                        />
                      </FormControl>
                    </Grid>
                    <Grid item xl={4} lg={6} md={12} xs={12}>
                      <FormControl fullWidth variant="filled">
                        <TextField
                          size="small"
                          disabled={submitting}
                          id="standard-basic"
                          label="Caste"
                          value={Cast}
                          name="Cast"
                          color="primary"
                          variant="filled"
                          onChange={handleChange}
                        />
                      </FormControl>
                    </Grid>
                    <Grid item xl={4} lg={6} md={12} xs={12}>
                      <FormControl fullWidth variant="filled">
                        <TextField
                          size="small"
                          disabled={submitting}
                          id="standard-basic"
                          label="Sub Caste"
                          color="primary"
                          value={SubCast}
                          name="SubCast"
                          variant="filled"
                          onChange={handleChange}
                        />
                      </FormControl>
                    </Grid>
                    <Grid item xl={4} lg={6} md={12} xs={12}>
                      <FormControl fullWidth variant="filled">
                        <TextField
                          size="small"
                          disabled={submitting}
                          id="standard-basic"
                          label="Emergency Contact No."
                          color="primary"
                          required
                          variant="filled"
                          value={EmergencyContact}
                          name="EmergencyContact"
                          onChange={handleChange}
                        />
                      </FormControl>
                    </Grid>
                    <Grid item xl={4} lg={6} md={12} xs={12}>
                      <FormControl fullWidth variant="filled">
                        <TextField
                          size="small"
                          disabled={submitting}
                          id="standard-basic"
                          label="Annual Income"
                          color="primary"
                          variant="filled"
                          value={AnnualIncome}
                          name="AnnualIncome"
                          onChange={handleChange}
                        />
                      </FormControl>
                    </Grid>
                    <Grid item xl={4} lg={6} md={12} xs={12}>
                      <FormControl fullWidth variant="filled">
                        <TextField
                          size="small"
                          disabled={submitting}
                          id="standard-basic"
                          label="Previous School Name"
                          color="primary"
                          variant="filled"
                          value={PreviousSchool}
                          name="PreviousSchool"
                          onChange={handleChange}
                        />
                      </FormControl>
                    </Grid>
                    <Grid item xl={4} lg={6} md={12} xs={12}>
                      <FormControl fullWidth variant="filled">
                        <InputLabel id="demo-simple-select-standard-label">Source of Information *</InputLabel>
                        <Select
                          size="small"
                          disabled={submitting}
                          labelId="demo-simple-select-standard-label"
                          id="demo-simple-select-standard"
                          label="Source of Information *"
                          required
                          name="SourceOfInformation"
                          value={SourceOfInformation}
                          onChange={handleChange}
                          error={touched.SourceOfInformation && !!errors.SourceOfInformation}
                        >
                          <MenuItem value="Any Other">Any Other</MenuItem>
                          <MenuItem value="Hoarding">Hoarding</MenuItem>
                          <MenuItem value="Newspaper">Newspaper</MenuItem>
                          <MenuItem value="Word Of Mouth">Word Of Mouth</MenuItem>
                        </Select>
                        {touched.SourceOfInformation && !!errors.SourceOfInformation && (
                          <Typography color="red" fontSize="12px" mt={1} variant="subtitle1">
                            {errors.SourceOfInformation}
                          </Typography>
                        )}
                      </FormControl>
                    </Grid>
                    <Grid item xl={4} lg={6} md={12} xs={12}>
                      <FormControl fullWidth variant="filled">
                        <TextField
                          size="small"
                          disabled={submitting}
                          id="standard-basic"
                          label="Reason for your interest in Angels Paradise"
                          value={ReasonOfInterest}
                          name="ReasonOfInterest"
                          color="primary"
                          onChange={handleChange}
                          variant="filled"
                        />
                      </FormControl>
                    </Grid>
                  </Grid>
                  <Stack direction="row" justifyContent="center" mt={10}>
                    <Button
                      sx={{
                        width: '100px',
                        backgroundColor: '#05326ae9',
                        '&:hover': {
                          backgroundColor: '#05336a',
                        },
                      }}
                      type="submit"
                      variant="contained"
                    >
                      Submit
                    </Button>
                  </Stack>
                </form>
              </Box>
            </Grid>
          </Grid>
        </Card>
      </RegistrationRoot>
      {submitting && (
        <LoadingPopup
          // title="Message Creating"
          popupContent={<LoadingMessage icon={Loading} message="Enquiry Processing Please Wait..." />}
        />
      )}
    </Page>
  );
}

export default Registration;
