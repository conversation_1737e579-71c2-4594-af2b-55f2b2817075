import useAuth from '@/hooks/useAuth';
import { getTextColor } from '@/utils/Colors';
import { Avatar, Box } from '@mui/material';
import { deepOrange } from '@mui/material/colors';
import styled from 'styled-components';

const LoginUserRoot = styled(Box)`
  display: flex;
  align-items: center;

  .details {
    display: flex;
    flex-direction: column;
    justify-content: center;
    .name {
      font-family: 'Poppins Semibold';
      color: ${(props) => getTextColor(props.theme)};
      margin: 0;
      margin-bottom: 0.25rem;
      font-size: 0.875rem;
    }
    .role {
      display: block;
      font-family: 'Poppins Light';
      color: ${(props) =>
        props.theme.themeMode === 'light'
          ? props.theme.palette.secondary.light
          : props.theme.palette.secondary.lighter};
      margin: 0;
      font-size: 0.75rem;
      line-height: 1;
    }
  }
`;

function getInitials(name: string) {
  const names = name.split(' ');
  let initials = names[0].substring(0, 1).toUpperCase();

  if (names.length > 1) {
    initials += names[names.length - 1].substring(0, 1).toUpperCase();
  }
  return initials;
}

export type LoginUserProps = {
  className?: string;
};

function LoginUser({ className = '' }: LoginUserProps) {
  const { isAuthenticated, user } = useAuth();

  if (!isAuthenticated || user === null) {
    return null;
  }

  return (
    <LoginUserRoot className={className}>
      <Avatar sx={{ bgcolor: deepOrange[500], mr: 1 }}>{getInitials(user.displayName)}</Avatar>
      <Box className="details">
        <h4 className="name">{user.displayName}</h4>
        <span className="role">{user.roles && user.roles.length > 0 ? user.roles[0] : ''}</span>
      </Box>
    </LoginUserRoot>
  );
}

export default LoginUser;
