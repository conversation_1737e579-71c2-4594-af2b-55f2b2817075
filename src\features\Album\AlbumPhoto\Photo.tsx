import React, { useCallback, useEffect, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  Collapse,
  FormControl,
  CardMedia,
  useTheme,
  Tooltip,
  ImageListItemBar,
} from '@mui/material';
import styled from 'styled-components';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import SearchIcon from '@mui/icons-material/Search';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import DeleteIcon from '@mui/icons-material/Delete';
import { MdAdd } from 'react-icons/md';
import { AlbumImageInfo } from '@/types/Album';
import useSettings from '@/hooks/useSettings';
import { BiTable } from 'react-icons/bi';
import { TbPhoto } from 'react-icons/tb';
import CreatePhotoForm from './CreatePhotoForm';

export const dummyPhotoArray = [
  {
    slNo: 1,
    albumName: 'Annual Day',
    imageTitle: 'Card',
    imageFile:
      'https://images.unsplash.com/photo-1595981234522-aa6bae3f0dac?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
  },
  {
    slNo: 2,
    albumName: 'Independance Day',
    imageTitle: 'Card',
    imageFile:
      'https://images.unsplash.com/photo-1500051638674-ff996a0ec29e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1836&q=80',
  },
  {
    slNo: 1,
    albumName: 'Annual Day',
    imageTitle: 'Card',
    imageFile:
      'https://images.unsplash.com/photo-1595981234522-aa6bae3f0dac?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
  },
  {
    slNo: 2,
    albumName: 'Independance Day',
    imageTitle: 'Card',
    imageFile:
      'https://images.unsplash.com/photo-1500051638674-ff996a0ec29e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1836&q=80',
  },
  {
    slNo: 1,
    albumName: 'Annual Day',
    imageTitle: 'Card',
    imageFile:
      'https://images.unsplash.com/photo-1595981234522-aa6bae3f0dac?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
  },
  {
    slNo: 2,
    albumName: 'Independance Day',
    imageTitle: 'Card',
    imageFile:
      'https://images.unsplash.com/photo-1500051638674-ff996a0ec29e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1836&q=80',
  },
  {
    slNo: 1,
    albumName: 'Annual Day',
    imageTitle: 'Card',
    imageFile:
      'https://images.unsplash.com/photo-1595981234522-aa6bae3f0dac?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
  },
  {
    slNo: 2,
    albumName: 'Independance Day',
    imageTitle: 'Card',
    imageFile:
      'https://images.unsplash.com/photo-1500051638674-ff996a0ec29e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1836&q=80',
  },
];
const PhotoListRoot = styled.div`
  padding: 1rem;
  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }
    .card-main-body {
      display: flex;
      flex-direction: column;
      height: calc(100% - 40px);
      flex-grow: 1;
      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;
        .MuiTableContainer-root {
          height: 100%;
        }
        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

const DefaultAlbumInfo: AlbumImageInfo = {
  albumId: 0,
  albumName: '',
  imageTitle: '',
  imageFile: '',
};
function PhotoList() {
  const [drawerOpen, setDrawerOpen] = React.useState<boolean>(false);
  const [showFilter, setShowFilter] = useState(false);
  const [changeView, setChangeView] = useState(true);
  const [selectedClassDetail, setSelectedClassDetail] = useState<AlbumImageInfo>(DefaultAlbumInfo);
  const [albumDatas, setAlbumDatas] = useState<AlbumImageInfo[]>([]);

  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';

  const handleOpen = () => {
    setDrawerOpen(true);
  };

  useEffect(() => {
    try {
      const existingArray = JSON.parse(localStorage.getItem('myDummyArray')) || [];
      setAlbumDatas(existingArray);
    } catch (error) {
      // Handle error if JSON parsing fails or localStorage.getItem returns null
      console.error('Error retrieving data from localStorage:', error);
      setAlbumDatas([]);
    }
  }, []);

  const toggleDrawerClose = useCallback(() => setDrawerOpen(false), []);

  const handleDelete = (row: number) => {
    const updatedArray = albumDatas.filter((item) => item.albumId !== row.albumId);
    setAlbumDatas(updatedArray);
    localStorage.setItem('myDummyArray', JSON.stringify(updatedArray));
  };

  const handleSaveorEdit = useCallback(
    async (value: AlbumImageInfo, uploadedImage: string) => {
      // const { albumId, ...rest } = value;
      // await localStorage.setItem('album', rest.albumName);
      toggleDrawerClose();
    },
    [toggleDrawerClose]
  );

  const getRowKey = useCallback((row: any) => row.examId, []);
  const photoListColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'albumId',
        dataKey: 'albumId',
        headerLabel: 'Sl.No',
        sortable: true,
      },
      {
        name: 'name',
        dataKey: 'albumName',
        headerLabel: 'Album Name',
      },
      {
        name: 'title',
        dataKey: 'imageTitle',
        headerLabel: 'Image Title',
      },
      {
        name: 'file',
        headerLabel: 'Image File',
        renderCell: (row) => {
          return <img src={row.imageFile} alt={row.imageTitle} width={100} style={{ borderRadius: '10px' }} />;
        },
      },
      {
        name: 'actions',
        headerLabel: 'Actions',
        renderCell: (row) => {
          return (
            <Stack direction="row" gap={1}>
              <IconButton size="small" color="error" onClick={() => handleDelete(row)} sx={{ padding: 0.5 }}>
                <DeleteIcon />
              </IconButton>
            </Stack>
          );
        },
      },
    ],
    []
  );
  return (
    <Page title="List">
      <PhotoListRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17}>
              Photos List
            </Typography>
            <Box pb={1} sx={{ flexShrink: 0 }}>
              <IconButton aria-label="delete" color="primary" sx={{ mr: 0 }} onClick={() => setShowFilter((x) => !x)}>
                <SearchIcon />
              </IconButton>
              <Tooltip title={changeView === true ? 'Table View' : 'Card View'}>
                <IconButton
                  sx={{ mr: 1 }}
                  className="change-view-button"
                  color="primary"
                  onClick={() => setChangeView((prevChangeView) => !prevChangeView)}
                >
                  {changeView === true ? <BiTable /> : <TbPhoto />}
                </IconButton>
              </Tooltip>
              <Button onClick={handleOpen} sx={{ borderRadius: '20px' }} variant="outlined" size="small">
                <MdAdd size="20px" /> Add
              </Button>
            </Box>
          </Stack>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={2} container spacing={3} alignItems="end">
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 250 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Album Name
                      </Typography>
                      <TextField placeholder="Enter Name" />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 250 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Image Title
                      </Typography>
                      <TextField placeholder="Enter Title" />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12} sm={6}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>
            {changeView === true ? (
              <Box
                my={2}
                sx={{
                  overflow: 'auto',
                  '&::-webkit-scrollbar': {
                    width: 0,
                  },
                }}
              >
                <Grid container spacing={3}>
                  {albumDatas.map((item) => (
                    <Grid key={item.albumId} item xl={2} lg={3} md={6} sm={4} xs={12}>
                      <Card sx={{ bgcolor: isLight ? theme.palette.common.white : theme.palette.grey[900] }}>
                        <IconButton
                          onClick={() => handleDelete(item)}
                          size="small"
                          sx={{
                            position: 'absolute',
                            right: 5,
                            top: 5,
                            border: `1px solid ${theme.palette.grey[300]}`,
                            backgroundColor: '#fff',
                            '&:hover': {
                              backgroundColor: theme.palette.grey[200],
                            },
                          }}
                          aria-label={`info about ${item.imageTitle}`}
                        >
                          <DeleteIcon color="error" sx={{ fontSize: '18px' }} />
                        </IconButton>
                        <CardMedia
                          sx={{ objectFit: 'cover' }}
                          component="img"
                          height="150"
                          image={item.imageFile}
                          alt={item.imageTitle}
                        />

                        <ImageListItemBar title={item.albumName} />
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </Box>
            ) : (
              <Paper className="card-table-container" sx={{ marginTop: '12px' }}>
                <DataTable columns={photoListColumns} data={albumDatas} getRowKey={getRowKey} fetchStatus="success" />
              </Paper>
            )}
          </div>
          {/* <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 2 }}>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="secondary
                Cancel
              </Button>
              <Button onClick={handleOpen} variant="contained" color="primary">
                Save
              </Button>
            </Stack>
          </Box> */}
        </Card>
      </PhotoListRoot>
      <TemporaryDrawer
        onClose={toggleDrawerClose}
        Title="Upload Details"
        state={drawerOpen}
        DrawerContent={
          <CreatePhotoForm
            onSave={handleSaveorEdit}
            albumInfo={DefaultAlbumInfo}
            onCancel={toggleDrawerClose}
            onClose={toggleDrawerClose}
          />
        }
      />
    </Page>
  );
}

export default PhotoList;
