/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import {
  Chip,
  Divider,
  Paper,
  Stack,
  Typography,
  Box,
  Avatar,
  Button,
  useTheme,
  IconButton,
  Card,
  CardMedia,
} from '@mui/material';
import styled from 'styled-components';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import CloseIcon from '@mui/icons-material/Close';
import { BsFillPlayCircleFill } from 'react-icons/bs';
import BackButton from '@/components/shared/BackButton';
import Popup from '@/components/shared/Popup/Popup';
import { Grid } from '@mui/material';
import PopupVideoPlayer from '@/components/shared/PopupVideoPlayer';

const ViewReportRoot = styled.div`
  padding: 1rem;
  .Card {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 40px);
      flex-grow: 1;
      padding-bottom: 15px;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

export const data = {
  studentName: 'Joseph Alexander',
  class: 'VII-D',
  duration: '01:00:00',
  timeTaken: '00:00:50',
  startDate: '11-oct-2024',
  endDate: '11-oct-2024',
  totalQuestion: '5',
  paused: 'No',
  correctAnswer: '3',
  unAnswered: '0',
  answeredQuestion: '3',
  inCorrectAnswer: '0',
  status: 'On Going',
  totalPause: '00',
};

function ViewReport() {
  const theme = useTheme();
  const [videoPopup, setVideoPopup] = React.useState(false);

  const getRowKey = useCallback((row: any) => row.examId, []);
  const reportViewRowsList: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'studentName',
        headerLabel: 'Student',
        renderCell: (row) => {
          return (
            <Stack direction="row" gap={1} alignItems="center">
              <Avatar
                alt=""
                src="https://images.unsplash.com/photo-1628157588553-5eeea00af15c?q=80&w=1780&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
              />
              <Typography variant="subtitle2">{row.studentName}</Typography>
            </Stack>
          );
        },
      },

      {
        name: 'class',
        dataKey: 'class',
        headerLabel: 'Class',
      },
      {
        name: 'duration',
        dataKey: 'duration',
        headerLabel: 'Duration',
      },
      {
        name: 'timeTaken',
        dataKey: 'timeTaken',
        headerLabel: 'Time Taken',
      },
      {
        name: 'startDate',
        dataKey: 'startDate',
        headerLabel: 'Start Date',
      },
      {
        name: 'endDate',
        dataKey: 'endDate',
        headerLabel: 'End Date',
      },
      {
        name: 'totalQuestion',
        dataKey: 'totalQuestion',
        headerLabel: 'Total Question',
      },
      {
        name: 'paused',
        dataKey: 'paused',
        headerLabel: 'Paused',
      },
      {
        name: 'correctAnswer',
        dataKey: 'correctAnswer',
        headerLabel: 'Correct Answer',
      },
      {
        name: 'unAnswered',
        dataKey: 'unAnswered',
        headerLabel: 'Unanswered',
      },
      {
        name: 'answeredQuestion',
        headerLabel: 'Answered Question',
        renderCell: (row) => {
          return (
            <Box>
              <Chip size="small" label={`${row.answeredQuestion}`} color="success" />
            </Box>
          );
        },
      },
      {
        name: 'inCorrectAnswer',
        headerLabel: 'In Correct Answer',
        renderCell: (row) => {
          return (
            <Box>
              <Chip size="small" label={`${row.inCorrectAnswer}`} color="error" />
            </Box>
          );
        },
      },
      {
        name: 'status',
        headerLabel: 'Status',
        renderCell: (row) => {
          return (
            <Box>
              <Chip size="small" label={`${row.status}`} color="warning" />
            </Box>
          );
        },
      },
      {
        name: 'totalPause',
        dataKey: 'totalPause',
        headerLabel: 'Total Pause',
      },
    ],
    []
  );

  return (
    <ViewReportRoot>
      <Grid container spacing={10} px={3}>
        <Grid item lg={6} xs={6}>
          <Typography mb={2} variant="h6">
            Detailed Report
          </Typography>
          <Card sx={{ p: 2, boxShadow: 0, border: 1, borderColor: theme.palette.grey[300] }}>
            {reportViewRowsList.map((row) => (
              <Grid container key={row.name}>
                <Grid item lg={4.5} xs={6} mt={2}>
                  <Typography variant="subtitle2" fontSize={13}>
                    {row.headerLabel}
                  </Typography>
                </Grid>
                <Grid item lg={7.5} xs={6} mb={0} mt="auto">
                  <Typography variant="h6" fontSize={13}>
                    {row.dataKey
                      ? `:${' '}${(data as { [key: string]: any })[row.dataKey ?? '']}`
                      : row && row.renderCell && row.renderCell(data)}
                  </Typography>
                </Grid>
              </Grid>
            ))}
          </Card>
        </Grid>
        <Grid item lg={6} xs={6} sx={{}}>
          <Typography mb={2} variant="h6">
            Question Detail
          </Typography>
          <Box sx={{ height: '480px', overflow: 'scroll' }}>
            <Stack gap={3}>
              <Card
                sx={{
                  p: 2,
                  boxShadow: 0,
                  backgroundColor: theme.palette.grey[100],
                }}
              >
                <Chip size="small" label="Wrong Answer" color="error" />
                <Stack gap={2} mt={2} color={theme.palette.grey[600]}>
                  <Typography variant="subtitle2" color={theme.palette.grey[900]}>
                    1. How many weeks are therein one year?
                  </Typography>
                  <Typography variant="subtitle2">1. 45</Typography>
                  <Typography variant="subtitle2">2. 52</Typography>
                  <Typography variant="subtitle2" color="error">
                    3. 31
                  </Typography>
                  <Typography variant="subtitle2">4. 34</Typography>
                </Stack>
              </Card>
              <Card
                sx={{
                  p: 2,
                  boxShadow: 0,
                  backgroundColor: theme.palette.grey[100],
                }}
              >
                <Chip size="small" label="Correct Answer" color="success" />
                <Stack gap={2} mt={2} color={theme.palette.grey[600]}>
                  <Typography variant="subtitle2" color={theme.palette.grey[900]}>
                    1. How many girls in the video?
                  </Typography>
                  <Card sx={{ boxShadow: '0' }}>
                    <CardMedia
                      component="img"
                      alt="Events"
                      height="160"
                      image="https://images.unsplash.com/photo-1640952131659-49a06dd90ad2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                      sx={{ position: 'relative', cursor: 'pointer' }}
                      onClick={() =>  setVideoPopup(true)}
                    />
                    <IconButton
                      onClick={() => setVideoPopup(true)}
                      sx={{
                        position: 'absolute',
                        top: '50%',
                        left: '50%',
                        transform: 'translate(-50%, -50%)',
                        color: 'white',
                        fontSize: '3rem', // Adjust icon size as needed
                      }}
                      aria-label="play"
                    >
                      <BsFillPlayCircleFill />
                    </IconButton>
                  </Card>
                  <Typography variant="subtitle2">1. 45</Typography>
                  <Typography variant="subtitle2">2. 52</Typography>
                  <Typography variant="subtitle2" color="error">
                    3. 31
                  </Typography>
                  <Typography variant="subtitle2">4. 34</Typography>
                </Stack>
              </Card>
            </Stack>
          </Box>
        </Grid>
      </Grid>
      <Popup
        title="Events"
        size="sm"
        state={videoPopup}
        onClose={() => setVideoPopup(false)}
        popupContent={<PopupVideoPlayer videoFile="https://www.youtube.com/shorts/gZ_y5GUJ7CE?feature=share" />}
      />
    </ViewReportRoot>
  );
}

export default ViewReport;
