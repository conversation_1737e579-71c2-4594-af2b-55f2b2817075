import Loadable from '@/components/shared/Loadable';
import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';

const AcademicManagement = Loadable(lazy(() => import('@/pages/StaffManagement')));
const ManageStaffs = Loadable(lazy(() => import('@/features/StaffManagement/ManageStaffs/ManageStaffs ')));
const CTSAllocation = Loadable(lazy(() => import('@/features/StaffManagement/CTSAllocation/CTSAllocation')));
const CTSallocationCW = Loadable(lazy(() => import('@/features/StaffManagement/CTSAllocationCW')));
const CTSallocationTW = Loadable(lazy(() => import('@/features/StaffManagement/CTSAllocationTW')));
const StaffReAllocation = Loadable(lazy(() => import('@/features/StaffManagement/StaffReAllocation')));
const HoursList = Loadable(lazy(() => import('@/features/StaffManagement/HoursEngagedList')));
const StaffCategory = Loadable(lazy(() => import('@/features/StaffManagement/StaffCategory')));
const StaffCategoryMap = Loadable(lazy(() => import('@/features/StaffManagement/StaffCategoryMap')));
const Conveyers = Loadable(lazy(() => import('@/features/StaffManagement/ManageConveyors/ManageConveyers')));
const StaffActivityReport = Loadable(
  lazy(() => import('@/features/StaffManagement/StaffActivityReport/StaffActivityReport'))
);

export const staffRoutes: RouteObject[] = [
  {
    path: '/staff-management',
    element: <AcademicManagement />,
    children: [
      {
        path: 'list',
        element: <ManageStaffs />,
      },
      {
        path: 'allocation-list',
        element: <CTSAllocation />,
      },
      {
        path: 'class-wise-allocation',
        element: <CTSallocationCW />,
      },
      {
        path: 'teacher-wise-allocation',
        element: <CTSallocationTW />,
      },
      {
        path: 're-allocate',
        element: <StaffReAllocation />,
      },
      {
        path: 'hours-list',
        element: <HoursList />,
      },
      {
        path: 'staff-activity-report',
        element: <StaffActivityReport />,
      },
      {
        path: 'category-list',
        element: <StaffCategory />,
      },
      {
        path: 'category-map',
        element: <StaffCategoryMap />,
      },
      {
        path: 'conveyors',
        element: <Conveyers />,
      },
    ],
  },
];
