import { ComponentProps, forwardRef, ReactNode, useEffect, useRef } from 'react';
import { Helmet } from 'react-helmet-async';
import { Box, Hidden } from '@mui/material';
import styled from 'styled-components';
import { motion, Variants, useInView, useAnimation } from 'framer-motion';
import { delay } from '@reduxjs/toolkit/dist/utils';

export type AnimationProps = {
  children: ReactNode;
  containerProps?: Omit<ComponentProps<typeof Box>, 'children'>;
  whileHover?: any;
};

const AnimationTransition: Variants = {
  //   initial: { opacity: 0, x: -200, y: 0 },
  //   animate: { opacity: 1, x: 0, y: 0 },
  //   exit: { opacity: 0, x: 0, y: -100 },
  initial: { opacity: 0, y: 75 },
  animate: { opacity: 1, y: 0 },
};

const AnimationContainer = motion(styled(Box)`
  /* background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.grey[800]}; */
`);

const Animation = forwardRef(({ children, whileHover }: AnimationProps) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true });

  const mainControls = useAnimation();

  useEffect(() => {
    if (isInView) {
      mainControls.start('animate');
    }
  }, [isInView, mainControls]);
  return (
    <div ref={ref}>
      <motion.div
        variants={AnimationTransition}
        initial="initial"
        animate={mainControls}
        //   exit="exit"
        transition={{ duration: 1, delay: 0.25 }}
        whileHover={whileHover}
      >
        {children}
      </motion.div>
    </div>
  );
});

export default Animation;
