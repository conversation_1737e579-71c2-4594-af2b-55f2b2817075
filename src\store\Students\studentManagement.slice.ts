import { PayloadAction, createSlice } from '@reduxjs/toolkit';
import type { StudentManagementState } from '@/types/StudentManagement';
import {
  fetchStudentList,
  addNewStudent,
  updateStudent,
  deleteStudent,
  addNewMultipleStudent,
  fetchQucickUpdateStudentList,
  qucickUpdate,
  // fetchStudentLists,
} from './studentManagement.thunks';
import { flushStore } from '../flush.slice';

const initialState: StudentManagementState = {
  studentList: {
    status: 'idle',
    data: [],
    error: null,
    pageInfo: {
      totalrecords: 0,
      pagesize: 20,
      pagenumber: 1,
      totalpages: 0,
      remainingpages: 0,
    },
    sortColumn: 'classId',
    sortDirection: 'asc',
  },
  quickUpdateStudentList: {
    data: [],
    status: 'idle',
    error: null,
  },
  submitting: false,
  deletingRecords: {},
  error: null,
};

export const studentManagementSlice = createSlice({
  name: 'studentManagement',
  initialState,
  reducers: {
    setStudentListPage: (state, action: PayloadAction<number>) => {
      state.studentList.pageInfo.pagenumber = action.payload;
    },
    setStudentListPageSize: (state, action: PayloadAction<number>) => {
      state.studentList.pageInfo.pagenumber = 1;
      state.studentList.pageInfo.pagesize = action.payload;
    },
    setSortColumn: (state, action: PayloadAction<string>) => {
      state.studentList.pageInfo.pagenumber = 1;
      state.studentList.sortColumn = action.payload;
      state.studentList.sortDirection = 'asc';
    },
    setSortDirection: (state, action: PayloadAction<'asc' | 'desc'>) => {
      state.studentList.pageInfo.pagenumber = 1;
      state.studentList.sortDirection = action.payload;
    },
    setSortColumnAndDirection: (state, action: PayloadAction<{ column: string; direction: 'asc' | 'desc' }>) => {
      state.studentList.pageInfo.pagenumber = 1;
      state.studentList.sortColumn = action.payload.column;
      state.studentList.sortDirection = action.payload.direction;
    },
    setSubmitting: (state, action: PayloadAction<boolean>) => {
      state.submitting = action.payload;
    },
    setDeletingRecords: (state, action: PayloadAction<{ recordId: number }>) => {
      state.deletingRecords[action.payload.recordId] = true;
    },
    clearDeletingRecords: (state, action: PayloadAction<{ recordId: number }>) => {
      delete state.deletingRecords[action.payload.recordId];
    },
  },
  extraReducers(builder) {
    builder
      //   .addCase(fetchStudentLists.pending, (state) => {
      //     state.studentList.status = 'loading';
      //     state.studentList.error = null;
      //   })
      //   .addCase(fetchStudentLists.fulfilled, (state, action) => {
      //     const data = action.payload;
      //     state.studentList.status = 'success';
      //     state.studentList.data = data;
      //     state.studentList.error = null;
      //     // state.studentList.pageInfo = pageInfo;
      //   })
      //   .addCase(fetchStudentLists.rejected, (state, action) => {
      //     state.studentList.status = 'error';
      //     state.studentList.error = action.payload || 'Unknown error in fetching student list';
      //   })

      .addCase(fetchStudentList.pending, (state) => {
        state.studentList.status = 'loading';
        state.studentList.error = null;
      })
      .addCase(fetchStudentList.fulfilled, (state, action) => {
        const { data, ...pageInfo } = action.payload;
        state.studentList.status = 'success';
        state.studentList.data = data;
        state.studentList.error = null;
        state.studentList.pageInfo = pageInfo;
      })
      .addCase(fetchStudentList.rejected, (state, action) => {
        state.studentList.status = 'error';
        state.studentList.error = action.payload || 'Unknown error in fetching student list';
      })

      .addCase(addNewStudent.pending, (state) => {
        state.submitting = true;
        state.error = null;
      })
      .addCase(addNewStudent.fulfilled, (state) => {
        state.submitting = false;
        state.error = null;
      })
      .addCase(addNewStudent.rejected, (state, action) => {
        state.submitting = false;
        state.error = action.error.message;
      })

      .addCase(addNewMultipleStudent.pending, (state) => {
        state.submitting = true;
        state.error = null;
      })
      .addCase(addNewMultipleStudent.fulfilled, (state) => {
        state.submitting = false;
        state.error = null;
      })
      .addCase(addNewMultipleStudent.rejected, (state, action) => {
        state.submitting = false;
        state.error = action.error.message;
      })

      .addCase(updateStudent.pending, (state) => {
        state.submitting = true;
        state.error = null;
      })
      .addCase(updateStudent.fulfilled, (state, action) => {
        state.submitting = false;
        state.error = null;
        const dataIndex = state.studentList.data.findIndex((x) => x.studentId === action.meta.arg.studentId);
        if (dataIndex > -1) {
          state.studentList.data[dataIndex] = action.meta.arg;
        }
      })
      .addCase(updateStudent.rejected, (state, action) => {
        state.submitting = false;
        state.error = action.error.message;
      })

      .addCase(deleteStudent.pending, (state, action) => {
        state.deletingRecords[action.meta.arg] = true;
        state.error = null;
      })
      .addCase(deleteStudent.fulfilled, (state, action) => {
        if (action.payload.deleted) {
          const { [action.meta.arg]: deletedRecord, ...restDeletingRecords } = state.deletingRecords;
          state.deletingRecords = restDeletingRecords;
        }
        state.error = null;
      })
      .addCase(deleteStudent.rejected, (state, action) => {
        state.deletingRecords = { ...initialState.deletingRecords };
        state.error = action.error.message;
      })

      .addCase(fetchQucickUpdateStudentList.pending, (state) => {
        state.quickUpdateStudentList.status = 'loading';
        state.quickUpdateStudentList.error = null;
      })
      .addCase(fetchQucickUpdateStudentList.fulfilled, (state, action) => {
        state.quickUpdateStudentList.status = 'success';
        state.quickUpdateStudentList.data = action.payload;
        state.quickUpdateStudentList.error = null;
      })
      .addCase(fetchQucickUpdateStudentList.rejected, (state, action) => {
        state.quickUpdateStudentList.status = 'error';
        state.quickUpdateStudentList.error = action.payload || 'Unknown error in fetching Student Term Fee Status';
      })

      .addCase(qucickUpdate.pending, (state) => {
        state.submitting = true;
        state.error = null;
      })
      .addCase(qucickUpdate.fulfilled, (state, action) => {
        state.submitting = false;
        state.error = null;
        const dataIndex = state.studentList.data.findIndex((x) => x.studentId === action.meta.arg.studentId);
        if (dataIndex > -1) {
          state.quickUpdateStudentList.data[dataIndex] = action.meta.arg;
        }
      })
      .addCase(qucickUpdate.rejected, (state, action) => {
        state.submitting = false;
        state.error = action.error.message;
      })
      .addCase(flushStore, () => initialState);
  },
});

export const {
  setStudentListPage,
  setStudentListPageSize,
  setSortColumn,
  setSortDirection,
  setSortColumnAndDirection,
  setSubmitting,
  setDeletingRecords,
  clearDeletingRecords,
} = studentManagementSlice.actions;

export default studentManagementSlice.reducer;
