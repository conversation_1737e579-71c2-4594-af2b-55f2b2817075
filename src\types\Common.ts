import { ReactElement } from 'react';

export type FetchStatus = 'idle' | 'loading' | 'success' | 'error';

export type CreateResponse = { id: number; result?: string; feeId?: number };
export type UpdateResponse = {
  rowsAffected: number;
};
export type DeleteResponse = {
  deleted: boolean;
};

export type InvalidRecordInfo = {
  rowNumber: number;
  rowKey: string;
  errors: string;
};

export type CreateResponseMulti = {
  inserted: boolean;
  invalidRecords: InvalidRecordInfo[];
  dbResult?: string;
};

export type CreateProps = {
  onClose: () => void;
  open: () => void;
  updates?: any;
};

export type SendResponse = { result: string };
export type SubmitResponse = { id: number; result?: string };

export type MessagesTypeProps = {
  message: string | ReactElement;
  icon?: string;
  jsonIcon?: any;
  loop?: boolean;
};

export type SendPopupProps = {
  notificationId?: number | undefined;
  messageId?: number | undefined;
  voiceId?: number | undefined;
  isSubmitting: boolean;
  templateId?: number | string | undefined;
};

export type OverViewProps = {
  academicId: number;
  feeTypeId: number;
  setReceiptOpenTest?: boolean | undefined;
};

export type OutletContextType = {
  topBarHeight: number;
};