import { configureStore } from '@reduxjs/toolkit';
import layoutReducer from '@/store/Layout/layout.slice';
import drawerReducer from '@/store/Layout/drawer.slice';
import popupReducer from '@/store/Layout/popup.slice';
import logger from 'redux-logger';
import classManagementReducer from './Academics/ClassManagement/classManagement.slice';
import subjectManagementReducer from './Academics/SubjectManagement/subjectManagement.slice';
import staffManagementReducer from './StaffMangement/StaffMangement/staffMangement.slice';
import sectionManagementReducer from './Academics/SectionManagement/sectionManagement.slice';
import yearManagementReducer from './Academics/YearManagement/yearManagement.slice';
import studentManagementReducer from './Students/studentManagement.slice';
import dashboardReducer from './Dashboard/dashbaord.slice';
import attendanceMarkingReducer from './AttendanceMarking/attendanceMarking.slice';
import messageBoxReducer from './MessageBox/messageBox.slice';
import notificationReducer from './AppNotification/appNotification.slice';
import voiceReducer from './VoiceMessage/voiceMessage.slice';
import manageFeeReducer from './ManageFee/manageFee.slice';
import paymentReducer from './Payment/payment.slice';
import flushReducer from './flush.slice';
import examCenterReducer from './ExamCenter/ExamCenter.slice';
import meetingReducer from './ZoomMeeting/meeting.slice';

export const store = configureStore({
  reducer: {
    layout: layoutReducer,
    drawer: drawerReducer,
    popup: popupReducer,
    classManagement: classManagementReducer,
    sectionManagement: sectionManagementReducer,
    subjectManagement: subjectManagementReducer,
    yearManagement: yearManagementReducer,
    studentManagement: studentManagementReducer,
    staffManagement: staffManagementReducer,
    dashboard: dashboardReducer,
    attendanceMarking: attendanceMarkingReducer,
    messageBox: messageBoxReducer,
    notification: notificationReducer,
    voiceMessage: voiceReducer,
    manageFee: manageFeeReducer,
    payment: paymentReducer,
    examCenter: examCenterReducer,
    flush: flushReducer,
    meeting: meetingReducer,
  },
  middleware: (getDefaultMiddleware) => getDefaultMiddleware().concat(logger),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
export type ThunkConfig<T> = { state: RootState; dispatch: AppDispatch; extra: T };
