import Loadable from '@/components/shared/Loadable';
import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';

const MessageBox = Loadable(lazy(() => import('@/pages/MessageBox')));
const SmsTemplete = Loadable(lazy(() => import('@/features/MessageBox/SmsTemplate/SmsTemplete')));
const QuickSend = Loadable(lazy(() => import('@/features/MessageBox/QuickSend/QuickSend')));
const QuickSendNew = Loadable(lazy(() => import('@/features/MessageBox/QuickSend/QuickSend2')));
const SendToOthers = Loadable(lazy(() => import('@/features/MessageBox/SendToOthers')));
const DeliveryReport = Loadable(lazy(() => import('@/features/MessageBox/DeliveryReport/DeliveryReport')));

export const messageRoutes: RouteObject[] = [
  {
    path: '/message-box',
    element: <MessageBox />,
    children: [
      {
        path: 'quick-send',
        element: <QuickSend />,
      },
      {
        path: 'quick-send2',
        element: <QuickSendNew />,
      },
      {
        path: 'sms-template',
        element: <SmsTemplete />,
      },
      {
        path: 'send-others',
        element: <SendToOthers />,
      },
      {
        path: 'delivery-report',
        element: <DeliveryReport />,
      },
    ],
  },
];
