/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  Collapse,
  FormControl,
  Avatar,
} from '@mui/material';
import { MdAdd } from 'react-icons/md';
import styled from 'styled-components';
import SearchIcon from '@mui/icons-material/Search';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import DeleteIcon from '@mui/icons-material/Delete';
import { STATUS_SELECT } from '@/config/Selection';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import CreateMembers from './CreatePublicMembers';
// import MembersList from './MembersList';

const PublicMembersRoot = styled.div`
  padding: 16px;

  .Card {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    height: calc(100vh - 10rem);
    @media screen and (max-width: 62.25rem) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 2.5rem);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 0.0625rem solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

export const data = [
  {
    slNo: 1,
    memberNumber: '+91 9856773663',
    class: 'VII-D',
    academicYear: '2023-2024',
    memberName: 'Alex',
    group: 'Welcome to PassDaily',
  },
  {
    slNo: 2,
    memberNumber: '+91 9856773663',
    class: 'VII-D',
    academicYear: '2023-2024',
    memberName: 'Mathew',
    group: 'Password',
  },
  {
    slNo: 3,
    memberNumber: '+91 9856773663',
    class: 'VII-D',
    academicYear: '2023-2024',
    memberName: 'Peter',
    group: 'Message',
  },
  {
    slNo: 4,
    memberNumber: '+91 9856773663',
    class: 'VII-D',
    academicYear: '2023-2024',
    memberName: 'Alex Mic',
    group: 'PassDaily',
  },
  {
    slNo: 5,
    memberNumber: '+91 9856773663',
    class: 'VII-D',
    academicYear: '2023-2024',
    memberName: 'john',
    group: 'Password',
  },
  {
    slNo: 6,
    memberNumber: '+91 9856773663',
    class: 'VII-D',
    academicYear: '2023-2024',
    memberName: 'Micheal',
    group: 'Message',
  },
  {
    slNo: 7,
    memberNumber: '+91 9856773663',
    class: 'VII-D',
    academicYear: '2023-2024',
    memberName: 'jack',
    group: 'Welcome',
  },
];

function PublicMembers() {
  const [showFilter, setShowFilter] = useState(false);
  const [drawerOpen, setDrawerOpen] = useState(false);

  const handleDrawerOpen = () => {
    setDrawerOpen(true);
  };

  const getRowKey = useCallback((row: any) => row.examId, []);
  const AuthorizeKeywordsColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'slNo',
        dataKey: 'slNo',
        headerLabel: 'Sl.No',
        sortable: true,
      },
      {
        name: 'academicYear',
        dataKey: 'academicYear',
        headerLabel: 'Academic Year',
      },
      {
        name: 'group',
        dataKey: 'group',
        headerLabel: 'Group',
      },
      {
        name: 'memberName',
        headerLabel: 'Member Name',
        renderCell: (row) => {
          return (
            <Stack direction="row" gap={1} alignItems="center">
              <Avatar alt="" src="/static/images/avatar/1.jpg" />
              <Typography variant="subtitle2">{row.memberName}</Typography>
            </Stack>
          );
        },
      },

      {
        name: 'memberNumber',
        dataKey: 'memberNumber',
        headerLabel: 'Member Number',
      },

      {
        name: 'actions',
        headerLabel: 'Actions',
        renderCell: () => {
          return (
            <Stack direction="row" gap={1}>
              <IconButton size="small" color="error" sx={{ padding: 0.5 }}>
                <DeleteIcon />
              </IconButton>
            </Stack>
          );
        },
      },
    ],
    []
  );

  return (
    <Page title="List">
      <PublicMembersRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17} width="80%">
              Student Members
            </Typography>
            <Box pb={1} sx={{ flexShrink: 0 }}>
              <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                <SearchIcon />
              </IconButton>
              <Button
                onClick={handleDrawerOpen}
                sx={{ borderRadius: '1.25rem', mr: 2 }}
                variant="outlined"
                size="small"
              >
                <MdAdd size="1.25rem" /> Create
              </Button>
            </Box>
          </Stack>
          <Divider />

          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={3} pt={1} container spacing={3} alignItems="end">
                  <Grid item lg="auto" md={6} xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', lg: 250 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Group
                      </Typography>
                      <Autocomplete
                        options={STATUS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select" />}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item lg="auto" md={6} xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', lg: 250 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Autocomplete
                        options={STATUS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select" />}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item lg="auto" xs={12}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '.125rem' }}>
                      <Button variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Paper className="card-table-container" sx={{ marginTop: '.75rem' }}>
              <DataTable columns={AuthorizeKeywordsColumns} data={data} getRowKey={getRowKey} fetchStatus="success" />
            </Paper>
          </div>
        </Card>
      </PublicMembersRoot>

      <TemporaryDrawer
        onClose={() => setDrawerOpen(false)}
        state={drawerOpen}
        Title="Create Members"
        DrawerContent={<CreateMembers onCancel={() => setDrawerOpen(false)} />}
      />
    </Page>
  );
}

export default PublicMembers;
