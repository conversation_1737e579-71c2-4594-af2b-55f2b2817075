import { createSlice } from '@reduxjs/toolkit';

interface FlushState {
  [key: string]: any;
}

const initialState: FlushState = {};

const flushSlice = createSlice({
  name: 'flush',
  initialState,
  reducers: {
    flushStore: (state) => {
      // Reset the state to initial state
      Object.keys(state).forEach((key) => {
        delete state[key];
      });
      return {};
    },
  },
});

export const { flushStore } = flushSlice.actions;
export default flushSlice.reducer;
