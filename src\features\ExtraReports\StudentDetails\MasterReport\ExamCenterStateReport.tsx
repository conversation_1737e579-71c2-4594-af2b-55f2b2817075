/* eslint-disable jsx-a11y/alt-text */
import { useCallback, useMemo } from 'react';
import { Box, Grid, Card, useTheme, Chip, Paper } from '@mui/material';
import styled from 'styled-components';
import useSettings from '@/hooks/useSettings';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { ExamCenterStateReportType } from '@/types/Reports';

const ExamCenterStateReportData = [
  {
    id: 1,
    exam: 'Annual Exam',
    outOffMark: 10,
    getMark: 6,
    totalAbsent: 4,
    totalFailed: 6,
    totalPassed: 16,
  },
  {
    id: 2,
    exam: 'Annual Exam',
    outOffMark: 10,
    getMark: 6,
    totalAbsent: 4,
    totalFailed: 6,
    totalPassed: 16,
  },
  {
    id: 3,
    exam: 'Annual Exam',
    outOffMark: 10,
    getMark: 6,
    totalAbsent: 4,
    totalFailed: 6,
    totalPassed: 16,
  },

  {
    id: 4,
    exam: 'Annual Exam',
    outOffMark: 10,
    getMark: 6,
    totalAbsent: 4,
    totalFailed: 6,
    totalPassed: 16,
  },
  {
    id: 5,
    exam: 'Annual Exam',
    outOffMark: 10,
    getMark: 6,
    totalAbsent: 4,
    totalFailed: 6,
    totalPassed: 16,
  },
  {
    id: 6,
    exam: 'Annual Exam',
    outOffMark: 10,
    getMark: 6,
    totalAbsent: 4,
    totalFailed: 6,
    totalPassed: 16,
  },
  {
    id: 7,
    exam: 'Annual Exam',
    outOffMark: 10,
    getMark: 6,
    totalAbsent: 4,
    totalFailed: 6,
    totalPassed: 16,
  },
];

const ExamCenterStateReportRoot = styled.div`
  .ReportCard {
    display: flex;
    flex-direction: column;
    height: 350px;
    background-color: ${(props) => (props.theme.themeMode === 'light' ? '#f7f3fb' : props.theme.palette.grey[900])};
    /* border: 1px solid ${(props) => props.theme.palette.grey[300]}; */

    .card-main-body {
      display: flex;
      flex-direction: column;
      min-height: calc(100% - 5px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }
    }
  }
`;

function ExamCenterStateReport() {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const tableStyles = {
    '& th:nth-of-type(n+2):nth-of-type(-n+6), & td:nth-of-type(n+2):nth-of-type(-n+6)': {
      textAlign: 'center',
    },
    backgroundColor: isLight ? '#f7f3fb' : theme.palette.grey[900],
  };

  const getRowKeyExamCenterStateReport = useCallback((row: ExamCenterStateReportType) => row.id, []);

  const ExamCenterStateReportColumns: DataTableColumn<ExamCenterStateReportType>[] = useMemo(
    () => [
      {
        name: 'exam',
        dataKey: 'exam',
        headerLabel: 'Exam',
      },
      {
        name: 'outOffMark',
        dataKey: 'outOffMark',
        headerLabel: 'Out Off Mark',
      },
      {
        name: 'getMark',
        dataKey: 'getMark',
        headerLabel: 'Get Mark',
      },
      {
        name: 'totalAbsent',
        dataKey: 'totalAbsent',
        headerLabel: 'Total Absent',
      },
      {
        name: 'totalFailed',
        dataKey: 'totalFailed',
        headerLabel: 'Total Failed',
      },
      {
        name: 'totalPassed',
        dataKey: 'totalPassed',
        headerLabel: 'Total Passed',
      },
    ],
    []
  );

  return (
    <ExamCenterStateReportRoot>
      <Grid container spacing={3}>
        <Grid item xl={12} lg={12} xs={12}>
          <Card className="ReportCard" sx={{ mt: 3, boxShadow: 0, px: { xs: 3, md: 2 }, py: { xs: 2, md: 2 } }}>
            <div className="card-main-body">
              <Box>
                <Chip
                  label="Exam Center State"
                  variant="filled"
                  sx={{
                    height: 25,
                    background: isLight ? theme.palette.common.black : theme.palette.common.white,
                    color: isLight ? theme.palette.common.white : theme.palette.common.black,
                  }}
                />
              </Box>
              <Paper className="card-table-container">
                <DataTable
                  columns={ExamCenterStateReportColumns}
                  data={ExamCenterStateReportData}
                  getRowKey={getRowKeyExamCenterStateReport}
                  fetchStatus="success"
                  tableStyles={tableStyles}
                />
              </Paper>
            </div>
          </Card>
        </Grid>
      </Grid>
    </ExamCenterStateReportRoot>
  );
}

export default ExamCenterStateReport;
