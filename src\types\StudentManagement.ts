import { FetchStatus } from './Common';
import { PageRequestWithFilters, PagedData, PaginationInfo } from './Pagination';

export type StudentListFilters = {
  adminId: number;
  academicId: number | string;
  classId: number;
  studentName: string;
  studentGender: number;
  admissionDate: string;
  admissionNumber: string;
  studentDob: string;
  studentFatherName: string;
  studentFatherNumber: string;
  studentBloodGroup: string;
  studentCaste: string;
};

export type StudentListInfo = {
  studentId?: number | undefined;
  academicId: number;
  classId: number;
  studentName: string;
  className?: string;
  academicTime?: string;
  admissionNumber: string;
  admissionDate: string;
  studentGender: number;
  studentDOB: string;
  studentLastStudied: string;
  studentBloodGroup: string;
  studentBirthPlace: string;
  studentNationality: string;
  studentMotherTongue: string;
  studentReligion: string;
  studentCaste: string;
  studentPAddress: string;
  studentCAddress: string;
  studentFatherName: string;
  studentFatherQualification: string;
  studentFatherOccupation: string;
  studentFatherNumber: string;
  studentEmailId: string;
  studentMotherName: string;
  studentMotherQualification: string;
  studentMotherOccupation: string;
  studentMotherNumber: string;
  studentImage: string;
  createdBy: number;
};

export type QuickUpdateStudentListInfo = {
  studentId: number;
  studentRollNo?: number;
  admissionNumber: string;
  studentName: string;
  gender: number;
  fatherName: string;
  fatherNumber: string;
  motherName: string;
  motherNumber: string;
  dbResult?: string;
};
export type StudentListRequest = PageRequestWithFilters<StudentListFilters>;
export type StudentListPagedData = PagedData<StudentListInfo>;

export type StudentManagementState = {
  studentList: {
    [key: string]: any;
    status: FetchStatus;
    data: StudentListInfo[];
    error: string | null;
    pageInfo: PaginationInfo;
    sortColumn: string;
    sortDirection: 'asc' | 'desc';
  };
  quickUpdateStudentList: {
    status: FetchStatus;
    data: QuickUpdateStudentListInfo[];
    error: string | null;
  };
  submitting: boolean;
  deletingRecords: Record<number, boolean>;
  error: string | null | undefined;
};

export type StudentCreateRequest = Omit<StudentListInfo, 'studentId'>;

export type StudentCreateRow = { rowKey: string } & StudentCreateRequest;
