/* eslint-disable jsx-a11y/alt-text */
import { useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Avatar,
  Autocomplete,
  Box,
  Divider,
  Grid,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  useTheme,
  IconButton,
  Collapse,
  Tooltip,
  FormControl,
  Chip,
} from '@mui/material';
import styled from 'styled-components';
import { CLASS_SELECT, STATUS_SELECT, YEAR_SELECT } from '@/config/Selection';
import SearchIcon from '@mui/icons-material/Search';
import Popup from '@/components/shared/Popup/Popup';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import useSettings from '@/hooks/useSettings';
import { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import MenuEditDelete from '@/components/shared/Selections/MenuEditDelete';
import { purplePreset } from '@/utils/Colors';
import QuickReport from './QuickReport';

export const data = [
  {
    className: 'VII-B',
    name: 'Ajesh',
    academicYear: '2023-2024',
    adm_date: '12/06/2023',
    admissionNumber: '98623',
    gardianName: 'Parent',
    gardianNumber: 64106464513,
    status: 'Published',
  },
  {
    className: 'VI-C',
    name: 'David',
    meetingID: 'IDGHDGB',
    academicYear: '2023-2024',
    adm_date: '12/06/2023',
    admissionNumber: '45678',
    gardianName: 'Parent',
    gardianNumber: 64106464513,
    status: 'Published',
  },
  {
    className: 'XII-B',
    name: 'Peter',
    meetingID: 'IDGHDGB',
    academicYear: '2023-2024',
    adm_date: '12/06/2023',
    admissionNumber: '98650',
    gardianName: 'Parent',
    gardianNumber: 64106464513,
    status: 'Published',
  },
  {
    className: 'V-A',
    name: 'Christopher',
    meetingID: 'IDGHDGB',
    academicYear: '2023-2024',
    adm_date: '12/06/2023',
    admissionNumber: '63456',
    gardianName: 'Parent',
    gardianNumber: 64106464513,
    status: 'Published',
  },
  {
    className: 'II-B',
    name: 'john',
    meetingID: 'IDGHDGB',
    academicYear: '2023-2024',
    adm_date: '12/06/2023',
    admissionNumber: '76543',
    gardianName: 'Parent',
    gardianNumber: 64106464513,
    status: 'Published',
  },
  {
    className: 'XII-C',
    name: 'Micheal',
    meetingID: 'IDGHDGB',
    academicYear: '2023-2024',
    adm_date: '12/06/2023',
    admissionNumber: '88999',
    gardianName: 'Parent',
    gardianNumber: 64106464513,
    status: 'Published',
  },
  {
    className: 'I-B',
    name: 'jack',
    meetingID: 'IDGHDGB',
    academicYear: '2023-2024',
    adm_date: '12/06/2023',
    admissionNumber: '67888',
    gardianName: 'Parent',
    gardianNumber: 64106464513,
    status: 'Published',
  },
  {
    className: 'VII-A',
    name: 'Ajesh',
    meetingID: 'IDGHDGB',
    academicYear: '2023-2024',
    adm_date: '12/06/2023',
    admissionNumber: '45623',
    gardianName: 'Parent',
    gardianNumber: 64106464513,
    status: 'Published',
  },
  {
    className: 'VI-C',
    name: 'Ajesh',
    meetingID: 'IDGHDGB',
    academicYear: '2023-2024',
    adm_date: '12/06/2023',
    admissionNumber: '65474',
    gardianName: 'Parent',
    gardianNumber: 64106464513,
    status: 'Published',
  },
  {
    className: 'X-B',
    name: 'Ajesh',
    meetingID: 'IDGHDGB',
    academicYear: '2023-2024',
    adm_date: '12/06/2023',
    admissionNumber: '1245',
    gardianName: 'Parent',
    gardianNumber: 64106464513,
    status: 'Published',
  },
];

const StudentDetailsRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  .student_card {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  }

  .Card {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%; 
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 40px);
      flex-grow: 1;

      .card-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: auto;
        ::-webkit-scrollbar {
          width: 0px;
        }
      }
    }
  }
`;

function StudentDetails({ handleMasterReport }) {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const [showFilter, setShowFilter] = useState(false);
  const [Delete, setDelete] = useState(false);
  const [quickReport, setQuickReport] = useState(false);

  const handleClickDelete = () => setDelete(true);
  const handleClickCloseDelete = () => setDelete(false);

  const StudentDetailsColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'studentName',
        headerLabel: 'Student',
        renderCell: (row) => {
          return (
            <Stack direction="row" gap={0.5} alignItems="center">
              <Typography variant="subtitle2">:</Typography>
              <Avatar alt="" src="" />
              <Typography variant="subtitle2">{row.name}</Typography>
            </Stack>
          );
        },
      },
      {
        name: 'className',
        dataKey: 'className',
        headerLabel: 'Class',
      },
      {
        name: 'academicYear',
        dataKey: 'academicYear',
        headerLabel: 'Academic',
      },
      {
        name: 'admissionNumber',
        headerLabel: 'Adm.No',
        renderCell: (row) => {
          return (
            <Stack direction="row" alignItems="center" maxWidth="90%" gap={0.5}>
              <Typography variant="subtitle2">:</Typography>
              <Chip
                size="small"
                label={row.admissionNumber}
                variant="outlined"
                sx={{
                  border: '0px',
                  backgroundColor: isLight ? purplePreset.lighter : '',
                  color: isLight ? purplePreset.main : '',
                  width: 'auto',
                }}
              />
            </Stack>
          );
        },
      },
      {
        name: 'admissionNumber',
        dataKey: 'adm_date',
        headerLabel: 'Adm.Date',
      },

      {
        name: 'guardian',
        dataKey: 'gardianName',
        headerLabel: 'Guardian',
      },
      {
        name: 'guardianNumber',
        dataKey: 'gardianNumber',
        headerLabel: 'Number',
      },
    ],
    []
  );

  return (
    <Page title="Student Details">
      <StudentDetailsRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17} width="100%">
              Student Details
            </Typography>
            <Box pb={1} sx={{ flexShrink: 0 }}>
              <Tooltip title="Search">
                <IconButton aria-label="" color="primary" sx={{ mr: 1 }} onClick={() => setShowFilter((x) => !x)}>
                  <SearchIcon />
                </IconButton>
              </Tooltip>
              {/* <Button
                sx={{ borderRadius: '20px' }}
                size="small"
                variant="outlined"
                onClick={() => setCreatePopup(true)}
              >
                <MdAdd size="20px" /> Create
              </Button> */}
            </Box>
          </Stack>
          <Stack>
            <Divider />
          </Stack>
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={2}>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Autocomplete
                        options={YEAR_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select year" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Autocomplete
                        options={CLASS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select class" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" md={6} xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', lg: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Status
                      </Typography>
                      <Autocomplete
                        options={STATUS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select" />}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item lg={1} xs={12}>
                    <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                      <Button variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                      {/* <Button variant="contained" color="primary" fullWidth>
                          Search
                        </Button> */}
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Box className="card-container" my={2}>
              <Grid container spacing={2}>
                {data.map((student, rowIndex) => (
                  <Grid item xl={4} lg={6} md={12} sm={6} xs={12} key={rowIndex}>
                    <Card
                      className="student_card"
                      sx={{ p: 2, boxShadow: 0, border: `1px solid ${theme.palette.grey[300]}` }}
                    >
                      <Stack direction="row" alignItems="top" justifyContent="space-between">
                        <Box flexGrow={1}>
                          {StudentDetailsColumns.map((item, index) => (
                            <Stack direction="row" key={index}>
                              <Grid container>
                                <Grid item lg={5} xs={6}>
                                  <Typography key={item.name} variant="subtitle1" fontSize={13} mr={2}>
                                    {item.headerLabel}
                                  </Typography>
                                </Grid>
                                <Grid item lg={7} xs={6} mb={0.5} mt="auto">
                                  <Typography variant="h6" fontSize={13}>
                                    {item.dataKey
                                      ? `:${' '}${(student as { [key: string]: any })[item.dataKey ?? '']}`
                                      : item && item.renderCell && item.renderCell(student, rowIndex)}
                                  </Typography>
                                </Grid>
                              </Grid>
                            </Stack>
                          ))}
                        </Box>
                        <MenuEditDelete
                          Edit={() => {
                            return 0;
                          }}
                          Delete={handleClickDelete}
                        />
                      </Stack>
                      <Stack direction="row" mt={2} justifyContent="space-around">
                        <Button color="error" variant="contained" size="small" onClick={() => setQuickReport(true)}>
                          Quick Report
                        </Button>
                        <Button
                          color="warning"
                          variant="contained"
                          size="small"
                          onClick={() => handleMasterReport(student)}
                        >
                          Master Report
                        </Button>
                      </Stack>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Box>
          </div>
        </Card>
      </StudentDetailsRoot>
      <Popup
        size="xs"
        state={Delete}
        onClose={handleClickCloseDelete}
        popupContent={<DeleteMessage message="Are you sure want to delete?" />}
      />
      <QuickReport size="sm" state={quickReport} onClose={() => setQuickReport(false)} />
    </Page>
  );
}

export default StudentDetails;
