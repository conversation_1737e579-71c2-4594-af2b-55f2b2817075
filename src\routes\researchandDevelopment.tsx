/* eslint-disable prettier/prettier */
import Loadable from '@/components/shared/Loadable';
import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';

const ResearchandDevelopment = Loadable(lazy(() => import('@/pages/Research&Development')));
const RND = Loadable(lazy(() => import('@/components/shared/RND/RND')));

export const researchandDevelopmentRoutes: RouteObject[] = [
  {
    path: '/research&development',
    element: <ResearchandDevelopment />,
    children: [
      {
        path: 'rnd',
        element: <RND />,
      },
    ],
  },
];
