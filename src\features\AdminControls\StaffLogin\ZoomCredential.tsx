import { useState } from 'react';
import {
  <PERSON>ton,
  TextField,
  Typography,
  Box,
  Stack,
  FormControl,
  IconButton,
  useTheme,
  Grid,
  Autocomplete,
} from '@mui/material';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { AlbumImageInfo } from '@/types/Album';
import PrgressUploadImage from '@/components/shared/Progress';
import { TbCloudUpload } from 'react-icons/tb';
import LoadingButton from '@mui/lab/LoadingButton';
import CloseIcon from '@mui/icons-material/Close';

// Remove unused imports: LoadingButton, TbCloudUpload

export type ZoomCredentialProps = {
  onSave: (values: AlbumImageInfo) => void;
  onCancel: () => void;
  onClose: () => void;
  albumInfo?: AlbumImageInfo;
  isSubmitting: boolean;
};

function ZoomCredential({ onSave, onCancel, isSubmitting, albumInfo }: ZoomCredentialProps) {
  return (
    <Box mt={1} padding="1.5rem">
      <form noValidate>
        <Grid container spacing={3}>
          <Grid item md={6} xs={12}>
            <Typography variant="h6" fontSize={14}>
              Teacher Name
            </Typography>
            <TextField fullWidth />
          </Grid>
          <Grid item md={6} xs={12}>
            <Typography variant="h6" fontSize={14}>
              User Name
            </Typography>
            <TextField fullWidth />
          </Grid>
          <Grid item md={6} xs={12}>
            <Typography variant="h6" fontSize={14}>
              Password
            </Typography>
            <TextField fullWidth />
          </Grid>
          <Grid item md={6} xs={12}>
            <Typography variant="h6" fontSize={14}>
              App Key
            </Typography>
            <TextField fullWidth />
          </Grid>
          <Grid item md={6} xs={12}>
            <Typography variant="h6" fontSize={14}>
              App Secret Key
            </Typography>
            <TextField fullWidth />
          </Grid>
          <Grid item md={6} xs={12}>
            <Typography variant="h6" fontSize={14}>
              JWT Token
            </Typography>
            <TextField fullWidth />
          </Grid>
          <Grid item md={6} xs={12}>
            <Typography variant="h6" fontSize={14}>
              Access Token
            </Typography>
            <TextField fullWidth />
          </Grid>

          <Grid item md={6} xs={12}>
            <Typography variant="h6" fontSize={14}>
              User Id
            </Typography>
            <TextField fullWidth />
          </Grid>
          <Grid item md={6} xs={12}>
            <Typography variant="h6" fontSize={14}>
              Field 1
            </Typography>
            <TextField fullWidth />
          </Grid>
          <Grid item md={6} xs={12}>
            <Typography variant="h6" fontSize={14}>
              Field 2
            </Typography>
            <TextField fullWidth />
          </Grid>
          <Grid item md={12} xs={12}>
            <Typography variant="h6" fontSize={14}>
              Status
            </Typography>
            <Autocomplete options={['Active', 'Inactive']} renderInput={(params) => <TextField {...params} />} />
          </Grid>
        </Grid>
        <Stack spacing={3} direction="row" mt={3} justifyContent="end">
          <Button onClick={onCancel} variant="contained" color="secondary" type="button">
            Cancel
          </Button>
          <LoadingButton
            loading={isSubmitting}
            variant="contained"
            color="primary"
            type="submit"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Saving...' : 'Save'}
          </LoadingButton>
        </Stack>
      </form>
    </Box>
  );
}

export default ZoomCredential;
