import { PayloadAction, createSlice } from '@reduxjs/toolkit';
import type { YearManagementState } from '@/types/AcademicManagement';
import { flushStore } from '@/store/flush.slice';
import { OutletContextType } from '@/types/Common';
import {
  fetchYearList,
  addNewYear,
  updateYear,
  deleteYear,
  // fetchYearLists,
} from './yearManagement.thunks';

const initialState: YearManagementState = {
  yearList: {
    status: 'idle',
    data: [],
    error: null,
    pageInfo: {
      totalrecords: 0,
      pagesize: 20,
      pagenumber: 1,
      totalpages: 0,
      remainingpages: 0,
    },
    sortColumn: 'academicId',
    sortDirection: 'asc',
  },
  submitting: false,
  deletingRecords: {},
  error: null,
};

const yearManagementSlice = createSlice({
  name: 'yearManagement',
  initialState,
  reducers: {
    setYearListPage: (state, action: PayloadAction<number>) => {
      state.yearList.pageInfo.pagenumber = action.payload;
    },
    setYearListPageSize: (state, action: PayloadAction<number>) => {
      state.yearList.pageInfo.pagenumber = 1;
      state.yearList.pageInfo.pagesize = action.payload;
    },
    setSortColumn: (state, action: PayloadAction<string>) => {
      state.yearList.pageInfo.pagenumber = 1;
      state.yearList.sortColumn = action.payload;
      state.yearList.sortDirection = 'asc';
    },
    setSortDirection: (state, action: PayloadAction<'asc' | 'desc'>) => {
      state.yearList.pageInfo.pagenumber = 1;
      state.yearList.sortDirection = action.payload;
    },
    setSortColumnAndDirection: (state, action: PayloadAction<{ column: string; direction: 'asc' | 'desc' }>) => {
      state.yearList.pageInfo.pagenumber = 1;
      state.yearList.sortColumn = action.payload.column;
      state.yearList.sortDirection = action.payload.direction;
    },
    setSubmitting: (state, action: PayloadAction<boolean>) => {
      state.submitting = action.payload;
    },
    setDeletingRecords: (state, action: PayloadAction<{ recordId: number }>) => {
      state.deletingRecords[action.payload.recordId] = true;
    },
    clearDeletingRecords: (state, action: PayloadAction<{ recordId: number }>) => {
      delete state.deletingRecords[action.payload.recordId];
    },
  },
  extraReducers: (builder) => {
    builder
      //   .addCase(fetchYearLists.pending, (state) => {
      //     state.yearList.status = 'loading';
      //     state.yearList.error = null;
      //   })
      //   .addCase(fetchYearLists.fulfilled, (state, action) => {
      //     const data = action.payload;
      //     state.yearList.status = 'success';
      //     state.yearList.data = data;
      //     state.yearList.error = null;
      //     // state.yearList.pageInfo = pageInfo;
      //   })
      //   .addCase(fetchYearLists.rejected, (state, action) => {
      //     state.yearList.status = 'error';
      //     state.yearList.error = action.payload || 'Unknown error in fetching year list';
      //   })

      .addCase(fetchYearList.pending, (state) => {
        state.yearList.status = 'loading';
        state.yearList.error = null;
      })
      .addCase(fetchYearList.fulfilled, (state, action) => {
        const { data, ...pageInfo } = action.payload;
        state.yearList.status = 'success';
        state.yearList.data = data;
        state.yearList.error = null;
        state.yearList.pageInfo = pageInfo;
      })
      .addCase(fetchYearList.rejected, (state, action) => {
        state.yearList.status = 'error';
        state.yearList.error = action.payload || 'Unknown error in fetching year list';
      })

      .addCase(addNewYear.pending, (state) => {
        state.submitting = true;
        state.error = null;
      })
      .addCase(addNewYear.fulfilled, (state) => {
        state.submitting = false;
        state.error = null;
      })
      .addCase(addNewYear.rejected, (state, action) => {
        state.submitting = false;
        state.error = action.error.message;
      })

      .addCase(updateYear.pending, (state) => {
        state.submitting = true;
        state.error = null;
      })
      .addCase(updateYear.fulfilled, (state, action) => {
        state.submitting = false;
        state.error = null;
        const dataIndex = state.yearList.data.findIndex((x) => x.academicId === action.meta.arg.academicId);
        if (dataIndex > -1) {
          state.yearList.data[dataIndex] = action.meta.arg;
        }
      })
      .addCase(updateYear.rejected, (state, action) => {
        state.submitting = false;
        state.error = action.error.message;
      })

      .addCase(deleteYear.pending, (state, action) => {
        state.deletingRecords[action.meta.arg] = true;
        state.error = null;
      })
      .addCase(deleteYear.fulfilled, (state, action) => {
        if (action.payload.deleted) {
          const { [action.meta.arg]: deletedRecord, ...restDeletingRecords } = state.deletingRecords;
          state.deletingRecords = restDeletingRecords;
        }
        state.error = null;
      })
      .addCase(deleteYear.rejected, (state, action) => {
        state.deletingRecords = { ...initialState.deletingRecords };
        state.error = action.error.message;
      })
      .addCase(flushStore, () => initialState);
  },
});

export const {
  setYearListPage,
  setYearListPageSize,
  setSortColumn,
  setSortDirection,
  setSortColumnAndDirection,
  setSubmitting,
  setDeletingRecords,
  clearDeletingRecords,
} = yearManagementSlice.actions;

export default yearManagementSlice.reducer;
