/* eslint-disable jsx-a11y/alt-text */
import React, { useState } from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Typography,
  Card,
  IconButton,
  Tooltip,
  Select,
  MenuItem,
  Collapse,
  FormControl,
} from '@mui/material';
import styled from 'styled-components';
import { CLASS_SELECT, YEAR_SELECT, YEAR_SELECT_OPTIONS } from '@/config/Selection';
import { smsrows } from '@/config/smsData';
import DeleteIcon from '@mui/icons-material/Delete';
import SearchIcon from '@mui/icons-material/Search';
import { IoIosArrowUp } from 'react-icons/io';
import Popup from '@/components/shared/Popup/Popup';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import DatePickers from '@/components/shared/Selections/DatePicker';
import StaffActivtyTabPanel from './Tab';

const StaffActivityReportRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  h6 {
    font-weight: 600;
  }
  .MuiTextField-root:hover .MuiInputBase-input {
    cursor: text;
  }
`;

function StaffActivityReport() {
  const [Delete, setDelete] = React.useState(false);
  const [showFilter, setShowFilter] = useState(false);

  const handleClickDelete = () => setDelete(true);
  const handleClickCloseDelete = () => setDelete(false);
  return (
    <Page title="Manage Year">
      <StaffActivityReportRoot>
        <Card elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
            flexWrap="wrap"
            className="top-head"
          >
            <Typography variant="h6" fontSize={17}>
              Staff Activity Report
            </Typography>
            <Box
              pb={1}
              sx={{ flexShrink: 0 }}
              display="flex"
              alignItems="center"
              justifyContent="end"
              flex="1"
              // flexDirection={{ xs: 'column', sm: 'row' }}
            >
              {showFilter === false ? (
                <Tooltip title="Search">
                  <IconButton
                    aria-label="delete"
                    color="primary"
                    sx={{ mr: { xs: 0, sm: 1 } }}
                    onClick={() => setShowFilter((x) => !x)}
                  >
                    <SearchIcon />
                  </IconButton>
                </Tooltip>
              ) : (
                <IconButton
                  aria-label="delete"
                  color="primary"
                  sx={{ mr: { xs: 0, sm: 1 } }}
                  onClick={() => setShowFilter((x) => !x)}
                >
                  <IoIosArrowUp />
                </IconButton>
              )}

              {/* <Button sx={{ borderRadius: '20px' }} variant="contained" size="small" onClick={handleCreateMessage}>
                <MdAdd size="20px" />
                Create
              </Button> */}
            </Box>
          </Stack>
          <Divider sx={{ marginBottom: 1 }} />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={4} container spacing={2} alignItems="end">
                  <Grid item lg="auto" md={6} sm={6} xs={12}>
                    <FormControl fullWidth sx={{ width: { xs: '100%', lg: 180 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Year
                      </Typography>
                      <Select
                        labelId="messageTypeFilter"
                        id="messageTypeFilterSelect"
                        // value={messageTypeFilter?.toString() || '-1'}
                        // onChange={handleTypeChange}
                      >
                        {YEAR_SELECT_OPTIONS.map((opt) => (
                          <MenuItem key={opt.id} value={opt.id}>
                            {opt.year}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" md={6} sm={6} xs={12}>
                    <FormControl fullWidth sx={{ width: { xs: '100%', lg: 180 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Select Techer
                      </Typography>
                      <Select
                        labelId="messageTypeFilter"
                        id="messageTypeFilterSelect"
                        // value={messageTypeFilter?.toString() || '-1'}
                        // onChange={handleTypeChange}
                      >
                        {YEAR_SELECT_OPTIONS.map((opt) => (
                          <MenuItem key={opt.id} value={opt.id}>
                            {opt.year}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" md={6} sm={6} xs={12}>
                    <FormControl fullWidth sx={{ width: { xs: '100%', lg: 180 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        From
                      </Typography>
                      <DatePickers
                        name="messageDateFilter"
                        // value={dayjs(messageDateFilter, 'DD/MM/YYYY')}
                        onChange={(e) => {
                          const formattedDate = e ? e.format('YYYY-MM-DD') : '';
                          // setMessageDateFilter(formattedDate);
                          console.log('date::::', formattedDate);
                          // const formattedDate = e ? e.format('YYYY-MM-DD') : '';
                          // setSelectedDate(formattedDate);
                          // loadMessageTempList({ ...currentMessageTempRequest, messageDate: formattedDate });
                        }}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" md={6} sm={6} xs={12}>
                    <FormControl fullWidth sx={{ width: { xs: '100%', lg: 180 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        To
                      </Typography>
                      <DatePickers
                        name="messageDateFilter"
                        // value={dayjs(messageDateFilter, 'DD/MM/YYYY')}
                        onChange={(e) => {
                          const formattedDate = e ? e.format('YYYY-MM-DD') : '';
                          // setMessageDateFilter(formattedDate);
                          console.log('date::::', formattedDate);
                          // const formattedDate = e ? e.format('YYYY-MM-DD') : '';
                          // setSelectedDate(formattedDate);
                          // loadMessageTempList({ ...currentMessageTempRequest, messageDate: formattedDate });
                        }}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto">
                    <Stack direction="row" sx={{ marginBottom: '2px' }}>
                      <Button type="reset" variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Box>
              <StaffActivtyTabPanel />
            </Box>
          </div>
        </Card>
      </StaffActivityReportRoot>
      <Popup
        size="xs"
        state={Delete}
        onClose={handleClickCloseDelete}
        popupContent={<DeleteMessage message="Are you sure want to delete?" />}
      />
    </Page>
  );
}

export default StaffActivityReport;
