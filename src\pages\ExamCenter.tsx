import * as React from 'react';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';
import Page from '@/components/shared/Page';
import styled from 'styled-components';
import AddOnlineMarks from '@/features/ExamCenter/AddOnlineMarks';
import ManageExam from '@/features/ExamCenter/ExamManage/ManageExam';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import ExamTimetable from '@/features/ExamCenter/ExamTimetable/ExamTimetable';
import MarkRegister from '@/features/ExamCenter/MarkRegister/MarkRegister';
import DeleteMarks from '@/features/ExamCenter/DeleteMarks';
import SendTimetable from '@/features/ExamCenter/SendTimetable';
import ProgressReport from '@/features/ExamCenter/ProgressReport/Index';
import SendProgressReportIndex from '@/features/ExamCenter/SendProgressReport/Index';
import { Link, useLocation, useOutletContext } from 'react-router-dom';
import MarkRegisterCE from '@/features/ExamCenter/MarkRegisterCE/MarkRegisterCE';
import { useDispatch } from 'react-redux';
import { flushStore } from '@/store/flush.slice';
import { OutletContextType } from '@/types/Common';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const ExamCenterRoot = styled.div`
  min-height: calc(100vh - ${TOP_BAR_HEIGHT});

  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};

  .tab {
    background-color: ${(props) => (props.theme.themeMode === 'light' ? '#fff' : props.theme.palette.grey[800])};
    @media screen and (min-width: 1250px) {
      width: 100%;
    }
    @media screen and (max-width: 720px) {
      width: 100%;
    }
  }
  .Mui-selected {
    color: ${(props) => props.theme.palette.primary.main};
  }
`;

function TabPanel({ children, value, index }: TabPanelProps) {
  return (
    <Box
      sx={{ paddingTop: '3rem' }}
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
    >
      {value === index && <Box>{children}</Box>}
    </Box>
  );
}

function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`,
  };
}

export default function ExamCenter() {
  const { topBarHeight } = useOutletContext<OutletContextType>();
  const location = useLocation();
  const [value, setValue] = React.useState(0);

  const dispatch = useDispatch();

  React.useEffect(() => {
    dispatch(flushStore());
    console.log('Flush Store');
  }, [dispatch, value]);

  React.useEffect(() => {
    const currentPath = location.pathname;
    const routePaths = [
      '/exam-center/online-marks',
      '/exam-center/list',
      '/exam-center/timetable',
      '/exam-center/send-timetable',
      '/exam-center/mark-register',
      '/exam-center/mark-register-ce',
      '/exam-center/delete-marks',
      '/exam-center/progress-report',
      '/exam-center/send-progress-report',
    ];
    const index = routePaths.indexOf(currentPath);
    if (index !== -1) {
      setValue(index);
    }
  }, [location]);

  return (
    <Page title="Time Table">
      <ExamCenterRoot>
        <Tabs
          className="tab"
          variant="scrollable"
          scrollButtons
          allowScrollButtonsMobile
          value={value}
          aria-label="basic tabs"
          sx={{
            ml: '0px',
            borderBottom: 1,
            borderColor: 'divider',
            position: 'fixed',
             top: `${topBarHeight}px`,
            zIndex: 1,
            // width: '100%',
          }}
        >
          <Tab {...a11yProps(1)} label="Map Online Marks" component={Link} to="/exam-center/online-marks" />
          <Tab {...a11yProps(1)} label="Manage Exams" component={Link} to="/exam-center/list" />
          <Tab {...a11yProps(2)} label="Exam Timetable" component={Link} to="/exam-center/timetable" />
          <Tab {...a11yProps(3)} label="Send Timetable" component={Link} to="/exam-center/send-timetable" />
          <Tab {...a11yProps(4)} label="Marks Register" component={Link} to="/exam-center/mark-register" />
          <Tab {...a11yProps(5)} label="Marks Register CE" component={Link} to="/exam-center/mark-register-ce" />
          <Tab {...a11yProps(6)} label="Delete Marks" component={Link} to="/exam-center/delete-marks" />
          <Tab {...a11yProps(7)} label="Progress Report" component={Link} to="/exam-center/progress-report" />
          <Tab {...a11yProps(8)} label="Send Progress Report" component={Link} to="/exam-center/send-progress-report" />
        </Tabs>
        <TabPanel value={value} index={0}>
          <AddOnlineMarks />
        </TabPanel>
        <TabPanel value={value} index={1}>
          <ManageExam />
        </TabPanel>
        <TabPanel value={value} index={2}>
          <ExamTimetable />
        </TabPanel>
        <TabPanel value={value} index={3}>
          <SendTimetable />
        </TabPanel>
        <TabPanel value={value} index={4}>
          <MarkRegister />
        </TabPanel>
        <TabPanel value={value} index={5}>
          <MarkRegisterCE />
        </TabPanel>
        <TabPanel value={value} index={6}>
          <DeleteMarks />
        </TabPanel>
        <TabPanel value={value} index={7}>
          <ProgressReport />
        </TabPanel>
        <TabPanel value={value} index={8}>
          <SendProgressReportIndex />
        </TabPanel>
      </ExamCenterRoot>
    </Page>
  );
}
