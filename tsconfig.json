{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "lib": ["DOM", "DOM.Iterable", "ESNext"], "allowJs": false, "skipLibCheck": true, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "Node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": "./", "paths": {"@/*": ["src/*"]}}, "include": ["vite.config.ts", "src", "src/pages/ManageStudents.tsx", "src/components/shared/MessageTempletes/ClassDivision.tsx", "src/features/FeeAlert/FeeAlertList.tsx", "src/pages/SchoolCalendar.tsx", "src/routes/socialRoutes.tsx", "src/Parent-Side/pages/Reports.tsx"], "references": [{"path": "./tsconfig.node.json"}]}