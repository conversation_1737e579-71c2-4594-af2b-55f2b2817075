import { PayloadAction, createSlice } from '@reduxjs/toolkit';
import {
  StudentTermFeeStatusNewType,
  CreateBasicFeeSettingDataType,
  CreateBasicFeeSettingTitleDataType,
  CreateTermFeeSettingDataType,
  CreateTermFeeSettingTitleDataType,
  ManageFeeState,
  StudentTermFeePayType,
  CreateScholarshipFeeSettingDataType,
  GetFineListDataType,
} from '@/types/ManageFee';
import {
  CheckReceiptNo,
  CreateEditFineList,
  DeleteAllBasicFee,
  DeleteAllBusMappedStudent,
  DeleteAllOptionalFee,
  DeleteAllScholarshipMapped,
  DeleteAllTermFee,
  DeleteBasicFee,
  DeleteBusMapped,
  DeleteFineList,
  DeleteFineMap,
  DeleteOptionalFee,
  DeleteScholarship,
  DeleteScholarshipMapped,
  DeleteTermFee,
  ReceiptApprove,
  ReceiptCancel,
  createBasicFeeSetting,
  createBasicFeeSettingTitle,
  createFineMap,
  createScholarshipFeeSetting,
  createScholarshipSettings,
  createStopMapping,
  createTermFeeSetting,
  createTermFeeSettingTitle,
  deleteBasicFeeList,
  deleteTermFeeList,
  editBasicFeeList,
  editTermFeeList,
  fetchBasicFeeFilter,
  fetchBasicFeeList,
  fetchClassList,
  fetchClassSections,
  fetchFeeDateSettings,
  fetchFeeOverviewChart,
  fetchFeeOverviewModeList,
  fetchFeeOverviewPaidList,
  fetchFeeOverviewStatus,
  fetchFeePaidBasicList,
  fetchFeePaidList,
  fetchFeePaidTermList,
  fetchFeePendingBasicList,
  fetchFeePendingList,
  fetchFeePendingTermList,
  fetchFineList,
  fetchFineMappingList,
  fetchGetOptionalFeeSettings,
  fetchGetOptionalFeeSettingsIndividual,
  fetchGetTermFee,
  fetchReceiptForPrint,
  fetchScholarshipFeeList,
  fetchScholarshipSettings,
  fetchStopMappingSettings,
  fetchStudentPickerData,
  fetchStudentTermFeeStatus,
  fetchStudentTermFeeStatusNew,
  fetchStudentsFeeStatus,
  fetchStudentsFilter,
  fetchTermFeeFilter,
  fetchTermFeeList,
  optionalFeeSettings,
  optionalFeeSettingsIndividual,
  studentTermFeePay,
} from './manageFee.thunks';
import { flushStore } from '../flush.slice';

const initialState: ManageFeeState = {
  feeSettingsList: {
    data: [],
    status: 'idle',
    error: null,
  },
  classSectionsList: {
    data: [],
    status: 'idle',
    error: null,
  },
  feeDateSettingsList: {
    data: [],
    status: 'idle',
    error: null,
  },
  termFeeSettingsList: {
    data: [],
    status: 'idle',
    error: null,
  },
  classList: {
    data: [],
    status: 'idle',
    error: null,
  },
  studentsFeeStatusList: {
    data: [],
    status: 'idle',
    error: null,
  },
  studentTermFeeStatusList: {
    data: [],
    status: 'idle',
    error: null,
  },
  studentTermFeeStatusNewList: {
    data: null,
    status: 'idle',
    error: null,
  },
  studentTermFeePayDatas: {
    data: [],
    status: 'idle',
    error: null,
  },
  createBasicFeeSettingList: {
    data: [],
    status: 'idle',
    error: null,
  },
  createTermFeeSettingList: {
    data: [],
    status: 'idle',
    error: null,
  },
  createBasicFeeSettingTitleList: {
    data: [],
    status: 'idle',
    error: null,
  },
  createTermFeeSettingTitleList: {
    data: [],
    status: 'idle',
    error: null,
  },
  optionalFeeSettingsList: {
    data: [],
    status: 'idle',
    error: null,
  },
  individualFeeSettingsList: {
    data: [],
    status: 'idle',
    error: null,
  },
  createOptionalFeeSettingsList: {
    data: [],
    status: 'idle',
    error: null,
  },
  createIndividualFeeSettingsList: {
    data: [],
    status: 'idle',
    error: null,
  },
  createScholarshipFeeSettingList: {
    data: null,
    status: 'idle',
    error: null,
  },
  ScholarshipFeeList: {
    data: null,
    status: 'idle',
    error: null,
  },
  scholarshipSettingsList: {
    data: [],
    status: 'idle',
    error: null,
  },
  createScholarshipSettingsList: {
    data: [],
    status: 'idle',
    error: null,
  },
  termFeeList: {
    data: [],
    status: 'idle',
    error: null,
  },
  basicFeeList: {
    data: [],
    status: 'idle',
    error: null,
  },
  feeOverviewStatusList: {
    data: null,
    status: 'idle',
    error: null,
  },
  feeOverviewChartList: {
    data: [],
    status: 'idle',
    error: null,
  },
  feeOverviewPaidList: {
    data: [],
    status: 'idle',
    error: null,
  },
  feeOverviewModeList: {
    data: [],
    status: 'idle',
    error: null,
  },
  receiptForPrintList: {
    data: [],
    status: 'idle',
    error: null,
  },
  feePaidList: {
    data: [],
    status: 'idle',
    error: null,
  },
  studentsFilter: {
    data: [],
    status: 'idle',
    error: null,
  },
  feePaidBasicList: {
    data: [],
    status: 'idle',
    error: null,
  },
  basicFeeFilter: {
    data: [],
    status: 'idle',
    error: null,
  },
  feePaidTermList: {
    data: [],
    status: 'idle',
    error: null,
  },
  termFeeFilter: {
    data: [],
    status: 'idle',
    error: null,
  },
  feePendingList: {
    data: [],
    status: 'idle',
    error: null,
  },
  feePendingBasicList: {
    data: [],
    status: 'idle',
    error: null,
  },
  feePendingTermList: {
    data: [],
    status: 'idle',
    error: null,
  },
  fineList: {
    data: [],
    status: 'idle',
    error: null,
  },
  fineMappingList: {
    data: [],
    status: 'idle',
    error: null,
  },
  fineMapList: {
    data: [],
    status: 'idle',
    error: null,
  },
  checkReceiptNo: {
    data: null,
    status: 'idle',
    error: null,
  },
  stopMappingSettingsList: {
    data: [],
    status: 'idle',
    error: null,
  },
  studentPickerList: {
    data: [],
    status: 'idle',
    error: null,
  },
  submitting: false,
  deletingRecords: {},
  error: null,
};

export const manageFeeSlice = createSlice({
  name: 'manageFee',
  initialState,
  reducers: {
    setfeeSettingsList: (state, action: PayloadAction<StudentTermFeePayType[]>) => {
      state.studentTermFeePayDatas.data = action.payload;
    },

    setStudentTermFeeStatusNewList: (state, action: PayloadAction<StudentTermFeeStatusNewType>) => {
      state.studentTermFeeStatusNewList.data = action.payload;
    },

    setCreateBasicFeeSetting: (state, action: PayloadAction<CreateBasicFeeSettingDataType[]>) => {
      state.createBasicFeeSettingList.data = action.payload;
    },
    setCreateTermFeeSetting: (state, action: PayloadAction<CreateTermFeeSettingDataType[]>) => {
      state.createTermFeeSettingList.data = action.payload;
    },
    setCreateBasicFeeSettingTitle: (state, action: PayloadAction<CreateBasicFeeSettingTitleDataType[]>) => {
      state.createBasicFeeSettingTitleList.data = action.payload;
    },
    setCreateTermFeeSettingTitle: (state, action: PayloadAction<CreateTermFeeSettingTitleDataType[]>) => {
      state.createTermFeeSettingTitleList.data = action.payload;
    },
    setCreateScholarshipFeeSetting: (state, action: PayloadAction<CreateScholarshipFeeSettingDataType>) => {
      state.createScholarshipFeeSettingList.data = action.payload;
    },
    setCreateEditFineList: (state, action: PayloadAction<GetFineListDataType[]>) => {
      state.fineList.data = action.payload;
    },
    setDeletingRecords: (state, action: PayloadAction<{ recordId: number }>) => {
      state.deletingRecords[action.payload.recordId] = true;
    },

    setSubmitting: (state, action: PayloadAction<boolean>) => {
      state.submitting = action.payload;
    },
  },
  extraReducers(builder) {
    builder
      // extraReducers for fetching messageTempList
      // ------Get Term Fee------- //
      .addCase(fetchGetTermFee.pending, (state) => {
        state.feeSettingsList.status = 'loading';
        state.feeSettingsList.error = null;
      })
      .addCase(fetchGetTermFee.fulfilled, (state, action) => {
        state.feeSettingsList.status = 'success';
        state.feeSettingsList.data = action.payload;
        state.feeSettingsList.error = null;
      })
      .addCase(fetchGetTermFee.rejected, (state, action) => {
        state.feeSettingsList.status = 'error';
        state.feeSettingsList.error = action.payload || 'Unknown error in fetching Term Fee';
      })
      // ------Get Class Sections ------- //
      .addCase(fetchClassSections.pending, (state) => {
        state.classSectionsList.status = 'loading';
        state.classSectionsList.error = null;
      })
      .addCase(fetchClassSections.fulfilled, (state, action) => {
        state.classSectionsList.status = 'success';
        state.classSectionsList.data = action.payload;
        state.classSectionsList.error = null;
      })
      .addCase(fetchClassSections.rejected, (state, action) => {
        state.classSectionsList.status = 'error';
        state.classSectionsList.error = action.payload || 'Unknown error in fetching Class Sections';
      })
      // ------Get Fee Date Settings------- //
      .addCase(fetchFeeDateSettings.pending, (state) => {
        state.feeDateSettingsList.status = 'loading';
        state.feeDateSettingsList.error = null;
      })
      .addCase(fetchFeeDateSettings.fulfilled, (state, action) => {
        state.feeDateSettingsList.status = 'success';
        state.feeDateSettingsList.data = action.payload;
        state.feeDateSettingsList.error = null;
      })
      .addCase(fetchFeeDateSettings.rejected, (state, action) => {
        state.feeDateSettingsList.status = 'error';
        state.feeDateSettingsList.error = action.payload || 'Unknown error in fetching Fee Date Setting';
      })
      // ------Get Class ------- //
      .addCase(fetchClassList.pending, (state) => {
        state.classList.status = 'loading';
        state.classList.error = null;
      })
      .addCase(fetchClassList.fulfilled, (state, action) => {
        state.classList.status = 'success';
        state.classList.data = action.payload;
        state.classList.error = null;
      })
      .addCase(fetchClassList.rejected, (state, action) => {
        state.classList.status = 'error';
        state.classList.error = action.payload || 'Unknown error in fetching Class';
      })
      // ------Get Students Fee Status ------- //
      .addCase(fetchStudentsFeeStatus.pending, (state) => {
        state.studentsFeeStatusList.status = 'loading';
        state.studentsFeeStatusList.error = null;
      })
      .addCase(fetchStudentsFeeStatus.fulfilled, (state, action) => {
        state.studentsFeeStatusList.status = 'success';
        state.studentsFeeStatusList.data = action.payload;
        state.studentsFeeStatusList.error = null;
      })
      .addCase(fetchStudentsFeeStatus.rejected, (state, action) => {
        state.studentsFeeStatusList.status = 'error';
        state.studentsFeeStatusList.error = action.payload || 'Unknown error in fetching Student Term Fee Status';
      })
      // ------Get Student Term Fee Status ------- //
      .addCase(fetchStudentTermFeeStatus.pending, (state) => {
        state.studentTermFeeStatusList.status = 'loading';
        state.studentTermFeeStatusList.error = null;
      })
      .addCase(fetchStudentTermFeeStatus.fulfilled, (state, action) => {
        state.studentTermFeeStatusList.status = 'success';
        state.studentTermFeeStatusList.data = action.payload;
        state.studentTermFeeStatusList.error = null;
      })
      .addCase(fetchStudentTermFeeStatus.rejected, (state, action) => {
        state.studentTermFeeStatusList.status = 'error';
        state.studentTermFeeStatusList.error = action.payload || 'Unknown error in fetching Student Term Fee Status';
      })
      // ------Get Student Term Fee New Status ------- //
      .addCase(fetchStudentTermFeeStatusNew.pending, (state) => {
        state.studentTermFeeStatusNewList.status = 'loading';
        state.studentTermFeeStatusNewList.error = null;
      })
      .addCase(fetchStudentTermFeeStatusNew.fulfilled, (state, action) => {
        state.studentTermFeeStatusNewList.status = 'success';
        state.studentTermFeeStatusNewList.data = action.payload;
        state.studentTermFeeStatusNewList.error = null;
      })
      .addCase(fetchStudentTermFeeStatusNew.rejected, (state, action) => {
        state.studentTermFeeStatusNewList.status = 'error';
        state.studentTermFeeStatusNewList.error =
          action.payload || 'Unknown error in fetching Student TermFee StatusNew List';
      })
      // ------ Pay -/- Student Term Fee Pay------- //
      .addCase(studentTermFeePay.pending, (state) => {
        state.submitting = true;
        state.studentTermFeePayDatas.error = null;
      })
      .addCase(studentTermFeePay.fulfilled, (state) => {
        state.submitting = false;
        state.studentTermFeePayDatas.error = null;
      })
      .addCase(studentTermFeePay.rejected, (state, action) => {
        state.submitting = false;
        state.studentTermFeePayDatas.error = action.error.message || 'Unknown error in fetching Student Term Fee Pay';
      })
      // ------ Create Basic Fee Setting ------- //
      .addCase(createBasicFeeSetting.pending, (state) => {
        state.submitting = true;
        state.createBasicFeeSettingList.error = null;
      })
      .addCase(createBasicFeeSetting.fulfilled, (state) => {
        state.submitting = false;
        state.createBasicFeeSettingList.error = null;
      })
      .addCase(createBasicFeeSetting.rejected, (state, action) => {
        state.submitting = false;
        state.createBasicFeeSettingList.error =
          action.error.message || 'Unknown error in fetching Create Basic Fee Setting';
      })
      // ------ Create Term Fee Setting ------- //
      .addCase(createTermFeeSetting.pending, (state) => {
        state.submitting = true;
        state.createTermFeeSettingList.error = null;
      })
      .addCase(createTermFeeSetting.fulfilled, (state) => {
        state.submitting = false;
        state.createTermFeeSettingList.error = null;
      })
      .addCase(createTermFeeSetting.rejected, (state, action) => {
        state.submitting = false;
        state.createTermFeeSettingList.error =
          action.error.message || 'Unknown error in fetching Create Term Fee Setting';
      })
      // ------ Create Basic Fee Setting Title ------- //
      .addCase(createBasicFeeSettingTitle.pending, (state) => {
        state.submitting = true;
        state.createBasicFeeSettingTitleList.error = null;
      })
      .addCase(createBasicFeeSettingTitle.fulfilled, (state) => {
        state.submitting = false;
        state.createBasicFeeSettingTitleList.error = null;
      })
      .addCase(createBasicFeeSettingTitle.rejected, (state, action) => {
        state.submitting = false;
        state.createBasicFeeSettingTitleList.error =
          action.error.message || 'Unknown error in Create Basic Fee Setting Title';
      })
      // ------ Create Term Fee Setting Title ------- //
      .addCase(createTermFeeSettingTitle.pending, (state) => {
        state.submitting = true;
        state.createTermFeeSettingTitleList.error = null;
      })
      .addCase(createTermFeeSettingTitle.fulfilled, (state) => {
        state.submitting = false;
        state.createTermFeeSettingTitleList.error = null;
      })
      .addCase(createTermFeeSettingTitle.rejected, (state, action) => {
        state.submitting = false;
        state.createTermFeeSettingTitleList.error =
          action.error.message || 'Unknown error in fetching Create Basic Fee Setting';
      })
      // ---------- Get Optional Fee Settings --------//
      .addCase(fetchGetOptionalFeeSettings.pending, (state) => {
        state.optionalFeeSettingsList.status = 'loading';
        state.optionalFeeSettingsList.error = null;
      })
      .addCase(fetchGetOptionalFeeSettings.fulfilled, (state, action) => {
        state.optionalFeeSettingsList.status = 'success';
        state.optionalFeeSettingsList.data = action.payload;
        state.optionalFeeSettingsList.error = null;
      })
      .addCase(fetchGetOptionalFeeSettings.rejected, (state, action) => {
        state.optionalFeeSettingsList.status = 'error';
        state.optionalFeeSettingsList.error = action.payload || 'Unknown error in fetching Student Term Fee Status';
      })
      // ---------- Get Individual Fee Settings --------//
      .addCase(fetchGetOptionalFeeSettingsIndividual.pending, (state) => {
        state.individualFeeSettingsList.status = 'loading';
        state.individualFeeSettingsList.error = null;
      })
      .addCase(fetchGetOptionalFeeSettingsIndividual.fulfilled, (state, action) => {
        state.individualFeeSettingsList.status = 'success';
        state.individualFeeSettingsList.data = action.payload;
        state.individualFeeSettingsList.error = null;
      })
      .addCase(fetchGetOptionalFeeSettingsIndividual.rejected, (state, action) => {
        state.individualFeeSettingsList.status = 'error';
        state.individualFeeSettingsList.error = action.payload || 'Unknown error in fetching Individual Fee Settings';
      })
      // ------ Create Optional Fee Settings ------- //
      .addCase(optionalFeeSettings.pending, (state) => {
        state.submitting = true;
        state.createOptionalFeeSettingsList.error = null;
      })
      .addCase(optionalFeeSettings.fulfilled, (state) => {
        state.submitting = false;
        state.createOptionalFeeSettingsList.error = null;
      })
      .addCase(optionalFeeSettings.rejected, (state, action) => {
        state.submitting = false;
        state.createOptionalFeeSettingsList.error =
          action.error.message || 'Unknown error in Create Optional Fee Settings';
      })
      // ------ Create Individual Fee Settings ------- //
      .addCase(optionalFeeSettingsIndividual.pending, (state) => {
        state.submitting = true;
        state.createIndividualFeeSettingsList.error = null;
      })
      .addCase(optionalFeeSettingsIndividual.fulfilled, (state) => {
        state.submitting = false;
        state.createIndividualFeeSettingsList.error = null;
      })
      .addCase(optionalFeeSettingsIndividual.rejected, (state, action) => {
        state.submitting = false;
        state.createIndividualFeeSettingsList.error =
          action.error.message || 'Unknown error in Create Individual Fee Settings';
      })
      // ------ create Scholarship Fee Setting ------- //
      .addCase(createScholarshipFeeSetting.pending, (state) => {
        state.submitting = true;
        state.createScholarshipFeeSettingList.error = null;
      })
      .addCase(createScholarshipFeeSetting.fulfilled, (state) => {
        state.submitting = false;
        state.createScholarshipFeeSettingList.error = null;
      })
      .addCase(createScholarshipFeeSetting.rejected, (state, action) => {
        state.submitting = false;
        state.createScholarshipFeeSettingList.error =
          action.error.message || 'Unknown error in fetching create Scholarship Fee Setting';
      })
      // ------ Get Scholarship Fee List ------- //
      .addCase(fetchScholarshipFeeList.pending, (state) => {
        state.ScholarshipFeeList.status = 'loading';
        state.ScholarshipFeeList.error = null;
      })
      .addCase(fetchScholarshipFeeList.fulfilled, (state, action) => {
        state.ScholarshipFeeList.status = 'success';
        state.ScholarshipFeeList.data = action.payload;
        state.ScholarshipFeeList.error = null;
      })
      .addCase(fetchScholarshipFeeList.rejected, (state, action) => {
        state.ScholarshipFeeList.status = 'error';
        state.ScholarshipFeeList.error = action.error.message || 'Unknown error in fetching Scholarship Fee List';
      })

      // ---------- Get Scholarship Settings --------//
      .addCase(fetchScholarshipSettings.pending, (state) => {
        state.scholarshipSettingsList.status = 'loading';
        state.scholarshipSettingsList.error = null;
      })
      .addCase(fetchScholarshipSettings.fulfilled, (state, action) => {
        state.scholarshipSettingsList.status = 'success';
        state.scholarshipSettingsList.data = action.payload;
        state.scholarshipSettingsList.error = null;
      })
      .addCase(fetchScholarshipSettings.rejected, (state, action) => {
        state.scholarshipSettingsList.status = 'error';
        state.scholarshipSettingsList.error = action.payload || 'Unknown error in fetching Student Term Fee Status';
      })
      // ------ Create Scholarship Settings ------- //
      .addCase(createScholarshipSettings.pending, (state) => {
        state.submitting = true;
        state.createScholarshipSettingsList.error = null;
      })
      .addCase(createScholarshipSettings.fulfilled, (state) => {
        state.submitting = false;
        state.createScholarshipSettingsList.error = null;
      })
      .addCase(createScholarshipSettings.rejected, (state, action) => {
        state.submitting = false;
        state.createScholarshipSettingsList.error =
          action.error.message || 'Unknown error in Create Optional Fee Settings';
      })
      // ------ Delete Scholarship ------- //
      .addCase(DeleteScholarship.pending, (state, action) => {
        action.meta.arg.forEach((i) => {
          state.deletingRecords[i.scholarshipId] = true;
        });
        state.error = null;
      })
      .addCase(DeleteScholarship.fulfilled, (state, action) => {
        if (action.payload.deleted) {
          action.meta.arg.forEach((i) => {
            const { [i.scholarshipId]: deletedRecord, ...restDeletingRecords } = state.deletingRecords;
            state.deletingRecords = restDeletingRecords;
          });
        }
        state.error = null;
      })
      .addCase(DeleteScholarship.rejected, (state, action) => {
        state.submitting = false;
        state.error = action.error.message || 'Unknown error in deleting Scholarship';
      })
      // ----------  Get Term Fee List --------
      .addCase(fetchTermFeeList.pending, (state) => {
        state.termFeeList.status = 'loading';
        state.termFeeList.error = null;
      })
      .addCase(fetchTermFeeList.fulfilled, (state, action) => {
        state.termFeeList.status = 'success';
        state.termFeeList.data = action.payload;
        state.termFeeList.error = null;
      })
      .addCase(fetchTermFeeList.rejected, (state, action) => {
        state.termFeeList.status = 'error';
        state.termFeeList.error = action.payload || 'Unknown error in fetchingTerm Fee List';
      })
      // ------ Delete Term Fee List  ------- //
      .addCase(deleteTermFeeList.pending, (state, action) => {
        action.meta.arg.forEach((i) => {
          state.deletingRecords[i.termId] = true;
        });
        state.error = null;
      })
      .addCase(deleteTermFeeList.fulfilled, (state, action) => {
        if (action.payload.deleted) {
          action.meta.arg.forEach((i) => {
            const { [i.termId]: deletedRecord, ...restDeletingRecords } = state.deletingRecords;
            state.deletingRecords = restDeletingRecords;
          });
        }
        state.error = null;
      })
      .addCase(deleteTermFeeList.rejected, (state, action) => {
        state.submitting = false;
        state.error = action.error.message || 'Unknown error in deleting Term Fee List';
      })
      // ------ Update Term Fee List  ------- //
      .addCase(editTermFeeList.pending, (state) => {
        state.submitting = true;
        state.termFeeList.error = null;
      })
      .addCase(editTermFeeList.fulfilled, (state) => {
        state.submitting = false;
        state.termFeeList.error = null;
      })
      .addCase(editTermFeeList.rejected, (state, action) => {
        state.submitting = false;
        state.termFeeList.error = action.error.message || 'Unknown error in Editing Term Fee List';
      })
      // ----------  Get Basic Fee List --------
      .addCase(fetchBasicFeeList.pending, (state) => {
        state.basicFeeList.status = 'loading';
        state.basicFeeList.error = null;
      })
      .addCase(fetchBasicFeeList.fulfilled, (state, action) => {
        state.basicFeeList.status = 'success';
        state.basicFeeList.data = action.payload;
        state.basicFeeList.error = null;
      })
      .addCase(fetchBasicFeeList.rejected, (state, action) => {
        state.basicFeeList.status = 'error';
        state.basicFeeList.error = action.payload || 'Unknown error in fetching Term Fee List';
      })
      // ------ Delete Basic Fee List  ------- //
      .addCase(deleteBasicFeeList.pending, (state, action) => {
        action.meta.arg.forEach((i) => {
          state.deletingRecords[i.feeId] = true;
        });
        state.error = null;
      })
      .addCase(deleteBasicFeeList.fulfilled, (state, action) => {
        if (action.payload.deleted) {
          action.meta.arg.forEach((i) => {
            const { [i.feeId]: deletedRecord, ...restDeletingRecords } = state.deletingRecords;
            state.deletingRecords = restDeletingRecords;
          });
        }
        state.error = null;
      })
      .addCase(deleteBasicFeeList.rejected, (state, action) => {
        state.submitting = false;
        state.error = action.error.message || 'Unknown error in deleting Term Fee List';
      })
      // ------ Update Basic Fee List  ------- //
      .addCase(editBasicFeeList.pending, (state) => {
        state.submitting = true;
        state.basicFeeList.error = null;
      })
      .addCase(editBasicFeeList.fulfilled, (state) => {
        state.submitting = false;
        state.basicFeeList.error = null;
      })
      .addCase(editBasicFeeList.rejected, (state, action) => {
        state.submitting = false;
        state.basicFeeList.error = action.error.message || 'Unknown error in Editing Term Fee List';
      })
      // ----------  Get Fee Overview Status --------
      .addCase(fetchFeeOverviewStatus.pending, (state) => {
        state.feeOverviewStatusList.status = 'loading';
        state.feeOverviewStatusList.error = null;
      })
      .addCase(fetchFeeOverviewStatus.fulfilled, (state, action) => {
        state.feeOverviewStatusList.status = 'success';
        state.feeOverviewStatusList.data = action.payload;
        state.feeOverviewStatusList.error = null;
      })
      .addCase(fetchFeeOverviewStatus.rejected, (state, action) => {
        state.feeOverviewStatusList.status = 'error';
        state.feeOverviewStatusList.error = action.payload || 'Unknown error in fetching Fee Overview Status List';
      })
      // ----------  Get Fee Overview Chart --------
      .addCase(fetchFeeOverviewChart.pending, (state) => {
        state.feeOverviewChartList.status = 'loading';
        state.feeOverviewChartList.error = null;
      })
      .addCase(fetchFeeOverviewChart.fulfilled, (state, action) => {
        state.feeOverviewChartList.status = 'success';
        state.feeOverviewChartList.data = action.payload;
        state.feeOverviewChartList.error = null;
      })
      .addCase(fetchFeeOverviewChart.rejected, (state, action) => {
        state.feeOverviewChartList.status = 'error';
        state.feeOverviewChartList.error = action.payload || 'Unknown error in fetching Fee Overview Chart List';
      })
      // ----------  Get Fee Overview Paid List --------
      .addCase(fetchFeeOverviewPaidList.pending, (state) => {
        state.feeOverviewPaidList.status = 'loading';
        state.feeOverviewPaidList.error = null;
      })
      .addCase(fetchFeeOverviewPaidList.fulfilled, (state, action) => {
        state.feeOverviewPaidList.status = 'success';
        state.feeOverviewPaidList.data = action.payload;
        state.feeOverviewPaidList.error = null;
      })
      .addCase(fetchFeeOverviewPaidList.rejected, (state, action) => {
        state.feeOverviewPaidList.status = 'error';
        state.feeOverviewPaidList.error = action.payload || 'Unknown error in fetching CTS list';
      })
      // ----------  Get Fee Overview Mode List --------
      .addCase(fetchFeeOverviewModeList.pending, (state) => {
        state.feeOverviewModeList.status = 'loading';
        state.feeOverviewModeList.error = null;
      })
      .addCase(fetchFeeOverviewModeList.fulfilled, (state, action) => {
        state.feeOverviewModeList.status = 'success';
        state.feeOverviewModeList.data = action.payload;
        state.feeOverviewModeList.error = null;
      })
      .addCase(fetchFeeOverviewModeList.rejected, (state, action) => {
        state.feeOverviewModeList.status = 'error';
        state.feeOverviewModeList.error = action.payload || 'Unknown error in fetching Fee Overview Mode List';
      })
      // ----------  Get Receipt For Print --------
      .addCase(fetchReceiptForPrint.pending, (state) => {
        state.receiptForPrintList.status = 'loading';
        state.receiptForPrintList.error = null;
      })
      .addCase(fetchReceiptForPrint.fulfilled, (state, action) => {
        state.receiptForPrintList.status = 'success';
        state.receiptForPrintList.data = action.payload;
        state.receiptForPrintList.error = null;
      })
      .addCase(fetchReceiptForPrint.rejected, (state, action) => {
        state.receiptForPrintList.status = 'error';
        state.receiptForPrintList.error = action.payload || 'Unknown error in fetching Receipt For Print List';
      })
      // ----------  Get Fee Paid List --------
      .addCase(fetchFeePaidList.pending, (state) => {
        state.feePaidList.status = 'loading';
        state.feePaidList.error = null;
      })
      .addCase(fetchFeePaidList.fulfilled, (state, action) => {
        state.feePaidList.status = 'success';
        state.feePaidList.data = action.payload;
        state.feePaidList.error = null;
      })
      .addCase(fetchFeePaidList.rejected, (state, action) => {
        state.feePaidList.status = 'error';
        state.feePaidList.error = action.payload || 'Unknown error in fetching Fee Paid List';
      })
      // ---------- Get Students Filter --------
      .addCase(fetchStudentsFilter.pending, (state) => {
        state.studentsFilter.status = 'loading';
        state.studentsFilter.error = null;
      })
      .addCase(fetchStudentsFilter.fulfilled, (state, action) => {
        state.studentsFilter.status = 'success';
        state.studentsFilter.data = action.payload;
        state.studentsFilter.error = null;
      })
      .addCase(fetchStudentsFilter.rejected, (state, action) => {
        state.studentsFilter.status = 'error';
        state.studentsFilter.error = action.payload || 'Unknown error in fetching Receipt For Print List';
      })
      // ---------- Get Fee Paid Basic List --------
      .addCase(fetchFeePaidBasicList.pending, (state) => {
        state.feePaidBasicList.status = 'loading';
        state.feePaidBasicList.error = null;
      })
      .addCase(fetchFeePaidBasicList.fulfilled, (state, action) => {
        state.feePaidBasicList.status = 'success';
        state.feePaidBasicList.data = action.payload;
        state.feePaidBasicList.error = null;
      })
      .addCase(fetchFeePaidBasicList.rejected, (state, action) => {
        state.feePaidBasicList.status = 'error';
        state.feePaidBasicList.error = action.payload || 'Unknown error in fetching Fee Paid Basic List';
      })
      // ---------- Get Basic Fee Filter --------
      .addCase(fetchBasicFeeFilter.pending, (state) => {
        state.basicFeeFilter.status = 'loading';
        state.basicFeeFilter.error = null;
      })
      .addCase(fetchBasicFeeFilter.fulfilled, (state, action) => {
        state.basicFeeFilter.status = 'success';
        state.basicFeeFilter.data = action.payload;
        state.basicFeeFilter.error = null;
      })
      .addCase(fetchBasicFeeFilter.rejected, (state, action) => {
        state.basicFeeFilter.status = 'error';
        state.basicFeeFilter.error = action.payload || 'Unknown error in fetching Basic Fee Filter';
      })
      // ---------- Get Fee Paid Term List --------
      .addCase(fetchFeePaidTermList.pending, (state) => {
        state.feePaidTermList.status = 'loading';
        state.feePaidTermList.error = null;
      })
      .addCase(fetchFeePaidTermList.fulfilled, (state, action) => {
        state.feePaidTermList.status = 'success';
        state.feePaidTermList.data = action.payload;
        state.feePaidTermList.error = null;
      })
      .addCase(fetchFeePaidTermList.rejected, (state, action) => {
        state.feePaidTermList.status = 'error';
        state.feePaidTermList.error = action.payload || 'Unknown error in fetching Get Fee Paid Term List';
      })
      // ---------- Get Term Fee Filter --------
      .addCase(fetchTermFeeFilter.pending, (state) => {
        state.termFeeFilter.status = 'loading';
        state.termFeeFilter.error = null;
      })
      .addCase(fetchTermFeeFilter.fulfilled, (state, action) => {
        state.termFeeFilter.status = 'success';
        state.termFeeFilter.data = action.payload;
        state.termFeeFilter.error = null;
      })
      .addCase(fetchTermFeeFilter.rejected, (state, action) => {
        state.termFeeFilter.status = 'error';
        state.termFeeFilter.error = action.payload || 'Unknown error in fetching Term Fee Filter';
      })
      // ---------- Get Fee Pending List --------
      .addCase(fetchFeePendingList.pending, (state) => {
        state.feePendingList.status = 'loading';
        state.feePendingList.error = null;
      })
      .addCase(fetchFeePendingList.fulfilled, (state, action) => {
        state.feePendingList.status = 'success';
        state.feePendingList.data = action.payload;
        state.feePendingList.error = null;
      })
      .addCase(fetchFeePendingList.rejected, (state, action) => {
        state.feePendingList.status = 'error';
        state.feePendingList.error = action.payload || 'Unknown error in fetching Fee Pending List';
      })
      // ---------- Get Fee Pending Basic List --------
      .addCase(fetchFeePendingBasicList.pending, (state) => {
        state.feePendingBasicList.status = 'loading';
        state.feePendingBasicList.error = null;
      })
      .addCase(fetchFeePendingBasicList.fulfilled, (state, action) => {
        state.feePendingBasicList.status = 'success';
        state.feePendingBasicList.data = action.payload;
        state.feePendingBasicList.error = null;
      })
      .addCase(fetchFeePendingBasicList.rejected, (state, action) => {
        state.feePendingBasicList.status = 'error';
        state.feePendingBasicList.error = action.payload || 'Unknown error in fetching Fee Pending Basic List';
      })
      // ---------- Get Fee Pending Term List --------
      .addCase(fetchFeePendingTermList.pending, (state) => {
        state.feePendingTermList.status = 'loading';
        state.feePendingTermList.error = null;
      })
      .addCase(fetchFeePendingTermList.fulfilled, (state, action) => {
        state.feePendingTermList.status = 'success';
        state.feePendingTermList.data = action.payload;
        state.feePendingTermList.error = null;
      })
      .addCase(fetchFeePendingTermList.rejected, (state, action) => {
        state.feePendingTermList.status = 'error';
        state.feePendingTermList.error = action.payload || 'Unknown error in fetching Fee Pending Term List';
      })
      // ---------- Get Fine List --------
      .addCase(fetchFineList.pending, (state) => {
        state.fineList.status = 'loading';
        state.fineList.error = null;
      })
      .addCase(fetchFineList.fulfilled, (state, action) => {
        state.fineList.status = 'success';
        state.fineList.data = action.payload;
        state.fineList.error = null;
      })
      .addCase(fetchFineList.rejected, (state, action) => {
        state.fineList.status = 'error';
        state.fineList.error = action.payload || 'Unknown error in fetching Fine List';
      })
      // ---------- Create/Edit Fine List --------
      .addCase(CreateEditFineList.pending, (state) => {
        state.submitting = true;
        state.fineList.error = null;
      })
      .addCase(CreateEditFineList.fulfilled, (state) => {
        state.submitting = false;
        state.fineList.error = null;
      })
      .addCase(CreateEditFineList.rejected, (state, action) => {
        state.submitting = false;
        state.fineList.error = action.payload || 'Unknown error in Create/Edit Fine List';
      })
      // ---------- Delete Fine List --------
      .addCase(DeleteFineList.pending, (state, action) => {
        action.meta.arg.forEach((i) => {
          state.deletingRecords[i.fineId] = true;
        });
        state.error = null;
      })
      .addCase(DeleteFineList.fulfilled, (state, action) => {
        if (action.payload.deletedId) {
          action.meta.arg.forEach((i) => {
            const { [i.fineId]: deletedRecord, ...restDeletingRecords } = state.deletingRecords;
            state.deletingRecords = restDeletingRecords;
          });
        }
        state.error = null;
      })
      .addCase(DeleteFineList.rejected, (state, action) => {
        // state.status = 'error';
        state.error = action.payload || 'Unknown error in Deleting Fine List';
      })
      // ---------- Get Fine Mappings --------
      .addCase(fetchFineMappingList.pending, (state) => {
        state.fineMappingList.status = 'loading';
        state.fineMappingList.error = null;
      })
      .addCase(fetchFineMappingList.fulfilled, (state, action) => {
        state.fineMappingList.status = 'success';
        state.fineMappingList.data = action.payload;
        state.fineMappingList.error = null;
      })
      .addCase(fetchFineMappingList.rejected, (state, action) => {
        state.fineMappingList.status = 'error';
        state.fineMappingList.error = action.payload || 'Unknown error in fetching Fine Mapping List';
      })
      // ------ Create Scholarship Settings ------- //
      .addCase(createFineMap.pending, (state) => {
        state.submitting = true;
        state.fineMapList.error = null;
      })
      .addCase(createFineMap.fulfilled, (state) => {
        state.submitting = false;
        state.fineMapList.error = null;
      })
      .addCase(createFineMap.rejected, (state, action) => {
        state.submitting = false;
        state.fineMapList.error = action.error.message || 'Unknown error in Create Optional Fee Settings';
      })
      // ---------- Delete Fine Map --------
      .addCase(DeleteFineMap.pending, (state, action) => {
        action.meta.arg.forEach((i) => {
          state.deletingRecords[i.fineMappedId] = true;
        });
        state.error = null;
      })
      .addCase(DeleteFineMap.fulfilled, (state, action) => {
        if (action.payload) {
          action.meta.arg.forEach((i) => {
            const { [i.fineMappedId]: deletedRecord, ...restDeletingRecords } = state.deletingRecords;
            state.deletingRecords = restDeletingRecords;
          });
        }
        state.error = null;
      })
      .addCase(DeleteFineMap.rejected, (state, action) => {
        state.submitting = false;
        state.error = action.error.message || 'Unknown error in deleting Fine Map';
      })
      // ---------- Delete Basic Fee Mapped --------
      .addCase(DeleteBasicFee.pending, (state, action) => {
        action.meta.arg.forEach((i) => {
          state.deletingRecords[i.academicFeeId] = true;
        });
        state.error = null;
      })
      .addCase(DeleteBasicFee.fulfilled, (state, action) => {
        if (action.payload.deletedId) {
          action.meta.arg.forEach((i) => {
            const { [i.academicFeeId]: deletedRecord, ...restDeletingRecords } = state.deletingRecords;
            state.deletingRecords = restDeletingRecords;
          });
        }
        state.error = null;
      })
      .addCase(DeleteBasicFee.rejected, (state, action) => {
        state.submitting = false;
        state.error = action.error.message || 'Unknown error in deleting Basic Fee Mapped';
      })
      // ---------- Delete All Basic Fee Mapped --------
      .addCase(DeleteAllBasicFee.pending, (state, action) => {
        action.meta.arg.forEach((i) => {
          state.deletingRecords[i.feeId] = true;
        });
        state.error = null;
      })
      .addCase(DeleteAllBasicFee.fulfilled, (state, action) => {
        if (action.payload.deleted) {
          action.meta.arg.forEach((i) => {
            const { [i.feeId]: deletedRecord, ...restDeletingRecords } = state.deletingRecords;
            state.deletingRecords = restDeletingRecords;
          });
        }
        state.error = null;
      })
      .addCase(DeleteAllBasicFee.rejected, (state, action) => {
        state.submitting = false;
        state.error = action.error.message || 'Unknown error in deleting All Basic Fee Mapped';
      })
      // ---------- Delete Term Fee Mapped --------
      .addCase(DeleteTermFee.pending, (state, action) => {
        action.meta.arg.forEach((i) => {
          state.deletingRecords[i.academicTermId] = true;
        });
        state.error = null;
      })
      .addCase(DeleteTermFee.fulfilled, (state, action) => {
        if (action.payload) {
          action.meta.arg.forEach((i) => {
            const { [i.academicTermId]: deletedRecord, ...restDeletingRecords } = state.deletingRecords;
            state.deletingRecords = restDeletingRecords;
          });
        }
        state.error = null;
      })
      .addCase(DeleteTermFee.rejected, (state, action) => {
        state.submitting = false;
        state.error = action.error.message || 'Unknown error in deleting Term Fee Mapped';
      })
      // ---------- Delete All Term Fee Mapped --------
      .addCase(DeleteAllTermFee.pending, (state, action) => {
        action.meta.arg.forEach((i) => {
          state.deletingRecords[i.feeId] = true;
        });
        state.error = null;
      })
      .addCase(DeleteAllTermFee.fulfilled, (state, action) => {
        if (action.payload) {
          action.meta.arg.forEach((i) => {
            const { [i.feeId]: deletedRecord, ...restDeletingRecords } = state.deletingRecords;
            state.deletingRecords = restDeletingRecords;
          });
        }
        state.error = null;
      })
      .addCase(DeleteAllTermFee.rejected, (state, action) => {
        state.submitting = false;
        state.error = action.error.message || 'Unknown error in deleting All Term Fee Mapped';
      })
      // ---------- Receipt Cancel --------
      .addCase(ReceiptCancel.pending, (state) => {
        state.error = null;
      })
      .addCase(ReceiptCancel.fulfilled, (state) => {
        state.error = null;
      })
      .addCase(ReceiptCancel.rejected, (state, action) => {
        state.submitting = false;
        state.error = action.error.message || 'Unknown error in deleting Receipt Cancel';
      })
      // ---------- Check Receipt No --------
      .addCase(CheckReceiptNo.pending, (state) => {
        state.checkReceiptNo.status = 'loading';
        state.checkReceiptNo.error = null;
      })
      .addCase(CheckReceiptNo.fulfilled, (state, action) => {
        state.checkReceiptNo.status = 'success';
        state.checkReceiptNo.data = action.payload;
        state.checkReceiptNo.error = null;
      })
      .addCase(CheckReceiptNo.rejected, (state, action) => {
        state.checkReceiptNo.status = 'error';
        state.checkReceiptNo.error = action.payload || 'Unknown error in fetching Check Receipt No';
      })
      // ---------- Delete Optional Fee Mapped --------
      .addCase(DeleteOptionalFee.pending, (state, action) => {
        action.meta.arg.forEach((i) => {
          state.deletingRecords[i.optionalMapId] = true;
        });
        state.error = null;
      })
      .addCase(DeleteOptionalFee.fulfilled, (state, action) => {
        if (action.payload) {
          action.meta.arg.forEach((i) => {
            const { [i.optionalMapId]: deletedRecord, ...restDeletingRecords } = state.deletingRecords;
            state.deletingRecords = restDeletingRecords;
          });
        }
        state.error = null;
      })
      .addCase(DeleteOptionalFee.rejected, (state, action) => {
        state.submitting = false;
        state.error = action.error.message || 'Unknown error in deleting Optional Fee Mapped';
      })
      // ---------- Delete All Optional Fee Mapped --------
      .addCase(DeleteAllOptionalFee.pending, (state, action) => {
        action.meta.arg.forEach((i) => {
          state.deletingRecords[i.studentId] = true;
        });
        state.error = null;
      })
      .addCase(DeleteAllOptionalFee.fulfilled, (state, action) => {
        if (action.payload) {
          action.meta.arg.forEach((i) => {
            const { [i.studentId]: deletedRecord, ...restDeletingRecords } = state.deletingRecords;
            state.deletingRecords = restDeletingRecords;
          });
        }
        state.error = null;
      })
      .addCase(DeleteAllOptionalFee.rejected, (state, action) => {
        state.submitting = false;
        state.error = action.error.message || 'Unknown error in deleting All Optional Fee Mapped';
      })
      // ---------- Delete Scholarship Mapped --------
      .addCase(DeleteScholarshipMapped.pending, (state, action) => {
        action.meta.arg.forEach((i) => {
          state.deletingRecords[i.scholarshipStudentMapId] = true;
        });
        state.error = null;
      })
      .addCase(DeleteScholarshipMapped.fulfilled, (state, action) => {
        if (action.payload) {
          action.meta.arg.forEach((i) => {
            const { [i.scholarshipStudentMapId]: deletedRecord, ...restDeletingRecords } = state.deletingRecords;
            state.deletingRecords = restDeletingRecords;
          });
        }
        state.error = null;
      })
      .addCase(DeleteScholarshipMapped.rejected, (state, action) => {
        state.submitting = false;
        state.error = action.error.message || 'Unknown error in deleting Scholarship Mapped';
      })
      // ---------- Delete All Scholarship Mapped --------
      .addCase(DeleteAllScholarshipMapped.pending, (state, action) => {
        action.meta.arg.forEach((i) => {
          state.deletingRecords[i.studentId] = true;
        });
        state.error = null;
      })
      .addCase(DeleteAllScholarshipMapped.fulfilled, (state, action) => {
        if (action.payload) {
          action.meta.arg.forEach((i) => {
            const { [i.studentId]: deletedRecord, ...restDeletingRecords } = state.deletingRecords;
            state.deletingRecords = restDeletingRecords;
          });
        }
        state.error = null;
      })
      .addCase(DeleteAllScholarshipMapped.rejected, (state, action) => {
        state.submitting = false;
        state.error = action.error.message || 'Unknown error in deleting All Scholarship Mapped';
      })
      // ---------- Get Stop Mapping Settings --------//
      .addCase(fetchStopMappingSettings.pending, (state) => {
        state.stopMappingSettingsList.status = 'loading';
        state.stopMappingSettingsList.error = null;
      })
      .addCase(fetchStopMappingSettings.fulfilled, (state, action) => {
        state.stopMappingSettingsList.status = 'success';
        state.stopMappingSettingsList.data = action.payload;
        state.stopMappingSettingsList.error = null;
      })
      .addCase(fetchStopMappingSettings.rejected, (state, action) => {
        state.stopMappingSettingsList.status = 'error';
        state.stopMappingSettingsList.error = action.payload || 'Unknown error in fetching Stop Mapping Settings';
      })
      // ------ Create Stop Mapping ------- //
      .addCase(createStopMapping.pending, (state) => {
        state.submitting = true;
        state.stopMappingSettingsList.error = null;
      })
      .addCase(createStopMapping.fulfilled, (state) => {
        state.submitting = false;
        state.stopMappingSettingsList.error = null;
      })
      .addCase(createStopMapping.rejected, (state, action) => {
        state.submitting = false;
        state.stopMappingSettingsList.error = action.error.message || 'Unknown error in Create Stop Mapping';
      })
      // ---------- Delete Bus Mapped --------
      .addCase(DeleteBusMapped.pending, (state, action) => {
        action.meta.arg.forEach((i) => {
          state.deletingRecords[i.stopTermMapId] = true;
        });
        state.error = null;
      })
      .addCase(DeleteBusMapped.fulfilled, (state, action) => {
        if (action.payload) {
          action.meta.arg.forEach((i) => {
            const { [i.stopTermMapId]: deletedRecord, ...restDeletingRecords } = state.deletingRecords;
            state.deletingRecords = restDeletingRecords;
          });
        }
        state.error = null;
      })
      .addCase(DeleteBusMapped.rejected, (state, action) => {
        state.submitting = false;
        state.error = action.error.message || 'Unknown error in deleting Bus Mapped';
      })
      // ---------- Delete All Bus Mapped Student --------
      .addCase(DeleteAllBusMappedStudent.pending, (state, action) => {
        action.meta.arg.forEach((i) => {
          state.deletingRecords[i.studentId] = true;
        });
        state.error = null;
      })
      .addCase(DeleteAllBusMappedStudent.fulfilled, (state, action) => {
        if (action.payload) {
          action.meta.arg.forEach((i) => {
            const { [i.studentId]: deletedRecord, ...restDeletingRecords } = state.deletingRecords;
            state.deletingRecords = restDeletingRecords;
          });
        }
        state.error = null;
      })
      .addCase(DeleteAllBusMappedStudent.rejected, (state, action) => {
        state.submitting = false;
        state.error = action.error.message || 'Unknown error in deleting All Bus Mapped Student';
      })
      // ---------- Receipt Approve --------
      .addCase(ReceiptApprove.pending, (state) => {
        state.error = null;
      })
      .addCase(ReceiptApprove.fulfilled, (state) => {
        state.error = null;
      })
      .addCase(ReceiptApprove.rejected, (state, action) => {
        state.submitting = false;
        state.error = action.error.message || 'Unknown error in deleting Receipt Cancel';
      })
      .addCase(fetchStudentPickerData.pending, (state) => {
        state.studentPickerList.status = 'loading';
        state.studentPickerList.error = null;
      })
      .addCase(fetchStudentPickerData.fulfilled, (state, action) => {
        state.studentPickerList.status = 'success';
        state.studentPickerList.data = action.payload;
        state.studentPickerList.error = null;
      })
      .addCase(fetchStudentPickerData.rejected, (state, action) => {
        state.studentPickerList.status = 'error';
        state.studentPickerList.error = action.payload || 'Unknown error in fetching CTS list';
      })
      .addCase(flushStore, () => initialState);
  },
});

export const {
  setfeeSettingsList,
  setStudentTermFeeStatusNewList,
  setSubmitting,
  setCreateBasicFeeSetting,
  setCreateTermFeeSetting,
  setCreateBasicFeeSettingTitle,
  setCreateTermFeeSettingTitle,
  setCreateEditFineList,
} = manageFeeSlice.actions;

export default manageFeeSlice.reducer;
