/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  Collapse,
  FormControl,
} from '@mui/material';
import styled from 'styled-components';
import SearchIcon from '@mui/icons-material/Search';
import deleteBin from '@/assets/ManageFee/deleteBin.json';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import DeleteIcon from '@mui/icons-material/Delete';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import { MdAdd } from 'react-icons/md';
import Popup from '@/components/shared/Popup/Popup';
import CreateBook from './CreateBook';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import { deleteTermFeeList } from '@/store/ManageFee/manageFee.thunks';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import { useAppDispatch } from '@/hooks/useAppDispatch';

const BookListRoot = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      height: calc(100% - 40px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

export const data = [
  {
    SlNo: 1,
    BookTitle: 'An Introduction to Information Science',
    BookCode: 1005,
    Author: 'Roger Flynn',
    Category: 'General',
    Location: 'General',
    Holder: 'Library',
  },
  {
    SlNo: 2,
    BookTitle: 'Becoming a Digital Library',
    BookCode: 1001,
    Author: 'Susan J. Barnes',
    Category: 'General',
    Location: 'General',
    Holder: 'Library',
  },
  {
    SlNo: 3,
    BookTitle: 'Designing Instruction for Library',
    BookCode: 1004,
    Author: 'Marilla Svinicki',
    Category: 'General',
    Location: 'General',
    Holder: 'Student',
  },
  {
    SlNo: 4,
    BookTitle: 'Effective Online Searching: A Basic Text',
    BookCode: 1009,
    Author: 'Borgman',
    Category: 'Acconting',
    Location: 'General',
    Holder: 'Library',
  },
  {
    SlNo: 5,
    BookTitle: 'Furnishing the Library Interior',
    BookCode: 1008,
    Author: 'Pierce',
    Category: 'General',
    Location: 'General',
    Holder: 'Library',
  },
  {
    SlNo: 6,
    BookTitle: 'Geometry Explained: Visual Learning',
    BookCode: 1022,
    Author: 'Michael Smith',
    Category: 'Mathematics',
    Location: 'General',
    Holder: 'Library',
  },
  {
    SlNo: 7,
    BookTitle: 'Global Librarianship',
    BookCode: 1000,
    Author: 'Martin A. Kesselman',
    Category: 'General',
    Location: 'General',
    Holder: 'Library',
  },
  {
    SlNo: 8,
    BookTitle: 'Library Information Technology and Networks',
    BookCode: 1003,
    Author: 'Charles Grosch',
    Category: 'General',
    Location: 'General',
    Holder: 'Library',
  },
  {
    SlNo: 9,
    BookTitle: "Manheimer's Cataloging",
    BookCode: 1002,
    Author: 'Jerry Saye',
    Category: 'Acconting',
    Location: 'General',
    Holder: 'Student',
  },
  {
    SlNo: 10,
    BookTitle: 'Mastering Algebra: A Comprehensive Guide',
    BookCode: 1020,
    Author: 'Emily Williams',
    Category: 'Mathematics',
    Location: 'General',
    Holder: 'Library',
  },
  {
    SlNo: 11,
    BookTitle: 'Math Magic: Fun with Numbers',
    BookCode: 1019,
    Author: 'Alex Johnson',
    Category: 'Mathematics',
    Location: 'General',
    Holder: 'Library',
  },
  {
    SlNo: 12,
    BookTitle: 'Organizing Nonprint Materials, Second Edition',
    BookCode: 1006,
    Author: 'Daily',
    Category: 'Acconting',
    Location: 'General',
    Holder: 'Library',
  },
  {
    SlNo: 13,
    BookTitle: 'Pride and Prejudice',
    BookCode: 1013,
    Author: 'Jane Austen',
    Category: 'English Literature',
    Location: 'General',
    Holder: 'Library',
  },
  {
    SlNo: 14,
    BookTitle: 'Rich Dad, Poor Dad',
    BookCode: 1010,
    Author: 'Robert Kiyosaki',
    Category: 'Inspirations and Motivations',
    Location: 'General',
    Holder: 'Library',
  },
  {
    SlNo: 15,
    BookTitle: 'Scientific and Technical Information Resources',
    BookCode: 1007,
    Author: 'Subramanyam',
    Category: 'Acconting',
    Location: 'General',
    Holder: 'Library',
  },
];

function BookList() {
  const { confirm } = useConfirm();
  const dispatch = useAppDispatch();
  const [showFilter, setShowFilter] = useState(false);
  const [createBookPopup, setCreateBookPopup] = useState(false);

  const handleCreatePopupOpen = () => {
    setCreateBookPopup(true);
  };

  const getRowKey = useCallback((row: any) => row.examId, []);
  const handleDeleteTermFeeList = useCallback(
    async (termObj: any) => {
      const { termId } = termObj;
      const deleteConfirmMessage = (
        <DeleteMessage
          jsonIcon={deleteBin}
          message={
            <div>
              Are you sure you want to delete the Row <br />
              &quot;{termObj.termTitle}&quot; ?
            </div>
          }
        />
      );
      if (await confirm(deleteConfirmMessage, 'Delete Row?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
        const sendReq = [{ dbResult: 'string', deletedId: 0 }];

        console.log('sendReq', sendReq);

        const deleteResponse = await dispatch(deleteTermFeeList(sendReq));
        console.log('deleteResponse::::----', deleteResponse);
        if (deleteResponse && Array.isArray(deleteResponse.payload)) {
          const results: any[] = deleteResponse.payload;
          const errorMessages = results.find((result) => result.dbResult === 'Failed');
          const successMessages = results.find((result) => result.dbResult === 'Success');
          // Reload only the specific message template with the deleted messageId

          if (!errorMessages) {
            const deleteSuccessMessage = <SuccessMessage loop={false} message="Delete successfully" />;
            await confirm(deleteSuccessMessage, 'Success', { okLabel: 'Ok', showOnlyOk: true });
          } else if (!successMessages) {
            const deleteErrorMessage = <DeleteMessage message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          } else {
            const deleteErrorMessage = <DeleteMessage message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          }
          // loadTermFeeList({ ...currentTermFeeListRequest });

          // setSnackBar(true);
          // setTermFeeData((prevDetails) => prevDetails.filter((item) => item.termId !== termId));
        }
      }
    },
    [confirm]
  );
  const BookListColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'SlNo',
        dataKey: 'SlNo',
        headerLabel: 'Sl.No',
        sortable: true,
      },
      {
        name: 'BookTitle',
        dataKey: 'BookTitle',
        headerLabel: 'Book Title',
      },
      {
        name: 'BookCode',
        dataKey: 'BookCode',
        headerLabel: 'Code',
      },
      {
        name: 'Author',
        dataKey: 'Author',
        headerLabel: 'Author',
      },
      {
        name: 'Category',
        dataKey: 'Category',
        headerLabel: 'Category',
      },
      {
        name: 'Location',
        dataKey: 'Location',
        headerLabel: 'Location',
      },
      {
        name: 'Holder',
        dataKey: 'Holder',
        headerLabel: 'Holder',
      },
      {
        name: 'actions',
        headerLabel: 'Actions',
        renderCell: () => {
          return (
            <Stack direction="row" gap={1}>
              <IconButton onClick={handleCreatePopupOpen} size="small" sx={{ padding: 0.5 }}>
                <ModeEditIcon />
              </IconButton>
              <IconButton onClick={handleDeleteTermFeeList} size="small" color="error" sx={{ padding: 0.5 }}>
                <DeleteIcon />
              </IconButton>
            </Stack>
          );
        },
      },
    ],
    []
  );

  // Now you have the paymentListColumns array with the specified attributes and dataKey values.

  return (
    <Page title="List">
      <BookListRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17}>
              Books List
            </Typography>
            <Box pb={1} sx={{ flexShrink: 0 }}>
              <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                <SearchIcon />
              </IconButton>
              <Button onClick={handleCreatePopupOpen} sx={{ borderRadius: '20px' }} variant="outlined" size="small">
                <MdAdd size="20px" /> Create
              </Button>
            </Box>
          </Stack>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={3} alignItems="end">
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 250 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Category Name
                      </Typography>
                      <TextField placeholder="Enter Name" />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 250 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Sub Category Name
                      </Typography>
                      <TextField placeholder="Enter Name" />
                    </FormControl>
                  </Grid>

                  <Grid item lg="auto" xs={12} sm={6}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Paper className="card-table-container" sx={{ marginTop: '12px' }}>
              <DataTable columns={BookListColumns} data={data} getRowKey={getRowKey} fetchStatus="success" />
            </Paper>
          </div>
          {/* <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 2 }}>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="secondary">
                Cancel
              </Button>
              <Button onClick={handleOpen} variant="contained" color="primary">
                Save
              </Button>
            </Stack>
          </Box> */}
        </Card>
      </BookListRoot>

      <Popup
        size="md"
        state={createBookPopup}
        title="Create Book"
        onClose={() => setCreateBookPopup(false)}
        popupContent={<CreateBook />}
      />
    </Page>
  );
}

export default BookList;
