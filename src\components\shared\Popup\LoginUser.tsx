import * as React from 'react';
import useAuth from '@/hooks/useAuth';
import { getTextColor } from '@/utils/Colors';
import {
  Avatar,
  Box,
  ClickAwayListener,
  Divider,
  IconButton,
  ListItemIcon,
  ListItemText,
  MenuItem,
  Paper,
  Switch,
  Tooltip,
  Typography,
  MenuList,
  <PERSON>row,
  Popper,
} from '@mui/material';
import styled, { useTheme } from 'styled-components';
import { MdOutlineLightMode } from 'react-icons/md';
import { CiDark } from 'react-icons/ci';
import useSettings from '@/hooks/useSettings';
import { FiLogOut } from 'react-icons/fi';
import { SlUser } from 'react-icons/sl';
import { Account } from '@/types/Auth';
import LogoutBox from './LogoutBox';

const LoginUserRoot = styled(Box)`
  display: flex;
  align-items: center;

  .details {
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-left: 5px;
    .name {
      font-family: 'Poppins Semibold';
      color: ${(props) => getTextColor(props.theme)};
      margin: 0;
      margin-bottom: 0.25rem;
      font-size: 0.875rem;
    }
    .role {
      display: block;
      font-family: 'Poppins Regular';
      color: ${(props) =>
        props.theme.themeMode === 'light' ? props.theme.palette.light : props.theme.palette.secondary.lighter};
      margin: 0;
      font-size: 0.75rem;
      line-height: 1;
    }
  }
`;

function getInitials(user: Account) {
  if (user.firstName && user.lastName) {
    return `${user.firstName.trim().slice(0, 1).toUpperCase()}${user.lastName.slice(0, 1).toUpperCase()}`;
  }

  if (user.firstName) {
    if (user.firstName.trim().indexOf(' ') > -1) {
      const [part1, part2] = user.firstName.trim().split(' ');
      return `${part1.trim().slice(0, 1).toUpperCase()}${part2.slice(0, 1).toUpperCase()}`;
    }
    return `${user.firstName.trim().slice(0, 1).toUpperCase()}`;
  }

  return '';
}

function getDiplayName(user: Account) {
  if (user.firstName && user.lastName) {
    return `${user.firstName} ${user.lastName}`;
  }

  if (user.firstName) {
    return `${user.firstName}`;
  }

  return 'Unknown Name';
}

export type LoginUserProps = {
  className?: string;
};

function LoginUser({ className = '' }: LoginUserProps) {
  const theme = useTheme();
  const { isAuthenticated, user } = useAuth();
  const { themeMode, onToggleMode } = useSettings();
  // const { colorOption, onChangeColor } = useSettings();
  const [popup, setPopup] = React.useState(false);
  const [open, setOpen] = React.useState(false);
  const anchorRef = React.useRef<HTMLButtonElement>(null);
  // const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const handleToggle = () => {
    setOpen((prevOpen) => !prevOpen);
  };

  const prevOpen = React.useRef(open);
  React.useEffect(() => {
    if (prevOpen.current === true && open === false) {
      anchorRef.current!.focus();
    }

    prevOpen.current = open;
  }, [open]);

  const handleClickOpen = () => {
    setPopup(true);
  };
  const handleClickClose = () => {
    setPopup(false);
  };

  const handleToggleTheme = () => {
    onToggleMode();
  };

  // const handleChangeColor = (colorPreset) => {
  //   onChangeColor(colorPreset);
  // };

  if (!isAuthenticated || user === null) {
    return null;
  }

  const handleClose = (event: Event | React.SyntheticEvent) => {
    if (anchorRef.current && anchorRef.current.contains(event.target as HTMLElement)) {
      return;
    }

    setOpen(false);
  };

  const handleLogoutPopup = () => {
    setOpen(false);
    handleClickOpen();
  };

  // function handleListKeyDown(event: React.KeyboardEvent) {
  //   if (event.key === 'Tab') {
  //     event.preventDefault();
  //     setOpen(false);
  //   } else if (event.key === 'Escape') {
  //     setOpen(false);
  //   }
  // }

  // return focus to the button when we transitioned from !open -> open

  return (
    <LoginUserRoot className={className}>
      <Box sx={{ display: 'flex', alignItems: 'center', textAlign: 'center' }}>
        <Tooltip title="Account settings">
          <IconButton
            ref={anchorRef}
            id="composition-button"
            aria-controls={open ? 'composition-menu' : undefined}
            aria-expanded={open ? 'true' : undefined}
            aria-haspopup="true"
            onClick={handleToggle}
          >
            <Avatar
              src={user.photo}
              sx={{
                bgcolor: theme.themeMode === 'light' ? theme.palette.primary.main : theme.palette.primary.main,
                width: '40px',
                height: '40px',
                color: theme.palette.common.white,
              }}
            >
              {getInitials(user)}
            </Avatar>
          </IconButton>
        </Tooltip>
      </Box>
      <Popper open={open} anchorEl={anchorRef.current} role={undefined} placement="top-start" transition disablePortal>
        {({ TransitionProps, placement }) => (
          <Grow
            {...TransitionProps}
            style={{
              transformOrigin: placement === 'top-start' ? 'top right ' : 'top right ',
            }}
          >
            <Paper sx={{ boxShadow: 10, overFlow: 'hidden' }}>
              <ClickAwayListener onClickAway={handleClose}>
                <MenuList autoFocusItem={open} id="composition-menu" aria-labelledby="composition-button">
                  <MenuItem onClick={handleClose}>
                    <Avatar
                      sx={{
                        width: '35px',
                        height: '35px',
                        color: theme.palette.common.white,
                        bgcolor: theme.themeMode === 'light' ? theme.palette.primary.main : theme.palette.primary.main,
                      }}
                    >
                      {getInitials(user)}
                    </Avatar>
                    <Box ml={2.5}>
                      <Typography sx={{ fontSize: '0.875rem', fontFamily: 'Poppins semibold' }}>
                        {getDiplayName(user)}
                      </Typography>
                      <Typography sx={{ fontSize: '0.75rem', fontFamily: 'Poppins Regular' }}>
                        {user.roleName}
                      </Typography>
                    </Box>
                  </MenuItem>
                  <Divider />
                  <MenuItem onClick={handleClose} sx={{ py: 2 }}>
                    <ListItemIcon>
                      <SlUser className="fs-5" />
                    </ListItemIcon>
                    <Typography variant="body2" fontSize={14}>
                      Profile settings
                    </Typography>
                  </MenuItem>
                  {/* <MenuItem sx={{ py: 2 }}>
                    <ListItemIcon>
                      <VscSymbolColor className="fs-5" />
                    </ListItemIcon>
                    <Typography variant="body2" fontSize={14} pr={1}>
                      Themes
                    </Typography>
                    <Stack direction="column" spacing={1}>
                      <Box
                        sx={{ width: '15px', height: '15px', borderRadius: '50%', backgroundColor: '#fda92d' }}
                        onClick={() => {
                          handleChangeColor('orange');
                        }}
                      />
                    </Stack>
                  </MenuItem> */}
                  <MenuItem className="d-lg-none ">
                    <ListItemIcon>
                      {themeMode === 'light' ? <CiDark className=" fs-3" /> : <MdOutlineLightMode className=" fs-3" />}
                    </ListItemIcon>
                    <ListItemText
                      id="switch-list-label-wifi"
                      primaryTypographyProps={{ fontSize: '14px' }}
                      primary={`Switch to ${themeMode === 'light' ? 'Dark' : 'Light'}`}
                    />
                    <Switch edge="end" onChange={handleToggleTheme} checked={themeMode !== 'light'} />
                  </MenuItem>
                  <MenuItem onClick={handleLogoutPopup} sx={{ py: 2 }}>
                    <ListItemIcon>
                      <FiLogOut color={theme.palette.primary.main} className=" fs-5" />
                    </ListItemIcon>
                    <Typography variant="body2" fontSize={14} color="primary">
                      Logout
                    </Typography>
                  </MenuItem>
                </MenuList>
              </ClickAwayListener>
            </Paper>
          </Grow>
        )}
      </Popper>

      <Box className="details d-none d-lg-flex">
        <h4 className="name">{getDiplayName(user)}</h4>
        <span className="role">{user.roleName}</span>
      </Box>
      <LogoutBox popup={popup} handleClickOpen={handleClickOpen} handleClickClose={handleClickClose} />
    </LoginUserRoot>
  );
}

export default LoginUser;
