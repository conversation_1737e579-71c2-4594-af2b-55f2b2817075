/* eslint-disable no-plusplus */
/* eslint-disable react/jsx-no-useless-fragment */
/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import Page from '@/components/shared/Page';
import { Autocomplete, Grid, TextField, Box, Typography, Stack, Button, Card, Divider } from '@mui/material';
import styled from 'styled-components';
import Popup from '@/components/shared/Popup/Popup';
import { TYPE_SELECT } from '@/config/Selection';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';

const SendToOthersRoot = styled.div`
  padding: 1rem;

  li {
    padding-bottom: 5px;
  }
  .sub-heading {
    font-size: 90%;
    color: ${(props) => props.theme.palette.grey[600]};
  }
  .Card {
    min-height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }
  }
  .message {
    font-size: 13px;
    color: ${(props) => props.theme.palette.grey[600]};
  }
`;

function SendToOthers() {
  const [popup, setPopup] = React.useState(false);
  const handleClickOpen = () => {
    setPopup(true);
  };
  const handleClickClose = () => {
    setPopup(false);
  };

  return (
    <Page title="Send To Others">
      <SendToOthersRoot>
        <Card className="Card" elevation={5} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Typography variant="h6" fontSize={17}>
            Send To Others
          </Typography>
          <Divider />
          <Grid container py={2} spacing={3}>
            <Grid item md={6} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Message Title
              </Typography>
              <TextField fullWidth placeholder="Enter Message Title" />
            </Grid>
            <Grid item md={6} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Message Type
              </Typography>
              <Autocomplete
                options={TYPE_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select Type" />}
              />
            </Grid>
            <Grid item md={6} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Message Template Id
              </Typography>
              <TextField fullWidth placeholder="Enter Message Template Id" />
            </Grid>
            <Grid item md={6} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Phone Number
              </Typography>
              <TextField fullWidth placeholder="Enter Phone Number" />
            </Grid>
            <Grid item xs={12}>
              <Typography variant="h6" fontSize={14}>
                Message
              </Typography>
              <TextField
                fullWidth
                multiline
                minRows={4}
                InputProps={{ inputProps: { style: { resize: 'both' } } }}
                placeholder="Enter Message Content..."
              />
            </Grid>
          </Grid>

          <Grid pt={3} container spacing={2}>
            <Grid item xs={12} md={6}>
              <ul className="message">
                <li>Please do not copy paste message from Word, Excel, Notepad etc.</li>
                <li>If copied, Please ensure the special character like @ ,# ,$ ,& ,&quot; ,etc.</li>
              </ul>
            </Grid>
            <Grid item xs={12} md={6}>
              <ul className="message">
                <li>Or please remove special characters then retype from the keyboard.</li>
                <li>Kindly avoid unwanted space and unwanted next line for better delivery.</li>
              </ul>
            </Grid>
          </Grid>
          <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 3 }}>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="secondary">
                Cancel
              </Button>
              <Button onClick={handleClickOpen} variant="contained" color="primary">
                Send
              </Button>
            </Stack>
          </Box>
        </Card>
      </SendToOthersRoot>
      {/* <TemporaryDrawer onclickdrwer={onclick} open="right" /> */}
      <Popup size="xs" state={popup} onClose={handleClickClose} popupContent={<SuccessMessage />} />
    </Page>
  );
}

export default SendToOthers;
