import React, { use<PERSON><PERSON>back, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  FormControl,
  MenuItem,
  Select,
  Collapse,
} from '@mui/material';
import styled from 'styled-components';
import { ClassListInfo } from '@/types/AcademicManagement';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { EXAM_SELECT_OPTIONS } from '@/config/Selection';
import SearchIcon from '@mui/icons-material/Search';
import typography from '@/theme/typography';
import TabButton from '@/components/shared/TabButton';

const StudentWiseRoot = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      height: calc(100% - 40px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
    .card_top {
      display: flex;
      justify-content: space-between;
      flex-direction: row;
      flex-wrap: wrap;
    }
    .title_searchbar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex: 1;
    }
    .progress_report_tab {
      display: flex;
      align-items: center;
      flex-direction: row;
    }
    @media screen and (max-width: 820px) {
      .card_top {
        display: flex;
        flex-direction: column;
      }
      .progress_report_tab {
        display: flex;
        justify-content: end;
        overflow-x: scroll;
        ::-webkit-scrollbar {
          height: 10px;
        }
        ::-webkit-scrollbar-thumb {
          background-color: ${(props) => props.theme.palette.grey[400]};
          border-radius: 20px;
        }
      }
    }
    @media screen and (max-width: 998px) {
      .progress_report_tab {
        justify-content: end;
      }
    }
    @media screen and (max-width: 768px) {
      .card_top {
        display: flex;
        flex-direction: row;
      }
      .progress_report_tab {
        ::-webkit-scrollbar {
          height: 0px;
        }
      }
    }
    @media screen and (max-width: 580px) {
      .card_top {
        display: flex;
        flex-direction: column;
      }
    }
  }
`;
export type SendProgressReportProps = {
  onClickSendProgressReport: () => void;
};
function StudentWise({ onClickSendProgressReport }: SendProgressReportProps) {
  const [showFilter, setShowFilter] = useState(false);

  const getRowKey = useCallback((row: ClassListInfo) => row.classId, []);

  const classListColumns: DataTableColumn<ClassListInfo>[] = useMemo(
    () => [
      {
        name: 'slNo',
        headerLabel: 'Sl No',
        dataKey: 'slNo',
        sortable: true,
      },
      {
        name: 'studentName',
        dataKey: 'studentName',
        headerLabel: 'Student Name',
      },
      {
        name: 'className',
        dataKey: 'className',
        headerLabel: 'Class',
        sortable: true,
      },
      {
        name: 'rollNo',
        headerLabel: 'Roll Number',
        dataKey: 'rollNo',
        sortable: true,
      },
      {
        name: 'contactName',
        headerLabel: 'Contact Name',
        dataKey: 'contactName',
      },
      {
        name: 'contactNo',
        headerLabel: 'Contact No',
        dataKey: 'contactNo',
      },
    ],
    []
  );

  return (
    <Page title="Student Individual">
      <StudentWiseRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <div className="card_top ">
            <div className="title_searchbar">
              <Typography variant="h6" fontSize={17}>
                Student Individual
              </Typography>

              <Box sx={{ flexShrink: 0 }}>
                <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                  <SearchIcon />
                </IconButton>
              </Box>
            </div>
            <Stack className="progress_report_tab">
              <div style={{ flexShrink: 0 }}>
                <TabButton title="Send To Parents " variant="outlined" onClick={onClickSendProgressReport} />
                <TabButton title="Student Wise" variant="contained" />
              </div>
            </Stack>
          </div>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={3} alignItems="end">
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', xl: 240 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Year
                      </Typography>
                      <Select className="select_box" labelId="classStatusFilter" id="classStatusFilterSelect">
                        <MenuItem value={-1}>All</MenuItem>
                        <MenuItem>2025-2026</MenuItem>
                        <MenuItem>2024-2025</MenuItem>
                        <MenuItem>2023-2024</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', xl: 240 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Select className="select_box" labelId="classStatusFilter" id="classStatusFilterSelect">
                        <MenuItem value={-1}>All</MenuItem>
                        <MenuItem>I</MenuItem>
                        <MenuItem>II</MenuItem>
                        <MenuItem>III</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', xl: 240 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Exam
                      </Typography>
                      <Select className="select_box" labelId="classStatusFilter" id="classStatusFilterSelect">
                        <MenuItem value={-1}>All</MenuItem>
                        {EXAM_SELECT_OPTIONS.map((opt) => (
                          <MenuItem key={opt.id} value={opt.id}>
                            {opt.exam}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12} sm={6}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      {/* <Button type="submit" variant="contained" color="primary" fullWidth>
                    Search
                  </Button> */}
                      <Button type="reset" variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>
            <Paper className="card-table-container" sx={{ marginTop: '12px' }}>
              <DataTable
                tableWidth={600}
                columns={classListColumns}
                data={[]}
                getRowKey={getRowKey}
                fetchStatus="success"
              />
            </Paper>
          </div>
          <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 3 }}>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="secondary" sx={{ fontFamily: typography.fontFamily }}>
                Cancel
              </Button>
              <Button variant="contained" color="primary">
                Send
              </Button>
            </Stack>
          </Box>
        </Card>
      </StudentWiseRoot>
    </Page>
  );
}

export default StudentWise;
