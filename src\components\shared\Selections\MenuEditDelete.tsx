import * as React from 'react';
import IconButton from '@mui/material/IconButton';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import { EditDeleteProps } from '@/types/Templates';
import { Typography } from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import { useTheme } from 'styled-components';

const ITEM_HEIGHT = 48;

export default function MenuEditDelete({ Delete, Edit, color }: EditDeleteProps) {
  const theme = useTheme();
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  const handleDelete = () => {
    Delete();
    handleClose();
  };
  const handleEdit = () => {
    // updatedatas();
    Edit();
    handleClose();
  };

  return (
    <div>
      <IconButton
        size="small"
        aria-label="more"
        id="long-button"
        aria-controls={open ? 'long-menu' : undefined}
        aria-expanded={open ? 'true' : undefined}
        aria-haspopup="true"
        onClick={handleClick}
      >
        <MoreVertIcon fontSize="small" style={{ color: color ?? '' }} />
      </IconButton>
      <Menu
        id="long-menu"
        MenuListProps={{
          'aria-labelledby': 'long-button',
        }}
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        PaperProps={{
          style: {
            maxHeight: ITEM_HEIGHT * 4.5,
            width: '15ch',
          },
        }}
      >
        <MenuItem onClick={handleEdit} sx={{ gap: 2, borderBottom: 1, borderColor: theme.palette.grey[300] }}>
          <ModeEditIcon sx={{ fontSize: 18, color: theme.palette.grey[600] }} />
          <Typography variant="h6" fontSize={12}>
            Edit
          </Typography>
        </MenuItem>
        <MenuItem onClick={handleDelete} sx={{ gap: 2 }}>
          <DeleteIcon sx={{ fontSize: 18, color: theme.palette.grey[600] }} />
          <Typography variant="h6" fontSize={12}>
            Delete
          </Typography>
        </MenuItem>
      </Menu>
    </div>
  );
}
