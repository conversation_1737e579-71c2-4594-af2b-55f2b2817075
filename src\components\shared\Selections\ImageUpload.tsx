import React, { useState } from 'react';
import { TextField, InputAdornment, Button, Avatar, IconButton, Typography } from '@mui/material';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import CloseIcon from '@mui/icons-material/Close';
import { TbCloudUpload } from 'react-icons/tb';

const SingleImageUpload = ({ setState, state }) => {
  const [error, setError] = useState('');

  const handleFileChange = (event) => {
    const selectedFile = event.target.files[0];

    if (selectedFile) {
      setState(selectedFile);
      setError('');
    } else if (state === null) {
      setError('Please select a valid image file.');
    }
  };

  const handleRemoveFile = () => {
    setState(null);
    setError('');
  };

  return (
    <>
      <TextField
        error={!!error}
        helperText={error}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              {state && (
                <Avatar alt="Uploaded File" src={URL.createObjectURL(state)}>
                  <IconButton onClick={handleRemoveFile} size="small" sx={{ zIndex: 11 }}>
                    <CloseIcon />
                  </IconButton>
                </Avatar>
              )}
            </InputAdornment>
          ),
          endAdornment: (
            <InputAdornment position="end">
              <Button size="small" component="label" variant="contained" startIcon={<TbCloudUpload />}>
                Upload Image
                <input type="file" accept="image/*" style={{ display: 'none' }} onChange={handleFileChange} />
              </Button>
            </InputAdornment>
          ),
        }}
        variant="outlined"
        fullWidth
        disabled={state !== ''}
      />
      {state && (
        <Typography variant="caption" color="textSecondary" mt={1} ml={2.5}>
          Image selected: {state.name}
        </Typography>
      )}
    </>
  );
};

export default SingleImageUpload;
