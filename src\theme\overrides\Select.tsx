import { Components, Theme } from '@mui/material';
import useSettings from '@/hooks/useSettings';
import { InputSelectIcon } from './CustomIcons';

export default function Select(theme: Theme): Components<Omit<Theme, 'components'>> {
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  return {
    MuiSelect: {
      defaultProps: {
        IconComponent: InputSelectIcon,
        // variant: 'standard',
      },
      styleOverrides: {
        standard: {
          backgroundColor: isLight ? '#F4F6F8' : '#161C24',
          padding: 9,
          borderRadius: 8,
        },
        outlined: {
          padding: '0.57rem 0.675rem',
        },
        icon: {
          right: 7, // <-- adjust this for paddingRight
          color: '#5c5c5c', // optional, for icon color
          pointerEvents: 'none', // optional if you want it to be non-clickable
        },
      },
    },
    MuiInput: {
      styleOverrides: {
        root: {
          borderRadius: 8, // ✅ Correct placement
          backgroundColor: isLight ? '#F4F6F8' : '#161C24',
          fontSize: '13px',
        },
        underline: {
          '&:before': {
            borderBottom: 'none',
          },
          '&:after': {
            borderBottom: 'none',
          },
          '&:hover:not(.Mui-disabled):before': {
            borderBottom: 'none',
          },
        },
      },
    },
  };
}
