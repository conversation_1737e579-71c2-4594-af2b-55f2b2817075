/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import {
  Autocomplete,
  Grid,
  Stack,
  TextField,
  Button,
  Typography,
  Box,
  FormControl,
  Select,
  MenuItem,
  RadioGroup,
  FormControlLabel,
  Radio,
} from '@mui/material';
import styled from 'styled-components';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import { CLASS_SELECT, STATUS_OPTIONS, SUBJECT_SELECT, YEAR_SELECT } from '@/config/Selection';
import SelectDateTimePicker from '@/components/shared/Selections/TimePicker';
import InputFileUpload from '@/components/shared/Selections/FilesUpload';
import DateSelect from '@/components/shared/Selections/DateSelect';
import TextareaField from '@/components/shared/Selections/TextareaField';

const CreateCcFormRoot = styled.div`
  width: 100%;
  padding: 1.5rem;
`;

function CreateSessionForm({ onClose }: any) {
  return (
    <CreateCcFormRoot>
      <form noValidate>
        <Grid container spacing={2} direction="row" justifyContent="space-between">
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Academic Year
              </Typography>
              <Select
                labelId="messageTypeFilter"
                id="messageTypeFilterSelect"
                // value={messageTypeFilter?.toString() || '-1'}
                // onChange={handleTypeChange}
              >
                {/* {MESSAGE_TYPE_OPTIONS.map((opt) => (
                  <MenuItem key={opt.id} value={opt.id}>
                    {opt.name}
                  </MenuItem> */}
                {/* ))} */}
              </Select>
            </FormControl>
          </Grid>
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Class
              </Typography>
              <Select
                labelId="messageTypeFilter"
                id="messageTypeFilterSelect"
                // value={messageTypeFilter?.toString() || '-1'}
                // onChange={handleTypeChange}
              >
                {/* {MESSAGE_TYPE_OPTIONS.map((opt) => (
                  <MenuItem key={opt.id} value={opt.id}>
                    {opt.name}
                  </MenuItem> */}
                {/* ))} */}
              </Select>
            </FormControl>
          </Grid>
          <Grid item lg={12} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Subject
              </Typography>
              <Select
                labelId="messageTypeFilter"
                id="messageTypeFilterSelect"
                // value={messageTypeFilter?.toString() || '-1'}
                // onChange={handleTypeChange}
              >
                {/* {MESSAGE_TYPE_OPTIONS.map((opt) => (
                  <MenuItem key={opt.id} value={opt.id}>
                    {opt.name}
                  </MenuItem> */}
                {/* ))} */}
              </Select>
            </FormControl>
          </Grid>
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Unit
              </Typography>
              <TextField placeholder="Enter student name " name="" />
            </FormControl>
          </Grid>
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Topic
              </Typography>
              <TextField placeholder="Enter name " name="" />
            </FormControl>
          </Grid>
          <Grid item lg={12} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Curricular Objectives
              </Typography>
              <TextareaField
                // limit="4000"
                // ShowCharectersCount={messageType}
                placeholder="Enter content..."
                name="messageContent"
                value=""
                // onChange={''}
                // error={touched.messageContent && !!errors.messageContent}
                // helperText={touched.messageContent && errors.messageContent}
                InputProps={{
                  inputProps: {
                    style: { resize: 'vertical', minHeight: '60px', maxHeight: '100px' },
                    // maxLength: limit,
                  },
                  //   endAdornment: touched.messageContent && !!errors.messageContent && (
                  //     <InputAdornment position="end">
                  //       <ErrorIcon color="error" sx={{ mr: 2 }} />
                  //     </InputAdornment>
                  //   ),
                }}
              />
            </FormControl>
          </Grid>
          <Grid item lg={12} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Learning Aids
              </Typography>
              <TextareaField
                // limit="4000"
                // ShowCharectersCount={messageType}
                placeholder="Enter content..."
                name="messageContent"
                value=""
                // onChange={''}
                // error={touched.messageContent && !!errors.messageContent}
                // helperText={touched.messageContent && errors.messageContent}
                InputProps={{
                  inputProps: {
                    style: { resize: 'vertical', minHeight: '60px', maxHeight: '100px' },
                    // maxLength: limit,
                  },
                  //   endAdornment: touched.messageContent && !!errors.messageContent && (
                  //     <InputAdornment position="end">
                  //       <ErrorIcon color="error" sx={{ mr: 2 }} />
                  //     </InputAdornment>
                  //   ),
                }}
              />
            </FormControl>
          </Grid>
          <Grid item lg={12} xs={12}>
            <FormControl fullWidth>
              <Typography variant="h6" fontSize={14} mb={1}>
                Entry Activity
              </Typography>
              <TextareaField
                // limit="4000"
                // ShowCharectersCount={messageType}
                placeholder="Enter content..."
                name="messageContent"
                value=""
                // onChange={''}
                // error={touched.messageContent && !!errors.messageContent}
                // helperText={touched.messageContent && errors.messageContent}
                InputProps={{
                  inputProps: {
                    style: { resize: 'vertical', minHeight: '60px', maxHeight: '100px' },
                    // maxLength: limit,
                  },
                  //   endAdornment: touched.messageContent && !!errors.messageContent && (
                  //     <InputAdornment position="end">
                  //       <ErrorIcon color="error" sx={{ mr: 2 }} />
                  //     </InputAdornment>
                  //   ),
                }}
              />
            </FormControl>
          </Grid>
          <Grid item lg={12} xs={12}>
            <FormControl fullWidth>
              <Typography variant="h6" fontSize={14} mb={1}>
                Explanation
              </Typography>
              <TextareaField
                // limit="4000"
                // ShowCharectersCount={messageType}
                placeholder="Enter content..."
                name="messageContent"
                value=""
                // onChange={''}
                // error={touched.messageContent && !!errors.messageContent}
                // helperText={touched.messageContent && errors.messageContent}
                InputProps={{
                  inputProps: {
                    style: { resize: 'vertical', minHeight: '60px', maxHeight: '100px' },
                    // maxLength: limit,
                  },
                  //   endAdornment: touched.messageContent && !!errors.messageContent && (
                  //     <InputAdornment position="end">
                  //       <ErrorIcon color="error" sx={{ mr: 2 }} />
                  //     </InputAdornment>
                  //   ),
                }}
              />
            </FormControl>
          </Grid>
          <Grid item lg={12} xs={12}>
            <FormControl fullWidth>
              <Typography variant="h6" fontSize={14} mb={1}>
                Explanation
              </Typography>
              <TextareaField
                // limit="4000"
                // ShowCharectersCount={messageType}
                placeholder="Enter content..."
                name="messageContent"
                value=""
                // onChange={''}
                // error={touched.messageContent && !!errors.messageContent}
                // helperText={touched.messageContent && errors.messageContent}
                InputProps={{
                  inputProps: {
                    style: { resize: 'vertical', minHeight: '60px', maxHeight: '100px' },
                    // maxLength: limit,
                  },
                  //   endAdornment: touched.messageContent && !!errors.messageContent && (
                  //     <InputAdornment position="end">
                  //       <ErrorIcon color="error" sx={{ mr: 2 }} />
                  //     </InputAdornment>
                  //   ),
                }}
              />
            </FormControl>
          </Grid>
        </Grid>
        <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 3 }}>
          <Stack spacing={2} direction="row">
            <Button variant="contained" color="secondary">
              Cancel
            </Button>
            <Button variant="contained" color="primary">
              Save
            </Button>
          </Stack>
        </Box>
      </form>
    </CreateCcFormRoot>
  );
}

export default CreateSessionForm;
