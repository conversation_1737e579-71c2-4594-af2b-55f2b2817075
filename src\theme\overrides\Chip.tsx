//
import { Components, Theme } from '@mui/material';
import { CloseIcon } from './CustomIcons';

export default function Chip(theme: Theme): Components<Theme> {
  return {
    MuiChip: {
      defaultProps: {
        deleteIcon: <CloseIcon />,
      },

      styleOverrides: {
        filled: {
          '& .MuiChip-avatarMedium, .MuiChip-avatarSmall': {
            color: theme.palette.text.secondary,
          },
          '&.MuiChip-colorSuccess': {
            backgroundColor: theme.palette.success.lighter,
            color: theme.palette.success.dark,
          },
          '&.MuiChip-colorWarning': {
            backgroundColor: theme.palette.warning.lighter,
            color: theme.palette.warning.dark,
          },
          '&.MuiChip-colorError': {
            backgroundColor: theme.palette.error.lighter,
            color: theme.palette.error.dark,
          },
          '&.MuiChip-colorSecondary': {
            backgroundColor: theme.palette.secondary.lighter,
            color: theme.palette.secondary.dark,
          },
          '&.MuiChip-colorInfo': {
            backgroundColor: theme.palette.info.lighter,
            color: theme.palette.info.dark,
          },
        },
        outlined: {
          borderColor: theme.palette.grey[500_32],
          '&.MuiChip-colorPrimary': {
            borderColor: theme.palette.primary.main,
            backgroundColor: theme.palette.primary.lighter,
            color: theme.palette.primary.dark,
          },
          '&.MuiChip-colorSecondary': {
            borderColor: theme.palette.secondary.main,
            backgroundColor: theme.palette.secondary.lighter,
            color: theme.palette.secondary.dark,
          },
          '&.MuiChip-colorSuccess': {
            borderColor: theme.palette.success.dark,
            backgroundColor: theme.palette.success.lighter,
            color: theme.palette.success.dark,
          },
          '&.MuiChip-colorError': {
            borderColor: theme.palette.error.dark,
            backgroundColor: theme.palette.error.lighter,
            color: theme.palette.error.main,
          },
          '&.MuiChip-colorWarning': {
            borderColor: theme.palette.warning.dark,
            backgroundColor: theme.palette.warning.lighter,
            color: theme.palette.warning.dark,
          },
          '&.MuiChip-colorInfo': {
            borderColor: theme.palette.info.dark,
            backgroundColor: theme.palette.info.lighter,
            color: theme.palette.info.dark,
          },
        },

        colorInfo: {
          color: theme.palette.info.contrastText,
          backgroundColor: theme.palette.info.dark,
        },
        // colorSuccess: {
        //   color: theme.palette.success.contrastText,
        //   borderColor: theme.palette.success.dark,
        // },
        colorWarning: {
          color: theme.palette.common.white,
          backgroundColor: theme.palette.warning.main,
          '&:hover': {
            backgroundColor: theme.palette.warning.main,
          },
        },
        colorError: {
          color: theme.palette.error.contrastText,
          backgroundColor: theme.palette.error.dark,
        },
      },
    },
  };
}
