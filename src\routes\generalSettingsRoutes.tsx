import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import Loadable from '@/components/shared/Loadable';

const GeneralSettings = Loadable(lazy(() => import('@/pages/GeneralSettings')));
const AboutUs = Loadable(lazy(() => import('@/features/GeneralSettings/AboutUs')));
const AuthorizeKeywords = Loadable(lazy(() => import('@/features/GeneralSettings/AuthorizeKeywords')));
const ContactUs = Loadable(lazy(() => import('@/features/GeneralSettings/ContactUs')));
const LandingImages = Loadable(lazy(() => import('@/features/GeneralSettings/LandingImages')));

export const generalSettingsRoutes: RouteObject[] = [
  {
    path: '/general-settings',
    element: <GeneralSettings />,
    children: [
      {
        path: 'about-us',
        element: <AboutUs />,
      },
      {
        path: 'contact-us',
        element: <ContactUs />,
      },
      {
        path: 'landing-images-list',
        element: <LandingImages />,
      },
      {
        path: 'authorise-keywords',
        element: <AuthorizeKeywords />,
      },
    ],
  },
];
