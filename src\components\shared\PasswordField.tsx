import { IconButton, InputAdornment, TextField } from '@mui/material';
import { ComponentProps, useCallback, useState } from 'react';
import VisibilityIcon from '@mui/icons-material/Visibility';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';

export type PasswordFieldProps = Omit<ComponentProps<typeof TextField>, 'variant' | 'type'> & {
  passwordViewable?: boolean;
};

function PasswordField({ passwordViewable = false, ...rest }: PasswordFieldProps) {
  const [showPassword, setShowPassword] = useState(false);

  const handleViewPasswordClick = useCallback(() => {
    setShowPassword((state) => !state);
  }, []);

  return (
    <TextField
      InputProps={{
        endAdornment: passwordViewable ? (
          <InputAdornment position="end">
            <IconButton
              onClick={handleViewPasswordClick}
              edge="end"
              size="small"
              sx={{ '& .MuiSvgIcon-root': { width: '0.938rem', height: '0.938rem' } }}
            >
              {showPassword ? <VisibilityIcon /> : <VisibilityOffIcon />}
            </IconButton>
          </InputAdornment>
        ) : null,
      }}
      variant="outlined"
      type={showPassword ? 'text' : 'password'}
      {...rest}
    />
  );
}

PasswordField.defaultProps = {
  passwordViewable: false,
};

export default PasswordField;
