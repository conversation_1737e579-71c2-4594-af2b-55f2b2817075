/* eslint-disable jsx-a11y/alt-text */
import React, { useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Autocomplete,
  Box,
  Chip,
  Divider,
  Grid,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  useTheme,
  IconButton,
  Collapse,
  Tooltip,
  FormControl,
  Popover,
  Checkbox,
} from '@mui/material';

import { DragDropContext, Draggable, Droppable } from 'react-beautiful-dnd';

import styled from 'styled-components';
import { CLASS_SELECT, STATUS_SELECT, SUBJECT_SELECT, YEAR_SELECT } from '@/config/Selection';
import SearchIcon from '@mui/icons-material/Search';
import Popup from '@/components/shared/Popup/Popup';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import { MdAdd } from 'react-icons/md';
import { BsArrowsAngleExpand } from 'react-icons/bs';
import useSettings from '@/hooks/useSettings';
import { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import DateSelect from '@/components/shared/Selections/DateSelect';
import MenuEditDelete from '@/components/shared/Selections/MenuEditDelete';
import CreateQuestions from './CreateQuestions';
import MapQuestions from './MapQuestions';

export const data = [
  {
    id: 1,
    order: 1,
    class: 'VII-B',
    subject: 'Test Subject',
    exam: 'Online Demo Examination 2023',
    question:
      'Lorem Ipsum available, but the majority have an alteration for this. Lorem Ipsum available, but the majority have an alteration for this?',
    options: { A: 'India', B: 'China', C: 'America', D: 'England' },
    correct: 'A',
    explaination: 'This is the sample explaination for the given question',
    explainationfile:
      'https://plus.unsplash.com/premium_photo-1661634073903-2ecdccdfc8a2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    image: '',
    video: '',
  },
  {
    id: 2,
    order: 2,
    class: 'VI-C',
    subject: 'Test Subject',
    exam: 'Online Demo Examination 2023',
    question: 'Lorem Ipsum available, but the majority haven alteration for this?',
    options: { A: 'India', B: 'China', C: 'America', D: 'England' },
    correct: 'C',
    explaination: 'This is the sample explaination for the given question',
    explainationfile:
      'https://images.unsplash.com/photo-1588075592405-d3d4f0846961?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2072&q=80',
    image:
      'https://plus.unsplash.com/premium_photo-1661634073903-2ecdccdfc8a2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    video: '',
  },
  {
    id: 3,
    order: 3,
    class: 'XII-B',
    subject: 'Test Subject',
    exam: 'Online Demo Examination 2023',
    question: 'Lorem Ipsum available, but the majority have an alteration for this?',
    options: { A: 'India', B: 'China', C: 'America', D: 'England' },
    correct: 'B',
    explaination: 'This is the sample explaination for the given question',
    explainationfile: '',
    image:
      'https://images.unsplash.com/photo-1588075592405-d3d4f0846961?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2072&q=80',
    video: '',
  },
  {
    id: 4,
    order: 4,
    class: 'V-A',
    subject: 'Test Subject',
    exam: 'Online Demo Examination 2023',
    question: 'Lorem Ipsum available, but the majority have an alteration for this?',
    options: { A: 'India', B: 'China', C: 'America', D: 'England' },
    correct: 'C',
    explaination: 'This is the sample explaination for the given question',
    explainationfile: '',
    image: '',
    video: '',
  },
  {
    id: 5,
    order: 5,
    class: 'II-B',
    subject: 'Test Subject',
    exam: 'Online Demo Examination 2023',
    question: 'Lorem Ipsum available, but the majority have an alteration for this?',
    options: { A: 'India', B: 'China', C: 'America', D: 'England' },
    correct: 'D',
    explaination: 'This is the sample explaination for the given question',
    explainationfile: '',
    image:
      'https://images.unsplash.com/photo-1588072432836-e10032774350?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2072&q=80',
    video: '',
  },
  {
    id: 6,
    order: 6,
    class: 'XII-C',
    subject: 'Test Subject',
    exam: 'Online Demo Examination 2023',
    question: 'Lorem Ipsum available, but the majority have an alteration for this?',
    options: { A: 'India', B: 'China', C: 'America', D: 'England' },
    correct: 'A',
    explaination: 'This is the sample explaination for the given question',
    explainationfile: '',
    image: '',
    video: '',
  },
  {
    id: 7,
    order: 7,
    class: 'XII-C',
    subject: 'Test Subject',
    exam: 'Online Demo Examination 2023',
    question: 'Lorem Ipsum available, but the majority have an alteration for this?',
    options: { A: 'India', B: 'China', C: 'America', D: 'England' },
    correct: 'A',
    explaination: 'This is the sample explaination for the given question',
    explainationfile: '',
    image: '',
    video: '',
  },
];

const QuestionListRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  .question_card {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  }

  .Card {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 80px);
      flex-grow: 1;

      .card-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: auto;
        ::-webkit-scrollbar {
          width: 0px;
        }

        .MuiTableContainer-root {
          max-height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

function QuestionList() {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const [showFilter, setShowFilter] = useState(false);
  const [Delete, setDelete] = useState(false);

  const handleClickDelete = () => setDelete(true);
  const handleClickCloseDelete = () => setDelete(false);

  const [createPopup, setCreatePopup] = useState(false);
  const [mapPopup, setMapPopup] = useState(false);
  const [expPopup, setExpPopup] = React.useState<HTMLDivElement | null>(null);
  const [index, setIndex] = useState<number>(-1);
  const [viewPopup, setViewPopup] = React.useState<HTMLDivElement | null>(null);

  const handleExpPopup = (event: React.MouseEvent<HTMLDivElement>, rowIndex: number) => {
    setExpPopup(event.currentTarget);
    setIndex(rowIndex);
  };
  const open = Boolean(expPopup);
  const id = open ? 'simple-popover' : undefined;

  const handleViewPopup = (event: React.MouseEvent<HTMLDivElement>, rowIndex: number) => {
    setViewPopup(event.currentTarget);
    setIndex(rowIndex);
  };
  const openView = Boolean(viewPopup);
  const idView = openView ? 'simple-popover' : undefined;
  const [isDraggingEnabled, setIsDraggingEnabled] = useState(false);
  const [orderedData, setOrderedData] = useState(data);

  const onDragEnd = (result) => {
    if (!result.destination) {
      return;
    }

    const startIndex = result.source.index;
    const endIndex = result.destination.index;

    if (startIndex === endIndex) {
      return;
    }

    const updatedData = [...orderedData];
    const [movedItem] = updatedData.splice(startIndex, 1);
    updatedData.splice(endIndex, 0, movedItem);

    // Update the order property of affected cards
    updatedData.forEach((card, index) => {
      if (index >= startIndex && index <= endIndex) {
        card.order = index + 1;
      }
    });

    setOrderedData(updatedData);
  };

  // const [changeView, setChangeView] = useState(false);
  // const [students, setStudents] = useState(StudentInfoData);
  // const [open, setOpen] = useState(false);
  // const [showNewMultiple, setShowNewMultiple] = useState(false);
  // const [selectedStudent, setSelectedStudent] = useState(null);

  // const handlePageChange = useCallback((event: unknown, newPage: number) => {
  //   // loadClassList({ ...currentClassListRequest, pageNumber: newPage + 1 });
  // }, []);

  // const handleChangeRowsPerPage = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
  //   const newPageSize = +event.target.value;
  //   // loadClassList({ ...currentClassListRequest, pageNumber: 1, pageSize: newPageSize });
  // }, []);

  // const pageProps = useMemo(
  //   () => ({
  //     rowsPerPageOptions: [10, 20, 30],
  //     pageNumber: 1,
  //     pageSize: 10,
  //     totalRecords: 100,
  //     onPageChange: handlePageChange,
  //     onRowsPerPageChange: handleChangeRowsPerPage,
  //   }),
  //   [handleChangeRowsPerPage, handlePageChange]
  // );

  // const handleOpen = (student: Student) => {
  //   setSelectedStudent(student);
  //   setOpen(true);
  // };

  // const handleClose = () => {
  //   setOpen(false);
  // };
  // const handleToggleNewMultiple = () => {
  //   setShowNewMultiple((prevState) => !prevState);
  // };

  // const handleToggle = () => {
  //   setChangeView((prevChangeView) => !prevChangeView);
  // };

  // const handleUpdate = (id: number, updatedData: Student) => {
  // const handleUpdate = (order: number, updatedData: Student) => {
  //   const updatedStudents = students.map((student) => (student.id === id ? { ...student, ...updatedData } : student));
  //   setStudents(updatedStudents);
  //   setOpen(false);
  // };

  // const getRowKey = useCallback((row: any) => row.examId, []);

  // const handleCreate = (newStudents) => {
  //   const updatedStudents = [...students, newStudents];
  //   setStudents(updatedStudents);
  //   setOpenNew(false);
  // };

  const QuestionListColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'class',
        dataKey: 'class',
        headerLabel: 'Class',
      },
      {
        name: 'subject',
        dataKey: 'subject',
        headerLabel: 'Sub',
      },
    ],
    []
  );

  //   const renderContent = () => {
  //     return students.map((student, rowIndex) => (
  //       <Card key={student.id}>
  //         {QuestionListColumns.map((column) => (
  //           <Typography key={column.name}>{student[column.dataKey]}</Typography>
  //         ))}
  //       </Card>
  //     ));
  //   };

  // const content: ReactNode = data.map((item, rowIndex: number) => {
  //   let cellData: ReactNode = null;

  //   return (
  //     <Typography key={column.name} variant="subtitle1" mb={1} fontSize={13}>
  //       <b>{cellData}</b>
  //     </Typography>
  //   );
  // });

  // Content = content;

  return (
    <Page title="Schedule List">
      <QuestionListRoot>
        <Card className="Card" elevation={1} sx={{ pb: { xs: 2, md: 3 } }}>
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
            sx={{ px: { xs: 3, md: 5 }, pt: { xs: 2, md: 3 } }}
          >
            <Typography variant="h6" fontSize={20} width="100%">
              Questions List
            </Typography>
            <Box sx={{ flexShrink: 0 }}>
              <Tooltip title="Search">
                <IconButton aria-label="" color="primary" sx={{ mr: 1 }} onClick={() => setShowFilter((x) => !x)}>
                  <SearchIcon />
                </IconButton>
              </Tooltip>
              <Button
                sx={{ borderRadius: '20px', mr: 1 }}
                size="small"
                variant="outlined"
                onClick={() => setIsDraggingEnabled((prev) => !prev)}
              >
                Order Questions
              </Button>
              <Button
                sx={{ borderRadius: '20px', borderWidth: 2 }}
                size="small"
                variant="contained"
                onClick={() => setCreatePopup(true)}
              >
                <MdAdd /> Create
              </Button>
            </Box>
          </Stack>
          <Stack direction="row" justifyContent="space-between" sx={{ px: { xs: 3, md: 5 }, pt: 1 }}>
            <Typography fontSize={15} color="GrayText" width="100%">
              <Checkbox />
              Select All
            </Typography>
            <Box sx={{ flexShrink: 0 }}>
              <Button
                variant="contained"
                size="small"
                color="success"
                sx={{
                  backgroundColor: isLight ? theme.palette.success.light : theme.palette.grey[700],
                  color: isLight ? theme.palette.success.darker : theme.palette.grey[100],
                  '&:hover': {
                    backgroundColor: isLight ? theme.palette.success.main : theme.palette.grey[800],
                    color: theme.palette.grey[100],
                  },
                  boxShadow: 0,
                  mr: 1,
                  borderRadius: '20px',
                }}
                onClick={() => setMapPopup(true)}
              >
                Map
              </Button>
              <Button
                variant="contained"
                size="small"
                color="secondary"
                sx={{
                  backgroundColor: isLight ? theme.palette.secondary.light : theme.palette.grey[700],
                  color: isLight ? theme.palette.secondary.darker : theme.palette.grey[100],
                  '&:hover': {
                    backgroundColor: isLight ? theme.palette.secondary.main : theme.palette.grey[800],
                    color: theme.palette.grey[100],
                  },
                  boxShadow: 0,
                  borderRadius: '20px',
                }}
                onClick={handleClickDelete}
              >
                Delete
              </Button>
            </Box>
          </Stack>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={2} sx={{ px: { xs: 3, md: 5 }, pb: { xs: 2, md: 5 } }}>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Autocomplete
                        options={YEAR_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select year" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Autocomplete
                        options={CLASS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select class" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Subject
                      </Typography>
                      <Autocomplete
                        options={SUBJECT_SELECT}
                        renderInput={(params) => <TextField {...params} fullWidth placeholder="Enter Subject" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Start Date
                      </Typography>
                      <DateSelect />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" md={6} xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', lg: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Status
                      </Typography>
                      <Autocomplete
                        options={STATUS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select" />}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item lg={1} xs={12}>
                    <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                      <Button variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                      {/* <Button variant="contained" color="primary" fullWidth>
                          Search
                        </Button> */}
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            {isDraggingEnabled ? (
              <Box className="card-container" sx={{ pt: 2, backgroundColor: theme.palette.info.lighter }}>
                <DragDropContext onDragEnd={isDraggingEnabled ? onDragEnd : null}>
                  <Droppable droppableId="droppable">
                    {(provided) => (
                      <div {...provided.droppableProps} ref={provided.innerRef}>
                        <Grid container spacing={2} sx={{ px: { xs: 3, md: 5 }, pb: { xs: 2, md: 3 } }}>
                          {orderedData.map((question, rowIndex) => (
                            <Draggable key={question.id} draggableId={question.id.toString()} index={rowIndex}>
                              {(provided) => (
                                <Grid
                                  item
                                  xxl={4}
                                  lg={6}
                                  md={12}
                                  sm={6}
                                  xs={12}
                                  ref={provided.innerRef}
                                  {...provided.draggableProps}
                                  {...provided.dragHandleProps}
                                >
                                  <Card
                                    className="student_card"
                                    sx={{ p: 2, boxShadow: 0, border: `1px solid ${theme.palette.grey[300]}` }}
                                  >
                                    <Box display="flex" flexDirection="column">
                                      <Stack direction="row" alignItems="center">
                                        <Checkbox />
                                        <Chip
                                          size="small"
                                          color="success"
                                          label={question.order}
                                          sx={{ border: 0 }}
                                          variant={isLight ? 'outlined' : 'filled'}
                                        />
                                        <Chip
                                          size="small"
                                          color="success"
                                          label={question.exam}
                                          sx={{ border: 0 }}
                                          variant={isLight ? 'outlined' : 'filled'}
                                        />
                                        <Box ml="auto" mr={0}>
                                          <MenuEditDelete
                                            Edit={() => {
                                              return 0;
                                            }}
                                            Delete={handleClickDelete}
                                          />
                                        </Box>
                                      </Stack>

                                      <Grid container>
                                        {QuestionListColumns.map((item) => (
                                          <Grid item xs={6}>
                                            <Stack direction="row" ml={1.5}>
                                              <Typography key={item.name} variant="h6" fontSize={11} mt={0.2}>
                                                <span style={{ color: 'Grey' }}>{item.headerLabel} : </span>
                                                {item.dataKey
                                                  ? (question as { [key: string]: any })[item.dataKey ?? '']
                                                  : item && item.renderCell && item.renderCell(question, rowIndex)}
                                              </Typography>
                                            </Stack>
                                            <Divider
                                              sx={{
                                                borderColor: isLight
                                                  ? theme.palette.grey[800]
                                                  : theme.palette.grey[100],
                                              }}
                                            />
                                          </Grid>
                                        ))}
                                        <Grid item>
                                          <Box py={1} flexGrow={1} sx={{ maxHeight: '50px', overflow: 'hidden' }}>
                                            <Typography
                                              variant="h6"
                                              fontSize={13}
                                              sx={{
                                                display: '-webkit-box',
                                                WebkitBoxOrient: 'vertical',
                                                WebkitLineClamp: 2, // Set the number of lines to display
                                                maxWidth: '400px',
                                                textOverflow: 'ellipsis',
                                              }}
                                            >
                                              {question.question}
                                            </Typography>
                                          </Box>
                                        </Grid>
                                        <Grid container mb={1} mt="auto" md={6} xs={12}>
                                          {Object.entries(question.options).map(([key, value]) => (
                                            <Grid item mx={1} xs={6} md={12}>
                                              <Typography
                                                key={key}
                                                variant="h6"
                                                color={question.correct === key ? 'Red' : 'Grey'}
                                                fontSize={13}
                                                pt={1}
                                                sx={{
                                                  maxWidth: '130px',
                                                  textOverflow: 'ellipsis',
                                                  whiteSpace: 'nowrap',
                                                  overflow: 'hidden',
                                                }}
                                              >
                                                {key} : {value}
                                              </Typography>
                                            </Grid>
                                          ))}
                                        </Grid>
                                        {question.image && (
                                          <Grid item md={6} xs={12}>
                                            <Box>
                                              <img src={question.image} height="105px" />
                                            </Box>
                                          </Grid>
                                        )}
                                      </Grid>
                                      <Stack direction="row" spacing={1} ml="auto" mt={1}>
                                        <IconButton
                                          size="small"
                                          aria-describedby={id}
                                          onClick={(event) => handleViewPopup(event, rowIndex)}
                                          // onMouseEnter={(event) => handleViewPopup(event, rowIndex)}
                                          // onMouseLeave={handleClose}
                                          sx={{
                                            backgroundColor: isLight
                                              ? theme.palette.error.lighter
                                              : theme.palette.grey[700],
                                            color: isLight ? theme.palette.error.dark : theme.palette.grey[100],
                                            height: 25,
                                            width: 100,
                                            borderRadius: '20px',
                                            fontSize: 14,
                                            justifyContent: 'space-around',
                                            pb: 1,
                                          }}
                                        >
                                          Expand <BsArrowsAngleExpand size={12} />
                                        </IconButton>
                                        <IconButton
                                          size="small"
                                          aria-describedby={id}
                                          onClick={(event) => handleExpPopup(event, rowIndex)}
                                          // onMouseEnter={(event) => handleViewPopup(event, rowIndex)}
                                          // onMouseLeave={handleClose}
                                          sx={{
                                            backgroundColor: isLight
                                              ? theme.palette.info.lighter
                                              : theme.palette.grey[700],
                                            color: isLight ? theme.palette.info.dark : theme.palette.grey[100],
                                            height: 25,
                                            width: 100,
                                            borderRadius: '20px',
                                            fontSize: 14,
                                            justifyContent: 'space-around',
                                            pb: 1,
                                          }}
                                        >
                                          Explaination
                                        </IconButton>
                                      </Stack>
                                    </Box>
                                  </Card>
                                </Grid>
                              )}
                            </Draggable>
                          ))}
                        </Grid>
                        {provided.placeholder}
                      </div>
                    )}
                  </Droppable>
                </DragDropContext>
              </Box>
            ) : (
              <Box className="card-container">
                <Grid container spacing={2} sx={{ px: { xs: 3, md: 5 }, pb: { xs: 2, md: 3 } }}>
                  {data.map((question, rowIndex) => (
                    <Grid item xxl={4} lg={6} md={12} sm={6} xs={12}>
                      <Card
                        className="student_card"
                        sx={{ p: 2, boxShadow: 0, border: `1px solid ${theme.palette.grey[300]}` }}
                      >
                        <Box display="flex" flexDirection="column">
                          <Box display="flex" alignItems="center">
                            <Checkbox />
                            <Chip
                              size="small"
                              color="success"
                              label={question.exam}
                              sx={{ border: 0 }}
                              variant={isLight ? 'outlined' : 'filled'}
                            />
                            <Box ml="auto" mr={0}>
                              <MenuEditDelete
                                Edit={() => {
                                  return 0;
                                }}
                                Delete={handleClickDelete}
                              />
                            </Box>
                          </Box>

                          <Grid container>
                            {QuestionListColumns.map((item) => (
                              <Grid item xs={6}>
                                <Stack direction="row" ml={1.5}>
                                  <Typography key={item.name} variant="h6" fontSize={11} mt={0.2}>
                                    <span style={{ color: 'Grey' }}>{item.headerLabel} : </span>
                                    {item.dataKey
                                      ? (question as { [key: string]: any })[item.dataKey ?? '']
                                      : item && item.renderCell && item.renderCell(question, rowIndex)}
                                  </Typography>
                                </Stack>
                                <Divider
                                  sx={{ borderColor: isLight ? theme.palette.grey[800] : theme.palette.grey[100] }}
                                />
                              </Grid>
                            ))}
                            <Grid item>
                              <Box py={1} flexGrow={1} sx={{ maxHeight: '50px', overflow: 'hidden' }}>
                                <Typography
                                  variant="h6"
                                  fontSize={13}
                                  sx={{
                                    display: '-webkit-box',
                                    WebkitBoxOrient: 'vertical',
                                    WebkitLineClamp: 2, // Set the number of lines to display
                                    maxWidth: '400px',
                                    textOverflow: 'ellipsis',
                                  }}
                                >
                                  {question.question}
                                </Typography>
                              </Box>
                            </Grid>
                            <Grid container mb={1} mt="auto" md={6} xs={12}>
                              {Object.entries(question.options).map(([key, value]) => (
                                <Grid item mx={1} xs={6} md={12}>
                                  <Typography
                                    key={key}
                                    variant="h6"
                                    color={question.correct === key ? 'Red' : 'Grey'}
                                    fontSize={13}
                                    pt={1}
                                    sx={{
                                      maxWidth: '130px',
                                      textOverflow: 'ellipsis',
                                      whiteSpace: 'nowrap',
                                      overflow: 'hidden',
                                    }}
                                  >
                                    {key} : {value}
                                  </Typography>
                                </Grid>
                              ))}
                            </Grid>
                            {question.image && (
                              <Grid item md={6} xs={12}>
                                <Box>
                                  <img src={question.image} height="105px" />
                                </Box>
                              </Grid>
                            )}
                          </Grid>
                          <Stack direction="row" spacing={1} ml="auto" mt={1}>
                            <IconButton
                              size="small"
                              aria-describedby={id}
                              onClick={(event) => handleViewPopup(event, rowIndex)}
                              // onMouseEnter={(event) => handleViewPopup(event, rowIndex)}
                              // onMouseLeave={handleClose}
                              sx={{
                                backgroundColor: isLight ? theme.palette.error.lighter : theme.palette.grey[700],
                                color: isLight ? theme.palette.error.dark : theme.palette.grey[100],
                                height: 25,
                                width: 100,
                                borderRadius: '20px',
                                fontSize: 14,
                                justifyContent: 'space-around',
                                pb: 1,
                              }}
                            >
                              Expand <BsArrowsAngleExpand size={12} />
                            </IconButton>
                            <IconButton
                              size="small"
                              aria-describedby={id}
                              onClick={(event) => handleExpPopup(event, rowIndex)}
                              // onMouseEnter={(event) => handleViewPopup(event, rowIndex)}
                              // onMouseLeave={handleClose}
                              sx={{
                                backgroundColor: isLight ? theme.palette.info.lighter : theme.palette.grey[700],
                                color: isLight ? theme.palette.info.dark : theme.palette.grey[100],
                                height: 25,
                                width: 100,
                                borderRadius: '20px',
                                fontSize: 14,
                                justifyContent: 'space-around',
                                pb: 1,
                              }}
                            >
                              Explaination
                            </IconButton>
                          </Stack>
                        </Box>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </Box>
            )}
          </div>
        </Card>
      </QuestionListRoot>
      <Popup
        size="xs"
        state={Delete}
        onClose={handleClickCloseDelete}
        popupContent={<DeleteMessage message="Are you sure want to delete?" />}
      />
      <Popup
        size="md"
        title="Add a new Question"
        state={createPopup}
        onClose={() => setCreatePopup(false)}
        popupContent={<CreateQuestions />}
      />
      <Popup
        size="xs"
        title="Map Questions To:"
        state={mapPopup}
        onClose={() => setMapPopup(false)}
        popupContent={<MapQuestions />}
      />
      <Popover
        id={id}
        open={open}
        anchorEl={expPopup}
        onClose={() => setExpPopup(null)}
        anchorOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
      >
        <Grid container px={3} pb={3} width={500}>
          {data[index]?.explaination && (
            <Grid item xs={12}>
              <Box>
                <Typography variant="h6" fontSize={20} py={2}>
                  Exlpaination:
                </Typography>
                <Typography fontSize={16}>{data[index]?.explaination}</Typography>
              </Box>
            </Grid>
          )}
          {data[index]?.explainationfile && (
            <Grid item xs={12}>
              <Box pt={2}>
                <img src={data[index]?.explainationfile} height="250px" />
              </Box>
            </Grid>
          )}
        </Grid>
      </Popover>
      <Popover
        id={idView}
        open={openView}
        anchorEl={viewPopup}
        onClose={() => setViewPopup(null)}
        anchorOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
      >
        <Grid container p={3} width={600} minHeight={350}>
          <Grid item>
            <Box py={3}>
              <Typography fontSize={16}>{data[index]?.question}</Typography>
            </Box>
          </Grid>
          {data[index]?.image && (
            <Grid item xs={12} pr={2}>
              <Box>
                <img src={data[index]?.image} height="300px" />
              </Box>
            </Grid>
          )}
          <Grid container xs={12}>
            {Object.entries(data[index]?.options ?? {}).map(([key, value]) => (
              <Grid item mx={1} xs={5}>
                <Typography key={key} color={data[index]?.correct === key ? 'Red' : 'inherit'} fontSize={16} pt={1}>
                  <span style={{ color: data[index]?.correct === key ? 'Red' : 'Grey' }}>{`${key}: `}</span> {value}
                </Typography>
              </Grid>
            ))}
          </Grid>
        </Grid>
      </Popover>
    </Page>
  );
}

export default QuestionList;
