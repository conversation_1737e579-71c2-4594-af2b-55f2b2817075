import React, { useRef, useState } from 'react';
import axios from 'axios'; // Import Axios library
import styled from 'styled-components';

const FileUploadRoot = styled.div`
  .file-btn {
    width: 330px;
    height: 150px;
    font-size: 18px;
    font-weight: 500;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 15px;
    color: #5d4dcc;
    background-color: #fff;
    border: 1.5px dashed #5d4dcc;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .file-btn:hover {
    color: #5d4dcc;
    background-color: #fff;
  }

  .file-btn span {
    width: 50px;
    height: 50px;
    font-size: 30px;
    color: #5d4dcc;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 25px;
    background-color: #f1efff;
  }

  .file-card {
    width: 300px;
    display: flex;
    align-items: center;
    gap: 15px;
    color: #000;
    background-color: #fff;
    border: 1px solid rgba(117, 96, 255, 0.281);
    border-radius: 6px;
    padding: 8px 15px;
  }

  .file-info {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 15px;
  }

  .file-info h6 {
    flex: 1;
    font-size: 13px;
    font-weight: 400;
  }

  .progress-bg {
    width: 100%;
    height: 5px;
    background-color: rgba(0, 0, 0, 0.076);
    border-radius: 8px;
    margin-top: 8px;
  }

  .progress {
    width: 0%;
    height: 5px;
    background-color: #5d4dcc;
    border-radius: 8px;
    transition: width 0.5s ease;
  }

  .icon {
    font-size: 30px;
    color: #7460ff;
  }

  .close-icon {
    font-size: 18px;
    cursor: pointer;
  }

  .file-info button,
  .check-circle {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 13px;
    color: #463a99;
    background-color: #f1efff;
    border: none;
    border-radius: 30px;
  }

  .upload-btn {
    width: 330px;
    font-size: 14px;
    font-weight: 500;
    color: #fff;
    background-color: #7460ff;
    border: none;
    border-radius: 8px;
    padding: 10px;
    margin-top: 15px;
    cursor: pointer;
  }
`;

const FileUpload = () => {
  const inputRef = useRef();

  const [selectedFile, setSelectedFile] = useState(null);
  const [progress, setProgress] = useState(0);
  const [uploadStatus, setUploadStatus] = useState('select');

  const handleFileChange = (event) => {
    if (event.target.files && event.target.files.length > 0) {
      setSelectedFile(event.target.files[0]);
    }
  };

  const onChooseFile = () => {
    inputRef.current.click();
  };

  const clearFileInput = () => {
    inputRef.current.value = '';
    setSelectedFile(null);
    setProgress(0);
    setUploadStatus('select');
  };

  const handleUpload = async () => {
    if (!selectedFile) return; // Ensure there is a selected file

    try {
      setUploadStatus('uploading');

      const formData = new FormData();
      formData.append('file', selectedFile);

      const response = await axios.post('http://api.passdaily.in/api/message/UploadFiles', formData, {
        onUploadProgress: (progressEvent) => {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          setProgress(percentCompleted);
        },
      });

      console.log('response', response);

      if (response.status === 200) {
        setUploadStatus('done');
      } else {
        setUploadStatus('select');
      }
    } catch (error) {
      console.error('Error uploading file:', error);
      setUploadStatus('select');
    }
  };

  return (
    <FileUploadRoot>
      <input ref={inputRef} type="file" onChange={handleFileChange} style={{ display: 'none' }} />

      {!selectedFile && (
        <button className="file-btn" onClick={onChooseFile}>
          <span className="material-symbols-outlined">upload</span> Upload File
        </button>
      )}

      {selectedFile && (
        <>
          {/* Display selected file information */}

          <button className="upload-btn" onClick={handleUpload}>
            {uploadStatus === 'select' || uploadStatus === 'uploading' ? 'Upload' : 'Done'}
          </button>
        </>
      )}
    </FileUploadRoot>
  );
};

export default FileUpload;
