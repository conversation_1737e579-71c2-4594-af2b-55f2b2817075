/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Stack,
  TextField,
  Button,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Typography,
  Card,
  Checkbox,
} from '@mui/material';
import styled from 'styled-components';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import Popup from '@/components/shared/Popup/Popup';
import { Pay } from './PayDrawer';
import { DetailedReceipt, GetReceipt } from './Receipt';

const status = ['Publish', 'Unpublish'];

const PayFeeRoot = styled.div`
  padding: 1rem;
  .Card {
    min-height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }
  }
  .MuiTableCell-root {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.grey[200] : props.theme.palette.grey[900]};
  }
  .TableCard {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.grey[200] : props.theme.palette.grey[900]};
  }
`;

function PayFee() {
  const [popup1, setPopup1] = React.useState(false);
  const [popup2, setPopup2] = React.useState(false);
  const [drawerOpen, setDrawerOpen] = React.useState<boolean>(false);

  const toggleDrawerOpen = () => setDrawerOpen(true);

  const toggleDrawerClose = () => setDrawerOpen(false);

  const handleClickReceipt1 = () => {
    setPopup1(true);
    toggleDrawerClose();
  };
  const handleClickReceipt2 = () => {
    setPopup2(true);
    toggleDrawerClose();
  };

  const handleClickCloseReceipt1 = () => setPopup1(false);
  const handleClickCloseReceipt2 = () => setPopup2(false);

  return (
    <Page title="Pay Fee">
      <PayFeeRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Typography variant="h6" fontSize={17}>
            Pay Fee
          </Typography>
          <Divider />
          <Grid pb={4} pt={2} container spacing={3}>
            <Grid item lg={3} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Academic Year
              </Typography>
              <Autocomplete
                options={status}
                renderInput={(params) => <TextField {...params} placeholder="Select Year" />}
              />
            </Grid>
            <Grid item lg={3} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Class
              </Typography>
              <Autocomplete
                options={status}
                renderInput={(params) => <TextField {...params} placeholder="Select Class" />}
              />
            </Grid>
            <Grid item lg={3} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Student
              </Typography>
              <Autocomplete
                options={status}
                renderInput={(params) => <TextField {...params} placeholder="Select Student" />}
              />
            </Grid>

            <Grid item lg={3} xs={12}>
              <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                <Button variant="contained" color="secondary" fullWidth>
                  Reset
                </Button>
                <Button variant="contained" color="primary" fullWidth>
                  Show
                </Button>
              </Stack>
            </Grid>
          </Grid>

          <Grid container spacing={10}>
            <Grid item lg={6} xs={12}>
              <Typography variant="h6" mb={2} fontSize={14}>
                Fee Info
              </Typography>
              <Card className="TableCard" sx={{ boxShadow: 0 }}>
                <TableContainer
                  sx={{
                    px: { xs: 0.5, md: 2 },
                  }}
                >
                  <Table stickyHeader aria-label="simple table">
                    <TableHead sx={{ backgroundImage: 'grey' }}>
                      <TableRow sx={{ height: '50px' }}>
                        <TableCell>Fees Paid</TableCell>
                        <TableCell align="right">Total Amount(INR)</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {[1, 2, 3, 4, 5].map((row) => (
                        <TableRow key={row} sx={{ height: '50px' }}>
                          <TableCell sx={{ fontWeight: 600 }}>Tution Fee</TableCell>
                          <TableCell align="right">650</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Card>
            </Grid>
            <Grid item lg={6} xs={12}>
              <Typography variant="h6" mb={2} fontSize={14}>
                Enter Payment Details
              </Typography>
              <Card className="TableCard" sx={{ boxShadow: 0 }}>
                <TableContainer
                  sx={{
                    px: { xs: 0.5, md: 2 },
                  }}
                >
                  <Table stickyHeader aria-label="simple table">
                    <TableHead>
                      <TableRow sx={{ height: '50px' }}>
                        <TableCell>
                          <Checkbox /> Fees
                        </TableCell>
                        <TableCell align="right">Total Amount(INR)</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {[1, 2, 3, 4, 5].map((row) => (
                        <TableRow key={row} sx={{ height: '50px' }}>
                          <TableCell sx={{ fontWeight: 600 }}>
                            {' '}
                            <Checkbox />
                            Tution Fee
                          </TableCell>
                          <TableCell align="right">
                            <TextField placeholder="Enter amount" size="small" sx={{ width: '9.375rem' }} />
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
                <Box
                  sx={{ borderTop: '2px dashed gray', mx: 2 }}
                  px={4}
                  py={3}
                  display="flex"
                  justifyContent="space-between"
                >
                  <Typography variant="body2" fontWeight={600} color="secondary">
                    Grand Total
                  </Typography>
                  <Typography variant="body2" fontWeight={600}>
                    2000
                  </Typography>
                </Box>
              </Card>
              <Box display="flex" justifyContent="space-between">
                <Stack direction="row">
                  <Stack direction="row" display="flex" alignItems="center">
                    <Checkbox />
                    <Typography color="secondary" variant="body2" fontWeight={600}>
                      Cash
                    </Typography>
                  </Stack>
                  <Stack direction="row" display="flex" alignItems="center">
                    <Checkbox />
                    <Typography color="secondary" variant="body2" fontWeight={600}>
                      Bank
                    </Typography>
                  </Stack>
                </Stack>
                <Box mt={2} display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' } }}>
                  <Button
                    onClick={toggleDrawerOpen}
                    variant="contained"
                    color="primary"
                    size="medium"
                    sx={{ width: '9.375rem' }}
                  >
                    Pay
                  </Button>
                </Box>
              </Box>
            </Grid>
          </Grid>
        </Card>
      </PayFeeRoot>
      <TemporaryDrawer
        onClose={toggleDrawerClose}
        state={drawerOpen}
        DrawerContent={<Pay handleClickReceipt1={handleClickReceipt1} handleClickReceipt2={handleClickReceipt2} />}
      />
      <Popup size="md" state={popup1} onClose={handleClickCloseReceipt1} popupContent={<GetReceipt />} />
      <Popup size="md" state={popup2} onClose={handleClickCloseReceipt2} popupContent={<DetailedReceipt />} />
    </Page>
  );
}

export default PayFee;
