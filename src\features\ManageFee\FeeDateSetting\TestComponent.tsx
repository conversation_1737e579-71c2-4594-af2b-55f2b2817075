import React, { useState, useMemo } from 'react';
import {
  Checkbox,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from '@mui/material';

// Generate 200 rows dynamically
const generateRows = (count) =>
  Array.from({ length: count }, (_, index) => ({
    id: index + 1,
    name: `Row ${index + 1}`,
    age: 20 + (index % 10), // Example age values
    city: `City ${index + 1}`, // Example city values
  }));

const initialRows = generateRows(200);

const TestComponent = () => {
  const [rows, setRows] = useState(initialRows);
  const [selectedRows, setSelectedRows] = useState(new Set());
  const [selectAll, setSelectAll] = useState(false);

  const handleRowCheckboxChange = (id) => {
    setSelectedRows((prev) => {
      const updated = new Set(prev);
      if (updated.has(id)) {
        updated.delete(id);
      } else {
        updated.add(id);
      }
      // Adjust the "select all" state
      setSelectAll(updated.size === rows.length);
      return updated;
    });
  };

  const handleAllCheckboxChange = (e) => {
    if (e.target.checked) {
      setSelectAll(true);
      setSelectedRows(new Set(rows.map((row) => row.id)));
    } else {
      setSelectAll(false);
      setSelectedRows(new Set());
    }
  };

  const isRowSelected = (id) => selectedRows.has(id);

  const renderTableCells = (row) => {
    return Object.keys(row)
      .filter((key) => key !== 'id')
      .map((columnKey) => (
        <TableCell
          key={columnKey}
          style={{
            backgroundColor: isRowSelected(row.id) ? '#d1e7fd' : 'transparent',
            border: isRowSelected(row.id) ? '2px solid #1976d2' : '1px solid #e0e0e0',
          }}
        >
          {/* <TextField
            value={row[columnKey]}
            onChange={(e) => {
              const value = e.target.value;
              setRows((prev) => prev.map((r) => (r.id === row.id ? { ...r, [columnKey]: value } : r)));
            }}
            variant="standard"
            fullWidth
          /> */}
          <input type="text" />
        </TableCell>
      ));
  };

  return (
    <TableContainer component={Paper}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell padding="checkbox">
              <Checkbox
                indeterminate={selectedRows.size > 0 && selectedRows.size < rows.length}
                checked={selectAll}
                onChange={handleAllCheckboxChange}
              />
            </TableCell>
            <TableCell>Name</TableCell>
            <TableCell>Age</TableCell>
            <TableCell>City</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {rows.map((row) => (
            <TableRow key={row.id}>
              <TableCell padding="checkbox">
                <Checkbox checked={isRowSelected(row.id)} onChange={() => handleRowCheckboxChange(row.id)} />
              </TableCell>
              {renderTableCells(row)}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default TestComponent;
