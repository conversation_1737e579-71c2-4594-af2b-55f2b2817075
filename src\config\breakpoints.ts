export const sizes = {
  xs: '390px',
  sm: '576px',
  md: '768px',
  lg: '992px',
  xl: '1300px',
  xxl: '1400px',
};

export const breakPointsMinwidth = {
  xs: `(min-width: ${sizes.xs})`,
  sm: `(min-width: ${sizes.sm})`,
  md: `(min-width: ${sizes.md})`,
  lg: `(min-width: ${sizes.lg})`,
  xl: `(min-width: ${sizes.xl})`,
  xxl: `(min-width: ${sizes.xxl})`,
};

export const breakPointsMaxwidth = {
  xs: `(max-width: ${sizes.xs})`,
  sm: `(max-width: ${sizes.sm})`,
  md: `(max-width: ${sizes.md})`,
  lg: `(max-width: ${sizes.lg})`,
  xl: `(max-width: ${sizes.xl})`,
  xxl: `(max-width: ${sizes.xxl})`,
};
