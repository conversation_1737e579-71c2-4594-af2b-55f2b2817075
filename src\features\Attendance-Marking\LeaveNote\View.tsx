import { Text<PERSON><PERSON>, Grid, <PERSON><PERSON>, <PERSON><PERSON> } from '@mui/material';

export const View = ({ onClick1, onClick2, datadummy }: any) => {
  return (
    <div>
      <Grid p={5} container columnSpacing={2} rowSpacing={4}>
        <Grid item xs={6}>
          <TextField fullWidth disabled id="" label="Student Name" defaultValue={datadummy.name} />
        </Grid>
        <Grid item xs={6}>
          <TextField fullWidth disabled id="" label="Class Name" defaultValue="VII A" />
        </Grid>
        <Grid item xs={6}>
          <TextField fullWidth disabled id="" label="Roll No." defaultValue="02" />
        </Grid>
        <Grid item xs={6}>
          <TextField fullWidth disabled id="" label="Subject" defaultValue="Leave Subject" />
        </Grid>
        <Grid item xs={12}>
          <TextField fullWidth disabled id="" label="Descriptipon" defaultValue={datadummy.description} />
        </Grid>
        <Grid item xs={6}>
          <TextField fullWidth disabled id="" label="From Date" defaultValue="15-02-2023" />
        </Grid>
        <Grid item xs={6}>
          <TextField fullWidth disabled id="" label="To Date" defaultValue="19-02-2023" />
        </Grid>
        <Grid item xs={6}>
          <TextField fullWidth disabled id="" label="Submitted Date" defaultValue="13-02-2023" />
        </Grid>
        <Grid item xs={6}>
          <TextField fullWidth disabled id="" label="Approved or Rejected By" defaultValue="15-02-2023" />
        </Grid>
        <Grid item xs={12}>
          <TextField fullWidth disabled id="" label="Status" defaultValue="Pending" />
        </Grid>
      </Grid>
      <Stack spacing={2} direction="row" pb={2} pr={3} justifyContent="right">
        <Button variant="contained" onClick={onClick2} color="secondary">
          Not Approve
        </Button>
        <Button variant="contained" onClick={onClick1} color="primary">
          Approve
        </Button>
      </Stack>
    </div>
  );
};
