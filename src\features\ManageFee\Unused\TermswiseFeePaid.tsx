/* eslint-disable jsx-a11y/alt-text */
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Typography,
  Card,
  Avatar,
} from '@mui/material';
import styled from 'styled-components';
import Excel from '@/assets/attendance/Excel.svg';
import Pdf from '@/assets/attendance/PDF.svg';
import { CLASS_SELECT, YEAR_SELECT } from '@/config/Selection';

const TermswiseFeePaidRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;
`;

export function TermswiseFeePaid() {
  return (
    <Page title="Termswise Fees Paid List">
      <TermswiseFeePaidRoot>
        <Card elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Typography variant="h6" fontSize={17}>
            Termswise Fees Paid List
          </Typography>
          <Divider />
          <Grid pb={4} pt={2} container spacing={3}>
            <Grid item lg={2} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Select Year
              </Typography>
              <Autocomplete
                options={YEAR_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select Year" />}
              />
            </Grid>

            <Grid item lg={2} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Select Class
              </Typography>
              <Autocomplete
                options={CLASS_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select Class" />}
              />
            </Grid>
            <Grid item lg={2} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Fee Title
              </Typography>
              <Autocomplete
                options={CLASS_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select Fee" />}
              />
            </Grid>

            <Grid item lg={2} xs={12}>
              <Typography variant="h6" fontSize={14}>
                From
              </Typography>
              <Autocomplete
                options={YEAR_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select Title" />}
              />
            </Grid>
            <Grid item lg={2} xs={12}>
              <Typography variant="h6" fontSize={14}>
                To
              </Typography>
              <Autocomplete
                options={YEAR_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select Title" />}
              />
            </Grid>

            <Grid item lg={2} xs={12}>
              <Stack spacing={1} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                <Button variant="contained" color="secondary" fullWidth>
                  Reset
                </Button>
                <Button variant="contained" color="primary" fullWidth>
                  Search
                </Button>
              </Stack>
            </Grid>
          </Grid>

          <Paper
            sx={{
              border: '1px solid #e8e8e9',
              width: '100%',
              overflow: 'auto',
              '&::-webkit-scrollbar': {
                width: 0,
              },
            }}
          >
            <TableContainer
              sx={{
                maxHeight: 410,
                width: { xs: '75.75rem', md: '100%' },
                '&::-webkit-scrollbar': {
                  width: 0,
                },
              }}
            >
              <Table stickyHeader aria-label="simple table">
                <TableHead>
                  <TableRow>
                    <TableCell>SL.No</TableCell>
                    <TableCell>Student Name</TableCell>
                    <TableCell>Fee Description</TableCell>
                    <TableCell>Amount Paid</TableCell>
                    <TableCell>Paid Date</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {[1, 2, 3, 4, 5].map((row) => (
                    <TableRow hover key={row}>
                      <TableCell>01</TableCell>
                      <TableCell>
                        <Stack direction="row" alignItems="center" gap={1}>
                          <Avatar src="" /> Fionna Grand
                        </Stack>
                      </TableCell>
                      <TableCell>Special Fee</TableCell>
                      <TableCell>4000</TableCell>
                      <TableCell>20/05/2023</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
          <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pt: 2 }}>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="error" sx={{ gap: 1 }} size="medium">
                <img className="icon" src={Pdf} />{' '}
                <Typography color="white" fontSize={12}>
                  Download
                </Typography>
              </Button>
              <Button variant="contained" color="success" sx={{ gap: 1 }} size="medium">
                <img className="icon" src={Excel} />{' '}
                <Typography color="white" fontSize={12}>
                  Download
                </Typography>
              </Button>
            </Stack>
          </Box>
        </Card>
      </TermswiseFeePaidRoot>
    </Page>
  );
}
