import { describe, it, expect } from 'vitest';
import { createSearchableMenuItems, filterMenuItems, highlightText, SearchableMenuItem } from '@/utils/searchUtils';
import { ListMenuItem } from '@/types/Layout';

// Mock menu data for testing
const mockMenuData: ListMenuItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: 'dashboard',
    link: '/',
    url: '/',
  },
  {
    id: 'academicManagement',
    label: 'Manage Academics',
    icon: 'academicManagement',
    link: '/academic-management',
    url: '/academic-management/year',
  },
  {
    id: 'manageStudents',
    label: 'Manage Students',
    icon: 'manageStudents',
    link: '/manage-students',
    url: '/manage-students/new',
  },
];

describe('Search Utils', () => {
  describe('createSearchableMenuItems', () => {
    it('should create searchable items from menu data', () => {
      const searchableItems = createSearchableMenuItems(mockMenuData);

      // Should include main menu items
      expect(searchableItems.length).toBeGreaterThan(mockMenuData.length);

      // Check main menu item
      const dashboardItem = searchableItems.find((item) => item.id === 'dashboard');
      expect(dashboardItem).toBeDefined();
      expect(dashboardItem?.breadcrumb).toEqual(['Dashboard']);
      expect(dashboardItem?.searchableText).toBe('dashboard');

      // Check sub-menu items are included
      const academicSubItems = searchableItems.filter(
        (item) => item.breadcrumb[0] === 'Manage Academics' && item.breadcrumb.length > 1
      );
      expect(academicSubItems.length).toBeGreaterThan(0);
    });
  });

  describe('filterMenuItems', () => {
    it('should filter items based on search query', () => {
      const searchableItems = createSearchableMenuItems(mockMenuData);

      // Test filtering by main menu label
      const dashboardResults = filterMenuItems(searchableItems, 'dashboard');
      expect(dashboardResults.length).toBeGreaterThan(0);
      expect(dashboardResults[0].label).toBe('Dashboard');

      // Test filtering by sub-menu label
      const yearResults = filterMenuItems(searchableItems, 'year');
      expect(yearResults.length).toBeGreaterThan(0);

      // Test case insensitive search
      const academicResults = filterMenuItems(searchableItems, 'ACADEMIC');
      expect(academicResults.length).toBeGreaterThan(0);

      // Test partial matching
      const manageResults = filterMenuItems(searchableItems, 'manage');
      expect(manageResults.length).toBeGreaterThan(0);
    });

    it('should return empty array for empty query', () => {
      const searchableItems = createSearchableMenuItems(mockMenuData);
      const results = filterMenuItems(searchableItems, '');
      expect(results).toEqual([]);
    });

    it('should return empty array for no matches', () => {
      const searchableItems = createSearchableMenuItems(mockMenuData);
      const results = filterMenuItems(searchableItems, 'nonexistent');
      expect(results).toEqual([]);
    });
  });

  describe('highlightText', () => {
    it('should highlight matching text', () => {
      const text = 'Manage Students';
      const query = 'manage';
      const result = highlightText(text, query);
      expect(result).toBe('<mark>Manage</mark> Students');
    });

    it('should handle case insensitive highlighting', () => {
      const text = 'Academic Management';
      const query = 'ACADEMIC';
      const result = highlightText(text, query);
      expect(result).toBe('<mark>Academic</mark> Management');
    });

    it('should return original text for empty query', () => {
      const text = 'Dashboard';
      const query = '';
      const result = highlightText(text, query);
      expect(result).toBe('Dashboard');
    });

    it('should highlight multiple matches', () => {
      const text = 'Manage Academic Management';
      const query = 'manage';
      const result = highlightText(text, query);
      expect(result).toBe('<mark>Manage</mark> Academic <mark>Management</mark>');
    });
  });
});
