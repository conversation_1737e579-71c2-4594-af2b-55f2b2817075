import React, { useState } from 'react';
import { Box, FormControl, MenuItem, Select, SelectChangeEvent, Typography } from '@mui/material';
import Holy from './StudentWiseTypes/HolyProgressReport';
import Holy2 from './StudentWiseTypes/HolyProgressReport2';
// import Holy3 from './StudentWiseTypes/HolyProgressReport3';
import <PERSON><PERSON><PERSON><PERSON> from './StudentWiseTypes/StFrancis';
import Nirmala from './StudentWiseTypes/Nirmala9&10';
import Sunrise from './StudentWiseTypes/Sunrise';
import StThomas from './StudentWiseTypes/StThomas9&10';
import StTherese from './StudentWiseTypes/StTherese';
import StThomas2 from './StudentWiseTypes/StThomasUpto8th';
import Nirmala2 from './StudentWiseTypes/NirmalaUpto8th';
import Carmel from './StudentWiseTypes/Carmel(Default)';

export type StudentClassWiseProps = {
  onClickPromotionList: () => void;
  onClickClassWise: () => void;
  onClickTopper: () => void;
  onClickGradeWise: () => void;
};

function StudentWise({
  onClickPromotionList,
  onClickClassWise,
  onClickTopper,
  onClickGradeWise,
}: StudentClassWiseProps) {
  const [selectedComponent, setSelectedComponent] = useState('StThomas');

  const handleChange = (event: SelectChangeEvent) => {
    setSelectedComponent(event.target.value as string);
  };

  return (
    <Box mt={1}>
      <FormControl
        fullWidth
        sx={{ display: 'flex', flexDirection: 'row', justifyContent: 'end', alignItems: 'center' }}
      >
        <Typography variant="h6" color="grayText" sx={{ mr: 2 }} fontSize={16}>
          Select Type :
        </Typography>
        <Select
          labelId="select-component-label"
          id="select-component"
          value={selectedComponent}
          onChange={handleChange}
          sx={{ width: '200px' }}
        >
          <MenuItem value="StThomas">St.Thomas 9th & 10th</MenuItem>
          <MenuItem value="StThomas2">St.Thomas Upto 8th</MenuItem>
          <MenuItem value="StTherese">St.Therese</MenuItem>
          <MenuItem value="Nirmala">Nirmala 9th & 10th</MenuItem>
          <MenuItem value="Nirmala2">Nirmala Upto 8th</MenuItem>
          <MenuItem value="Sunrise">Sunrise</MenuItem>
          <MenuItem value="StFrancis">St.Francis</MenuItem>
          <MenuItem value="Carmel">Carmel</MenuItem>
          <MenuItem value="Holy">Holy Angels</MenuItem>
          <MenuItem value="Holy2">Holy Angels Old</MenuItem>
        </Select>
      </FormControl>
      {selectedComponent === 'StFrancis' && (
        <StFrancis {...{ onClickPromotionList, onClickClassWise, onClickTopper, onClickGradeWise }} />
      )}
      {selectedComponent === 'Nirmala2' && (
        <Nirmala2 {...{ onClickPromotionList, onClickClassWise, onClickTopper, onClickGradeWise }} />
      )}
      {selectedComponent === 'Nirmala' && (
        <Nirmala {...{ onClickPromotionList, onClickClassWise, onClickTopper, onClickGradeWise }} />
      )}
      {selectedComponent === 'Sunrise' && (
        <Sunrise {...{ onClickPromotionList, onClickClassWise, onClickTopper, onClickGradeWise }} />
      )}
      {selectedComponent === 'StThomas' && (
        <StThomas {...{ onClickPromotionList, onClickClassWise, onClickTopper, onClickGradeWise }} />
      )}
      {selectedComponent === 'StThomas2' && (
        <StThomas2 {...{ onClickPromotionList, onClickClassWise, onClickTopper, onClickGradeWise }} />
      )}
      {selectedComponent === 'StTherese' && (
        <StTherese {...{ onClickPromotionList, onClickClassWise, onClickTopper, onClickGradeWise }} />
      )}
      {selectedComponent === 'Carmel' && (
        <Carmel {...{ onClickPromotionList, onClickClassWise, onClickTopper, onClickGradeWise }} />
      )}
      {selectedComponent === 'Holy' && (
        <Holy {...{ onClickPromotionList, onClickClassWise, onClickTopper, onClickGradeWise }} />
      )}
      {selectedComponent === 'Holy2' && <Holy2 />}
      {/* {selectedComponent === 'Holy3' && (
        <Holy3 {...{ onClickPromotionList, onClickClassWise, onClickTopper, onClickGradeWise }} />
      )} */}
    </Box>
  );
}

export default StudentWise;
