import React, { FormEvent, useCallback, useEffect, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  FormControl,
  MenuItem,
  Select,
  SelectChangeEvent,
  Collapse,
} from '@mui/material';
import styled from 'styled-components';
import { useAppSelector } from '@/hooks/useAppSelector';
import {
  getClassListData,
  getClassListPageInfo,
  getClassListStatus,
  getDeletingRecords,
  getSortColumn,
  getSortDirection,
} from '@/config/storeSelectors';
import { setSortColumn, setSortDirection } from '@/store/Academics/ClassManagement/classManagement.slice';
import { fetchClassList } from '@/store/Academics/ClassManagement/classManagement.thunks';
import { ClassListInfo, ClassListRequest } from '@/types/AcademicManagement';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { STATUS_OPTIONS, YEAR_SELECT } from '@/config/Selection';
import SearchIcon from '@mui/icons-material/Search';

const QuickMarkingRoot = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      height: calc(100% - 40px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

function QuickMarking() {
  // const [forceDispatch, setForceDispatch] = useState(false);

  const dispatch = useAppDispatch();
  const [classNameFilter, setClassNameFilter] = useState('');
  const [classStatusFilter, setClassStatusFilter] = useState(-1);

  const classListStatus = useAppSelector(getClassListStatus);
  const classListData = useAppSelector(getClassListData);
  const paginationInfo = useAppSelector(getClassListPageInfo);
  const sortColumn = useAppSelector(getSortColumn);
  const sortDirection = useAppSelector(getSortDirection);
  const deletingRecords = useAppSelector(getDeletingRecords);

  const { pagenumber, pagesize, totalrecords } = paginationInfo;

  const currentClassListRequest = useMemo(
    () => ({
      pageNumber: pagenumber,
      pageSize: pagesize,
      sortColumn,
      sortDirection,
      filters: {
        className: classNameFilter,
        classStatus: classStatusFilter,
      },
    }),
    [classNameFilter, classStatusFilter, pagenumber, pagesize, sortColumn, sortDirection]
  );

  const loadClassList = useCallback(
    (request: ClassListRequest) => {
      dispatch(fetchClassList(request));
    },
    [dispatch]
  );

  const [showFilter, setShowFilter] = useState(false);

  useEffect(() => {
    if (classListStatus === 'idle') {
      loadClassList(currentClassListRequest);
    }
  }, [loadClassList, classListStatus, currentClassListRequest]);

  const handleStatusChange = (e: SelectChangeEvent) => {
    const statusVal = parseInt(e.target.value, 10);
    setClassStatusFilter(statusVal);
    loadClassList({
      ...currentClassListRequest,
      filters: { ...currentClassListRequest.filters, classStatus: statusVal },
    });
  };

  const getRowKey = useCallback((row: ClassListInfo) => row.classId, []);

  const handlePageChange = useCallback(
    (event: unknown, newPage: number) => {
      loadClassList({ ...currentClassListRequest, pageNumber: newPage + 1 });
    },
    [currentClassListRequest, loadClassList]
  );

  const handleChangeRowsPerPage = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const newPageSize = +event.target.value;
      loadClassList({ ...currentClassListRequest, pageNumber: 1, pageSize: newPageSize });
    },
    [currentClassListRequest, loadClassList]
  );

  const handleSort = useCallback(
    (column: string) => {
      const newReq = { ...currentClassListRequest };
      if (column === sortColumn) {
        console.log('Toggling direction');
        const sortDirectionNew = sortDirection === 'asc' ? 'desc' : 'asc';
        newReq.pageNumber = 1;
        newReq.sortDirection = sortDirectionNew;
        dispatch(setSortDirection(sortDirectionNew));
      } else {
        newReq.pageNumber = 1;
        newReq.sortColumn = column;
        dispatch(setSortColumn(column));
      }

      loadClassList(newReq);
    },
    [currentClassListRequest, dispatch, loadClassList, sortColumn, sortDirection]
  );

  const classListColumns: DataTableColumn<ClassListInfo>[] = useMemo(
    () => [
      {
        name: 'classId',
        headerLabel: 'Sl Id',
        dataKey: 'classId',
        sortable: true,
      },
      {
        name: 'className',
        dataKey: 'className',
        headerLabel: 'Class',
        sortable: true,
      },
      {
        name: 'rollNo',
        dataKey: 'rollNo',
        headerLabel: 'Enter Roll No.',
        renderCell: () => {
          return <TextField fullWidth placeholder="Eg: 1 ,8 ,10 ,35...." />;
        },
      },
      {
        name: 'name',
        dataKey: 'name',
        headerLabel: 'Students Name',
        renderCell: () => {
          return <TextField minRows={1} multiline fullWidth placeholder="Alex,Jack,Micheal...." />;
        },
      },
    ],
    []
  );

  const pageProps = useMemo(
    () => ({
      rowsPerPageOptions: [10, 20, 30],
      pageNumber: pagenumber - 1,
      pageSize: pagesize,
      totalRecords: totalrecords,
      onPageChange: handlePageChange,
      onRowsPerPageChange: handleChangeRowsPerPage,
    }),
    [handleChangeRowsPerPage, handlePageChange, pagenumber, pagesize, totalrecords]
  );

  const handleReset = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      setClassNameFilter('');
      setClassStatusFilter(-1);
      loadClassList({
        ...currentClassListRequest,
        pageNumber: 1,
        pageSize: 20,
        sortColumn: 'classId',
        sortDirection: 'desc',
        filters: {
          className: '',
          classStatus: -1,
        },
      });
    },
    [currentClassListRequest, loadClassList]
  );

  const publishedClasses = classListData.filter((data) => {
    return data.classStatus === 1;
  });

  return (
    <Page title="Quick Marking">
      <QuickMarkingRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17} width="80%">
              Quick Marking
            </Typography>
            <Box pb={1} sx={{ flexShrink: 0 }}>
              <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                <SearchIcon />
              </IconButton>
            </Box>
          </Stack>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate onReset={handleReset}>
                <Grid pb={2} pt={1} container spacing={2} alignItems="end">
                  <Grid item sm="auto" xs={12}>
                    <FormControl sx={{ minWidth: 240 }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class Name
                      </Typography>
                      <TextField
                        sx={{ minWidth: 240 }}
                        name="className"
                        value={classNameFilter}
                        onChange={(e) => {
                          setClassNameFilter(e.target.value);
                          loadClassList({
                            ...currentClassListRequest,
                            filters: { ...currentClassListRequest.filters, className: e.target.value },
                          });
                        }}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item sm="auto" xs={12}>
                    <FormControl sx={{ minWidth: 240 }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Year
                      </Typography>
                      <Select
                        labelId="classStatusFilter"
                        id="classStatusFilterSelect"
                        value={classStatusFilter?.toString() || '-1'}
                      >
                        <MenuItem value={-1}>Select</MenuItem>
                        {YEAR_SELECT.map((opt) => (
                          <MenuItem key={opt} value={opt}>
                            {opt}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item sm="auto" xs={12}>
                    <FormControl sx={{ minWidth: 240 }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class Status
                      </Typography>
                      <Select
                        labelId="classStatusFilter"
                        id="classStatusFilterSelect"
                        value={classStatusFilter?.toString() || '-1'}
                        onChange={handleStatusChange}
                      >
                        <MenuItem value={-1}>All</MenuItem>
                        {STATUS_OPTIONS.map((opt) => (
                          <MenuItem key={opt.id} value={opt.id}>
                            {opt.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item sm="auto" xs={12}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      {/* <Button type="submit" variant="contained" color="primary" fullWidth>
                        Search
                      </Button> */}
                      <Button type="reset" variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>
            <Paper className="card-table-container" sx={{ marginTop: '12px' }}>
              <DataTable
                columns={classListColumns}
                data={publishedClasses}
                getRowKey={getRowKey}
                fetchStatus={classListStatus}
                allowPagination
                allowSorting
                PaginationProps={pageProps}
                deletingRecords={deletingRecords}
                sortColumn={sortColumn}
                sortDirection={sortDirection}
                onSort={handleSort}
              />
            </Paper>
          </div>
        </Card>
      </QuickMarkingRoot>
    </Page>
  );
}

export default QuickMarking;
