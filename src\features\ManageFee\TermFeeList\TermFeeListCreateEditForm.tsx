import React from 'react';
import { Box, Button, FormControl, Stack, TextField, Typography, Select, MenuItem } from '@mui/material';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { ErrorIcon } from '@/theme/overrides/CustomIcons';
import LoadingButton from '@mui/lab/LoadingButton';
import DatePickers from '@/components/shared/Selections/DatePicker';
import dayjs from 'dayjs';
import { CreateTermFeeSettingTitleDataType } from '@/types/ManageFee';
import { getManageFeeSubmitting } from '@/config/storeSelectors';
import { useAppSelector } from '@/hooks/useAppSelector';
import styled from 'styled-components';
import useAuth from '@/hooks/useAuth';
import { FEE_TYPE_ID_OPTIONS } from '@/config/Selection';

export type CreateEditTermFeeTitleFormProps = {
  onSave: (values: CreateTermFeeSettingTitleDataType, mode: 'create' | 'edit') => void;
  onCancel: (mode: 'create' | 'edit') => void;
  termFeeDetails: CreateTermFeeSettingTitleDataType;
};

const TermFeeListCreateEditFormRoot = styled.div`
  .MuiStack-root {
    width: 100%;
  }
`;

const CreateEditTermFeeTitleValidationSchema = Yup.object({
  termTitle: Yup.string().required('Please enter Term Title'),
  startDate: Yup.string().required('Please select Start Date'),
  endDate: Yup.string().required('Please select End Date'),
  feeTypeId: Yup.number().oneOf([1, 2], 'Please select Type'),
});

export const TermFeeListCreateEditForm = ({ onCancel, onSave, termFeeDetails }: CreateEditTermFeeTitleFormProps) => {
  const mode = termFeeDetails.termId === 0 ? 'create' : 'edit';
  const isSubmitting = useAppSelector(getManageFeeSubmitting);
  const { user } = useAuth();
  const adminId: number = user ? user.accountId : 0;

  const {
    values: { termTitle, startDate, endDate, feeTypeId },
    handleChange,
    handleBlur,
    handleSubmit,
    touched,
    errors,
    setFieldValue,
  } = useFormik<CreateTermFeeSettingTitleDataType>({
    initialValues: {
      adminId,
      termId: termFeeDetails.termId,
      termTitle: termFeeDetails.termTitle,
      startDate: termFeeDetails.startDate,
      endDate: termFeeDetails.endDate,
      feeTypeId: termFeeDetails.feeTypeId,
    },
    validationSchema: CreateEditTermFeeTitleValidationSchema,
    onSubmit: async (values) => {
      onSave(values, mode);
    },
    validateOnBlur: false,
  });

  return (
    <TermFeeListCreateEditFormRoot>
      <form noValidate onSubmit={handleSubmit}>
        <Stack
          sx={{
            height: 'calc(100vh - 150px)',
            overflow: 'auto',
            '&::-webkit-scrollbar': {
              width: 0,
            },
          }}
          direction="column"
        >
          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Term Title
            </Typography>
            <TextField
              placeholder="Enter term title"
              name="termTitle"
              value={termTitle}
              onBlur={handleBlur}
              onChange={handleChange}
              error={touched.termTitle && !!errors.termTitle}
              helperText={errors.termTitle}
              disabled={isSubmitting}
              InputProps={{
                endAdornment: touched.termTitle && !!errors.termTitle && <ErrorIcon color="error" />,
              }}
            />
          </FormControl>
          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Select Type
            </Typography>
            <Select
              name="feeTypeId"
              value={feeTypeId}
              onChange={handleChange}
              error={touched.feeTypeId && !!errors.feeTypeId}
              disabled={isSubmitting}
            >
              <MenuItem sx={{ display: 'none' }} value={0}>
                Select Type
              </MenuItem>
              {FEE_TYPE_ID_OPTIONS.map((opt) => (
                <MenuItem key={opt.id} value={opt.id}>
                  {opt.name}
                </MenuItem>
              ))}
            </Select>
            {touched.feeTypeId && !!errors.feeTypeId && (
              <Typography color="red" fontSize="12px" ml={2.5} mt={1} variant="subtitle1">
                {errors.feeTypeId}
              </Typography>
            )}
          </FormControl>
          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Start Date
            </Typography>
            <DatePickers
              disabled={isSubmitting}
              name="startDate"
              value={dayjs(startDate)}
              onChange={(e) => {
                const formattedDate = e ? e.format('DD/MM/YYYY') : '';
                setFieldValue('startDate', formattedDate);
                console.log('date::::', formattedDate);
              }}
            />
            {touched.startDate && !!errors.startDate && (
              <Typography color="red" fontSize="12px" ml={2.5} mt={1} variant="subtitle1">
                {errors.startDate}
              </Typography>
            )}
          </FormControl>
          <FormControl fullWidth>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              End Date
            </Typography>
            <DatePickers
              disabled={isSubmitting}
              name="endDate"
              value={dayjs(endDate)}
              onChange={(e) => {
                const formattedDate = e ? e.format('DD/MM/YYYY') : '';
                setFieldValue('endDate', formattedDate);
                console.log('date::::', formattedDate);
              }}
            />
            {touched.endDate && !!errors.endDate && (
              <Typography color="red" fontSize="12px" ml={2.5} mt={1} variant="subtitle1">
                {errors.endDate}
              </Typography>
            )}
          </FormControl>
        </Stack>
        <Box sx={{ justifyContent: 'center', pt: 3, position: 'relative' }}>
          <Stack spacing={2} direction="row" sx={{}}>
            <Button
              disabled={isSubmitting}
              onClick={() => onCancel(mode)}
              fullWidth
              variant="contained"
              color="secondary"
              type="button"
            >
              Cancel
            </Button>
            <LoadingButton
              loading={isSubmitting}
              fullWidth
              variant="contained"
              color="primary"
              type="submit"
              loadingPosition="start"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Saving...' : 'Save'}
            </LoadingButton>
          </Stack>
        </Box>
      </form>
    </TermFeeListCreateEditFormRoot>
  );
};
