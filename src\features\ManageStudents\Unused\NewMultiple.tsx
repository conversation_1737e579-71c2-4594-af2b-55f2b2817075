/* eslint-disable prettier/prettier */
import React from 'react';
import { <PERSON>rid, Typography, Button, Stack, TextField, Paper, Card } from '@mui/material';
import CustomTextField from '@/components/shared/Selections/CustomTextField';
import { MdArrowBack, MdDelete } from 'react-icons/md';
import BackButton from '@/components/shared/BackButton';

const DetailsField = ({ onChange }: any) => {
  return (
    <Grid container spacing={2} m={2} maxWidth="95%">
      <Grid item md={1.5} xs={6}>
        <CustomTextField name="admissionNo" label="Adm No" variant="outlined" value={undefined} onChange={onChange} />
      </Grid>
      <Grid item md={1.5} xs={6}>
        <CustomTextField name="rollNo" label="Roll No" variant="outlined" value={undefined} onChange={onChange} />
      </Grid>
      <Grid item md={3} xs={12}>
        <CustomTextField name="name" label="First Name" variant="outlined" value={undefined} onChange={onChange} />
      </Grid>
      <Grid item md={3} xs={12}>
        <CustomTextField name="name" label="Middle Name" variant="outlined" value={undefined} onChange={onChange}/>
      </Grid>
      <Grid item md={3} xs={12}>
        <CustomTextField name="name" label="Last Name" variant="outlined" value={undefined} onChange={onChange} />
      </Grid>
      <Grid item md={3} xs={12}>
        <TextField fullWidth type="file" variant="outlined" label="Photo" InputLabelProps={{ shrink: true }} />
      </Grid>
      <Grid item md={1.5} xs={6}>
        <CustomTextField name="class" label="Class" variant="outlined" value={undefined} onChange={onChange} />
      </Grid>
      <Grid item md={1.5} xs={6}>
        <CustomTextField name="S. ID" label="S. ID" variant="outlined" value={undefined} onChange={onChange} />
      </Grid>
      <Grid item md={1.5} xs={6}>
        <CustomTextField name="gender" label="Gender" variant="outlined" value={undefined} onChange={onChange} />
      </Grid>
      <Grid item md={1.5} xs={6}>
        <CustomTextField
          name="bloodGroup"
          label="Blood Group"
          variant="outlined"
          value={undefined}
          onChange={onChange}
        />
      </Grid>
      <Grid item md={1.5} xs={6}>
        <CustomTextField
          name="dateOfBirth"
          label="Date of Birth"
          variant="outlined"
          value={undefined}
          onChange={onChange}
        />
      </Grid>
      <Grid item md={3} xs={12}>
        <CustomTextField
          value={undefined}
          onChange={onChange}
          name="Contact Name"
          label="Contact Name"
          variant="outlined"
        />
      </Grid>
      <Grid item md={3} xs={12}>
        <CustomTextField
          value={undefined}
          onChange={onChange}
          name="Contact Number"
          label="Contact Number"
          variant="outlined"
        />
      </Grid>
      {/* <Grid item md={3} xs={12}>
        <CustomTextField value={undefined} onChange={onChange} name="Mother'sName" label="Mother's Name" variant="outlined" />
      </Grid>
      <Grid item md={3} xs={12}>
        <CustomTextField value={undefined} onChange={onChange} name="Mother'sNumber" label="Mother's Number" variant="outlined" />
      </Grid> */}
      {/* <Grid item md={1.5} xs={12}>
        <CustomTextField name="caste" label="Caste" variant="outlined" value={undefined} onChange={onChange} />
      </Grid>
      <Grid item md={1.5} xs={12}>
        <CustomTextField name="religion" label="Religion" variant="outlined" value={undefined} onChange={onChange} />
      </Grid>
      <Grid item md={3} xs={12}>
        <CustomTextField name="busNo" label="Bus Route" variant="outlined" value={undefined} onChange={onChange} />
      </Grid>
      <Grid item md={7.5} xs={12}>
        <CustomTextField name="Address" label="Address" variant="outlined" value={undefined} onChange={onChange} />
      </Grid>
      <Grid item md={1.5} xs={12}>
        <CustomTextField name="district" label="District" variant="outlined" value={undefined} onChange={onChange} />
      </Grid>
      <Grid item md={1.5} xs={12}>
        <CustomTextField name="taluka" label="Taluka" variant="outlined" value={undefined} onChange={onChange} />
      </Grid>
      <Grid item md={1.5} xs={12}>
        <CustomTextField name="state" label="State" variant="outlined" value={undefined} onChange={onChange} />
      </Grid> */}
    </Grid>
  );
};

function NewMultiple({ onCancel }: any) {
  const [students, setStudents] = React.useState([<DetailsField onChange={undefined} key={0} />]);

  const handleAddStudent = () => {
    setStudents([...students]);
  };

  const handleRemoveStudent = (index: number) => {
    const updatedStudents = [...students];
    updatedStudents.splice(index, 1);
    setStudents(updatedStudents);
  };

  const handleStudentNameChange = (index: number, value: any) => {
    const updatedStudents = [...students];
    updatedStudents[index] = value;
    setStudents(updatedStudents);
  };

  const handleSave = () => {
    console.log(students);
    onCancel();
  };

  return (
    <div style={{ padding: '1rem' }}>
      <Card sx={{ p: '1rem' }}>
        <Stack mb={2} direction="row" alignItems="center" spacing={2}>
          <BackButton onBackClick={onCancel} title="Information" />
        </Stack>
        {students.map((student, index) => (
          <Stack direction="column">
            <Paper sx={{ borderRadius: '20px', border: '1px solid #e8e8e9' }}>
              <Stack sx={{ flexDirection: { xs: 'column', md: 'row' } }} spacing={2} alignItems="center" m={1}>
                <DetailsField onChange={(e: any) => handleStudentNameChange(index, e.target.value)} />
                <Button variant="outlined" size="small" onClick={() => handleRemoveStudent(index)}>
                  <MdDelete />
                </Button>
              </Stack>
            </Paper>
          </Stack>
        ))}
        <Button variant="outlined" size="small" onClick={handleAddStudent} sx={{ ml: 2, mt: 2 }}>
          Add Student
        </Button>
        <Stack direction="row" justifyContent="end" mt={2}>
          <Button variant="contained" color="primary" onClick={handleSave}>
            Save
          </Button>
        </Stack>
      </Card>
    </div>
  );
}

export default NewMultiple;
