import LoginCarousel from '@/features/login/LoginCarousel';
import { breakPointsMinwidth } from '@/config/breakpoints';
import styled from 'styled-components';
import Page from '@/components/shared/Page';
import LoginForm from '@/features/login/LoginForm';
import InfoCard from '@/features/login/InfoCard';
import { useState } from 'react';
import ParentLogin from '@/Parent-Side/pages/ParentLogin';
import { Switch, FormControlLabel, Stack, Button, InputLabel, FormControl, Select, MenuItem } from '@mui/material';
import InfoCard2 from '@/features/login/InfoCard2';
import InfoCard3 from '@/features/login/InfoCard3';
import { title } from 'process';
import { Link, useNavigate } from 'react-router-dom';
import SchoolSelector from '@/components/shared/SchoolsList';
import { useSchool } from '@/contexts/SchoolContext';
import CopyList from '@/components/shared/RND/TextCopy';

const LoginRoot = styled.div`
  min-height: 100vh;
  padding: 0;
  padding-top: 1.25rem;
  display: flex;
  flex-direction: column;
  gap: 20px;
  justify-content: center;

  @media ${breakPointsMinwidth.lg} {
    padding: 2rem;
  }
`;
// ========================== Holy Angels Info Card =============================//
const InfoCardData = [
  {
    id: 1,
    title: 'Angels Paradise',
    content: 'For students seeking admissions from Nursery, Jr. kg & Sr. kg.',
  },
  {
    id: 2,
    title: 'Holy Angels School',
    content: 'For students seeking admissions from standard 1st to standard 10.',
  },
  {
    id: 3,
    title: 'Holy Angels College',
    content: 'Holy Angels’ Junior College to Class XI Science & Commerce.',
  },
];
// ========================== Carmel  Convent Info Card =============================//
// const InfoCardData = [
//   {
//     id: 1,
//     title: 'Our Vision',
//     content:
//       'To mould every prospective potential into dynamic , individualistic and the most pursued citizens of the country to be independent , lifelong who strive and encourage for excellence and becomes responsible student of our global and natural environment.',
//   },
//   {
//     id: 2,
//     title: 'Our Mission',
//     content:
//       'To create an educational world in which children of all types and from all corners of the society have an opportunity for Qualitative education.',
//   },
//   {
//     id: 3,
//     title: 'MOTTO Love & Service',
//     content:
//       'The circle signifies the universe. The star stands for the star of the sea,OUR LADY of MOUNT CARMEL to whom the school is dedicated .The lamp tells us that the students should be lamps radiating light to millions who are still in darkness. Our Alma Mater wants her students to be torch bearers of LOVE AND SERVICE.',
//   },
// ];
// ========================== St Therese Info Card =============================//
// const InfoCardData = [
//   {
//     id: 1,
//     title: 'Our Vision',
//     content:
//       'We envision a holistic transformation of young minds and for this, a kind of enrichment that would ultimately engulf our society and nation as a whole.',
//   },
//   {
//     id: 2,
//     title: 'Our Mission',
//     content:
//       'We dedicate ourselves to reinvent ourselves as teachers and educators for the august mission of enriching the young for academic excellence, development of skills and character formation, based on Love of God and service to humanity as our motto.',
//   },
//   {
//     id: 3,
//     title: 'Motto',
//     content:
//       'The motto of our school is LOVE, TRUTH AND SERVICE. And the emblem, in brief, represents the ideal bounding among the school community, all of whom are pledged to live up to it.',
//   },
// ];
// ========================== St Thomas Info Card =============================//
// const InfoCardData = [
//   {
//     id: 1,
//     title: '',
//     content:
//       'It is with great pleasure that I welcome you to our school website. I am happy to inform that our school has completed beyond 25 glorious years in Dombivli. The Management and the staff are grateful to God and thank the Almighty for His manifold blessings in helping us to achieve great heights.',
//   },
//   {
//     id: 2,
//     title: '',
//     content:
//       'As Principal I am hugely impressed by the commitment of the management and the staff to the provision of an excellent all-round education for our students in our state of the art facilities. As a team working together, we strongly promote academic achievement among our students. The cultural, sporting and other successes of all of our students and staff are also proudly celebrated together.',
//   },
//   {
//     id: 3,
//     title: '',
//     content:
//       'St. Thomas School, is an innovative school drawing on the talents and skills of staff, students and parents to provide a host of educational programmes and projects. Wholesome participation is encouraged in the extensive range of extra-curricular activities and care is also taken to ensure the well-being and happiness of each and every student in the school.',
//   },
// ];
// ========================== Nirmala Info Card =============================//
// const InfoCardData = [
//   {
//     id: 1,
//     title: 'Our Vision',
//     content:
//       'To mould every prospective potential into dynamic, individualistic and the most pursued citizens of the country to be independent, lifelong who strive and encourage for responsible student of our global and natural environment.',
//   },
//   {
//     id: 2,
//     title: 'Mission & Mission Statement',
//     content:
//       'To create an educational world in which children of all types and from all corners of the society have an opportunity for qualitative education. “ The steadfast love of the Lord never ceases, His mercies never come to an end, they are new every morning, great is your faithfulness.” -Holy Bible.',
//   },
//   {
//     id: 3,
//     title: 'School Motto',
//     content:
//       ' To create an educational world in which children of all types and from all corners of the society have an opportunity for qualitative education. “ The steadfast love of the Lord never ceases, His mercies never come to an end, they are new every morning, great is your faithfulness.” -Holy Bible.',
//   },
// ];
// =========================================
function Login() {
  const [parentLogin, setParentLogin] = useState(false);
  const [selectedOption, setSelectedOption] = useState('');
  const navigate = useNavigate();
  const { selectedSchool } = useSchool();

  const handleChange = (event: any) => {
    const { value } = event.target;
    setSelectedOption(value);
    navigate(value); // Navigate to the selected page
  };
  return (
    <Page title="Login">
      <Stack direction="row" justifyContent="space-between" p={{ xs: 1, sm: 2 }}>
        <FormControlLabel
          onClick={() => setParentLogin((s) => !s)}
          control={<Switch color="success" />}
          label="Parent Login"
        />
        {/* <CopyList /> */}
        {!parentLogin ? (
          <Stack direction="row" gap={2} justifyContent="space-between" alignItems="center">
            <SchoolSelector />
            <FormControl fullWidth>
              <Select sx={{ width: 150 }} size="small" value={selectedOption} onChange={handleChange} displayEmpty>
                <MenuItem value="" disabled sx={{ display: 'none' }}>
                  Select Page
                </MenuItem>
                <MenuItem value="/join-meeting">Zoom Meeting</MenuItem>
                <MenuItem value="/authenticateLogin">Test Login</MenuItem>
                <MenuItem value="/parent/downloadreceipt">Download</MenuItem>
                <MenuItem value="/admission-form">Admission Form</MenuItem>
                <MenuItem value="/admission-form-new">Admission Form New</MenuItem>
              </Select>
            </FormControl>
          </Stack>
        ) : (
          <Stack width={150}>
            <FormControl fullWidth>
              <Select size="small" value={selectedOption} onChange={handleChange} displayEmpty>
                <MenuItem value="" disabled sx={{ display: 'none' }}>
                  Select Page
                </MenuItem>
                <MenuItem value="/parent/pay-fee"> Online Pay</MenuItem>
                <MenuItem value="/parent/pay-fee-new"> Online Pay</MenuItem>
                <MenuItem value="/parent/reciept">Online Receipt</MenuItem>
                <MenuItem value="/parent/pay-response">Response Page</MenuItem>
              </Select>
            </FormControl>
          </Stack>
        )}
      </Stack>
      {!parentLogin ? (
        <LoginRoot className="container-fluid">
          <div className="row container-lg mx-auto">
            <div className="col-lg-7 d-none d-lg-flex flex-column justify-content-center">
              <LoginCarousel />
            </div>
            <div className="col-lg-5">
              <LoginForm />
            </div>
          </div>
          <div className="row container-lg mx-auto">
            {selectedSchool?.infoCardData &&
              selectedSchool?.infoCardData.map((item) => (
                <div key={item.id} className="col-md-4 mb-2">
                  <InfoCard title={item.title} content={item.content} url="/" />
                </div>
              ))}
          </div>
        </LoginRoot>
      ) : (
        <ParentLogin />
      )}
    </Page>
  );
}

export default Login;
