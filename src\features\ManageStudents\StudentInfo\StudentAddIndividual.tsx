/* eslint-disable no-nested-ternary */
/* eslint-disable prettier/prettier */
import React, { useEffect, useState } from 'react';
import Page from '@/components/shared/Page';
import styled from 'styled-components';
import {
  Card,
  Box,
  Typography,
  Grid,
  Button,
  Avatar,
  Badge,
  Stack,
  useTheme,
  TextField,
  MenuItem,
  SelectChangeEvent,
  IconButton,
  alpha,
  Alert,
  Tooltip,
  FormControl,
  Select,
  Snackbar,
} from '@mui/material';
import BackButton from '@/components/shared/BackButton';
import LoadingButton from '@mui/lab/LoadingButton';
import typography from '@/theme/typography';
import AddIcon from '@mui/icons-material/Add';
import CloseIcon from '@mui/icons-material/Close';
import useSettings from '@/hooks/useSettings';
import DatePickers from '@/components/shared/Selections/DatePicker';
import { getClassData, getStudentSubmitting, getYearData } from '@/config/storeSelectors';
import { useAppSelector } from '@/hooks/useAppSelector';
import { fetchClassList, fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import useAuth from '@/hooks/useAuth';
import { ClassListInfo } from '@/types/AcademicManagement';
import SaveIcon from '@mui/icons-material/Save';
import Slide, { SlideProps } from '@mui/material/Slide';
import { keyframes } from '@emotion/react';
import { useFormik } from 'formik';
import { StudentListInfo } from '@/types/StudentManagement';
import * as Yup from 'yup';
import dayjs from 'dayjs';
import { GENDER_SELECT } from '@/config/Selection';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import ProfileImage from '@/components/shared/ProfileImage';

const StudentAddIndividualRoot = styled.div`
  /* margin: 1rem 1rem 1rem 1rem; */
  padding: 1rem;
  .info-card {
    padding: 1rem;
    position: relative;
    height: 100%;
    box-shadow: none;
    border: 1px solid
      ${(props) => (props.theme.themeMode === 'light' ? props.theme.palette.grey[300] : props.theme.palette.grey[900])};
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.grey[900]};
  }
  .icon-wrapper {
    position: absolute;
    bottom: 0px;
    right: 0px;
  }
  .MuiSnackbar-root {
    left: 15px;
    right: 15px;
    bottom: 50px;
  }
  .date-feild {
    .MuiStack-root {
      width: 100%;
    }
  }
  @media screen and (max-width: 380px) {
    .student_info_header {
      flex-wrap: wrap;
    }
  }
`;

const SlideTransition = (props: SlideProps) => {
  return <Slide {...props} direction="up" timeout={{ enter: 500, exit: 300 }} />;
};

const rotateAnimation = keyframes`
  0% {
    transform: rotate(360deg);
  }
  100% {
    transform: rotate(0deg);
  }
`;

export type CreateEditStudentFormProps = {
  onSave: (values: StudentListInfo, mode: 'create' | 'edit') => void;
  onCancel: (mode: 'create' | 'edit') => void;
  onClose: (mode: 'create' | 'edit') => void;
  onBackClick: () => void;
  studentDetail: StudentListInfo;
  // isSubmitting: boolean;
};

const StudentAddIndividual = ({ studentDetail, onBackClick, onSave, onCancel }: CreateEditStudentFormProps) => {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const { user } = useAuth();
  const { confirm } = useConfirm();
  const dispatch = useAppDispatch();
  const adminId: number = user ? user.accountId : 0;
  const YearData = useAppSelector(getYearData);
  const [academicYearFilter, setAcademicYearFilter] = useState(studentDetail ? studentDetail.academicId : 0);
  const ClassData = useAppSelector(getClassData);
  const isSubmitting = useAppSelector(getStudentSubmitting);

  const CreateEditStudentValidationSchema = Yup.object({
    admissionNumber: Yup.string().required('Please enter admission number'),
    studentName: Yup.string()
      .required('Please enter the student name')
      .min(2, 'Name must be at least 2 characters long'),
    studentDOB: Yup.string().required('Please select the date of birth'),
    classId: Yup.string()
      .oneOf(
        ClassData.map((item) => item.classId.toString()),
        'Please select a valid class'
      )
      .required('Please select a class'),
    academicId: Yup.string()
      .oneOf(
        YearData.map((item) => item.accademicId.toString()),
        'Please select a valid academic year'
      )
      .required('Please select an academic year'),
    studentGender: Yup.string().oneOf(['0', '1'], 'Please select a gender').required(),
    studentBloodGroup: Yup.string().oneOf(
      ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'],
      'Please select a valid blood group'
    ),
    studentFatherName: Yup.string().required('Please enter father name'),
    studentFatherNumber: Yup.string()
      .required('Please enter father phone number')
      .matches(/^\d{10}$/, 'Phone number must be exactly 10 digits'),
  });

  const AllClassOption = {
    classId: 0,
    className: 'Select Class',
    classDescription: 'string',
    classStatus: 1,
  };

  const classDataWithAllClass = [AllClassOption, ...ClassData];
  const [classFilter, setClassFilter] = useState(
    studentDetail
      ? classDataWithAllClass.find((item) => item.classId === studentDetail.classId)
      : classDataWithAllClass[0]
  );

  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [snackbarOpen, setSnackbarOpen] = useState<'1' | '2' | '3' | '4' | '5' | false>(false);
  const [loading, setLoading] = useState<'1' | '2' | '3' | '4' | '5' | false>(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const [isSolid, setIsSolid] = useState(false);
  const [genderFilter, setGenderFilter] = useState(-1);

  const mode = studentDetail.studentId === 0 ? 'create' : 'edit';
  const {
    values: {
      academicId,
      classId,
      studentName,
      admissionNumber,
      admissionDate,
      studentGender,
      studentDOB,
      studentLastStudied,
      studentBloodGroup,
      studentBirthPlace,
      studentNationality,
      studentMotherTongue,
      studentReligion,
      studentCaste,
      studentPAddress,
      studentCAddress,
      studentFatherName,
      studentFatherQualification,
      studentFatherOccupation,
      studentFatherNumber,
      studentEmailId,
      studentMotherName,
      studentMotherQualification,
      studentMotherOccupation,
      studentMotherNumber,
      studentImage,
    },
    handleChange,
    handleSubmit,
    setFieldValue,
    touched,
    errors,
    resetForm,
  } = useFormik<StudentListInfo>({
    initialValues: {
      studentId: studentDetail.studentId,
      academicId: studentDetail.academicId,
      classId: studentDetail.classId,
      studentName: studentDetail.studentName,
      admissionNumber: studentDetail.admissionNumber,
      admissionDate: studentDetail.admissionDate,
      studentGender: studentDetail.studentGender,
      studentDOB: studentDetail.studentDOB,
      studentLastStudied: studentDetail.studentLastStudied,
      studentBloodGroup: studentDetail.studentBloodGroup,
      studentBirthPlace: studentDetail.studentBirthPlace,
      studentNationality: studentDetail.studentNationality,
      studentMotherTongue: studentDetail.studentMotherTongue,
      studentReligion: studentDetail.studentReligion,
      studentCaste: studentDetail.studentCaste,
      studentPAddress: studentDetail.studentPAddress,
      studentCAddress: studentDetail.studentCAddress,
      studentFatherName: studentDetail.studentFatherName,
      studentFatherQualification: studentDetail.studentFatherQualification,
      studentFatherOccupation: studentDetail.studentFatherOccupation,
      studentFatherNumber: studentDetail.studentFatherNumber,
      studentEmailId: studentDetail.studentEmailId,
      studentMotherName: studentDetail.studentMotherName,
      studentMotherQualification: studentDetail.studentMotherQualification,
      studentMotherOccupation: studentDetail.studentMotherQualification,
      studentMotherNumber: studentDetail.studentMotherNumber,
      studentImage: studentDetail.studentImage,
      createdBy: adminId,
    },
    validationSchema: CreateEditStudentValidationSchema,
    onSubmit: (values) => {
      onSave(values, mode);
    },
  });

  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files.length > 0) {
      const file = event.target.files[0];
      const imageUrl = URL.createObjectURL(file);
      // const fileName = file.name;
      setSelectedImage(imageUrl);
      setFieldValue('studentImage', imageUrl);
    }
  };

  const handleAvatarClick = () => {
    document.getElementById('imageInput')?.click();
  };

  const handleRemoveImage = () => {
    setSelectedImage('');
    setFieldValue('studentImage', '');
    setIsAnimating(false); // Stop the animation when image is removed
    setIsSolid(false);
  };

  const handleClassChange = (e: SelectChangeEvent) => {
    const selectedClassId = e.target.value;
    setFieldValue('classId', selectedClassId); // Update Formik state
  };

  const handleYearChange = (e: SelectChangeEvent) => {
    const selectedYearId = parseInt(e.target.value, 10);
    setAcademicYearFilter(selectedYearId);
    setFieldValue('academicId', selectedYearId);
  };
  const handleGenderChange = (e: SelectChangeEvent) => {
    const selectedGenderId = parseInt(e.target.value, 10);
    // parseInt(GENDER_SELECT.filter((item) => item.id === e.target.value)[0].id, 10)
    setGenderFilter(selectedGenderId);

    setFieldValue('studentGender', selectedGenderId);
  };

  React.useEffect(() => {
    dispatch(fetchYearList(adminId));
    dispatch(fetchClassList(adminId));
  }, [dispatch, adminId]);

  // const handleSave = (formId: false | '1' | '2' | '3' | '4' | '5') => {
  //   // Logic to save data
  //   setLoading(formId);

  //   setTimeout(() => {
  //     setLoading(false);
  //     if (loading === false) setSnackbarOpen(formId); // Show snackbar on save
  //   }, 2000);
  // };

  // const handleCloseSnackbar = () => {
  //   setSnackbarOpen(false);
  // };

  // Automatically stop the animation after 3 seconds

  useEffect(() => {
    if (isAnimating) {
      const timer = setTimeout(() => {
        setIsAnimating(false); // Stop the animation after 3 seconds
        setIsSolid(true);
      }, 500);

      return () => clearTimeout(timer); // Cleanup the timeout
    }
    return undefined;
  }, [isAnimating]);

  // const varLow = alpha(theme.palette.grey[900], 0.48);
  // const varHigh = alpha(theme.palette.grey[900], 1);
  const handleReset = async () => {
    if (await confirm('Are you sure you want to reset form?', 'Reset?')) {
      resetForm();
    }
  };
  const buttonText =
    mode === 'create' ? (isSubmitting ? 'Saving...' : 'Save') : isSubmitting ? 'Updating...' : 'Update';
  return (
    <Page title="Student Add Individual">
      <StudentAddIndividualRoot>
        <form noValidate onSubmit={handleSubmit} onReset={handleReset}>
          <Card sx={{ p: { xs: '1rem', sm: '1.5rem' } }}>
            <Box
              className="student_info_header"
              display="flex"
              justifyContent="space-between"
              alignItems="center"
              gap={2}
            >
              <BackButton disabled={isSubmitting} onBackClick={onBackClick} title="Information" />
              <Stack spacing={2} direction="row" justifyContent="end" width="100%" sx={{}}>
                <Button disabled={isSubmitting} type="reset" size="small" variant="contained" color="secondary">
                  Reset
                </Button>
                <LoadingButton
                  loadingPosition="start"
                  loading={isSubmitting}
                  disabled={isSubmitting}
                  startIcon={<SaveIcon />}
                  variant="contained"
                  color="primary"
                  type="submit"
                  size="small"
                >
                  {buttonText}
                </LoadingButton>
              </Stack>
            </Box>
            <Typography mt={2} mb={1} variant="subtitle1" fontSize={14} fontWeight={typography.fontWeightMedium}>
              Student Information
            </Typography>
            <Grid container columnSpacing={4} rowSpacing={4}>
              <Grid item xl={4} md={6} xs={12}>
                <Card
                  className="info-card"
                  sx={{
                    background: snackbarOpen === false ? theme.palette.common.white : '#0000000',
                  }}
                >
                  <Stack gap={2}>
                    <Box display="flex" alignItems="center" gap={3} justifyContent="space-between">
                      <ProfileImage
                        width="65px"
                        height="65px"
                        selectedImage={studentImage}
                        setFieldValue={setFieldValue}
                      />
                      {/* <Avatar
                          sx={{
                            backgroundColor: isLight ? theme.palette.common.white : theme.palette.grey[900],
                            color: theme.palette.primary.main,
                            cursor: 'pointer',
                            width: '55px',
                            height: '55px',
                            position: 'relative',
                            // Add a pseudo-element for the border animation
                            '&::before': {
                              content: '""',
                              position: 'absolute',
                              top: '0px',
                              left: '0px',
                              right: '0px',
                              bottom: '0px',
                              border: `2px ${theme.palette.primary.main}`,
                              borderStyle: studentImage ? 'solid' : 'dashed',
                              // borderStyle: isAnimating ? 'solid' : 'dashed',
                              borderRadius: '50%',
                              animation: isAnimating ? `${rotateAnimation} .5s linear` : 'none',
                            },
                          }}
                          alt="Student Avatar"
                          src={studentImage}
                          onClick={handleAvatarClick} // Trigger file input on click
                        />
                        {studentImage ? (
                          <Tooltip title="Remove Image">
                            <CloseIcon
                              className="icon-wrapper"
                              sx={{
                                fontSize: '15px',
                                cursor: 'pointer',
                                bgcolor: theme.palette.error.main,
                                color: theme.palette.common.white,
                                borderRadius: '50px',
                              }}
                              onClick={handleRemoveImage} // Handle image removal
                            />
                          </Tooltip>
                        ) : (
                          <AddIcon
                            className="icon-wrapper"
                            sx={{
                              fontSize: '15px',
                              cursor: 'pointer',
                              bgcolor: theme.palette.primary.main,
                              color: theme.palette.common.white,
                              borderRadius: '50px',
                            }}
                            onClick={handleAvatarClick} // Trigger file input on click
                          />
                        )}
                        <input
                          disabled={isSubmitting}
                          id="imageInput"
                          type="file"
                          accept="image/*"
                          style={{ display: 'none' }}
                          onChange={handleImageChange} // Handle image change
                        /> */}

                      <FormControl fullWidth>
                        <Typography variant="subtitle2" fontSize={12}>
                          Admission No
                        </Typography>
                        <TextField
                          fullWidth
                          placeholder="Enter number"
                          name="admissionNumber"
                          value={admissionNumber}
                          onChange={handleChange}
                          error={touched.admissionNumber && !!errors.admissionNumber}
                          helperText={errors.admissionNumber}
                          disabled={isSubmitting}
                        />
                      </FormControl>
                    </Box>
                    <FormControl fullWidth>
                      <Typography variant="subtitle2" fontSize={12}>
                        Student Name
                      </Typography>
                      <TextField
                        name="studentName"
                        value={studentName}
                        onChange={handleChange}
                        error={touched.studentName && !!errors.studentName}
                        helperText={errors.studentName}
                        disabled={isSubmitting}
                        fullWidth
                        placeholder="Enter name"
                      />
                    </FormControl>

                    <FormControl fullWidth className="date-feild">
                      <Typography variant="subtitle2" fontSize={12}>
                        Date of Birth
                      </Typography>
                      <DatePickers
                        // width="340px"
                        disabled={isSubmitting}
                        name="studentDOB"
                        value={dayjs(studentDOB)}
                        onChange={(e) => {
                          const formattedDate = mode === 'create' && e ? e.format('DD/MM/YYYY') : dayjs(e);
                          setFieldValue('studentDOB', formattedDate);
                          console.log('studentDOB::::', formattedDate);
                        }}
                      />
                      {touched.studentDOB && !!errors.studentDOB && (
                        <Typography color="red" fontSize="12px" ml={2.5} mt={1} variant="subtitle1">
                          {errors.studentDOB}
                        </Typography>
                      )}
                    </FormControl>
                    <FormControl fullWidth className="date-feild">
                      <Typography variant="subtitle2" fontSize={12}>
                        Admission Date
                      </Typography>
                      <DatePickers
                        // width="340px "
                        disabled={isSubmitting}
                        name="admissionDate"
                        value={dayjs(admissionDate)}
                        onChange={(e) => {
                          const formattedDate = mode === 'create' && e ? e.format('DD/MM/YYYY') : dayjs(e);
                          setFieldValue('admissionDate', formattedDate);
                          console.log('admissionDate::::', formattedDate);
                        }}
                      />
                    </FormControl>
                    <FormControl fullWidth>
                      <Typography variant="subtitle2" fontSize={12}>
                        Academic Year
                      </Typography>
                      <Select
                        labelId="academicYearFilter"
                        id="academicYearFilterSelect"
                        value={academicId.toString()}
                        onChange={handleYearChange}
                        placeholder="Select Year"
                        disabled={isSubmitting}
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Year
                        </MenuItem>
                        {YearData.map((opt) => (
                          <MenuItem key={opt.accademicId} value={opt.accademicId}>
                            {opt.accademicTime}
                          </MenuItem>
                        ))}
                      </Select>
                      {touched.academicId && !!errors.academicId && (
                        <Typography color="red" fontSize="12px" ml={2.5} mt={1} variant="subtitle1">
                          {errors.academicId}
                        </Typography>
                      )}
                    </FormControl>
                    <FormControl fullWidth>
                      <Typography variant="subtitle2" fontSize={12}>
                        Class
                      </Typography>
                      <Select
                        labelId="classFilter"
                        id="classFilterSelect"
                        value={classId.toString()}
                        onChange={handleClassChange}
                        placeholder="Select Class"
                        disabled={isSubmitting}
                      >
                        {classDataWithAllClass?.map((item: ClassListInfo) => (
                          <MenuItem sx={{ fontSize: '13px' }} key={item.classId} value={item.classId}>
                            {item.className}
                          </MenuItem>
                        ))}
                      </Select>
                      {touched.classId && !!errors.classId && (
                        <Typography color="red" fontSize="12px" ml={2.5} mt={1} variant="subtitle1">
                          {errors.classId}
                        </Typography>
                      )}
                    </FormControl>

                    <Stack direction="row" justifyContent="end" alignItems="end">
                      {/* <LoadingButton
                        startIcon={<SaveIcon />}
                        loadingPosition="start"
                        loading={loading === '1'}
                        onClick={() => handleSave('1')}
                        size="small"
                        variant="contained"
                        color="primary"
                        sx={{ width: loading === '1' ? 110 : 75 }}
                      >
                        {loading === '1' ? 'Saving...' : 'Save'}
                      </LoadingButton> */}
                    </Stack>
                  </Stack>
                  {/* <Snackbar
                    sx={{ position: 'absolute' }}
                    open={snackbarOpen === '1'}
                    autoHideDuration={2000}
                    onClose={handleCloseSnackbar}
                    TransitionComponent={SlideTransition}
                  >
                    <Alert severity="success" sx={{ px: 1, py: 0, width: '100%', color: theme.palette.success.main }}>
                      Details Save successfully!
                    </Alert>
                  </Snackbar> */}
                </Card>
              </Grid>

              <Grid item xl={4} md={6} xs={12}>
                <Card className="info-card">
                  <Stack gap={2} mt={0} sx={{ width: '100%' }}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle2" fontSize={12}>
                        Gender
                      </Typography>
                      <Select
                        labelId="studentGender"
                        id="studentGender"
                        placeholder="Select Gender"
                        name="studentGender"
                        value={studentGender.toString()}
                        onChange={handleGenderChange}
                        error={touched.studentGender && !!errors.studentGender}
                        disabled={isSubmitting}
                      >
                        <MenuItem value={-2} sx={{ display: 'none' }}>
                          Select Gender
                        </MenuItem>
                        {GENDER_SELECT.map((opt) => (
                          <MenuItem key={opt.id} value={opt.id} sx={{ display: opt.id === '-1' ? 'none' : 'block' }}>
                            {opt.gender}
                          </MenuItem>
                        ))}
                      </Select>
                      {touched.studentGender && !!errors.studentGender && (
                        <Typography color="red" fontSize="12px" ml={2.5} mt={1} variant="subtitle1">
                          {errors.studentGender}
                        </Typography>
                      )}
                    </FormControl>
                    <FormControl fullWidth>
                      <Typography variant="subtitle2" fontSize={12}>
                        Blood Group
                      </Typography>
                      <TextField
                        name="studentBloodGroup"
                        value={studentBloodGroup}
                        onChange={handleChange}
                        error={touched.studentBloodGroup && !!errors.studentBloodGroup}
                        helperText={errors.studentBloodGroup}
                        disabled={isSubmitting}
                        fullWidth
                        placeholder="Enter blood group"
                      />
                    </FormControl>
                    <FormControl fullWidth>
                      <Typography variant="subtitle2" fontSize={12}>
                        Birth Place
                      </Typography>
                      <TextField
                        name="studentBirthPlace"
                        value={studentBirthPlace}
                        onChange={handleChange}
                        error={touched.studentBirthPlace && !!errors.studentBirthPlace}
                        helperText={errors.studentBirthPlace}
                        disabled={isSubmitting}
                        fullWidth
                        placeholder="Enter place"
                      />
                    </FormControl>
                    <FormControl fullWidth>
                      <Typography variant="subtitle2" fontSize={12}>
                        Nationality
                      </Typography>
                      <TextField
                        name="studentNationality"
                        value={studentNationality}
                        onChange={handleChange}
                        error={touched.studentNationality && !!errors.studentNationality}
                        helperText={errors.studentNationality}
                        disabled={isSubmitting}
                        fullWidth
                        placeholder="Enter nationality"
                      />
                    </FormControl>
                    <FormControl fullWidth>
                      <Typography variant="subtitle2" fontSize={12}>
                        Mother Tongue
                      </Typography>
                      <TextField
                        name="studentMotherTongue"
                        value={studentMotherTongue}
                        onChange={handleChange}
                        error={touched.studentMotherTongue && !!errors.studentMotherTongue}
                        helperText={errors.studentMotherTongue}
                        disabled={isSubmitting}
                        fullWidth
                        placeholder="Enter mother tongue"
                      />
                    </FormControl>
                    <FormControl fullWidth>
                      <Typography variant="subtitle2" fontSize={12}>
                        Religion
                      </Typography>
                      <TextField
                        name="studentReligion"
                        value={studentReligion}
                        onChange={handleChange}
                        error={touched.studentReligion && !!errors.studentReligion}
                        helperText={errors.studentReligion}
                        disabled={isSubmitting}
                        fullWidth
                        placeholder="Enter religion"
                      />
                    </FormControl>
                    <Stack direction="row" justifyContent="end" alignItems="end">
                      {/* <LoadingButton
                        loadingPosition="start"
                        loading={loading === '2'}
                        onClick={() => handleSave('2')}
                        startIcon={<SaveIcon />}
                        size="small"
                        variant="contained"
                        color="primary"
                        sx={{ width: loading === '2' ? 110 : 75 }}
                      >
                        {loading === '2' ? 'Saving...' : 'Save'}
                      </LoadingButton> */}
                    </Stack>
                  </Stack>
                  {/* <Snackbar
                    sx={{ position: 'absolute' }}
                    open={snackbarOpen === '2'}
                    autoHideDuration={2000}
                    onClose={handleCloseSnackbar}
                    TransitionComponent={SlideTransition}
                  >
                    <Alert severity="success" sx={{ px: 1, py: 0, width: '100%', color: theme.palette.success.main }}>
                      Details Save successfully!
                    </Alert>
                  </Snackbar> */}
                </Card>
              </Grid>

              <Grid item xl={4} md={6} xs={12}>
                <Card className="info-card">
                  <Stack gap={2} sx={{ width: '100%' }}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle2" fontSize={12}>
                        Caste
                      </Typography>
                      <TextField
                        name="studentCaste"
                        value={studentCaste}
                        onChange={handleChange}
                        error={touched.studentCaste && !!errors.studentCaste}
                        helperText={errors.studentCaste}
                        disabled={isSubmitting}
                        fullWidth
                        placeholder="Enter caste"
                      />
                    </FormControl>

                    <FormControl fullWidth>
                      <Typography variant="subtitle2" fontSize={12}>
                        Last Studied
                      </Typography>
                      <TextField
                        name="studentLastStudied"
                        value={studentLastStudied}
                        onChange={handleChange}
                        error={touched.studentLastStudied && !!errors.studentLastStudied}
                        helperText={errors.studentLastStudied}
                        disabled={isSubmitting}
                        fullWidth
                        placeholder="Enter last studied"
                      />
                    </FormControl>
                    <FormControl fullWidth>
                      <Typography variant="subtitle2" fontSize={12}>
                        Permanent Address
                      </Typography>
                      <TextField
                        multiline
                        name="studentPAddress"
                        value={studentPAddress}
                        onChange={handleChange}
                        error={touched.studentPAddress && !!errors.studentPAddress}
                        helperText={errors.studentPAddress}
                        disabled={isSubmitting}
                        fullWidth
                        placeholder="Enter address"
                        InputProps={{
                          inputProps: {
                            style: { resize: 'vertical', width: '100%', minHeight: '100px', maxHeight: '100px' },
                          },
                        }}
                      />
                    </FormControl>
                    <FormControl fullWidth>
                      <Typography variant="subtitle2" fontSize={12}>
                        Current Address
                      </Typography>
                      <TextField
                        multiline
                        name="studentCAddress"
                        value={studentCAddress}
                        onChange={handleChange}
                        error={touched.studentCAddress && !!errors.studentCAddress}
                        helperText={errors.studentCAddress}
                        disabled={isSubmitting}
                        fullWidth
                        placeholder="Enter address"
                        InputProps={{
                          inputProps: {
                            style: { resize: 'vertical', width: '100%', minHeight: '100px', maxHeight: '100px' },
                          },
                        }}
                      />
                    </FormControl>
                    <Stack
                      direction="row"
                      justifyContent="end"
                      position={{ lg: 'absolute' }}
                      bottom="1rem"
                      right="1rem"
                    >
                      {/* <LoadingButton
                        loadingPosition="start"
                        loading={loading === '3'}
                        onClick={() => handleSave('3')}
                        startIcon={<SaveIcon />}
                        size="small"
                        variant="contained"
                        color="primary"
                        sx={{ width: loading === '3' ? 110 : 75 }}
                      >
                        {loading === '3' ? 'Saving...' : 'Save'}
                      </LoadingButton> */}
                    </Stack>
                  </Stack>
                  {/* <Snackbar
                    sx={{ position: 'absolute' }}
                    open={snackbarOpen === '3'}
                    autoHideDuration={2000}
                    onClose={handleCloseSnackbar}
                    TransitionComponent={SlideTransition}
                  >
                    <Alert severity="success" sx={{ px: 1, py: 0, width: '100%', color: theme.palette.success.main }}>
                      Details Save successfully!
                    </Alert>
                  </Snackbar> */}
                </Card>
              </Grid>

              <Grid item xl={12} xs={12}>
                <Typography variant="subtitle1" fontSize={14} fontWeight={typography.fontWeightMedium}>
                  Parent Information
                </Typography>
              </Grid>
              <Grid item xl={4} md={6} xs={12}>
                <Card className="info-card">
                  <Stack gap={2} sx={{ width: '100%' }}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle2" fontSize={12}>
                        Father Name
                      </Typography>
                      <TextField
                        name="studentFatherName"
                        value={studentFatherName}
                        onChange={handleChange}
                        error={touched.studentFatherName && !!errors.studentFatherName}
                        helperText={errors.studentFatherName}
                        disabled={isSubmitting}
                        placeholder="Enter guardian name"
                        fullWidth
                      />
                    </FormControl>
                    <FormControl fullWidth>
                      <Typography variant="subtitle2" fontSize={12}>
                        Father Number
                      </Typography>
                      <TextField
                        name="studentFatherNumber"
                        value={studentFatherNumber}
                        onChange={handleChange}
                        error={touched.studentFatherNumber && !!errors.studentFatherNumber}
                        helperText={errors.studentFatherNumber}
                        disabled={isSubmitting}
                        placeholder="Enter guardian number"
                        fullWidth
                      />
                    </FormControl>
                    <FormControl fullWidth>
                      <Typography variant="subtitle2" fontSize={12}>
                        Mother Number
                      </Typography>
                      <TextField
                        name="studentMotherNumber"
                        value={studentMotherNumber}
                        onChange={handleChange}
                        error={touched.studentMotherNumber && !!errors.studentMotherNumber}
                        helperText={errors.studentMotherNumber}
                        disabled={isSubmitting}
                        fullWidth
                        placeholder="Enter number"
                      />
                    </FormControl>
                    <FormControl fullWidth>
                      <Typography variant="subtitle2" fontSize={12}>
                        Father Qualification
                      </Typography>
                      <TextField
                        name="studentFatherQualification"
                        value={studentFatherQualification}
                        onChange={handleChange}
                        error={touched.studentFatherQualification && !!errors.studentFatherQualification}
                        helperText={errors.studentFatherQualification}
                        disabled={isSubmitting}
                        fullWidth
                        placeholder="Enter  father qualification"
                      />
                    </FormControl>
                    <FormControl fullWidth>
                      <Typography variant="subtitle2" fontSize={12}>
                        Father Occupation
                      </Typography>
                      <TextField
                        name="studentFatherOccupation"
                        value={studentFatherOccupation}
                        onChange={handleChange}
                        error={touched.studentFatherOccupation && !!errors.studentFatherOccupation}
                        helperText={errors.studentFatherOccupation}
                        disabled={isSubmitting}
                        fullWidth
                        placeholder="Enter father occupation"
                      />
                    </FormControl>
                    <Stack direction="row" justifyContent="end">
                      {/* <LoadingButton
                        loadingPosition="start"
                        loading={loading === '4'}
                        onClick={() => handleSave('4')}
                        startIcon={<SaveIcon />}
                        size="small"
                        variant="contained"
                        color="primary"
                        sx={{ width: loading === '4' ? 110 : 75 }}
                      >
                        {loading === '4' ? 'Saving...' : 'Save'}
                      </LoadingButton> */}
                    </Stack>
                  </Stack>
                  {/* <Snackbar
                    sx={{ position: 'absolute' }}
                    open={snackbarOpen === '4'}
                    autoHideDuration={2000}
                    onClose={handleCloseSnackbar}
                    TransitionComponent={SlideTransition}
                  >
                    <Alert severity="success" sx={{ px: 1, py: 0, width: '100%', color: theme.palette.success.main }}>
                      Details Save successfully!
                    </Alert>
                  </Snackbar> */}
                </Card>
              </Grid>

              <Grid item xl={4} md={6} xs={12}>
                <Card className="info-card">
                  <Stack gap={2} sx={{ width: '100%' }}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle2" fontSize={12}>
                        Mother Name
                      </Typography>
                      <TextField
                        name="studentMotherName"
                        value={studentMotherName}
                        onChange={handleChange}
                        error={touched.studentMotherName && !!errors.studentMotherName}
                        helperText={errors.studentMotherName}
                        disabled={isSubmitting}
                        fullWidth
                        placeholder="Enter mother name"
                      />
                    </FormControl>
                    <FormControl fullWidth>
                      <Typography variant="subtitle2" fontSize={12}>
                        Mother Qualification
                      </Typography>
                      <TextField
                        name="studentMotherQualification"
                        value={studentMotherQualification}
                        onChange={handleChange}
                        error={touched.studentMotherQualification && !!errors.studentMotherQualification}
                        helperText={errors.studentMotherQualification}
                        disabled={isSubmitting}
                        fullWidth
                        placeholder="Enter  mother qualification"
                      />
                    </FormControl>
                    <FormControl fullWidth>
                      <Typography variant="subtitle2" fontSize={12}>
                        Mother Occupation
                      </Typography>
                      <TextField
                        name="studentMotherOccupation"
                        value={studentMotherOccupation}
                        onChange={handleChange}
                        error={touched.studentMotherOccupation && !!errors.studentMotherOccupation}
                        helperText={errors.studentMotherOccupation}
                        disabled={isSubmitting}
                        fullWidth
                        placeholder="Enter mother occupation"
                      />
                    </FormControl>
                    <FormControl fullWidth>
                      <Typography variant="subtitle2" fontSize={12}>
                        Email Id
                      </Typography>
                      <TextField
                        type="email"
                        name="studentEmailId"
                        value={studentEmailId}
                        onChange={handleChange}
                        error={touched.studentEmailId && !!errors.studentEmailId}
                        helperText={errors.studentEmailId}
                        disabled={isSubmitting}
                        fullWidth
                        placeholder="Enter email id"
                      />
                    </FormControl>
                    <Stack
                      direction="row"
                      justifyContent="end"
                      position={{ md: 'absolute' }}
                      bottom="1rem"
                      right="1rem"
                    >
                      {/* <LoadingButton
                        loadingPosition="start"
                        loading={loading === '5'}
                        onClick={() => handleSave('5')}
                        startIcon={<SaveIcon />}
                        size="small"
                        variant="contained"
                        color="primary"
                        sx={{ width: loading === '5' ? 110 : 75 }}
                      >
                        {loading === '5' ? 'Saving...' : 'Save'}
                      </LoadingButton> */}
                    </Stack>
                  </Stack>
                  {/* <Snackbar
                    sx={{ position: 'absolute' }}
                    open={snackbarOpen === '5'}
                    autoHideDuration={2000}
                    onClose={handleCloseSnackbar}
                    TransitionComponent={SlideTransition}
                  >
                    <Alert severity="success" sx={{ px: 1, py: 0, width: '100%', color: theme.palette.success.main }}>
                      Details Save successfully!
                    </Alert>
                  </Snackbar> */}
                </Card>
              </Grid>
            </Grid>
          </Card>
        </form>
      </StudentAddIndividualRoot>
    </Page>
  );
};

export default StudentAddIndividual;
