/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo } from 'react';
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Box,
  Button,
  Divider,
  Grid,
  Paper,
  TextField,
  Typography,
  Card,
  FormControl,
  Stack,
} from '@mui/material';
import styled from 'styled-components';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { CLASS_SELECT, YEAR_SELECT } from '@/config/Selection';

const MapProductsRoot = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      height: calc(100% - 77px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

export const data = [
  {
    SlNo: 1,
    ProductTitle: 'SCHOOL DIARY',
    ProductDescription: 'School Diary 2023-2024',
    LastUpdated: '6/23/2023 3:16:11 PM',
    Status: 1,
  },
  {
    SlNo: 2,
    ProductTitle: 'NOTEBOOK BIG UNRULED',
    ProductDescription: 'Unruled Notebook',
    LastUpdated: '6/23/2023 3:16:39 PM',
    Status: 0,
  },
  {
    SlNo: 3,
    ProductTitle: 'NOTEBOOK SMALL RULED',
    ProductDescription: 'Small Ruled Notebook',
    LastUpdated: '6/23/2023 3:17:10 PM',
    Status: 1,
  },
  {
    SlNo: 4,
    ProductTitle: 'SCHOOL SHOE',
    ProductDescription: 'Black & White Shoes',
    LastUpdated: '6/23/2023 3:17:40 PM',
    Status: 1,
  },
  {
    SlNo: 5,
    ProductTitle: 'TIE',
    ProductDescription: 'Uniform Tie',
    LastUpdated: '6/23/2023 3:17:54 PM',
    Status: 0,
  },
  {
    SlNo: 6,
    ProductTitle: 'SCHOOL UNIFORM',
    ProductDescription: 'School Uniform',
    LastUpdated: '6/23/2023 3:18:13 PM',
    Status: 1,
  },
  {
    SlNo: 7,
    ProductTitle: 'BELT',
    ProductDescription: 'Belt',
    LastUpdated: '6/23/2023 3:18:31 PM',
    Status: 0,
  },
  {
    SlNo: 8,
    ProductTitle: 'PENCIL CASE',
    ProductDescription: 'Hardtop Pencil Case',
    LastUpdated: '6/23/2023 3:19:00 PM',
    Status: 1,
  },
  {
    SlNo: 9,
    ProductTitle: 'SHARPENER',
    ProductDescription: 'Double Hole Pencil Sharpener',
    LastUpdated: '6/23/2023 3:19:20 PM',
    Status: 1,
  },
  {
    SlNo: 10,
    ProductTitle: 'ERASER',
    ProductDescription: 'White Eraser',
    LastUpdated: '6/23/2023 3:19:40 PM',
    Status: 1,
  },
  {
    SlNo: 11,
    ProductTitle: 'RULER',
    ProductDescription: '12 Inch Plastic Ruler',
    LastUpdated: '6/23/2023 3:20:00 PM',
    Status: 0,
  },
  {
    SlNo: 12,
    ProductTitle: 'SCISSORS',
    ProductDescription: '5 Inch Stainless Steel Scissors',
    LastUpdated: '6/23/2023 3:20:20 PM',
    Status: 1,
  },
  {
    SlNo: 13,
    ProductTitle: 'WATER BOTTLE',
    ProductDescription: '1 Liter Plastic Water Bottle',
    LastUpdated: '6/23/2023 3:20:40 PM',
    Status: 1,
  },
];

function MapProducts() {
  const [drawerOpen, setDrawerOpen] = React.useState<boolean>(false);

  const toggleDrawerClose = useCallback(() => setDrawerOpen(false), []);

  const getRowKey = useCallback((row: any) => row.examId, []);
  const mapProductsColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'slNo',
        dataKey: 'SlNo',
        headerLabel: 'Sl.No',
        sortable: true,
      },
      {
        name: 'title',
        dataKey: 'ProductTitle',
        headerLabel: 'Product Title',
      },
      {
        name: 'description',
        dataKey: 'ProductDescription',
        headerLabel: 'Product Description',
      },
      {
        name: 'Amount',
        dataKey: 'TextField',
        headerLabel: 'Amount(INR)/unit',
      },
      {
        name: 'Unit',
        dataKey: 'TextField',
        headerLabel: 'Quantity',
      },
    ],
    []
  );

  return (
    <Page title="List">
      <MapProductsRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: 2 }}>
          <Typography variant="h6" fontSize={19}>
            Map Store Products
          </Typography>
          <Divider />
          <div className="card-main-body">
            <form noValidate>
              <Grid pb={2} pt={2} container spacing={5} alignItems="end">
                <Grid item md={6} xs={12}>
                  <FormControl sx={{ minWidth: { xs: '100%' } }}>
                    <Typography variant="h6" fontSize={17}>
                      Academic Year
                    </Typography>
                    <Autocomplete
                      options={YEAR_SELECT}
                      renderInput={(params) => <TextField {...params} placeholder="Select Year" />}
                    />
                  </FormControl>
                </Grid>
                <Grid item md={6} xs={12}>
                  <FormControl sx={{ minWidth: { xs: '100%' } }}>
                    <Typography variant="h6" fontSize={17}>
                      Class Section
                    </Typography>
                    <Autocomplete
                      options={CLASS_SELECT}
                      renderInput={(params) => <TextField {...params} placeholder="Select Class" />}
                    />
                  </FormControl>
                </Grid>
              </Grid>
            </form>

            <Paper className="card-table-container" sx={{ marginTop: 2 }}>
              <DataTable
                ShowCheckBox
                RowSelected
                columns={mapProductsColumns}
                data={data}
                getRowKey={getRowKey}
                fetchStatus="success"
              />
            </Paper>
          </div>
          <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 2 }}>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="secondary">
                Cancel
              </Button>
              <Button variant="contained" color="primary">
                Map Products
              </Button>
            </Stack>
          </Box>
        </Card>
      </MapProductsRoot>

      <TemporaryDrawer onClose={toggleDrawerClose} Title="Upload Details" state={drawerOpen} DrawerContent="" />
    </Page>
  );
}

export default MapProducts;
