import * as React from 'react';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';
import Page from '@/components/shared/Page';
import styled from 'styled-components';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import CreateNew from '@/features/TimeTable/CreateNew';
import ListTimeTable from '@/features/TimeTable/ListTimeTable';
import StaffAttendance from '@/features/TimeTable/StaffAttendance';
import Substitution from '@/features/TimeTable/Substitution';
import TimetableClass from '@/features/TimeTable/TimetableClass/TimetableClass';
import TimetableStaff from '@/features/TimeTable/TimetableStaff/TimetableStaff';
import TimetableGenerator from '@/features/TimeTable/TimetableGenerator/TimetableGenerator';
import { Link, useLocation, useOutletContext } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { flushStore } from '@/store/flush.slice';
import { OutletContextType } from '@/types/Common';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TimeTableRoot = styled.div`
  min-height: calc(100vh - ${TOP_BAR_HEIGHT});

  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};

  .tab {
    background-color: ${(props) => (props.theme.themeMode === 'light' ? '#fff' : props.theme.palette.grey[800])};
    @media screen and (min-width: 1050px) {
      width: 100%;
    }
    /* @media screen and (max-width: 720px) {
      width: 100%;
    } */
  }
  .Mui-selected {
    color: ${(props) => props.theme.palette.primary.main};
  }
`;

function TabPanel({ children, value, index }: TabPanelProps) {
  return (
    <Box
      sx={{ paddingTop: '3rem' }}
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
    >
      {value === index && <Box>{children}</Box>}
    </Box>
  );
}

function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`,
  };
}

export default function TimeTable() {
  const { topBarHeight } = useOutletContext<OutletContextType>();
  const location = useLocation();
  const [value, setValue] = React.useState(0);

  const dispatch = useDispatch();

  React.useEffect(() => {
    dispatch(flushStore());
    console.log('Flush Store');
  }, [dispatch, value]);

  React.useEffect(() => {
    const currentPath = location.pathname;
    const routePaths = [
      '/time-table/new',
      '/time-table/list',
      '/time-table/class-wise',
      '/time-table/staff-wise',
      '/time-table/staff-attendance',
      '/time-table/staff-substitution',
      '/time-table/generator',
    ];
    const index = routePaths.indexOf(currentPath);
    if (index !== -1) {
      setValue(index);
    }
  }, [location]);

  return (
    <Page title="Time Table">
      <TimeTableRoot>
        <Tabs
          className="tab"
          variant="scrollable"
          scrollButtons
          allowScrollButtonsMobile
          value={value}
          aria-label="basic tabs"
          sx={{
            ml: '0px',
            borderBottom: 1,
            borderColor: 'divider',
            position: 'fixed',
             top: `${topBarHeight}px`,
            zIndex: 1,
            // width: '100%',
          }}
        >
          <Tab {...a11yProps(0)} label="Create New" component={Link} to="/time-table/new" />
          <Tab {...a11yProps(1)} label="Timetable List" component={Link} to="/time-table/list" />
          <Tab {...a11yProps(2)} label="Timetable Class" component={Link} to="/time-table/class-wise" />
          <Tab {...a11yProps(3)} label="Timetable Staff" component={Link} to="/time-table/staff-wise" />
          <Tab {...a11yProps(4)} label="Staff Attandance Marking" component={Link} to="/time-table/staff-attendance" />
          <Tab {...a11yProps(5)} label="Staff Substitution" component={Link} to="/time-table/staff-substitution" />
          <Tab {...a11yProps(6)} label="Timetable Generator" component={Link} to="/time-table/generator" />
          {/* <Tab label="Staff Substitution List" {...a11yProps(7)} /> */}
        </Tabs>
        <TabPanel value={value} index={0}>
          <CreateNew />
        </TabPanel>
        <TabPanel value={value} index={1}>
          <ListTimeTable />
        </TabPanel>
        <TabPanel value={value} index={2}>
          <TimetableClass />
        </TabPanel>
        <TabPanel value={value} index={3}>
          <TimetableStaff />
        </TabPanel>
        <TabPanel value={value} index={4}>
          <StaffAttendance />
        </TabPanel>
        <TabPanel value={value} index={5}>
          <Substitution />
        </TabPanel>
        <TabPanel value={value} index={6}>
          <TimetableGenerator />
        </TabPanel>
        {/* <TabPanel value={value} index={7}>
          <SubstitutionList />
        </TabPanel> */}
      </TimeTableRoot>
    </Page>
  );
}
