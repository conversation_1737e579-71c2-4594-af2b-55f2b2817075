/* eslint-disable no-nested-ternary */
/* eslint-disable react/no-unstable-nested-components */

import React, { ChangeEvent, useCallback, useState } from 'react';
import { Virtuoso } from 'react-virtuoso';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TableContainer,
  Checkbox,
  Typography,
  TextField,
  IconButton,
} from '@mui/material';
import ErrorIcon from '@/assets/ManageFee/ErrorIcon.json';
import { createTermFeeSetting, fetchFeeDateSettings } from '@/store/ManageFee/manageFee.thunks';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import {
  getcreateTermFeeSettingListData,
  getfeeDateSettingsData,
  getfeeDateSettingsStatus,
} from '@/config/storeSelectors';
import { useAppSelector } from '@/hooks/useAppSelector';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import { ErrorMessage } from '@/components/shared/Popup/ErrorMessage';

const generateRows = (count) => {
  return Array.from({ length: count }).map((_, index) => ({
    id: index + 1,
    feeTitle: `Fee ${index + 1}`,
    feeMonths: {
      January: Math.floor(Math.random() * 1000),
      February: Math.floor(Math.random() * 1000),
      March: Math.floor(Math.random() * 1000),
      April: Math.floor(Math.random() * 1000),
      May: Math.floor(Math.random() * 1000),
      June: Math.floor(Math.random() * 1000),
      July: Math.floor(Math.random() * 1000),
      August: Math.floor(Math.random() * 1000),
      September: Math.floor(Math.random() * 1000),
      October: Math.floor(Math.random() * 1000),
      November: Math.floor(Math.random() * 1000),
      December: Math.floor(Math.random() * 1000),
    },
  }));
};

const rows = generateRows(1000);

const DataTable = ({ handleSave, rows, termFee, selectedRows, toggleRowSelection, handleInputChange, inputValues }) => {
  // const dynamicColumns = rows[0] ? Object.keys(rows[0].feeMonths) : [];

  const dispatch = useAppDispatch();
  const createTermFeeSettingListData = useAppSelector(getcreateTermFeeSettingListData);
  const feeDateSettingsData = useAppSelector(getfeeDateSettingsData);
  const feeDateSettingsStatus = useAppSelector(getfeeDateSettingsStatus);
  const [clickedCells, setClickedCells] = useState<{ feeId?: number; termId?: number; value?: string }[]>([]);
  const [rowSums, setRowSums] = useState<Record<string, number>>({});
  const [feeAmounts, setFeeAmounts] = useState<{ [key: string]: number }>({});

  const handleAmountChange = useCallback(
    (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>, row: any, termId: number, id: number) => {
      const { feeId } = row;
      const cellKey: string = `${feeId}_${termId}`;

      const newValue = parseInt(e.target.value, 10);
      const newValueString = e.target.value;

      const termFeeMapped = Array.isArray(row.termFeeMapped) ? row.termFeeMapped : [];
      const amountObj = termFeeMapped.find((f) => f.termId === termId);
      const amount = amountObj ? amountObj.amount : 0;
      console.log('feeAmounts::::', feeAmounts);

      // Update the feeAmounts array based on the new value
      setFeeAmounts((prevAmounts) => {
        const updatedAmounts = { ...prevAmounts };
        if (newValue === 0 || Number.isNaN(newValue)) {
          delete updatedAmounts[cellKey];
        } else if (newValue !== 0 || !Number.isNaN(newValue) || id !== 1) {
          updatedAmounts[cellKey] = newValue;
        } else {
          updatedAmounts[cellKey] = amount;
        }

        return updatedAmounts;
      });
    },
    [setFeeAmounts, feeAmounts]
  );

  const handleCellClick = useCallback(
    (row: any, column: any, i: any) => {
      const { feeId, termFeeMapped, amount } = row;
      const { termId } = column;

      const key = `${feeId}_`;
      const enteredAmountsSum = rowSums[key] || 0;
      // console.log('enteredAmountsSum::::----', enteredAmountsSum);

      // // Calculate sumRowAmounts
      const sumRowAmounts = termFeeMapped?.reduce((total, item) => total + (item.amount || 0), 0) || 0;
      const tAmount = sumRowAmounts + enteredAmountsSum;

      if (tAmount < amount) {
        setClickedCells((prevClickedCells) => [...prevClickedCells, { feeId, termId }]);
      } else {
        // setCellValidationError(true);
        // seTtotalTermAmount(amount);
      }
      console.log('tAmount::::----', tAmount);
      console.log('rowSums5::::----', rowSums);
      console.log('sumRowAmounts::::----', sumRowAmounts);
    },
    [setClickedCells, rowSums] // Dependencies
  );

  return (
    <TableContainer sx={{ my: 10 }}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>
              <Checkbox
                checked={rows.length > 0 && selectedRows.length === rows.length}
                onChange={(e) => toggleRowSelection(null, e.target.checked)} // Select all rows
              />
            </TableCell>
            <TableCell>Fee Title</TableCell>
            <TableCell>Amount</TableCell>
            {termFee.terms?.map((month) => (
              <TableCell key={month} sx={{ textAlign: 'center' }}>
                {month.termTitle}
              </TableCell>
            ))}
          </TableRow>
        </TableHead>
      </Table>
      <Virtuoso
        style={{ height: '300px' }}
        totalCount={rows.length}
        itemContent={(index, item) => {
          const row = rows[index];
          const isRowSelected = selectedRows.includes(row?.feeId);
          const id = 0;
          console.log('row::::', row);

          return (
            <Table>
              <TableBody>
                <TableRow key={item}>
                  <TableCell>
                    <Checkbox
                      checked={isRowSelected}
                      onChange={() => toggleRowSelection(row?.feeId, !isRowSelected)} // Toggle row selection
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="subtitle2" fontSize={13}>
                      {row?.feeTitle}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="subtitle2" fontSize={13}>
                      {row?.amount}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <TextField
                      name="amount"
                      type="number"
                      variant="outlined"
                      size="small"
                      placeholder="Enter amount"
                      // value={inputValues[row.id]?.amount || ''}
                      // onChange={(e) => handleInputChange(row.id, 'amount', e.target.value)}
                    />
                  </TableCell>
                  {termFee.terms?.map((i, index) => {
                    const monthData = row.termFeeMapped?.find((data) => data.termId === i.termId);
                    return (
                      <TableCell
                        key={index}
                        sx={{
                          minWidth: 130,
                          textAlign: 'center',
                          borderLeft: isRowSelected ? '1px solid #23f523' : 'white',
                          backgroundColor: isRowSelected ? '#d4fcd4' : 'white',
                          color: isRowSelected ? 'green' : 'black',
                        }}
                      >
                        {monthData ? (
                          isRowSelected ? (
                            <div>
                              <TextField
                                name="amount"
                                type="number"
                                variant="outlined"
                                size="small"
                                placeholder="Enter amount"
                                defaultValue={feeAmounts[`${row.feeId}_${i.termId}`] || monthData.amount}
                                onChange={(e) => handleAmountChange(e, row, i.termId, id)}
                              />
                            </div>
                          ) : (
                            monthData.amount
                          )
                        ) : (
                          <Typography variant="subtitle2">+</Typography>
                        )}
                      </TableCell>
                    );
                  })}
                  <TableCell>
                    <IconButton
                      size="small"
                      color={isRowSelected ? 'warning' : 'success'}
                      aria-label=""
                      onClick={() => handleSave(row)}
                    >
                      Save
                    </IconButton>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          );
        }}
      />
    </TableContainer>
  );
};

export default function TestScrolling() {
  const [selectedRows, setSelectedRows] = useState([]);
  const [inputValues, setInputValues] = useState({});

  const { confirm } = useConfirm();
  const dispatch = useAppDispatch();

  const createTermFeeSettingListData = useAppSelector(getcreateTermFeeSettingListData);
  const feeDateSettingsData = useAppSelector(getfeeDateSettingsData);
  const feeDateSettingsStatus = useAppSelector(getfeeDateSettingsStatus);
  const [termFee, setTermFeeData] = useState<any>([]);
  const [termFeeDetails, setTermFeeDetails] = useState<any[]>([]);

  const [individualSaveLoading, setIndividualSaveLoading] = useState<Record<string, boolean>>({});
  const [saving, setSaving] = useState(false);
  const [savingAll, setSavingAll] = useState(false);
  const [switchToUpdateButton, setSwitchToUpdateButton] = useState(false);
  const [loading, setLoading] = useState(false);
  const [succesResponse, setSuccesResponse] = useState('');
  const [showSuccessIcon, setShowSuccessIcon] = useState<{ [key: string]: boolean }>({});
  const [showErrorIcon, setShowErrorIcon] = useState<{ [key: string]: boolean }>({});
  const [individualSaveButtonEnabled, setIndividualSaveButtonEnabled] = useState<{ [key: string]: boolean }>({});
  const [termAmounts, setTermAmounts] = useState<{ [key: string]: number }>({});
  const [checkedRows, setCheckedRows] = useState({});
  const [feeAmounts, setFeeAmounts] = useState<{ [key: string]: number }>({});
  const [amountEnter, setAmountEnter] = useState<any>(0 || '');

  const currentTermFeeListRequest = React.useMemo(
    () => ({
      adminId: 8,
      academicId: 11,
      feeTypeId: 1,
      sectionId: 1,
    }),
    []
  );

  const loadFeeDateSettingsList = useCallback(
    async (request: { adminId: number; academicId: number; sectionId: number; feeTypeId: number }) => {
      try {
        const data: any = await dispatch(fetchFeeDateSettings(request)).unwrap();
        console.log('data::::', data);
        const TermFeeDetails = data.termFeeDetails ? data.termFeeDetails.map((item: any) => ({ ...item })) : [];
        setTermFeeDetails(TermFeeDetails);
        // console.log('TermFeeDetails::::', TermFeeDetails);
      } catch (error) {
        console.error('Error loading term fee list:', error);
      }
    },
    [dispatch]
  );

  React.useEffect(() => {
    if (feeDateSettingsStatus === 'idle') {
      loadFeeDateSettingsList(currentTermFeeListRequest);
    }
    setTermFeeData(feeDateSettingsData);
  }, [dispatch, currentTermFeeListRequest, feeDateSettingsData, feeDateSettingsStatus, loadFeeDateSettingsList]);

  const toggleRowSelection = (rowId, isSelected) => {
    setSelectedRows((prevSelectedRows) => {
      if (rowId === null) {
        return isSelected ? rows.map((row) => row.id) : [];
      }
      if (isSelected) {
        return [...prevSelectedRows, rowId];
      } else {
        return prevSelectedRows.filter((id) => id !== rowId);
      }
    });
  };

  const handleInputChange = (rowId, field, value) => {
    setInputValues((prev) => ({
      ...prev,
      [rowId]: {
        ...prev[rowId],
        [field]: field === 'amount' ? value : prev[rowId]?.feeMonths,
        feeMonths: {
          ...(prev[rowId]?.feeMonths || {}),
          ...(field !== 'amount' && { [field]: value }),
        },
      },
    }));
  };

  const handleSave = useCallback(
    async (row: any) => {
      try {
        const termsLength = Array.isArray(termFee?.terms) ? termFee.terms.length : 0;
        const calAmount = termsLength * termAmounts[`${row.feeId}_`] || 0;
        const cellAmounts = termAmounts[`${row.feeId}_`] || 0;

        console.log('calAmount----::::', calAmount);
        console.log('cellAmounts----::::', cellAmounts);
        console.log('row----::::', row);

        if (calAmount > row.amount || cellAmounts > row.amount) {
          // setCellValidationError(true);
          // seTtotalTermAmount(row.amount);
        } else {
          setSaving(true);
          const { feeId } = row;
          console.log('row::::----', row);

          const allTermAmntWithFeeIdUpdates: { [key: number]: any[] } = {};

          // Check if the first feeId in the feeAmounts matches the current row's feeId
          const feeIdArray = Object.keys(feeAmounts).map((key) => parseInt(key.split('_')[0], 10));
          // if (firstFeeId !== feeId) {
          //   // If the feeId doesn't match, return early without executing further logic
          //   return;
          // }

          // Construct feeAmount array based on section amounts
          const feeAmountsArray = Object.keys(feeAmounts)
            .filter((key) => parseInt(key.split('_')[0], 10) === feeId) // Filter objects where feeId matches row.feeId
            .map((key) => {
              const amount = feeAmounts[key];
              if (!Number.isNaN(amount)) {
                return {
                  termId: parseInt(key.split('_')[1], 10),
                  amount,
                  dbResult: 'string',
                  academicTermId: 0,
                };
              }
              return null;
            })
            .filter((feeAmount) => feeAmount !== null); // Remove any null values from the array

          const feeAmountsKeys = Object.keys(feeAmountsArray);

          // Check if any of the amounts is zero
          const zeroAmountExists = feeAmountsKeys.some((key) => feeAmounts[key] === 0);

          // const zeroAmountExists = Object.values(feeAmounts).some((amount) => amount === 0);

          // Check if any of the amounts is zero
          console.log('feeAmountsArray----::::', feeAmountsArray);
          console.log('feeIdArray----::::', feeIdArray);
          console.log('feeAmounts----::::', feeAmounts);
          if (zeroAmountExists) {
            setShowErrorIcon((prevMap) => ({ ...prevMap, [feeId]: true }));
            setTimeout(() => {
              setShowErrorIcon((prevMap) => ({ ...prevMap, [feeId]: false }));
            }, 2000);
            setTermAmounts({});
            setFeeAmounts({});
            // Display error message for zero amount
            console.error('Error: Amount cannot be zero');
          } else {
            // Store feeAmounts in state

            setIndividualSaveLoading((prevMap) => ({ ...prevMap, [feeId]: true }));

            const sendReq = [
              {
                adminId: 8,
                accademicId: 11,
                academicFeeId: 0,
                sectionId: 1,
                feeId,
                feeTypeId: 1,
                termAmount: feeAmountsArray,
              },
            ];
            const actionResult = await dispatch(createTermFeeSetting(sendReq));

            if (actionResult && Array.isArray(actionResult.payload)) {
              const results = actionResult.payload;
              console.log('results::::----', results);
              // Filter out items without termAmount property
              const filteredResults = results.filter((f) => f.termAmount);
              console.log('filteredResults::::----', filteredResults);

              // Extract termAmount arrays
              const termAmountsArray = filteredResults.map((f) => f.termAmount);

              // Flatten the array of termAmount arrays
              const termAmnt = termAmountsArray.flat();
              console.log('termAmnt::::----', termAmnt);

              // Find the item with dbResult === 'Success'
              const SuccessResult = termAmnt.find((f) => f.dbResult === 'Success');
              console.log('SuccessResult::::----', SuccessResult);
              if (SuccessResult) {
                setSuccesResponse(SuccessResult.dbResult);
              }

              // Find the item with dbResult === 'Updated'
              const UpdateResult = termAmnt.find((f) => f.dbResult === 'Updated');
              console.log('UpdateResult::::----', UpdateResult);
              if (UpdateResult) {
                setSuccesResponse(UpdateResult.dbResult);
              }
              setSaving(false);
              // setTermFeeDetails(response);
              setIndividualSaveLoading((prevMap) => ({ ...prevMap, [row.feeId]: false }));
              setIndividualSaveButtonEnabled((prevEnabled) => ({
                ...prevEnabled,
                [feeId]: false, // Enable the button again after the operation completes
              }));
              console.log('checkedRows::::----', checkedRows);

              // setFeeAmounts([]);

              setFeeAmounts((prevAmounts) => {
                const updatedAmounts = { ...prevAmounts };

                // Remove the values corresponding to the current row's feeId
                Object.keys(updatedAmounts).forEach((key: any) => {
                  const [currentFeeId, currentTermId] = key.split('_').map(Number);
                  if (currentFeeId === feeId) {
                    delete updatedAmounts[key];
                  }
                });

                return updatedAmounts;
              });

              const termAmntWithFeeId = termAmnt.map((item) => ({
                ...item,
              }));
              loadFeeDateSettingsList(currentTermFeeListRequest);

              // Collect all updates for each feeId
              if (!allTermAmntWithFeeIdUpdates[feeId]) {
                allTermAmntWithFeeIdUpdates[feeId] = [];
              }
              allTermAmntWithFeeIdUpdates[feeId] = [...allTermAmntWithFeeIdUpdates[feeId], ...termAmntWithFeeId];

              // // Update termFeeMapped for the current row
              const updatedTermFeeDetails = termFeeDetails.map((item) => {
                if (item.feeId === feeId && Array.isArray(item.termFeeMapped)) {
                  const updatedTermFeeMapped = [...item.termFeeMapped, ...termAmntWithFeeId];
                  return { ...item, termFeeMapped: updatedTermFeeMapped };
                }
                return item;
              });

              setTermFeeDetails(updatedTermFeeDetails);

              setShowSuccessIcon((prevMap) => ({ ...prevMap, [row.feeId]: true }));
              setTimeout(() => {
                setShowSuccessIcon((prevMap) => ({ ...prevMap, [row.feeId]: false }));
              }, 5000);
            } else {
              setIndividualSaveLoading((prevMap) => ({ ...prevMap, [row.feeId]: false }));
              setShowErrorIcon((prevMap) => ({ ...prevMap, [row.feeId]: true }));
              setTimeout(() => {
                setShowErrorIcon((prevMap) => ({ ...prevMap, [row.feeId]: false }));
              }, 2000);
            }
            // loadFeeDateSettingsList(currentTermFeeListRequest);
          }
          const updatedTermFeeDetails = termFeeDetails.map((item) => {
            if (allTermAmntWithFeeIdUpdates[item.feeId] && Array.isArray(item.termFeeMapped)) {
              const updatedTermFeeMapped = [...item.termFeeMapped, ...allTermAmntWithFeeIdUpdates[item.feeId]];
              return { ...item, termFeeMapped: updatedTermFeeMapped };
            }
            return item;
          });

          setTermFeeDetails(updatedTermFeeDetails);
        }
      } catch (error) {
        // Handle errors here
        setIndividualSaveButtonEnabled((prevEnabled) => ({
          ...prevEnabled,
          [row.feeId]: true,
        }));
        setShowErrorIcon((prevMap) => ({ ...prevMap, [row.feeId]: true }));
        setTimeout(() => {
          setShowErrorIcon((prevMap) => ({ ...prevMap, [row.feeId]: false }));
        }, 2000);
        setIndividualSaveLoading((prevMap) => ({ ...prevMap, [row.feeId]: false }));
        const errorMessage = (
          <ErrorMessage loop={false} jsonIcon={ErrorIcon} message="Something went wrong please try again." />
        );
        await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
      }
    },
    [
      dispatch,
      feeAmounts,
      checkedRows,
      loadFeeDateSettingsList,
      currentTermFeeListRequest,
      termFeeDetails,
      confirm,
      termAmounts,
      termFee,
    ]
  );

  return (
    <DataTable
      rows={termFeeDetails}
      handleSave={handleSave}
      termFee={termFee}
      selectedRows={selectedRows}
      toggleRowSelection={toggleRowSelection}
      handleInputChange={handleInputChange}
      inputValues={inputValues}
    />
  );
}
