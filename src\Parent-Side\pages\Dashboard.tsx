import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import LinkCardRoot from '@/Parent-Side/features/Dashboard/LinkCard';
import styled, { useTheme } from 'styled-components';
import { Box, Card, Divider, Grid, Stack } from '@mui/material';
import Birthday from '@/Parent-Side/features/Dashboard/Birthday/Birthday';
import DownloadLinkCard from '@/Parent-Side/features/Dashboard/DownloadLinkCard';
import StatusCard from '@/Parent-Side/features/Dashboard/StatusCard';
import Events from '@/Parent-Side/features/Dashboard/Events';
import Graph from '@/Parent-Side/features/Dashboard/Graph';
import Timetable from '@/Parent-Side/features/Dashboard/TimetableCard';
import { breakPointsMaxwidth } from '@/config/breakpoints';
import Statistic from '@/Parent-Side/features/Dashboard/Statistic';
import BottomBirthdayDrawer from '@/Parent-Side/features/Dashboard/RightSideDrawer';
import useAuth from '@/hooks/useAuth';
import { useEffect } from 'react';
import { fetchClassList } from '@/store/Dashboard/dashboard.thunks';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { getClassData, getClassStatus } from '@/config/storeSelectors';
import { useAppSelector } from '@/hooks/useAppSelector';
import SmallCalendar from '@/components/Calendar/SmallCalendar';
import Carousel from '@/Parent-Side/features/Dashboard/Carousel';
import LinkCard2 from '../features/Dashboard/LinkCard2';
import Notification from '../features/Dashboard/notification';
import Noticeboard from '../features/Dashboard/Noticeboard';

const MainDashboardRoot = styled.div`
  min-height: calc(100vh - ${TOP_BAR_HEIGHT});
  padding: 0.5rem;
  padding-top: 0rem;
  padding-bottom: 0rem;
  overflow: hidden;
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.wihite : props.theme.palette.grey[800]};
  @media ${breakPointsMaxwidth.sm} {
    /* min-height: calc(100vh  - 1rem); */
  }

  .status-card {
  }
  .Bday-Card {
    height: calc(100vh - 112px);
    width: 290px;
    position: fixed;
    right: 0;
    @media screen and (max-width: 1300px) {
      width: 250px;
    }
  }

  .right-side::-webkit-scrollbar {
    display: none;
  }
  .center::-webkit-scrollbar {
    display: none;
  }
  .events-statistic {
  }
  .darkmode-color {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  }
  .main {
    width: calc(100% - 300px);
    @media screen and (max-width: 1300px) {
      width: calc(100% - 265px);
    }
    @media ${breakPointsMaxwidth.lg} {
      width: 100%;
    }
  }
`;
function MainDashboard() {
  const { user } = useAuth();
  const theme = useTheme();
  const dispatch = useAppDispatch();
  const adminId: number | undefined = user?.accountId;
  console.log(adminId);

  const ClassData = useAppSelector(getClassData);
  const ClassStatus = useAppSelector(getClassStatus);

  useEffect(() => {
    dispatch(fetchClassList(adminId));
  }, [dispatch, adminId]);
  return (
    <Page title="Dashboard">
      <MainDashboardRoot>
        <Stack
          className="main"
          sx={{
            '&::-webkit-scrollbar': {
              width: '0px',
            },
          }}
        >
          <Box>
            <LinkCardRoot />
            <Card
              elevation={0}
              sx={{
                margin: '0rem .5rem 0rem .5rem',
                minHeight: '240px',
                borderRadius: 5,
                backgroundColor:
                  theme.themeMode === 'light' ? theme.palette.success.lighter : theme.palette.success.lighter,
              }}
            >
              <Carousel />
            </Card>
            <LinkCard2 />
            <Grid container>
              <Grid item xl={8.5} xs={12} lg={12} className="events-statistic">
                {/* <Card sx={{ margin: '1rem .5rem 1rem .5rem', height: '340px' }} className="darkmode-color">
                  <Events title="Events" adminId={adminId} />
                </Card>
                <Card
                  elevation={5}
                  sx={{ margin: '1rem .5rem 1rem .5rem', height: { xs: '390px', sm: '340px' } }}
                  className="darkmode-color"
                >
                  <Statistic title="Fee Statistics" />
                </Card> */}
              </Grid>
              <Grid item xl={3.5} xs={12} lg={12} className="Graph ">
                {/* <Grid container>
                  <Grid item xl={12} lg={6} md={6} sm={6} xs={12}>
                    <Graph />
                  </Grid>
                  <Grid item xl={12} lg={6} md={6} sm={6} xs={12}>
                    <OnlineVideo />
                  </Grid>
                </Grid> */}
              </Grid>
            </Grid>
            <Card elevation={5} sx={{ margin: '.5rem .5rem 1rem .5rem' }} className="darkmode-color">
              <Timetable ClassData={ClassData} />
            </Card>
            <Card elevation={5} sx={{ margin: '.5rem .5rem 1rem .5rem' }} className="darkmode-color">
              <Notification />
            </Card>
            <Card sx={{ margin: '1rem .5rem 1rem .5rem', height: '340px' }} className="darkmode-color">
              <Events title="Online Video Class" adminId={adminId} />
            </Card>
          </Box>
          {/* ========== right side ========= */}
          <Box
            sx={{
              '&::-webkit-scrollbar': {
                width: '0px', // Adjust the width of the scrollbar as per your requirement
              },
              display: { xs: 'none', lg: 'block ' },
              margin: '1rem .5rem 1rem 0rem',
              overflow: 'auto',
            }}
            className=" Bday-Card "
          >
            <Card
              elevation={0}
              sx={{ borderBottomLeftRadius: 0, borderBottomRightRadius: 0 }}
              className="darkmode-color"
            >
              <Box p={2}>
                <SmallCalendar />
              </Box>
            </Card>
            {/* <Divider sx={{ mt: '1rem', borderColor: 'grey.500', borderWidth: '2px' }} /> */}
            <Card elevation={0} sx={{ borderRadius: 0, my: 1 }} className="darkmode-color">
              <Birthday />
            </Card>
            <Card
              elevation={0}
              sx={{
                height: 'calc(100vh - 350px)',
                overflow: 'auto',
                borderTopLeftRadius: 0,
                borderTopRightRadius: 0,
              }}
            >
              <Noticeboard />
            </Card>
          </Box>
          <Grid
            item
            xl={3}
            lg={4}
            sm={12}
            sx={{ display: { xs: 'block', lg: 'none ' }, position: 'fixed', right: '10px', bottom: '10px' }}
          >
            <BottomBirthdayDrawer />
          </Grid>
        </Stack>
      </MainDashboardRoot>
    </Page>
  );
}

export default MainDashboard;
