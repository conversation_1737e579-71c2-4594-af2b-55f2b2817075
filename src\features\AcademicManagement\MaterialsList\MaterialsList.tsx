/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Typography,
  Card,
  IconButton,
} from '@mui/material';
import styled from 'styled-components';
import { CLASS_SELECT, YEAR_SELECT } from '@/config/Selection';
import { smsrows } from '@/config/smsData';
import Popup from '@/components/shared/Popup/Popup';
import DeleteIcon from '@mui/icons-material/Delete';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import VisibilityIcon from '@mui/icons-material/Visibility';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import { BsMap } from 'react-icons/bs';
import { MdAdd, MdDelete, MdDownload } from 'react-icons/md';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import MapMaterials from './MapMaterials';
import NewMaterial from './NewMaterial';

const MaterialsListRoot = styled.div`
  padding: 1rem;
  .Card {
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }
  }
`;

function MaterialsList() {
  const [Delete, setDelete] = React.useState(false);
  const [drawerOpen, setDrawerOpen] = React.useState<boolean>(false);
  const [map, setMap] = React.useState(false);
  const [popupnew, setNew] = React.useState(false);

  const handleDelete = () => {
    setDelete(true);
    if (drawerOpen === true) {
      setDrawerOpen(false);
    }
  };
  const handleCloseDelete = () => setDelete(false);

  const handleMap = () => setMap(true);
  const handleCloseMap = () => setMap(false);

  const handleNew = () => setNew(true);
  const handleCloseNew = () => setNew(false);

  const DrawerOpen = () => setDrawerOpen(true);
  const DrawerClose = () => setDrawerOpen(false);

  // ----------------------------------------Success Popup-------------------------------
  const [popupSuccess, setPopupSuccess] = React.useState(false);
  const handleClickOpen = () => {
    if (map === true) {
      setMap(false);
    } else if (popupnew === true) {
      setNew(false);
    }
    setPopupSuccess(true);
  };
  const handleClickClose = () => setPopupSuccess(false);
  return (
    <Page title="Materials List">
      <MaterialsListRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between" sx={{}}>
            <Typography variant="h6" fontSize={17} width="100%">
              Materials List
            </Typography>
            <Stack
              pb={1}
              sx={{ flexDirection: { xs: 'column', md: 'row' } }}
              gap={1}
              width="100%"
              justifyContent="end"
              alignItems="end"
            >
              <Button sx={{ borderRadius: '20px', width: '150px' }} variant="outlined" size="small" onClick={handleMap}>
                <BsMap size="20px" style={{ paddingRight: 5 }} /> Map Material
              </Button>
              <Button size="small" sx={{ borderRadius: '20px', width: '150px' }} variant="outlined" onClick={handleNew}>
                <MdAdd size="20px" /> New Material
              </Button>
            </Stack>
          </Stack>
          <Divider />
          <Grid pb={4} pt={2} container spacing={3}>
            <Grid item lg={3} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Academic Year
              </Typography>
              <Autocomplete
                options={YEAR_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select" />}
              />
            </Grid>
            <Grid item lg={3} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Select Class
              </Typography>
              <Autocomplete
                options={CLASS_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select" />}
              />
            </Grid>
            <Grid item lg={3} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Select Subject
              </Typography>
              <Autocomplete
                options={YEAR_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select" />}
              />
            </Grid>
            <Grid item lg={3} xs={12}>
              <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                <Button variant="contained" color="secondary" fullWidth>
                  Reset
                </Button>
                <Button variant="contained" color="primary" fullWidth>
                  Search
                </Button>
              </Stack>
            </Grid>
          </Grid>

          <Box>
            <Paper
              sx={{
                border: `1px solid #e8e8e9`,
                width: '100%',
                overflow: 'auto',
                '&::-webkit-scrollbar': {
                  width: 0,
                },
              }}
            >
              <TableContainer
                sx={{
                  width: { xs: '950px', md: '100%' },
                }}
              >
                <Table stickyHeader aria-label="simple table">
                  <TableHead>
                    <TableRow>
                      <TableCell>Sl.No</TableCell>
                      <TableCell>Class</TableCell>
                      <TableCell>Subject</TableCell>
                      <TableCell>Title</TableCell>
                      <TableCell>Total Material</TableCell>
                      <TableCell>Created By</TableCell>
                      <TableCell>Created Date</TableCell>
                      <TableCell>Action</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {smsrows.map((lnrow) => (
                      <TableRow hover key={lnrow.rollNo}>
                        <TableCell>{lnrow.rollNo}</TableCell>
                        <TableCell>VIII-A</TableCell>
                        <TableCell>Test Subject</TableCell>
                        <TableCell>Title Name</TableCell>
                        <TableCell>02</TableCell>
                        <TableCell>Passdaily Admin</TableCell>
                        <TableCell>07/05/2023,10:03 AM</TableCell>
                        <TableCell>
                          <Stack direction="row" spacing={1}>
                            <IconButton size="small" onClick={handleNew}>
                              <ModeEditIcon />
                            </IconButton>
                            <IconButton onClick={DrawerOpen} size="small" color="info">
                              <VisibilityIcon />
                            </IconButton>
                            <IconButton onClick={handleDelete} color="error" size="small">
                              <DeleteIcon />
                            </IconButton>
                          </Stack>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Paper>
          </Box>
        </Card>
        <Popup
          size="xs"
          state={Delete}
          onClose={handleCloseDelete}
          popupContent={<DeleteMessage message="Are you sure want to delete?" />}
        />
        <Popup
          size="md"
          state={map}
          onClose={handleCloseMap}
          popupContent={<MapMaterials onClick={handleClickOpen} />}
        />
        <Popup
          size="md"
          state={popupnew}
          onClose={handleCloseNew}
          popupContent={<NewMaterial onClick={handleClickOpen} />}
        />
        <TemporaryDrawer
          onClose={DrawerClose}
          Title="Added Materials"
          state={drawerOpen}
          DrawerContent={
            <TableContainer
              sx={{
                mt: 5,
                width: '100%',
                '&::-webkit-scrollbar': {
                  width: 0,
                },
              }}
            >
              <Divider />
              <Box sx={{ display: 'flex', justifyContent: 'end' }}>
                <Button sx={{ borderRadius: '20px' }} onClick={handleNew}>
                  <MdAdd size="20px" /> Add File
                </Button>
              </Box>
              <Table stickyHeader aria-label="simple table">
                <TableHead>
                  <TableRow>
                    <TableCell>Sl.No</TableCell>
                    <TableCell width="50%">File Title</TableCell>
                    <TableCell>Action</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {smsrows.map((lnrow) => (
                    <TableRow hover key={lnrow.rollNo}>
                      <TableCell>{lnrow.rollNo}</TableCell>
                      <TableCell>Title Name</TableCell>
                      <TableCell>
                        <Stack direction="row" spacing={1}>
                          <IconButton size="small">
                            <MdDownload />
                          </IconButton>
                          <IconButton onClick={handleDelete} size="small">
                            <MdDelete />
                          </IconButton>
                        </Stack>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          }
        />
      </MaterialsListRoot>
      <Popup
        size="xs"
        state={popupSuccess}
        onClose={handleClickClose}
        popupContent={<SuccessMessage message="Materials Mapped Successfully" />}
      />
    </Page>
  );
}

export default MaterialsList;
