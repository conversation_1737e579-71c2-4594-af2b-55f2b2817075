import Loadable from '@/components/shared/Loadable';
import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';

const ParentEnquiry = Loadable(lazy(() => import('@/pages/ParentEnquiry')));
const EnquiryList = Loadable(lazy(() => import('@/features/ParentEnquiry/EnquiryList')));

export const enquiryRoutes: RouteObject[] = [
  {
    path: '/parent-enquiry',
    element: <ParentEnquiry />,
    children: [
      {
        path: 'list',
        element: <EnquiryList />,
      },
    ],
  },
];
