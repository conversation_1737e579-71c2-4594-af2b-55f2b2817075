import { useState } from 'react';
import {
  Button,
  TextField,
  Select,
  MenuItem,
  Typography,
  Box,
  Stack,
  FormControl,
  IconButton,
  useTheme,
} from '@mui/material';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import EditIcon from '@mui/icons-material/Edit';
import { AlbumImageInfo } from '@/types/Album';
import LoadingButton from '@mui/lab/LoadingButton';
import { STATUS_OPTIONS } from '@/config/Selection';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { toggleDrawer } from '@/store/Layout/drawer.slice';

export type CreateEditClassFormProps = {
  onSave?: (values: AlbumImageInfo) => void;
  onCancel?: () => void;
  onClose?: () => void;
  albumInfo?: AlbumImageInfo;
  isSubmitting?: boolean;
};

const CreateGroupsValidationSchema = Yup.object({
  albumName: Yup.string().required('Please enter Album Name'),
  imageTitle: Yup.string().required('Please enter Image Title'),
});

function CreateGroups({ onSave, onCancel, isSubmitting, albumInfo }: CreateEditClassFormProps) {
  const [uploadedImages, setUploadedImages] = useState('');
  const theme = useTheme();
  const {
    values: { albumName, imageTitle },
    handleChange,
    handleSubmit,
    touched,
    errors,
    setFieldValue,
  } = useFormik<AlbumImageInfo>({
    initialValues: {
      //   groupName: albumInfo.albumId,
      //   groupName: albumInfo.groupName,
      //   groupType: albumInfo.groupType,
    },
    validationSchema: CreateGroupsValidationSchema,
    onSubmit: (values) => {
      // Call the onSave function with the updated values
      onSave(values);
    },
  });

  return (
    <Box mt={3}>
      <form noValidate onSubmit={handleSubmit}>
        <Stack
          sx={{
            height: 'calc(100vh - 150px)',
            overflow: 'auto',
            '&::-webkit-scrollbar': {
              width: 0,
            },
          }}
          direction="column"
        >
          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Group Name
            </Typography>
            <TextField
              placeholder="Enter name"
              name=""
              value={imageTitle}
              onChange={handleChange}
              error={touched.imageTitle && !!errors.imageTitle}
              helperText={errors.imageTitle}
              disabled={isSubmitting}
            />
          </FormControl>
          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Group Type
            </Typography>
            <Select
              name="classStatus"
              value=""
              onChange={handleChange}
              //   error={touched.classStatus && !!errors.classStatus}
              disabled={isSubmitting}
            >
              {STATUS_OPTIONS.map((opt) => (
                <MenuItem key={opt.id} value={opt.id}>
                  {opt.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Status
            </Typography>
            <Select
              name="classStatus"
              value=""
              onChange={handleChange}
              //   error={touched.classStatus && !!errors.classStatus}
              disabled={isSubmitting}
            >
              {STATUS_OPTIONS.map((opt) => (
                <MenuItem key={opt.id} value={opt.id}>
                  {opt.name}
                </MenuItem>
              ))}
            </Select>
            {/* {touched.classStatus && !!errors.classStatus && (
              <Typography color="red" fontSize="0.625rem" variant="subtitle1">
                {errors.classStatus}
              </Typography>
            )} */}
          </FormControl>
        </Stack>
        <Box sx={{ justifyContent: 'center', pt: 3, position: 'relative' }}>
          <Stack spacing={2} direction="row" sx={{}}>
            <Button onClick={onCancel} fullWidth variant="contained" color="secondary" type="button">
              Cancel
            </Button>
            <LoadingButton
              loading={isSubmitting}
              fullWidth
              variant="contained"
              color="primary"
              type="submit"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Saving...' : 'Save'}
            </LoadingButton>
          </Stack>
        </Box>
      </form>
    </Box>
  );
}

export default CreateGroups;
