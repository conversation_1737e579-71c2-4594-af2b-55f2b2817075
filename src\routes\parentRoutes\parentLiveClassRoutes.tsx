import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import Loadable from '@/components/shared/Loadable';

const LiveClassIndex = Loadable(lazy(() => import('@/Parent-Side/pages/LiveClassIndex')));
const ZoomLiveClass = Loadable(lazy(() => import('@/Parent-Side/features/LiveClass/ZoomLiveClass')));
const ZoomScheduleList = Loadable(lazy(() => import('@/Parent-Side/features/LiveClass/ZoomScheduleList')));
const ParentLiveClass = Loadable(lazy(() => import('@/Parent-Side/features/LiveClass/ParentLiveClass')));

export const parentLiveClassRoutes: RouteObject[] = [
  {
    path: '/parent-live-class',
    element: <LiveClassIndex />,
    children: [
      {
        path: 'zoom-live-class',
        element: <ZoomLiveClass />,
      },
      {
        path: 'zoom-schedule-list',
        element: <ZoomScheduleList />,
      },
      {
        path: 'live-class',
        element: <ParentLiveClass />,
      },
    ],
  },
];
