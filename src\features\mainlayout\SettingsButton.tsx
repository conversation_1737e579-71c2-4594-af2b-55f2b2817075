import CircleButton from '@/components/Dashboard/CircleButton';
import { MdOutlineDarkMode, MdOutlineLightMode } from 'react-icons/md';
import useSettings from '@/hooks/useSettings';
import { Box } from '@mui/material';

// export type SettingsButtonProps = Omit<ComponentProps<typeof CircleButton>, 'size' | 'children'>;

function SettingsButton() {
  const { themeMode, onToggleMode } = useSettings();

  // const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);

  // const { onClick } = props;

  // const open = Boolean(anchorEl);
  // const id = open ? 'settings-menu' : undefined;

  // const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
  //   setAnchorEl(event.currentTarget);
  // };

  // const handleClose = () => {
  //   setAnchorEl(null);
  // };

  const handleToggleTheme = () => {
    onToggleMode();
  };

  return (
    <>
      <CircleButton size="small" className="d-none d-lg-flex" onClick={handleToggleTheme}>
        <Box>
          {themeMode === 'light' ? <MdOutlineDarkMode className="fs-4" /> : <MdOutlineLightMode className="fs-4" />}
        </Box>
        {/* <SlSettings size="1.2rem" /> */}
      </CircleButton>
      {/* <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        PaperProps={{
          sx: { padding: 2 },
        }}
      >
        <List>
          <ListItem disablePadding>
            <ListItemIcon>{themeMode === 'light' ? <DarkModeIcon /> : <LightModeIcon />}</ListItemIcon>
            <ListItemText
              id="switch-list-label-wifi"
              primary={`Switch to ${themeMode === 'light' ? 'Dark' : 'Light'}`}
            />
            <Switch edge="end" onChange={handleToggleTheme} checked={themeMode !== 'light'} />
          </ListItem>
          <ListItem>
            <Button
              variant="contained"
              color="secondary"
              size="small"
              type="button"
              disableElevation
              onClick={() => {
                logout();
              }}
            >
              Logout
            </Button>
          </ListItem>
        </List>
      </Popover> */}
    </>
  );
}

export default SettingsButton;
