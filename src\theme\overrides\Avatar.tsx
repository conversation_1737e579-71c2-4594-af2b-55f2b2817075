import { Components, Theme } from '@mui/material';

export default function Avatar(theme: Theme): Components<Theme> {
  return {
    MuiAvatar: {
      styleOverrides: {
        colorDefault: {
          backgroundColor: theme.palette.grey[400],
        },
        root: {
          width: 30,
          height: 30,
        },
      },
    },
    MuiAvatarGroup: {
      styleOverrides: {
        avatar: {
          fontSize: 16,
          fontWeight: theme.typography.fontWeightMedium,
          '&:first-of-type': {
            fontSize: 14,
            color: theme.palette.primary.main,
            backgroundColor: theme.palette.primary.lighter,
          },
        },
      },
    },
  };
}
