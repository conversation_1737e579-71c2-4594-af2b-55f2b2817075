import LoginUser from '@/components/shared/Popup/LoginUser';
import { SIDE_BAR_WIDTH, TOP_BAR_HEIGHT } from '@/config/Constants';
import useAuth from '@/hooks/useAuth';
import { getTextColor } from '@/utils/Colors';
import styled from 'styled-components';
import NotificationButton from '@/features/mainlayout/NotificationButton';
import SettingsButton from '@/features/mainlayout/SettingsButton';
import { Button, IconButton, Typography } from '@mui/material';
import { breakPointsMaxwidth, breakPointsMinwidth } from '@/config/breakpoints';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { toggleSidebar } from '@/store/Layout/layout.slice';
import { AiOutlineMenu } from 'react-icons/ai';
import { Link, useLocation, useOutletContext } from 'react-router-dom';
import React, { forwardRef } from 'react';
import Registration from '@/pages/Registration';
import passdailyLogo from '@/assets/SchoolLogos/logo-small.svg';
import holyLogo from '@/assets/SchoolLogos/HolyLogo.jpeg';
import carmelLogo from '@/assets/SchoolLogos/CarmelLogo.png';
import thereseLogo from '@/assets/SchoolLogos/StthereseLogo.png';
import thomasLogo from '@/assets/SchoolLogos/StThomasLogo.png';
import nirmalaLogo from '@/assets/SchoolLogos/NirmalaLogo.png';
import MIMLogo from '@/assets/SchoolLogos/MIMLogo.png';
import alFitrahLogo from '@/assets/SchoolLogos/alFitrahLogo.png';
import { useSchool } from '@/contexts/SchoolContext';

const TopBarRoot = styled.nav`
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  min-height: ${TOP_BAR_HEIGHT};
  color: ${(props) => getTextColor(props.theme)};
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.grey[900]};
  display: flex;
  padding-left: 15px;
  padding-right: 15px;
  box-shadow: ${(props) =>
    props.theme.themeMode === 'light' ? '0 2px 7px -1px rgb(0 0 0 / 9%)' : '0 2px 2px 1px rgb(14 14 14 / 61%)'};
  z-index: ${(props) => props.theme.zIndex.appBar};
  @media ${breakPointsMaxwidth.sm} {
    /* height: calc(${TOP_BAR_HEIGHT} - 1rem); */
  }
  .lhs {
    flex-shrink: 0;
    width: calc(${SIDE_BAR_WIDTH} - 15px);
    display: flex;
    align-items: center;
    .brand-wrap {
      width: 100%;
      display: flex;
      align-items: center;
      color: ${(props) =>
        props.theme.themeMode === 'light' ? props.theme.palette.common.black : props.theme.palette.common.white};
      .brand-icon {
        flex-shrink: 0;
        width: 45px;
        height: 45px;
        background-color: ${(props) =>
          props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.grey[800]};
        border-radius: 50%;
        /* box-shadow: ${(props) =>
          props.theme.themeMode === 'light'
            ? '0px 0px 15px 0px rgb(113 113 113 / 37%)'
            : '0px 0px 15px 0px rgb(0 0 0 / 60%)'}; */
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid
          ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.grey[200] : props.theme.palette.grey[700]};

        img {
          /* width: 45px;
          height: 45px; */
        }
      }

      span {
        display: block;
        width: 100%;
        font-size: 1.2rem;
        font-weight: 700;
      }
    }
  }

  .middle {
    width: 100%;
    flex-grow: 1;
    display: flex;
    justify-content: space-between;
  }

  .rhs {
    width: 100%;
    flex-grow: 1;
    display: flex;
    justify-content: space-between;

    .rhsl {
      width: 100%;
      flex-grow: 1;
      display: flex;
      justify-content: space-between;

      @media ${breakPointsMinwidth.lg} {
        padding-right: 30px;
      }

      .greet-wrap {
        display: flex;
        flex-direction: column;
        justify-content: center;

        .greet-msg {
          font-family: 'Poppins Medium';
          font-size: 1.6rem;
          color: ${(props) => getTextColor(props.theme)};
          .orange {
            font-family: 'Poppins Semibold';
            font-size: 1.6rem;
            color: ${(props) => props.theme.palette.primary.main};
          }
        }

        .wish {
          font-family: 'Poppins Regular';
          font-size: 1.1rem;
          color: ${(props) => props.theme.palette.grey[600]};
        }
      }

      .reg-button-wrap {
        flex-shrink: 0;
        display: flex;
        align-items: center;
      }
    }

    .circle-button-wrap {
      flex-shrink: 0;
      display: flex;
      align-items: center;
      gap: 15px;

      .login-user-root {
        margin-right: 15px;
      }
      @media ${breakPointsMaxwidth.sm} {
        gap: 0px;
        .login-user-root {
          margin-right: 0px;
        }
      }
    }
  }
  a {
    text-decoration: none;
  }
`;

const TopBar = forwardRef<HTMLElement>((props, ref) => {
  const { user, loginMode } = useAuth();
  const location = useLocation();
  const { selectedSchool } = useSchool();

  const dispatch = useAppDispatch();

  const handleHamburgerClick = () => {
    dispatch(toggleSidebar());
  };

  return (
    <TopBarRoot ref={ref}>
      <section className="lhs">
        <IconButton
          color="inherit"
          aria-label="open sidebar"
          edge="start"
          onClick={handleHamburgerClick}
          sx={{ mr: 2, display: { md: 'none' } }}
        >
          <AiOutlineMenu />
        </IconButton>
        <Link to={selectedSchool?.schoolId !== '2' ? '/' : 'http://holy.passdaily.in/NewDesign/Dashboard.aspx'}>
          <div className="brand-wrap ">
            <div className="brand-icon me-2 d-none d-sm-flex">
              <img
                width={50}
                src={selectedSchool?.schoolLogo || passdailyLogo}
                alt={selectedSchool?.schoolLogo || passdailyLogo}
              />
              {/* <img width={50} src={passdailyLogo} alt="passdailLogo" /> */}
              {/* <img width={45} src={holyLogo} alt="holyLogo" /> */}
              {/* <img width={45} src={carmelLogo} alt="carmelLogo" /> */}
              {/* <img width={45} src={thereseLogo} alt="thereseLogo" /> */}
              {/* <img width={45} src={thomasLogo} alt="thomasLogo" /> */}
              {/* <img width={45} src={nirmalaLogo} alt="nirmalaLogo" /> */}
              {/* <img width={45} src={alFitrahLogo} alt="alFitrahLogo" /> */}
              {/* <img width={100} src={MIMLogo} alt="MIMLogo" /> */}
            </div>
            <Typography fontWeight={600} fontSize={20}>
              {selectedSchool?.schoolName || 'Passdaily'}
              {/* Holy Angels */}
              {/* Carmel */}
              {/* St Therese */}
              {/* St Thomas */}
              {/* Nirmala */}
              {/* Al Fitrah */}
              {/* MIM School */}
            </Typography>
          </div>
        </Link>
      </section>

      <section className="rhs">
        <div className="rhsl">
          <div className="greet-wrap d-none d-md-flex">
            <h3 className="greet-msg">
              Welcome to&nbsp;
              <span className="orange">
                {/* {user?.schoolName} */}
                {/* Passdaily */}
                {/* Holy Angels School */}
                {/* Carmel Convent School */}
                {/* St Therese Convent High School */}
                {/* St Thomas School */}
                {/* Nirmala Convent School */}
                {/* Al Fitrah School */}
                Mueenul Islam Manoor High School
              </span>
            </h3>
            <h4 className="wish">Good morning, {user?.firstName || 'Guest'}</h4>
          </div>
          {location.pathname === '/' && selectedSchool?.schoolId !== '2' && (
            <div className="reg-button-wrap d-none d-lg-flex">
              <Link to="/registration">
                <Button variant="outlined" color="primary" size="medium">
                  Registration List
                </Button>
              </Link>
            </div>
          )}
        </div>
        {selectedSchool?.schoolId !== '2' && (
          <div className="circle-button-wrap">
            <NotificationButton />
            <div className="d-none d-lg-flex">
              <SettingsButton />
            </div>
            <LoginUser className="login-user-root" />
          </div>
        )}
      </section>
    </TopBarRoot>
  );
});

export default TopBar;
