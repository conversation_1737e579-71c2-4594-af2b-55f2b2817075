/* eslint-disable jsx-a11y/alt-text */
import React, { useEffect, useState } from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Stack,
  Button,
  Typography,
  Card,
  Divider,
  Tabs,
  Box,
  Avatar,
  IconButton,
  useTheme,
  tabsClasses,
  Tooltip,
  Chip,
  Collapse,
  Grid,
  Tab,
} from '@mui/material';
import styled from 'styled-components';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import { bluePreset, purplePreset } from '@/utils/Colors';
import useSettings from '@/hooks/useSettings';
import LeaveReport from './LeaveReport';
import EnquiryReport from './EnquiryReport';
import AssignmentReport from './AssignmentReport';
import ObjectiveExamReport from './ObjectiveExamReport';
import DescriptiveExamReport from './DescriptiveExamReport';
import ExamCenterStateReport from './ExamCenterStateReport';
import FeeDetailsReport from './FeeDetailsReport';
import StoreLibraryDetailsReport from './Store&LibraryDetailsReport';

const buttonNames = [
  {
    btnId: 'leaveInformation',
    btnName: 'Leave Information',
  },
  {
    btnId: 'leaveNote',
    btnName: 'Leave Note',
  },
  {
    btnId: 'enquiry',
    btnName: 'Enquiry',
  },
  {
    btnId: 'assignment',
    btnName: 'Assignment',
  },
  {
    btnId: 'objectiveExam',
    btnName: 'Objective Exam',
  },
  {
    btnId: 'descriptiveExam',
    btnName: 'DescriptiveExam',
  },
  {
    btnId: 'examCenterState',
    btnName: 'Exam Center State',
  },
  {
    btnId: 'studentFeeDetails',
    btnName: 'Student Fee Details',
  },
  {
    btnId: 'termFeeDetails',
    btnName: 'Term Fee Details',
  },
  {
    btnId: 'storeDetails',
    btnName: 'Store Details',
  },
  {
    btnId: 'libraryDetails',
    btnName: 'Library Details',
  },
];

const MasterReportRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  position: relative;
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;
  .tab {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.grey[800]};
    @media screen and (min-width: 1250px) {
      width: 100%;
    }
  }
  .MuiTabs-root {
    height: 20px;
  }
  .MuiTab-root {
    border-radius: 50px;
    height: 25px;
    border: 1px solid red;
    padding: 0px 10px;
  }

  .MuiTabScrollButton-root {
    width: 30px;
    height: 30px;
    margin: 5px;
  }
  .MuiTabScrollButton-root:hover {
    background-color: ${(props) => props.theme.palette.grey[300]};
    color: ${(props) => props.theme.palette.common.black};
  }
  .buttonStyle {
    border-radius: 50px;
    height: 25px;
  }

  .Card {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    height: calc(100vh - 7.1875rem);
    /* height: 100%; */
    overflow: auto;
    /* @media screen and (max-width: 996px) {
      height: 100%;
    } */

    .card-main-body {
      display: flex;
      flex-direction: column;
      /* max-height: calc(100% - 40px); */
      flex-grow: 1;

      .card-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: auto;
        ::-webkit-scrollbar {
          width: 0px;
        }
      }
    }
  }
`;
export type MasterReportProps = {
  onBackClick: () => void;
  datas: any;
};

function MasterReport({ onBackClick, datas }: MasterReportProps) {
  const { name, className, admissionNumber, gardianName, gardianNumber } = datas;
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const [selectedButton, setSelectedButton] = useState('Leave Information');
  const [showMore, setShowMore] = useState(false);
  const [values, setValues] = useState(0);
  // const [selectedButtonId, setSelectedButtonId] = useState('leaveInformation');

  const handleButtonClick = (buttonName: string, index: number) => {
    setValues(index);
    setSelectedButton(buttonName);
    // setSelectedButtonId(btnId);
    // Scroll to the corresponding component
  };

  return (
    <Page title="Student Details">
      <MasterReportRoot>
        <Card className="Card" elevation={1}>
          <Box
          // sx={{
          //   width: { md: 'calc(100% - 272.5px)', xs: 'calc(100% - 32px)' },
          //   borderTopRightRadius: '10px',
          //   borderTopLeftRadius: '10px',
          //   zIndex: 1,
          //   position: 'fixed',
          //   backgroundColor: isLight ? theme.palette.common.white : theme.palette.grey[800],
          // }}
          >
            <Box
              display="flex"
              justifyContent="space-between"
              alignItems="center"
              sx={{ px: { xs: 3, md: 5 }, pt: { xs: 2, md: 3 } }}
            >
              <Stack direction="row" alignItems="center" gap={2}>
                <Tooltip title="Back">
                  <IconButton
                    onClick={onBackClick}
                    sx={{
                      backgroundColor: theme.palette.secondary.main,
                      color: theme.palette.common.white,
                      '&:hover': { backgroundColor: theme.palette.secondary.dark },
                      width: '25px',
                      height: '25px',
                    }}
                    size="small"
                  >
                    <ArrowBackIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
                <Stack direction="row" gap={1} alignItems="center">
                  <Avatar
                    // sx={{ width: 40, height: 40 }}
                    alt=""
                    src="https://images.unsplash.com/photo-1633332755192-727a05c4013d?q=80&w=1780&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                  />
                  <Stack direction="row" gap={10} alignItems="center">
                    <Typography variant="h6" fontSize={17}>
                      {name}
                    </Typography>
                  </Stack>
                </Stack>
              </Stack>
              <Stack direction="row" alignItems="center" gap={2}>
                <Stack direction="row" gap={2} alignItems="center" sx={{ display: { xs: 'none', sm: 'block' } }}>
                  <Chip
                    size="small"
                    label={`${className}`}
                    variant="filled"
                    sx={{
                      fontWeight: 'Bold',
                      backgroundColor: isLight ? bluePreset.lighter : '',
                      color: isLight ? bluePreset.main : '',
                      mr: 2,
                    }}
                  />
                  <Chip
                    size="small"
                    label={`AD : ${admissionNumber}`}
                    variant="filled"
                    sx={{
                      fontWeight: 'Bold',
                      backgroundColor: isLight ? purplePreset.lighter : '',
                      color: isLight ? purplePreset.main : '',
                    }}
                  />
                </Stack>
                <Stack direction="row" alignItems="center" gap={2}>
                  <Tooltip title={showMore === false ? 'Show more' : ''}>
                    <IconButton color="primary" onClick={() => setShowMore((x) => !x)} size="small">
                      {showMore === false ? (
                        <KeyboardArrowDownIcon fontSize="small" />
                      ) : (
                        <KeyboardArrowUpIcon fontSize="small" />
                      )}
                    </IconButton>
                  </Tooltip>
                </Stack>
              </Stack>
            </Box>

            <Collapse in={showMore}>
              <Box sx={{ px: { xs: 3, md: 5 }, pt: { xs: 2, md: 3 } }}>
                <Grid container direction="row" justifyContent="space-around" alignItems="center">
                  <Grid item lg={0} xs={12} sm={6} md={6}>
                    <Typography variant="subtitle1" fontSize={13} mr={2} sx={{ display: { xs: 'block', sm: 'none' } }}>
                      Class : <b>{className}</b>
                    </Typography>
                  </Grid>
                  <Grid item lg={0} xs={12} sm={6} md={6}>
                    <Typography variant="subtitle1" fontSize={13} mr={2} sx={{ display: { xs: 'block', sm: 'none' } }}>
                      Admission Number : <b>{admissionNumber}</b>
                    </Typography>
                  </Grid>
                  <Grid item lg={2} xs={12} sm={6} md={6}>
                    <Typography variant="subtitle1" fontSize={13}>
                      Gender : <b>Male</b>
                    </Typography>
                  </Grid>
                  <Grid item lg={3} xs={12} sm={6} md={6}>
                    <Typography variant="subtitle1" fontSize={13} mr={2}>
                      D.O.B : <b>10/02/2000</b>
                    </Typography>
                  </Grid>
                  <Grid item lg={4} xs={12} sm={6} md={6}>
                    <Typography variant="subtitle1" fontSize={13}>
                      Guardian Name : <b>{gardianName}</b>
                    </Typography>
                  </Grid>
                  <Grid item lg={3} xs={12} sm={6} md={6}>
                    <Typography variant="subtitle1" fontSize={13}>
                      Number : <b>{gardianNumber}</b>
                    </Typography>
                  </Grid>
                </Grid>
              </Box>
            </Collapse>
            <Divider sx={{ mt: 1 }} />
            <Tabs
              className="tab"
              // value={values}
              sx={{
                display: 'flex',
                alignItems: 'center',
                px: { xs: 3, sm: 0 },
                [`& .${tabsClasses.scrollButtons}`]: {
                  '&.Mui-disabled': { opacity: 0.3 },
                },
              }}
              variant="scrollable"
              scrollButtons
              aria-label="basic tabs"
            >
              {buttonNames.map((button, index) => (
                // <Tab
                //   key={button.btnId}
                //   label={button.btnName}
                //   component={Button}
                //   href={`#${button.btnId}`}
                //   onClick={() => handleButtonClick(button.btnName, index)}
                //   // className="buttonStyle"
                // />
                <Button
                  href={`#${button.btnId}`}
                  fullWidth
                  sx={{
                    flexShrink: 0,
                    mr: 1,
                    '&:hover': {
                      backgroundColor: selectedButton === button.btnName ? null : theme.palette.primary.lighter,
                      borderColor: selectedButton === button.btnName ? null : theme.palette.primary.main,
                      color: selectedButton === button.btnName ? null : theme.palette.primary.main,
                    },
                  }}
                  key={button.btnId}
                  color={selectedButton === button.btnName ? 'primary' : 'secondary'}
                  onClick={() => handleButtonClick(button.btnName, index)}
                  variant={selectedButton === button.btnName ? 'contained' : 'outlined'}
                  className="buttonStyle"
                >
                  {button.btnName}
                </Button>
              ))}
            </Tabs>
            <Divider />
          </Box>

          <Stack
            direction="column"
            sx={{
              position: 'relative',
              height: '100%',
              // mt: '100px',
              px: { xs: 3, md: 5 },
              pb: { xs: 2, md: 3 },
              overflow: 'auto',
            }}
          >
            <div>
              <LeaveReport idLeaveInfo="leaveInformation" idLeaveNote="leaveNote" />
            </div>
            <div id="enquiry">
              <EnquiryReport />
            </div>
            <div id="assignment">
              <AssignmentReport />
            </div>
            <div id="objectiveExam">
              <ObjectiveExamReport />
            </div>
            <div id="descriptiveExam">
              <DescriptiveExamReport />
            </div>
            <div id="examCenterState">
              <ExamCenterStateReport />
            </div>
            <div>
              <FeeDetailsReport idStudentFee="studentFeeDetails" idTermFee="termFeeDetails" />
            </div>
            <div>
              <StoreLibraryDetailsReport idStoreDetails="storeDetails" idLibraryDetails="libraryDetails" />
            </div>
          </Stack>
        </Card>
      </MasterReportRoot>
    </Page>
  );
}

export default MasterReport;
