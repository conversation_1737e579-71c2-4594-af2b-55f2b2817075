import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import Loadable from '@/components/shared/Loadable';

const StoreManagement = Loadable(lazy(() => import('@/pages/StoreManagement')));
const ManageProducts = Loadable(lazy(() => import('@/features/StoreManagement/ManageProducts')));
const MapProducts = Loadable(lazy(() => import('@/features/StoreManagement/MapProducts')));
const MappedList = Loadable(lazy(() => import('@/features/StoreManagement/MappedList')));
const ProductSale = Loadable(lazy(() => import('@/features/StoreManagement/ProductSale')));
const SaleList = Loadable(lazy(() => import('@/features/StoreManagement/SaleList')));
const DailyReport = Loadable(lazy(() => import('@/features/StoreManagement/DailyReport')));

export const storeManagementRoutes: RouteObject[] = [
  {
    path: '/store-management',
    element: <StoreManagement />,
    children: [
      {
        path: 'product-list',
        element: <ManageProducts />,
      },
      {
        path: 'map-product',
        element: <MapProducts />,
      },
      {
        path: 'mapped-list',
        element: <MappedList />,
      },
      {
        path: 'new-sale',
        element: <ProductSale />,
      },
      {
        path: 'sale-list',
        element: <SaleList />,
      },
      {
        path: 'daily-report',
        element: <DailyReport />,
      },
    ],
  },
];
