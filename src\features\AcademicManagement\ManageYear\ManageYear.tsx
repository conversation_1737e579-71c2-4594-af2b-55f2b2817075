/* eslint-disable jsx-a11y/alt-text */
import React, { FormEvent, useCallback, useEffect, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  Collapse,
  FormControl,
  Select,
  MenuItem,
  Chip,
  SelectChangeEvent,
  Tooltip,
} from '@mui/material';
import styled from 'styled-components';
import SearchIcon from '@mui/icons-material/Search';
import { STATUS_OPTIONS } from '@/config/Selection';
import { MdAdd } from 'react-icons/md';
import DeleteIcon from '@mui/icons-material/Delete';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import { YearListRequest, YearListInfo } from '@/types/AcademicManagement';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useAppSelector } from '@/hooks/useAppSelector';
import {
  getYearListData,
  getYearListPageInfo,
  getYearListStatus,
  getYearSortColumn,
  getYearSortDirection,
  getYearSubmitting,
} from '@/config/storeSelectors';
import { setSortColumn, setSortDirection } from '@/store/Academics/YearManagement/yearManagement.slice';
import {
  addNewYear,
  deleteYear,
  fetchYearList,
  updateYear,
} from '@/store/Academics/YearManagement/yearManagement.thunks';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import CreateEditYearForm from './Create&Edit';

const ManageYearRoot = styled.div`
  padding: 1rem;
  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }
    .card-main-body {
      display: flex;
      flex-direction: column;
      height: calc(100% - 60px);
      /* flex-grow: 1; */

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

// const DefaultYearInfo: YearListInfo = {
//   academicId: 0,
//   academicTime: '',
//   academicDescription: '',
//   academicStatus: 1,
// };
const DefaultYearInfo: YearListInfo = {
  academicId: 0,
  academicTime: '',
  academicDescription: '',
  academicStatus: 1,
};

function ManageYear() {
  const { confirm } = useConfirm();
  const dispatch = useAppDispatch();

  const [selectedYearDetail, setSelectedYearDetail] = useState<YearListInfo>(DefaultYearInfo);
  const [academicTimeFilter, setYearNameFilter] = useState('');
  const [academicStatusFilter, setYearStatusFilter] = useState(-1);

  const yearListStatus = useAppSelector(getYearListStatus);
  const yearListData = useAppSelector(getYearListData);
  // const yearListError = useAppSelector(getYearListError);
  const paginationInfo = useAppSelector(getYearListPageInfo);
  const sortColumn = useAppSelector(getYearSortColumn);
  const sortDirection = useAppSelector(getYearSortDirection);
  const isSubmitting = useAppSelector(getYearSubmitting);

  const { pagenumber, pagesize, totalrecords } = paginationInfo;

  const currentYearListRequest = useMemo(
    () => ({
      pageNumber: pagenumber,
      pageSize: pagesize,
      sortColumn,
      sortDirection,
      filters: {
        academicTime: academicTimeFilter,
        academicStatus: academicStatusFilter,
      },
    }),
    [academicTimeFilter, academicStatusFilter, pagenumber, pagesize, sortColumn, sortDirection]
  );
  const loadYearList = useCallback(
    (request: YearListRequest) => {
      dispatch(fetchYearList(request));
    },
    [dispatch]
  );

  const [drawerOpen, setDrawerOpen] = useState(false);
  const [showFilter, setShowFilter] = useState(false);

  // const [view, setView] = useState<'YearList' | 'CreateYearMultiple'>('YearList');

  const handleAddNewYear = () => {
    setSelectedYearDetail(DefaultYearInfo);
    setDrawerOpen(true);
  };

  const toggleDrawerClose = useCallback(() => setDrawerOpen(false), []);

  const handleSaveorEdit = useCallback(
    async (value: YearListInfo, mode: 'create' | 'edit') => {
      if (mode === 'create') {
        const { academicId, ...rest } = value;
        const response = await dispatch(addNewYear(rest)).unwrap();

        if (response.id > 0) {
          setDrawerOpen(false);
          const successMessage = <SuccessMessage message="Year created successfully" />;
          await confirm(successMessage, 'Year Created', { okLabel: 'Ok', showOnlyOk: true });

          loadYearList({ ...currentYearListRequest, pageNumber: 1, sortColumn: 'academicId', sortDirection: 'desc' });
        }
      } else {
        const response = await dispatch(updateYear(value)).unwrap();
        if (response.rowsAffected > 0) {
          setDrawerOpen(false);
          const successMessage = <SuccessMessage message="Year updated successfully" />;
          await confirm(successMessage, 'Year Updated', { okLabel: 'Ok', showOnlyOk: true });

          loadYearList(currentYearListRequest);
        }
      }
    },
    [confirm, currentYearListRequest, dispatch, loadYearList]
  );
  useEffect(() => {
    if (yearListStatus === 'idle') {
      loadYearList(currentYearListRequest);
    }
  }, [loadYearList, yearListStatus, currentYearListRequest]);

  const handleStatusChange = (e: SelectChangeEvent) => {
    const statusVal = parseInt(e.target.value, 10);
    setYearStatusFilter(statusVal);
    loadYearList({
      ...currentYearListRequest,
      filters: { ...currentYearListRequest.filters, academicStatus: statusVal },
    });
  };

  const getRowKey = useCallback((row: YearListInfo) => row.academicId, []);

  const handlePageChange = useCallback(
    (event: unknown, newPage: number) => {
      loadYearList({ ...currentYearListRequest, pageNumber: newPage + 1 });
    },
    [currentYearListRequest, loadYearList]
  );

  const handleChangeRowsPerPage = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const newPageSize = +event.target.value;
      loadYearList({ ...currentYearListRequest, pageNumber: 1, pageSize: newPageSize });
    },
    [currentYearListRequest, loadYearList]
  );

  const handleEditYear = useCallback((yearObj: YearListInfo) => {
    setSelectedYearDetail(yearObj);
    setDrawerOpen(true);
  }, []);

  const currentItemCount = yearListData.length;

  const handleDeleteYear = useCallback(
    async (yearObj: YearListInfo) => {
      const deleteConfirmMessage = (
        <DeleteMessage
          message={
            <div>
              Are you sure you want to delete the year <br />
              &quot;{yearObj.academicTime}&quot; ?
            </div>
          }
        />
      );
      if (await confirm(deleteConfirmMessage, 'Delete Year?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
        const deleteResponse = await dispatch(deleteYear(yearObj.academicId)).unwrap();
        if (deleteResponse.deleted) {
          const deleteDoneMessage = <DeleteMessage message="Year deleted successfully." />;
          await confirm(deleteDoneMessage, 'Deleted', { okLabel: 'Ok', showOnlyOk: true });
          let pageNumberToMove = pagenumber;
          if (paginationInfo.remainingpages === 0 && pagenumber > 1 && currentItemCount === 1) {
            pageNumberToMove = pagenumber - 1;
          }
          loadYearList({ ...currentYearListRequest, pageNumber: pageNumberToMove });
        }
      }
    },
    [
      confirm,
      currentYearListRequest,
      currentItemCount,
      dispatch,
      loadYearList,
      pagenumber,
      paginationInfo.remainingpages,
    ]
  );

  const handleSort = useCallback(
    (column: string) => {
      const newReq = { ...currentYearListRequest };
      if (column === sortColumn) {
        console.log('Toggling direction');
        const sortDirectionNew = sortDirection === 'asc' ? 'desc' : 'asc';
        newReq.pageNumber = 1;
        newReq.sortDirection = sortDirectionNew;
        dispatch(setSortDirection(sortDirectionNew));
      } else {
        newReq.pageNumber = 1;
        newReq.sortColumn = column;
        dispatch(setSortColumn(column));
      }

      loadYearList(newReq);
    },
    [currentYearListRequest, dispatch, loadYearList, sortColumn, sortDirection]
  );

  const yearListColumns: DataTableColumn<YearListInfo>[] = useMemo(
    () => [
      {
        name: 'academicId',
        headerLabel: 'Year Id',
        dataKey: 'academicId',
        sortable: true,
      },
      {
        name: 'academicTime',
        dataKey: 'academicTime',
        headerLabel: 'Year Name',
        sortable: true,
      },
      {
        name: 'academicDescription',
        dataKey: 'academicDescription',
        headerLabel: 'Year Description',
      },
      {
        name: 'academicStatus',
        headerLabel: 'Year Status',
        sortable: true,
        renderCell: (row) => {
          return (
            <Chip
              size="small"
              label={row.academicStatus === 1 ? 'Published' : 'Unpublished'}
              variant="outlined"
              color={row.academicStatus === 1 ? 'success' : 'error'}
            />
          );
        },
      },
      {
        name: 'actions',
        headerLabel: 'Actions',
        renderCell: (row) => {
          return (
            <Stack direction="row" gap={1}>
              <IconButton size="small" onClick={() => handleEditYear(row)} sx={{ padding: 0 }}>
                <ModeEditIcon />
              </IconButton>
              <IconButton size="small" color="error" sx={{ padding: 0 }} onClick={() => handleDeleteYear(row)}>
                <DeleteIcon />
              </IconButton>
            </Stack>
          );
        },
      },
    ],
    [handleDeleteYear, handleEditYear]
  );

  const pageProps = useMemo(
    () => ({
      rowsPerPageOptions: [10, 20, 30, 50, 100, 200, 300, 500],
      pageNumber: pagenumber - 1,
      pageSize: pagesize,
      totalRecords: totalrecords,
      onPageChange: handlePageChange,
      onRowsPerPageChange: handleChangeRowsPerPage,
    }),
    [handleChangeRowsPerPage, handlePageChange, pagenumber, pagesize, totalrecords]
  );

  const handleReset = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      setYearNameFilter('');
      setYearStatusFilter(-1);
      loadYearList({
        ...currentYearListRequest,
        pageNumber: 1,
        pageSize: 20,
        sortColumn: 'academicId',
        sortDirection: 'asc',
        filters: {
          academicTime: '',
          academicStatus: -1,
        },
      });
    },
    [currentYearListRequest, loadYearList]
  );

  return (
    <Page title="Manage Year">
      <ManageYearRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, pt: { xs: 2, md: 3 } }}>
          <Stack direction="row" pb={1} justifyContent="space-between" flexWrap="wrap">
            <Stack direction="row" alignItems="center" justifyContent="space-between" flex={12} whiteSpace="nowrap">
              <Typography variant="h6" fontSize={17}>
                Manage Year
              </Typography>
              <Tooltip title="Search">
                <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                  <SearchIcon />
                </IconButton>
              </Tooltip>
            </Stack>
            <Box
              display="flex"
              justifyContent="end"
              alignItems="center"
              columnGap={2}
              sx={{
                flexShrink: 0,
                flex: 1,
                '@media (max-width: 340px)': {
                  flexWrap: 'wrap',
                  rowGap: 1,
                },
              }}
            >
              <Button
                sx={{
                  borderRadius: '20px',
                  whiteSpace: 'nowrap',
                  '@media (max-width: 340px)': {
                    width: '100%',
                  },
                }}
                variant="outlined"
                size="small"
                onClick={handleAddNewYear}
              >
                <MdAdd size="20px" /> Add Year
              </Button>
            </Box>
          </Stack>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate onReset={handleReset}>
                <Grid pb={2} pt={1} container spacing={2} alignItems="end">
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 300 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Accademic Year
                      </Typography>
                      <TextField
                        sx={{ minWidth: { xs: '100%', sm: 300 } }}
                        name="academicTime"
                        value={academicTimeFilter}
                        onChange={(e) => {
                          setYearNameFilter(e.target.value);
                          loadYearList({
                            ...currentYearListRequest,
                            filters: { ...currentYearListRequest.filters, academicTime: e.target.value },
                          });
                        }}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 300 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Year Status
                      </Typography>
                      <Select
                        labelId="academicStatusFilter"
                        id="academicStatusFilterSelect"
                        value={academicStatusFilter?.toString() || '-1'}
                        onChange={handleStatusChange}
                      >
                        <MenuItem value={-1}>All</MenuItem>
                        {STATUS_OPTIONS.map((opt) => (
                          <MenuItem key={opt.id} value={opt.id}>
                            {opt.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12} sm={6}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      {/* <Button type="submit" variant="contained" color="primary" fullWidth>
                        Search
                      </Button> */}
                      <Button type="reset" variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>
            <Paper className="card-table-container" sx={{ marginTop: '12px' }}>
              <DataTable
                tableStyles={{ minWidth: { xs: '700px', lg: '1200px', xl: '100% ' } }}
                showHorizontalScroll
                columns={yearListColumns}
                data={yearListData}
                getRowKey={getRowKey}
                fetchStatus="success"
                allowPagination
                allowSorting
                PaginationProps={pageProps}
                sortColumn={sortColumn}
                sortDirection={sortDirection}
                onSort={handleSort}
              />
            </Paper>
          </div>
        </Card>
      </ManageYearRoot>

      <TemporaryDrawer
        onClose={toggleDrawerClose}
        Title="Add New Year"
        state={drawerOpen}
        DrawerContent={
          <CreateEditYearForm
            yearDetail={selectedYearDetail}
            onSave={handleSaveorEdit}
            onCancel={toggleDrawerClose}
            onClose={toggleDrawerClose}
            isSubmitting={isSubmitting}
          />
        }
      />
      {/* <TemporaryDrawer
        onClose={toggleDrawerClose}
        Title={titles}
        state={drawerOpen}
        DrawerContent={<Create open={handleClickOpen} onClose={toggleDrawerClose} />}
      /> */}
    </Page>
  );
}

export default ManageYear;
