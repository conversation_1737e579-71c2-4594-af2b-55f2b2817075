/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import {
  Autocomplete,
  Avatar,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Typography,
  Checkbox,
} from '@mui/material';
import styled from 'styled-components';
import { CLASS_SELECT, YEAR_SELECT } from '@/config/Selection';

import { Students } from '@/config/TableData';

const AddStructureRoot = styled.div`
  width: 100%;
  padding: 0.5rem 1.5rem;
`;

type AddStructureProps = {
  onClick: () => void;
};

function AddStructure({ onClick }: AddStructureProps) {
  return (
    <AddStructureRoot>
      <Box>
        <Stack>
          <Typography variant="h6" fontSize={17} width="80%">
            List
          </Typography>
        </Stack>
        <Divider />
        <Grid pb={4} pt={2} container spacing={3}>
          <Grid item lg={3} xs={12}>
            <Typography variant="h6" fontSize={14}>
              Academic Year
            </Typography>
            <Autocomplete
              options={YEAR_SELECT}
              renderInput={(params) => <TextField {...params} placeholder="Select" />}
            />
          </Grid>
          <Grid item lg={3} xs={12}>
            <Typography variant="h6" fontSize={14}>
              Class
            </Typography>
            <Autocomplete
              options={CLASS_SELECT}
              renderInput={(params) => <TextField {...params} placeholder="Select" />}
            />
          </Grid>
          <Grid item lg={3} xs={12}>
            <Typography variant="h6" fontSize={14}>
              Language
            </Typography>
            <Autocomplete
              options={CLASS_SELECT}
              renderInput={(params) => <TextField {...params} placeholder="Select" />}
            />
          </Grid>

          <Grid item lg={3} xs={12}>
            <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
              <Button variant="contained" color="secondary" fullWidth>
                Reset
              </Button>
              <Button variant="contained" color="primary" fullWidth>
                Students
              </Button>
            </Stack>
          </Grid>
        </Grid>

        <Box>
          <Paper
            sx={{
              border: `1px solid #e8e8e9`,
              width: '100%',
              overflow: 'auto',
              // '&::-webkit-scrollbar': {
              //   width: 0,
              // },
            }}
          >
            <TableContainer
              sx={{
                maxHeight: 300,
                width: { xs: '700px', md: '100%' },
                '&::-webkit-scrollbar': {
                  width: 0,
                },
              }}
            >
              <Table stickyHeader aria-label="simple table">
                <TableHead>
                  <TableRow>
                    <TableCell>
                      <Checkbox />
                    </TableCell>
                    <TableCell>Sl.No</TableCell>
                    <TableCell>Admission No</TableCell>
                    <TableCell>Student Name</TableCell>
                    <TableCell>Class</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {Students.map((lnrow) => (
                    <TableRow hover key={lnrow.rollNo}>
                      <TableCell width={20}>
                        <Checkbox />
                      </TableCell>
                      <TableCell>{lnrow.rollNo}</TableCell>
                      <TableCell>45678</TableCell>
                      <TableCell>
                        <Stack direction="row">
                          <Avatar src={lnrow.image} sx={{ mr: 2 }} />
                          <Typography pt={0.7} fontSize={15}>
                            {lnrow.name}
                          </Typography>
                        </Stack>
                      </TableCell>
                      <TableCell>VII-A</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        </Box>
        <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 2 }}>
          <Stack>
            <Button fullWidth variant="contained" color="primary" onClick={onClick}>
              Map Language
            </Button>
          </Stack>
        </Box>
      </Box>
    </AddStructureRoot>
  );
}

export default AddStructure;
