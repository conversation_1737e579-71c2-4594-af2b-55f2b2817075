import { TableBody, Box, Paper, Table, TableContainer, TableHead, TableRow, TableCell } from '@mui/material';
import React from 'react';

export const MappedList = () => {
  return (
    <Box mt={3}>
      <Paper
        sx={{
          border: `1px solid #e8e8e9`,
          width: '100%',
          overflow: 'auto',
          // '&::-webkit-scrollbar': {
          //   width: 0,
          // },
        }}
      >
        <TableContainer
          sx={{
            maxHeight: 300,
            width: { xs: '700px', md: '100%' },
            '&::-webkit-scrollbar': {
              width: 0,
            },
          }}
        >
          <Table stickyHeader aria-label="simple table">
            <TableHead>
              <TableRow>
                <TableCell>Admission No</TableCell>
                <TableCell>Student Name</TableCell>
                <TableCell>Status</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              <TableRow hover>
                <TableCell>45678</TableCell>
                <TableCell>Fionna Grand</TableCell>
                <TableCell>
                  <Paper
                    sx={{
                      border: '1px solid green',
                      display: 'flex',
                      justifyContent: 'center',
                      borderRadius: '20px',
                      p: 1,
                      maxWidth: 85,
                      backgroundColor: '#E9FCD4',
                      color: 'green',
                    }}
                  >
                    Passed
                  </Paper>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>
    </Box>
  );
};
