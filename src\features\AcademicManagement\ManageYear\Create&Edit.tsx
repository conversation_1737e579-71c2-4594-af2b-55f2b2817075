import { STATUS_OPTIONS } from '@/config/Selection';
import { Button, TextField, Typography, Box, Stack, FormControl, Select, MenuItem } from '@mui/material';
import { YearListInfo } from '@/types/AcademicManagement';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import LoadingButton from '@mui/lab/LoadingButton/LoadingButton';

export type CreateEditYearFormProps = {
  onSave: (values: YearListInfo, mode: 'create' | 'edit') => void;
  onCancel: (mode: 'create' | 'edit') => void;
  onClose: (mode: 'create' | 'edit') => void;
  yearDetail: YearListInfo;
  isSubmitting: boolean;
};

const CreateEditYearValidationSchema = Yup.object({
  academicTime: Yup.string().required('Please enter Year'),
  academicDescription: Yup.string().required('Please enter Year description'),
  academicStatus: Yup.number().oneOf([0, 1], 'Please select Year Status'),
});

function CreateEditYearForm({ yearDetail, onSave, onCancel, isSubmitting }: CreateEditYearFormProps) {
  const mode = yearDetail.academicId === 0 ? 'create' : 'edit';
  const {
    values: { academicTime, academicDescription, academicStatus },
    handleChange,
    handleSubmit,
    touched,
    errors,
  } = useFormik<YearListInfo>({
    initialValues: {
      academicId: yearDetail.academicId,
      academicTime: yearDetail.academicTime,
      academicDescription: yearDetail.academicDescription,
      academicStatus: yearDetail.academicStatus,
    },
    validationSchema: CreateEditYearValidationSchema,
    onSubmit: (values) => {
      onSave(values, mode);
    },
  });
  return (
    <Box mt={3}>
      <form noValidate onSubmit={handleSubmit}>
        <Stack sx={{ height: 'calc(100vh - 150px)' }} direction="column">
          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Academic Year
            </Typography>
            <TextField
              placeholder="year Name"
              name="academicTime"
              value={academicTime}
              onChange={handleChange}
              error={touched.academicTime && !!errors.academicTime}
              helperText={errors.academicTime}
              disabled={isSubmitting}
            />
          </FormControl>
          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Description
            </Typography>
            <TextField
              multiline
              fullWidth
              minRows={2}
              InputProps={{ inputProps: { style: { resize: 'vertical' } } }}
              placeholder="Enter description..."
              name="academicDescription"
              value={academicDescription}
              onChange={handleChange}
              error={touched.academicDescription && !!errors.academicDescription}
              helperText={errors.academicDescription}
              disabled={isSubmitting}
            />
          </FormControl>
          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Status
            </Typography>
            <Select
              name="academicStatus"
              value={academicStatus}
              onChange={handleChange}
              error={touched.academicStatus && !!errors.academicStatus}
              disabled={isSubmitting}
            >
              {STATUS_OPTIONS.map((opt) => (
                <MenuItem key={opt.id} value={opt.id}>
                  {opt.name}
                </MenuItem>
              ))}
            </Select>
            {touched.academicStatus && !!errors.academicStatus && (
              <Typography color="red" fontSize="0.625rem" variant="subtitle1">
                {errors.academicStatus}
              </Typography>
            )}
          </FormControl>
        </Stack>
        <Box sx={{ justifyContent: 'center', pt: 3, position: 'relative' }}>
          <Stack spacing={2} direction="row" sx={{}}>
            <Button onClick={() => onCancel(mode)} fullWidth variant="contained" color="secondary" type="button">
              Cancel
            </Button>
            <LoadingButton
              loading={isSubmitting}
              fullWidth
              variant="contained"
              color="primary"
              type="submit"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Saving...' : 'Save'}
            </LoadingButton>
          </Stack>
        </Box>
      </form>
    </Box>
  );
}
export default CreateEditYearForm;
