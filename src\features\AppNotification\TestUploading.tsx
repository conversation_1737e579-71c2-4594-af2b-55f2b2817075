import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>, Card, LinearProgress, Stack, Typography } from '@mui/material';
import { useTheme } from 'styled-components';
import Files from '@/assets/NotificationIcons/docsFile.png';
import useSettings from '@/hooks/useSettings';

const MultipleFileUpload = () => {
  const theme = useTheme();
  const isLight = useSettings().themeMode === 'light';
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [progressBars, setProgressBars] = useState([]);

  const handleFileChange = (event) => {
    const filesArray = Array.from(event.target.files);
    setSelectedFiles([...selectedFiles, ...filesArray]);
    setProgressBars([...progressBars, ...filesArray.map(() => 0)]);
  };

  const uploadFile = async (file, index) => {
    const formData = new FormData();
    formData.append('file', file);

    // Simulate file upload progress
    let loaded = 0;
    let total = 0;

    // Wrap XHR request in a Promise
    const xhrPromise = new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();
      xhr.open('POST', '/upload', true);
      xhr.upload.onprogress = (event) => {
        if (event.lengthComputable) {
          loaded = event.loaded;
          total = event.total;
          const percentage = Math.round((loaded / total) * 100);
          const newProgressBars = [...progressBars];
          newProgressBars[index] = percentage;
          setProgressBars(newProgressBars);
        }
      };
      xhr.onload = () => {
        if (xhr.status === 200) {
          resolve(file);
        } else {
          reject(new Error('Upload failed'));
        }
      };
      xhr.send(formData);
    });

    // Wait for the upload to finish
    try {
      await xhrPromise;
      return file;
    } catch (error) {
      console.error('Upload failed:', error);
      return null;
    }
  };

  const uploadFiles = async () => {
    const uploadedFiles = await Promise.all(selectedFiles.map((file, index) => uploadFile(file, index)));
    const filteredFiles = uploadedFiles.filter((file) => file !== null);
    if (filteredFiles.length === selectedFiles.length) {
      alert('All files uploaded successfully!');
      setSelectedFiles([]);
      setProgressBars([]);
    }
  };

  return (
    <div className="file-upload-container">
      <input
        accept="/*"
        id="contained-button-file"
        multiple
        type="file"
        onChange={handleFileChange}
        style={{ display: 'none' }}
      />
      <label htmlFor="contained-button-file">
        <Button variant="contained" color="primary" component="span">
          Choose Files
        </Button>
      </label>
      <Button variant="contained" color="primary" disabled={selectedFiles.length === 0} onClick={uploadFiles}>
        Upload
      </Button>
      {selectedFiles.map((file, index) => (
        <Card
          sx={{ mt: 3, px: 3, py: 1, backgroundColor: isLight ? theme.palette.common.white : theme.palette.grey[900] }}
          key={index}
        >
          <Box display="flex" alignItems="center">
            <img width="30px" src={Files} alt="" />
            <Typography variant="subtitle2">{file.name}</Typography>
            <Stack width="100%">
              <LinearProgress variant="determinate" value={progressBars[index]} />
            </Stack>
            <Stack direction="row" alignContent="end">
              <Typography variant="subtitle1">{progressBars[index]}%</Typography>
            </Stack>
          </Box>
        </Card>
      ))}
    </div>
  );
};

export default MultipleFileUpload;
