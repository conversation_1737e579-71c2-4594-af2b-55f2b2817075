import styled from 'styled-components';
import { Card, CardContent, Typography, CardActionArea, Grid, Box } from '@mui/material';
import { Link } from 'react-router-dom';
import { ParentLinkCard2Data } from '@/config/LinkCard';
import useSettings from '@/hooks/useSettings';
import { useTheme } from '@mui/material';

const LinkCard2Root = styled.div`
  padding: 1rem 0.5rem 1rem 0.5rem;
  /* margin-top: 1rem; */
  /* margin-bottom: 1rem; */
  .cards {
    transition: 0.5s;
    background-color: ;
  }
  .icon {
    transition: 0.5s;
  }
  .cards:hover {
    transform: scale(1.03);
  }
  .icon:hover {
    transform: scale(1.1);
  }
  .content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
  .icon-circle {
    border-radius: 100%;
  }

  a {
    text-decoration: none;
    color: black;
  }
`;

function LinkCard2() {
  // const theme = useTheme();
  const theme = useTheme();
  const isLight = useSettings().themeMode === 'light';

  return (
    <LinkCard2Root className="LinkCard2">
      <Box sx={{ width: '100%' }}>
        <Grid container spacing={{ xxl: 15, xl: 10, lg: 2, md: 4, sm: 5, xs: 5 }}>
          {ParentLinkCard2Data?.map((item) => (
            <Grid item xxl={3} xl={3} lg={3} md={3} sm={3} xs={6} key={item.id}>
              <Link to={`${item.link}`}>
                <Card
                  className="cards"
                  sx={{
                    boxShadow: 0,
                    border: 1,
                    borderColor: isLight ? theme.palette.grey[300] : theme.palette.grey[900],
                    backgroundColor: isLight ? theme.palette.common.white : theme.palette.grey[900],
                  }}
                >
                  <CardActionArea>
                    <CardContent className="content">
                      <div className="icon-circle p-2 mb-1">
                        <img src={item.icon} className="icon" alt="" />
                      </div>
                      <Typography
                        variant="subtitle2"
                        sx={{
                          fontFamily: 'Poppins Regular',
                          fontSize: { xxl: '13px', xl: '12px', lg: '10px', md: '10px', sm: '11px' },
                          fontWeight: '800',
                        }}
                      >
                        {item.label}
                      </Typography>
                      <Typography
                        variant="subtitle2"
                        sx={{
                          fontFamily: 'Poppins Regular',
                          fontSize: '13px',
                          fontWeight: '800',
                        }}
                      >
                        {item.count}
                      </Typography>
                    </CardContent>
                  </CardActionArea>
                </Card>
              </Link>
            </Grid>
          ))}
        </Grid>
      </Box>
    </LinkCard2Root>
  );
}

export default LinkCard2;
