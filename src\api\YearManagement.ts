import ApiUrls from '@/config/ApiUrls';
import { YearCreateRequest, YearListInfo, YearListPagedData, YearListRequest } from '@/types/AcademicManagement';
import { CreateResponse, DeleteResponse, UpdateResponse } from '@/types/Common';
import { privateApi } from '@/api/base/api';
import { APIResponse } from '@/api/base/types';

async function GetYearList(request: YearListRequest): Promise<APIResponse<YearListPagedData>> {
  const response = await privateApi.post<YearListPagedData>(ApiUrls.GetYearList, request);
  return response;
}

async function YearList(academicId: number | undefined): Promise<APIResponse<YearListPagedData[]>> {
  if (academicId === undefined) {
    throw new Error('academicId is required');
  }

  const url = ApiUrls.GetYear.replace('{academicId}', academicId.toString());
  const response = await privateApi.get<any>(url);
  return response;
}

async function AddNewYear(request: YearCreateRequest): Promise<APIResponse<CreateResponse>> {
  const response = await privateApi.post<CreateResponse>(ApiUrls.AddYear, request);
  return response;
}

async function UpdateYear(request: YearListInfo): Promise<APIResponse<UpdateResponse>> {
  const response = await privateApi.post<UpdateResponse>(ApiUrls.UpdateYear, request);
  return response;
}

async function DeleteYear(academicId: number): Promise<APIResponse<DeleteResponse>> {
  const url = ApiUrls.DeleteYear.replace('{academicId}', academicId.toString());
  const response = await privateApi.get<DeleteResponse>(url);
  return response;
}

async function YearNameExists(academicTime: string): Promise<APIResponse<boolean>> {
  const url = `${ApiUrls.YearNameExists}?q=${academicTime}`;
  const response = await privateApi.get<boolean>(url);
  return response;
}

const methods = {
  GetYearList,
  AddNewYear,
  UpdateYear,
  DeleteYear,
  YearNameExists,
  YearList,
};

export default methods;
