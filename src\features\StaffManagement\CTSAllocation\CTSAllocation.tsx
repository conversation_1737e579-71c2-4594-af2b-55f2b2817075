/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Divider,
  Grid,
  Paper,
  Stack,
  Button,
  Typography,
  Card,
  IconButton,
  Collapse,
  FormControl,
  MenuItem,
  Tooltip,
  Select,
  useTheme,
  SelectChangeEvent,
} from '@mui/material';
import styled from 'styled-components';
import { MdAdd } from 'react-icons/md';
import DeleteIcon from '@mui/icons-material/Delete';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import SearchIcon from '@mui/icons-material/Search';
import { IoIosArrowUp } from 'react-icons/io';
import ErrorIcon from '@/assets/ManageFee/ErrorIcon.json';
import Success from '@/assets/ManageFee/Success.json';
import deleteBin from '@/assets/ManageFee/deleteBin.json';
import deleteSuccess from '@/assets/ManageFee/deleteSuccess.json';
import errorIcon from '@/assets/ManageFee/Error.json';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import useSettings from '@/hooks/useSettings';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import useAuth from '@/hooks/useAuth';
import { useAppSelector } from '@/hooks/useAppSelector';
import {
  getCTSClassList,
  getCTSDetailsListData,
  getCTSDetailsListPageInfo,
  getCTSDetailsListStatus,
  getCTSFilterListData,
  getCTSFilterListStatus,
  getCTSSortColumn,
  getCTSSortDirection,
  getCTSStaffList,
  getCTSSubjectList,
  getCTSYearList,
  getStaffSubmitting,
} from '@/config/storeSelectors';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import {
  createCTSAllocationSingle,
  deleteCTS,
  fetchCTSFilter,
  fetchCTSList,
  fetchCTSUpdateDetail,
  updateCTSAllocationSingle,
} from '@/store/StaffMangement/StaffMangement/staffMangement.thunks';
import {
  CTSAllocationCreateUpdateInfo,
  CTSDetailsInfo,
  CTSDetailsListRequest,
  DeleteCTSAllocationRequest,
} from '@/types/StaffManagement';
import { setCTSSortColumn, setCTSSortDirection } from '@/store/StaffMangement/StaffMangement/staffMangement.slice';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import { ErrorMessage } from '@/components/shared/Popup/ErrorMessage';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import { useNavigate } from 'react-router-dom';
import { CreateEditCTSForm } from './CreateEditCTSForm';
import CtsAllocationClassWise from '../CTSAllocationCW';
import CtsAllocationTeacherWise from '../CTSAllocationTW';
import { Box } from '@mui/material';

const CtsAllocationRoot = styled.div`
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 576px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 50px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid ${(props) => props.theme.palette.grey[200]};
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
        /* .MuiTableCell-root:first-child {
          padding: 5px;
        } */
      }
    }
  }
`;

const DefaultCTSInfo = {
  adminId: 0,
  academicId: 0,
  classId: 0,
  subjectId: 0,
  staffId: 0,
  staffRole: -1,
  cteacherId: 0,
};

function CtsAllocation() {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const dispatch = useAppDispatch();
  const { confirm } = useConfirm();
  const { user } = useAuth();
  const adminId: number = user ? user.accountId : 0;
  // const YearData = useAppSelector(getYearData);
  const CTSFilterListData = useAppSelector(getCTSFilterListData);
  const CTSFilterListStatus = useAppSelector(getCTSFilterListStatus);
  const CTSDetailsListStatus = useAppSelector(getCTSDetailsListStatus);
  const CTSDetailsListData = useAppSelector(getCTSDetailsListData);
  const sortColumn = useAppSelector(getCTSSortColumn);
  const sortDirection = useAppSelector(getCTSSortDirection);
  const paginationInfo = useAppSelector(getCTSDetailsListPageInfo);
  const CTSYearList = useAppSelector(getCTSYearList);
  const CTSClassList = useAppSelector(getCTSClassList);
  const CTSSubjectList = useAppSelector(getCTSSubjectList);
  const CTSStaffList = useAppSelector(getCTSStaffList);
  const isSubmitting = useAppSelector(getStaffSubmitting);

  const { pagenumber, pagesize, totalrecords } = paginationInfo;

  const [showFilter, setShowFilter] = useState(false);
  const [drawerOpen, setDrawerOpen] = React.useState<boolean>(false);
  const [selectedCTSListDetail, setSelectedCTSListDetail] = useState<any>(DefaultCTSInfo);
  const [academicYearFilter, setAcademicYearFilter] = useState(-1);
  const [classFilter, setClassFilter] = useState(-1);
  const [staffFilter, setStaffFilter] = useState(-1);
  const [subjectFilter, setSubjectFilter] = useState(-1);
  const [ctsPages, setCtsPages] = useState<'CTSAllocation' | 'CTSAllocationCW' | 'CTSAllocationTW'>('CTSAllocation');
  const [selectedRows, setSelectedRows] = useState<CTSDetailsInfo[]>([]);

  const navigate = useNavigate();

  const currentCTSListRequest: CTSDetailsListRequest = React.useMemo(
    () => ({
      pageNumber: pagenumber,
      pageSize: pagesize,
      sortColumn,
      sortDirection,
      filters: {
        adminId,
        academicId: academicYearFilter,
        classId: classFilter,
        staffId: staffFilter,
        subjectId: subjectFilter,
      },
    }),
    [
      adminId,
      pagenumber,
      pagesize,
      sortColumn,
      academicYearFilter,
      classFilter,
      subjectFilter,
      staffFilter,
      sortDirection,
    ]
  );

  const loadCTSList = React.useCallback(
    async (request: CTSDetailsListRequest) => {
      try {
        const data = await dispatch(fetchCTSList(request));
        console.log('data', data);
      } catch (error) {
        console.error('Error loading list:', error);
      }
    },
    [dispatch]
  );

  React.useEffect(() => {
    // if (YearStatus === 'idle') {
    //   setAcademicYearFilter(defualtYear);
    // }
    if (CTSDetailsListStatus === 'idle') {
      loadCTSList(currentCTSListRequest);
    }
    if (CTSFilterListStatus === 'idle') {
      const CtsFilerDatas = dispatch(fetchCTSFilter(adminId));
      console.log('CtsFilerDatas', CtsFilerDatas);
    }
  }, [
    dispatch,
    adminId,
    CTSFilterListStatus,
    CTSDetailsListStatus,
    CTSFilterListData,
    loadCTSList,
    currentCTSListRequest,
  ]);

  const handleYearChange = (e: SelectChangeEvent) => {
    const selectedAcademicId = e.target.value;
    setAcademicYearFilter(parseInt(selectedAcademicId, 10));

    loadCTSList({
      ...currentCTSListRequest,
      filters: { ...currentCTSListRequest.filters, academicId: parseInt(selectedAcademicId, 10) },
    });
  };

  const handleClassChange = (e: SelectChangeEvent) => {
    const selectedClass = parseInt(e.target.value, 10);
    if (selectedClass) {
      setClassFilter(selectedClass);
    }
    loadCTSList({
      ...currentCTSListRequest,
      filters: { ...currentCTSListRequest.filters, classId: selectedClass || -1 },
    });
  };

  const handleStaffChange = (e: SelectChangeEvent) => {
    const selectedStaff = parseInt(e.target.value, 10);
    if (selectedStaff) {
      setStaffFilter(selectedStaff);
    }
    loadCTSList({
      ...currentCTSListRequest,
      filters: { ...currentCTSListRequest.filters, staffId: selectedStaff || -1 },
    });
  };

  const handleSubjectChange = (e: SelectChangeEvent) => {
    const selectedSubject = parseInt(e.target.value, 10);
    if (selectedSubject) {
      setSubjectFilter(selectedSubject);
    }
    loadCTSList({
      ...currentCTSListRequest,
      filters: { ...currentCTSListRequest.filters, subjectId: selectedSubject || -1 },
    });
  };

  const toggleDrawerOpen = () => {
    setDrawerOpen(true);
  };

  const toggleDrawerClose = () => setDrawerOpen(false);

  const handleAddCTSAllocationList = () => {
    setSelectedCTSListDetail(DefaultCTSInfo);
    setDrawerOpen(true);
  };

  const handleEditCTSAllocationList = useCallback(
    async (ctsObj: CTSDetailsInfo) => {
      const { cteacherId } = ctsObj;
      try {
        const data = await dispatch(fetchCTSUpdateDetail({ adminId, cteacherId })).unwrap();
        if (data) {
          setSelectedCTSListDetail(data); // Use the returned data
          setDrawerOpen(true);
        }
      } catch (error) {
        console.error('Error fetching CTS Update Detail:', error);
      }
    },
    [dispatch, adminId]
  );

  const handleSaveOrEdit = useCallback(
    async (values: CTSAllocationCreateUpdateInfo, mode: 'create' | 'edit') => {
      try {
        console.log('values::::----', values);
        if (mode === 'create') {
          console.log('values::::----', values);
          const response = await dispatch(createCTSAllocationSingle(values)).unwrap();
          console.log('response::::----', response);
          if (response.result === 'Success') {
            toggleDrawerClose();
            loadCTSList({ ...currentCTSListRequest, pageNumber: 1, sortColumn: 'classId', sortDirection: 'desc' });
            const successMessage = (
              <SuccessMessage icon="" loop={false} jsonIcon={Success} message="CTS create successfully" />
            );
            await confirm(successMessage, '', { okLabel: 'Ok', showOnlyOk: true });
          } else if (response.result === 'Exist') {
            toggleDrawerClose();
            const errorMessage = <ErrorMessage icon="" jsonIcon={ErrorIcon} message="CTS already created" />;
            await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
            toggleDrawerOpen();
          } else if (response.result === 'Failed') {
            const errorMessage = <ErrorMessage icon="" jsonIcon={ErrorIcon} message="CTS create failed" />;
            await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
          } else {
            const errorMessage = <ErrorMessage icon="" jsonIcon={ErrorIcon} message="CTS create failed" />;
            await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
          }
        } else {
          const response = await dispatch(updateCTSAllocationSingle(values)).unwrap();
          console.log('values::::----', values);

          if (response) {
            setDrawerOpen(false);
            const successMessage = (
              <SuccessMessage jsonIcon={Success} loop={false} message="CTS updated successfully" />
            );
            await confirm(successMessage, 'CTS Updated', { okLabel: 'Ok', showOnlyOk: true });

            loadCTSList({ ...currentCTSListRequest });
          } else {
            const errorMessage = <ErrorMessage loop={false} jsonIcon={ErrorIcon} message="CTS update failed" />;
            await confirm(errorMessage, 'CTS Update', { okLabel: 'Ok', showOnlyOk: true });
          }
        }
      } catch {
        const errorMessage = (
          <ErrorMessage loop={false} jsonIcon={ErrorIcon} message="Something went wrong please try again " />
        );
        await confirm(errorMessage, 'Fee Title Update', { okLabel: 'Ok', showOnlyOk: true });
      }
    },
    [dispatch, confirm, loadCTSList, currentCTSListRequest]
  );

  const handleDeleteCtsList = useCallback(
    async (ctsObj: CTSAllocationCreateUpdateInfo) => {
      const { cteacherId } = ctsObj;
      const deleteConfirmMessage = (
        <DeleteMessage jsonIcon={deleteBin} message={<div>Are you sure you want to delete the CTS Row ?</div>} />
      );
      if (await confirm(deleteConfirmMessage, 'Delete Row?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
        const sendReq = [{ adminId, cteacherId, dbResult: 'string', id: 0 }];

        console.log('sendReq', sendReq);

        const deleteResponse = await dispatch(deleteCTS(sendReq));
        console.log('deleteResponse::::----', deleteResponse);
        if (deleteResponse && Array.isArray(deleteResponse.payload)) {
          const results: DeleteCTSAllocationRequest[] = deleteResponse.payload;
          const errorMessages = results.find((result) => result.dbResult === 'Failed');
          const successMessages = results.find((result) => result.dbResult === 'Success');
          // Reload only the specific message template with the deleted messageId

          if (!errorMessages) {
            loadCTSList({ ...currentCTSListRequest });
            const deleteSuccessMessage = (
              <SuccessMessage loop={false} jsonIcon={deleteSuccess} message="Delete successfully" />
            );
            await confirm(deleteSuccessMessage, 'Success', { okLabel: 'Ok', showOnlyOk: true });
          } else if (!successMessages) {
            const deleteErrorMessage = <DeleteMessage jsonIcon={errorIcon} message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          } else {
            const deleteErrorMessage = <DeleteMessage jsonIcon={errorIcon} message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          }

          // setSnackBar(true);
          // setBasicFeeData((prevDetails) => prevDetails.filter((item) => item.feeId !== feeId));
        }
      }
    },
    [dispatch, adminId, confirm, loadCTSList, currentCTSListRequest]
  );

  const handleDeleteMultiple = useCallback(async () => {
    const sendConfirmMessage = (
      <DeleteMessage jsonIcon={deleteBin} message={<div>Are you sure you want to delete all ?</div>} />
    );
    if (await confirm(sendConfirmMessage, 'Delete Basic Fee?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
      const sendRequests: DeleteCTSAllocationRequest[] = await Promise.all(
        selectedRows?.map(async (row) => {
          const sendReq = {
            adminId,
            cteacherId: row.cteacherId,
            dbResult: '',
            id: 0,
          };
          return sendReq;
        }) || []
      );
      const deleteResponse = await dispatch(deleteCTS(sendRequests));

      console.log('deleteResponse', deleteResponse);
      if (deleteResponse && Array.isArray(deleteResponse.payload)) {
        const results: DeleteCTSAllocationRequest[] = deleteResponse.payload;
        const errorMessages = results.find((result) => result.dbResult === 'Failed');
        const successMessages = results.find((result) => result.dbResult === 'Success');

        if (!errorMessages) {
          loadCTSList({ ...currentCTSListRequest });
          const deleteSuccessMessage = (
            <SuccessMessage loop={false} jsonIcon={deleteSuccess} message="Delete All Successfully" />
          );
          await confirm(deleteSuccessMessage, 'Success', { okLabel: 'Ok', showOnlyOk: true });
        } else if (!successMessages) {
          const deleteErrorMessage = <DeleteMessage loop={false} jsonIcon={errorIcon} message="Delete Failed" />;
          await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
        } else {
          const deleteErrorMessage = (
            <DeleteMessage loop={false} jsonIcon={errorIcon} message="Something went wrong please try again" />
          );
          await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
        }
        setSelectedRows([]);
        // setBasicFeeData((prevDetails) =>
        //   prevDetails.filter((item: CreateBasicFeeSettingTitleDataType) => !selectedRows.includes(item.feeId))
        // );
      }
    }
  }, [confirm, dispatch, selectedRows, adminId, loadCTSList, currentCTSListRequest]);

  const handleSort = useCallback(
    (column: string) => {
      const newReq = { ...currentCTSListRequest };
      if (column === sortColumn) {
        console.log('Toggling direction');
        const sortDirectionNew = sortDirection === 'asc' ? 'desc' : 'asc';
        newReq.pageNumber = 1;
        newReq.sortDirection = sortDirectionNew;
        dispatch(setCTSSortDirection(sortDirectionNew));
      } else {
        newReq.pageNumber = 1;
        newReq.sortColumn = column;
        dispatch(setCTSSortColumn(column));
      }

      loadCTSList(newReq);
    },
    [currentCTSListRequest, dispatch, loadCTSList, sortColumn, sortDirection]
  );
  const handlePageChange = useCallback(
    (event: unknown, newPage: number) => {
      loadCTSList({ ...currentCTSListRequest, pageNumber: newPage + 1 });
    },
    [currentCTSListRequest, loadCTSList]
  );

  const handleChangeRowsPerPage = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const newPageSize = +event.target.value;
      loadCTSList({ ...currentCTSListRequest, pageNumber: 1, pageSize: newPageSize });
    },
    [currentCTSListRequest, loadCTSList]
  );

  const pageProps = useMemo(
    () => ({
      rowsPerPageOptions: [10, 20, 30, 50, 100, 200, 300, 500],
      pageNumber: pagenumber - 1,
      pageSize: pagesize,
      totalRecords: totalrecords,
      onPageChange: handlePageChange,
      onRowsPerPageChange: handleChangeRowsPerPage,
    }),
    [handleChangeRowsPerPage, handlePageChange, pagenumber, pagesize, totalrecords]
  );
  const handleReset = React.useCallback(
    (e: any) => {
      e.preventDefault();
      setAcademicYearFilter(-1);
      setClassFilter(-1);
      setSubjectFilter(-1);
      setStaffFilter(-1);
      loadCTSList({
        pageNumber: 1,
        pageSize: 20,
        sortColumn: 'classId',
        sortDirection: 'asc',
        filters: {
          adminId,
          academicId: -1,
          classId: -1,
          staffId: -1,
          subjectId: -1,
        },
      });
    },
    [loadCTSList, adminId]
  );

  const CTSListReportColumns: DataTableColumn<CTSDetailsInfo>[] = useMemo(
    () => [
      {
        name: 'cteacherId',
        headerLabel: 'Sl.No',
        dataKey: 'cteacherId',
      },
      {
        name: 'className',
        headerLabel: 'Class',
        dataKey: 'className',
        sortable: true,
      },
      {
        name: 'staffName',
        headerLabel: 'Staff',
        dataKey: 'staffName',
      },
      {
        name: 'subjectName',
        headerLabel: 'Subject',
        dataKey: 'subjectName',
      },
      {
        name: 'staffRole',
        headerLabel: 'Role',
        renderCell: (row) => {
          return (
            <Stack direction="row" gap={1} alignItems="center">
              <Typography variant="subtitle2">{row.staffRole === 0 ? 'Teacher' : 'Class Teacher'}</Typography>
            </Stack>
          );
        },
      },
      {
        name: 'actions',
        headerLabel: 'Actions',
        renderCell: (row) => {
          return (
            <Stack direction="row" gap={1}>
              <IconButton size="small" sx={{ padding: 0.5 }} onClick={() => handleEditCTSAllocationList(row)}>
                <ModeEditIcon />
              </IconButton>
              <IconButton size="small" color="error" sx={{ padding: 0.5 }} onClick={() => handleDeleteCtsList(row)}>
                <DeleteIcon />
              </IconButton>
            </Stack>
          );
        },
      },
    ],
    [handleEditCTSAllocationList, handleDeleteCtsList]
  );
  const getRowKey = useCallback((row: CTSDetailsInfo) => row.cteacherId, []);

  return ctsPages === 'CTSAllocationCW' ? (
    <CtsAllocationClassWise onBackClick={() => setCtsPages('CTSAllocation')} />
  ) : ctsPages === 'CTSAllocationTW' ? (
    <CtsAllocationTeacherWise onBackClick={() => setCtsPages('CTSAllocation')} />
  ) : (
    <Page title="CTS List">
      <CtsAllocationRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack
            direction="row"
            justifyContent="space-between"
            pb={1}
            alignItems="center"
            flexWrap="wrap"
            sx={{ '@media (max-width: 500px)': { flexDirection: 'column', alignItems: 'start' } }}
          >
            <Stack
              direction="row"
              alignItems="center"
              justifyContent="space-between"
              flex={3}
              width="100%"
              whiteSpace="nowrap"
            >
              <Typography variant="h6" fontSize={17}>
                CTS Allocation
              </Typography>
              {showFilter === false ? (
                <Tooltip title="Search">
                  <IconButton
                    aria-label="delete"
                    color="primary"
                    sx={{ mr: 1 }}
                    onClick={() => setShowFilter((x) => !x)}
                  >
                    <SearchIcon />
                  </IconButton>
                </Tooltip>
              ) : (
                <IconButton aria-label="delete" color="primary" sx={{ mr: 1 }} onClick={() => setShowFilter((x) => !x)}>
                  <IoIosArrowUp />
                </IconButton>
              )}
            </Stack>
            <Box
              display="flex"
              columnGap={2}
              justifyContent="end"
              alignItems="center"
              sx={{
                flexShrink: 0,
                flex: 1,
                '@media (max-width: 500px)': {
                  flexWrap: 'wrap',
                  rowGap: 1,
                },
              }}
            >
              {selectedRows.length > 0 && (
                <Tooltip title="Delete All">
                  <IconButton
                    sx={{ visibility: { xs: 'hidden', sm: 'visible' }, mr: 1 }}
                    aria-label="delete"
                    color="error"
                    onClick={handleDeleteMultiple}
                  >
                    <DeleteIcon />
                  </IconButton>
                </Tooltip>
              )}
              <Button
                sx={{
                  borderRadius: '20px',
                  whiteSpace: 'nowrap',
                  '@media (max-width: 500px)': {
                    width: '100%',
                  },
                }}
                variant="outlined"
                size="small"
                onClick={handleAddCTSAllocationList}
              >
                <MdAdd size="20px" /> Create
              </Button>
              <Button
                onClick={() => {
                  setCtsPages('CTSAllocationCW');
                  navigate('/staff-management/class-wise-allocation');
                }}
                sx={{
                  borderRadius: '20px',
                  whiteSpace: 'nowrap',
                  '@media (max-width: 500px)': {
                    width: '100%',
                  },
                }}
                size="small"
                variant="outlined"
              >
                <MdAdd size="20px" />
                CTS Allocation Class Wise
              </Button>
              <Button
                onClick={() => {
                  setCtsPages('CTSAllocationTW');
                  navigate('/staff-management/teacher-wise-allocation');
                }}
                sx={{
                  borderRadius: '20px',
                  whiteSpace: 'nowrap',
                  '@media (max-width: 500px)': {
                    width: '100%',
                  },
                }}
                size="small"
                variant="outlined"
              >
                <MdAdd size="20px" /> CTS Allocation Teacher Wise
              </Button>
            </Box>
          </Stack>

          <Stack>
            <Divider />
          </Stack>
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={5} pt={1} container spacing={2}>
                  <Grid item xxl={2} xl={2.5} lg={2.5} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Select
                        labelId="academicYearFilter"
                        id="academicYearFilterSelect"
                        value={academicYearFilter?.toString()}
                        onChange={handleYearChange}
                        placeholder="Select Year"
                      >
                        <MenuItem value={-1}>Select All</MenuItem>
                        {CTSYearList.map((opt) => (
                          <MenuItem key={opt.academicId} value={opt.academicId}>
                            {opt.academicTime}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={2.5} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Select Class
                      </Typography>
                      <Select
                        labelId="classFilter"
                        id="classFilterSelect"
                        value={classFilter?.toString()}
                        onChange={handleClassChange}
                        placeholder="Select Class"
                        MenuProps={{
                          PaperProps: {
                            style: {
                              maxHeight: '250px', // Adjust the value to your desired height
                            },
                          },
                        }}
                      >
                        <MenuItem value={-1}>Select All</MenuItem>
                        {CTSClassList.map((opt) => (
                          <MenuItem key={opt.classId} value={opt.classId}>
                            {opt.className}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={2.5} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Select Teacher
                      </Typography>
                      <Select
                        labelId="staffFilter"
                        id="staffFilterSelect"
                        value={staffFilter?.toString()}
                        onChange={handleStaffChange}
                        placeholder="Select Class"
                        MenuProps={{
                          PaperProps: {
                            style: {
                              maxHeight: '250px', // Adjust the value to your desired height
                            },
                          },
                        }}
                      >
                        <MenuItem value={-1}>Select All</MenuItem>
                        {CTSStaffList.map((opt) => (
                          <MenuItem key={opt.staffId} value={opt.staffId}>
                            {opt.staffName}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={2.5} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Select Subject
                      </Typography>
                      <Select
                        labelId="subjectFilter"
                        id="subjectFilterSelect"
                        value={subjectFilter?.toString()}
                        onChange={handleSubjectChange}
                        placeholder="Select Class"
                        MenuProps={{
                          PaperProps: {
                            style: {
                              maxHeight: '250px', // Adjust the value to your desired height
                            },
                          },
                        }}
                      >
                        <MenuItem value={-1}>Select All</MenuItem>
                        {CTSSubjectList.map((opt) => (
                          <MenuItem key={opt.subjectId} value={opt.subjectId}>
                            {opt.subjectName}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item lg={1} xs={12}>
                    <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                      <Button variant="contained" color="secondary" fullWidth onClick={handleReset}>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Paper className="card-table-container">
              <DataTable
                showHorizontalScroll
                ShowCheckBox
                setSelectedRows={setSelectedRows}
                selectedRows={selectedRows}
                columns={CTSListReportColumns}
                tableStyles={{ minWidth: { xs: '1000px' } }}
                data={CTSDetailsListData}
                getRowKey={getRowKey}
                fetchStatus={CTSDetailsListStatus}
                allowPagination
                allowSorting
                sortColumn={sortColumn}
                sortDirection={sortDirection}
                onSort={handleSort}
                PaginationProps={pageProps}
                // deletingRecords={deletingRecords}
              />
            </Paper>
          </div>
        </Card>
      </CtsAllocationRoot>
      <TemporaryDrawer
        onClose={toggleDrawerClose}
        state={drawerOpen}
        Title={selectedCTSListDetail === DefaultCTSInfo ? 'Create CTS' : 'Edit CTS'}
        DrawerContent={
          <CreateEditCTSForm
            ctsAllocationDetails={selectedCTSListDetail}
            onSave={handleSaveOrEdit}
            onCancel={toggleDrawerClose}
          />
        }
      />
    </Page>
  );
}

export default CtsAllocation;
