/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Typography,
  Card,
} from '@mui/material';
import styled from 'styled-components';
import { lnrows } from '@/config/TableData';
import { CLASS_SELECT, YEAR_SELECT } from '@/config/Selection';
import SelectBox from '@/components/shared/Selections/SelectBox';

const StaffCategoryMapRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  h6 {
    font-weight: 600;
  }
  .MuiTextField-root:hover .MuiInputBase-input {
    cursor: text;
  }
`;

function StaffCategoryMap() {
  return (
    <Page title="Staff Category Map">
      <StaffCategoryMapRoot>
        <Card elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Typography variant="h6" fontSize={17}>
            Staff Category Map
          </Typography>
          <Divider />
          <Grid pb={4} pt={2} container spacing={3}>
            <Grid item lg={4} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Select Year
              </Typography>
              <Autocomplete
                options={YEAR_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select" />}
              />
            </Grid>
            <Grid item lg={4} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Select Status
              </Typography>
              <Autocomplete
                options={CLASS_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select" />}
              />
            </Grid>
            <Grid item lg={4} xs={12}>
              <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                <Button variant="contained" color="secondary" fullWidth>
                  Reset
                </Button>
                <Button variant="contained" color="primary" fullWidth>
                  Search
                </Button>
              </Stack>
            </Grid>
          </Grid>

          <Box>
            <Paper
              sx={{
                border: `1px solid #e8e8e9`,
                width: '100%',
                overflow: 'auto',
                '&::-webkit-scrollbar': {
                  width: 0,
                },
              }}
            >
              <TableContainer
                sx={{
                  maxHeight: 410,
                  width: { xs: '700px', md: '100%' },
                  '&::-webkit-scrollbar': {
                    width: 0,
                  },
                }}
              >
                <Table stickyHeader aria-label="simple table">
                  <TableHead>
                    <TableRow>
                      <TableCell>Sl.No</TableCell>
                      <TableCell>Academic Year</TableCell>
                      <TableCell>Staff Code</TableCell>
                      <TableCell>Staff Name</TableCell>
                      <TableCell>Category</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {lnrows.map((lnrow) => (
                      <TableRow hover key={lnrow.id}>
                        <TableCell width="15%">{lnrow.id}</TableCell>
                        <TableCell width="20%">2022-2023</TableCell>
                        <TableCell width="20%">45678</TableCell>
                        <TableCell width="20%">Passdaily</TableCell>
                        <TableCell width="20%">
                          <SelectBox Selection_Options={['LP', 'UP', 'HS', 'HSS']} placeholder="Select" />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Paper>
          </Box>
        </Card>
      </StaffCategoryMapRoot>
    </Page>
  );
}

export default StaffCategoryMap;
