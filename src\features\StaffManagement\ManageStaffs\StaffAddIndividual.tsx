/* eslint-disable no-nested-ternary */
/* eslint-disable prettier/prettier */
import React, { useEffect, useState } from 'react';
import Page from '@/components/shared/Page';
import styled from 'styled-components';
import {
  Card,
  Box,
  Typography,
  Grid,
  Avatar,
  Badge,
  Stack,
  useTheme,
  TextField,
  MenuItem,
  SelectChangeEvent,
  IconButton,
  alpha,
  Alert,
  Tooltip,
  Button,
  FormControl,
  Select,
  Snackbar,
} from '@mui/material';
import BackButton from '@/components/shared/BackButton';
import LoadingButton from '@mui/lab/LoadingButton';
import typography from '@/theme/typography';
import AddIcon from '@mui/icons-material/Add';
import CloseIcon from '@mui/icons-material/Close';
import useSettings from '@/hooks/useSettings';
import DatePickers from '@/components/shared/Selections/DatePicker';
import { getClassData, getStaffSubmitting, getYearData } from '@/config/storeSelectors';
import { useAppSelector } from '@/hooks/useAppSelector';
import { fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import useAuth from '@/hooks/useAuth';
import { ClassListInfo } from '@/types/AcademicManagement';
import { SuccessIcon } from '@/theme/overrides/CustomIcons';
import SaveIcon from '@mui/icons-material/Save';
import Slide from '@mui/material/Slide';
import { keyframes } from '@emotion/react';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { StaffListInfo } from '@/types/StaffManagement';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import { addNewStaff } from '@/store/StaffMangement/StaffMangement/staffMangement.thunks';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import dayjs from 'dayjs';
import { GENDER_SELECT, STAFF_CATEGORY_SELECT } from '@/config/Selection';
import { ErrorMessage } from '@/components/shared/Popup/ErrorMessage';

const StaffAddIndividualRoot = styled.div`
  /* margin: 1rem 1rem 1rem 1rem; */
  /* padding: 1rem; */
  .info-card {
    padding: 1rem;
    position: relative;
    height: 100%;
    box-shadow: none;
    border: 1px solid
      ${(props) => (props.theme.themeMode === 'light' ? props.theme.palette.grey[300] : props.theme.palette.grey[900])};
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.grey[900]};
  }
  .icon-wrapper {
    position: absolute;
    bottom: 0px;
    right: 0px;
  }
  .MuiSnackbar-root {
    left: 15px;
    right: 15px;
    bottom: 50px;
  }
  .date-feild {
    .MuiStack-root {
      width: 100%;
    }
  }
  @media screen and (max-width: 380px) {
    .staff_info_header {
      flex-wrap: wrap;
    }
  }
`;

const SlideTransition = (props) => {
  return <Slide {...props} direction="up" timeout={{ enter: 500, exit: 300 }} />;
};

const rotateAnimation = keyframes`
  0% {
    transform: rotate(360deg);
  }
  100% {
    transform: rotate(0deg);
  }
`;

const CreateEditStaffValidationSchema = Yup.object({
  staffName: Yup.string().required('Please enter the staff name').min(2, 'Name must be at least 2 characters long'),
  staffCode: Yup.string().required('Please enter the staff code'),
  // .matches(/^[A-Za-z0-9]+$/, 'Staff code must be alphanumeric'),
  staffPhoneNumber: Yup.string()
    .required('Please enter a phone number')
    .matches(/^\d{10}$/, 'Phone number must be exactly 10 digits'),
  // staffEmailID: Yup.string().required('Please enter an email').email('Enter a valid email'),
  staffJoinDate: Yup.string().required('Please select the joining date'),
  staffDOB: Yup.string().required('Please select the date of birth'),
  staffBloodGroup: Yup.string().oneOf(
    ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'],
    'Please select a valid blood group'
  ),
  // .required('Blood group is required'),
  staffGender: Yup.string().oneOf(['0', '1'], 'Please select a gender').required(),
  academicId: Yup.string().oneOf(['11', '10'], 'Please select a year').required(),
  // staffPAddress: Yup.string().required('Please enter a permanent address'),
  // staffCAddress: Yup.string().required('Please enter a current address'),
});

type StaffAddIndividualProps = {
  onBackClick?: () => void;
  staffDetails: StaffListInfo;
  onSave: (values: StaffListInfo, mode: 'create' | 'edit') => void;
};

const StaffAddIndividual = ({ onBackClick, staffDetails, onSave }: StaffAddIndividualProps) => {
  const dispatch = useAppDispatch();
  const { confirm } = useConfirm();

  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const { user } = useAuth();
  const adminId: number = user ? user.accountId : 0;
  const YearData = useAppSelector(getYearData);

  const ClassData = useAppSelector(getClassData);

  const [selectedImage, setSelectedImage] = useState<string>('');
  const [snackbarOpen, setSnackbarOpen] = useState<'1' | '2' | '3' | '4' | '5' | false>(false);
  const [loading, setLoading] = useState<'1' | '2' | '3' | '4' | '5' | false>(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const [isSolid, setIsSolid] = useState(false);
  const [academicYearFilter, setAcademicYearFilter] = useState(0);
  const [genderFilter, setGenderFilter] = useState(-1);
  const [categoryFilter, setCategoryFilter] = useState(0);
  const isSubmitting = useAppSelector(getStaffSubmitting);

  const mode = staffDetails.staffID === 0 ? 'create' : 'edit';
  const {
    values: {
      academicId,
      staffCode,
      staffJoinDate,
      staffName,
      staffGender,
      staffDOB,
      staffBloodGroup,
      staffBirthPlace,
      staffNationality,
      staffMotherTongue,
      staffReligion,
      staffCaste,
      staffFatherName,
      staffMotherName,
      staffFatherOccupation,
      staffJobExperience,
      staffJobRole,
      staffJobDescription,
      staffPhoneNumber,
      staffPAddress,
      staffCAddress,
      staffEmailID,
      staffImage,
      staffCategory,
    },
    handleChange,
    handleSubmit,
    setFieldValue,
    touched,
    errors,
    resetForm,
  } = useFormik<StaffListInfo>({
    initialValues: {
      staffID: staffDetails.staffID,
      academicId: YearData[0]?.accademicId,
      staffCode: staffDetails.staffCode,
      staffJoinDate: staffDetails.staffJoinDate,
      staffName: staffDetails.staffName,
      staffGender: staffDetails.staffGender,
      staffDOB: staffDetails.staffDOB,
      staffBloodGroup: staffDetails.staffBloodGroup,
      staffBirthPlace: staffDetails.staffBirthPlace,
      staffNationality: staffDetails.staffNationality,
      staffMotherTongue: staffDetails.staffMotherTongue,
      staffReligion: staffDetails.staffReligion,
      staffCaste: staffDetails.staffCaste,
      staffFatherName: staffDetails.staffFatherName,
      staffMotherName: staffDetails.staffMotherName,
      staffFatherOccupation: staffDetails.staffFatherOccupation,
      staffJobExperience: staffDetails.staffJobExperience,
      staffJobRole: staffDetails.staffJobRole,
      staffJobDescription: staffDetails.staffJobDescription,
      staffPhoneNumber: staffDetails.staffPhoneNumber,
      staffPAddress: staffDetails.staffPAddress,
      staffCAddress: staffDetails.staffCAddress,
      staffEmailID: staffDetails.staffEmailID,
      staffImage: staffDetails.staffImage,
      staffCategory: staffDetails.staffCategory,
      createdBy: adminId,
      staffCastType: '',
      staffStatus: '',
    },
    validationSchema: CreateEditStaffValidationSchema,
    onSubmit: (values) => {
      onSave(values, mode);
      //   console.log('values::::----', values);
      //   const response = await dispatch(addNewStaff(values)).unwrap();

      //   if (response.id > 0) {
      //     // setDrawerOpen(false);
      //     const successMessage = <SuccessMessage message="Staff created successfully" />;
      //     await confirm(successMessage, 'Staff Created', { okLabel: 'Ok', showOnlyOk: true });
      //   } else {
      //     const errorMessage = <ErrorMessage message="Staff created failed" />;
      //     await confirm(errorMessage, 'Staff Created Failed', { okLabel: 'Ok', showOnlyOk: true });
      //   }
      // },
    },
  });

  // Handle image change
  // const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
  //   if (event.target.files && event.target.files.length > 0) {
  //     const file = event.target.files[0];
  //     setSelectedImage(URL.createObjectURL(file));
  //     setIsAnimating(true); // Start the animation
  //     // Clear input value to allow re-uploading the same file
  //     event.target.value = '';
  //   }
  // };

  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files.length > 0) {
      const file = event.target.files[0];
      const imageUrl = URL.createObjectURL(file);
      // const fileName = file.name;
      setSelectedImage(imageUrl);
      setFieldValue('staffImage', imageUrl);
    }
  };

  const handleAvatarClick = () => {
    document.getElementById('imageInput')?.click();
  };

  const handleRemoveImage = () => {
    setSelectedImage('');
    setFieldValue('staffImage', '');
    setIsAnimating(false); // Stop the animation when image is removed
    setIsSolid(false);
  };

  const AllClassOption = {
    classId: 0,
    className: `All Class`,
    classDescription: 'string',
    classStatus: 1,
  };

  const classDataWithAllClass = [AllClassOption, ...ClassData];

  const [classOptions, setClassOptions] = useState<ClassListInfo | null>(classDataWithAllClass[0]);
  const { classId, className } = classOptions || {};

  const handleClassChange = (event: SelectChangeEvent) => {
    const selectedClass = classDataWithAllClass.find((item) => item.className === event.target.value);
    if (selectedClass) {
      setClassOptions(selectedClass);
    }
  };

  const handleYearChange = (e: SelectChangeEvent) => {
    setAcademicYearFilter(parseInt(YearData.filter((item) => item.accademicId === e.target.value)[0].accademicId, 10));
    const selectedYearId = parseInt(e.target.value, 10);
    setFieldValue('academicId', selectedYearId);
  };

  const handleGenderChange = (e: SelectChangeEvent) => {
    const selectedGenderId = parseInt(e.target.value, 10);
    // parseInt(GENDER_SELECT.filter((item) => item.id === e.target.value)[0].id, 10)
    setGenderFilter(selectedGenderId);

    setFieldValue('staffGender', selectedGenderId);
  };

  const handleCategoryChange = (e: SelectChangeEvent) => {
    const selectedCategoryId = parseInt(e.target.value, 10);
    // parseInt(GENDER_SELECT.filter((item) => item.id === e.target.value)[0].id, 10)
    setCategoryFilter(selectedCategoryId);

    setFieldValue('staffCategory', selectedCategoryId);
  };

  React.useEffect(() => {
    dispatch(fetchYearList(adminId));
  }, [dispatch, adminId]);

  const handleSave = (formId: string) => {
    // Logic to save data
    setLoading(formId);

    setTimeout(() => {
      setLoading(false);
      if (loading === false) setSnackbarOpen(formId); // Show snackbar on save
    }, 2000);
  };

  const handleCloseSnackbar = () => {
    setSnackbarOpen(false);
  };

  // Automatically stop the animation after 3 seconds
  useEffect(() => {
    if (isAnimating) {
      const timer = setTimeout(() => {
        setIsAnimating(false); // Stop the animation after 3 seconds
        setIsSolid(true);
      }, 500);

      return () => clearTimeout(timer); // Cleanup the timeout
    }
  }, [isAnimating]);

  const varLow = alpha(theme.palette.grey[900], 0.48);
  const varHigh = alpha(theme.palette.grey[900], 1);

  const handleReset = async () => {
    if (await confirm('Are you sure you want to reset form?', 'Reset?')) {
      resetForm();
    }
  };
  const buttonText =
    mode === 'create' ? (isSubmitting ? 'Saving...' : 'Save') : isSubmitting ? 'Updating...' : 'Update';
  return (
    <Page title="Student Add Individual">
      <StaffAddIndividualRoot style={{ padding: mode === 'create' ? '1rem' : 0 }}>
        <Card
          sx={{
            p: { xs: '1rem', sm: '1rem' },
            boxShadow: mode === 'create' ? theme.customShadows.card : 0,
            borderRadius: mode === 'create' ? '' : 0,
          }}
        >
          <form noValidate onSubmit={handleSubmit} onReset={handleReset}>
            <Box  className="staff_info_header" display="flex" justifyContent={mode === 'create' ? 'space-between' : 'end'} alignItems="center">
              {mode === 'create' && (
                <BackButton disabled={isSubmitting} onBackClick={onBackClick} title="Information" />
              )}
              <Stack direction="row" spacing={2} justifyContent="end" width="100%">
                {mode === 'create' && (
                  <Button disabled={isSubmitting} size="small" type="reset" color="secondary" variant="contained">
                    Reset
                  </Button>
                )}
                <LoadingButton
                  type="submit"
                  loading={isSubmitting}
                  disabled={isSubmitting}
                  size="small"
                  startIcon={<SaveIcon />}
                  variant="contained"
                  color={mode === 'create' ? 'primary' : 'warning'}
                >
                  {buttonText}
                </LoadingButton>
              </Stack>
            </Box>
            <Typography
              mt={mode === 'create' ? 2 : 0}
              mb={1}
              variant="subtitle1"
              fontSize={14}
              fontWeight={typography.fontWeightMedium}
            >
              {mode === 'create' && 'Staff Information'}
            </Typography>

            <Grid container spacing="1rem">
              <Grid item xl={4} md={6} xs={12}>
                <Card
                  className="info-card"
                  sx={{
                    background: snackbarOpen === false ? theme.palette.common.white : '#0000000',
                  }}
                >
                  <Stack gap={2}>
                    <Box display="flex" alignItems="center" gap={3}>
                      <IconButton
                        disabled={isSubmitting}
                        color="primary"
                        sx={{
                          width: 'fit-content',
                          p: 0,
                          position: 'relative',
                        }}
                      >
                        <Avatar
                          sx={{
                            backgroundColor: isLight ? theme.palette.common.white : theme.palette.grey[900],
                            color: theme.palette.primary.main,
                            cursor: 'pointer',
                            width: '55px',
                            height: '55px',
                            position: 'relative',
                            // Add a pseudo-element for the border animation
                            '&::before': {
                              content: '""',
                              position: 'absolute',
                              top: '0px',
                              left: '0px',
                              right: '0px',
                              bottom: '0px',
                              border: `2px ${theme.palette.primary.main}`,
                              borderStyle: staffImage ? 'solid' : 'dashed',
                              // borderStyle: isAnimating ? 'solid' : 'dashed',
                              borderRadius: '50%',
                              animation: isAnimating ? `${rotateAnimation} .5s linear` : 'none',
                            },
                          }}
                          alt="Student Avatar"
                          src={staffImage}
                          onClick={handleAvatarClick} // Trigger file input on click
                        />
                        {staffImage ? (
                          <Tooltip title="Remove Image">
                            <CloseIcon
                              className="icon-wrapper"
                              sx={{
                                fontSize: '15px',
                                cursor: 'pointer',
                                bgcolor: theme.palette.error.main,
                                color: theme.palette.common.white,
                                borderRadius: '50px',
                              }}
                              onClick={handleRemoveImage} // Handle image removal
                            />
                          </Tooltip>
                        ) : (
                          <AddIcon
                            className="icon-wrapper"
                            sx={{
                              fontSize: '15px',
                              cursor: 'pointer',
                              bgcolor: theme.palette.primary.main,
                              color: theme.palette.common.white,
                              borderRadius: '50px',
                            }}
                            onClick={handleAvatarClick} // Trigger file input on click
                          />
                        )}
                        <input
                          disabled={isSubmitting}
                          id="imageInput"
                          type="file"
                          accept="image/*"
                          style={{ display: 'none' }}
                          onChange={handleImageChange} // Handle image change
                        />
                      </IconButton>
                      <FormControl fullWidth>
                        <Typography variant="subtitle2" fontSize={12}>
                          First Name
                        </Typography>
                        <TextField
                          disabled={isSubmitting}
                          fullWidth
                          placeholder="Enter name"
                          name="staffName"
                          value={staffName}
                          onChange={handleChange}
                          error={touched.staffName && !!errors.staffName}
                          helperText={errors.staffName}
                          // disabled={isSubmitting}
                        />
                      </FormControl>
                    </Box>

                    <FormControl fullWidth>
                      <Typography variant="subtitle2" fontSize={12}>
                        Staff Code
                      </Typography>
                      <TextField
                        disabled={isSubmitting}
                        fullWidth
                        placeholder="Enter code"
                        name="staffCode"
                        value={staffCode}
                        onChange={handleChange}
                        error={touched.staffCode && !!errors.staffCode}
                        helperText={errors.staffCode}
                      />
                    </FormControl>
                    <FormControl fullWidth>
                      <Typography variant="subtitle2" fontSize={12}>
                        Gender
                      </Typography>
                      <Select
                        disabled={isSubmitting}
                        labelId="genderFilter"
                        id="genderFilterSelect"
                        value={genderFilter.toString()}
                        onChange={handleGenderChange}
                        placeholder="Select gender"
                      >
                        {GENDER_SELECT.map((opt) => (
                          <MenuItem key={opt.id} value={opt.id}>
                            {opt.gender}
                          </MenuItem>
                        ))}
                      </Select>
                      {touched.staffGender && !!errors.staffGender && (
                        <Typography color="red" fontSize="12px" ml={2.5} mt={1} variant="subtitle1">
                          {errors.staffGender}
                        </Typography>
                      )}
                    </FormControl>
                    <FormControl fullWidth className="date-feild">
                      <Typography variant="subtitle2" fontSize={12}>
                        Date of Birth
                      </Typography>
                      <DatePickers
                        name="staffDOB"
                        value={dayjs(staffDOB)}
                        onChange={(e) => {
                          const formattedDate = mode === 'create' && e ? e.format('DD/MM/YYYY') : dayjs(e);
                          // const formattedDate = e ? dayjs(e) : '';
                          setFieldValue('staffDOB', formattedDate);
                          console.log('date::::', formattedDate);
                        }}
                      />
                      {touched.staffDOB && !!errors.staffDOB && (
                        <Typography color="red" fontSize="12px" ml={2.5} mt={1} variant="subtitle1">
                          {errors.staffDOB}
                        </Typography>
                      )}
                    </FormControl>
                    <FormControl fullWidth className="date-feild">
                      <Typography variant="subtitle2" fontSize={12}>
                        Joining Date
                      </Typography>
                      <DatePickers
                        name="staffJoinDate"
                        value={dayjs(staffJoinDate)}
                        onChange={(e) => {
                          // const formattedDate = e ? e.format('DD/MM/YYYY') : '';
                          const formattedDate = mode === 'create' && e ? e.format('DD/MM/YYYY') : dayjs(e);
                          // const formattedDate = e ? dayjs(e) : '';
                          setFieldValue('staffJoinDate', formattedDate);
                          console.log('date::::', formattedDate);
                        }}
                      />
                      {touched.staffJoinDate && !!errors.staffJoinDate && (
                        <Typography color="red" fontSize="12px" ml={2.5} mt={1} variant="subtitle1">
                          {errors.staffJoinDate}
                        </Typography>
                      )}
                    </FormControl>

                    <FormControl fullWidth>
                      <Typography variant="subtitle2" fontSize={12}>
                        Academic Year
                      </Typography>
                      <Select
                        disabled={isSubmitting}
                        labelId="academicYearFilter"
                        id="academicYearFilterSelect"
                        value={academicId.toString()}
                        onChange={handleYearChange}
                        placeholder="Select Year"
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Year
                        </MenuItem>
                        {YearData.map((opt) => (
                          <MenuItem key={opt.accademicId} value={opt.accademicId}>
                            {opt.accademicTime}
                          </MenuItem>
                        ))}
                      </Select>
                      {touched.academicId && !!errors.academicId && (
                        <Typography color="red" fontSize="12px" ml={2.5} mt={1} variant="subtitle1">
                          {errors.academicId}
                        </Typography>
                      )}
                    </FormControl>
                    <FormControl fullWidth>
                      <Typography variant="subtitle2" fontSize={12}>
                        Blood Group
                      </Typography>
                      <TextField
                        disabled={isSubmitting}
                        fullWidth
                        placeholder="Enter blood group"
                        name="staffBloodGroup"
                        value={staffBloodGroup}
                        onChange={handleChange}
                        error={touched.staffBloodGroup && !!errors.staffBloodGroup}
                        helperText={errors.staffBloodGroup}
                      />
                    </FormControl>
                    <FormControl fullWidth>
                      <Typography variant="subtitle2" fontSize={12}>
                        Birth Place
                      </Typography>
                      <TextField
                        disabled={isSubmitting}
                        fullWidth
                        placeholder="Enter place"
                        name="staffBirthPlace"
                        value={staffBirthPlace}
                        onChange={handleChange}
                        error={touched.staffBirthPlace && !!errors.staffBirthPlace}
                        helperText={errors.staffBirthPlace}
                      />
                    </FormControl>
                    {/* <FormControl fullWidth>
                      <Typography variant="subtitle2" fontSize={12}>
                        Class
                      </Typography>
                      <Select
                      disabled={isSubmitting}
                        labelId="classFilter"
                        id="classFilterSelect"
                        value={className}
                        onChange={handleClassChange}
                        placeholder="Select Class"
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Year
                        </MenuItem>
                        {classDataWithAllClass?.map((item: ClassListInfo) => (
                          <MenuItem sx={{ fontSize: '13px' }} key={item.classId} value={item.className}>
                            {item.className}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl> */}
                    <Stack direction="row" justifyContent="end" alignItems="end">
                      {/* <LoadingButton
                        type="submit"
                        startIcon={<SaveIcon />}
                        loadingPosition="start"
                        loading={loading === '1'}
                        size="small"
                        variant="contained"
                        color="primary"
                        sx={{ width: loading === '1' ? 110 : 75 }}
                      >
                        {loading === '1' ? 'Saving...' : 'Save'}
                      </LoadingButton> */}
                    </Stack>
                  </Stack>
                  <Snackbar
                    sx={{ position: 'absolute' }}
                    open={snackbarOpen === '1'}
                    autoHideDuration={2000}
                    onClose={handleCloseSnackbar}
                    TransitionComponent={SlideTransition}
                  >
                    <Alert severity="success" sx={{ px: 1, py: 0, width: '100%', color: theme.palette.success.main }}>
                      Details Save successfully!
                    </Alert>
                  </Snackbar>
                </Card>
              </Grid>

              <Grid item xl={4} md={6} xs={12}>
                <Card className="info-card">
                  <Stack gap={2} mt={0} sx={{ width: '100%' }}>
                    {/* <FormControl fullWidth>
                      <Typography variant="subtitle2" fontSize={12}>
                        Middle Name
                      </Typography>
                      <TextField
                      disabled={isSubmitting} fullWidth placeholder="Enter Name" />
                    </FormControl>
                    <FormControl fullWidth>
                      <Typography variant="subtitle2" fontSize={12}>
                        Last Name
                      </Typography>
                      <TextField
                      disabled={isSubmitting} fullWidth placeholder="Enter Name" />
                    </FormControl> */}

                    <FormControl fullWidth>
                      <Typography variant="subtitle2" fontSize={12}>
                        Nationality
                      </Typography>
                      <TextField
                        disabled={isSubmitting}
                        fullWidth
                        placeholder="Enter nationality"
                        name="staffNationality"
                        value={staffNationality}
                        onChange={handleChange}
                        error={touched.staffNationality && !!errors.staffNationality}
                        helperText={errors.staffNationality}
                      />
                    </FormControl>
                    <FormControl fullWidth>
                      <Typography variant="subtitle2" fontSize={12}>
                        Mother Tongue
                      </Typography>
                      <TextField
                        disabled={isSubmitting}
                        fullWidth
                        placeholder="Enter Mother Tongue"
                        name="staffMotherTongue"
                        value={staffMotherTongue}
                        onChange={handleChange}
                        error={touched.staffMotherTongue && !!errors.staffMotherTongue}
                        helperText={errors.staffMotherTongue}
                      />
                    </FormControl>
                    <FormControl fullWidth>
                      <Typography variant="subtitle2" fontSize={12}>
                        Religion
                      </Typography>
                      <TextField
                        disabled={isSubmitting}
                        fullWidth
                        placeholder="Enter Religion"
                        name="staffReligion"
                        value={staffReligion}
                        onChange={handleChange}
                        error={touched.staffReligion && !!errors.staffReligion}
                        helperText={errors.staffReligion}
                      />
                    </FormControl>
                    <FormControl fullWidth>
                      <Typography variant="subtitle2" fontSize={12}>
                        Previous Organization
                      </Typography>
                      <TextField
                        disabled={isSubmitting}
                        fullWidth
                        placeholder="Caste"
                        name="staffCaste"
                        value={staffCaste}
                        onChange={handleChange}
                        error={touched.staffCaste && !!errors.staffCaste}
                        helperText={errors.staffCaste}
                      />
                    </FormControl>
                    <FormControl fullWidth>
                      <Typography variant="subtitle2" fontSize={12}>
                        Father Name
                      </Typography>
                      <TextField
                        disabled={isSubmitting}
                        fullWidth
                        placeholder="Enter father name"
                        name="staffFatherName"
                        value={staffFatherName}
                        onChange={handleChange}
                        error={touched.staffFatherName && !!errors.staffFatherName}
                        helperText={errors.staffFatherName}
                      />
                    </FormControl>
                    <FormControl fullWidth>
                      <Typography variant="subtitle2" fontSize={12}>
                        Mother Name
                      </Typography>
                      <TextField
                        disabled={isSubmitting}
                        fullWidth
                        placeholder="Enter mother name"
                        name="staffMotherName"
                        value={staffMotherName}
                        onChange={handleChange}
                        error={touched.staffMotherName && !!errors.staffMotherName}
                        helperText={errors.staffMotherName}
                      />
                    </FormControl>
                    <FormControl fullWidth>
                      <Typography variant="subtitle2" fontSize={12}>
                        Father Occupation
                      </Typography>
                      <TextField
                        disabled={isSubmitting}
                        fullWidth
                        placeholder="Enter father occupation"
                        name="staffFatherOccupation"
                        value={staffFatherOccupation}
                        onChange={handleChange}
                        error={touched.staffFatherOccupation && !!errors.staffFatherOccupation}
                        helperText={errors.staffFatherOccupation}
                      />
                    </FormControl>
                    <Stack direction="row" justifyContent="end" alignItems="end">
                      {/* <LoadingButton
                        loadingPosition="start"
                        loading={loading === '2'}
                        onClick={() => handleSave('2')}
                        startIcon={<SaveIcon />}
                        size="small"
                        variant="contained"
                        color="primary"
                        sx={{ width: loading === '2' ? 110 : 75 }}
                      >
                        {loading === '2' ? 'Saving...' : 'Save'}
                      </LoadingButton> */}
                    </Stack>
                  </Stack>
                  <Snackbar
                    sx={{ position: 'absolute' }}
                    open={snackbarOpen === '2'}
                    autoHideDuration={2000}
                    onClose={handleCloseSnackbar}
                    TransitionComponent={SlideTransition}
                  >
                    <Alert severity="success" sx={{ px: 1, py: 0, width: '100%', color: theme.palette.success.main }}>
                      Details Save successfully!
                    </Alert>
                  </Snackbar>
                </Card>
              </Grid>

              <Grid item xl={4} md={6} xs={12}>
                <Card className="info-card">
                  <Stack gap={2} sx={{ width: '100%' }}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle2" fontSize={12}>
                        Job Experience
                      </Typography>
                      <TextField
                        disabled={isSubmitting}
                        fullWidth
                        placeholder="Enter job experience"
                        name="staffJobExperience"
                        value={staffJobExperience}
                        onChange={handleChange}
                        error={touched.staffJobExperience && !!errors.staffJobExperience}
                        helperText={errors.staffJobExperience}
                      />
                    </FormControl>
                    <FormControl fullWidth>
                      <Typography variant="subtitle2" fontSize={12}>
                        Job Role
                      </Typography>
                      <TextField
                        disabled={isSubmitting}
                        fullWidth
                        placeholder="Enter job role"
                        name="staffJobRole"
                        value={staffJobRole}
                        onChange={handleChange}
                        error={touched.staffJobRole && !!errors.staffJobRole}
                        helperText={errors.staffJobRole}
                      />
                    </FormControl>
                    <FormControl fullWidth>
                      <Typography variant="subtitle2" fontSize={12}>
                        Job Description
                      </Typography>
                      <TextField
                        disabled={isSubmitting}
                        fullWidth
                        placeholder="Enter description"
                        name="staffJobDescription"
                        value={staffJobDescription}
                        onChange={handleChange}
                        error={touched.staffJobDescription && !!errors.staffJobDescription}
                        helperText={errors.staffJobDescription}
                      />
                    </FormControl>
                    <FormControl fullWidth>
                      <Typography variant="subtitle2" fontSize={12}>
                        Contact Number
                      </Typography>
                      <TextField
                        disabled={isSubmitting}
                        // type="number"
                        fullWidth
                        placeholder="Enter Number"
                        name="staffPhoneNumber"
                        value={staffPhoneNumber}
                        onChange={handleChange}
                        error={touched.staffPhoneNumber && !!errors.staffPhoneNumber}
                        helperText={errors.staffPhoneNumber}
                      />
                    </FormControl>
                    <FormControl fullWidth>
                      <Typography variant="subtitle2" fontSize={12}>
                        Permanent Address
                      </Typography>
                      <TextField
                        disabled={isSubmitting}
                        fullWidth
                        placeholder="Enter permanet address"
                        name="staffPAddress"
                        value={staffPAddress}
                        onChange={handleChange}
                        error={touched.staffPAddress && !!errors.staffPAddress}
                        helperText={errors.staffPAddress}
                      />
                    </FormControl>
                    <FormControl fullWidth>
                      <Typography variant="subtitle2" fontSize={12}>
                        Current Address
                      </Typography>
                      <TextField
                        disabled={isSubmitting}
                        fullWidth
                        placeholder="Enter current address"
                        name="staffCAddress"
                        value={staffCAddress}
                        onChange={handleChange}
                        error={touched.staffCAddress && !!errors.staffCAddress}
                        helperText={errors.staffCAddress}
                      />
                    </FormControl>
                    <FormControl fullWidth>
                      <Typography variant="subtitle2" fontSize={12}>
                        Job Email Id
                      </Typography>
                      <TextField
                        disabled={isSubmitting}
                        type="email"
                        fullWidth
                        placeholder="Enter Land Number"
                        name="staffEmailID"
                        value={staffEmailID}
                        onChange={handleChange}
                        error={touched.staffEmailID && !!errors.staffEmailID}
                        helperText={errors.staffEmailID}
                      />
                    </FormControl>
                    <FormControl fullWidth>
                      <Typography variant="subtitle2" fontSize={12}>
                        Category
                      </Typography>
                      <Select
                        disabled={isSubmitting}
                        value={staffCategory.toString()}
                        onChange={handleCategoryChange}
                        placeholder="Select category"
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select category
                        </MenuItem>
                        {STAFF_CATEGORY_SELECT.map((opt) => (
                          <MenuItem key={opt.id} value={opt.id}>
                            {opt.category}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>

                    <Stack
                      direction="row"
                      justifyContent="end"
                      position={{ lg: 'absolute' }}
                      bottom="1rem"
                      right="1rem"
                    >
                      {/* <LoadingButton
                        loadingPosition="start"
                        loading={loading === '3'}
                        onClick={() => handleSave('3')}
                        startIcon={<SaveIcon />}
                        size="small"
                        variant="contained"
                        color="primary"
                        sx={{ width: loading === '3' ? 110 : 75 }}
                      >
                        {loading === '3' ? 'Saving...' : 'Save'}
                      </LoadingButton> */}
                    </Stack>
                  </Stack>
                  <Snackbar
                    sx={{ position: 'absolute' }}
                    open={snackbarOpen === '3'}
                    autoHideDuration={2000}
                    onClose={handleCloseSnackbar}
                    TransitionComponent={SlideTransition}
                  >
                    <Alert severity="success" sx={{ px: 1, py: 0, width: '100%', color: theme.palette.success.main }}>
                      Details Save successfully!
                    </Alert>
                  </Snackbar>
                </Card>
              </Grid>
              {/* <Grid item xs={12}>
                <Typography variant="subtitle1" fontSize={14} fontWeight={typography.fontWeightMedium}>
                  Parent Information
                </Typography>
              </Grid> */}
              {/* <Grid item xl={4} md={6} xs={12}>
                <Card className="info-card">
                  <Stack gap={2} sx={{ width: '100%' }}>
                    <Stack direction="row" justifyContent="end">
                      <LoadingButton
                        loadingPosition="start"
                        loading={loading === '4'}
                        onClick={() => handleSave('4')}
                        startIcon={<SaveIcon />}
                        size="small"
                        variant="contained"
                        color="primary"
                        sx={{ width: loading === '4' ? 110 : 75 }}
                      >
                        {loading === '4' ? 'Saving...' : 'Save'}
                      </LoadingButton>
                    </Stack>
                  </Stack>
                  <Snackbar
                    sx={{ position: 'absolute' }}
                    open={snackbarOpen === '4'}
                    autoHideDuration={2000}
                    onClose={handleCloseSnackbar}
                    TransitionComponent={SlideTransition}
                  >
                    <Alert severity="success" sx={{ px: 1, py: 0, width: '100%', color: theme.palette.success.main }}>
                      Details Save successfully!
                    </Alert>
                  </Snackbar>
                </Card>
              </Grid> */}

              {/* <Grid item xl={4} md={6} xs={12}>
                <Card className="info-card">
                  <Stack gap={2} sx={{ width: '100%' }}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle2" fontSize={12}>
                        Father
                      </Typography>
                      <TextField
                      disabled={isSubmitting} fullWidth placeholder="Enter Mother Name" />
                    </FormControl>
                    <FormControl fullWidth>
                      <Typography variant="subtitle2" fontSize={12}>
                        Mother Name
                      </Typography>
                      <TextField
                      disabled={isSubmitting} fullWidth placeholder="Enter  Mother Qualification" />
                    </FormControl>
                    <Stack
                      direction="row"
                      justifyContent="end"
                      position={{ md: 'absolute' }}
                      bottom="1rem"
                      right="1rem"
                    >
                      <LoadingButton
                        loadingPosition="start"
                        loading={loading === '5'}
                        onClick={() => handleSave('5')}
                        startIcon={<SaveIcon />}
                        size="small"
                        variant="contained"
                        color="primary"
                        sx={{ width: loading === '5' ? 110 : 75 }}
                      >
                        {loading === '5' ? 'Saving...' : 'Save'}
                      </LoadingButton>
                    </Stack>
                  </Stack>
                  <Snackbar
                    sx={{ position: 'absolute' }}
                    open={snackbarOpen === '5'}
                    autoHideDuration={2000}
                    onClose={handleCloseSnackbar}
                    TransitionComponent={SlideTransition}
                  >
                    <Alert severity="success" sx={{ px: 1, py: 0, width: '100%', color: theme.palette.success.main }}>
                      Details Save successfully!
                    </Alert>
                  </Snackbar>
                </Card>
              </Grid> */}
            </Grid>
          </form>
        </Card>
      </StaffAddIndividualRoot>
    </Page>
  );
};

export default StaffAddIndividual;
