/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/alt-text */
import React, { useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Box,
  Divider,
  Grid,
  Stack,
  Button,
  Typography,
  Card,
  useTheme,
  IconButton,
  Collapse,
  Tooltip,
  FormControl,
  Chip,
  Avatar,
  Select,
  MenuItem,
  SelectChangeEvent,
  TablePagination,
} from '@mui/material';
import styled from 'styled-components';
import SearchIcon from '@mui/icons-material/Search';
import { MdAdd } from 'react-icons/md';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import useSettings from '@/hooks/useSettings';
import NoData from '@/assets/no-datas.png';
import {
  getClassData,
  getLeaveNoteListData,
  getLeaveNoteListStatus,
  getYearData,
  getAttendanceListSubmitting,
  getLeaveNoteListPageInfo,
  getSortColumn,
  getSortDirection,
  getYearStatus,
} from '@/config/storeSelectors';
import { useAppSelector } from '@/hooks/useAppSelector';
import { fetchClassList, fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import useAuth from '@/hooks/useAuth';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import DatePickers from '@/components/shared/Selections/DatePicker';
import Popup from '@/components/shared/Popup/Popup';
import { useNavigate } from 'react-router-dom';
import dayjs, { Dayjs } from 'dayjs';
import { LeaveNoteListInfo, LeaveNoteListRequest } from '@/types/AttendanceMarking';
import { ApproveLeave, fetchLeaveNoteList, RejectLeave } from '@/store/AttendanceMarking/attendanceMarking.thunks';
import { CloseIcon, SuccessIcon } from '@/theme/overrides/CustomIcons';
import LoadingButton from '@mui/lab/LoadingButton';
import { View } from './View';

// export const data = [
//   {
//     studentName: 'Peter Jack',
//     class: 'VII-B',
//     date: 'Sep 25th',
//     admission: '34765',
//     mobileNumber: '+91-8976 756 457',
//     academicYear: '2023-2024',
//     device: 'Android',
//     image:
//       'https://images.unsplash.com/photo-1633332755192-727a05c4013d?q=80&w=1780&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
//   },
//   {
//     studentName: 'Peter Jack',
//     class: 'VII-B',
//     date: 'Sep 25th',
//     admission: '34765',
//     mobileNumber: '+91-8976 756 457',
//     academicYear: '2023-2024',
//     device: 'IOS',
//   },
//   {
//     studentName: 'Peter Jack',
//     class: 'VII-B',
//     date: 'Sep 25th',
//     admission: '34765',
//     mobileNumber: '+91-8976 756 457',
//     academicYear: '2023-2024',
//     device: 'Computer & Laptop',
//   },
//   {
//     studentName: 'Peter Jack',
//     class: 'VII-B',
//     date: 'Sep 25th',
//     admission: '34765',
//     mobileNumber: '+91-8976 756 457',
//     academicYear: '2023-2024',
//     device: 'Computer & Laptop',
//   },
//   {
//     studentName: 'Peter Jack',
//     class: 'VII-B',
//     date: 'Sep 25th',
//     admission: '34765',
//     mobileNumber: '+91-8976 756 457',
//     academicYear: '2023-2024',
//     device: 'IOS',
//   },
//   {
//     studentName: 'Peter Jack',
//     class: 'VII-B',
//     date: 'Sep 25th',
//     admission: '34765',
//     mobileNumber: '+91-8976 756 457',
//     academicYear: '2023-2024',
//     device: 'Android',
//   },
//   {
//     studentName: 'Peter Jack',
//     class: 'VII-B',
//     date: 'Sep 25th',
//     admission: '34765',
//     mobileNumber: '+91-8976 756 457',
//     academicYear: '2023-2024',
//     device: 'Computer & Laptop',
//   },
//   {
//     studentName: 'Peter Jack',
//     class: 'VII-B',
//     date: 'Sep 25th',
//     admission: '34765',
//     mobileNumber: '+91-8976 756 457',
//     academicYear: '2023-2024',
//     device: 'IOS',
//   },
//   {
//     studentName: 'Peter Jack',
//     class: 'VII-B',
//     date: 'Sep 25th',
//     admission: '34765',
//     mobileNumber: '+91-8976 756 457',
//     academicYear: '2023-2024',
//     device: 'Computer & Laptop',
//   },
//   {
//     studentName: 'Peter Jack',
//     class: 'VII-B',
//     date: 'Sep 25th',
//     admission: '34765',
//     mobileNumber: '+91-8976 756 457',
//     academicYear: '2023-2024',
//     device: 'Computer & Laptop',
//   },
//   {
//     studentName: 'Peter Jack',
//     class: 'VII-B',
//     date: 'Sep 25th',
//     admission: '34765',
//     mobileNumber: '+91-8976 756 457',
//     academicYear: '2023-2024',
//     device: 'Android',
//   },
//   {
//     studentName: 'Peter Jack',
//     class: 'VII-B',
//     date: 'Sep 25th',
//     admission: '34765',
//     mobileNumber: '+91-8976 756 457',
//     academicYear: '2023-2024',
//     device: 'Computer & Laptop',
//   },
// ];

const LeaveNoteListRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;
  .Card {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 40px);
      flex-grow: 1;

      .card-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: auto;
        ::-webkit-scrollbar {
          width: 0px;
        }
      }
      .student_card .date {
        color: ${(props) =>
          props.theme.themeMode === 'light' ? props.theme.palette.grey[500] : props.theme.palette.grey[100]};
      }
      .view_file_btn {
        color: ${(props) => props.theme.palette.grey[500]};
        text-decoration: none;
        font-size: 12px;
        :hover {
          color: ${(props) => props.theme.palette.grey[700]};
        }
      }
    }
  }
`;

function LeaveNoteList() {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const dispatch = useAppDispatch();
  const { user } = useAuth();
  const navigate = useNavigate();
  const adminId: number = user ? user.accountId : 0;
  const YearData = useAppSelector(getYearData);
  const ClassData = useAppSelector(getClassData);
  const defualtYear = YearData[0]?.accademicId || -1;
  const [academicYearFilter, setAcademicYearFilter] = useState(-1);
  const [showFilter, setShowFilter] = useState(false);
  const [createOpen, setCreateOpen] = React.useState(false);
  const [classFilter, setClassFilter] = useState(-1);
  const [fromDateFilter, setFromDateFilter] = useState<Dayjs | string>('');
  const [toDateFilter, setToDateFilter] = useState<Dayjs | string>('');
  const sortColumn = useAppSelector(getSortColumn);
  const sortDirection = useAppSelector(getSortDirection);
  const paginationInfo = useAppSelector(getLeaveNoteListPageInfo);
  const LeaveNoteListStatus = useAppSelector(getLeaveNoteListStatus);
  const YearStatus = useAppSelector(getYearStatus);
  const LeaveNoteListData = useAppSelector(getLeaveNoteListData);
  const isSubmitting = useAppSelector(getAttendanceListSubmitting);

  const { pagenumber, pagesize, totalrecords } = paginationInfo;

  const currentLeaveNoteListRequest: LeaveNoteListRequest = React.useMemo(
    () => ({
      pageNumber: pagenumber,
      pageSize: pagesize,
      sortColumn,
      sortDirection,
      filters: {
        academicId: academicYearFilter,
        adminId,
        classId: classFilter,
        fromdate: fromDateFilter,
        todate: toDateFilter,
      },
    }),
    [
      pagenumber,
      pagesize,
      sortColumn,
      sortDirection,
      academicYearFilter,
      adminId,
      classFilter,
      fromDateFilter,
      toDateFilter,
    ]
  );
  // useEffect(() => {
  //   const data = JSON.parse(localStorage.getItem('absenteesData') || '[]');
  //   setSelectedABS(data);
  //   console.log('abssss', selectedABS);
  // }, [selectedABS]);

  const loadLeaveNoteList = React.useCallback(
    async (request: LeaveNoteListRequest) => {
      try {
        // const data = await Promise.all([dispatch(fetchLeaveNoteList(request)), setSelectedABS([])]);
        // const datas = await
        dispatch(fetchLeaveNoteList(request)).unwrap();
        // const LeaveData = datas.data ? datas.data.map((item: LeaveNoteListInfo) => ({ ...item })) : [];
      } catch (error) {
        console.error('Error loading list:', error);
      }
    },
    [dispatch]
  );
  React.useEffect(() => {
    if (YearStatus === 'idle') {
      setAcademicYearFilter(defualtYear);
    }
    if (LeaveNoteListStatus === 'idle') {
      dispatch(fetchYearList(adminId));
      dispatch(fetchClassList(adminId));
      loadLeaveNoteList(currentLeaveNoteListRequest);
    }
    // loadLeaveNoteList(currentLeaveNoteListRequest);
    // dispatch(
    //   fetchLeaveNoteList({
    //     accademicId: 10,
    //     adminId,
    //     classId: 1,
    //     absentDate: dateFilter,
    //   })
    // );
  }, [dispatch, adminId, currentLeaveNoteListRequest, loadLeaveNoteList, YearStatus, LeaveNoteListStatus, defualtYear]);

  const handleYearChange = (e: SelectChangeEvent) => {
    const selectedAcademicId = e.target.value;
    setAcademicYearFilter(parseInt(selectedAcademicId, 10));
    loadLeaveNoteList({
      ...currentLeaveNoteListRequest,
      filters: {
        ...currentLeaveNoteListRequest.filters,
        academicId: parseInt(selectedAcademicId, 10),
      },
    });
  };

  const handleClassChange = (e: SelectChangeEvent) => {
    const selectedClass = parseInt(e.target.value, 10);
    if (selectedClass) {
      setClassFilter(selectedClass);
    }
    loadLeaveNoteList({
      ...currentLeaveNoteListRequest,
      pageNumber: 1,
      filters: { ...currentLeaveNoteListRequest.filters, classId: selectedClass || 0 },
    });
  };
  const handleReset = React.useCallback(
    (e: any) => {
      e.preventDefault();
      setClassFilter(-1);
      setAcademicYearFilter(defualtYear);
      setFromDateFilter('');
      setToDateFilter('');
      loadLeaveNoteList({
        pageNumber: 1,
        pageSize: 20,
        sortColumn: 'studentId',
        sortDirection: 'asc',
        filters: {
          academicId: -1,
          adminId,
          classId: -1,
          fromdate: '',
          todate: '',
        },
      });
    },
    [defualtYear, loadLeaveNoteList, adminId]
  );
  const pageProps = useMemo(
    () => ({
      rowsPerPageOptions: [10, 20, 30, 50, 100, 200, 300, 500],
      pageNumber: pagenumber - 1,
      pageSize: pagesize,
      totalRecords: totalrecords,
      onPageChange: () => {},
      onRowsPerPageChange: () => {},
    }),
    [pagenumber, pagesize, totalrecords]
  );

  const [anchorEl, setAnchorEl] = useState(null);

  const handleApprove = React.useCallback(
    async (id: number) => {
      const actionResult = await dispatch(ApproveLeave({ leaveId: id, approvedBy: adminId }));
      console.log('actionResult:', actionResult);
      loadLeaveNoteList(currentLeaveNoteListRequest);
    },
    [dispatch, adminId, currentLeaveNoteListRequest, loadLeaveNoteList]
  );

  const handleReject = React.useCallback(
    async (id: number) => {
      const actionResult = await dispatch(RejectLeave({ leaveId: id, rejectedBy: adminId }));
      console.log('actionResult:', actionResult);
      loadLeaveNoteList(currentLeaveNoteListRequest);
    },
    [dispatch, adminId, loadLeaveNoteList, currentLeaveNoteListRequest]
  );

  const open = Boolean(anchorEl);
  const id = open ? 'avatar-popover' : undefined;

  return (
    <Page title="Leave List">
      <LeaveNoteListRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack pb={1} direction="row" justifyContent="space-between" alignItems="center" flexWrap="wrap">
            <Stack direction="row" alignItems="center" justifyContent="space-between" flex={1} whiteSpace="nowrap">
              <Typography variant="h6" fontSize={17} width="100%">
                Leave List
              </Typography>
              <Tooltip title="Search">
                <IconButton aria-label="" color="primary" sx={{ mr: 1 }} onClick={() => setShowFilter((x) => !x)}>
                  <SearchIcon />
                </IconButton>
              </Tooltip>
            </Stack>
            {/* <Box
              display="flex"
              justifyContent="end"
              alignItems="center"
              columnGap={2}
              sx={{
                flexShrink: 0,

                '@media (max-width: 340px)': {
                  flexWrap: 'wrap',
                  rowGap: 1,
                },
                '@media (max-width: 280px)': {
                  flex: 1,
                },
              }}
            >
              <Button
                onClick={() => setCreateOpen(true)}
                sx={{
                  borderRadius: '20px',
                  whiteSpace: 'nowrap',
                  '@media (max-width: 500px)': {
                    width: '100%',
                  },
                }}
                size="small"
                variant="outlined"
              >
                <MdAdd size="20px" />
                Create
              </Button>
            </Box> */}
          </Stack>
          <Stack>
            <Divider />
          </Stack>
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={5} pt={1} container spacing={2}>
                  <Grid item xxl={2} xl={2.5} lg={2.5} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Select
                        labelId="academicYearFilter"
                        id="academicYearFilterSelect"
                        value={academicYearFilter?.toString()}
                        // defaultValue={YearData[0]?.accademicId}
                        onChange={handleYearChange}
                        placeholder="Select Year"
                      >
                        <MenuItem value={-1}>Select All</MenuItem>
                        {YearData.map((opt) => (
                          <MenuItem key={opt.accademicId} value={opt.accademicId}>
                            {opt.accademicTime}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={2.5} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Select Class
                      </Typography>
                      <Select
                        labelId="classFilter"
                        id="classFilterSelect"
                        value={classFilter?.toString()}
                        // defaultValue={YearData[0]?.accademicId}
                        onChange={handleClassChange}
                        placeholder="Select Class"
                      >
                        <MenuItem value={-1}>Select All</MenuItem>
                        {ClassData.map((opt) => (
                          <MenuItem key={opt.classId} value={opt.classId}>
                            {opt.className}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={2.5} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        From Date
                      </Typography>
                      <DatePickers
                        name="studentFromDate"
                        value={dayjs(fromDateFilter, 'DD/MM/YYYY')}
                        onChange={(e) => {
                          const formattedDate = e ? e.format('DD/MM/YYYY') : '';
                          setFromDateFilter(formattedDate);
                          console.log('fromdate::::', formattedDate);
                          loadLeaveNoteList({
                            ...currentLeaveNoteListRequest,
                            filters: {
                              ...currentLeaveNoteListRequest.filters,
                              fromdate: formattedDate,
                            },
                          });
                        }}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={2.5} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        To Date
                      </Typography>
                      <DatePickers
                        name="studentToDate"
                        value={dayjs(toDateFilter, 'DD/MM/YYYY')}
                        onChange={(e) => {
                          const formattedDate = e ? e.format('DD/MM/YYYY') : '';
                          setToDateFilter(formattedDate);
                          console.log('todate::::', formattedDate);
                          loadLeaveNoteList({
                            ...currentLeaveNoteListRequest,
                            filters: {
                              ...currentLeaveNoteListRequest.filters,
                              todate: formattedDate,
                            },
                          });
                        }}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg={1} sm={1} xs={12}>
                    <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, sm: 3.79 } }}>
                      <Button variant="contained" color="secondary" fullWidth onClick={handleReset}>
                        Reset
                      </Button>
                      {/* <Button variant="contained" color="primary" fullWidth>
                          Search
                        </Button> */}
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Box className="card-container" mt={2}>
              {LeaveNoteListData.length !== 0 ? (
                <Grid container spacing={2}>
                  {LeaveNoteListData.map((student) => (
                    <Grid item xl={3} lg={6} md={12} sm={6} xs={12}>
                      <Card
                        className="student_card"
                        sx={{ border: `1px solid ${theme.palette.grey[300]}`, p: 2, boxShadow: 0 }}
                      >
                        <Stack direction="row" gap={2}>
                          <Avatar sx={{ width: 50, height: 50 }} src="student.image" />
                          <Stack gap={0.5}>
                            <Typography variant="subtitle2" fontSize={15}>
                              {student.studentName ? student.studentName : 'Student Name'}
                            </Typography>
                            <Typography variant="subtitle2" fontSize={12} display="flex" gap={0.75} alignItems="center">
                              <span className="date">Class</span>: {student.className} &nbsp;&nbsp;{' '}
                              <span className="date">Roll No</span>: {student.rollNo}
                            </Typography>
                          </Stack>
                        </Stack>
                        <Stack gap={1.5} mt={1.5}>
                          <Stack direction="row" justifyContent="space-between" flexWrap="wrap" rowGap={1.5}>
                            <Chip
                              size="small"
                              color="info"
                              sx={{ fontSize: 11 }}
                              label={`Applied on : ${dayjs(student.appliedDate).format('DD/MM/YY')}`}
                            />
                            {student.aprovedDate && (
                              <Chip
                                size="small"
                                color={student.status === 1 ? 'success' : 'error'}
                                sx={{ fontSize: 11 }}
                                label={
                                  student.status === 1
                                    ? `Approved on : ${dayjs(student.aprovedDate).format('DD/MM/YY')}`
                                    : `Rejected on : ${dayjs(student.aprovedDate).format('DD/MM/YY')}`
                                }
                              />
                            )}
                          </Stack>
                          <Stack
                            direction="row"
                            justifyContent="space-between"
                            flexWrap="wrap"
                            columnGap={1.5}
                            rowGap={1.5}
                          >
                            <Typography variant="subtitle2" fontSize={12} display="flex" gap={0.5} alignItems="center">
                              <CalendarTodayIcon sx={{ fontSize: 12 }} />
                              <span className="date">From</span>: {dayjs(student.fromDate).format('DD-MM-YYYY')}
                            </Typography>
                            <Typography variant="subtitle2" fontSize={12} display="flex" gap={0.5} alignItems="center">
                              <CalendarTodayIcon sx={{ fontSize: 12 }} />
                              <span className="date">To</span>: {dayjs(student.todate).format('DD-MM-YYYY')}
                            </Typography>
                          </Stack>
                          <Typography variant="subtitle2" fontSize={12}>
                            Subject: {student.subject}
                          </Typography>
                          <Card
                            sx={{
                              boxShadow: 0,
                              border: 1,
                              borderColor: theme.palette.grey[300],
                              px: 1,
                              borderRadius: 1,
                            }}
                          >
                            <Stack height={50} overflow="scroll">
                              <Typography variant="body1" color={theme.palette.grey[500]} fontSize={12}>
                                {student.description}
                              </Typography>
                            </Stack>
                          </Card>
                          <Box height={30}>
                            {student.status === 0 ? (
                              <Stack direction="row" gap={7} px={3}>
                                <LoadingButton
                                  fullWidth
                                  variant="contained"
                                  color="error"
                                  size="small"
                                  // loading={isSubmitting}
                                  onClick={() => handleReject(student.leaveId)}
                                >
                                  Reject
                                </LoadingButton>
                                <LoadingButton
                                  fullWidth
                                  variant="contained"
                                  color="success"
                                  size="small"
                                  // loading={isSubmitting}
                                  onClick={() => handleApprove(student.leaveId)}
                                >
                                  Approve
                                </LoadingButton>
                              </Stack>
                            ) : student.status === 1 ? (
                              <Chip
                                size="medium"
                                color="success"
                                sx={{ fontSize: 13, width: '100%', height: '100%' }}
                                icon={<SuccessIcon />}
                                label="Approved"
                              />
                            ) : (
                              <Chip
                                size="medium"
                                color="error"
                                sx={{ fontSize: 13, width: '100%', height: '100%' }}
                                icon={<CloseIcon />}
                                label="Rejected"
                              />
                            )}
                          </Box>
                        </Stack>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              ) : (
                <Box
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                  width="100%"
                  height={{ xs: 'calc(100vh - 400px)', sm: 'calc(100vh - 400px)', lg: 'calc(100vh - 284px)' }}
                >
                  <Stack direction="column" alignItems="center">
                    <img src={NoData} width="150px" alt="" />
                    <Typography variant="subtitle2" mt={2} color="GrayText">
                      No data found !
                    </Typography>
                  </Stack>
                </Box>
              )}
            </Box>
            <TablePagination
              rowsPerPageOptions={pageProps.rowsPerPageOptions}
              component="div"
              count={pageProps.totalRecords}
              rowsPerPage={pageProps.pageSize}
              page={pageProps.pageNumber}
              onPageChange={pageProps.onPageChange}
              onRowsPerPageChange={pageProps.onRowsPerPageChange}
              showFirstButton
              showLastButton
              // sx={{
              //   paddingRight: '30px',
              //   borderEndEndRadius: 20,
              //   borderBottomLeftRadius: 20,
              //   boxShadow: 0,
              //   backgroundColor: isLight ? theme.palette.common.white : theme.palette.grey[800],
              //   position: 'fixed',
              //   bottom: { xs: '0px', md: '16px' },
              //   right: { xs: '16px', md: '16px' },
              //   left: { xs: '16px', md: `calc(${SIDE_BAR_WIDTH} + 16px)` },
              // }}
            />
          </div>
        </Card>
      </LeaveNoteListRoot>
      <Popup
        size="sm"
        title="Leave Details Create"
        state={createOpen}
        onClose={() => setCreateOpen(false)}
        popupContent={<View onCancel={() => setCreateOpen(false)} />}
      />
    </Page>
  );
}

export default LeaveNoteList;
