import React, { ChangeEvent, FormEvent, useCallback, useEffect, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Divider,
  Paper,
  TextField,
  Typography,
  Card,
  IconButton,
  Box,
  Stack,
  Select,
  MenuItem,
  Button,
  Collapse,
  useTheme,
  Grid,
  FormControl,
  SelectChangeEvent,
  Checkbox,
} from '@mui/material';
import styled from 'styled-components';
import { useAppSelector } from '@/hooks/useAppSelector';
import {
  getYearData,
  getYearStatus,
  getQucikUpdateStudentListData,
  getQucikUpdateStudentListStatus,
  getStudentSubmitting,
  getClassData,
  getClassStatus,
} from '@/config/storeSelectors';
import SearchIcon from '@mui/icons-material/Search';
import ErrorIcon from '@/assets/ManageFee/ErrorIcon.json';
import Success from '@/assets/ManageFee/Success.json';
import SaveIcon from '@mui/icons-material/Save';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import useAuth from '@/hooks/useAuth';
import useSettings from '@/hooks/useSettings';
import { fetchClassList, fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import { ErrorMessage } from '@/components/shared/Popup/ErrorMessage';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import LoadingButton from '@mui/lab/LoadingButton';
import { QuickUpdateStudentListInfo } from '@/types/StudentManagement';
import { fetchQucickUpdateStudentList, qucickUpdate } from '@/store/Students/studentManagement.thunks';

const QuickUpdateStudentRoot = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      height: calc(100% - 40px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

function QuickUpdateStudent() {
  // const [forceDispatch, setForceDispatch] = useState(false);
  const theme = useTheme();
  const isLight = useSettings().themeMode === 'light';
  const { confirm } = useConfirm();
  const dispatch = useAppDispatch();
  const { user } = useAuth();
  const adminId: number = user ? user.accountId : 0;
  const YearData = useAppSelector(getYearData);

  const classStatus = useAppSelector(getClassStatus);
  const ClassData = useAppSelector(getClassData);
  const YearStatus = useAppSelector(getYearStatus);
  const defualtYear = YearData[0]?.accademicId || 0;

  const AllClassOption = {
    classId: -1,
    className: 'All Class',
    classDescription: 'string',
    classStatus: 1,
  };
  const classDataWithAllClass = [AllClassOption, ...ClassData];

  const qucikUpdateStudentListStatus = useAppSelector(getQucikUpdateStudentListStatus);
  const qucikUpdateStudentListData = useAppSelector(getQucikUpdateStudentListData);
  const isSubmitting = useAppSelector(getStudentSubmitting);
  // const deletingRecords = useAppSelector(getDeletingRecords);
  const [showFilter, setShowFilter] = useState(true);
  const [academicYearFilter, setAcademicYearFilter] = useState(defualtYear);
  const [classFilter, setClassFilter] = useState(classDataWithAllClass[0]);
  const [classSectionsFilter, setClassSectionsFilter] = useState(-1);
  const [selectedRows, setSelectedRows] = useState<Record<number, boolean>>({});
  const [editedRows, setEditedRows] = useState<QuickUpdateStudentListInfo[]>([]);
  const [isAllSelected, setIsAllSelected] = useState<boolean>(false);
  const [currentPage, setCurrentPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const { classId, className } = classFilter || {};

  const currentQuickUpdateListRequest = useMemo(
    () => ({
      academicId: academicYearFilter,
      classId,
    }),
    [classId, academicYearFilter]
  );

  const loadQuickUpdateList = useCallback(
    (request: { academicId: number; classId: number }) => {
      dispatch(fetchQucickUpdateStudentList(request));
    },
    [dispatch]
  );

  useEffect(() => {
    if (qucikUpdateStudentListStatus === 'idle') {
      loadQuickUpdateList(currentQuickUpdateListRequest);
      dispatch(fetchClassList(adminId));
      dispatch(fetchYearList(adminId));
    }
  }, [
    loadQuickUpdateList,
    qucikUpdateStudentListStatus,
    currentQuickUpdateListRequest,
    classSectionsFilter,
    academicYearFilter,
    adminId,
    dispatch,
  ]);

  const handleYearChange = (e: SelectChangeEvent) => {
    const selectedAcademicId = parseInt(e.target.value, 10);
    setAcademicYearFilter(selectedAcademicId);
    loadQuickUpdateList({
      ...currentQuickUpdateListRequest,
      academicId: selectedAcademicId,
    });
  };

  const handleClassChange = (e: SelectChangeEvent) => {
    const selectedClass = classDataWithAllClass.find((item) => item.className === e.target.value);
    if (selectedClass) {
      setClassFilter(selectedClass);
    }
    loadQuickUpdateList({
      ...currentQuickUpdateListRequest,
      classId: selectedClass ? selectedClass.classId : 0,
    });
  };

  const getRowKey = useCallback((row: QuickUpdateStudentListInfo) => row.studentId, []);

  const handleReset = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      setAcademicYearFilter(defualtYear);
      setClassFilter(classDataWithAllClass[0]);
      loadQuickUpdateList({
        ...currentQuickUpdateListRequest,
        academicId: defualtYear,
      });
    },
    [loadQuickUpdateList, currentQuickUpdateListRequest, classDataWithAllClass, defualtYear]
  );

  const classListUpdateColumns: DataTableColumn<QuickUpdateStudentListInfo>[] = useMemo(
    () => [
      {
        name: 'studentId',
        headerLabel: 'Sl.No',
        dataKey: 'studentId',
      },
      {
        name: 'admissionNumber',
        headerLabel: 'Adm No',
        dataKey: 'admissionNumber',
      },
      {
        name: 'studentName',
        dataKey: 'studentName',
        headerLabel: 'Class Name',
      },
      {
        name: 'fatherName',
        dataKey: 'fatherName',
        headerLabel: 'Father Name',
      },
      {
        name: 'fatherNumber',
        dataKey: 'fatherNumber',
        headerLabel: 'Father Number',
      },
    ],
    []
  );

  const handleUpdateAll = useCallback(async () => {
    if (editedRows.length < 1) return; // No rows to update

    try {
      const ConfirmMessage = (
        <SuccessMessage
          message={
            <Card sx={{ my: 1, boxShadow: 0, width: 500, border: 1, borderColor: theme.palette.grey[300] }}>
              <DataTable
                columns={classListUpdateColumns}
                data={editedRows}
                getRowKey={getRowKey}
                fetchStatus={qucikUpdateStudentListStatus}
              />
            </Card>
          }
        />
      );

      if (await confirm(ConfirmMessage, 'Update Rows?', { okLabel: 'Update', cancelLabel: 'Cancel' })) {
        const promises = editedRows.map(async (row) => {
          const updateReq = [
            {
              ...row,
              createdBy: adminId,
              dbResult: 'string',
            },
          ];
          console.log('Update request:', updateReq);
          const response = await dispatch(qucickUpdate(updateReq)).unwrap();
          return response;
        });

        const responses = await Promise.all(promises);

        const successfulResponses = responses.filter((response) => response !== null);
        const isSuccess = successfulResponses.length === responses.length && successfulResponses.every(Boolean);

        if (isSuccess) {
          setIsAllSelected(false);
          setSelectedRows([]);
          setEditedRows([]);
          loadQuickUpdateList({ ...currentQuickUpdateListRequest });
          await confirm(
            <SuccessMessage loop={false} jsonIcon={Success} message="Students updated successfully" />,
            'Student Updated',
            { okLabel: 'Ok', showOnlyOk: true }
          );
        } else {
          await confirm(
            <ErrorMessage loop={false} jsonIcon={ErrorIcon} message="Some updates failed." />,
            'Student Update',
            { okLabel: 'Ok', showOnlyOk: true }
          );
        }
      }
    } catch (error) {
      await confirm(
        <ErrorMessage loop={false} jsonIcon={ErrorIcon} message="Something went wrong. Please try again." />,
        'Student Update',
        { okLabel: 'Ok', showOnlyOk: true }
      );
      console.error('Error updating student:', error);
    }
  }, [
    dispatch,
    confirm,
    loadQuickUpdateList,
    qucikUpdateStudentListStatus,
    classListUpdateColumns,
    currentQuickUpdateListRequest,
    adminId,
    editedRows,
    getRowKey,
    theme,
  ]);

  // Calculate the paginated data
  const paginatedData = qucikUpdateStudentListData.slice(
    currentPage * rowsPerPage,
    currentPage * rowsPerPage + rowsPerPage
  );

  const handleRowAllClick = useCallback(
    (event: ChangeEvent<HTMLInputElement>) => {
      const isChecked = event.target.checked;
      setIsAllSelected(isChecked);

      // Delay the heavy update logic to the next paint cycle
      requestAnimationFrame(() => {
        const newSelectedRows = isChecked
          ? paginatedData.reduce((acc, item) => ({ ...acc, [item.studentId]: true }), {})
          : {};
        const newEditedRows = isChecked ? paginatedData : [];

        setSelectedRows(newSelectedRows);
        setEditedRows(newEditedRows);
      });
    },
    [qucikUpdateStudentListData]
  );

  const handleRowClick = (event: React.ChangeEvent<HTMLInputElement>, row: QuickUpdateStudentListInfo) => {
    const { checked } = event.target;

    requestAnimationFrame(() => {
      setSelectedRows((prev) => ({
        ...prev,
        [row.studentId]: checked,
      }));

      setEditedRows((prev) => {
        if (checked) {
          return prev.some((item) => item.studentId === row.studentId) ? prev : [...prev, row];
        }
        return prev.filter((item) => item.studentId !== row.studentId);
      });
    });
  };

  // Pagination handlers
  const handlePageChange = useCallback(
    (event: any, newPage: any) => {
      setCurrentPage(newPage); // Update current page
    },
    [setCurrentPage]
  );

  const handleChangeRowsPerPage = useCallback(
    (event: any) => {
      setRowsPerPage(parseInt(event.target.value, 10)); // Update rows per page
      // Reset to first page when changing rows per page
    },
    [setRowsPerPage]
  );

  const pageProps = useMemo(
    () => ({
      rowsPerPageOptions: [10, 20, 30, 50, 100, 200, 300, 500],
      pageNumber: currentPage,
      pageSize: rowsPerPage,
      totalRecords: qucikUpdateStudentListData.length,
      onPageChange: handlePageChange,
      onRowsPerPageChange: handleChangeRowsPerPage,
    }),
    [handleChangeRowsPerPage, handlePageChange, currentPage, rowsPerPage, qucikUpdateStudentListData]
  );
  const classListColumns: DataTableColumn<QuickUpdateStudentListInfo>[] = useMemo(
    () => [
      {
        name: 'checkBox',
        renderHeader: () => {
          return (
            <Checkbox
              disabled={isSubmitting}
              color="primary"
              onChange={handleRowAllClick}
              indeterminate={
                qucikUpdateStudentListData.length > 0 &&
                Object.values(selectedRows).some((checked) => checked) &&
                !isAllSelected
              }
              // indeterminate={
              //   Object.keys(selectedRows).length > 0 && Object.keys(selectedRows).length < qucikUpdateStudentListData.length
              // }
              checked={isAllSelected}
            />
          );
        },
        renderCell: (row) => {
          const isRowSelected = selectedRows[row.studentId] || false;
          return (
            <Checkbox
              disabled={isSubmitting}
              color="primary"
              checked={isRowSelected}
              onChange={(e) => {
                handleRowClick(e, row);
              }}
              inputProps={{ 'aria-labelledby': `checkbox-${row}` }}
            />
          );
        },
      },
      {
        name: 'studentId',
        headerLabel: 'Sl.No',
        dataKey: 'studentId',
      },
      {
        name: 'admissionNumber',
        headerLabel: 'Adm No',
        renderCell: (row) => {
          const isRowSelected = selectedRows[row.studentId] || false;

          return !isRowSelected ? (
            <Typography variant="subtitle1" minWidth={100}>{row.admissionNumber}</Typography>
          ) : (
            <TextField
              sx={{ minWidth: 100 }}
              disabled={isSubmitting}
              defaultValue={row.admissionNumber}
              onChange={(e) => {
                setEditedRows((prev) =>
                  prev.map((item) =>
                    item.studentId === row.studentId ? { ...item, admissionNumber: e.target.value } : item
                  )
                );
              }}
              placeholder="Enter class"
            />
          );
        },
      },
      {
        name: 'studentName',
        headerLabel: 'Student Name',
        renderCell: (row) => {
          const isRowSelected = selectedRows[row.studentId] || false;

          return !isRowSelected ? (
            <Typography variant="subtitle1" minWidth={200}>{row.studentName}</Typography>
          ) : (
            <TextField
              sx={{ minWidth: 200 }}
              disabled={isSubmitting}
              defaultValue={row.studentName}
              onChange={(e) => {
                setEditedRows((prev) =>
                  prev.map((item) =>
                    item.studentId === row.studentId ? { ...item, studentName: e.target.value } : item
                  )
                );
              }}
              placeholder="Enter student name"
            />
          );
        },
      },
      {
        name: 'fatherName',
        headerLabel: 'Father Name',
        renderCell: (row) => {
          const isRowSelected = selectedRows[row.studentId] || false;

          return !isRowSelected ? (
            <Typography variant="subtitle1" minWidth={200}>{row.fatherName}</Typography>
          ) : (
            <TextField
              sx={{ minWidth: 200 }}
              disabled={isSubmitting}
              defaultValue={row.fatherName}
              onChange={(e) => {
                setEditedRows((prev) =>
                  prev.map((item) =>
                    item.studentId === row.studentId ? { ...item, fatherName: e.target.value } : item
                  )
                );
              }}
              placeholder="Enter father name"
            />
          );
        },
      },
      {
        name: 'fatherNumber',
        headerLabel: 'Father Number',
        renderCell: (row) => {
          const isRowSelected = selectedRows[row.studentId] || false;

          return !isRowSelected ? (
            <Typography variant="subtitle1" minWidth={120}>{row.fatherNumber}</Typography>
          ) : (
            <TextField
              sx={{ minWidth: 120 }}
              disabled={isSubmitting}
              defaultValue={row.fatherNumber}
              onChange={(e) => {
                setEditedRows((prev) =>
                  prev.map((item) =>
                    item.studentId === row.studentId ? { ...item, fatherNumber: e.target.value } : item
                  )
                );
              }}
              placeholder="Enter class"
            />
          );
        },
      },
      {
        name: 'motherName',
        headerLabel: 'Mother Name',
        renderCell: (row) => {
          const isRowSelected = selectedRows[row.studentId] || false;

          return !isRowSelected ? (
            <Typography variant="subtitle1" minWidth={150}>{row.motherName}</Typography>
          ) : (
            <TextField
              sx={{ minWidth: 200 }}
              disabled={isSubmitting}
              defaultValue={row.motherName}
              onChange={(e) => {
                setEditedRows((prev) =>
                  prev.map((item) =>
                    item.studentId === row.studentId ? { ...item, motherName: e.target.value } : item
                  )
                );
              }}
              placeholder="Enter class"
            />
          );
        },
      },
      {
        name: 'motherNumber',
        headerLabel: 'Mother Number',
        renderCell: (row) => {
          const isRowSelected = selectedRows[row.studentId] || false;

          return !isRowSelected ? (
            <Typography variant="subtitle1" minWidth={120}>{row.motherNumber}</Typography>
          ) : (
            <TextField
              sx={{ minWidth: 120 }}
              disabled={isSubmitting}
              defaultValue={row.motherNumber}
              onChange={(e) => {
                setEditedRows((prev) =>
                  prev.map((item) =>
                    item.studentId === row.studentId ? { ...item, motherNumber: e.target.value } : item
                  )
                );
              }}
              placeholder="Enter mother's number"
            />
          );
        },
      },
      {
        name: 'aadharNumber',
        headerLabel: 'Aadhar Number',
        renderCell: (row) => {
          const isRowSelected = selectedRows[row.studentId] || false;

          return !isRowSelected ? (
            <Typography variant="subtitle1" minWidth={150}>{row.aadharNumber}</Typography>
          ) : (
            <TextField
              sx={{ minWidth: 200 }}
              disabled={isSubmitting}
              defaultValue={row.aadharNumber}
              onChange={(e) => {
                setEditedRows((prev) =>
                  prev.map((item) =>
                    item.studentId === row.studentId ? { ...item, aadharNumber: e.target.value } : item
                  )
                );
              }}
              placeholder="Enter Aadhar number"
            />
          );
        },
      },
      {
        name: 'religion',
        headerLabel: 'Religion',
        renderCell: (row) => {
          const isRowSelected = selectedRows[row.studentId] || false;

          return !isRowSelected ? (
            <Typography variant="subtitle1" minWidth={150}>{row.religion}</Typography>
          ) : (
            <TextField
              sx={{ minWidth: 200 }}
              disabled={isSubmitting}
              defaultValue={row.religion}
              onChange={(e) => {
                setEditedRows((prev) =>
                  prev.map((item) => (item.studentId === row.studentId ? { ...item, religion: e.target.value } : item))
                );
              }}
              placeholder="Enter religion"
            />
          );
        },
      },
      {
        name: 'caste',
        headerLabel: 'Caste',
        renderCell: (row) => {
          const isRowSelected = selectedRows[row.studentId] || false;

          return !isRowSelected ? (
            <Typography variant="subtitle1" minWidth={150}>{row.caste}</Typography>
          ) : (
            <TextField
              sx={{ minWidth: 200 }}
              disabled={isSubmitting}
              defaultValue={row.caste}
              onChange={(e) => {
                setEditedRows((prev) =>
                  prev.map((item) => (item.studentId === row.studentId ? { ...item, caste: e.target.value } : item))
                );
              }}
              placeholder="Enter caste"
            />
          );
        },
      },
      {
        name: 'category',
        headerLabel: 'Category',
        renderCell: (row) => {
          const isRowSelected = selectedRows[row.studentId] || false;

          return !isRowSelected ? (
            <Typography variant="subtitle1" minWidth={150}>{row.category}</Typography>
          ) : (
            <TextField
              sx={{ minWidth: 200 }}
              disabled={isSubmitting}
              defaultValue={row.category}
              onChange={(e) => {
                setEditedRows((prev) =>
                  prev.map((item) => (item.studentId === row.studentId ? { ...item, category: e.target.value } : item))
                );
              }}
              placeholder="Enter category"
            />
          );
        },
      },
      {
        name: 'motherTongue',
        headerLabel: 'Mother Tongue',
        renderCell: (row) => {
          const isRowSelected = selectedRows[row.studentId] || false;

          return !isRowSelected ? (
            <Typography variant="subtitle1" minWidth={150}>{row.motherTongue}</Typography>
          ) : (
            <TextField
              sx={{ minWidth: 200 }}
              disabled={isSubmitting}
              defaultValue={row.motherTongue}
              onChange={(e) => {
                setEditedRows((prev) =>
                  prev.map((item) =>
                    item.studentId === row.studentId ? { ...item, motherTongue: e.target.value } : item
                  )
                );
              }}
              placeholder="Enter mother tongue"
            />
          );
        },
      },
      {
        name: 'studentIDNumber',
        headerLabel: 'Student ID Number',
        renderCell: (row) => {
          const isRowSelected = selectedRows[row.studentId] || false;

          return !isRowSelected ? (
            <Typography variant="subtitle1" minWidth={150}>{row.studentIDNumber}</Typography>
          ) : (
            <TextField
              sx={{ minWidth: 200 }}
              disabled={isSubmitting}
              defaultValue={row.studentIDNumber}
              onChange={(e) => {
                setEditedRows((prev) =>
                  prev.map((item) =>
                    item.studentId === row.studentId ? { ...item, studentIDNumber: e.target.value } : item
                  )
                );
              }}
              placeholder="Enter student ID number"
            />
          );
        },
      },
      {
        name: 'emailId',
        headerLabel: 'Email ID',
        renderCell: (row) => {
          const isRowSelected = selectedRows[row.studentId] || false;

          return !isRowSelected ? (
            <Typography variant="subtitle1" minWidth={150}>{row.emailId}</Typography>
          ) : (
            <TextField
              sx={{ minWidth: 200 }}
              disabled={isSubmitting}
              defaultValue={row.emailId}
              onChange={(e) => {
                setEditedRows((prev) =>
                  prev.map((item) => (item.studentId === row.studentId ? { ...item, emailId: e.target.value } : item))
                );
              }}
              placeholder="Enter email ID"
            />
          );
        },
      },
      {
        name: 'placeOfBirth',
        headerLabel: 'Place Of Birth',
        renderCell: (row) => {
          const isRowSelected = selectedRows[row.studentId] || false;

          return !isRowSelected ? (
            <Typography variant="subtitle1" minWidth={150}>{row.placeOfBirth}</Typography>
          ) : (
            <TextField
              sx={{ minWidth: 200 }}
              disabled={isSubmitting}
              defaultValue={row.placeOfBirth}
              onChange={(e) => {
                setEditedRows((prev) =>
                  prev.map((item) =>
                    item.studentId === row.studentId ? { ...item, placeOfBirth: e.target.value } : item
                  )
                );
              }}
              placeholder="Enter place of birth"
            />
          );
        },
      },
      {
        name: 'taluka',
        headerLabel: 'Taluka',
        renderCell: (row) => {
          const isRowSelected = selectedRows[row.studentId] || false;

          return !isRowSelected ? (
            <Typography variant="subtitle1" minWidth={150}>{row.taluka}</Typography>
          ) : (
            <TextField
              sx={{ minWidth: 200 }}
              disabled={isSubmitting}
              defaultValue={row.taluka}
              onChange={(e) => {
                setEditedRows((prev) =>
                  prev.map((item) => (item.studentId === row.studentId ? { ...item, taluka: e.target.value } : item))
                );
              }}
              placeholder="Enter taluka"
            />
          );
        },
      },
      {
        name: 'district',
        headerLabel: 'District',
        renderCell: (row) => {
          const isRowSelected = selectedRows[row.studentId] || false;

          return !isRowSelected ? (
            <Typography variant="subtitle1" minWidth={150}>{row.district}</Typography>
          ) : (
            <TextField
              sx={{ minWidth: 200 }}
              disabled={isSubmitting}
              defaultValue={row.district}
              onChange={(e) => {
                setEditedRows((prev) =>
                  prev.map((item) => (item.studentId === row.studentId ? { ...item, district: e.target.value } : item))
                );
              }}
              placeholder="Enter district"
            />
          );
        },
      },
      {
        name: 'state',
        headerLabel: 'State',
        renderCell: (row) => {
          const isRowSelected = selectedRows[row.studentId] || false;

          return !isRowSelected ? (
            <Typography variant="subtitle1" minWidth={150}>{row.state}</Typography>
          ) : (
            <TextField
              sx={{ minWidth: 200 }}
              disabled={isSubmitting}
              defaultValue={row.state}
              onChange={(e) => {
                setEditedRows((prev) =>
                  prev.map((item) => (item.studentId === row.studentId ? { ...item, state: e.target.value } : item))
                );
              }}
              placeholder="Enter state"
            />
          );
        },
      },
      {
        name: 'nationality',
        headerLabel: 'Nationality',
        renderCell: (row) => {
          const isRowSelected = selectedRows[row.studentId] || false;

          return !isRowSelected ? (
            <Typography variant="subtitle1" minWidth={150}>{row.nationality}</Typography>
          ) : (
            <TextField
              sx={{ minWidth: 200 }}
              disabled={isSubmitting}
              defaultValue={row.nationality}
              onChange={(e) => {
                setEditedRows((prev) =>
                  prev.map((item) =>
                    item.studentId === row.studentId ? { ...item, nationality: e.target.value } : item
                  )
                );
              }}
              placeholder="Enter nationality"
            />
          );
        },
      },
      {
        name: 'address',
        headerLabel: 'Address',
        renderCell: (row) => {
          const isRowSelected = selectedRows[row.studentId] || false;

          return !isRowSelected ? (
            <Typography variant="subtitle1" minWidth={150}>{row.address}</Typography>
          ) : (
            <TextField
              sx={{ minWidth: 200 }}
              disabled={isSubmitting}
              defaultValue={row.address}
              onChange={(e) => {
                setEditedRows((prev) =>
                  prev.map((item) => (item.studentId === row.studentId ? { ...item, address: e.target.value } : item))
                );
              }}
              placeholder="Enter address"
            />
          );
        },
      },
    ],
    [selectedRows, isAllSelected, isSubmitting, qucikUpdateStudentListData, handleRowAllClick]
  );

  return (
    <Page title="Quick Update">
      <QuickUpdateStudentRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack pb={1} direction="row" justifyContent="space-between" alignItems="center" flexWrap="wrap">
            <Stack direction="row" alignItems="center" justifyContent="space-between" flex={1} whiteSpace="nowrap">
              <Typography variant="h6" fontSize={17}>
                Quick Update
              </Typography>
              <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                <SearchIcon />
              </IconButton>
            </Stack>
            <Box
              sx={{ flexShrink: 0 }}
              display="flex"
              justifyContent="end"
              // width={{ xs: '100%', sm: 'fit-content' }}
            >
              <LoadingButton
                loading={isSubmitting}
                loadingPosition="start"
                disabled={editedRows.length === 0 || isSubmitting}
                // disabled={
                //   selectedRows.every((selectedRow) => qucikUpdateStudentListData.some((row) => row === selectedRow)) ||
                //   isSubmitting
                // }
                variant="contained"
                startIcon={<SaveIcon />}
                size="small"
                sx={{ width: 'fit-content' }}
                onClick={handleUpdateAll}
              >
                {isSubmitting ? 'Updating...' : 'Update All'}
              </LoadingButton>
            </Box>
          </Stack>

          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate onReset={handleReset}>
                <Grid pb={2} pt={1} container spacing={3} alignItems="end">
                  <Grid item xxl={2} xl={2.5} lg={3} md={3} sm={3.3} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" minWidth={150} fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Select
                        disabled={isSubmitting}
                        labelId="academicYearFilter"
                        id="academicYearFilterSelect"
                        value={academicYearFilter?.toString()}
                        // defaultValue={YearData[0]?.accademicId}
                        onChange={handleYearChange}
                        placeholder="Select Year"
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Year
                        </MenuItem>
                        {YearData.map((opt) => (
                          <MenuItem key={opt.accademicId} value={opt.accademicId}>
                            {opt.accademicTime}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={3} md={3} sm={3.3} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" minWidth={150} fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Select
                        labelId="classFilter"
                        id="classFilter"
                        value={className}
                        onChange={handleClassChange}
                        placeholder="Select Class"
                        sx={{
                          '& .MuiInputBase-input.Mui-disabled': {
                            backgroundColor: theme.palette.grey[200],
                          },
                        }}
                        MenuProps={{
                          PaperProps: {
                            style: {
                              maxHeight: '270px', // Adjust the value to your desired height
                            },
                          },
                        }}
                      >
                        {classDataWithAllClass?.map((opt) => (
                          <MenuItem key={opt.classId} value={opt.className}>
                            {opt.className}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12} sm={6}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      {/* <Button type="submit" variant="contained" color="primary" fullWidth>
                        Search
                      </Button> */}
                      <Button disabled={isSubmitting} type="reset" variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>
            <Paper className="card-table-container" sx={{ marginTop: '12px' }}>
              <DataTable
                // ShowCheckBox
                // setSelectedRows={setSelectedRows}
                // selectedRows={selectedRows}
                showHorizontalScroll
                tableStyles={{ minWidth: { xs: '800px' } }}
                columns={classListColumns}
                data={paginatedData}
                getRowKey={getRowKey}
                fetchStatus={qucikUpdateStudentListStatus}
                PaginationProps={pageProps}
                allowPagination
              />
            </Paper>
          </div>
        </Card>
      </QuickUpdateStudentRoot>
    </Page>
  );
}

export default QuickUpdateStudent;
