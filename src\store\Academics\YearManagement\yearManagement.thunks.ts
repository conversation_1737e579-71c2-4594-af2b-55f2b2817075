import api from '@/api';
import { YearCreateRequest, YearListInfo, YearListPagedData, YearListRequest } from '@/types/AcademicManagement';
import { CreateResponse, DeleteResponse, UpdateResponse } from '@/types/Common';
import { createAsyncThunk } from '@reduxjs/toolkit';

export const fetchYearList = createAsyncThunk<YearListPagedData, YearListRequest, { rejectValue: string }>(
  'YearManagement/list',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.YearManagement.GetYearList(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in fetching Year List');
    }
  }
);
export const fetchYearLists = createAsyncThunk<any, any, { rejectValue: string }>(
  'YearManagement/lists',
  async (adminId, { rejectWithValue }) => {
    try {
      const response = await api.YearManagement.YearList(adminId);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in fetching Year List');
    }
  }
);

export const addNewYear = createAsyncThunk<CreateResponse, YearCreateRequest, { rejectValue: string }>(
  'YearManagement/addnew',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.YearManagement.AddNewYear(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in adding new Year');
    }
  }
);

export const updateYear = createAsyncThunk<UpdateResponse, YearListInfo, { rejectValue: string }>(
  'YearManagement/update',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.YearManagement.UpdateYear(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in updating Year');
    }
  }
);

export const deleteYear = createAsyncThunk<DeleteResponse, number, { rejectValue: string }>(
  'YearManagement/delete',
  async (academicId, { rejectWithValue }) => {
    try {
      const response = await api.YearManagement.DeleteYear(academicId);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in deleting Year');
    }
  }
);
