import api from '@/api';
import {
  QuickUpdateStudentListInfo,
  StudentCreateRequest,
  StudentCreateRow,
  StudentListInfo,
  StudentListPagedData,
  StudentListRequest,
} from '@/types/StudentManagement';
import { CreateResponse, CreateResponseMulti, DeleteResponse, UpdateResponse } from '@/types/Common';
import { createAsyncThunk } from '@reduxjs/toolkit';

export const fetchStudentList = createAsyncThunk<StudentListPagedData, StudentListRequest, { rejectValue: string }>(
  'StudentManagement/list',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.StudentManagement.GetStudentList(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in fetching Student List');
    }
  }
);
export const fetchStudentLists = createAsyncThunk<any, any, { rejectValue: string }>(
  'StudentManagement/lists',
  async (adminId, { rejectWithValue }) => {
    try {
      const response = await api.StudentManagement.StudentList(adminId);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in fetching Student List');
    }
  }
);

export const addNewStudent = createAsyncThunk<CreateResponse, StudentCreateRequest, { rejectValue: string }>(
  'StudentManagement/addnew',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.StudentManagement.AddNewStudent(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in adding new Student');
    }
  }
);
export const addNewMultipleStudent = createAsyncThunk<CreateResponseMulti, StudentCreateRow[], { rejectValue: string }>(
  'StudentManagement/addnewmultiple',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.StudentManagement.AddNewMultipleStudent(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in adding multiple Student');
    }
  }
);

export const updateStudent = createAsyncThunk<UpdateResponse, StudentListInfo, { rejectValue: string }>(
  'StudentManagement/update',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.StudentManagement.UpdateStudent(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in updating Student');
    }
  }
);

export const deleteStudent = createAsyncThunk<DeleteResponse, number, { rejectValue: string }>(
  'StudentManagement/delete',
  async (studentId, { rejectWithValue }) => {
    try {
      const response = await api.StudentManagement.DeleteStudent(studentId);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in deleting Student');
    }
  }
);

export const fetchQucickUpdateStudentList = createAsyncThunk<
  QuickUpdateStudentListInfo[],
  {
    academicId: number | undefined;
    classId: number | undefined;
  },
  { rejectValue: string }
>('StudentManagement/getQucickUpdateStudentList ', async ({ academicId, classId }, { rejectWithValue }) => {
  try {
    const response = await api.StudentManagement.GetQucickUpdateStudentList(academicId, classId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching Manage Fee Get Optional Fee Settings');
  }
});

export const qucickUpdate = createAsyncThunk<UpdateResponse, QuickUpdateStudentListInfo, { rejectValue: string }>(
  'StudentManagement/qucickUpdate',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.StudentManagement.QucickUpdate(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in updating Quick Update Student');
    }
  }
);
