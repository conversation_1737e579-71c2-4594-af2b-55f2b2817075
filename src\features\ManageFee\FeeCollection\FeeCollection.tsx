/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/alt-text */
import React, { FormEvent, useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  Button,
  Typography,
  Card,
  Avatar,
  IconButton,
  Collapse,
  Select,
  MenuItem,
  FormControl,
  useTheme,
  SelectChangeEvent,
  Tooltip,
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import { IoIosArrowUp } from 'react-icons/io';
import styled from 'styled-components';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import useSettings from '@/hooks/useSettings';
import useAuth from '@/hooks/useAuth';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useAppSelector } from '@/hooks/useAppSelector';
import {
  getClassSectionsData,
  getClassSectionsStatus,
  getManageFeeclassListData,
  getYearData,
  getstudentsFeeStatusListData,
  getstudentsFeeStatusListStatus,
} from '@/config/storeSelectors';
import { fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import { fetchClassList, fetchClassSections, fetchStudentsFeeStatus } from '@/store/ManageFee/manageFee.thunks';
import { StudentFeeStatusDataType } from '@/types/ManageFee';
import PayFeeCollection from './PayFeeCollection/PayFeeCollection';
import FeesDetails from '../FeesDetails/FeesDetails';

const FeeCollectionRoot = styled.div`
  padding: 1rem;
  @media screen and (max-width: 576px) {
    padding: 0.5rem;
  }
  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 576px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 50px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        /* border: 1px solid ${(props) => props.theme.palette.grey[200]}; */
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

type FeeCollectionProps = {
  handlePayView: () => void;
  handlePayView2: () => void;
};

export default function FeeCollection({ handlePayView, handlePayView2 }: FeeCollectionProps) {
  const theme = useTheme();
  const isLight = useSettings().themeMode === 'light';
  const { user } = useAuth();
  const dispatch = useAppDispatch();
  const adminId: number = user ? user.accountId : 0;
  const academicId = 10;
  const [showFeeCollectionPage, setShowFeeCollectionPage] = useState<
    'FeeCollection' | 'PayFeeCollection1' | 'PayFeeCollection2'
  >('FeeCollection');
  const [studentFeeStatusData, setStudentFeeStatusData] = useState<StudentFeeStatusDataType | undefined>(undefined);

  const [showFilter, setShowFilter] = useState(true);

  const YearData = useAppSelector(getYearData);
  const classSectionsStatus = useAppSelector(getClassSectionsStatus);
  const [classSectionsFilter, setClassSectionsFilter] = useState(0);
  const [classFilter, setClassFilter] = useState(0);
  const ClassSectionsData = useAppSelector(getClassSectionsData);
  const classListData = useAppSelector(getManageFeeclassListData);
  // const classListStatus = useAppSelector(getclassListStatus);

  const defaultYear = YearData[0]?.accademicId || 0;
  const [academicYearFilter, setAcademicYearFilter] = useState(defaultYear);

  const studentsFeeStatusListData = useAppSelector(getstudentsFeeStatusListData);
  const studentsFeeStatusListStatus = useAppSelector(getstudentsFeeStatusListStatus);

  const [feeCollectionData, setFeeCollectionData] = useState<StudentFeeStatusDataType[]>([]);

  const initialFeeCollectionRequest = React.useMemo(
    () => ({
      adminId,
      academicId: academicYearFilter,
      sectionId: 0,
      classId: 0,
    }),
    [adminId, academicYearFilter]
  );
  const currentFeeCollectionRequest = React.useMemo(
    () => ({
      adminId,
      academicId: academicYearFilter,
      sectionId: classSectionsFilter,
      classId: classFilter,
    }),
    [adminId, academicYearFilter, classSectionsFilter, classFilter]
  );

  const loadFeeCollectionList = useCallback(
    async (request: { adminId: number; academicId: number; sectionId: number; classId: number }) => {
      try {
        const data = await dispatch(fetchStudentsFeeStatus(request)).unwrap();
        console.log('data::::', data);
      } catch (error) {
        console.error('Error loading term fee list:', error);
      }
    },
    [dispatch]
  );
  // const loadClassListData = useCallback(
  //   async (request: { adminId: number; academicId: number; sectionId: number }) => {
  //     try {
  //       const data = await dispatch(fetchClassList(request)).unwrap();
  //       console.log('fetchClassListdata::::', data);
  //     } catch (error) {
  //       console.error('Error loading term fee list:', error);
  //     }
  //   },
  //   [dispatch]
  // );

  React.useEffect(() => {
    dispatch(fetchYearList(adminId));
    dispatch(fetchClassSections({ adminId, academicId: academicYearFilter }));
    dispatch(fetchClassList({ adminId, academicId: academicYearFilter, sectionId: classSectionsFilter }));
    // if (studentsFeeStatusListStatus === 'idle') {
    //     loadFeeCollectionList(initialFeeCollectionRequest);
    // }
    setFeeCollectionData(studentsFeeStatusListData);
  }, [
    dispatch,
    adminId,
    initialFeeCollectionRequest,
    loadFeeCollectionList,
    classSectionsFilter,
    studentsFeeStatusListStatus,
    studentsFeeStatusListData,
    academicYearFilter,
  ]);

  const handleReset = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      loadFeeCollectionList(initialFeeCollectionRequest);
      setAcademicYearFilter(0);
      setClassSectionsFilter(0);
      setClassFilter(0);
      // dispatch(fetchFeeDateSettings({ adminId, academicId: 10, sectionId: 0 }));
    },
    [initialFeeCollectionRequest, loadFeeCollectionList]
  );

  const handleYearChange = (e: SelectChangeEvent) => {
    setAcademicYearFilter(parseInt(YearData.filter((item) => item.accademicId === e.target.value)[0].accademicId, 10));
    loadFeeCollectionList({ ...currentFeeCollectionRequest, academicId: parseInt(e.target.value, 10) });
    setClassFilter(0);
  };

  const handleClassSectionChange = (e: SelectChangeEvent) => {
    setClassSectionsFilter(
      parseInt(ClassSectionsData.filter((item) => item.sectionId === e.target.value)[0].sectionId, 10)
    );
    loadFeeCollectionList({ ...currentFeeCollectionRequest, sectionId: parseInt(e.target.value, 10) });
    setClassFilter(0);
  };

  const handleClassChange = (e: SelectChangeEvent) => {
    setClassFilter(parseInt(classListData.filter((item) => item.classId === e.target.value)[0].classId, 10));
    loadFeeCollectionList({ ...currentFeeCollectionRequest, classId: parseInt(e.target.value, 10) });
  };

  const handleShowPayCollections = useCallback((row: StudentFeeStatusDataType) => {
    setStudentFeeStatusData(row);
    setShowFeeCollectionPage('PayFeeCollection1');
  }, []);

  const handleShowPayCollections2 = useCallback((row: StudentFeeStatusDataType) => {
    setStudentFeeStatusData(row);
    setShowFeeCollectionPage('PayFeeCollection2');
  }, []);

  const feeCollectionListColumns: DataTableColumn<StudentFeeStatusDataType>[] = useMemo(
    () => [
      {
        name: 'slNo',
        headerLabel: 'Sl.No',
        renderCell: (_, index) => {
          return (
            <Typography variant="subtitle1" fontSize={13}>
              {index + 1}
            </Typography>
          );
        },
        sortable: true,
      },
      {
        name: 'studentName',
        headerLabel: 'Student Name',
        renderCell: (row) => {
          return (
            <Stack direction="row" alignItems="center" gap={1}>
              <Avatar src="" />
              <Typography variant="subtitle2" fontSize={13}>
                {row.studentName}
              </Typography>
            </Stack>
          );
        },
      },
      {
        name: 'admNo',
        headerLabel: 'Admission No.',
        dataKey: 'admissionNo',
      },
      {
        name: 'classDiv',
        dataKey: 'className',
        headerLabel: 'Class',
      },
      {
        name: 'prvYearPending',
        headerLabel: 'Prev Year Pending',
        dataKey: 'prvYearPending',
      },
      {
        name: 'totalFees',
        headerLabel: 'Total Fees',
        dataKey: 'totalFee',
      },
      {
        name: 'paid',
        headerLabel: 'Paid',
        renderCell: (row) => {
          return (
            <Typography color="success" sx={{ color: theme.palette.success.main }} variant="subtitle1">
              {row.paid}
            </Typography>
          );
        },
      },
      {
        name: 'balance',
        headerLabel: 'Balance',
        renderCell: (row) => {
          return (
            <Typography color="error" variant="subtitle1" fontSize={14}>
              {row.balance}
            </Typography>
          );
        },
      },
      {
        name: 'action',
        headerLabel: 'Action',
        renderCell: (row) => {
          return (
            <Stack direction="row" gap={1}>
              <Button
                onClick={() => handleShowPayCollections(row)}
                variant="contained"
                size="small"
                color="primary"
                sx={{ py: 0.1, borderRadius: 10 }}
              >
                Pay
              </Button>
              {/* <Button
                onClick={() => handleShowPayCollections2(row)}
                variant="contained"
                size="small"
                color="success"
                sx={{ py: 0.1, borderRadius: 10 }}
              >
                Pay
              </Button> */}
            </Stack>
          );
        },
      },
    ],
    [theme.palette.success.main, handleShowPayCollections]
  );

  const getRowKey = useCallback((row: StudentFeeStatusDataType) => row.studentId, []);

  return showFeeCollectionPage === 'FeeCollection' ? (
    <Page title="Fees Collection">
      <FeeCollectionRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17}>
              Fees Collection
            </Typography>
            <Box pb={1} sx={{ flexShrink: 0 }}>
              {showFilter === false ? (
                <Tooltip title="Search">
                  <IconButton
                    aria-label="delete"
                    color="primary"
                    sx={{ mr: { xs: 0, sm: 1 } }}
                    onClick={() => setShowFilter((x) => !x)}
                  >
                    <SearchIcon />
                  </IconButton>
                </Tooltip>
              ) : (
                <IconButton
                  aria-label="delete"
                  color="primary"
                  sx={{ mr: { xs: 0, sm: 1 } }}
                  onClick={() => setShowFilter((x) => !x)}
                >
                  <IoIosArrowUp />
                </IconButton>
              )}
            </Box>
          </Stack>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate onReset={handleReset}>
                <Grid pb={4} container spacing={2} alignItems="end">
                  <Grid item xxl={2} xl={2.5} lg={3} md={3} sm={3.3} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Select
                        labelId="academicYearFilter"
                        id="academicYearFilterSelect"
                        value={academicYearFilter?.toString()}
                        onChange={handleYearChange}
                        placeholder="Select Year"
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Year
                        </MenuItem>
                        {YearData.map((opt) => (
                          <MenuItem key={opt.accademicId} value={opt.accademicId}>
                            {opt.accademicTime}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={3} md={3} sm={3.3} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Section
                      </Typography>
                      <Select
                        labelId="classSectionsFilter"
                        id="classSectionsFilterSelect"
                        value={classSectionsFilter?.toString()}
                        onChange={handleClassSectionChange}
                        placeholder="Select Section"
                        MenuProps={{
                          PaperProps: {
                            style: {
                              maxHeight: '270px', // Adjust the value to your desired height
                            },
                          },
                        }}
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Section
                        </MenuItem>
                        {ClassSectionsData.map((opt) => (
                          <MenuItem key={opt.sectionId} value={opt.sectionId}>
                            {opt.sectionName}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={3} md={3} sm={3.3} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Select
                        disabled={classListData.length === 0}
                        labelId="classFilter"
                        id="classFilter"
                        value={classFilter?.toString()}
                        onChange={handleClassChange}
                        placeholder="Select Class"
                        sx={{
                          '& .MuiInputBase-input.Mui-disabled': {
                            backgroundColor: theme.palette.grey[200],
                          },
                        }}
                        MenuProps={{
                          PaperProps: {
                            style: {
                              maxHeight: '270px', // Adjust the value to your desired height
                            },
                          },
                        }}
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Class
                        </MenuItem>
                        {classListData.map((opt) => (
                          <MenuItem key={opt.classId} value={opt.classId}>
                            {opt.className}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  {/* <Grid item lg={2} md={6} sm={6} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Date
                      </Typography>
                      <DatePickers name="messageDateFilter" value="" />
                    </FormControl>
                  </Grid> */}

                  <Grid item lg="auto">
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      {/* <Button type="submit" variant="contained" color="primary" fullWidth>
                        Search
                      </Button> */}
                      <Button type="reset" variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>
            <Paper
              className="card-table-container"
              sx={{
                marginTop: '12px',
                border: feeCollectionData.length !== 0 ? `2px solid ${theme.palette.grey[200]} ` : '',
              }}
            >
              <DataTable
                tableStyles={{ minWidth: { xs: '1250px', xxl: '100%' } }}
                columns={feeCollectionListColumns}
                data={feeCollectionData}
                getRowKey={getRowKey}
                fetchStatus={studentsFeeStatusListStatus}
                showHorizontalScroll
              />
            </Paper>
          </div>
        </Card>
      </FeeCollectionRoot>
    </Page>
  ) : showFeeCollectionPage === 'PayFeeCollection1' ? (
    studentFeeStatusData && (
      <PayFeeCollection
        studentId={studentFeeStatusData.studentId}
        sectionId={studentFeeStatusData.sectionId}
        classId={studentFeeStatusData.classId}
        studentDetails={studentFeeStatusData}
        academicId={academicYearFilter}
        onBackClick={() => setShowFeeCollectionPage('FeeCollection')}
      />
    )
  ) : (
    studentFeeStatusData && (
      <FeesDetails student={studentFeeStatusData} onBackClick={() => setShowFeeCollectionPage('FeeCollection')} />
    )
  );
}
