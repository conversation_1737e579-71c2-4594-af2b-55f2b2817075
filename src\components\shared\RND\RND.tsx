/* eslint-disable no-else-return */
/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/alt-text */
import React, { FormEvent, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  Button,
  Typography,
  Card,
  Avatar,
  IconButton,
  Collapse,
  Select,
  MenuItem,
  FormControl,
  useTheme,
  SelectChangeEvent,
  Tooltip,
  Radio,
  FormHelperText,
  Chip,
} from '@mui/material';
import NoData from '@/assets/no-datas.png';
import DeleteIcon from '@mui/icons-material/Delete';
import SaveIcon from '@mui/icons-material/Save';
import Success from '@/assets/ManageFee/Success.json';
import Updated from '@/assets/ManageFee/Updated.json';
import UpdateLoading from '@/assets/ManageFee/UpdateLoading.json';
import SaveLoading from '@/assets/ManageFee/SaveLoading.json';
import deleteBin from '@/assets/ManageFee/deleteBin.json';
import errorIcon from '@/assets/ManageFee/Error.json';
import deleteSuccess from '@/assets/ManageFee/deleteSuccess.json';
import studentSuccessIcon from '@/assets/ManageFee/studentSuccessJsonIcon.json';
import studentSelectedIcon from '@/assets/ManageFee/studentSelectedIcon.json';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import SearchIcon from '@mui/icons-material/Search';
import { IoIosArrowUp } from 'react-icons/io';
import styled from 'styled-components';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import useSettings from '@/hooks/useSettings';
import useAuth from '@/hooks/useAuth';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useAppSelector } from '@/hooks/useAppSelector';
import {
  getClassData,
  getClassSectionsData,
  getManageFeeclassListData,
  getOptionalFeeSettingListData,
  getOptionalFeeSettingListStatus,
  getParentsListData,
  getStopMappingSettingsData,
  getStopMappingSettingsStatus,
  getYearData,
} from '@/config/storeSelectors';
import { fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import {
  createStopMapping,
  DeleteAllBusMappedStudent,
  DeleteAllOptionalFee,
  DeleteBusMapped,
  DeleteOptionalFee,
  fetchClassList,
  fetchClassSections,
  fetchGetOptionalFeeSettings,
  fetchStopMappingSettings,
  optionalFeeSettings,
} from '@/store/ManageFee/manageFee.thunks';
import {
  BasicFeeMappedDeleteAllDataType,
  CreateStopMappingType,
  DeleteAllBusMappedStudentType,
  GetOptionalFeeSettingsDataType,
  GetOptionalFeeStudentMapDataType,
  GetStopTermMappedType,
  OptionalFeeMappedDeleteAllType,
  StopMappingSettingsType,
  StopTermMapped,
  StudentsMappedType,
  TermListType,
} from '@/types/ManageFee';
import { SuccessIcon } from '@/theme/overrides/CustomIcons';
import HorizontalRuleIcon from '@mui/icons-material/HorizontalRule';
import Lottie from 'lottie-react';
import LoadingButton from '@mui/lab/LoadingButton';
import PositionedSnackbar from '@/components/shared/Popup/SnackBar';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import { FEE_TYPE_ID_OPTIONS } from '@/config/Selection';
import { fetchParentsList } from '@/store/MessageBox/messageBox.thunks';
import Autocomplete from '../Selections/AutocompleteField';
import StudentsPickerField from '../Selections/StudentsPicker';
import SearchSelectBoxField from '../Selections/SearchSelectBox';
import { ParentsDataType } from '@/types/MessageBox';
import { useReactToPrint } from 'react-to-print';
import TestScrolling from '@/features/ManageFee/BasicFeeSetting/TestScrolling';
import DataTable2 from '@/features/ManageFee/FeeCollection/PayFeeCollection/Test';
import { TestScrollTable } from '../TableComponents/DataTable2';
import FeeDateSettings from './FeeDateSettingsTEST';
import MultiTextField from './TextFeildFocusTest';
import FeeDateSettings2 from './FeeDateSettingsTEST2';
import FeeDateSettingsList from './DataTableVirtuoso';
import FeeTableVirtuoso from './DataTableVirtuoso';
import DTVirtuoso from './DataTableVirtuoso';
import FeeSetting from './FeeSettingNew';
import ProgressBar from './ProgressBarAnimation';
import TestPrint from './TestPrint';
import ReportCard from './HolyProgressReport';
import ExcelUploader from './ExcelUploader';
import AreaChartComponent from './AreaChart';
import StudentTable from './AnimatioTable';
import Timetable from '@/features/Dashboard/TimetableCard';
import TemplateVoiceMessage from './VoiceTemplateNew';
import SendButton from './SendButton';
import StudentsAttendance from '@/features/Attendance-Marking/StudentsAttendance';
import StaffPickerField from '../Selections/StaffPicker';
import NotificationCreate from './NotificationCreateNew';
import CustomMDXEditor from './MDXEditor';
import { TipTap } from './TipTap';

const RNDRoot = styled.div`
  @media print {
    @page {
      size: A4;
      padding: 1mm;
      margin: 6.5mm;
      /* background-color: '#ffff99'; */
    }

    body {
      -webkit-print-color-adjust: exact;
      print-color-adjust: exact;
    }
  }
  padding: 1rem;
  @media screen and (max-width: 576px) {
    padding: 0.5rem;
  }
  .Card {
    display: flex;
    flex-direction: column;
    min-height: calc(100vh - 160px);
    @media screen and (max-width: 576px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 50px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        /* border: 1px solid ${(props) => props.theme.palette.grey[200]}; */
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }

        .MuiTableCell-root {
          border: 1px solid ${(props) => props.theme.palette.grey[100]};
        }
        .MuiTableCell-head {
          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.info.light : props.theme.palette.grey[900]};
        }
        .MuiTableCell-head:nth-child(1) {
          z-index: 11;
          position: sticky;
          left: 0;
          width: 50px;
        }
        .MuiTableCell-head:nth-child(2) {
          z-index: 11;
          position: sticky;
          left: 48px;
          width: 70px;
        }
        .MuiTableCell-head:nth-child(3) {
          z-index: 11;
          position: sticky;
          left: 124px;
          width: 200px;
        }
        .MuiTableCell-head:nth-child(4) {
          z-index: 11;
          position: sticky;
          left: 331px;
          width: 100px;
        }
        .MuiTableCell-head:nth-child(5) {
          z-index: 11;
          position: sticky;
          left: 444px;
          width: 100px;
        }
        .MuiTableCell-head:nth-child(6) {
          z-index: 11;
          position: sticky;
          left: 558px;
          width: 100px;
        }
        .MuiTableCell-head:last-child {
          z-index: 11;
          position: sticky;
          right: 0px;
          width: 80px;
        }
        .MuiTableCell-root.MuiTableCell-body:nth-child(1) {
          position: sticky;
          left: 0;
          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.info.lighter : props.theme.palette.grey[900]};
          width: 50px;
          z-index: 1;
        }
        .MuiTableCell-root.MuiTableCell-body:nth-child(2) {
          position: sticky;
          left: 48px;
          padding-left: 5px;
          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.info.lighter : props.theme.palette.grey[900]};
          width: 70px;
          z-index: 1;
        }
        .MuiTableCell-root.MuiTableCell-body:nth-child(3) {
          position: sticky;
          left: 124px;
          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.info.lighter : props.theme.palette.grey[900]};
          width: 200px;
          z-index: 1;
        }
        .MuiTableCell-root.MuiTableCell-body:nth-child(4) {
          position: sticky;
          left: 331px;
          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.info.lighter : props.theme.palette.grey[900]};
          width: 100px;
          z-index: 1;
        }
        .MuiTableCell-root.MuiTableCell-body:nth-child(5) {
          position: sticky;
          left: 444px;
          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.info.lighter : props.theme.palette.grey[900]};
          width: 100px;
          z-index: 1;
        }
        .MuiTableCell-root.MuiTableCell-body:nth-child(6) {
          position: sticky;
          left: 558px;
          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.info.lighter : props.theme.palette.grey[900]};
          width: 100px;
          z-index: 1;
        }
        .MuiTableCell-root.MuiTableCell-body:last-child {
          position: sticky;
          right: 0px;
          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.info.lighter : props.theme.palette.grey[800]};
          width: 80px;
          z-index: 1;
        }
        .MuiTableCell-root {
          padding: 0px;
          height: 100%;
        }
        .MuiTableCell-root.MuiTableCell-head {
          padding-left: 5px;
        }

        .MuiTableCell-root:first-child {
          padding: 5px;
        }
      }
    }
    .pointTitle {
      display: -webkit-box;
      max-width: 250px;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
`;

export default function RND() {
  const theme = useTheme();
  const isLight = useSettings().themeMode === 'light';
  const { user } = useAuth();
  const adminId: number | undefined = user?.accountId;
  const dispatch = useAppDispatch();
  const ClassData = useAppSelector(getClassData);
  const [selectedData, setSelectedData] = React.useState<any[]>([]);
  const ParentsListData = useAppSelector(getParentsListData);
  const [content, setContent] = useState<string>('## Hello from MDXEditor!');

  const [reportCount, setReportCount] = useState(1); // Initial 1 ReportCards
  const reportRefs = useRef<(HTMLDivElement | null)[]>([]);

  const handlePrint = useReactToPrint({
    content: () => {
      const printableContent = document.createElement('div');
      reportRefs.current.forEach((ref) => {
        if (ref) {
          printableContent.appendChild(ref.cloneNode(true));
        }
      });
      return printableContent;
    },
  });

  interface ParentListRequest {
    adminId: number | undefined;
    academicId: number;
    classId: number;
  }
  const initialParentListRequest: ParentListRequest = React.useMemo(
    () => ({
      adminId,
      academicId: 10,
      classId: 2,
    }),
    [adminId]
  );

  React.useEffect(() => {
    dispatch(fetchParentsList(initialParentListRequest));
  }, [dispatch, initialParentListRequest]);

  return (
    <Page title="Fees Collection">
      {/* <CustomMDXEditor value={content} onChange={setContent} label="Article Body" height={400} /> */}
      {/* <TipTap /> */}
      <NotificationCreate />
      <SendButton />
      <StudentsAttendance />
      {/* <TableZoom /> */}
      {/* <DTVirtuoso /> */}
      {/* <FeeDateSettings /> */}
      {/* <TestPrint />
      <FeeSetting />
      <FeeDateSettings2 /> */}

      {/* <MultiTextField /> */}
      <TemplateVoiceMessage />
      <RNDRoot>
        <Card sx={{ my: 5, p: 5 }}>
          <StudentTable />
        </Card>
        <AreaChartComponent />

        <Card sx={{ my: 5, p: 5 }}>
          <Timetable ClassData={ClassData} />
        </Card>

        <Card sx={{ my: 5, p: 5 }}>
          <ExcelUploader />
        </Card>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <div className="card-main-body">
            <Box display="flex" flexWrap="wrap" gap={5} mb={5}>
              <Stack>
                <Typography variant="subtitle2">Students Picker</Typography>
                <StudentsPickerField multiple />
              </Stack>
              <Stack>
                <Typography variant="subtitle2">Staff Picker</Typography>
                <StaffPickerField multiple />
              </Stack>
              <Stack>
                <Typography variant="subtitle2">Select Single</Typography>
                <Autocomplete
                  options={ParentsListData}
                  placeholder="Select"
                  getOptionLabel={(option) => option.studentName || ''}
                />
              </Stack>
              <Stack>
                <Typography variant="subtitle2">Select Multiple</Typography>
                <Autocomplete
                  options={ParentsListData}
                  multiple
                  placeholder="Select"
                  getOptionLabel={(option) => option.studentName || ''}
                  renderTags={(value, getTagProps) =>
                    value.map((option: ParentsDataType, index: number) => (
                      <Chip size="small" label={option.studentName} {...getTagProps({ index })} />
                    ))
                  }
                />
              </Stack>
              <Stack>
                <SearchSelectBoxField options={ParentsListData} />
              </Stack>
            </Box>
            <div>
              {/* Buttons to increase/decrease ReportCards */}

              {/* ReportCard List */}
              {/* {Array.from({ length: reportCount }).map((_, index) => {
                return (
                  <Box key={index} ref={(el) => (reportRefs.current[index] = el)}>
                  </Box>
                  );
                  })} */}
            </div>
            {/* <TestScrolling />
            <TestScrollTable /> */}
          </div>
        </Card>
        <Card sx={{ my: 5, p: 5 }}>
          <Typography variant="subtitle2">Progress Bar</Typography>
          <ProgressBar />
        </Card>
        <Card sx={{ my: 5, p: 5 }}>
          <ReportCard />
        </Card>
      </RNDRoot>
    </Page>
  );
}
