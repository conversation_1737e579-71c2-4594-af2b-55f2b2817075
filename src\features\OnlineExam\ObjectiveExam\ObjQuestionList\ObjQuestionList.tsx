/* eslint-disable jsx-a11y/alt-text */
import React, { useMemo, useState } from 'react';
import {
  Box,
  Chip,
  Divider,
  Grid,
  Stack,
  Typography,
  Card,
  useTheme,
  IconButton,
  Popover,
  Checkbox,
} from '@mui/material';
import Popup from '@/components/shared/Popup/Popup';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import { BsArrowsAngleExpand } from 'react-icons/bs';
import useSettings from '@/hooks/useSettings';
import { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import MenuEditDelete from '@/components/shared/Selections/MenuEditDelete';

export const data = [
  {
    id: 1,
    order: 1,
    class: 'VII-B',
    subject: 'Test Subject',
    exam: 'Online Demo Examination 2023',
    question:
      'Lorem Ipsum available, but the majority have an alteration for this. Lorem Ipsum available, but the majority have an alteration for this?',
    options: { A: 'India', B: 'China', C: 'America', D: 'England' },
    correct: 'A',
    explaination: 'This is the sample explaination for the given question',
    explainationfile:
      'https://plus.unsplash.com/premium_photo-1661634073903-2ecdccdfc8a2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    image: '',
    video: '',
  },
  {
    id: 2,
    order: 2,
    class: 'VI-C',
    subject: 'Test Subject',
    exam: 'Online Demo Examination 2023',
    question: 'Lorem Ipsum available, but the majority haven alteration for this?',
    options: { A: 'India', B: 'China', C: 'America', D: 'England' },
    correct: 'C',
    explaination: 'This is the sample explaination for the given question',
    explainationfile:
      'https://images.unsplash.com/photo-1588075592405-d3d4f0846961?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2072&q=80',
    image:
      'https://plus.unsplash.com/premium_photo-1661634073903-2ecdccdfc8a2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    video: '',
  },
  {
    id: 3,
    order: 3,
    class: 'XII-B',
    subject: 'Test Subject',
    exam: 'Online Demo Examination 2023',
    question: 'Lorem Ipsum available, but the majority have an alteration for this?',
    options: { A: 'India', B: 'China', C: 'America', D: 'England' },
    correct: 'B',
    explaination: 'This is the sample explaination for the given question',
    explainationfile: '',
    image:
      'https://images.unsplash.com/photo-1588075592405-d3d4f0846961?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2072&q=80',
    video: '',
  },
  {
    id: 4,
    order: 4,
    class: 'V-A',
    subject: 'Test Subject',
    exam: 'Online Demo Examination 2023',
    question: 'Lorem Ipsum available, but the majority have an alteration for this?',
    options: { A: 'India', B: 'China', C: 'America', D: 'England' },
    correct: 'C',
    explaination: 'This is the sample explaination for the given question',
    explainationfile: '',
    image: '',
    video: '',
  },
  {
    id: 5,
    order: 5,
    class: 'II-B',
    subject: 'Test Subject',
    exam: 'Online Demo Examination 2023',
    question: 'Lorem Ipsum available, but the majority have an alteration for this?',
    options: { A: 'India', B: 'China', C: 'America', D: 'England' },
    correct: 'D',
    explaination: 'This is the sample explaination for the given question',
    explainationfile: '',
    image:
      'https://images.unsplash.com/photo-1588072432836-e10032774350?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2072&q=80',
    video: '',
  },
  {
    id: 6,
    order: 6,
    class: 'XII-C',
    subject: 'Test Subject',
    exam: 'Online Demo Examination 2023',
    question: 'Lorem Ipsum available, but the majority have an alteration for this?',
    options: { A: 'India', B: 'China', C: 'America', D: 'England' },
    correct: 'A',
    explaination: 'This is the sample explaination for the given question',
    explainationfile: '',
    image: '',
    video: '',
  },
  {
    id: 7,
    order: 7,
    class: 'XII-C',
    subject: 'Test Subject',
    exam: 'Online Demo Examination 2023',
    question: 'Lorem Ipsum available, but the majority have an alteration for this?',
    options: { A: 'India', B: 'China', C: 'America', D: 'England' },
    correct: 'A',
    explaination: 'This is the sample explaination for the given question',
    explainationfile: '',
    image: '',
    video: '',
  },
];

function ObjQuestionList() {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const [Delete, setDelete] = useState(false);

  const handleClickDelete = () => setDelete(true);
  const handleClickCloseDelete = () => setDelete(false);

  const [expPopup, setExpPopup] = React.useState<HTMLButtonElement | null>(null);
  const [index, setIndex] = useState<number>(-1);
  const [viewPopup, setViewPopup] = React.useState<HTMLButtonElement | null>(null);

  const handleExpPopup = (event: React.MouseEvent<HTMLButtonElement>, rowIndex: number) => {
    setExpPopup(event.currentTarget);
    setIndex(rowIndex);
  };
  const open = Boolean(expPopup);
  const id = open ? 'simple-popover' : undefined;

  const handleViewPopup = (event: React.MouseEvent<HTMLButtonElement>, rowIndex: number) => {
    setViewPopup(event.currentTarget);
    setIndex(rowIndex);
  };
  const openView = Boolean(viewPopup);
  const idView = openView ? 'simple-popover' : undefined;

  const ObjQuestionListColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'class',
        dataKey: 'class',
        headerLabel: 'Class',
      },
      {
        name: 'subject',
        dataKey: 'subject',
        headerLabel: 'Sub',
      },
    ],
    []
  );

  return (
    <Box className="card-container">
      <Grid container spacing={2} sx={{ px: { xs: 3, md: 5 }, pb: { xs: 2, md: 3 } }}>
        {data.map((question, rowIndex) => (
          <Grid item xxl={4} lg={6} md={12} sm={6} xs={12}>
            <Card className="student_card" sx={{ p: 2, boxShadow: 0, border: `1px solid ${theme.palette.grey[300]}` }}>
              <Box display="flex" flexDirection="column">
                <Box display="flex" alignItems="center">
                  <Checkbox />
                  <Chip
                    size="small"
                    color="success"
                    label={question.exam}
                    sx={{ border: 0 }}
                    variant={isLight ? 'outlined' : 'filled'}
                  />
                  <Box ml="auto" mr={0}>
                    <MenuEditDelete
                      Edit={() => {
                        return 0;
                      }}
                      Delete={handleClickDelete}
                    />
                  </Box>
                </Box>

                <Grid container>
                  {ObjQuestionListColumns.map((item) => (
                    <Grid item xs={6}>
                      <Stack direction="row" ml={1.5}>
                        <Typography key={item.name} variant="h6" fontSize={11} mt={0.2}>
                          <span style={{ color: 'Grey' }}>{item.headerLabel} : </span>
                          {item.dataKey
                            ? (question as { [key: string]: any })[item.dataKey ?? '']
                            : item && item.renderCell && item.renderCell(question, rowIndex)}
                        </Typography>
                      </Stack>
                      <Divider sx={{ borderColor: isLight ? theme.palette.grey[800] : theme.palette.grey[100] }} />
                    </Grid>
                  ))}
                  <Grid item xs={12}>
                    <Box py={1} flexGrow={1} sx={{ height: '50px', overflow: 'hidden' }}>
                      <Typography
                        variant="h6"
                        fontSize={13}
                        sx={{
                          display: '-webkit-box',
                          WebkitBoxOrient: 'vertical',
                          WebkitLineClamp: 2, // Set the number of lines to display
                          maxWidth: '90%',
                          textOverflow: 'ellipsis',
                        }}
                      >
                        {question.question}
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid container mb={1} mt="auto" md={6} xs={12}>
                    {Object.entries(question.options).map(([key, value]) => (
                      <Grid item mx={1} xs={6} md={12}>
                        <Typography
                          key={key}
                          variant="h6"
                          color={question.correct === key ? 'Red' : 'Grey'}
                          fontSize={13}
                          pt={1}
                          sx={{
                            maxWidth: '130px',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap',
                            overflow: 'hidden',
                          }}
                        >
                          {key} : {value}
                        </Typography>
                      </Grid>
                    ))}
                  </Grid>
                  {question.image && (
                    <Grid item md={6} xs={12}>
                      <Box>
                        <img src={question.image} height="105px" />
                      </Box>
                    </Grid>
                  )}
                </Grid>
                <Stack direction="row" spacing={1} ml="auto" mt={1}>
                  <IconButton
                    size="small"
                    aria-describedby={id}
                    onClick={(event) => handleViewPopup(event, rowIndex)}
                    // onMouseEnter={(event) => handleViewPopup(event, rowIndex)}
                    // onMouseLeave={handleClose}
                    sx={{
                      backgroundColor: isLight ? theme.palette.error.lighter : theme.palette.grey[700],
                      color: isLight ? theme.palette.error.dark : theme.palette.grey[100],
                      height: 25,
                      width: 100,
                      borderRadius: '20px',
                      fontSize: 14,
                      justifyContent: 'space-around',
                      pb: 1,
                    }}
                  >
                    Expand <BsArrowsAngleExpand size={12} />
                  </IconButton>
                  <IconButton
                    size="small"
                    aria-describedby={id}
                    onClick={(event) => handleExpPopup(event, rowIndex)}
                    // onMouseEnter={(event) => handleViewPopup(event, rowIndex)}
                    // onMouseLeave={handleClose}
                    sx={{
                      backgroundColor: isLight ? theme.palette.info.lighter : theme.palette.grey[700],
                      color: isLight ? theme.palette.info.dark : theme.palette.grey[100],
                      height: 25,
                      width: 100,
                      borderRadius: '20px',
                      fontSize: 14,
                      justifyContent: 'space-around',
                      pb: 1,
                    }}
                  >
                    Explaination
                  </IconButton>
                </Stack>
              </Box>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Popup
        size="xs"
        state={Delete}
        onClose={handleClickCloseDelete}
        popupContent={<DeleteMessage message="Are you sure want to delete?" />}
      />
      <Popover
        id={id}
        open={open}
        anchorEl={expPopup}
        onClose={() => setExpPopup(null)}
        anchorOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
      >
        <Grid container px={3} pb={3} width={500}>
          {data[index]?.explaination && (
            <Grid item xs={12}>
              <Box>
                <Typography variant="h6" fontSize={20} py={2}>
                  Exlpaination:
                </Typography>
                <Typography fontSize={16}>{data[index]?.explaination}</Typography>
              </Box>
            </Grid>
          )}
          {data[index]?.explainationfile && (
            <Grid item xs={12}>
              <Box pt={2}>
                <img src={data[index]?.explainationfile} height="250px" />
              </Box>
            </Grid>
          )}
        </Grid>
      </Popover>
      <Popover
        id={idView}
        open={openView}
        anchorEl={viewPopup}
        onClose={() => setViewPopup(null)}
        anchorOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
      >
        <Grid container p={3} width={600} minHeight={350}>
          <Grid item>
            <Box py={3}>
              <Typography fontSize={16}>{data[index]?.question}</Typography>
            </Box>
          </Grid>
          {data[index]?.image && (
            <Grid item xs={12} pr={2}>
              <Box>
                <img src={data[index]?.image} height="300px" />
              </Box>
            </Grid>
          )}
          <Grid container xs={12}>
            {Object.entries(data[index]?.options ?? {}).map(([key, value]) => (
              <Grid item mx={1} xs={5}>
                <Typography key={key} color={data[index]?.correct === key ? 'Red' : 'inherit'} fontSize={16} pt={1}>
                  <span style={{ color: data[index]?.correct === key ? 'Red' : 'Grey' }}>{`${key}: `}</span> {value}
                </Typography>
              </Grid>
            ))}
          </Grid>
        </Grid>
      </Popover>
    </Box>
  );
}

export default ObjQuestionList;
