/* eslint-disable react/no-unstable-nested-components */
/* eslint-disable no-nested-ternary */
import { ReactNode, useCallback, useEffect, useRef } from 'react';
import {
  useTheme,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TablePagination,
  TableBody,
  Skeleton,
  Checkbox,
  TextField,
  Typography,
  Box,
  Stack,
  TableFooter,
} from '@mui/material';
import { FetchStatus } from '@/types/Common';
import NoData from '@/assets/no-datas.png';
import { TableVirtuoso, Virtuoso } from 'react-virtuoso';
import SortableTableCell from './SortableTableCell';

export type DataTableColumn<D> = {
  name: string;
  headerLabel?: string;
  renderHeader?: () => ReactNode;
  sortable?: boolean;
  dataKey?: string;
  renderCell?: (row: D, rowIndex?: number) => ReactNode;
  width?: string;
  align?: string;
  renderFooter?: () => ReactNode;
  footerLabel?: string;
};

export type DataTablePaginationProps = {
  rowsPerPageOptions?: number[];
  totalRecords: number;
  pageNumber: number;
  pageSize: number;
  onPageChange: (event: React.MouseEvent<HTMLButtonElement> | null, page: number) => void;
  onRowsPerPageChange?: React.ChangeEventHandler<HTMLTextAreaElement | HTMLInputElement>;
  showPagination?: boolean;
  showFirstButton?: boolean;
  showLastButton?: boolean;
};

type ArrayStateProps = {
  feeId?: number;
  termId?: number;
  value?: string;
};

export type DataTableProps<D> = {
  columns: DataTableColumn<D>[];
  data: D[];
  allowPagination?: boolean;
  allowSorting?: boolean;
  sortColumn?: string;
  sortDirection?: 'asc' | 'desc';
  onSort?: (columnName: string) => void;
  headerClassName?: string;
  PaginationProps?: DataTablePaginationProps;
  fetchStatus?: FetchStatus;
  getRowKey: (item: D, index?: number) => React.Key | null | undefined;
  deletingRecords?: (string | number)[];
  onCheckboxClick?: any;
  ShowCheckBox?: boolean | undefined;
  setSelectedRows?: any;
  selectedRows?: any;
  RowSelected?: boolean;
  isCancell?: any;
  textAlign?: string;
  tableWidth?: number;
  tableStyles?: {} | undefined;
  tableCellStyles?: {} | undefined;
  respnseResults?: {} | undefined;
  tableRowSelect?: boolean;
  disabledCheckBox?: any[];
  isSubmitting?: boolean;
  footerColumn?: any;
  showHorizontalScroll?: boolean;
  tableCellSkeltonStyle?: {} | undefined;
  arrayState?: any;
  setTermAmounts?: { [key: string]: number } | undefined;
  setFeeAmounts?: { [key: string]: number } | undefined;
  setRowSums?: Record<string, number>;
};

function VirtuosoTable<D>({
  columns,
  data,
  getRowKey,
  allowPagination = false,
  allowSorting = false,
  ShowCheckBox = false,
  sortColumn,
  sortDirection = 'asc',
  onSort,
  headerClassName,
  fetchStatus,
  PaginationProps,
  deletingRecords,
  setSelectedRows,
  selectedRows,
  RowSelected,
  textAlign,
  tableWidth,
  tableStyles,
  tableCellStyles,
  tableRowSelect,
  disabledCheckBox,
  isSubmitting,
  footerColumn,
  showHorizontalScroll,
  onCheckboxClick,
  tableCellSkeltonStyle,
  setTermAmounts,
  setFeeAmounts,
  arrayState,
  setRowSums,
}: DataTableProps<D>) {
  const tableRef = useRef<any>(null);
  const theme = useTheme();
  // const [selected, setSelected] = useState<D[]>(selectedRows);
  const isSelected = (row: D) => selectedRows?.indexOf(row) !== -1;

  const handleRowClick = (row: D) => {
    // Check if the clicked row is disabled
    const rowIndex = data.findIndex((item) => item === row);
    const isRowDisabled = disabledCheckBox && disabledCheckBox[rowIndex];

    if (!isRowDisabled) {
      const selectedIndex = selectedRows?.indexOf(row);
      let newSelected: D[] = [];

      if (selectedIndex === -1) {
        newSelected = newSelected.concat(selectedRows, row);
        if (typeof arrayState === 'function') {
          arrayState((prev: ArrayStateProps[]) => prev.filter((cell: ArrayStateProps) => cell.feeId !== row.feeId));
        }

        setFeeAmounts({});
      } else if (selectedIndex === 0) {
        newSelected = newSelected.concat(selectedRows.slice(1));
      } else if (selectedIndex === selectedRows.length - 1) {
        newSelected = newSelected.concat(selectedRows.slice(0, -1));
      } else if (selectedIndex > 0) {
        newSelected = newSelected.concat(selectedRows.slice(0, selectedIndex), selectedRows.slice(selectedIndex + 1));
      }

      setSelectedRows(newSelected);
      // Update `termAmounts` state by removing the corresponding feeId
      setTermAmounts((prev) => {
        const updatedTermAmounts = { ...prev };
        delete updatedTermAmounts[`${row?.feeId}_`];
        return updatedTermAmounts;
      });
      // setRowSums((prev) => {
      //   const updatedRowSums = { ...prev };
      //   updatedRowSums[`${row?.feeId}_`] = {};
      //   return updatedRowSums;
      // });
      if (onCheckboxClick) {
        onCheckboxClick(newSelected);
      }
    }
  };
  const handleRowAllClick = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      if (typeof arrayState === 'function') {
        arrayState([]);
      }
      // Filter out disabled rows before setting selected rows
      const enabledRows = data.filter((row, index) => !disabledCheckBox || !disabledCheckBox[index]);
      setSelectedRows(enabledRows);
      if (onCheckboxClick) {
        onCheckboxClick(enabledRows);
      }
    } else {
      setSelectedRows([]);
      if (onCheckboxClick) {
        onCheckboxClick([]);
      }
    }
  };
  localStorage.setItem('absenteesData', JSON.stringify(selectedRows));

  useEffect(() => {
    if (tableRef.current) {
      tableRef.current.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    }
  }, [PaginationProps?.pageNumber, PaginationProps?.pageSize]);

  const handleSort = useCallback(
    (col: string) => {
      onSort?.(col);
    },
    [onSort]
  );

  if (allowPagination && !PaginationProps) {
    throw new Error("PaginationProps must be passed when 'allowPagination' is set to true");
  }

  let tableBodyContent: ReactNode = null;

  if (fetchStatus === 'loading') {
    const rowCount = allowPagination ? PaginationProps?.pageSize : data.length;
    tableBodyContent = Array.from(Array(rowCount).keys()).map((index) => (
      <TableRow key={index}>
        {ShowCheckBox && (
          <TableCell padding="checkbox">
            <Skeleton variant="text" />
          </TableCell>
        )}
        {columns.map((column) => (
          <TableCell key={`Row${index}_${column.name}`}>
            <Skeleton variant="text" />
          </TableCell>
        ))}
      </TableRow>
    ));
  }

  if (fetchStatus === 'success') {
    tableBodyContent = data.map((item, rowIndex: number) => {
      const rowkey = getRowKey(item, rowIndex);
      let isDeleting = false;
      if (rowkey && deletingRecords && deletingRecords.length > 0) {
        isDeleting = deletingRecords.includes(rowkey);
      }
      const isRowSelected = isSelected(item);

      return (
        <TableRow
          hover
          role="checkbox"
          aria-checked={RowSelected && isRowSelected}
          tabIndex={-1}
          selected={RowSelected && isRowSelected}
          key={rowkey}
          sx={{
            opacity: isDeleting ? 0.5 : 1,
            cursor: 'pointer',
            '&:last-child td': {
              borderBottom: '0px',
            },
          }}
        >
          {ShowCheckBox && (
            <TableCell>
              <Checkbox
                disabled={isSubmitting ? true : disabledCheckBox && disabledCheckBox[rowIndex]}
                color="primary"
                checked={isRowSelected}
                onClick={() => {
                  handleRowClick(item);
                }}
                inputProps={{ 'aria-labelledby': `checkbox-${item}` }}
              />
            </TableCell>
          )}
          {columns.map((column) => {
            let cellData = null;
            if (column.renderCell) {
              cellData = column.renderCell(item, rowIndex);
            } else if (column.dataKey === 'TextField') {
              cellData = <TextField disabled={!isRowSelected} placeholder={column.name} />;
            } else if (column.dataKey) {
              cellData = (item as Record<string, any>)[column.dataKey];
            }
            return (
              <TableCell
                onClick={
                  column.dataKey === 'TextField'
                    ? undefined
                    : column.dataKey === 'Button'
                    ? undefined
                    : () => tableRowSelect && handleRowClick(item)
                }
                className={textAlign}
                sx={{ textAlign: column.align || 'start' }}
                key={`Row_${rowkey}_${column.name}`}
                width={column.width ? column.width : 'auto'}
              >
                {cellData}
              </TableCell>
            );
          })}
        </TableRow>
      );
    });
  }

  return (
    <Paper className="card-table-container">
      {data.length !== 0 ? (
        <TableContainer
          sx={{
            '&::-webkit-scrollbar': {
              height: showHorizontalScroll ? '15px' : '',
            },
            overflowX: 'auto',
          }}
        >
          <Table style={{ width: '100%' }}>
            <TableHead className={headerClassName}>
              <TableRow>
                {ShowCheckBox && (
                  <TableCell>
                    <Checkbox
                      color="primary"
                      onChange={handleRowAllClick}
                      indeterminate={selectedRows?.length > 0 && selectedRows.length < data.length}
                      checked={selectedRows?.length > 0 && selectedRows.length === data.length}
                    />
                  </TableCell>
                )}
                {columns.map((column) => (
                  <TableCell
                    className={textAlign}
                    sx={{ textAlign: column.align || 'start', minWidth: column.width ?? 100 }}
                    key={column.name}
                  >
                    {allowSorting && column.sortable ? (
                      <SortableTableCell
                        columnName={column.name}
                        active={sortColumn === column.name}
                        direction={sortDirection}
                        onClick={handleSort}
                      >
                        {column.renderHeader ? column.renderHeader() : column.headerLabel}
                      </SortableTableCell>
                    ) : column.renderHeader ? (
                      column.renderHeader()
                    ) : (
                      column.headerLabel
                    )}
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
          </Table>

          <Virtuoso
            style={{ height: '373px', width: '100%' }}
            totalCount={data.length}
            components={{
              Table: (props) => (
                <Table
                  stickyHeader
                  size="small"
                  sx={{ tableLayout: 'fixed', width: '100%' }}
                  // sx={tableStyles}
                  {...props}
                />
              ),
              fixedHeaderContent: () => (
                <thead>
                  <tr>
                    <th
                      style={{
                        width: 150,
                        background: 'white',
                        position: 'sticky',
                        left: 0,
                        zIndex: 1,
                      }}
                    >
                      Name
                    </th>
                    <th style={{ background: 'white' }}>Description</th>
                    <th style={{ background: 'white' }}>Description</th>
                    <th style={{ background: 'white' }}>Description</th>
                    <th style={{ background: 'white' }}>Description</th>
                    <th style={{ background: 'white' }}>Description</th>
                  </tr>
                </thead>
              ),
              Header: () => (
                <TableHead className={headerClassName}>
                  <TableRow>
                    {ShowCheckBox && (
                      <TableCell>
                        <Checkbox
                          color="primary"
                          onChange={handleRowAllClick}
                          indeterminate={selectedRows?.length > 0 && selectedRows.length < data.length}
                          checked={selectedRows?.length > 0 && selectedRows.length === data.length}
                        />
                      </TableCell>
                    )}
                    {columns.map((column) => (
                      <TableCell
                        className={textAlign}
                        sx={{ textAlign: column.align || 'start', minWidth: column.width ?? 100 }}
                        key={column.name}
                      >
                        {allowSorting && column.sortable ? (
                          <SortableTableCell
                            columnName={column.name}
                            active={sortColumn === column.name}
                            direction={sortDirection}
                            onClick={handleSort}
                          >
                            {column.renderHeader ? column.renderHeader() : column.headerLabel}
                          </SortableTableCell>
                        ) : column.renderHeader ? (
                          column.renderHeader()
                        ) : (
                          column.headerLabel
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                </TableHead>
              ),
            }}
            itemContent={(index) => {
              const row = data[index];
              const rowKey = getRowKey(row, index);
              let isDeleting = false;
              if (rowKey && deletingRecords?.length > 0) {
                isDeleting = deletingRecords.includes(rowKey);
              }
              const isRowSelected = isSelected(row);
              return (
                <TableRow
                  hover
                  role="checkbox"
                  aria-checked={RowSelected && isRowSelected}
                  tabIndex={-1}
                  selected={RowSelected && isRowSelected}
                  key={rowKey}
                  sx={{
                    opacity: isDeleting ? 0.5 : 1,
                    cursor: 'pointer',
                    '&:last-child td': {
                      borderBottom: '0px',
                    },
                  }}
                >
                  {ShowCheckBox && (
                    <TableCell>
                      <Checkbox
                        disabled={isSubmitting ? true : disabledCheckBox?.[index]}
                        color="primary"
                        checked={isRowSelected}
                        onClick={() => {
                          handleRowClick(row);
                        }}
                        inputProps={{ 'aria-labelledby': `checkbox-${row}` }}
                      />
                    </TableCell>
                  )}
                  {columns.map((column) => {
                    let cellData = null;
                    if (column.renderCell) {
                      cellData = column.renderCell(row, index);
                    } else if (column.dataKey === 'TextField') {
                      cellData = <TextField disabled={!isRowSelected} placeholder={column.name} />;
                    } else if (column.dataKey) {
                      cellData = (row as Record<string, any>)[column.dataKey];
                    }
                    return (
                      <TableCell
                        onClick={
                          column.dataKey === 'TextField'
                            ? undefined
                            : column.dataKey === 'Button'
                            ? undefined
                            : () => tableRowSelect && handleRowClick(row)
                        }
                        className={textAlign}
                        sx={{ textAlign: column.align || 'start', p: 0, minWidth: column.width ?? 150 }}
                        key={`Row_${rowKey}_${column.name}`}
                      >
                        {cellData}
                      </TableCell>
                    );
                  })}
                </TableRow>
              );
            }}
          />
        </TableContainer>
      ) : (
        <Box
          display="flex"
          alignItems="center"
          justifyContent="center"
          width="100%"
          height={{ xs: 'calc(100vh - 400px)', sm: 'calc(100vh - 400px)', lg: 'calc(100vh - 100px)' }}
        >
          <Stack direction="column" alignItems="center">
            <img src={NoData} width="150px" alt="" />
            <Typography variant="subtitle2" mt={2} color="GrayText">
              No data found!
            </Typography>
          </Stack>
        </Box>
      )}
      {!!PaginationProps && allowPagination && (
        <TablePagination
          rowsPerPageOptions={PaginationProps.rowsPerPageOptions}
          component="div"
          count={PaginationProps.totalRecords}
          rowsPerPage={PaginationProps.pageSize}
          page={PaginationProps.pageNumber}
          onPageChange={PaginationProps.onPageChange}
          onRowsPerPageChange={PaginationProps.onRowsPerPageChange}
          showFirstButton
          showLastButton
        />
      )}
    </Paper>
  );
}

export default VirtuosoTable;

// components={{
//   Table: (props) => (
//     <Table
//       stickyHeader
//       size="small"
//       sx={{ tableLayout: 'fixed', width: '100%' }}
//       // sx={tableStyles}
//       {...props}
//     />
//   ),
//   Header: () => (
//     <TableHead className={headerClassName}>
//       <TableRow>
//         {ShowCheckBox && (
//           <TableCell>
//             <Checkbox
//               color="primary"
//               onChange={handleRowAllClick}
//               indeterminate={selectedRows?.length > 0 && selectedRows.length < data.length}
//               checked={selectedRows?.length > 0 && selectedRows.length === data.length}
//             />
//           </TableCell>
//         )}
//         {columns.map((column) => (
//           <TableCell
//             className={textAlign}
//             sx={{ textAlign: column.align || 'start', minWidth: column.width ?? 100 }}
//             key={column.name}
//           >
//             {allowSorting && column.sortable ? (
//               <SortableTableCell
//                 columnName={column.name}
//                 active={sortColumn === column.name}
//                 direction={sortDirection}
//                 onClick={handleSort}
//               >
//                 {column.renderHeader ? column.renderHeader() : column.headerLabel}
//               </SortableTableCell>
//             ) : column.renderHeader ? (
//               column.renderHeader()
//             ) : (
//               column.headerLabel
//             )}
//           </TableCell>
//         ))}
//       </TableRow>
//     </TableHead>
//   ),
// }}
