/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import { Autocomplete, Grid, Stack, TextField, Button, Typography, Box, FormControl } from '@mui/material';
import styled from 'styled-components';
import { CLASS_SELECT, STATUS_SELECT, SUBJECT_SELECT, YEAR_SELECT } from '@/config/Selection';
import SelectDateTimePicker from '@/components/shared/Selections/TimePicker';
import dayjs from 'dayjs';

const VehicleDetailedViewRoot = styled.div`
  width: 100%;
  padding: 1.5rem;
`;

function VehicleDetailedView({ onClose }: any) {
  return (
    <VehicleDetailedViewRoot>
      <form noValidate>
        <Grid container spacing={3}>
          <Grid item md={6} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle1" fontSize={12} color="GrayText">
                Vehicle Reg.No
              </Typography>
              <Typography variant="subtitle1" fontSize={14}>
                KL-49-5482
              </Typography>
            </FormControl>
          </Grid>
          <Grid item md={6} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle1" fontSize={12} color="GrayText">
                Vehicle Name
              </Typography>
              <Typography variant="subtitle1" fontSize={14}>
                Bus
              </Typography>
            </FormControl>
          </Grid>
          <Grid item md={6} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle1" fontSize={12} color="GrayText">
                Vehicle Root
              </Typography>
              <Typography variant="subtitle1" fontSize={14}>
                Palakkad to Coimbatore
              </Typography>
            </FormControl>
          </Grid>
          <Grid item md={6} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle1" fontSize={12} color="GrayText">
                Driver Name
              </Typography>
              <Typography variant="subtitle1" fontSize={14}>
                Krishnan
              </Typography>
            </FormControl>
          </Grid>
          <Grid item md={6} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle1" fontSize={12} color="GrayText">
                Driver Number
              </Typography>
              <Typography variant="subtitle1" fontSize={14}>
                9654871107
              </Typography>
            </FormControl>
          </Grid>
          <Grid item md={6} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle1" fontSize={12} color="GrayText">
                Terminal ID
              </Typography>
              <Typography variant="subtitle1" fontSize={14}>
                TN09ED2445
              </Typography>
            </FormControl>
          </Grid>
          <Grid item md={6} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle1" fontSize={12} color="GrayText">
                SIM Number
              </Typography>
              <Typography variant="subtitle1" fontSize={14}>
                6201099402
              </Typography>
            </FormControl>
          </Grid>
          <Grid item md={6} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle1" fontSize={12} color="GrayText">
                EXE Name
              </Typography>
              <Typography variant="subtitle1" fontSize={14}>
                0x4861e
              </Typography>
            </FormControl>
          </Grid>
          <Grid item md={6} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle1" fontSize={12} color="GrayText">
                IP Address
              </Typography>
              <Typography variant="subtitle1" fontSize={14}>
                196.9465.1022.8218
              </Typography>
            </FormControl>
          </Grid>
          <Grid item md={6} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle1" fontSize={12} color="GrayText">
                Port Number
              </Typography>
              <Typography variant="subtitle1" fontSize={14}>
                168446k1258V
              </Typography>
            </FormControl>
          </Grid>

          <Grid item md={12} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle1" fontSize={12} color="GrayText">
                Vehicle Status
              </Typography>
              <Typography variant="subtitle1" fontSize={14}>
                Publish
              </Typography>
            </FormControl>
          </Grid>
        </Grid>
      </form>
    </VehicleDetailedViewRoot>
  );
}

export default VehicleDetailedView;
