import ApiUrls from '@/config/ApiUrls';
import { ClassListPagedData, ClassListRequest } from '@/types/AcademicManagement';
import {
  ApproveLeaveRequest,
  AttendanceCalendarDataType,
  AttendanceCalendarRequest,
  AttendanceListInfo,
  AttendanceListRequest,
  AttendanceSummaryReportInfo,
  AttendanceSummaryReportPagedData,
  AttendanceSummaryReportRequest,
  EnquiryReplyRequestData,
  LeaveNoteListInfo,
  LeaveNoteListPagedData,
  LeaveNoteListRequest,
  MarkAttendance,
  RejectLeaveRequest,
  StudentEnquiryListPagedData,
  StudentEnquiryListRequest,
} from '@/types/AttendanceMarking';
import { SendResponse, SubmitResponse } from '@/types/Common';
import { privateApi } from './base/api';
import { APIResponse } from './base/types';

async function GetAllClasses(request: ClassListRequest): Promise<APIResponse<ClassListPagedData>> {
  const response = await privateApi.post<ClassListPagedData>(ApiUrls.GetClassList, request);
  return response;
}

// async function GetAttendanceList(
//   adminId: number | undefined,
//   academicId: number | undefined,
//   classId: number | undefined,
//   absentDate: string | undefined
// ): Promise<APIResponse<AttendanceListInfo[]>> {
//   if (adminId === undefined || academicId === undefined || classId === undefined || absentDate === undefined) {
//     throw new Error('adminId, academicId, absentDate and classId are required');
//   }

//   const url = ApiUrls.GetAttendanceList.replace('{adminId}', adminId.toString())
//     .replace('{academicId}', academicId.toString())
//     .replace('{classId}', classId.toString())
//     .replace('{absentDate}', absentDate);

//   const response = await privateApi.get<any>(url);
//   return response;
// }

async function GetAttendanceList(request: AttendanceListRequest): Promise<APIResponse<AttendanceListInfo[]>> {
  const response = await privateApi.post<AttendanceListInfo[]>(ApiUrls.GetAttendanceList, request);
  return response;
}

async function MarkAbsentees(request: MarkAttendance[]): Promise<APIResponse<MarkAttendance[]>> {
  const response = await privateApi.post<MarkAttendance[]>(ApiUrls.MarkAbsentees, request);
  return response;
}

async function MarkPresent(
  adminId: number | undefined,
  studentId: number | undefined,
  classId: number | undefined,
  attendanceId: number | undefined
): Promise<APIResponse<SendResponse>> {
  if (adminId === undefined || studentId === undefined || classId === undefined || attendanceId === undefined) {
    throw new Error('adminId, studentId, attendanceId and classId are required');
  }

  const url = ApiUrls.MarkPresent.replace('{adminId}', adminId.toString())
    .replace('{studentId}', studentId.toString())
    .replace('{classId}', classId.toString())
    .replace('{attendanceId}', attendanceId.toString());

  const response = await privateApi.get<any>(url);
  return response;
}
async function GetLeaveNoteList(request: LeaveNoteListRequest): Promise<APIResponse<LeaveNoteListPagedData>> {
  const response = await privateApi.post<LeaveNoteListPagedData>(ApiUrls.GetLeaveNoteList, request);
  return response;
}
async function ApproveLeave(request: ApproveLeaveRequest): Promise<APIResponse<SubmitResponse>> {
  const response = await privateApi.post<SubmitResponse>(ApiUrls.ApproveLeave, request);
  return response;
}
async function RejectLeave(request: RejectLeaveRequest): Promise<APIResponse<SubmitResponse>> {
  const response = await privateApi.post<SubmitResponse>(ApiUrls.RejectLeave, request);
  return response;
}

async function GetStudentEnquiryList(
  request: StudentEnquiryListRequest
): Promise<APIResponse<StudentEnquiryListPagedData>> {
  const response = await privateApi.post<StudentEnquiryListPagedData>(ApiUrls.GetStudentEnquiryList, request);
  return response;
}

async function SendEnquiryReply(request: EnquiryReplyRequestData): Promise<APIResponse<EnquiryReplyRequestData>> {
  const response = await privateApi.post<EnquiryReplyRequestData>(ApiUrls.SendEnquiryReply, request);
  return response;
}

async function GetAttendanceCalendar(
  request: AttendanceCalendarRequest
): Promise<APIResponse<AttendanceCalendarDataType>> {
  const response = await privateApi.post<AttendanceCalendarDataType>(ApiUrls.GetCalendarReport, request);
  return response;
}

async function GetAttendanceSummaryReport(
  request: AttendanceSummaryReportRequest
): Promise<APIResponse<AttendanceSummaryReportInfo[]>> {
  const response = await privateApi.post<AttendanceSummaryReportInfo[]>(ApiUrls.GetAttendanceSummaryReport, request);
  return response;
}

const methods = {
  GetAllClasses,
  GetAttendanceList,
  MarkAbsentees,
  MarkPresent,
  GetLeaveNoteList,
  GetStudentEnquiryList,
  SendEnquiryReply,
  ApproveLeave,
  RejectLeave,
  GetAttendanceCalendar,
  GetAttendanceSummaryReport,
};
export default methods;
