import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { fetchSignature } from './meeting.thunk';

// const initialState: ParentPaymentState = {
//   parentPayFee: {
//     data: [],
//     status: 'idle',
//     error: null,
//   },
//   receiptOnlineList: {
//     data: [],
//     status: 'idle',
//     error: null,
//   },
//   submitting: false,
//   deletingRecords: {},
//   error: null,
// };

const signatureSlice = createSlice({
  name: 'signature',
  initialState: {
    signature: '',
    status: 'idle' as 'idle' | 'loading' | 'succeeded' | 'failed',
    error: null as string | null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchSignature.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(fetchSignature.fulfilled, (state, action) => {
        state.signature = action.payload;
        state.status = 'succeeded';
      })
      .addCase(fetchSignature.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.error.message!;
      });
  },
});

export default signatureSlice.reducer;
