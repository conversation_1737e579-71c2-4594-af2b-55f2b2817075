/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  Collapse,
  FormControl,
  Chip,
} from '@mui/material';
import { MdAdd } from 'react-icons/md';
import styled from 'styled-components';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import SearchIcon from '@mui/icons-material/Search';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import DeleteIcon from '@mui/icons-material/Delete';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import { STATUS_SELECT } from '@/config/Selection';
import CreateGroups from './CreateGroups';

const GroupsRoot = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 40px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

export const data = [
  {
    slNo: 1,
    groupName: '',
    description: 'Welcome to PassDaily - eConnect for Schools and College',
    subject: 'Welcome to PassDaily',
  },
  { slNo: 2, groupName: '', description: 'Keep your password safe', subject: 'Password' },
  {
    slNo: 3,
    groupName: '',
    description: 'PassDaily Team Welcomes you to the eConnect tool for Schools... - eConnect for Schools and College',
    subject: 'Message',
  },
  {
    slNo: 4,
    groupName: '',
    description: 'Welcome to PassDaily - eConnect for Schools and College',
    subject: 'PassDaily',
  },
  { slNo: 5, groupName: '', description: 'Keep your password safe', subject: 'Password' },
  {
    slNo: 6,
    groupName: '',
    description: 'PassDaily Team Welcomes you to the eConnect tool for Schools... - eConnect for Schools and College',
    subject: 'Message',
  },
  {
    slNo: 7,
    groupName: '',
    description: 'Welcome to PassDaily - eConnect for Schools and College',
    subject: 'Welcome',
  },
];

function Groups() {
  const [showFilter, setShowFilter] = useState(false);
  const [drawerOpen, setDrawerOpen] = useState(false);

  const handleDrawerOpen = () => {
    setDrawerOpen(true);
  };

  const getRowKey = useCallback((row: any) => row.examId, []);
  const AuthorizeKeywordsColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'slNo',
        dataKey: 'slNo',
        headerLabel: 'Sl.No',
        sortable: true,
      },
      {
        name: 'groupName',
        dataKey: 'groupName',
        headerLabel: 'Group Name',
      },
      {
        name: 'groupType',
        dataKey: 'groupType',
        headerLabel: 'Group Type',
      },
      {
        name: 'status',
        dataKey: 'status',
        headerLabel: 'Status',
        sortable: true,
        renderCell: (row) => {
          return (
            <Chip
              size="small"
              label={row.classStatus === 0 ? 'Unpublished' : 'Published'}
              variant="outlined"
              color={row.classStatus === 1 ? 'error' : 'success'}
            />
          );
        },
      },

      {
        name: 'actions',
        headerLabel: 'Actions',
        renderCell: () => {
          return (
            <Stack direction="row" gap={1}>
              <IconButton size="small" sx={{ padding: 0.5 }}>
                <ModeEditIcon />
              </IconButton>
              <IconButton size="small" color="error" sx={{ padding: 0.5 }}>
                <DeleteIcon />
              </IconButton>
            </Stack>
          );
        },
      },
    ],
    []
  );

  return (
    <Page title="Groups">
      <GroupsRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17} width="80%">
              Groups
            </Typography>
            <Box pb={1} sx={{ flexShrink: 0 }}>
              <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                <SearchIcon />
              </IconButton>
              <Button sx={{ borderRadius: '20px', mr: 2 }} variant="outlined" size="small" onClick={handleDrawerOpen}>
                <MdAdd size="20px" /> Create
              </Button>
            </Box>
          </Stack>
          <Divider />

          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={3} pt={1} container spacing={3} alignItems="end">
                  <Grid item lg="auto" md={6} xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', lg: 250 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Category Name
                      </Typography>
                      <TextField placeholder="Enter" />
                    </FormControl>
                  </Grid>

                  <Grid item lg="auto" md={6} xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', lg: 250 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Status
                      </Typography>
                      <Autocomplete
                        options={STATUS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select" />}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item lg="auto" xs={12}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Paper className="card-table-container" sx={{ marginTop: '12px' }}>
              <DataTable columns={AuthorizeKeywordsColumns} data={data} getRowKey={getRowKey} fetchStatus="success" />
            </Paper>
          </div>
          {/* <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 2 }}>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="secondary">
                Cancel
              </Button>
              <Button onClick={handleOpen} variant="contained" color="primary">
                Save
              </Button>
            </Stack>
          </Box> */}
        </Card>
      </GroupsRoot>

      <TemporaryDrawer
        onClose={() => setDrawerOpen(false)}
        state={drawerOpen}
        Title="Create Groups Details"
        DrawerContent={<CreateGroups onCancel={() => setDrawerOpen(false)} />}
      />
    </Page>
  );
}

export default Groups;
