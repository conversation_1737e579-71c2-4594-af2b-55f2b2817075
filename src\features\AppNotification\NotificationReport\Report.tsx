/* eslint-disable jsx-a11y/alt-text */
import {
  Autocomplete,
  Divider,
  Grid,
  Stack,
  TextField,
  Button,
  Table,
  TableContainer,
  TableCell,
  TableHead,
  TableRow,
  TableBody,
  Typography,
  Card,
  Paper,
} from '@mui/material';
import styled, { useTheme } from 'styled-components';
import { smsrows } from '@/config/smsData';
import { CLASS_SELECT } from '@/config/Selection';
import React from 'react';

const ReportRoot = styled.div`
  width: 100%;
  padding: 0.5rem 1rem;
  height: calc(100%);
  @media screen and (max-width: 996px) {
    height: 100%;
  }
  .top-card {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.grey[200] : props.theme.palette.grey[700]};
  }
`;

function Report() {
  const theme = useTheme();
  return (
    <ReportRoot>
      <Card className="top-card" sx={{ p: 3, boxShadow: 0 }}>
        <Stack direction="row" justifyContent="space-between" flexWrap="wrap">
          <Typography variant="h6" fontSize={14}>
            Subject : Home Work
          </Typography>
          <Typography variant="h6" fontSize={14}>
            From Date : 15/02/2023
          </Typography>
          <Typography variant="h6" fontSize={14}>
            Mail Type : Class Division
          </Typography>
        </Stack>
        <Typography pt={2} variant="h6" fontSize={14}>
          Content
        </Typography>
        <Typography pt={1} variant="h6" fontWeight={500} fontSize={14}>
          Lorem ipsum dolor sit, amet consectetur adipisicing elit. Nemo, incidunt velit nam molestiae quos mollitia
          excepturi unde voluptas expedita quam officia perspiciatis, facere, voluptatem non molestias? Dolorem fugit
          molestias iure?
        </Typography>
      </Card>
      <Divider />
      <Grid pb={4} container spacing={3} pt={2}>
        <Grid item lg={4} xs={12}>
          <Typography variant="h6" fontSize={14}>
            Select Class
          </Typography>
          <Autocomplete
            options={CLASS_SELECT}
            renderInput={(params) => <TextField {...params} placeholder="Select Type" />}
          />
        </Grid>
        <Grid item lg={4} xs={12}>
          <Typography variant="h6" fontSize={14}>
            Select Status
          </Typography>
          <Autocomplete
            options={CLASS_SELECT}
            renderInput={(params) => <TextField {...params} placeholder="Select Type" />}
          />
        </Grid>
        <Grid item lg={4} xs={12}>
          <Stack spacing={2} sx={{ pt: { xs: 0, md: 3.79 } }} direction="row">
            <Button variant="contained" color="secondary" fullWidth>
              Reset
            </Button>
            <Button variant="contained" color="primary" fullWidth>
              Search
            </Button>
          </Stack>
        </Grid>
      </Grid>

      <Divider />

      <Paper sx={{ border: '1px solid #e8e8e9', overflow: 'auto' }}>
        <TableContainer sx={{ width: { xs: '850px', md: '100%' }, height: 'calc(100vh - 430px)' }}>
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                <TableCell>Sl.No</TableCell>
                <TableCell>Student</TableCell>
                <TableCell>Class</TableCell>
                <TableCell>Parent/Guardian</TableCell>
                <TableCell>Date</TableCell>
                <TableCell>Status</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {smsrows.map((smsrow) => (
                <TableRow key={smsrow.rollNo} sx={{ borderBottom: '1px solid  rgb(200, 200, 200)' }}>
                  <TableCell>{smsrow.rollNo}</TableCell>
                  <TableCell>Fionna Grand</TableCell>
                  <TableCell>VII-A</TableCell>
                  <TableCell>01/02/2023</TableCell>
                  <TableCell>Parent</TableCell>
                  <TableCell>
                    <Paper
                      sx={{
                        border: '1px solid ',
                        display: 'flex',
                        justifyContent: 'center',
                        p: 0.5,
                        maxWidth: 85,
                        borderRadius: '20px',
                        backgroundColor: theme.palette.error.lighter,
                        color: theme.palette.error.main,
                      }}
                      className="status"
                    >
                      Unread
                    </Paper>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>
    </ReportRoot>
  );
}

export default Report;
