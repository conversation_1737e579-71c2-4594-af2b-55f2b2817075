/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  useTheme,
  IconButton,
  Collapse,
  Tooltip,
  FormControl,
  Chip,
  Snackbar,
  Alert,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
} from '@mui/material';
import styled from 'styled-components';
import PauseCircleFilledRoundedIcon from '@mui/icons-material/PauseCircleFilledRounded';
import MenuRoundedIcon from '@mui/icons-material/MenuRounded';
import PlayCircleRoundedIcon from '@mui/icons-material/PlayCircleRounded';
import { TbCloudUpload } from 'react-icons/tb';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import SearchIcon from '@mui/icons-material/Search';
import Popup from '@/components/shared/Popup/Popup';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import empty from '@/Parent-Side/assets/descriptiveExam/emptystate.png';
import { MdAdd, MdContentCopy } from 'react-icons/md';
import CloseIcon from '@mui/icons-material/Close';
import { Student, StudentInfoData } from '@/config/StudentDetails';
import useSettings from '@/hooks/useSettings';
import AutorenewOutlinedIcon from '@mui/icons-material/AutorenewOutlined';
import { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import DateSelect from '@/components/shared/Selections/DateSelect';
import MenuEditDelete from '@/components/shared/Selections/MenuEditDelete';
import CreateScheduleList from '@/features/LiveClass/ScheduleList/CreateScheduleList';
import InsertDriveFileIcon from '@mui/icons-material/InsertDriveFile';
import View from '../StudyMaterials/View';
import BackButton from '@/components/shared/BackButton';
import { Pagination } from '@mui/material';
import FilesUpload from '@/components/shared/Selections/FilesUpload';

const ExamRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  .student_card {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  }

  .Card {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    min-height: calc(100vh - 110px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 40px);
      flex-grow: 1;

      .card-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: auto;
        ::-webkit-scrollbar {
          width: 0px;
        }

        .MuiTableContainer-root {
          max-height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

function Exam({ onBackClick, FinishExam, resultView }: any) {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const [showFilter, setShowFilter] = useState(false);
  const [submit, setSubmit] = useState(false);
  const [finishExam, setFinishExam] = useState(false);
  const [uploaded, setUploaded] = useState<File[]>([]);

  const [students, setStudents] = useState(StudentInfoData);
  const [open, setOpen] = useState(false);
  const [openNew, setOpenNew] = useState(false);
  const [examPause, setExamPause] = useState(false);
  const [showNewMultiple, setShowNewMultiple] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState(null);

  // const handleToggle = () => {
  //   setChangeView((prevChangeView) => !prevChangeView);
  // };

  const handleClickSubmit = () => setSubmit(true);
  const handleClickCloseSubmit = () => setSubmit(false);

  const handlePageChange = useCallback((event: unknown, newPage: number) => {
    // loadClassList({ ...currentClassListRequest, pageNumber: newPage + 1 });
  }, []);

  const handleChangeRowsPerPage = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const newPageSize = +event.target.value;
    // loadClassList({ ...currentClassListRequest, pageNumber: 1, pageSize: newPageSize });
  }, []);

  const pageProps = useMemo(
    () => ({
      rowsPerPageOptions: [10, 20, 30],
      pageNumber: 1,
      pageSize: 10,
      totalRecords: 100,
      onPageChange: handlePageChange,
      onRowsPerPageChange: handleChangeRowsPerPage,
    }),
    [handleChangeRowsPerPage, handlePageChange]
  );

  const handleClose = () => {
    setOpen(false);
  };
  const handleOpenNew = () => {
    setOpenNew(true);
  };

  const handleCloseNew = () => {
    setOpenNew(false);
  };
  const handleToggleNewMultiple = () => {
    setShowNewMultiple((prevState) => !prevState);
  };

  const handleUpdate = (id: number, updatedData: Student) => {
    const updatedStudents = students.map((student) => (student.id === id ? { ...student, ...updatedData } : student));
    setStudents(updatedStudents);
    setOpen(false);
  };

  const getRowKey = useCallback((row: any) => row.examId, []);

  const [isSnackbarOpen, setIsSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  const handleCopyClick = (text: string) => {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        setIsSnackbarOpen(true);
        setSnackbarMessage('Link copied to clipboard');
      })
      .catch((err) => {
        console.error('Unable to copy link to clipboard', err);
      });
  };

  const handleCloseSnackbar = () => {
    setIsSnackbarOpen(false);
  };

  const handlePauseClick = () => {
    setExamPause(true);
    setIsSnackbarOpen(true);
    setSnackbarMessage('Exam paused');
  };

  const handlePlayClick = () => {
    setExamPause(false);
    setIsSnackbarOpen(true);
    setSnackbarMessage('Exam continued');
  };

  const [page, setPage] = React.useState(1);
  const handleChange = (event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value);
  };
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement> | React.DragEvent<HTMLDivElement>) => {
    let uploadFiles;

    if ('dataTransfer' in event) {
      event.preventDefault();
      uploadFiles = event.dataTransfer.files;
    } else {
      uploadFiles = event.target.files;
    }
    if (uploadFiles && uploadFiles.length > 0) {
      const files = event.target.files;
      if (files) {
        // const selectedFiles = Array.from(event.target.files);
        const selectedFiles = Array.from(files).map((file) => URL.createObjectURL(file));
        setUploaded((prevFiles) => [...prevFiles, ...selectedFiles]);
        console.log('uploaded::::----', uploaded);
      }
    }
  };
  const handleRemoveFile = (fileToRemove: string) => {
    setUploaded((prevFiles) => prevFiles.filter((file) => file !== fileToRemove));
  };
  return (
    <Page title="Schedule List">
      <ExamRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 1, md: 2 } }}>
          <Stack direction="row" mb={2} justifyContent="space-between">
            <BackButton
              onBackClick={onBackClick}
              title={
                <div>
                  <span style={{ color: 'grey' }}>Descriptive Exam</span>
                  <ChevronRightIcon fontSize="small" />
                  Annual Exam
                </div>
              }
            />
            <MenuRoundedIcon />
          </Stack>
          <Divider />
          <Box mt={1} display="flex" justifyContent="space-between">
            <Stack>
              <Typography variant="h6" fontSize={17}>
                Alex Peter
              </Typography>
              <Typography variant="subtitle1" color="secondary" fontSize={14}>
                Class : X-A
              </Typography>
            </Stack>
            {!resultView ? (
              <Stack direction="row" alignItems="center">
                <div>
                  <Typography variant="h6" fontSize={17}>
                    01&nbsp;&nbsp;20&nbsp;&nbsp;00
                  </Typography>
                  <Typography variant="subtitle1" color="secondary" fontSize={14}>
                    Hrs&nbsp;&nbsp;Sec&nbsp;&nbsp;Mins
                  </Typography>
                </div>
                <IconButton size="small" onClick={examPause ? handlePlayClick : handlePauseClick}>
                  {!examPause ? (
                    <PauseCircleFilledRoundedIcon color="success" sx={{ fontSize: '40px' }} />
                  ) : (
                    <PlayCircleRoundedIcon color="success" sx={{ fontSize: '40px' }} />
                  )}
                </IconButton>
              </Stack>
            ) : (
              <Typography variant="subtitle2" fontSize={17}>
                Mark : 5/10
              </Typography>
            )}
          </Box>
          <Stack sx={{ width: '100%' }} spacing={2} mt={2}>
            <Pagination
              color="primary"
              boundaryCount={20}
              sx={{ width: '100%' }}
              count={25}
              page={page}
              onChange={handleChange}
            />
            <Stack direction="row" justifyContent="space-between" alignItems="center">
              <Typography variant="subtitle2">Question: {page}</Typography>
              {!resultView && (
                <Typography variant="subtitle1" fontSize={12}>
                  5 Mark
                </Typography>
              )}
            </Stack>
            <Typography variant="subtitle2">
              Lorem, ipsum dolor sit amet consectetur adipisicing elit. Eligendi nobis soluta quae magni non nulla
              voluptas at amet ullam, facilis minima sed veritatis dolores perspiciatis cum alias quam deserunt
              reiciendis?
            </Typography>
            <Typography variant="subtitle2">Answer</Typography>
            {!resultView ? (
              <>
                <TextField
                  multiline
                  placeholder="Type here..."
                  InputProps={{
                    inputProps: {
                      style: { resize: 'vertical', minHeight: '100px', maxHeight: '200px' },
                    },
                  }}
                  size="small"
                />
                <Stack direction="row" justifyContent="end">
                  <Typography mt={-2} variant="subtitle1" fontSize={12}>
                    Clear Answer
                  </Typography>
                </Stack>
              </>
            ) : (
              <Typography variant="subtitle1" color="secondary" fontSize={13.5}>
                Lorem, ipsum dolor sit amet consectetur adipisicing elit. Eligendi nobis soluta quae magni non nulla
                voluptas at amet ullam, facilis minima sed veritatis dolores perspiciatis cum alias quam deserunt
                reiciendis
              </Typography>
            )}
          </Stack>
          <Grid container mb={3} spacing={3}>
            {uploaded.map((i) => (
              <Grid item lg={3}>
                <Card>
                  <IconButton
                    size="small"
                    className="close-button"
                    aria-label="close"
                    onClick={() => handleRemoveFile(i)}
                    sx={{
                      position: 'absolute',
                      right: 2,
                      top: 2,
                      zIndex: 1,
                      color: theme.palette.grey[500],
                      backgroundColor: theme.palette.common.white,
                      '&:hover': {
                        color: theme.palette.error.main,
                        backgroundColor: theme.palette.grey[200],
                      },
                    }}
                  >
                    <CloseIcon sx={{ fontSize: '15px' }} />
                  </IconButton>
                  <img src={i} alt="" />
                </Card>
              </Grid>
            ))}
          </Grid>
          {!resultView && <FilesUpload multiple accept="/*" onChange={handleFileChange} />}
          {!resultView && (
            <Stack mt={6} gap={4} direction="row" justifyContent="center">
              <Button onClick={handleClickSubmit} variant="contained" color="success">
                Submit
              </Button>
              <Button onClick={() => setFinishExam(true)} variant="contained" color="error">
                Finish
              </Button>
            </Stack>
          )}
          {resultView && (
            <Stack mt={4}>
              <Typography variant="subtitle2">Teacher Comments</Typography>
              <TextField size="small" />
            </Stack>
          )}
        </Card>
      </ExamRoot>
      <Popup
        size="xs"
        title="Select Option"
        state={submit}
        onClose={handleClickCloseSubmit}
        popupContent={
          <Stack p={2} pl={5}>
            <FormControl>
              <RadioGroup
                aria-labelledby="demo-radio-buttons-group-label"
                defaultValue="Resume"
                name="radio-buttons-group"
              >
                <FormControlLabel value="Resume" control={<Radio color="success" />} label="Resume" />
                <FormControlLabel value="SaveandExit" control={<Radio color="success" />} label="Save and Exit" />
                <FormControlLabel value="FinishExam" control={<Radio color="success" />} label="Finish Exam" />
              </RadioGroup>
            </FormControl>
          </Stack>
        }
      />
      <Popup
        size="xs"
        title="Exam Status"
        state={finishExam}
        onClose={() => setFinishExam(false)}
        popupContent={
          <>
            <Box p={3} color="black" display="flex" justifyContent="space-between">
              <Stack p={2} alignItems="center" bgcolor={theme.palette.success.lighter}>
                <Typography variant="subtitle2" fontSize={16}>
                  07
                </Typography>
                <Typography variant="subtitle1" fontSize={12}>
                  Answered
                </Typography>
              </Stack>
              <Stack p={2} alignItems="center" bgcolor={theme.palette.primary.lighter}>
                <Typography variant="subtitle2" fontSize={16}>
                  03
                </Typography>
                <Typography variant="subtitle1" fontSize={12}>
                  Not Answered
                </Typography>
              </Stack>
              <Stack p={2} alignItems="center" bgcolor={theme.palette.error.lighter}>
                <Typography variant="subtitle2" fontSize={16}>
                  10
                </Typography>
                <Typography variant="subtitle1" fontSize={12}>
                  Total Question
                </Typography>
              </Stack>
            </Box>
            <Stack px={3} gap={4} direction="row" justifyContent="center">
              <Button fullWidth onClick={() => setFinishExam(false)} variant="contained" color="secondary">
                Cancel
              </Button>
              <Button
                fullWidth
                onClick={() => {
                  onBackClick();
                  FinishExam();
                }}
                variant="contained"
                color="error"
              >
                Finish Exam
              </Button>
            </Stack>
          </>
        }
      />
      <Snackbar
        open={isSnackbarOpen}
        autoHideDuration={4000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert severity="success" sx={{ width: '100%' }}>
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Page>
  );
}

export default Exam;
