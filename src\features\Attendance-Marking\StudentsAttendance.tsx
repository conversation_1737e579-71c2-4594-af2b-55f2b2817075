/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/alt-text */
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Divider,
  Grid,
  Paper,
  Stack,
  Button,
  IconButton,
  Box,
  Avatar,
  Typography,
  Card,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  TableContainer,
  Table,
  Tooltip,
  useTheme,
  MenuItem,
  Collapse,
  Select,
  FormControl,
  SelectChangeEvent,
  Skeleton,
  ButtonGroup,
} from '@mui/material';
import styled from 'styled-components';
import { strows1 } from '@/config/TableData';
import { MONTH_SELECT } from '@/config/Selection';
import React, { useRef, useState } from 'react';
import useSettings from '@/hooks/useSettings';
import { FiUserX } from 'react-icons/fi';
import { TbBeach } from 'react-icons/tb';
import SelectAllIcon from '@mui/icons-material/SelectAll';
import {
  getClassData,
  getAttendanceCalendarListData,
  getAttendanceCalendarListStatus,
  getYearData,
  getAttendanceListSubmitting,
  getYearStatus,
} from '@/config/storeSelectors';
import NavigateBeforeIcon from '@mui/icons-material/NavigateBefore';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import { useAppSelector } from '@/hooks/useAppSelector';
import { fetchClassList, fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import useAuth from '@/hooks/useAuth';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useNavigate } from 'react-router-dom';
import { Dayjs } from 'dayjs';
import { AttendanceCalendarRequest } from '@/types/AttendanceMarking';
import { fetchAttendanceCalendar } from '@/store/AttendanceMarking/attendanceMarking.thunks';
import NoData from '@/assets/no-datas.png';
import ReportIcon from '@mui/icons-material/Report';
import SearchIcon from '@mui/icons-material/Search';
import { CloseIcon, SuccessIcon } from '@/theme/overrides/CustomIcons';

const StudentsAttendanceRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  .MuiTableHead-root {
    position: sticky;
    left: 0px;
  }

  td {
    min-width: 110px;
  }
  .date {
    margin-left: auto;
  }

  .css-5m1m10-MuiTableCell-root:first-of-type {
    padding-left: 0;
  }
  .border_radius_none {
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
    border-bottom: 0px;
  }
  .stdnt_name {
    color: ${(props) => (props.theme.themeMode === 'light' ? props.theme.palette.grey[900] : '#fff')};
  }
  .Card {
    min-height: calc(100vh - 100px);
    @media screen and (max-width: 1199.5px) {
      height: 100%;
    }
  }
  /* width */
  .scrollBar::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }

  /* Track */
  .scrollBar::-webkit-scrollbar-track {
    /* box-shadow: inset 0 0 3px grey; */
    /* border-radius: 10px; */
  }

  /* Handle */
  .scrollBar::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background-color: ${(props) => (props.theme.themeMode === 'light' ? props.theme.palette.primary.light : '#fff')};
  }
`;

function StudentsAttendance() {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const dispatch = useAppDispatch();
  const { user } = useAuth();
  const navigate = useNavigate();
  const adminId: number = user ? user.accountId : 0;
  const YearData = useAppSelector(getYearData);
  const ClassData = useAppSelector(getClassData);
  const defualtYear = YearData[0] || { accademicId: 10, accademicTime: '2023-2024' };
  const [academicYearFilter, setAcademicYearFilter] = useState(defualtYear.accademicId);
  const [showFilter, setShowFilter] = useState(true);
  const [classFilter, setClassFilter] = useState(33);
  const [absentDateFilter, setAbsentDateFilter] = useState<Dayjs | string>('');
  const [monthFilter, setMonthFilter] = useState(12);
  const AttendanceCalendarStatus = useAppSelector(getAttendanceCalendarListStatus);
  const YearStatus = useAppSelector(getYearStatus);
  const AttendanceCalendarData = useAppSelector(getAttendanceCalendarListData);
  const isSubmitting = useAppSelector(getAttendanceListSubmitting);

  const [selectedStatus, setSelectedStatus] = useState('All');
  const currentAttendanceCalendarRequest: AttendanceCalendarRequest = React.useMemo(
    () => ({
      adminId,
      academicId: academicYearFilter,
      academicTime: YearData?.find((y) => y.accademicId === academicYearFilter)?.accademicTime || '',
      classId: classFilter,
      month: monthFilter,
    }),
    [YearData, academicYearFilter, adminId, classFilter, monthFilter]
  );

  const loadAttendanceCalendar = React.useCallback(
    async (request: AttendanceCalendarRequest) => {
      try {
        // const data = await Promise.all([dispatch(fetchAttendanceCalendar(request)), setSelectedABS([])]);
        const datas = await dispatch(fetchAttendanceCalendar(request)).unwrap();
        console.log('datas:', datas);
        // const LeaveData = datas.data ? datas.data.map((item: AttendanceCalendarInfo) => ({ ...item })) : [];
      } catch (error) {
        console.error('Error loading list:', error);
      }
    },
    [dispatch]
  );
  React.useEffect(() => {
    if (AttendanceCalendarStatus === 'idle') {
      dispatch(fetchYearList(adminId));
      dispatch(fetchClassList(adminId));
      loadAttendanceCalendar(currentAttendanceCalendarRequest);
    }
    // loadAttendanceCalendar(currentAttendanceCalendarRequest);
    // dispatch(
    //   fetchAttendanceCalendar({
    //     accademicId: 10,
    //     adminId,
    //     classId: 1,
    //     absentDate: dateFilter,
    //   })
    // );
  }, [
    dispatch,
    adminId,
    loadAttendanceCalendar,
    YearStatus,
    AttendanceCalendarStatus,
    currentAttendanceCalendarRequest,
    setAcademicYearFilter,
  ]);

  const handleYearChange = (e: SelectChangeEvent) => {
    const selectedAcademicId = parseInt(e.target.value, 10);
    const selectedYear = YearData?.find((y) => y.accademicId === selectedAcademicId);
    setAcademicYearFilter(selectedAcademicId);
    loadAttendanceCalendar({
      ...currentAttendanceCalendarRequest,
      academicId: selectedAcademicId,
      academicTime: selectedYear?.accademicTime || '',
    });
  };

  const handleClassChange = (e: SelectChangeEvent) => {
    const selectedClass = parseInt(e.target.value, 10);
    if (selectedClass) {
      setClassFilter(selectedClass);
    }
    loadAttendanceCalendar({
      ...currentAttendanceCalendarRequest,
      classId: selectedClass || 0,
    });
  };
  const handleMonthChange = (e: SelectChangeEvent) => {
    const selectedMonth = parseInt(e.target.value, 10);
    if (selectedMonth) {
      setMonthFilter(selectedMonth);
    }
    loadAttendanceCalendar({
      ...currentAttendanceCalendarRequest,
      month: selectedMonth || 0,
    });
  };

  const handleReset = React.useCallback(
    (e: any) => {
      e.preventDefault();
      setClassFilter(-1);
      setAcademicYearFilter(-1);
      setAbsentDateFilter('');
      setMonthFilter(-1);
      loadAttendanceCalendar({
        academicId: -1,
        adminId,
        academicTime: '',
        classId: -1,
        month: -1,
      });
    },
    [loadAttendanceCalendar, adminId]
  );

  const tableRef = useRef<HTMLDivElement | null>(null);

  const scrollTable = (direction: 'left' | 'right') => {
    if (tableRef.current) {
      const scrollAmount = 300; // You can adjust this
      tableRef.current.scrollBy({
        left: direction === 'left' ? -scrollAmount : scrollAmount,
        behavior: 'smooth',
      });
    }
  };

  return (
    <Page title="Detailed Class Marking">
      <StudentsAttendanceRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack pb={1} direction="row" justifyContent="space-between" alignItems="center" flexWrap="wrap">
            <Stack direction="row" alignItems="center" justifyContent="space-between" flex={1} whiteSpace="nowrap">
              <Typography variant="h6" fontSize={17} width="100%">
                Student Attendance
              </Typography>
              <Tooltip title="Search">
                <IconButton aria-label="" color="primary" sx={{ mr: 1 }} onClick={() => setShowFilter((x) => !x)}>
                  <SearchIcon />
                </IconButton>
              </Tooltip>
            </Stack>
          </Stack>
          <Divider />
          <Collapse in={showFilter}>
            <form noValidate style={{ width: '100%' }}>
              <Grid pb={4} pt={1} container spacing={2}>
                <Grid item xxl={1.5} xl={2.5} lg={2.5} md={4} sm={4} xs={12}>
                  <FormControl fullWidth>
                    <Typography variant="subtitle1" fontSize={12} color="GrayText">
                      Academic Year
                    </Typography>
                    <Select
                      labelId="academicYearFilter"
                      id="academicYearFilterSelect"
                      value={academicYearFilter?.toString()}
                      // defaultValue={YearData[0]?.accademicId}
                      onChange={handleYearChange}
                      placeholder="Select Year"
                    >
                      <MenuItem value={-1}>Select All</MenuItem>
                      {YearData.map((opt) => (
                        <MenuItem key={opt.accademicId} value={opt.accademicId}>
                          {opt.accademicTime}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xxl={1.5} xl={2.5} lg={2.5} md={4} sm={4} xs={12}>
                  <FormControl fullWidth>
                    <Typography variant="subtitle1" fontSize={12} color="GrayText">
                      Select Class
                    </Typography>
                    <Select
                      labelId="academicYearFilter"
                      id="academicYearFilterSelect"
                      value={classFilter?.toString()}
                      // defaultValue={YearData[0]?.accademicId}
                      onChange={handleClassChange}
                      placeholder="Select Class"
                    >
                      <MenuItem value={-1}>Select All</MenuItem>
                      {ClassData.map((opt) => (
                        <MenuItem key={opt.classId} value={opt.classId}>
                          {opt.className}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xxl={1.5} xl={2.5} lg={2.5} md={4} sm={4} xs={12}>
                  <FormControl fullWidth>
                    <Typography variant="subtitle1" fontSize={12} color="GrayText">
                      Select Month
                    </Typography>
                    <Select
                      labelId="academicYearFilter"
                      id="academicYearFilterSelect"
                      value={monthFilter?.toString()}
                      // defaultValue={YearData[0]?.accademicId}
                      onChange={handleMonthChange}
                      placeholder="Select Month"
                    >
                      {MONTH_SELECT.map((opt) => (
                        <MenuItem key={opt.id} value={opt.id}>
                          {opt.month}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item lg={1} sm={2} xs={12}>
                  <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, sm: 3.79 } }}>
                    <Button variant="contained" color="secondary" fullWidth onClick={handleReset}>
                      Reset
                    </Button>
                    {/* <Button variant="contained" color="primary" fullWidth>
                          Search
                        </Button> */}
                  </Stack>
                </Grid>
                <Grid item xxl={6.5} xl={6} lg={6} md={6} sm={6} xs={12} overflow={{ xs: 'auto', sm: 'initial' }}>
                  <Stack direction="row" gap={2} alignItems="center">
                    <ButtonGroup
                      sx={{ whiteSpace: 'nowrap', pt: { xs: 0, sm: 3.79 } }}
                      variant="outlined"
                      aria-label="attendance filter"
                    >
                      {[
                        {
                          status: 'All',
                          icon: <SelectAllIcon />,
                          color: theme.palette.secondary.main,
                        },
                        {
                          status: 'Not Taken',
                          icon: (
                            <Stack
                              sx={{
                                backgroundColor: theme.palette.warning.main,
                                borderRadius: 10,
                                padding: 0.5,
                              }}
                            >
                              <FiUserX fontSize={14} style={{ color: theme.palette.common.white }} />
                            </Stack>
                          ),
                          color: theme.palette.warning.main,
                        },
                        {
                          status: 'Present',
                          icon: <SuccessIcon />,
                          color: theme.palette.success.main,
                        },
                        {
                          status: 'Absent',
                          icon: <CloseIcon />,
                          color: theme.palette.error.main,
                        },
                        {
                          status: 'Holiday',
                          icon: (
                            <Stack
                              sx={{
                                backgroundColor: theme.palette.chart.violet[0],
                                borderRadius: 10,
                                padding: 0.5,
                              }}
                            >
                              <TbBeach fontSize={14} style={{ color: theme.palette.common.white }} />
                            </Stack>
                          ),
                          color: theme.palette.chart.violet[0],
                        },
                      ].map(({ status, icon, color }) => (
                        <Button
                          key={status}
                          variant={selectedStatus === status ? 'contained' : 'outlined'}
                          color="secondary"
                          sx={{
                            borderColor: color,
                            color,
                            ...(selectedStatus === status && {
                              backgroundColor: color,
                              color: theme.palette.common.white,
                            }),
                            '&:hover': {
                              borderColor: color,
                              backgroundColor: color,
                              color: theme.palette.common.white,
                            },
                          }}
                          onClick={() => setSelectedStatus(status)}
                          startIcon={icon}
                        >
                          {status}
                        </Button>
                      ))}
                    </ButtonGroup>
                    <ButtonGroup
                      color="secondary"
                      sx={{
                        pt: { xs: 0, sm: 3.79 },
                        whiteSpace: 'nowrap',
                        '& .MuiButton-root': {
                          minWidth: '20px',
                          padding: '5px 5px', // Reduce padding
                        },
                      }}
                      variant="outlined"
                      aria-label="attendance filter"
                    >
                      <Button onClick={() => scrollTable('left')}>
                        <NavigateBeforeIcon />
                      </Button>
                      <Button onClick={() => scrollTable('right')}>
                        <NavigateNextIcon />
                      </Button>
                    </ButtonGroup>
                  </Stack>
                </Grid>
              </Grid>
            </form>
          </Collapse>
          {AttendanceCalendarStatus === 'loading' ? (
            <Paper
              sx={{
                width: '100%',
                height: 'calc(100vh - 230px)',
                overflow: 'hidden',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                gap: 2,
                padding: 2,
              }}
            >
              {/* Table Header Skeleton */}
              <Skeleton variant="rectangular" width="100%" height={40} sx={{ borderRadius: 1 }} />

              {/* Table Rows Skeleton */}
              <Stack spacing={1} width="100%">
                {Array.from({ length: 6 }).map(() => (
                  <Skeleton
                    key={crypto.randomUUID()}
                    variant="rectangular"
                    width="100%"
                    height={30}
                    sx={{ borderRadius: 1 }}
                  />
                ))}
              </Stack>
            </Paper>
          ) : (
            <Paper
              sx={{
                // border: `1px solid #e8e8e9`,
                width: '100%',
                height: 'calc(100vh - 230px)',
                overflow: 'hidden',
              }}
            >
              {AttendanceCalendarData &&
              AttendanceCalendarData?.studentList &&
              AttendanceCalendarData?.studentList.length > 0 ? (
                <TableContainer
                  ref={tableRef}
                  className="scrollBar"
                  sx={{
                    width: { xs: '700px', md: '100%' },
                    height: '100%',
                  }}
                >
                  <Table stickyHeader aria-label="simple table">
                    <TableHead sx={{ zIndex: 1 }}>
                      <TableRow>
                        <TableCell
                          sx={{
                            backgroundColor: isLight ? theme.palette.grey[200] : theme.palette.grey[900],
                            borderBottom: 0,
                            position: 'sticky',
                            left: '0px',
                            zIndex: '10',
                            textAlign: 'center',
                            width: '50px',
                            color: theme.palette.text.secondary,
                          }}
                        >
                          STUDENT
                        </TableCell>
                        {strows1.map((header) => (
                          <TableCell
                            align="center"
                            sx={{
                              color: theme.palette.text.secondary,
                              backgroundColor: isLight ? theme.palette.grey[200] : theme.palette.grey[900],
                              borderBottom: 0,
                            }}
                          >
                            {header.days.toUpperCase()}
                          </TableCell>
                        ))}
                        {/* {AttendanceCalendarData?.reprtHeader.map((header) => (
                        <TableCell>{header.absentDay}</TableCell>
                      ))} */}
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {AttendanceCalendarData?.studentList.map((student) => (
                        <TableRow hover className="" key={student.rollNo}>
                          <TableHead>
                            <TableCell
                              sx={{ position: 'sticky', left: '0px', zIndex: '999999' }}
                              className="border_radius_none"
                            >
                              <Stack direction="column" display="flex" alignItems="center">
                                <Avatar src="student.image" />
                                <Tooltip title={student.studentName}>
                                  <Typography
                                    className="stdnt_name"
                                    sx={{
                                      textOverflow: 'ellipsis',
                                      overflow: 'hidden',
                                      width: '100px',
                                      textAlign: 'center',
                                      whiteSpace: 'nowrap',
                                    }}
                                    fontWeight={600}
                                    fontSize={11}
                                  >
                                    {student.studentName}
                                  </Typography>
                                </Tooltip>
                                <Typography fontWeight={600} fontSize={9}>
                                  Roll No. {student.rollNo}
                                </Typography>
                              </Stack>
                            </TableCell>
                          </TableHead>

                          {AttendanceCalendarData?.reprtHeader.map((row) => {
                            let attendanceStatus = 'Not Taken';

                            if (AttendanceCalendarData.holidays.some((h) => h.holiday === row.absentDay)) {
                              attendanceStatus = 'Holiday';
                            } else if (
                              AttendanceCalendarData.attendanceTakenDays.some(
                                (day) => day.attendanceTakenDays === row.absentDay
                              )
                            ) {
                              attendanceStatus = student.studentAbentDays?.some((ab) => ab.absentDay === row.absentDay)
                                ? 'Absent'
                                : 'Present';
                            }

                            // Show only selected status
                            if (selectedStatus !== 'All' && selectedStatus !== attendanceStatus) {
                              return (
                                <TableCell key={row.absentDay} sx={{ py: '7px' }}>
                                  <Paper
                                    sx={{
                                      height: '100%',
                                      background: isLight
                                        ? ' linear-gradient(180deg, rgba(231,240,244,1) 35%, rgba(239,245,248,1) 79%, rgba(255,255,255,1) 100%);'
                                        : theme.palette.grey[900],
                                      px: 1,
                                      py: 1,
                                    }}
                                  >
                                    <Stack justifyContent="center" alignItems="center" height="100%">
                                      <ReportIcon color="secondary" fontSize="small" />
                                    </Stack>
                                  </Paper>
                                </TableCell>
                              );
                            }

                            return (
                              <TableCell sx={{ '&:first-of-type': { pl: '5px' } }}>
                                <Paper
                                  sx={{
                                    height: '100%',
                                    background: isLight
                                      ? ' linear-gradient(180deg, rgba(231,240,244,1) 35%, rgba(239,245,248,1) 79%, rgba(255,255,255,1) 100%);'
                                      : theme.palette.grey[900],
                                    // background: AttendanceCalendarData.holidays.find((h) => h.holiday === row.absentDay)
                                    //   ? theme.palette.info.lighter
                                    //   : AttendanceCalendarData.attendanceTakenDays.find(
                                    //       (day) => day.attendanceTakenDays === row.absentDay
                                    //     )
                                    //   ? student.studentAbentDays?.find((ab) => ab.absentDay === row.absentDay)
                                    //     ? theme.palette.error.light
                                    //     : theme.palette.success.lighter
                                    //   : '  linear-gradient(180deg, rgba(231,240,244,1) 35%, rgba(239,245,248,1) 79%, rgba(255,255,255,1) 100%);',
                                    px: 1,
                                    py: 1,
                                  }}
                                >
                                  <Stack direction="column" display="flex" alignItems="center">
                                    <Typography
                                      variant="h6"
                                      fontSize={12}
                                      sx={{
                                        color: isLight ? theme.palette.common.black : theme.palette.common.white,
                                      }}
                                      className="date"
                                    >
                                      {String(row.absentDay).padStart(2, '0')}
                                    </Typography>

                                    <Stack
                                      direction="row"
                                      display="flex "
                                      alignItems="center"
                                      width="100%"
                                      justifyContent="end"
                                      gap={1}
                                      color="initial"
                                    >
                                      {' '}
                                      <Typography
                                        sx={{
                                          color: isLight ? theme.palette.common.black : theme.palette.common.white,
                                        }}
                                        fontSize={10}
                                        fontWeight={600}
                                      >
                                        {attendanceStatus}
                                      </Typography>
                                      {/* <Typography fontSize={10} fontWeight={600}>
                                    {AttendanceCalendarData.holidays.find((h) => h.holiday === row.absentDay)
                                      ? 'Holiday'
                                      : AttendanceCalendarData.attendanceTakenDays.find(
                                          (day) => day.attendanceTakenDays === row.absentDay
                                        )
                                      ? student.studentAbentDays?.find((ab) => ab.absentDay === row.absentDay)
                                        ? 'Absent'
                                        : 'Present'
                                      : 'Not Taken'}
                                  </Typography> */}
                                      <Stack>
                                        {AttendanceCalendarData.holidays.find((h) => h.holiday === row.absentDay) ? (
                                          <Stack
                                            sx={{
                                              backgroundColor: theme.palette.chart.violet[0],
                                              borderRadius: 10,
                                              padding: 0.5,
                                            }}
                                          >
                                            <TbBeach fontSize={14} style={{ color: theme.palette.common.white }} />
                                          </Stack>
                                        ) : AttendanceCalendarData.attendanceTakenDays.find(
                                            (day) => day.attendanceTakenDays === row.absentDay
                                          ) ? (
                                          student.studentAbentDays?.find((ab) => ab.absentDay === row.absentDay) ? (
                                            <CloseIcon color="error" />
                                          ) : (
                                            <SuccessIcon color="success" />
                                          )
                                        ) : (
                                          <Stack
                                            sx={{
                                              backgroundColor: theme.palette.warning.main,
                                              borderRadius: 10,
                                              padding: 0.5,
                                            }}
                                          >
                                            <FiUserX fontSize={14} style={{ color: theme.palette.common.white }} />
                                          </Stack>
                                        )}
                                        {/* <img
                                    className="icon"
                                    width={15}
                                    height={15}
                                    src={
                                      AttendanceCalendarData.holidays.find((h) => h.holiday === row.absentDay)
                                        ? holiday
                                        : AttendanceCalendarData.attendanceTakenDays.find(
                                            (day) => day.attendanceTakenDays === row.absentDay
                                          )
                                        ? student.studentAbentDays?.find((ab) => ab.absentDay === row.absentDay)
                                          ? absent
                                          : present
                                        : notTaken
                                    }
                                  /> */}
                                        {/* <Typography fontSize={12} fontWeight={600}>
                                    {AttendanceCalendarData.holidays.find((h) => h.holiday === row.absentDay)
                                      ? ' '
                                      : AttendanceCalendarData.attendanceTakenDays.find(
                                          (day) => day.attendanceTakenDays === row.absentDay
                                        )
                                      ? student.studentAbentDays?.find((ab) => ab.absentDay === row.absentDay)
                                        ? 'Absent'
                                        : 'Present'
                                      : ' '}
                                  </Typography> */}
                                      </Stack>
                                    </Stack>
                                  </Stack>
                                </Paper>
                              </TableCell>
                            );
                          })}
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <Box
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                  width="100%"
                  height={{ xs: 'calc(100vh - 400px)', sm: 'calc(100vh - 400px)', lg: 'calc(100vh - 284px)' }}
                >
                  <Stack direction="column" alignItems="center">
                    <img src={NoData} width="150px" alt="" />
                    <Typography variant="subtitle2" mt={2} color="GrayText">
                      No data found !
                    </Typography>
                  </Stack>
                </Box>
              )}
            </Paper>
          )}
          {/* 
          <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 2 }}>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="error" sx={{ gap: 1 }} size="medium">
                <img className="icon" src={Pdf} />
                <Typography color="white" fontSize={12}>
                  Download
                </Typography>
              </Button>
              <Button variant="contained" color="success" sx={{ gap: 1 }} size="medium">
                <img className="icon" src={Excel} />{' '}
                <Typography color="white" fontSize={12}>
                  Download
                </Typography>
              </Button>
            </Stack>
          </Box> */}
        </Card>
      </StudentsAttendanceRoot>
    </Page>
  );
}

export default StudentsAttendance;
