import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ParentPaymentState } from '@/types/Payment';
import { fetchParentPayFee, fetchReceiptOnline } from './payment.thunk';
import { flushStore } from '../flush.slice';

const initialState: ParentPaymentState = {
  parentPayFee: {
    data: [],
    status: 'idle',
    error: null,
  },
  receiptOnlineList: {
    data: [],
    status: 'idle',
    error: null,
  },
  submitting: false,
  deletingRecords: {},
  error: null,
};

export const messageBoxSlice = createSlice({
  name: 'payment',
  initialState,
  reducers: {
    setSubmitting: (state, action: PayloadAction<boolean>) => {
      state.submitting = action.payload;
    },
  },

  extraReducers(builder) {
    builder
      // ------Get Parent Pay Fee------- //
      .addCase(fetchParentPayFee.pending, (state) => {
        state.parentPayFee.status = 'loading';
        state.parentPayFee.error = null;
      })
      .addCase(fetchParentPayFee.fulfilled, (state, action) => {
        state.parentPayFee.status = 'success';
        state.parentPayFee.data = action.payload;
        state.parentPayFee.error = null;
      })
      .addCase(fetchParentPayFee.rejected, (state, action) => {
        state.parentPayFee.status = 'error';
        state.parentPayFee.error = action.payload || 'Unknown error in fetching Parent Pay Fee List';
      })
      // ------Get Receipt Online------- //
      .addCase(fetchReceiptOnline.pending, (state) => {
        state.receiptOnlineList.status = 'loading';
        state.receiptOnlineList.error = null;
      })
      .addCase(fetchReceiptOnline.fulfilled, (state, action) => {
        state.receiptOnlineList.status = 'success';
        state.receiptOnlineList.data = action.payload;
        state.receiptOnlineList.error = null;
      })
      .addCase(fetchReceiptOnline.rejected, (state, action) => {
        state.receiptOnlineList.status = 'error';
        state.receiptOnlineList.error = action.payload || 'Unknown error in fetching Receipt Online';
      })
      .addCase(flushStore, () => initialState);
  },
});

export const { setSubmitting } = messageBoxSlice.actions;

export default messageBoxSlice.reducer;
