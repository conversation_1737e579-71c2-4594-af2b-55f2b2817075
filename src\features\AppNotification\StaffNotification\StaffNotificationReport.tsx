/* eslint-disable jsx-a11y/alt-text */
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Autocomplete,
  Divider,
  Grid,
  Stack,
  TextField,
  Button,
  Table,
  TableContainer,
  TableCell,
  TableHead,
  TableRow,
  TableBody,
  Typography,
  Card,
  IconButton,
  Paper,
} from '@mui/material';
import styled from 'styled-components';
import DeleteIcon from '@mui/icons-material/Delete';
import { quicksendlinks1 } from '@/config/smsData';
import { CLASS_SELECT } from '@/config/Selection';
import Popup from '@/components/shared/Popup/Popup';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import React from 'react';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import { Details } from './Details';

const NotificationReportStaffRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  padding: 1rem;
  .Card {
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }
  }
`;

function NotificationReportStaff() {
  const [Delete, setDelete] = React.useState(false);
  const [openDetails, setOpenDetails] = React.useState(false);

  const handleClickDelete = () => setDelete(true);
  const handleClickCloseDelete = () => setDelete(false);

  const handleClickCloseDetails = () => setOpenDetails(false);
  return (
    <Page title="Notification Report Staff">
      <NotificationReportStaffRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Typography variant="h6" fontSize={17}>
            Notification Report Staff
          </Typography>
          <Divider />
          <Grid pb={4} container spacing={3} pt={2}>
            <Grid item lg={2.4} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Subject
              </Typography>
              <TextField fullWidth placeholder="Enter Subject" />
            </Grid>
            <Grid item lg={2.4} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Mail Type
              </Typography>
              <Autocomplete
                options={CLASS_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select Type" />}
              />
            </Grid>
            <Grid item lg={2.4} xs={12}>
              <Typography variant="h6" fontSize={14}>
                From
              </Typography>
              <TextField fullWidth type="date" placeholder="dd-mm-yyyy" />
            </Grid>
            <Grid item lg={2.4} xs={12}>
              <Typography variant="h6" fontSize={14}>
                To
              </Typography>
              <TextField fullWidth type="date" placeholder="dd-mm-yyyy" />
            </Grid>
            <Grid item lg={2.4} xs={12}>
              <Stack spacing={2} sx={{ pt: { xs: 0, md: 3.79 } }} direction="row">
                <Button variant="contained" color="secondary" fullWidth>
                  Reset
                </Button>
                <Button variant="contained" color="primary" fullWidth>
                  Search
                </Button>
              </Stack>
            </Grid>
          </Grid>

          <Divider />

          <Paper sx={{ border: '1px solid #e8e8e9', overflow: 'auto' }}>
            <TableContainer sx={{ width: { xs: '750px', md: '100%' } }}>
              <Table stickyHeader>
                <TableHead>
                  <TableRow>
                    <TableCell>Sl.No</TableCell>
                    <TableCell>Title</TableCell>
                    <TableCell>Date</TableCell>
                    <TableCell>Mail Type</TableCell>
                    <TableCell>Staff</TableCell>
                    <TableCell>Sent By</TableCell>
                    <TableCell>Action</TableCell>
                    <TableCell> </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {quicksendlinks1.map((smsrow) => (
                    <TableRow key={smsrow.id} sx={{ borderBottom: '1px solid  rgb(200, 200, 200)' }}>
                      <TableCell>{smsrow.id}</TableCell>
                      <TableCell>SMS Inbox</TableCell>
                      <TableCell>01/02/2023</TableCell>
                      <TableCell>Parent</TableCell>
                      <TableCell>Micheal</TableCell>
                      <TableCell>Passdaily</TableCell>
                      <TableCell>
                        <IconButton onClick={handleClickDelete} size="small">
                          <DeleteIcon />
                        </IconButton>
                        <IconButton onClick={() => setOpenDetails(true)} size="small">
                          <MoreVertIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        </Card>
      </NotificationReportStaffRoot>
      <Popup
        size="xs"
        state={Delete}
        onClose={handleClickCloseDelete}
        popupContent={<DeleteMessage message="Are you sure want to delete?" />}
      />
      <Popup size="md" state={openDetails} onClose={handleClickCloseDetails} popupContent={<Details />} />
    </Page>
  );
}

export default NotificationReportStaff;
