/* eslint-disable no-nested-ternary */
/* eslint-disable react/no-array-index-key */
/* eslint-disable jsx-a11y/alt-text */
import React, { FormEvent, useCallback, useState } from 'react';
import {
  Divider,
  Grid,
  Paper,
  Stack,
  Button,
  Typography,
  Box,
  FormControl,
  Select,
  MenuItem,
  SelectChangeEvent,
  useTheme,
  IconButton,
  Tooltip,
} from '@mui/material';
import styled from 'styled-components';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useAppSelector } from '@/hooks/useAppSelector';
import {
  getGroupMembersListStatus,
  getYearData,
  getMessageTempSubmitting,
  getGroupListData,
} from '@/config/storeSelectors';
import useAuth from '@/hooks/useAuth';
import { fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import {
  fetchGroupsList,
  fetchGroupMembersList,
  messageSendToGroupMembersList,
} from '@/store/MessageBox/messageBox.thunks';
import LoadingButton from '@mui/lab/LoadingButton';
import SendIcon from '@mui/icons-material/Send';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import LoadingMsg from '@/assets/MessageIcons/loading-message.gif';
import { FcCancel, FcOk } from 'react-icons/fc';
import { SendToGroupMembersType, GroupMembersDataType } from '@/types/MessageBox';
import ErrorMsg from '@/assets/MessageIcons/error-message.gif';
import ErrorMsg1 from '@/assets/MessageIcons/error-message1.gif';
import ErrorMsg2 from '@/assets/MessageIcons/error-message2.gif';
import SuccessMsg from '@/assets/MessageIcons/message-success.gif';
import Lottie from 'lottie-react';
import successIcon from '@/assets/MessageIcons/success.json';
import { SendPopupProps } from '@/types/Common';
import { voiceMessageSendToGroupMembersList } from '@/store/VoiceMessage/voiceMessage.thunk';
import { SendVoiceToGroupRequest } from '@/types/VoiceMessage';
import errorIcon from '@/assets/MessageIcons/error-message.json';
import { SuccessMessage } from '../Popup/SuccessMessage';
import { ErrorMessage } from '../Popup/ErrorMessage';
import { useConfirm } from '../Popup/Confirmation';
import LoadingPopup from '../Popup/LoadingPopup';
import { LoadingMessage } from '../Popup/LoadingMessage';

const GroupsRoot = styled.div`
  width: 100%;
  padding: 8px 24px;
`;

export type GroupMembersListRequest = {
  adminId: number;
  academicId: number;
  groupId: number;
};
interface GroupDataWithStatus extends GroupMembersDataType {
  sendStatus: 'Success' | 'Failed' | '' | undefined;
}

function Groups({ messageId, voiceId, isSubmitting, templateId }: SendPopupProps) {
  const { user } = useAuth();
  const theme = useTheme();
  const dispatch = useAppDispatch();
  const { confirm } = useConfirm();
  const defaultYearId: number | undefined = 10;
  const adminId: number = user ? user.accountId : 0;
  const [view, setView] = useState<'main' | 'report'>('main');
  const [academicYearFilter, setAcademicYearFilter] = useState(defaultYearId);
  const [groupFilter, setGroupFilter] = useState(-1);
  const [sendResults, setSendResults] = useState<GroupDataWithStatus[]>([]);
  const [selectedRows, setSelectedRows] = useState<GroupMembersDataType[]>([]);
  const YearData = useAppSelector(getYearData);
  const GroupsData = useAppSelector(getGroupListData);
  const GroupMembersStatus = useAppSelector(getGroupMembersListStatus);
  const [selectedData, setSelectedData] = useState<GroupDataWithStatus[]>([]);
  const [disabledCheckBoxes, setDisabledCheckBoxes] = useState<boolean[]>([]);
  const [errMsgTooltip, setErrMsgTooltip] = useState<React.ReactNode>(undefined);

  const initialGroupListRequest: GroupMembersListRequest = React.useMemo(
    () => ({
      adminId,
      academicId: defaultYearId,
      groupId: -1,
    }),
    [adminId, defaultYearId]
  );
  const currentGroupListRequest: GroupMembersListRequest = React.useMemo(
    () => ({
      adminId,
      academicId: academicYearFilter,
      groupId: groupFilter,
    }),
    [adminId, academicYearFilter, groupFilter]
  );

  const loadGroupMembersList = useCallback(
    async (request: GroupMembersListRequest) => {
      try {
        const data = await dispatch(fetchGroupMembersList(request)).unwrap();
        const dataWithStatus = data.map((item) => ({
          ...item,
          sendStatus: sendResults.find((i) => i.guardianNumber === item.guardianNumber)?.sendStatus,
        }));
        setSelectedData(dataWithStatus);
        setSelectedRows([]);
      } catch (error) {
        console.error('Error loading Group Members list:', error);
      }
    },
    [dispatch, setSelectedData, sendResults]
  );

  React.useEffect(() => {
    dispatch(fetchYearList(adminId));
    dispatch(fetchGroupsList({ adminId, academicId: academicYearFilter }));
    if (GroupMembersStatus === 'idle') {
      loadGroupMembersList(initialGroupListRequest);
    }
  }, [dispatch, adminId, initialGroupListRequest, GroupMembersStatus, loadGroupMembersList, academicYearFilter]);

  const handleYearChange = (e: SelectChangeEvent) => {
    setAcademicYearFilter(parseInt(YearData.filter((item) => item.accademicId === e.target.value)[0].accademicId, 10));
    loadGroupMembersList({ ...currentGroupListRequest, academicId: parseInt(e.target.value, 10) });
    setSelectedRows([]);
  };
  const handleGroupChange = (e: SelectChangeEvent) => {
    setGroupFilter(GroupsData.filter((item) => item.groupId === parseInt(e.target.value, 10))[0].groupId);
    loadGroupMembersList({ ...currentGroupListRequest, groupId: parseInt(e.target.value, 10) });
    setSelectedRows([]);
  };

  const handleReset = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      setAcademicYearFilter(parseInt(YearData[0].accademicId, 10));
      setGroupFilter(-1);
    },
    [YearData]
  );
  const handleSubmit = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      loadGroupMembersList(currentGroupListRequest);
      setSelectedRows([]);
    },
    [loadGroupMembersList, currentGroupListRequest]
  );

  const handleBack = useCallback(() => {
    setView('main');
    // setSendResults(null);
    setSelectedRows([]);
  }, []);

  const handleCancel = useCallback(() => {
    setSelectedRows([]);
    loadGroupMembersList(currentGroupListRequest);
    setDisabledCheckBoxes([]);
  }, [loadGroupMembersList, currentGroupListRequest]);

  const handleSendMessage = useCallback(async () => {
    try {
      if (!selectedRows || selectedRows.length === 0) {
        const errorMessage = <ErrorMessage icon={ErrorMsg1} message="Please Select Atleast one Group Member to sent" />;
        await confirm(errorMessage, 'Staff not selected', { okLabel: 'Ok', showOnlyOk: true });
        return;
      }
      const sendRequests: SendToGroupMembersType[] = await Promise.all(
        selectedRows?.map(async (item) => {
          const { accademicTime, ...rest } = item;
          const sendReq = { messageId, adminId, sendStatus: '', ...rest };
          return sendReq;
        }) || []
      );
      const actionResult = await dispatch(messageSendToGroupMembersList(sendRequests));
      if (actionResult && Array.isArray(actionResult.payload)) {
        const results: GroupDataWithStatus[] = actionResult.payload;

        const errorMessages = results.find((result) => result.sendStatus === 'Failed');
        const successMessages = results.find((result) => result.sendStatus === 'Success');
        if (!errorMessages) {
          const successMessage = (
            <SuccessMessage icon={SuccessMsg} message="Message sent to all selected Group Members Successfully" />
          );
          await confirm(successMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        } else if (!successMessages) {
          setErrMsgTooltip(
            <Typography variant="subtitle2" fontSize={12} style={{ color: theme.palette.error.main }}>
              No messages sent to Group Member, Please check the numbers and Try again
            </Typography>
          );
          const errorMessage = (
            <ErrorMessage
              icon={ErrorMsg2}
              message="No messages sent to any Group Members, Please check the numbers and Try again"
            />
          );
          await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        } else {
          setErrMsgTooltip(
            <Typography variant="subtitle2" fontSize={12} style={{ color: theme.palette.error.main }}>
              Error sending messages to Group Member, Please recheck and Try again
            </Typography>
          );
          const errorMessage = (
            <ErrorMessage
              icon={ErrorMsg}
              message="Error sending messages to some Group Members, Please recheck and Try again"
            />
          );
          await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        }
        setSendResults([...sendResults, ...results]);
        const dataResult = selectedData.map((item) => ({
          ...item,
          sendStatus:
            item.sendStatus !== 'Success' && item.sendStatus !== 'Failed'
              ? results.find((result) => result.guardianNumber === item.guardianNumber)?.sendStatus
              : item.sendStatus,
        }));

        // setSelectedRows([]);
        setSelectedData(dataResult);
      } else {
        const errorMessage = <ErrorMessage message="Error while sending messages, Please Try again" />;
        await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
      }
      setSelectedRows([]);
      const updatedDisabledCheckBoxes = selectedData.map((selectedItem) =>
        selectedRows.includes(selectedItem)
          ? !disabledCheckBoxes[selectedData.indexOf(selectedItem)]
          : disabledCheckBoxes[selectedData.indexOf(selectedItem)]
      );

      setDisabledCheckBoxes(updatedDisabledCheckBoxes);
    } catch (error) {
      console.error('Error sending messages:', error);
    }
  }, [
    dispatch,
    confirm,
    messageId,
    selectedRows,
    setSelectedRows,
    adminId,
    selectedData,
    sendResults,
    theme,
    disabledCheckBoxes,
  ]);
  const handleSendVoiceMessage = useCallback(async () => {
    try {
      if (!selectedRows || selectedRows.length === 0) {
        const errorMessage = <ErrorMessage icon={ErrorMsg1} message="Please Select Atleast one Group Member to sent" />;
        await confirm(errorMessage, 'Staff not selected', { okLabel: 'Ok', showOnlyOk: true });
        return;
      }
      const sendRequests: SendVoiceToGroupRequest[] = await Promise.all(
        selectedRows?.map(async (item) => {
          const { accademicTime, ...rest } = item;
          const sendReq = { voiceId, adminId, templateId, sendStatus: '', ...rest };
          return sendReq;
        }) || []
      );
      const actionResult = await dispatch(voiceMessageSendToGroupMembersList(sendRequests));
      if (actionResult && Array.isArray(actionResult.payload)) {
        const results: GroupDataWithStatus[] = actionResult.payload;

        const errorMessages = results.find((result) => result.sendStatus === 'Failed');
        const successMessages = results.find((result) => result.sendStatus === 'Success');
        if (!errorMessages) {
          const successMessage = (
            <SuccessMessage icon={SuccessMsg} message="Message sent to all selected Group Members Successfully" />
          );
          await confirm(successMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        } else if (!successMessages) {
          setErrMsgTooltip(
            <Typography variant="subtitle2" fontSize={12} style={{ color: theme.palette.error.main }}>
              No messages sent to Group Member, Please check the numbers and Try again
            </Typography>
          );
          const errorMessage = (
            <ErrorMessage
              icon={ErrorMsg2}
              message="No messages sent to any Group Members, Please check the numbers and Try again"
            />
          );
          await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        } else {
          setErrMsgTooltip(
            <Typography variant="subtitle2" fontSize={12} style={{ color: theme.palette.error.main }}>
              Error sending messages to Group Member, Please recheck and Try again
            </Typography>
          );
          const errorMessage = (
            <ErrorMessage
              icon={ErrorMsg}
              message="Error sending messages to some Group Members, Please recheck and Try again"
            />
          );
          await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        }
        setSendResults([...sendResults, ...results]);
        const dataResult = selectedData.map((item) => ({
          ...item,
          sendStatus:
            item.sendStatus !== 'Success' && item.sendStatus !== 'Failed'
              ? results.find((result) => result.guardianNumber === item.guardianNumber)?.sendStatus
              : item.sendStatus,
        }));

        // setSelectedRows([]);
        setSelectedData(dataResult);
      } else {
        const errorMessage = <ErrorMessage message="Error while sending messages, Please Try again" />;
        await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
      }
      setSelectedRows([]);
      const updatedDisabledCheckBoxes = selectedData.map((selectedItem) =>
        selectedRows.includes(selectedItem)
          ? !disabledCheckBoxes[selectedData.indexOf(selectedItem)]
          : disabledCheckBoxes[selectedData.indexOf(selectedItem)]
      );

      setDisabledCheckBoxes(updatedDisabledCheckBoxes);
    } catch (error) {
      console.error('Error sending messages:', error);
    }
  }, [
    dispatch,
    confirm,
    voiceId,
    selectedRows,
    setSelectedRows,
    adminId,
    selectedData,
    sendResults,
    theme,
    templateId,
    disabledCheckBoxes,
  ]);

  const getRowKey = useCallback((row: GroupDataWithStatus) => row.studentId, []);
  const getStatusIcon = (row: GroupDataWithStatus) => {
    let iconComponent;

    if (row.sendStatus === 'Failed') {
      iconComponent = (
        <Tooltip title={errMsgTooltip} placement="left">
          <Stack>
            <Lottie animationData={errorIcon} loop={false} style={{ width: '30px' }} />
          </Stack>
        </Tooltip>
      );
    } else if (row.sendStatus === 'Success') {
      iconComponent = (
        <Stack>
          <Lottie animationData={successIcon} loop={false} style={{ width: '30px' }} />
        </Stack>
      );
    } else if (row.sendStatus === null) {
      iconComponent = 'empty';
    } else {
      iconComponent = null;
    }

    return <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>{iconComponent}</Box>;
  };
  const GroupsListColumns: DataTableColumn<GroupDataWithStatus>[] = [
    {
      name: 'status',
      renderCell: getStatusIcon,
    },
    {
      name: 'slNo',
      headerLabel: 'Sl.No',
      renderCell: (row, index) => {
        return <Typography variant="subtitle2">{index + 1}</Typography>;
      },
      sortable: true,
    },
    {
      name: 'accademicTime',
      dataKey: 'accademicTime',
      headerLabel: 'Year',
    },
    {
      name: 'studentName',
      dataKey: 'studentName',
      headerLabel: 'Student Name',
    },
    {
      name: 'group',
      dataKey: 'groupName',
      headerLabel: 'Group',
    },
    {
      name: 'class',
      dataKey: 'className',
      headerLabel: 'Class',
    },
    {
      name: 'contactName',
      dataKey: 'guardianName',
      headerLabel: 'Contact Name',
    },
    {
      name: 'mobileNumber',
      dataKey: 'guardianNumber',
      headerLabel: 'Mobile Number',
    },
  ];
  return (
    <GroupsRoot>
      <Box>
        <Divider />
        {view === 'main' ? (
          <form noValidate onReset={handleReset} onSubmit={handleSubmit}>
            <Grid pb={2} pt={1} container spacing={3} alignItems="end">
              <Grid item lg={3} md={4} sm={4} xs={12}>
                <FormControl fullWidth>
                  <Typography variant="h6" fontSize={12} color="GrayText">
                    Academic Year
                  </Typography>
                  <Select
                    labelId="academicYearFilter"
                    id="academicYearFilterSelect"
                    value={academicYearFilter.toString()}
                    onChange={handleYearChange}
                    placeholder="Select Year"
                  >
                    {YearData.map((opt) => (
                      <MenuItem key={opt.accademicId} value={opt.accademicId}>
                        {opt.accademicTime}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item lg={3} md={4} sm={4} xs={12}>
                <FormControl fullWidth>
                  <Typography variant="subtitle2" fontSize={12} color="GrayText">
                    Group Name
                  </Typography>
                  <Select
                    labelId="groupFilter"
                    id="groupFilterSelect"
                    value={groupFilter.toString()}
                    onChange={handleGroupChange}
                    placeholder="Select Group"
                  >
                    <MenuItem value="-1" sx={{ display: 'none' }}>
                      Select All
                    </MenuItem>
                    {GroupsData.map((opt) => (
                      <MenuItem key={opt.groupId} value={opt.groupId}>
                        {opt.groupName}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item lg={3} md={4} sm={4} xs={12}>
                <FormControl fullWidth>
                  <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                    <Button variant="contained" color="secondary" type="reset" fullWidth>
                      Reset
                    </Button>
                    <Button variant="contained" color="primary" type="submit" fullWidth>
                      Search
                    </Button>
                  </Stack>
                </FormControl>
              </Grid>
            </Grid>
          </form>
        ) : (
          <Stack direction="row" alignItems="center" gap={1}>
            <IconButton onClick={handleBack} color="primary" size="small">
              <ArrowBackIcon fontSize="small" />
            </IconButton>
            <Typography variant="subtitle2" fontSize={17} py={2}>
              DELIVERY REPORT
            </Typography>
          </Stack>
        )}
        <Paper
          sx={{
            border:
              selectedData?.length === 0 && sendResults?.length === 0 ? `0px` : `1px solid ${theme.palette.grey[300]}`,
            width: '100%',
            overflow: 'auto',
          }}
        >
          <Box
            sx={{
              height: 'calc(100vh - 23.7rem)',
              width: { xs: selectedData?.length !== 0 ? '43.75rem' : '100%', md: '100%' },
            }}
          >
            {view === 'main' ? (
              <DataTable
                ShowCheckBox
                disabledCheckBox={disabledCheckBoxes}
                RowSelected
                setSelectedRows={setSelectedRows}
                selectedRows={selectedRows}
                columns={GroupsListColumns}
                data={selectedData}
                getRowKey={getRowKey}
                // getRowKey={(row: GroupMembersDataType) => row.studentId}
                fetchStatus={GroupMembersStatus}
              />
            ) : (
              <DataTable
                setSelectedRows={setSelectedRows}
                selectedRows={selectedRows}
                columns={GroupsListColumns}
                data={sendResults}
                getRowKey={getRowKey}
                // getRowKey={(row: GroupMembersDataType) => row.studentId}
                fetchStatus={GroupMembersStatus}
              />
            )}
          </Box>
        </Paper>
        <Box
          display="flex"
          sx={{
            justifyContent: {
              xs: 'right',
            },
            pr: {
              lg: '5',
            },
            pt: 3,
          }}
        >
          {view === 'main' && selectedData.length !== 0 && (
            <Stack
              spacing={2}
              direction={{ xs: 'column', sm: 'row' }}
              justifyContent={{ xs: 'ceneter', sm: 'space-between' }}
              width={sendResults.length > 0 ? '100%' : 'auto'}
            >
              {sendResults && sendResults.length > 0 && (
                <Button
                  className="delivery-report-btn"
                  sx={{ width: { xs: '48.5%', sm: 'auto' } }}
                  color="warning"
                  onClick={() => setView('report')}
                  variant="contained"
                >
                  Delivery Report
                </Button>
              )}
              <Stack spacing={2} direction="row">
                <Button
                  className="cancel-btn"
                  // sx={{ width: { xs: '25%', sm: 'auto' } }}
                  onClick={handleCancel}
                  variant="contained"
                  color="secondary"
                >
                  Cancel
                </Button>
                <LoadingButton
                  fullWidth
                  className="send-btn"
                  loadingPosition="start"
                  endIcon={<SendIcon />}
                  // sx={{ width: { sm: 'auto' } }}
                  onClick={voiceId && voiceId !== 0 ? handleSendVoiceMessage : handleSendMessage}
                  loading={isSubmitting}
                  variant="contained"
                  color="primary"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Sending...' : 'Send'}
                </LoadingButton>
              </Stack>
            </Stack>
          )}
        </Box>
      </Box>
      {isSubmitting ? (
        <LoadingPopup popupContent={<LoadingMessage icon={LoadingMsg} message="Sending messages please wait." />} />
      ) : null}
    </GroupsRoot>
  );
}

export default Groups;
