import { FetchStatus } from './Common';
import { PageRequestWithFilters, PagedData, PaginationInfo } from './Pagination';

export type EventsDetailsListType = {
  slno: number;
  type: string;
  title: string;
  Description: string;
  thumbnail: string;
  year: string;
  link: string;
  create: string;
};
export type YearSortStudentsType = [{ slNo: number; year: string; section: string }];
export type ClassSortStudentsType = [{ slNo: number; class: string; section: string }];

export type YearListInfo = {
  academicId: number;
  academicTime: string;
  academicDescription: string;
  academicStatus: number;
};

export type ClassListInfo = {
  classId: number;
  className: string;
  classDescription: string;
  classStatus: number;
};
export type ClassSortListInfo = {
  classId: number;
  className: string;
  classSection: string;
  sortOrder: number;
  classStatus: number;
  sectionId: number;
  createdBy: number;
  dbResult: string;
};
export type SectionListInfo = {
  sectionId: number;
  sectionName: string;
  sectionStatus: number;
};
export type SubjectListInfo = {
  subjectId: number;
  subjectName: string;
  subjectDescription: string;
  subjectStatus: number;
};
export type SubjectCategoryInfo = {
  subjectId: number;
  subjectCategory: string;
};

export type YearCreateRequest = Omit<YearListInfo, 'academicId'>;
export type ClassCreateRequest = Omit<ClassListInfo, 'classId'>;
// export type ClassSortCreateRequest = Omit<ClassSortListInfo, 'classId'>;
export type SectionCreateRequest = Omit<SectionListInfo, 'sectionId'>;
export type SubjectCreateRequest = Omit<SubjectListInfo, 'subjectId'>;

export type ClassCreateRow = { rowKey: string } & ClassCreateRequest;
export type SubjectCreateRow = { rowKey: string } & SubjectCreateRequest;

export type YearListFilters = {
  academicTime?: string;
  academicStatus?: number | null;
};

export type ClassListFilters = {
  className?: string;
  classStatus?: number | null;
};

export type ClassSortListFilters = {
  adminId: number;
  academicId: number | string;
  sectionId: number;
  classId: number;
};
export type SectionListFilters = {
  sectionName?: string;
  sectionStatus?: number | null;
};
export type SubjectListFilters = {
  subjectName?: string;
  subjectStatus?: number | null;
};
// this type remove
export type YearStatus = {
  id: number;
  name: string;
};
// ===============
export type StatusOptions = {
  id: number;
  name: string;
};

export type YearListRequest = PageRequestWithFilters<YearListFilters>;
export type ClassListRequest = PageRequestWithFilters<ClassListFilters>;
export type ClassSortListRequest = PageRequestWithFilters<ClassSortListFilters>;
export type SectionListRequest = PageRequestWithFilters<SectionListFilters>;
export type SubjectListRequest = PageRequestWithFilters<SubjectListFilters>;

export type YearListPagedData = PagedData<YearListInfo>;
export type ClassListPagedData = PagedData<ClassListInfo>;
export type ClassSortListPagedData = PagedData<ClassSortListInfo>;
export type SectionListPagedData = PagedData<SectionListInfo>;
export type SubjectListPagedData = PagedData<SubjectListInfo>;

export type YearManagementState = {
  yearList: {
    [key: string]: any;
    status: FetchStatus;
    data: YearListInfo[];
    error: string | null;
    pageInfo: PaginationInfo;
    sortColumn: string;
    sortDirection: 'asc' | 'desc';
  };
  submitting: boolean;
  deletingRecords: Record<number, boolean>;
  error: string | null | undefined;
};

export type ClassManagementState = {
  classList: {
    [key: string]: any;
    status: FetchStatus;
    data: ClassListInfo[];
    error: string | null;
    pageInfo: PaginationInfo;
    sortColumn: string;
    sortDirection: 'asc' | 'desc';
  };
  classSortList: {
    [key: string]: any;
    status: FetchStatus;
    data: ClassSortListInfo[];
    error: string | null;
    pageInfo: PaginationInfo;
    sortColumn: string;
    sortDirection: 'asc' | 'desc';
  };
  submitting: boolean;
  deletingRecords: Record<number, boolean>;
  error: string | null | undefined;
};

export type SectionManagementState = {
  sectionList: {
    [key: string]: any;
    status: FetchStatus;
    data: SectionListInfo[];
    error: string | null;
    pageInfo: PaginationInfo;
    sortColumn: string;
    sortDirection: 'asc' | 'desc';
  };
  submitting: boolean;
  deletingRecords: Record<number, boolean>;
  error: string | null | undefined;
};
export type SubjectManagementState = {
  subjectList: {
    [key: string]: any;
    status: FetchStatus;
    data: SubjectListInfo[];
    error: string | null;
    pageInfo: PaginationInfo;
    sortColumn: string;
    sortDirection: 'asc' | 'desc';
  };
  submitting: boolean;
  deletingRecords: Record<number, boolean>;
  error: string | null | undefined;
};
