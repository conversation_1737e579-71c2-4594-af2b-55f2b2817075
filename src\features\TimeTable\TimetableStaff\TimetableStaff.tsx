/* eslint-disable jsx-a11y/alt-text */
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Box,
  Typography,
  Card,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  TableContainer,
  Table,
  Tooltip,
  SelectChangeEvent,
  Popper,
  Chip,
} from '@mui/material';
import styled, { useTheme } from 'styled-components';
import { TimetableByDayProps, TimetableStaffSubjects, TimetableSubjects } from '@/config/TimetableData';
import { TimetableIconMap } from '@/config/TimetableIconMap';
import { CLASS_SELECT, YEAR_SELECT } from '@/config/Selection';
import { MdAdd } from 'react-icons/md';
import useSettings from '@/hooks/useSettings';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import React, { FormEvent, useCallback, useMemo, useState } from 'react';
import Popup from '@/components/shared/Popup/Popup';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import { IconButton } from '@mui/material';
import { Collapse } from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import { IoIosArrowUp } from 'react-icons/io';
import { FormControl } from '@mui/material';
import { Select } from '@mui/material';
import { getClassData, getYearData } from '@/config/storeSelectors';
import { useAppSelector } from '@/hooks/useAppSelector';
import { MenuItem } from '@mui/material';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { fetchClassList, fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import useAuth from '@/hooks/useAuth';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { Create } from './Create';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import DeleteIcon from '@mui/icons-material/Delete';
import SaveIcon from '@mui/icons-material/Save';
import english from '@/assets/timetable/English.svg';
import mathematics from '@/assets/timetable/Math.svg';
import physics from '@/assets/timetable/Physics.svg';
import biology from '@/assets/timetable/Biology.svg';
import social from '@/assets/timetable/Social.svg';
import arabic from '@/assets/timetable/Arabic.svg';
import chemistry from '@/assets/timetable/Chemistry.svg';
import computer from '@/assets/timetable/Computer.svg';
import malayalam from '@/assets/timetable/Malayalam.svg';
import hindi from '@/assets/timetable/Hindi.svg';
import economics from '@/assets/timetable/Economics.svg';
import accountancy from '@/assets/timetable/Accountancy.svg';
import thamil from '@/assets/timetable/Thamil.svg';
import business from '@/assets/timetable/Business.svg';
import general from '@/assets/timetable/General.svg';
import marathi from '@/assets/timetable/Marathi.svg';
import { Scale } from '@mui/icons-material';
import { CloseIcon } from '@/theme/overrides/CustomIcons';
import EditOffIcon from '@mui/icons-material/EditOff';

const TimetableStaffRoot = styled.div`
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  .border_radius_none {
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
    border-bottom: 0px;
  }
  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 576px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 50px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid ${(props) => props.theme.palette.grey[200]};
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
  .MuiTableCell-head {
    z-index: 1111;
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.grey[900]};
  }
  .MuiTableCell-root {
    /* border: 1px solid ${(props) => props.theme.palette.grey[300]}; */
  }
  .MuiTableCell-root:first-child {
    padding-left: 5px;
  }

  .MuiTableCell-head:nth-child(1) {
    z-index: 1111;
    position: sticky;
    left: 0;
  }

  .MuiTableCell-head:last-child {
    z-index: 1111;
    position: sticky;
    right: 0px;
    width: 40px;
  }

  .MuiTableCell-body:nth-child(1) {
    padding: 0px;
    z-index: 111;
    position: sticky;
    /* border: 1px solid ${(props) => props.theme.palette.grey[300]}; */
    left: 0;
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.grey[900]};
  }
  .MuiTableCell-root.MuiTableCell-body:last-child {
    padding: 0px;
    position: sticky;
    right: 0px;
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.grey[800]};
    z-index: 111;
  }
  .MuiTableRow-root:last-child td {
    border-bottom: 1px solid ${(props) => props.theme.palette.grey[200]};
  }
`;

const CustomPopper = (props) => (
  <Popper
    {...props}
    placement="bottom-start"
    modifiers={[
      {
        name: 'offset',
        options: {
          offset: [0, 4],
        },
      },
    ]}
    style={{ width: 200 }} // 👈 Set desired width here
  />
);

type Subject = { id: number; name: string; class: string; icon?: string };
type SelectedSubjectsMap = Record<string, Subject[]>; // key = periodId

const subjectList: Subject[] = [
  {
    id: 1,
    name: 'English',
    class: 'VII-A',
    icon: 'english',
  },
  {
    id: 2,
    name: 'Malayalam',
    class: 'VII-A',
    icon: 'malayalam',
  },
  {
    id: 3,
    name: 'Arabic',
    class: 'VII-A',
    icon: 'arabic',
  },
  {
    id: 4,
    name: 'Hindi',
    class: 'VII-A',
    icon: 'hindi',
  },
  {
    id: 5,
    name: 'Math',
    class: 'VII-A',
    icon: 'math',
  },
  {
    id: 6,
    name: 'Biology',
    class: 'VII-A',
    icon: 'biology',
  },
  {
    id: 7,
    name: 'Physics',
    class: 'VII-A',
    icon: 'physics',
  },
  {
    id: 8,
    name: 'Social',
    class: 'VII-A',
    icon: 'social',
  },
];

export const TimetableIcon = (props: any) => {
  const { Icon } = props;
  const icon = Icon ? TimetableIconMap[Icon] : null;
  return <img src={icon} alt={icon} />;
};
const SubjectCell = ({ subjects }: any) => {
  const theme = useTheme();

  return (
    <TableCell sx={{ border: `1px solid ${theme.palette.grey[300]} ` }} key={subjects.id}>
      <Paper
        sx={{
          minWidth: '100px',
          backgroundColor:
            (subjects.name === 'English' ? '#ecf9ff' : '') ||
            (subjects.name === 'Math' ? '#f3ebf9' : '') ||
            (subjects.name === 'Arabic' ? '#f2f7fb' : '') ||
            (subjects.name === 'Malayalam' ? '#fef4f7' : '') ||
            (subjects.name === 'Math' ? '#f2fbf9' : '') ||
            (subjects.name === 'Hindi' ? '#f2fbf9' : '') ||
            (subjects.name === 'Biology' ? '#f2faeb' : '') ||
            (subjects.name === 'Physics' ? '#ffe8e9' : '') ||
            (subjects.name === 'Social' ? '#fff1ea' : '') ||
            (subjects.name === 'Bussiness' ? '#ecf1fd' : '') ||
            (subjects.name === 'General' ? '#ebf2ff' : '') ||
            (subjects.name === 'Computer' ? '#eaf8fe' : '') ||
            (subjects.name === 'Economics' ? '#fff8eb' : '') ||
            (subjects.name === 'Accountancy' ? '#ecf8f2' : ''),
          px: 2,
          py: 1,
        }}
      >
        <Stack direction="column" display="flex" alignItems="center">
          <TimetableIcon Icon={subjects.icon} />
          <Typography variant="h6" fontSize={8} color="initial">
            {subjects.name}
          </Typography>

          <Typography fontSize={8} fontWeight={600} color="initial">
            {subjects.class}
          </Typography>
        </Stack>
      </Paper>
    </TableCell>
  );
};
function TimetableStaff() {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const dispatch = useAppDispatch();
  const { user } = useAuth();
  const adminId: number = user ? user.accountId : 0;

  const [createOpen, setCreateOpen] = React.useState(false);
  const [popup, setPopup] = React.useState(false);
  const [showFilter, setShowFilter] = useState(true);
  const [academicYearFilter, setAcademicYearFilter] = useState(0);
  const [classFilter, setClassFilter] = useState(0);
  const YearData = useAppSelector(getYearData);
  const classListData = useAppSelector(getClassData);
  const [selectedSubjectsByPeriod, setSelectedSubjectsByPeriod] = useState<SelectedSubjectsMap>({});
  const [editCell, setEditCell] = useState<{ rowId: number; periodIndex: number } | null>(null);

  // Handler when selection changes for a specific period
  const handleSelectionChange = (periodId: number, newSelected: Subject[] | null) => {
    setSelectedSubjectsByPeriod((prev) => ({
      ...prev,
      [periodId]: newSelected,
    }));
  };

  const handleClickOpen = () => {
    setCreateOpen(false);
    setPopup(true);
  };

  const handleClickClose = () => setPopup(false);

  const handleCreateClose = () => {
    setCreateOpen(false);
  };

  React.useEffect(() => {
    dispatch(fetchYearList(adminId));
    dispatch(fetchClassList(adminId));
    // }
  }, [dispatch, adminId]);

  const handleYearChange = (e: SelectChangeEvent) => {
    setAcademicYearFilter(
      parseInt(YearData.filter((item: any) => item.accademicId === e.target.value)[0].accademicId, 10)
    );
    // loadOptionalFeeList({ ...currentOptionalFeeRequest, academicId: parseInt(e.target.value, 10) });
    setClassFilter(0);
  };

  const handleClassChange = (e: SelectChangeEvent) => {
    setClassFilter(parseInt(classListData.filter((item) => item.classId === e.target.value)[0].classId, 10));
    // loadOptionalFeeList({ ...currentOptionalFeeRequest, classId: parseInt(e.target.value, 10) });
    // setSelectedCells([]);
  };

  const handleReset = useCallback((e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    // loadOptionalFeeList(initialOptionalFeeRequest);
    setAcademicYearFilter(0);
    setClassFilter(0);
    // dispatch(fetchFeeDateSettings({ adminId, academicId: 10, sectionId: 0 }));
  }, []);

  const subjectColorMap = useMemo(
    () => ({
      English: { bg: '#ecf9ff', text: '#0288d1', icon: english },
      Malayalam: { bg: '#fef4f7', text: '#c2185b', icon: malayalam },
      Arabic: { bg: '#f2f7fb', text: '#1565c0', icon: arabic },
      Hindi: { bg: '#f2fbf9', text: '#2e7d32', icon: hindi },
      Math: { bg: '#f3ebf9', text: '#6a1b9a', icon: mathematics },
      Biology: { bg: '#f2faeb', text: '#388e3c', icon: biology },
      Physics: { bg: '#ffe8e9', text: '#d32f2f', icon: physics },
      Social: { bg: '#fff1ea', text: '#ef6c00', icon: social },
      Bussiness: { bg: '#ecf1fd', text: '#1e88e5', icon: business },
      General: { bg: '#ebf2ff', text: '#3f51b5', icon: general },
      Computer: { bg: '#eaf8fe', text: '#0288d1', icon: computer },
      Economics: { bg: '#fff8eb', text: '#f9a825', icon: economics },
      Accountancy: { bg: '#ecf8f2', text: '#388e3c', icon: accountancy },
      Chemistry: { bg: '#fef4ff', text: '#8e24aa', icon: chemistry },
      Thamil: { bg: '#f5f5f5', text: '#5d4037', icon: thamil },
      Marathi: { bg: '#fff3e0', text: '#ef6c00', icon: marathi },
    }),
    []
  );

  const timetableStaffListColumn: DataTableColumn<TimetableByDayProps>[] = useMemo(() => {
    const periodColumns = Array.from({ length: 8 }, (_, index) => {
      const periodNumber = index + 1;
      return {
        name: `period${periodNumber}`,
        renderHeader: () => (
          <Typography textAlign="center" variant="subtitle2" fontSize={13}>
            Period {periodNumber}
          </Typography>
        ),
        renderCell: (row: TimetableByDayProps) => {
          const subject = row.subjects?.[index];
          const subjectName = subject?.name;
          const colors = subjectColorMap[subjectName ?? ''] || { bg: '#f5f5f5', text: '#555', icon: '' };
          const isEditing = editCell?.rowId === subject.id && editCell?.periodIndex === index;

          if (isEditing || !subject || subject.id === 0) {
            return (
              <FormControl fullWidth>
                <Autocomplete
                  options={subjectList}
                  getOptionLabel={(option) => option?.name || ''} // fallback for typing
                  value={selectedSubjectsByPeriod[index] || subject}
                  onChange={(event, newValue) => {
                    handleSelectionChange(index, newValue);
                  }}
                  isOptionEqualToValue={(option, value) => option.id === value.id}
                  renderInput={(params) => {
                    const selected = selectedSubjectsByPeriod[index];
                    return (
                      <TextField
                        {...params}
                        placeholder="Choose a subject"
                        variant="standard"
                        InputProps={{
                          ...params.InputProps,
                          startAdornment:
                            selected || (subject?.name && subject?.class) ? (
                              <div style={{ display: 'flex', flexDirection: 'column', padding: '4px 0', width: 58 }}>
                                <Typography
                                  variant="body2"
                                  color="black"
                                  fontWeight="bold"
                                  zIndex={11}
                                  sx={{
                                    whiteSpace: 'nowrap',
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis',
                                  }}
                                >
                                  {selected?.name || subject?.name}
                                </Typography>
                                <Typography
                                  variant="body2"
                                  color="black"
                                  sx={{
                                    fontSize: 12,
                                    whiteSpace: 'nowrap',
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis',
                                  }}
                                  zIndex={11}
                                >
                                  {selected?.class || subject?.class}
                                </Typography>
                              </div>
                            ) : (
                              <Typography
                                sx={{ fontSize: 9.5, width: 58 }}
                                color="secondary"
                                whiteSpace="nowrap"
                                variant="body2"
                                zIndex={11}
                              >
                                Choose a subject
                              </Typography>
                            ),

                          disableUnderline: false,
                        }}
                        sx={{
                          borderRadius: 1,
                          '& .MuiInputBase-root': {
                            backgroundColor: isEditing ? theme.palette.warning.lighter : '',
                            height: 70,
                            paddingX: 1,
                          },
                          '& input': {
                            display: 'none', // hide default input value
                          },
                        }}
                      />
                    );
                  }}
                  renderTags={(value, getTagProps) =>
                    value.map((option, index) => <Chip size="small" label={option.name} {...getTagProps({ index })} />)
                  }
                  // filterSelectedOptions
                  renderOption={(props, option) => (
                    <li {...props}>
                      <div style={{ display: 'flex', flexDirection: 'column' }}>
                        <span style={{ fontWeight: 'bold' }}>{option.name}</span>
                        <span style={{ fontSize: '0.85em', color: theme.palette.grey[500] }}>{option.class}</span>
                      </div>
                    </li>
                  )}
                  sx={{
                    height: 70,
                    borderRadius: 1,
                  }}
                  PopperComponent={CustomPopper}
                />
                {isEditing && (
                  <Tooltip title="Cancel Edit">
                    <IconButton sx={{ position: 'absolute', right: 0 }} size="small" onClick={() => setEditCell(null)}>
                      <EditOffIcon sx={{ fontSize: '12px' }} />
                    </IconButton>
                  </Tooltip>
                )}
              </FormControl>
            );
          }

          return (
            <Box
              sx={{
                backgroundColor: colors.bg,
                px: 0.5,
                py: 1,
                borderRadius: 1,
                height: 70,
                minWidth: 120,
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
                position: 'relative',
                overflow: 'hidden',
                cursor: 'pointer',
                '&:hover::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  height: '100%',
                  width: '100%',
                  // background: 'linear-gradient(to bottom, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.6))',
                  borderRadius: 1,
                  backgroundColor: 'black',
                  zIndex: 111,
                  opacity: 0.3,
                },
                '&:hover .delete-icon': {
                  opacity: 1,
                  // transform: 'scale(1)',
                },
                transition: 'transform 0.2s',
              }}
            >
              <Stack position="absolute" right={0} top={0}>
                <img width={25} src={colors.icon} alt={colors.icon} />
              </Stack>
              {/* Delete Icon (Hidden by default, shown on hover) */}
              <Stack
                className="delete-icon"
                position="absolute"
                zIndex={1111}
                sx={{
                  opacity: 0,
                  transition: 'opacity 0.2s',
                }}
              >
                <Stack direction="row" spacing={1} justifyContent="center" alignItems="center">
                  <Tooltip title="Edit">
                    <IconButton
                      onClick={() => setEditCell({ rowId: subject.id, periodIndex: index })}
                      size="small"
                      color="warning"
                      sx={{
                        backgroundColor: theme.palette.warning.main,
                        '&:hover': {
                          backgroundColor: theme.palette.warning.main,
                        },
                      }}
                    >
                      <ModeEditIcon fontSize="small" sx={{ color: 'white' }} />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Delete">
                    <IconButton
                      size="small"
                      color="error"
                      sx={{
                        backgroundColor: theme.palette.error.main,
                        '&:hover': {
                          backgroundColor: theme.palette.error.main,
                        },
                      }}
                    >
                      <DeleteIcon fontSize="small" sx={{ color: 'white' }} />
                    </IconButton>
                  </Tooltip>
                </Stack>
              </Stack>
              {/* Subject Name */}
              <Typography
                key={`${row.day}-period${periodNumber}`}
                sx={{ textAlign: 'center', color: colors.text }}
                variant="body2"
                fontWeight="bold"
                zIndex={2}
              >
                {subjectName || '-'}
              </Typography>

              {/* Teacher Name */}
              <Typography
                key={`${row.day}-period${periodNumber}-teacher`}
                sx={{ textAlign: 'center', fontSize: 12, color: colors.text }}
                variant="body2"
                zIndex={2}
              >
                {subject?.class || '-'}
              </Typography>
            </Box>
          );
        },
        sortable: false,
      };
    });

    return [
      {
        name: 'day',
        headerLabel: '',
        renderCell: (row) => (
          <Typography
            alignContent="center"
            height="100%"
            borderRight={1}
            borderColor={isLight ? theme.palette.grey[300] : theme.palette.grey[700]}
            minWidth={100}
            px={1}
            textTransform="uppercase"
            textAlign="center"
            variant="body2"
            fontWeight="bold"
          >
            {row.staff}
          </Typography>
        ),
        sortable: false,
      },
      ...periodColumns,
      {
        name: 'actions',
        headerLabel: 'Actions',
        renderCell: (row) => {
          return (
            <Stack
              sx={{ borderLeft: 1, borderColor: isLight ? theme.palette.grey[300] : theme.palette.grey[700] }}
              height="100%"
              width={62}
              direction="column"
              alignItems="center"
              justifyContent="center"
            >
              <IconButton size="small" color="success">
                <SaveIcon />
              </IconButton>
              <Typography sx={{ color: theme.palette.success.main }} variant="subtitle2" fontSize={10}>
                Save
              </Typography>
              {/* <IconButton size="small" color="error" sx={{ padding: 0.5 }}>
                <DeleteIcon />
              </IconButton> */}
            </Stack>
          );
        },
      },
    ];
  }, [selectedSubjectsByPeriod, theme, subjectColorMap, editCell, isLight]);

  const getRowKey = useCallback((row: any) => row.termId, []);

  return (
    <Page title="TimetableStaff">
      <TimetableStaffRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17}>
              Timetable Staff
            </Typography>
            <Box pb={1} sx={{ flexShrink: 0 }}>
              {showFilter === false ? (
                <Tooltip title="Search">
                  <IconButton
                    aria-label="delete"
                    color="primary"
                    sx={{ mr: { xs: 0, sm: 1 } }}
                    onClick={() => setShowFilter((x) => !x)}
                  >
                    <SearchIcon />
                  </IconButton>
                </Tooltip>
              ) : (
                <IconButton
                  aria-label="delete"
                  color="primary"
                  sx={{ mr: { xs: 0, sm: 1 } }}
                  onClick={() => setShowFilter((x) => !x)}
                >
                  <IoIosArrowUp />
                </IconButton>
              )}
              <Button
                startIcon={<MdAdd size="20px" />}
                sx={{ borderRadius: '20px', mr: 1 }}
                variant="outlined"
                size="small"
                onClick={() => setCreateOpen(true)}
              >
                Create
              </Button>
            </Box>
          </Stack>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate onReset={handleReset}>
                <Grid pb={4} container spacing={2} alignItems="end">
                  <Grid item xxl={2} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Select
                        labelId="academicYearFilter"
                        id="academicYearFilterSelect"
                        value={academicYearFilter?.toString()}
                        onChange={handleYearChange}
                        placeholder="Select Year"
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Year
                        </MenuItem>
                        {YearData.map((opt) => (
                          <MenuItem key={opt.accademicId} value={opt.accademicId}>
                            {opt.accademicTime}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Select
                        labelId="classFilter"
                        id="classFilter"
                        value={classFilter?.toString()}
                        onChange={handleClassChange}
                        placeholder="Select Class"
                        sx={{
                          '& .MuiInputBase-input.Mui-disabled': {
                            backgroundColor: theme.palette.grey[200],
                          },
                        }}
                        MenuProps={{
                          PaperProps: {
                            style: {
                              maxHeight: '270px', // Adjust the value to your desired height
                            },
                          },
                        }}
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Class
                        </MenuItem>
                        {classListData.map((opt) => (
                          <MenuItem key={opt.classId} value={opt.classId}>
                            {opt.className}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto">
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button type="reset" variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Paper
              className="card-table-container"
              sx={{
                border: `1px solid #e8e8e9`,
                width: '100%',
                overflow: 'auto',
              }}
            >
              <DataTable
                // disabledCheckBox={studentsData.map((m) => m.feeMapped?.length === 0)}
                hoverDisable={false}
                tableStyles={{ minWidth: { xs: '1100px' } }}
                showHorizontalScroll
                columns={timetableStaffListColumn}
                data={TimetableStaffSubjects}
                getRowKey={getRowKey}
                // fetchStatus={optionalFeeSettingListStatus}
                fetchStatus="success"
              />
            </Paper>
          </div>
        </Card>
      </TimetableStaffRoot>
      <TemporaryDrawer
        Title="Create"
        onClose={handleCreateClose}
        state={createOpen}
        DrawerContent={<Create onClose={handleCreateClose} open={handleClickOpen} />}
      />
      <Popup
        size="xs"
        state={popup}
        onClose={handleClickClose}
        popupContent={<SuccessMessage message=" Save Successfully" />}
      />
    </Page>
  );
}

export default TimetableStaff;
