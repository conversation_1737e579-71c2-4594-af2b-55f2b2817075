import { Paper, TableContainer, Table, TableHead, TableRow, TableCell, TableBody, Box } from '@mui/material';
import React from 'react';

export const UpdatedDetails = () => {
  return (
    <Box
      width="100%"
      sx={{
        padding: ' 0.5rem 1.5rem',
      }}
    >
      <Paper
        sx={{
          border: `1px solid #e8e8e9`,
          width: '100%',
          overflow: 'auto',
          '&::-webkit-scrollbar': {
            width: 0,
          },
        }}
      >
        <TableContainer>
          <Table stickyHeader aria-label="simple table">
            <TableHead>
              <TableRow>
                <TableCell>Class Name</TableCell>
                <TableCell>Class Section</TableCell>
                <TableCell>Sort Order</TableCell>
                <TableCell>Status</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              <TableRow hover>
                <TableCell>XI-A </TableCell>
                <TableCell>IX - A</TableCell>
                <TableCell>TEST CLASS</TableCell>
                <TableCell>
                  <Paper
                    sx={{
                      border: '1px solid green',
                      display: 'flex',
                      justifyContent: 'center',
                      borderRadius: '20px',
                      p: 1,
                      maxWidth: 85,
                      backgroundColor: '#E9FCD4',
                      color: 'green',
                    }}
                  >
                    Pending
                  </Paper>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>
    </Box>
  );
};
