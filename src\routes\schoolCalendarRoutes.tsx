import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import Loadable from '@/components/shared/Loadable';

const Album = Loadable(lazy(() => import('@/pages/SchoolCalendar')));
const Calendar = Loadable(lazy(() => import('@/features/SchoolCalendar/Calendar')));
const HolidaysList = Loadable(lazy(() => import('@/features/SchoolCalendar/ManageHolidays')));
const ActivitiesList = Loadable(lazy(() => import('@/features/SchoolCalendar/MapActivities')));

export const schoolCalendarRoutes: RouteObject[] = [
  {
    path: '/school-calendar',
    element: <Album />,
    children: [
      {
        path: 'calendar',
        element: <Calendar />,
      },
      {
        path: 'holidays-list',
        element: <HolidaysList />,
      },
      {
        path: 'activities-list',
        element: <ActivitiesList />,
      },
    ],
  },
];
