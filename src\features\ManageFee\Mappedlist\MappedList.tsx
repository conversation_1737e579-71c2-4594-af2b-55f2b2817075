/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Typography,
  Card,
  IconButton,
} from '@mui/material';
import styled from 'styled-components';
import { StudentMappedList } from '@/config/TableData';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import DeleteIcon from '@mui/icons-material/Delete';
import { CLASS_SELECT, YEAR_SELECT } from '@/config/Selection';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import Popup from '@/components/shared/Popup/Popup';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import { EditList } from './EditList';
import { EditedTable } from './EditedTable';

const FeeTitle = ['Exam <PERSON>', '<PERSON><PERSON><PERSON>'];

const MappedListRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 16px;
`;

export function MappedList() {
  const [drawerOpen, setDrawerOpen] = React.useState<boolean>(false);
  const [popup, setPopup] = React.useState(false);
  const [Delete, setDelete] = React.useState(false);

  const handleClickDelete = () => setDelete(true);
  const handleClickCloseDelete = () => setDelete(false);

  const toggleDrawerOpen = () => {
    setDrawerOpen(true);
  };
  const toggleDrawerClose = () => {
    setDrawerOpen(false);
  };
  const handleClickOpen = () => {
    setPopup(true);
    toggleDrawerClose();
  };
  const handleClickClose = () => setPopup(false);
  return (
    <Page title="Mapped List">
      <MappedListRoot>
        <Card elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Typography variant="h6" fontSize={17}>
            Mapped List
          </Typography>
          <Divider />
          <Grid pb={4} pt={2} container spacing={3}>
            <Grid item lg={3} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Select Class
              </Typography>
              <Autocomplete
                options={CLASS_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select Class" />}
              />
            </Grid>
            <Grid item lg={3} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Accademic Year
              </Typography>
              <Autocomplete
                options={YEAR_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select Year" />}
              />
            </Grid>
            <Grid item lg={3} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Fee Title
              </Typography>
              <Autocomplete
                options={FeeTitle}
                renderInput={(params) => <TextField {...params} placeholder="Select Title" />}
              />
            </Grid>

            <Grid item lg={3} xs={12}>
              <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                <Button variant="contained" color="secondary" fullWidth>
                  Reset
                </Button>
                <Button variant="contained" color="primary" fullWidth>
                  Search
                </Button>
              </Stack>
            </Grid>
          </Grid>

          <Box>
            <Paper
              sx={{
                border: `.0625rem solid #e8e8e9`,
                width: '100%',
                overflow: 'auto',
                '&::-webkit-scrollbar': {
                  width: 0,
                },
              }}
            >
              <TableContainer
                sx={{
                  maxHeight: 410,
                  width: { xs: '43.75rem', md: '100%' },
                  '&::-webkit-scrollbar': {
                    width: 0,
                  },
                }}
              >
                <Table stickyHeader aria-label="simple table">
                  <TableHead>
                    <TableRow>
                      <TableCell>SL.No</TableCell>
                      <TableCell>Fee Title</TableCell>
                      <TableCell>Class</TableCell>
                      <TableCell> Academic Year</TableCell>
                      <TableCell>Amount(INR)</TableCell>
                      <TableCell>Action</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {StudentMappedList.map((row) => (
                      <TableRow hover key={row.slNo}>
                        <TableCell>{row.slNo}</TableCell>
                        <TableCell>{row.title}</TableCell>
                        <TableCell>{row.Class}</TableCell>
                        <TableCell>{row.year}</TableCell>
                        <TableCell>{row.amount}</TableCell>
                        <TableCell width={30}>
                          <Stack direction="row" gap={1}>
                            <IconButton onClick={toggleDrawerOpen}>
                              <ModeEditIcon />
                            </IconButton>
                            <IconButton onClick={handleClickDelete}>
                              <DeleteIcon />
                            </IconButton>
                          </Stack>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Paper>
          </Box>
        </Card>
      </MappedListRoot>
      <TemporaryDrawer
        onClose={toggleDrawerClose}
        state={drawerOpen}
        // DrawerContent={<EditList onClose={toggleDrawerClose} open={handleClickOpen} />}
      />
      <Popup size="md" state={popup} onClose={handleClickClose} popupContent={<EditedTable />} />
      <Popup
        size="xs"
        state={Delete}
        onClose={handleClickCloseDelete}
        popupContent={<DeleteMessage message="Are you sure want to delete?" />}
      />
    </Page>
  );
}
