// components/SchoolSelector.tsx
import React from 'react';
import { MenuItem, Select, SelectChangeEvent, FormControl, InputLabel, Typography } from '@mui/material';
import { useSchool, School } from '@/contexts/SchoolContext';
import passdailLogo from '@/assets/SchoolLogos/logo-small.svg';
import holyLogo from '@/assets/SchoolLogos/HolyLogo.jpeg';
import carmelLogo from '@/assets/SchoolLogos/CarmelLogo.png';
import thereseLogo from '@/assets/SchoolLogos/StthereseLogo.png';
import thomasLogo from '@/assets/SchoolLogos/StThomasLogo.png';
import nirmalaLogo from '@/assets/SchoolLogos/NirmalaLogo.png';
import MIMLogo from '@/assets/SchoolLogos/MIMLogo.png';
import alFitrahLogo from '@/assets/SchoolLogos/alFitrahLogo.png';

const schoolList: School[] = [
  {
    schoolId: '1',
    schoolName: 'Passdaily',
    schoolFullName: 'Passdaily Demo School',
    schoolLogo: passdailLogo,
    eventThumbnail: 'http://demo.passdaily.in/EventFile/',
    videoFile: 'http://demo.passdaily.in/EventFile/',
    address: () => (
      <>
        <Typography variant="subtitle2" fontSize={16} color="primary">
          Passdaily
        </Typography>
        <Typography variant="body1" textAlign="center" fontSize={13} color="secondary">
          Palakad, Kerala.
        </Typography>
      </>
    ),
    infoCardData: [
      {
        id: 1,
        title: 'Passdaily',
        content: 'For students seeking admissions from Nursery, Jr. kg & Sr. kg.',
      },
      {
        id: 2,
        title: 'Passdaily School',
        content: 'For students seeking admissions from standard 1st to standard 10.',
      },
      {
        id: 3,
        title: 'Passdaily College',
        content: 'Passdaily Junior College to Class XI Science & Commerce.',
      },
    ],
  },
  {
    schoolId: '2',
    schoolName: 'Holy Angels',
    schoolFullName: 'Holy Angels School',
    schoolLogo: holyLogo,
    eventThumbnail: 'http://holy.passdaily.in/EventFile/',
    videoFile: 'http://holy.passdaily.in/EventFile/',
    address: () => (
      <>
        <Typography variant="subtitle2" fontSize={16} color="primary">
          Holy Angels Paradise
        </Typography>
        <Typography variant="body1" textAlign="center" fontSize={13} color="secondary">
          Dombivli, Mumbai, Maharashtra.
        </Typography>
      </>
    ),
    infoCardData: [
      {
        id: 1,
        title: 'Angels Paradise',
        content: 'For students seeking admissions from Nursery, Jr. kg & Sr. kg.',
      },
      {
        id: 2,
        title: 'Holy Angels School',
        content: 'For students seeking admissions from standard 1st to standard 10.',
      },
      {
        id: 3,
        title: 'Holy Angels College',
        content: 'Holy Angels’ Junior College to Class XI Science & Commerce.',
      },
    ],
  },
  {
    schoolId: '3',
    schoolName: 'Carmel',
    schoolFullName: 'Carmel Convent School',
    schoolLogo: carmelLogo,
    eventThumbnail: 'http://carmel.passdaily.in/EventFile/',
    videoFile: 'http://carmel.passdaily.in/EventFile/',
    address: () => (
      <>
        <Typography variant="subtitle2" fontSize={16} color="primary">
          Carmel Convent School
        </Typography>
        <Typography variant="body1" textAlign="center" fontSize={13} color="secondary">
          Bhanvaj road , Khopoli - 410 203, Dist. <br /> Raigad, Maharashtra.
        </Typography>
        <Typography variant="body1" textAlign="center" fontSize={13} color="secondary">
          <EMAIL> , +919763642887
        </Typography>
      </>
    ),
    infoCardData: [
      {
        id: 1,
        title: 'Our Vision',
        content:
          'To mould every prospective potential into dynamic , individualistic and the most pursued citizens of the country to be independent , lifelong who strive and encourage for excellence and becomes responsible student of our global and natural environment.',
      },
      {
        id: 2,
        title: 'Our Mission',
        content:
          'To create an educational world in which children of all types and from all corners of the society have an opportunity for Qualitative education.',
      },
      {
        id: 3,
        title: 'MOTTO Love & Service',
        content:
          'The circle signifies the universe. The star stands for the star of the sea,OUR LADY of MOUNT CARMEL to whom the school is dedicated .The lamp tells us that the students should be lamps radiating light to millions who are still in darkness. Our Alma Mater wants her students to be torch bearers of LOVE AND SERVICE.',
      },
    ],
  },
  {
    schoolId: '4',
    schoolName: 'St Therese',
    schoolFullName: 'St Therese Convent High School',
    schoolLogo: thereseLogo,
    eventThumbnail: 'http://therese.passdaily.in/EventFile/',
    videoFile: 'http://therese.passdaily.in/EventFile/',
    address: () => (
      <>
        <Typography variant="subtitle2" fontSize={16} color="primary">
          St Therese Convent High School
        </Typography>
        <Typography variant="body1" textAlign="center" fontSize={13} color="secondary">
          Nirmalwadi, Kharadi -411014,
          <br /> Tal- Haveli, Dist- Pune
        </Typography>
        <Typography variant="body1" textAlign="center" fontSize={13} color="secondary">
          <EMAIL>, 9765645776
        </Typography>
      </>
    ),
    infoCardData: [
      {
        id: 1,
        title: 'Our Vision',
        content:
          'We envision a holistic transformation of young minds and for this, a kind of enrichment that would ultimately engulf our society and nation as a whole.',
      },
      {
        id: 2,
        title: 'Our Mission',
        content:
          'We dedicate ourselves to reinvent ourselves as teachers and educators for the august mission of enriching the young for academic excellence, development of skills and character formation, based on Love of God and service to humanity as our motto.',
      },
      {
        id: 3,
        title: 'Motto',
        content:
          'The motto of our school is LOVE, TRUTH AND SERVICE. And the emblem, in brief, represents the ideal bounding among the school community, all of whom are pledged to live up to it.',
      },
    ],
  },
  {
    schoolId: '5',
    schoolName: 'St Thomas',
    schoolFullName: 'St Thomas School',
    schoolLogo: thomasLogo,
    eventThumbnail: 'http://stm.passdaily.in/EventFile/',
    videoFile: 'http://stm.passdaily.in/EventFile/',
    address: () => (
      <>
        <Typography variant="subtitle2" fontSize={16} color="primary">
          St Thomas School
        </Typography>
        <Typography variant="body1" textAlign="center" fontSize={13} color="secondary">
          Behind Ebenzer Church, Near Shankeshwar Nagar
          <br />
          Nandivil-Bhoper Road, Dombivli(E) - 421 201
        </Typography>
        <Typography variant="body1" textAlign="center" fontSize={13} color="secondary">
          <EMAIL>, 8928851277
        </Typography>
      </>
    ),
    infoCardData: [
      {
        id: 1,
        title: '',
        content:
          'It is with great pleasure that I welcome you to our school website. I am happy to inform that our school has completed beyond 25 glorious years in Dombivli. The Management and the staff are grateful to God and thank the Almighty for His manifold blessings in helping us to achieve great heights.',
      },
      {
        id: 2,
        title: '',
        content:
          'As Principal I am hugely impressed by the commitment of the management and the staff to the provision of an excellent all-round education for our students in our state of the art facilities. As a team working together, we strongly promote academic achievement among our students. The cultural, sporting and other successes of all of our students and staff are also proudly celebrated together.',
      },
      {
        id: 3,
        title: '',
        content:
          'St. Thomas School, is an innovative school drawing on the talents and skills of staff, students and parents to provide a host of educational programmes and projects. Wholesome participation is encouraged in the extensive range of extra-curricular activities and care is also taken to ensure the well-being and happiness of each and every student in the school.',
      },
    ],
  },
  {
    schoolId: '6',
    schoolName: 'Nirmala',
    schoolFullName: 'Nirmala Convent School',
    schoolLogo: nirmalaLogo,
    eventThumbnail: 'http://nirmala.passdaily.in/EventFile/',
    videoFile: 'http://nirmala.passdaily.in/EventFile/',
    address: () => (
      <>
        <Typography variant="subtitle2" fontSize={16} color="primary">
          Nirmala Convent School
        </Typography>
        <Typography variant="body1" textAlign="center" fontSize={13} color="secondary">
          Nirmalwadi, Kharadi -411014,
          <br /> Tal- Haveli, Dist- Pune
        </Typography>
        <Typography variant="body1" textAlign="center" fontSize={13} color="secondary">
          <EMAIL>, 9765645776
        </Typography>
      </>
    ),
    infoCardData: [
      {
        id: 1,
        title: 'Our Vision',
        content:
          'To mould every prospective potential into dynamic, individualistic and the most pursued citizens of the country to be independent, lifelong who strive and encourage for responsible student of our global and natural environment.',
      },
      {
        id: 2,
        title: 'Mission & Mission Statement',
        content:
          'To create an educational world in which children of all types and from all corners of the society have an opportunity for qualitative education. “ The steadfast love of the Lord never ceases, His mercies never come to an end, they are new every morning, great is your faithfulness.” -Holy Bible.',
      },
      {
        id: 3,
        title: 'School Motto',
        content:
          ' To create an educational world in which children of all types and from all corners of the society have an opportunity for qualitative education. “ The steadfast love of the Lord never ceases, His mercies never come to an end, they are new every morning, great is your faithfulness.” -Holy Bible.',
      },
    ],
  },
  {
    schoolId: '7',
    schoolName: 'Al Fitrah',
    schoolFullName: 'Al Fitrah School',
    schoolLogo: alFitrahLogo,
    eventThumbnail: 'http://demo.passdaily.in/EventFile/',
    videoFile: 'http://demo.passdaily.in/EventFile/',
    address: () => (
      <>
        <Typography variant="subtitle2" fontSize={16} color="primary">
          Al Fitrah School
        </Typography>
        <Typography variant="body1" textAlign="center" fontSize={13} color="secondary">
          Palakad, Kerala.
        </Typography>
      </>
    ),
    infoCardData: [
      {
        id: 1,
        title: 'About us',
        content:
          'Our establishment is dedicated to enriching the lives of believers from a tender age of 3, providing a holistic education that seamlessly integrates religious values, cultural heritage, and contemporary subjects, with the ultimate goal of transforming knowledge into wisdom.',
      },
      {
        id: 2,
        title: 'Our mission',
        content:
          'We support the development of every child in the areas of religion, culture, and exploring opportunities.',
      },
      {
        id: 3,
        title: 'Our vision',
        content:
          'Every student should strive to excel in reading the Quran, and four languages, including English, Hindi, Arabic, and Malayalam, while also applying theoretical knowledge of Islamic cultural values  to practical wisdom.',
      },
    ],
  },
  {
    schoolId: '8',
    schoolName: 'MIM School',
    schoolFullName: 'Mueenul Islam Manoor High School',
    schoolLogo: MIMLogo,
    eventThumbnail: 'http://demo.passdaily.in/EventFile/',
    videoFile: 'http://demo.passdaily.in/EventFile/',
    address: () => (
      <>
        <Typography variant="subtitle2" fontSize={16} color="primary">
          Mueenul Islam Manoor High School
        </Typography>
        <Typography variant="body1" fontSize={13} color="secondary">
          KANDANAKAM,KALADI PO MALAPPURAM 679582
        </Typography>
        <Typography variant="body1" fontSize={13} color="secondary">
          04942103095,9645942121
        </Typography>
      </>
    ),
    infoCardData: [
      {
        id: 1,
        title: 'Our Vision',
        content:
          'To mould every prospective potential into dynamic, individualistic and the most pursued citizens of the country to be independent, lifelong who strive and encourage for responsible student of our global and natural environment.',
      },
      {
        id: 2,
        title: 'Mission & Mission Statement',
        content:
          'M I M HIGH SCHOOL MANOOR is in cluster GLPS KALADI which is situated in Kerala. M I M HIGH SCHOOL MANOOR is Co-educational in type and it is Primary Upper Primary and Secondary Only from class 1 to class 10. M I M HIGH SCHOOL MANOOR is 5-Private Unaided (Recognized) and is 5-Private Unaided (Recognized). This school comes in 1-Rural area',
      },
      {
        id: 3,
        title: 'School Motto',
        content:
          'M I M HIGH SCHOOL MANOOR is in cluster GLPS KALADI which is situated in Kerala. M I M HIGH SCHOOL MANOOR is Co-educational in type and it is Primary Upper Primary and Secondary Only from class 1 to class 10. M I M HIGH SCHOOL MANOOR is 5-Private Unaided (Recognized) and is 5-Private Unaided (Recognized). This school comes in 1-Rural area',
      },
    ],
  },
];

const SchoolSelector = () => {
  const { selectedSchool, setSelectedSchool } = useSchool();

  const handleChange = (event: SelectChangeEvent) => {
    const school = schoolList.find((s) => s.schoolId === event.target.value);
    if (school) setSelectedSchool(school);
  };

  return (
    <FormControl fullWidth>
      <Select sx={{ width: 150 }} fullWidth value={selectedSchool?.schoolId || '1'} onChange={handleChange}>
        {schoolList.map((school) => (
          <MenuItem key={school.schoolId} value={school.schoolId}>
            {school.schoolName}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
};

export default SchoolSelector;
