/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import {
  Autocomplete,
  Grid,
  Stack,
  TextField,
  Button,
  Typography,
  Box,
  FormControl,
  Select,
  MenuItem,
} from '@mui/material';
import styled from 'styled-components';
import { STATUS_OPTIONS, YEAR_SELECT } from '@/config/Selection';
import SelectDateTimePicker from '@/components/shared/Selections/TimePicker';

const CreateLiveClassRoot = styled.div`
  width: 100%;
  padding: 0.5rem 1.5rem;
`;
const CLASS_SELECT = ['VIII A', 'VIII B', 'VIII C', 'IX A', 'X A'];
const SUBJECT_SELECTS = {
  'VIII A': ['English', 'Maths', 'Science', 'Social', 'Music', 'P.E.T'],
  'VIII B': ['English', 'Maths', 'Science', 'Social', 'Music', 'P.E.T'],
  'VIII C': ['English', 'Maths', 'Science', 'Social', 'Music', 'P.E.T'],
  'IX A': ['Hindi', 'Maths', 'Science', 'Social', 'Arabic', 'Music'],
  'X A': ['Maths', 'Science', 'Social', 'Arabic', 'Music', 'P.E.T'],
  // Add more subject arrays as needed
};

const generateCombinedOptions = (classItem, subjects) => {
  if (subjects) {
    return subjects.map((subject) => `${classItem} - ${subject}`);
  }
  return [];
};

const combinedOptions = CLASS_SELECT.flatMap((classItem) =>
  generateCombinedOptions(classItem, SUBJECT_SELECTS[classItem])
);

console.log(combinedOptions);


console.log(combinedOptions);

function CreateLiveClass() {
  return (
    <CreateLiveClassRoot>
      <form noValidate>
        <Grid container spacing={3}>
          <Grid item md={6} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Academic Year
              </Typography>
              <Autocomplete
                options={YEAR_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select year" />}
              />
            </FormControl>
          </Grid>
          <Grid item md={6} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Status
              </Typography>
              <Autocomplete
                options={['Published', 'UnPublished']}
                renderInput={(params) => <TextField {...params} placeholder="Select year" />}
              />
            </FormControl>
          </Grid>
          <Grid item xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Class & Subject
              </Typography>
              <Autocomplete
                multiple
                options={combinedOptions}
                renderInput={(params) => <TextField {...params} placeholder="Select class" />}
              />
            </FormControl>
          </Grid>
          <Grid item md={6} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Meeting ID
              </Typography>
              <TextField placeholder="Enter Meeting ID " name="" />
            </FormControl>
          </Grid>
          <Grid item md={6} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Meeting Password
              </Typography>
              <TextField placeholder="Enter Password " name="" />
            </FormControl>
          </Grid>
          <Grid item md={6} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Meeting Link
              </Typography>
              <TextField placeholder="Enter Meeting Link " name="" />
            </FormControl>
          </Grid>
          <Grid item md={6} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Meeting Date / Time
              </Typography>
              <SelectDateTimePicker disablePast />
            </FormControl>
          </Grid>
        </Grid>
        <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 3 }}>
          <Stack spacing={2} direction="row">
            <Button variant="contained" color="secondary">
              Cancel
            </Button>
            <Button variant="contained" color="primary">
              Go Live
            </Button>
          </Stack>
        </Box>
      </form>
    </CreateLiveClassRoot>
  );
}

export default CreateLiveClass;
