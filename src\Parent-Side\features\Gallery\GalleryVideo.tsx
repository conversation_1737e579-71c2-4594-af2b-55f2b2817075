/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  useTheme,
  IconButton,
  Collapse,
  Tooltip,
  FormControl,
  Chip,
  Snackbar,
  Alert,
  CardContent,
} from '@mui/material';
import styled from 'styled-components';
import { BsFillPlayCircleFill } from 'react-icons/bs';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import SearchIcon from '@mui/icons-material/Search';
import Popup from '@/components/shared/Popup/Popup';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import Physics from '@/Parent-Side/assets/liveclass/Icons/Physics.svg';
import { MdAdd, MdContentCopy } from 'react-icons/md';
import { CloseIcon } from '@/theme/overrides/CustomIcons';
import { Student, StudentInfoData } from '@/config/StudentDetails';
import useSettings from '@/hooks/useSettings';
import AutorenewOutlinedIcon from '@mui/icons-material/AutorenewOutlined';
import { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import DateSelect from '@/components/shared/Selections/DateSelect';
import MenuEditDelete from '@/components/shared/Selections/MenuEditDelete';
import CreateScheduleList from '@/features/LiveClass/ScheduleList/CreateScheduleList';
import InsertDriveFileIcon from '@mui/icons-material/InsertDriveFile';
import View from './View';
import { Paper } from '@mui/material';
import { Tabs } from '@mui/material';
import { CardMedia } from '@mui/material';
import PopupVideoPlayer from '@/components/shared/PopupVideoPlayer';

export const data = [
  {
    title: 'Alex Peter',
    author: 'Passdaily',
    issuedOn: '12/02/2024',
    returnDate: '30/02/2024',
    bookId: '021',
    price: '200',
    fine: '50',
    status: 'Not Returned',
  },
  {
    title: 'Alex Peter2',
    author: 'Passdaily',
    issuedOn: '12/02/2024',
    returnDate: '30/02/2024',
    bookId: '021',
    price: '200',
    fine: '50',
    status: 'Returned',
  },
  {
    title: 'Alex Peter',
    author: 'Passdaily',
    issuedOn: '12/02/2024',
    returnDate: '30/02/2024',
    bookId: '021',
    price: '200',
    fine: '50',
    status: 'Not Returned',
  },
  {
    title: 'Alex Peter',
    author: 'Passdaily',
    issuedOn: '12/02/2024',
    returnDate: '30/02/2024',
    bookId: '021',
    price: '200',
    fine: '50',
    status: 'Returned',
  },
  {
    title: 'Alex Peter',
    author: 'Passdaily',
    issuedOn: '12/02/2024',
    returnDate: '30/02/2024',
    bookId: '021',
    price: '200',
    fine: '50',
    status: 'Not Returned',
  },
  {
    title: 'Alex Peter',
    author: 'Passdaily',
    issuedOn: '12/02/2024',
    returnDate: '30/02/2024',
    bookId: '021',
    price: '200',
    fine: '50',
    status: 'Returned',
  },
  {
    title: 'Alex Peter',
    author: 'Passdaily',
    issuedOn: '12/02/2024',
    returnDate: '30/02/2024',
    bookId: '021',
    price: '200',
    fine: '50',
    status: 'Not Returned',
  },
  {
    title: 'Alex Peter',
    author: 'Passdaily',
    issuedOn: '12/02/2024',
    returnDate: '30/02/2024',
    bookId: '021',
    price: '200',
    fine: '50',
    status: 'Not Returned',
  },
  {
    title: 'Alex Peter',
    author: 'Passdaily',
    issuedOn: '12/02/2024',
    returnDate: '30/02/2024',
    bookId: '021',
    price: '200',
    fine: '50',
    status: 'Not Returned',
  },
  {
    title: 'Alex Peter',
    author: 'Passdaily',
    issuedOn: '12/02/2024',
    returnDate: '30/02/2024',
    bookId: '021',
    price: '200',
    fine: '50',
    status: 'Not Returned',
  },
];

const GalleryVideoRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  .student_card {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  }

  .Card {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    height: calc(100vh - 110px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 40px);
      flex-grow: 1;

      .card-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: auto;
        ::-webkit-scrollbar {
          width: 0px;
        }

        .MuiTableContainer-root {
          max-height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;
const years = ['2023-2024', '2024-2025'];

function GalleryVideo() {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const [changeView, setChangeView] = useState(false);
  const [showFilter, setShowFilter] = useState(false);
  const [Delete, setDelete] = useState(false);
  const [createPopup, setCreatePopup] = useState(false);

  const [students, setStudents] = useState(StudentInfoData);
  const [open, setOpen] = useState(false);
  const [openNew, setOpenNew] = useState(false);
  const [showNewMultiple, setShowNewMultiple] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [updatedData, setData] = useState(data);
  const [videoPopup, setVideoPopup] = React.useState(false);
  const [videoFile, setVideoFile] = React.useState();

  const [selectedYear, setSelectedYear] = useState(years[0]);

  const filteredData = data.filter((item) => years.includes(item.label));

  const handleTabChange = (event, newValue) => {
    setSelectedYear(newValue);
  };
  const handlePlayVideo = (eventLinkFile) => {
    setVideoPopup(true);
    setVideoFile(eventLinkFile);
    // console.log('videoFile::::----', videoFile);
  };

  const GalleryVideoColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'author',
        dataKey: 'author',
        headerLabel: 'Author',
      },
      {
        name: 'issuedOn',
        dataKey: 'issuedOn',
        headerLabel: 'Issued On',
      },
      {
        name: 'returnDate',
        dataKey: 'returnDate',
        headerLabel: 'Return Date',

        // renderCell: (row) => {
        //   return (
        //     <Stack direction="row" alignItems="center" maxWidth="90%">
        //       <Tooltip title={`${row.meetingLink}`} arrow>
        //         <Chip
        //           size="small"
        //           label="40"
        //           variant="outlined"
        //           sx={{
        //             border: '0px',
        //             mr: '1px',
        //             mb: 2,
        //             backgroundColor: isLight ? theme.palette.info.lighter : theme.palette.grey[700],
        //             color: isLight ? theme.palette.info.dark : theme.palette.grey[100],
        //             width: 'auto',
        //           }}
        //         />
        //       </Tooltip>
        //     </Stack>
        //   );
        // },
      },
      {
        name: 'bookId',
        dataKey: 'bookId',
        headerLabel: 'Book Id',
      },
    ],
    [theme, isLight]
  );

  return (
    <Page title="Schedule List">
      <GalleryVideoRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 1, md: 2 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={20} width="100%">
              Gallery
            </Typography>
          </Stack>
          <Stack>
            <Divider />
          </Stack>
          <Tabs sx={{ mt: 2 }} value={selectedYear} onChange={handleTabChange}>
            {years.map((year) => (
              <Button
                key={year}
                variant={selectedYear === year ? 'contained' : 'outlined'}
                color={selectedYear === year ? 'success' : 'secondary'}
                sx={{
                  mr: 2,
                  borderRadius: 10,
                  backgroundColor: selectedYear === year ? theme.palette.success.main : undefined,
                  color: selectedYear === year ? theme.palette.common.white : undefined,
                  '&:hover': {
                    backgroundColor: selectedYear === year ? theme.palette.success.main : undefined,
                    boxShadow: 'none',
                  },
                }}
                onClick={() => setSelectedYear(year)}
              >
                {year}
              </Button>
            ))}
          </Tabs>
          <div className="card-main-body">
            <Box className="card-container" my={2}>
              <Grid container spacing={3}>
                {updatedData.map((data, rowIndex) => (
                  <Grid position="relative" item xxl={3} xl={4} lg={6} md={12} sm={6} xs={12}>
                    <CardMedia
                      component="img"
                      alt="Events"
                      height="160"
                      image="https://images.unsplash.com/photo-1479685894911-37e888d38f0a?q=80&w=2069&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                      sx={{ position: 'relative', cursor: 'pointer  ' }}
                      onClick={() => handlePlayVideo('')}
                    />
                    <IconButton
                      onClick={() => handlePlayVideo('')}
                      sx={{
                        position: 'absolute',
                        top: '50%',
                        left: '50%',
                        transform: 'translate(-50%, -50%)',
                        color: 'white',
                        fontSize: '3rem', // Adjust icon size as needed
                      }}
                      aria-label="play"
                    >
                      <BsFillPlayCircleFill />
                    </IconButton>
                    <Stack mt={1} direction="row" justifyContent="space-between" alignItems="center">
                      <Stack>
                        <Typography variant="subtitle2" fontSize={13}>
                          {data.title}
                        </Typography>
                        <Box display="flex" alignItems="center">
                          <CalendarTodayIcon color="secondary" sx={{ fontSize: '10px', marginRight: '5px' }} />
                          <Typography
                            color="GrayText"  
                            sx={{ fontFamily: 'Poppins medium', fontWeight: '500', fontSize: '10px' }}
                          >
                            12/02/2024
                          </Typography>
                        </Box>
                      </Stack>
                      <Typography variant="subtitle2" fontSize={18}>
                        05
                      </Typography>
                    </Stack>
                  </Grid>
                ))}
              </Grid>
            </Box>
          </div>
        </Card>
      </GalleryVideoRoot>
      <Popup
        title="Alex Peter"
        size="xs"
        state={videoPopup}
        onClose={() => setVideoPopup(false)}
        popupContent={<PopupVideoPlayer videoFile={videoFile} />}
      />
      {/* <Popup
        size="xs"
        state={Delete}
        onClose={handleClickCloseDelete}
        popupContent={<DeleteMessage message="Are you sure want to delete?" />}
      /> */}
      {/* <Popup
        size="sm"
        title="Assignment Details"
        state={createPopup}
        onClose={() => setCreatePopup(false)}
        popupContent={<View />}
      /> */}
    </Page>
  );
}

export default GalleryVideo;
