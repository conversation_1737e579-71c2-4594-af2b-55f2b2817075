/* eslint-disable no-else-return */
/* eslint-disable jsx-a11y/alt-text */
import React, { useEffect, useState } from 'react';
import Page from '@/components/shared/Page';
import { Stack, Button, Typography, useTheme } from '@mui/material';
import styled from 'styled-components';
import CurrencyRupeeIcon from '@mui/icons-material/CurrencyRupee';
import useSettings from '@/hooks/useSettings';
import Lottie from 'lottie-react';
import PaySuccess from '@/Parent-Side/assets/payFee/PaySuccess.json';
import PayFailed from '@/Parent-Side/assets/payFee/PayFailed.json';
import PayExpired from '@/Parent-Side/assets/payFee/PayExpired.json';
import PayPending from '@/Parent-Side/assets/payFee/PayPending.json';
import PayFailure from '@/Parent-Side/assets/payFee/PayFailure.json';
import { TransactionAPIResponseData } from '@/types/Payment';
import api from '@/api';
import { Link } from 'react-router-dom';
import dayjs from 'dayjs';

const TransactionResponseRoot = styled.div`
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .status-icon {
    height: 300px;
  }

  .success-message {
    bottom: 310px;
  }
  .failure-message {
    bottom: 160px;
  }

  @media screen and (max-width: 500px) {
    .success-message {
      bottom: 290px;
    }
  }
  @media screen and (max-width: 340px) {
    .success-message {
      bottom: 270px;
    }
  }

  /* ---- */
  @media screen and (max-width: 450px) {
    .status-icon {
      height: 270px;
    }
  }

  @media screen and (max-width: 390px) {
    .status-icon {
      height: 230px;
    }
  }
  @media screen and (max-width: 350px) {
    .status-icon {
      height: 210px;
    }
  }
  @media screen and (max-width: 330px) {
    .status-icon {
      height: 150px;
    }
  }
`;

type ResponseStatus = 'idle' | 'failed' | 'failure' | 'pending' | 'success' | 'error';
type ShowMessageText = 'idle' | 'failed' | 'failure' | 'pending' | 'success' | 'error';
function TransactionResponse() {
  const theme = useTheme();
  const isLight = useSettings().themeMode === 'light';
  const [responseStatus, setResponseStatus] = useState<ResponseStatus>('idle');
  const [showMessageText, setShowMessageText] = useState<ShowMessageText>('idle');
  const [transactionData, setTransactionData] = useState<TransactionAPIResponseData | undefined>(undefined);
  const { amount, transactionDate, receiptNo, paymentMethod, transactionId, redirectUrl } = transactionData || {};

  // const datee = '2024-08-22T09:43:20+05:30';
  const formattedDate = dayjs(transactionDate, 'YYYY-MM-DDTHH:mm:ssZ').format('DD MMMM YYYY, h:mm a');

  const queryParams = new URLSearchParams(window.location.search);
  const term = queryParams.get('transactionResponse');

  useEffect(() => {
    console.log('term::::----', term);
    api.Payment.transactionResponse({
      studentId: 10,
      tr: term,
    })
      .then((transactionrequest) => {
        console.log('transactionrequest::::----', transactionrequest.status);
        if (transactionrequest.status === 'success') {
          setResponseStatus('success');
          setTransactionData(transactionrequest.data);
        }
        if (transactionrequest.status === 'failure') {
          setResponseStatus('failure');
          setTransactionData(transactionrequest.data);
        }
        if (transactionrequest.status === 'pending') {
          setResponseStatus('pending');
          setTransactionData(transactionrequest.data);
        }
        if (transactionrequest.status === 'failed') {
          setResponseStatus('failed');
        }
        if (transactionrequest.status === 'error') {
          setResponseStatus('error');
        }
      })
      .catch((error) => {
        setResponseStatus('failed');
        console.error('Error fetching transaction request:', error);
      });
  }, [term]);

  useEffect(() => {
    if (responseStatus === 'success' || responseStatus === 'failure' || responseStatus === 'pending') {
      const timer = setTimeout(() => {
        setShowMessageText(responseStatus);
      }, 2500); // Delay of 2 seconds
      return () => clearTimeout(timer); // Cleanup the timer on component unmount
    } else {
      const timer = setTimeout(() => {
        setShowMessageText(responseStatus);
      }, 1000); // Delay of 1 seconds
      return () => clearTimeout(timer); // Cleanup the timer on component unmount
    }
  }, [responseStatus]);

  return (
    <TransactionResponseRoot>
      {responseStatus === 'success' && (
        <Stack
          direction="column"
          alignItems="center"
          justifyContent="center"
          position="relative"
          maxWidth={500}
          // height={{ xs: 250, sm: 350 }}
          // className="status-icon"
        >
          <Lottie
            animationData={PaySuccess}
            loop={false}
            style={{
              marginTop: -100,
              // backgroundColor: theme.palette.common.white,
              transform: showMessageText === 'success' ? 'translateY(0)' : 'translateY(100px)',
              transition: ' transform .5s ease-in-out',
            }}
          />
          <Typography
            fontSize={20}
            fontWeight={600}
            position="absolute"
            className="success-message"
            // bottom={{ xs: 310, sm: 380, md: 380 }}
            sx={{
              display: 'flex',
              alignItems: 'center',
              whiteSpace: 'nowrap',
              opacity: showMessageText === 'success' ? 1 : 0,
              transform: showMessageText === 'success' ? 'translateY(0)' : 'translateY(100px)',
              transition: ' transform .5s ease-in-out',
            }}
          >
            Payment Successful
          </Typography>
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              gap: 10,
              opacity: showMessageText === 'success' ? 1 : 0,
              transform: showMessageText === 'success' ? 'translateY(0)' : 'translateY(100px)',
              transition: ' transform .5s ease-in-out',
            }}
          >
            <Stack direction="row" alignItems="center">
              <CurrencyRupeeIcon sx={{ fontSize: 30 }} />
              <Typography variant="subtitle2" fontSize={30}>
                {amount?.toFixed(2)}
              </Typography>
            </Stack>
            <Typography variant="subtitle2">{formattedDate}</Typography>
            <Typography variant="subtitle2">Receipt No : {receiptNo}</Typography>
            <Typography variant="subtitle2">Transaction Id : {transactionId}</Typography>
            <Typography variant="subtitle2">Payment Type : {paymentMethod}</Typography>
          </div>
          <Link
            to={redirectUrl}
            style={{
              marginTop: 30,
              opacity: showMessageText === 'success' ? 1 : 0,
              transform: showMessageText === 'success' ? 'translateY(0)' : 'translateY(100px)',
              transition: ' transform .5s ease-in-out',
            }}
          >
            <Button variant="outlined" color="success" sx={{ borderRadius: 10 }}>
              Done
            </Button>
          </Link>
        </Stack>
      )}
      {/* failure */}
      {responseStatus === 'failure' && (
        <Stack
          direction="column"
          alignItems="center"
          justifyContent="center"
          position="relative"
          maxWidth={500}
          // height={{ xs: 250, sm: 350 }}
          // className="status-icon"
        >
          <Lottie
            animationData={PayFailure}
            loop={false}
            style={{
              width: '190px',
              height: '190px',
              marginTop: -100,
              // backgroundColor: theme.palette.common.white,
              transform: showMessageText === 'failure' ? 'translateY(0)' : 'translateY(100px)',
              transition: ' transform .5s ease-in-out',
            }}
          />
          <Typography
            fontSize={20}
            fontWeight={600}
            mt={5}
            mb={8}
            // position="absolute"
            // className="failure-message"
            // bottom={{ xs: 310, sm: 380, md: 380 }}
            sx={{
              display: 'flex',
              alignItems: 'center',
              whiteSpace: 'nowrap',
              opacity: showMessageText === 'failure' ? 1 : 0,
              transform: showMessageText === 'failure' ? 'translateY(0)' : 'translateY(100px)',
              transition: ' transform .5s ease-in-out',
            }}
          >
            Payment Failed
          </Typography>
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              gap: 10,
              opacity: showMessageText === 'failure' ? 1 : 0,
              transform: showMessageText === 'failure' ? 'translateY(0)' : 'translateY(100px)',
              transition: ' transform .5s ease-in-out',
            }}
          >
            <Stack direction="row" alignItems="center">
              <CurrencyRupeeIcon sx={{ fontSize: 30 }} />
              <Typography variant="subtitle2" fontSize={30}>
                {amount?.toFixed(2)}
              </Typography>
            </Stack>
            <Typography variant="subtitle2">{formattedDate}</Typography>
            <Typography variant="subtitle2">Receipt No : {receiptNo}</Typography>
            <Typography variant="subtitle2">Transaction Id : {transactionId}</Typography>
            <Typography variant="subtitle2">Payment Type : {paymentMethod}</Typography>
          </div>
          <Link
            to={redirectUrl}
            style={{
              marginTop: 30,
              opacity: showMessageText === 'failure' ? 1 : 0,
              transform: showMessageText === 'failure' ? 'translateY(0)' : 'translateY(100px)',
              transition: ' transform .5s ease-in-out',
            }}
          >
            <Button variant="outlined" color="error" sx={{ borderRadius: 10 }}>
              Go Back
            </Button>
          </Link>
        </Stack>
      )}
      {responseStatus === 'pending' && (
        <Stack
          direction="column"
          alignItems="center"
          justifyContent="center"
          position="relative"
          maxWidth={500}
          // height={{ xs: 250, sm: 350 }}
          // className="status-icon"
        >
          <Lottie
            animationData={PayPending}
            loop={false}
            style={{
              width: '250px',
              marginTop: -100,
              // backgroundColor: theme.palette.common.white,
              transform: showMessageText === 'pending' ? 'translateY(0)' : 'translateY(100px)',
              transition: ' transform .5s ease-in-out',
            }}
          />
          <Typography
            fontSize={20}
            fontWeight={600}
            mb={8}
            // position="absolute"
            // className="success-message"
            // bottom={{ xs: 310, sm: 380, md: 380 }}
            sx={{
              display: 'flex',
              alignItems: 'center',
              whiteSpace: 'nowrap',
              opacity: showMessageText === 'pending' ? 1 : 0,
              transform: showMessageText === 'pending' ? 'translateY(0)' : 'translateY(100px)',
              transition: ' transform .5s ease-in-out',
            }}
          >
            Payment Pending
          </Typography>
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              gap: 10,
              opacity: showMessageText === 'pending' ? 1 : 0,
              transform: showMessageText === 'pending' ? 'translateY(0)' : 'translateY(100px)',
              transition: ' transform .5s ease-in-out',
            }}
          >
            <Stack direction="row" alignItems="center">
              <CurrencyRupeeIcon sx={{ fontSize: 30 }} />
              <Typography variant="subtitle2" fontSize={30}>
                {amount?.toFixed(2)}
              </Typography>
            </Stack>
            <Typography variant="subtitle2">{formattedDate}</Typography>
            <Typography variant="subtitle2">Receipt No : {receiptNo}</Typography>
            <Typography variant="subtitle2">Transaction Id : {transactionId}</Typography>
            <Typography variant="subtitle2">Payment Type : {paymentMethod}</Typography>
          </div>
          <Link
            to={redirectUrl}
            style={{
              marginTop: 30,
              opacity: showMessageText === 'pending' ? 1 : 0,
              transform: showMessageText === 'pending' ? 'translateY(0)' : 'translateY(100px)',
              transition: ' transform .5s ease-in-out',
            }}
          >
            <Button variant="outlined" color="warning" sx={{ borderRadius: 10 }}>
              Done
            </Button>
          </Link>
        </Stack>
      )}
      {responseStatus === 'failed' && (
        <>
          <Stack
            direction="column"
            alignItems="center"
            justifyContent="center"
            maxWidth={500}
            // className="status-icon"
          >
            <Lottie animationData={PayFailed} loop={false} />
          </Stack>
          <Link
            to={redirectUrl}
            style={{
              opacity: showMessageText === 'failed' ? 1 : 0,
              transform: showMessageText === 'failed' ? 'scale(1)' : 'scale(.5)',
              transition: 'opacity .5s ease-in-out, transform .5s ease-in-out',
            }}
          >
            <Button variant="outlined" color="error" sx={{ borderRadius: 10 }}>
              Go back
            </Button>
          </Link>
        </>
      )}
      {responseStatus === 'error' && (
        <>
          <Stack direction="column" alignItems="center" justifyContent="center" position="relative">
            <Lottie animationData={PayExpired} loop={false} />
          </Stack>
          <Typography
            color={theme.palette.warning.main}
            fontSize={20}
            fontWeight={600}
            //   position="absolute"
            //   top={{ xs: 400, sm: 380, md: 380 }}
            sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}
          >
            Payment Expired!
          </Typography>
          <Link
            to={redirectUrl}
            style={{
              marginTop: 30,
              opacity: showMessageText === 'error' ? 1 : 0,
              transform: showMessageText === 'error' ? 'scale(1)' : 'scale(.5)',
              transition: 'opacity .5s ease-in-out, transform .5s ease-in-out',
            }}
          >
            <Button variant="outlined" color="warning" sx={{ borderRadius: 10 }}>
              Go back
            </Button>
          </Link>
        </>
      )}
    </TransactionResponseRoot>
  );
}

export default TransactionResponse;
