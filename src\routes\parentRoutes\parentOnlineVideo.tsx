import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import Loadable from '@/components/shared/Loadable';

const OnlineVideoIndex = Loadable(lazy(() => import('@/Parent-Side/pages/OnlineVideoClass')));
const ParentOnlineVideo = Loadable(lazy(() => import('@/Parent-Side/features/OnlineVideo/ParentOnlineVideo')));
const ParentOfflineVideo = Loadable(lazy(() => import('@/Parent-Side/features/OnlineVideo/ParentOfflineVideo')));

export const parentOnlineVideo: RouteObject[] = [
  {
    path: '/parent-online-video',
    element: <OnlineVideoIndex />,
    children: [
      {
        path: 'online-video',
        element: <ParentOnlineVideo />,
      },
      {
        path: 'offline-video',
        element: <ParentOfflineVideo />,
      },
    ],
  },
];
