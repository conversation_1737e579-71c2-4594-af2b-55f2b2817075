import React, { useCallback, useEffect, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  Button,
  Typography,
  Card,
  IconButton,
  Collapse,
  CardContent,
  CardActionArea,
  useTheme,
  Tooltip,
  FormControl,
  TextField,
  CardMedia,
} from '@mui/material';
import styled from 'styled-components';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import SearchIcon from '@mui/icons-material/Search';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import DeleteIcon from '@mui/icons-material/Delete';
import { MdAdd } from 'react-icons/md';
import Videoplayer from '@/components/Dashboard/Videoplayer';
import { BiTable } from 'react-icons/bi';
import { RxVideo } from 'react-icons/rx';
import { AlbumVideoInfo } from '@/types/Album';
import CreateVideoForm from './CreateVideoForm';

// const dummy_array = [
//   {
//     slNo: 1,
//     albumName: 'AlbumName',
//     title: 'Video Title',
//     video: 'https://youtu.be/DlHC7I9dBNU',
//     thumbnail:
//       'https://plus.unsplash.com/premium_photo-1661634073903-2ecdccdfc8a2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
//   },
//   {
//     slNo: 2,
//     albumName: 'AlbumName',
//     title: 'Video Title',
//     video: 'https://youtu.be/DlHC7I9dBNU',
//     thumbnail:
//       'https://images.unsplash.com/photo-1588075592405-d3d4f0846961?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2072&q=80',
//   },
//   {
//     slNo: 3,
//     albumName: 'AlbumName',
//     title: 'Video Title',
//     video: 'https://youtu.be/DlHC7I9dBNU',
//     thumbnail:
//       'https://images.unsplash.com/photo-1588072432836-e10032774350?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2072&q=80',
//   },
//   {
//     slNo: 4,
//     albumName: 'AlbumName',
//     title: 'Video Title',
//     video: 'https://youtu.be/DlHC7I9dBNU',
//     thumbnail:
//       'https://images.unsplash.com/photo-1534643960519-11ad79bc19df?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
//   },
//   {
//     slNo: 5,
//     albumName: 'AlbumName',
//     title: 'Video Title',
//     video: 'https://youtu.be/DlHC7I9dBNU',
//     thumbnail:
//       'https://images.unsplash.com/photo-1636202339022-7d67f7447e3a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2071&q=80 ',
//   },
//   {
//     slNo: 6,
//     albumName: 'AlbumName',
//     title: 'Video Title',
//     video: 'https://youtu.be/DlHC7I9dBNU',
//     thumbnail:
//       'https://images.unsplash.com/photo-1503676685182-2531a01b5b5c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1776&q=80',
//   },
//   {
//     slNo: 7,
//     albumName: 'AlbumName',
//     title: 'Video Title',
//     video: 'https://youtu.be/DlHC7I9dBNU',
//     thumbnail:
//       'https://images.unsplash.com/photo-1577896851231-70ef18881754?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
//   },
//   {
//     slNo: 8,
//     albumName: 'AlbumName',
//     title: 'Video Title',
//     video: 'https://youtu.be/DlHC7I9dBNU',
//     thumbnail:
//       'https://images.unsplash.com/photo-1509062522246-3755977927d7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1864&q=80',
//   },
//   {
//     slNo: 9,
//     albumName: 'AlbumName',
//     title: 'Video Title',
//     video: 'https://youtu.be/DlHC7I9dBNU',
//     thumbnail:
//       'https://images.unsplash.com/photo-1536337005238-94b997371b40?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2069&q=80',
//   },
//   {
//     slNo: 10,
//     albumName: 'AlbumName',
//     title: 'Video Title',
//     video: 'https://youtu.be/DlHC7I9dBNU',
//     thumbnail:
//       'https://images.unsplash.com/photo-1427504494785-3a9ca7044f45?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
//   },
//   {
//     slNo: 11,
//     albumName: 'AlbumName',
//     title: 'Video Title',
//     video: 'https://youtu.be/DlHC7I9dBNU',
//     thumbnail:
//       'https://images.unsplash.com/photo-1431608660976-4fe5bcc2112c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
//   },
//   {
//     slNo: 12,
//     albumName: 'AlbumName',
//     title: 'Video Title',
//     video: 'https://youtu.be/DlHC7I9dBNU',
//     thumbnail:
//       'https://images.unsplash.com/photo-1544456203-0af5a69f5789?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1974&q=80',
//   },
//   {
//     slNo: 1,
//     albumName: 'AlbumName',
//     title: 'Video Title',
//     video: 'https://youtu.be/DlHC7I9dBNU',
//     thumbnail:
//       'https://plus.unsplash.com/premium_photo-1661634073903-2ecdccdfc8a2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
//   },
//   {
//     slNo: 2,
//     albumName: 'AlbumName',
//     title: 'Video Title',
//     video: 'https://youtu.be/DlHC7I9dBNU',
//     thumbnail:
//       'https://images.unsplash.com/photo-1588075592405-d3d4f0846961?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2072&q=80',
//   },
//   {
//     slNo: 3,
//     albumName: 'AlbumName',
//     title: 'Video Title',
//     video: 'https://youtu.be/DlHC7I9dBNU',
//     thumbnail:
//       'https://images.unsplash.com/photo-1588072432836-e10032774350?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2072&q=80',
//   },
//   {
//     slNo: 4,
//     albumName: 'AlbumName',
//     title: 'Video Title',
//     video: 'https://youtu.be/DlHC7I9dBNU',
//     thumbnail:
//       'https://images.unsplash.com/photo-1534643960519-11ad79bc19df?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
//   },
//   {
//     slNo: 5,
//     albumName: 'AlbumName',
//     title: 'Video Title',
//     video: 'https://youtu.be/DlHC7I9dBNU',
//     thumbnail:
//       'https://images.unsplash.com/photo-1636202339022-7d67f7447e3a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2071&q=80 ',
//   },
//   {
//     slNo: 6,
//     albumName: 'AlbumName',
//     title: 'Video Title',
//     video: 'https://youtu.be/DlHC7I9dBNU',
//     thumbnail:
//       'https://images.unsplash.com/photo-1503676685182-2531a01b5b5c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1776&q=80',
//   },
//   {
//     slNo: 7,
//     albumName: 'AlbumName',
//     title: 'Video Title',
//     video: 'https://youtu.be/DlHC7I9dBNU',
//     thumbnail:
//       'https://images.unsplash.com/photo-1577896851231-70ef18881754?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
//   },
//   {
//     slNo: 8,
//     albumName: 'AlbumName',
//     title: 'Video Title',
//     video: 'https://youtu.be/DlHC7I9dBNU',
//     thumbnail:
//       'https://images.unsplash.com/photo-1509062522246-3755977927d7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1864&q=80',
//   },
//   {
//     slNo: 9,
//     albumName: 'AlbumName',
//     title: 'Video Title',
//     video: 'https://youtu.be/DlHC7I9dBNU',
//     thumbnail:
//       'https://images.unsplash.com/photo-1536337005238-94b997371b40?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2069&q=80',
//   },
//   {
//     slNo: 10,
//     albumName: 'AlbumName',
//     title: 'Video Title',
//     video: 'https://youtu.be/DlHC7I9dBNU',
//     thumbnail:
//       'https://images.unsplash.com/photo-1427504494785-3a9ca7044f45?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
//   },
//   {
//     slNo: 11,
//     albumName: 'AlbumName',
//     title: 'Video Title',
//     video: 'https://youtu.be/DlHC7I9dBNU',
//     thumbnail:
//       'https://images.unsplash.com/photo-1431608660976-4fe5bcc2112c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
//   },
//   {
//     slNo: 12,
//     albumName: 'AlbumName',
//     title: 'Video Title',
//     video: 'https://youtu.be/DlHC7I9dBNU',
//     thumbnail:
//       'https://images.unsplash.com/photo-1544456203-0af5a69f5789?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1974&q=80',
//   },
// ];

const VideoListRoot = styled.div`
  padding: 1rem;
  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }
    .card-main-body {
      display: flex;
      flex-direction: column;
      height: calc(100% - 40px);
      flex-grow: 1;
      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;
        .MuiTableContainer-root {
          height: 100%;
        }
        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

const VideoOverlay = styled.div`
  position: absolute;
  width: 100%;
  bottom: 0px;
  display: flex;
  align-items: center;
  color: #fff;
  opacity: 1;
  transition: opacity 0.3s ease;
  z-index: 1;

  .overlay-content {
    padding: 0.5rem;
    backdrop-filter: blur(20px); /* Add backdrop-filter property */
    border-radius: 0 0 12px 12px;
  }
`;

const DefaultAlbumInfo: AlbumVideoInfo = {
  albumId: 0,
  albumName: '',
  videoTitle: '',
  thumbnail: '',
  videoFile: '',
};

function VideoList() {
  const theme = useTheme();
  const [drawerOpen, setDrawerOpen] = React.useState<boolean>(false);
  const [showFilter, setShowFilter] = useState(false);
  const [changeView, setChangeView] = useState(true);
  const [videoPlaying, setVideoPlaying] = useState(true);
  const [albumVideoData, setAlbumVideoData] = useState<AlbumVideoInfo[]>([]);

  const toggleDrawerClose = useCallback(() => setDrawerOpen(false), []);

  const handleSaveorEdit = useCallback(
    (value: AlbumVideoInfo, uploadedVideo: string) => {
      const existingArray = JSON.parse(localStorage.getItem('myDummyVideoArray')) || [];

      toggleDrawerClose();
    },
    [toggleDrawerClose]
  );

  const handleOpen = () => {
    setDrawerOpen(true);
  };

  useEffect(() => {
    try {
      const existingArray = JSON.parse(localStorage.getItem('myDummyVideoArray')) || [];
      setAlbumVideoData(existingArray);
    } catch (error) {
      // Handle error if JSON parsing fails or localStorage.getItem returns null
      console.error('Error retrieving data from localStorage:', error);
      setAlbumVideoData([]);
    }
  }, []);

  const handleDelete = (row: AlbumVideoInfo) => {
    const updatedArray = albumVideoData.filter((item) => item.albumId !== row.albumId);
    setAlbumVideoData(updatedArray);
    localStorage.setItem('myDummyVideoArray', JSON.stringify(updatedArray));
  };

  const getRowKey = useCallback((row: AlbumVideoInfo) => row.albumId, []);

  const videoListColumns: DataTableColumn<AlbumVideoInfo>[] = useMemo(
    () => [
      {
        name: 'albumId',
        dataKey: 'albumId',
        headerLabel: 'Sl.No',
        sortable: true,
      },
      {
        name: 'albumName',
        dataKey: 'albumName',
        headerLabel: 'Album Name',
      },
      {
        name: 'videoTitle',
        dataKey: 'videoTitle',
        headerLabel: 'Video Title',
      },
      {
        name: 'videoFile',
        headerLabel: 'Video File',
        renderCell: (row) => {
          return (
            <Card sx={{ width: 170, height: 100 }}>
              <CardActionArea>
                <Box height={100}>
                  <Videoplayer url={row.videoFile} thumbnail={row.thumbnail} />
                </Box>
              </CardActionArea>
            </Card>
          );
        },
      },
      {
        name: 'actions',
        headerLabel: 'Actions',
        renderCell: (row) => {
          return (
            <Stack direction="row" gap={1}>
              <IconButton size="small" color="error" onClick={() => handleDelete(row)} sx={{ padding: 0.5 }}>
                <DeleteIcon />
              </IconButton>
            </Stack>
          );
        },
      },
    ],
    [handleOpen]
  );

  return (
    <Page title="List">
      <VideoListRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17}>
              Videos List
            </Typography>
            <Box pb={1} sx={{ flexShrink: 0 }}>
              <Tooltip title="Search">
                <IconButton aria-label="delete" color="primary" sx={{ mr: 0 }} onClick={() => setShowFilter((x) => !x)}>
                  <SearchIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title={changeView === true ? 'Table View' : 'Card View'}>
                <IconButton
                  sx={{ mr: 1 }}
                  className="change-view-button"
                  color="primary"
                  onClick={() => setChangeView((prevChangeView) => !prevChangeView)}
                >
                  {changeView === true ? <BiTable /> : <RxVideo />}
                </IconButton>
              </Tooltip>
              <Button onClick={handleOpen} sx={{ borderRadius: '20px' }} variant="outlined" size="small">
                <MdAdd size="20px" /> Add
              </Button>
            </Box>
          </Stack>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={3} alignItems="end">
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 250 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Album Name
                      </Typography>
                      <TextField placeholder="Enter Name" />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 250 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Image Title
                      </Typography>
                      <TextField placeholder="Enter Title" />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12} sm={6}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>
            {changeView === true ? (
              <Box
                my={2}
                sx={{
                  overflow: 'auto',
                  '&::-webkit-scrollbar': {
                    width: 0,
                  },
                }}
              >
                <Grid container spacing={3}>
                  {albumVideoData.map((item) => (
                    <Grid item xxl={2} xl={3} lg={4} md={6} sm={4} xs={12} key={item.albumId}>
                      <Card sx={{ position: 'relative' }}>
                        <IconButton
                          onClick={() => handleDelete(item)}
                          size="small"
                          sx={{
                            position: 'absolute',
                            right: 5,
                            top: 5,
                            zIndex: 1,
                            border: `1px solid ${theme.palette.grey[300]}`,
                            backgroundColor: '#fff',
                            '&:hover': {
                              backgroundColor: theme.palette.grey[200],
                            },
                          }}
                          aria-label={`info about ${item.videoTitle}`}
                        >
                          <DeleteIcon color="error" sx={{ fontSize: '18px' }} />
                        </IconButton>

                        <CardMedia>
                          <Box height={150} onClick={() => setVideoPlaying(false)}>
                            {/* <video width="100%" controls src={item.videoFile} /> */}
                            <Videoplayer url={item.videoFile} thumbnail={item.thumbnail} />
                          </Box>
                          {videoPlaying && (
                            <VideoOverlay>
                              <CardContent className="overlay-content w-100">
                                <Typography variant="subtitle2" color={theme.palette.common.white} component="div">
                                  {item.albumName}
                                </Typography>
                              </CardContent>
                            </VideoOverlay>
                          )}
                        </CardMedia>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </Box>
            ) : (
              <Paper className="card-table-container" sx={{ marginTop: '12px' }}>
                <DataTable
                  allowPagination
                  PaginationProps={[]}
                  columns={videoListColumns}
                  data={albumVideoData}
                  getRowKey={getRowKey}
                  fetchStatus="success"
                />
              </Paper>
            )}
          </div>
        </Card>
      </VideoListRoot>
      <TemporaryDrawer
        onClose={toggleDrawerClose}
        Title="Upload Details"
        state={drawerOpen}
        DrawerContent={
          <CreateVideoForm
            onSave={handleSaveorEdit}
            albumInfo={DefaultAlbumInfo}
            onCancel={toggleDrawerClose}
            onClose={toggleDrawerClose}
          />
        }
      />
    </Page>
  );
}

export default VideoList;
