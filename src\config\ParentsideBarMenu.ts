import { ListMenuItem } from '@/types/Layout';

export const SideBarMenuData: ListMenuItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: 'dashboard',
    link: '/',
    url: '/',
  },
  {
    id: 'payFees',
    label: 'Pay Fees',
    icon: 'paymentDetails',
    link: '/parent-fees',
    url: '/parent-fees/pay-fee',
  },

  {
    id: 'liveClass',
    label: 'Live Class',
    icon: 'liveClass',
    link: '/parent-live-class',
    url: '/parent-live-class/zoom-live-class',
  },
  {
    id: 'onlineVideoClass',
    label: 'Online Video Class',
    icon: 'onlineVideoClass',
    link: '/parent-online-video',
    url: '/parent-online-video',
  },
  {
    id: 'studyMaterials',
    label: 'Study Materials',
    icon: 'staffManagement',
    link: '/parent-study-materials',
    url: '/parent-study-materials/study-materials',
  },
  {
    id: 'assignments',
    label: 'Assignments',
    icon: 'assignment',
    link: '/parent-assignments',
    url: '/parent-assignments',
  },
  {
    id: 'descriptiveExam',
    label: 'Descriptive Exam',
    icon: 'descriptiveExam',
    link: '/parent-descriptive-exam',
    url: '/parent-descriptive-exam',
  },
  {
    id: 'objectiveExam',
    label: 'Objective Exam',
    icon: 'objectiveExam',
    link: '/parent-objective-exam',
    url: '/parent-objective-exam',
  },
  {
    id: 'Enquiry',
    label: 'Enquiry',
    icon: 'parentEnquiry',
    link: '/parent-enquiry',
    url: '/parent-enquiry',
  },
  {
    id: 'Leave',
    label: 'Leave Application',
    icon: 'timeTable',
    link: '/leave-application',
    url: '/leave-application',
  },
  {
    id: 'Absent',
    label: 'Attendance Report',
    icon: 'feeAlert',
    link: '/absent-list',
    url: '/absent-list',
  },
  {
    id: 'library',
    label: 'Library',
    icon: 'library',
    link: '/library',
    url: '/library',
  },
  {
    id: 'gallery',
    label: 'Gallery',
    icon: 'album',
    link: '/gallery',
    url: '/gallery/gallery-photo',
  },
  {
    id: 'calendar',
    label: 'Calendar',
    icon: 'calendar',
    link: '/calendar',
    url: '/calendar',
  },
  {
    id: 'progressReport',
    label: 'Reports',
    icon: 'report',
    link: '/reports',
    url: '/reports',
  },
  {
    id: 'vehicleTrack',
    label: 'Track',
    icon: 'vehicleTracking',
    link: '/vehicle-track',
    url: '/vehicle-track',
  },
];
