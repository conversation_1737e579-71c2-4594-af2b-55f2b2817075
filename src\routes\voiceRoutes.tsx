import Loadable from '@/components/shared/Loadable';
import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';

const VoiceMessage = Loadable(lazy(() => import('@/pages/VoiceMessage')));
const CreateVoice = Loadable(lazy(() => import('@/features/VoiceMessage/CreateVoiceMessage/CreateVoiceMessage')));
const TemplateVoice = Loadable(lazy(() => import('@/features/VoiceMessage/VoiceTemplate/TemplateVoiceMessage')));
const OthersVoiceMessage = Loadable(lazy(() => import('@/features/VoiceMessage/OthersVoiceMessage')));

export const voiceRoutes: RouteObject[] = [
  {
    path: '/voice-message',
    element: <VoiceMessage />,
    children: [
      {
        path: 'create',
        element: <CreateVoice />,
      },
      {
        path: 'voice-template',
        element: <TemplateVoice />,
      },
      {
        path: 'send-others',
        element: <OthersVoiceMessage />,
      },
    ],
  },
];
