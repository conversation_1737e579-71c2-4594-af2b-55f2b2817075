/* eslint-disable react/no-array-index-key */
/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useEffect, useRef, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Box,
  Divider,
  Grid,
  Stack,
  TextField,
  Button,
  Table,
  TableContainer,
  Autocomplete,
  TableRow,
  TableCell,
  TableBody,
  Typography,
  Card,
  Checkbox,
} from '@mui/material';
import styled from 'styled-components';
// import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import RadioButtonUncheckedIcon from '@mui/icons-material/RadioButtonUnchecked';
import { SuccessIcon } from '@/theme/overrides/CustomIcons';
import { PaymentOrderRequest, PaymentOrderResponse } from '@/types/Payment';
import api from '@/api';

import { FetchStatus } from '@/types/Common';
import { nanoid } from '@reduxjs/toolkit';
import PaymentLaunchForm, { PaymentLaunchFormHandle } from './PaymentLaunchForm';
// import TemporaryDrawer from '@/components/shared/Popup/Drawer';
// import Popup from '@/components/shared/Popup/Popup';
// import { Pay } from './PayDrawer';
// import { DetailedReceipt, GetReceipt } from './Receipt';

// const status = ['Publish', 'Unpublish'];

const PayFeeRoot = styled.div`
  padding: 1rem;
  @media screen and (max-width: 420px) {
    padding: 0rem;
  }

  .Card {
    min-height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }
    @media screen and (max-width: 420px) {
      border-radius: 0px;
    }
  }
  .TableCard {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.grey[200] : props.theme.palette.grey[900]};
  }

  .fee-card {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.grey[200] : props.theme.palette.grey[900]};
  }
`;

function PayFee() {
  const paymentFormHandle = useRef<PaymentLaunchFormHandle>(null);
  const [createOrderStatus, setCreateOrderStatus] = useState<FetchStatus>('idle');
  const [orderResponse, setOrderResponse] = useState<PaymentOrderResponse | null>(null);
  const [readyToSubmit, setReadyToSubmit] = useState(false);

  // const [popup1, setPopup1] = React.useState(false);
  // const [popup2, setPopup2] = React.useState(false);
  // const [drawerOpen, setDrawerOpen] = React.useState<boolean>(false);

  // const toggleDrawerOpen = () => setDrawerOpen(true);

  // const toggleDrawerClose = () => setDrawerOpen(false);

  // const handleClickReceipt1 = () => {
  //   setPopup1(true);
  //   toggleDrawerClose();
  // };
  // const handleClickReceipt2 = () => {
  //   setPopup2(true);
  //   toggleDrawerClose();
  // };

  // const handleClickCloseReceipt1 = () => setPopup1(false);
  // const handleClickCloseReceipt2 = () => setPopup2(false);

  const resetPaymentData = () => {
    setCreateOrderStatus('idle');
    setOrderResponse(null);
  };

  useEffect(() => {
    if (readyToSubmit && createOrderStatus === 'success' && orderResponse !== null && paymentFormHandle.current) {
      paymentFormHandle.current.submitForm();
      setReadyToSubmit(false);
    }
  }, [createOrderStatus, orderResponse, readyToSubmit]);

  useEffect(() => {
    return () => {
      resetPaymentData();
    };
  }, []);

  const handlePaymentClick = useCallback(async () => {
    console.log('Here');
    setCreateOrderStatus('loading');

    // TODO: Replace this with actual implementation

    const testId = nanoid();
    const orderRequest: PaymentOrderRequest = {
      academicid: 10,
      sectionid: 1,
      classid: 1,
      studentid: 10,
      feetypeid: 1,
      devicetype: 'Web',
      totalamount: 10,
      payableamount: 10,
      items: [
        {
          feeid: 1,
          termid: 1,
          totalamount: 5,
          scholarship: 0,
          discount: 0,
          fine: 0,
          payableamount: 5,
        },
        {
          feeid: 2,
          termid: 1,
          totalamount: 5,
          scholarship: 0,
          discount: 0,
          fine: 0,
          payableamount: 5,
        },
      ],
      user_agent: window.navigator.userAgent,
      browser_tz: new Date().getTimezoneOffset().toString(),
      browser_color_depth: window.screen.colorDepth.toString(),
      browser_java_enabled: 'false',
      browser_screen_height: window.screen.height.toString(),
      browser_screen_width: window.screen.width.toString(),
      browser_language: window.navigator.language,
      additional_info1: `Fee payment Test - ${testId} `,
      additional_info2: `Academic fee paying - ${testId}`,
    };

    const createOrderRespose = await api.Payment.createOrder(orderRequest);
    if (createOrderRespose.status === 'success') {
      console.log(createOrderRespose);
      setOrderResponse(createOrderRespose.data);
      setCreateOrderStatus('success');
      setReadyToSubmit(true);
    }
  }, []);

  const FeeData = [
    {
      feeName: 'Tuition Fee',
      Amount: 500,
    },
    {
      feeName: 'Tuition Fee',
      Amount: 500,
    },
    {
      feeName: 'Admission Fee',
      Amount: 3000,
    },
    {
      feeName: 'Lab Fee',
      Amount: 400,
    },
    {
      feeName: 'Bus Fee',
      Amount: 1200,
    },
    {
      feeName: 'Uniform Fee',
      Amount: 2500,
    },
    {
      feeName: 'School Shoe Fee',
      Amount: 800,
    },
  ];

  return (
    <Page title="Pay Fee">
      <PayFeeRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Typography variant="h6" fontSize={18}>
            Pay Fee
          </Typography>
          <Divider />
          {/* <Grid pb={4} pt={2} container spacing={3}>
            <Grid item lg={3} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Academic Year
              </Typography>
              <Autocomplete
                options={status}
                renderInput={(params) => <TextField {...params} placeholder="Select Year" />}
              />
            </Grid>
            <Grid item lg={3} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Class
              </Typography>
              <Autocomplete
                options={status}
                renderInput={(params) => <TextField {...params} placeholder="Select Class" />}
              />
            </Grid>
            <Grid item lg={3} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Student
              </Typography>
              <Autocomplete
                options={status}
                renderInput={(params) => <TextField {...params} placeholder="Select Student" />}
              />
            </Grid>
            <Grid item lg={3} xs={12}>
              <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                <Button variant="contained" color="secondary" fullWidth>
                  Reset
                </Button>
                <Button variant="contained" color="primary" fullWidth>
                  Show
                </Button>
              </Stack>
            </Grid>
          </Grid> */}

          <Grid container spacing={10}>
            <Grid item lg={6} xs={12}>
              <Typography variant="subtitle1" mt={2} fontSize={14}>
                Select Term
              </Typography>
              <Autocomplete
                options={['Term 1', 'Term 2', 'Term 3']}
                renderInput={(params) => <TextField sx={{ mb: 2, pr: 1 }} {...params} placeholder="Select" />}
              />
              <Box sx={{ boxShadow: 0 }}>
                <TableContainer
                  sx={{
                    height: 'calc(100vh - 350px)',
                    overflow: 'auto',
                    // px: { xs: 0.5, md: 2 },
                  }}
                >
                  <Table stickyHeader aria-label="simple table">
                    <TableBody>
                      {FeeData.map((row, index) => (
                        <TableRow key={index} sx={{ height: '50px', boxShadow: 0 }}>
                          <Card
                            className="fee-card"
                            sx={{
                              border: '0.5px solid rgba(0, 0, 0, 0.12)',
                              mb: 2,
                              p: 1,
                              boxShadow: 0,
                              display: 'flex',
                              justifyContent: 'space-between',
                            }}
                          >
                            <Stack direction="row" alignItems="center" gap={1}>
                              <Checkbox
                                icon={<RadioButtonUncheckedIcon />}
                                checkedIcon={<SuccessIcon />}
                                size="small"
                                color="success"
                              />
                              <Typography variant="subtitle1" color="secondary" fontSize={13}>
                                {row.feeName}
                              </Typography>
                            </Stack>
                            <Typography
                              fontSize={14}
                              fontWeight={600}
                              sx={{ pr: 1, display: 'flex', alignItems: 'center' }}
                            >
                              ₹{row.Amount}
                            </Typography>
                          </Card>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Box>
            </Grid>
            <Grid item lg={6} xs={12} sx={{ display: { sm: 'block', xs: 'none' } }}>
              <Card sx={{ boxShadow: 0, mt: 5 }} className="TableCard">
                <Stack direction="row" justifyContent="space-between" px={5} my={3}>
                  <Typography variant="h6" fontSize={16}>
                    Payment Details
                  </Typography>
                  <Typography variant="h6" fontSize={16}>
                    Amount(INR)
                  </Typography>
                </Stack>
                <TableContainer
                  sx={{
                    px: { xs: 0.5, md: 2 },
                  }}
                >
                  <Table stickyHeader aria-label="simple table">
                    <TableBody>
                      {[1, 2, 3, 4, 5].map((row) => (
                        <TableRow key={row} sx={{ height: '50px' }}>
                          <TableCell sx={{ fontWeight: 600 }}> Tution Fee</TableCell>
                          <TableCell align="right">₹ 500</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
                <Box
                  sx={{ borderTop: '2px dashed gray', mx: 2 }}
                  pl={3}
                  py={3}
                  display="flex"
                  justifyContent="space-between"
                >
                  <Typography variant="body2" fontWeight={600} color="secondary">
                    Grand Total
                  </Typography>
                  <Typography variant="body2" fontWeight={600}>
                    ₹ 200
                  </Typography>
                </Box>
                <Button
                  // onClick={toggleDrawerOpen}
                  variant="contained"
                  color="success"
                  sx={{ width: '90%', mx: '5%', mb: 5 }}
                  onClick={handlePaymentClick}
                >
                  Pay Now
                </Button>
              </Card>
            </Grid>
          </Grid>
          {/* <Box
            sx={{ visibility: { xs: 'visible', sm: 'hidden' } }}
            pt={2}
            display="flex"
            justifyContent="space-between"
          >
            <Typography variant="body2" fontWeight={600} color="secondary">
              Total
            </Typography>
            <Typography variant="body2" fontWeight={600}>
              ₹ 2000
            </Typography>
          </Box> */}
          <Button
            fullWidth
            // onClick={toggleDrawerOpen}
            onClick={handlePaymentClick}
            variant="contained"
            color="success"
            sx={{ display: { xs: 'block', sm: 'none' }, mt: 2 }}
          >
            Pay Now
          </Button>
        </Card>

        {createOrderStatus === 'success' && orderResponse !== null && (
          <PaymentLaunchForm ref={paymentFormHandle} orderResponse={orderResponse} />
        )}
      </PayFeeRoot>
      {/* <TemporaryDrawer
        onClose={toggleDrawerClose}
        state={drawerOpen}
        DrawerContent={<Pay handleClickReceipt1={handleClickReceipt1} handleClickReceipt2={handleClickReceipt2} />}
      />
      <Popup size="md" state={popup1} onClose={handleClickCloseReceipt1} popupContent={<GetReceipt />} />
      <Popup size="md" state={popup2} onClose={handleClickCloseReceipt2} popupContent={<DetailedReceipt />} /> */}
    </Page>
  );
}

export default PayFee;
