import api from '@/api';
import {
  ClassCreateRequest,
  ClassCreateRow,
  ClassListInfo,
  ClassListPagedData,
  ClassListRequest,
  ClassSortListInfo,
  ClassSortListPagedData,
  ClassSortListRequest,
} from '@/types/AcademicManagement';
import { CreateResponse, CreateResponseMulti, DeleteResponse, UpdateResponse } from '@/types/Common';
import { createAsyncThunk } from '@reduxjs/toolkit';

export const fetchClassList = createAsyncThunk<ClassListPagedData, ClassListRequest, { rejectValue: string }>(
  'classManagement/list',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.ClassManagement.GetClassList(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in fetching class List');
    }
  }
);
export const fetchClassLists = createAsyncThunk<any, any, { rejectValue: string }>(
  'classManagement/lists',
  async (adminId, { rejectWithValue }) => {
    try {
      const response = await api.ClassManagement.ClassList(adminId);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in fetching class List');
    }
  }
);

export const addNewClass = createAsyncThunk<CreateResponse, ClassCreateRequest, { rejectValue: string }>(
  'classManagement/addnew',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.ClassManagement.AddNewClass(request);

      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in adding new class');
    }
  }
);

export const addNewClassMulti = createAsyncThunk<CreateResponseMulti, ClassCreateRow[], { rejectValue: string }>(
  'classManagement/addnewmulti',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.ClassManagement.AddNewClasses(request);

      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in adding new classes');
    }
  }
);

export const updateClass = createAsyncThunk<UpdateResponse, ClassListInfo, { rejectValue: string }>(
  'classManagement/update',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.ClassManagement.UpdateClass(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in updating class');
    }
  }
);

export const deleteClass = createAsyncThunk<DeleteResponse, number, { rejectValue: string }>(
  'classManagement/delete',
  async (classId, { rejectWithValue }) => {
    try {
      const response = await api.ClassManagement.DeleteClass(classId);

      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in deleting class');
    }
  }
);

export const fetchClassSortList = createAsyncThunk<
  ClassSortListPagedData,
  ClassSortListRequest,
  { rejectValue: string }
>('classManagement/classsortlist', async (request, { rejectWithValue }) => {
  try {
    const response = await api.ClassManagement.GetClassSortList(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching class sort List');
  }
});

export const updateClassSort = createAsyncThunk<UpdateResponse, ClassSortListInfo, { rejectValue: string }>(
  'classManagement/classsortupdate',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.ClassManagement.UpdateClassSort(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in updating class sort');
    }
  }
);
