import type { SortDirection } from '@mui/material';

export interface PageRequest {
  pageNumber: number;
  pageSize: number;
  sortColumn?: string | null;
  sortDirection?: SortDirection;
}

export interface PageRequestWithFilters<F> extends PageRequest {
  filters?: F;
}

export type PagedData<T> = {
  data: T[];
  totalrecords: number;
  pagesize: number;
  pagenumber: number;
  totalpages: number;
  remainingpages: number;
};

export type PaginationInfo = {
  totalrecords: number;
  pagesize: number;
  pagenumber: number;
  totalpages: number;
  remainingpages: number;
};
