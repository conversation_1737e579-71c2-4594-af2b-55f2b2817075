/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  Collapse,
  FormControl,
  Avatar,
} from '@mui/material';
import { MdAdd } from 'react-icons/md';
import styled from 'styled-components';
import SearchIcon from '@mui/icons-material/Search';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import DeleteIcon from '@mui/icons-material/Delete';
import Excel from '@/assets/attendance/Excel.svg';
import Pdf from '@/assets/attendance/PDF.svg';
import { CLASS_SELECT, STATUS_SELECT, YEAR_SELECT } from '@/config/Selection';
import Popup from '@/components/shared/Popup/Popup';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { togglePopup } from '@/store/Layout/popup.slice';

const StudentBlockListRoot = styled.div`
  padding: 16px;

  .Card {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    height: calc(100vh - 10rem);
    @media screen and (max-width: 62.25rem) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 2.5rem);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 0.0625rem solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

export const data = [
  {
    slNo: 1,
    class: 'VII-D',
    guardianName: 'Peter Alex',
    studentName: 'Alex',
    guardianNumber: '+91-8793734784',
  },
  {
    slNo: 2,
    class: 'VII-D',
    guardianName: 'Peter Alex',
    studentName: 'Mathew',
    guardianNumber: '+91-8793734784',
  },
  {
    slNo: 3,
    class: 'VII-D',
    guardianName: 'Peter Alex',
    studentName: 'Peter',
    guardianNumber: '+91-8793734784',
  },
  {
    slNo: 4,
    class: 'VII-D',
    guardianName: 'Peter Alex',
    studentName: 'Alex Mic',
    guardianNumber: '+91-8793734784',
  },
  {
    slNo: 5,
    class: 'VII-D',
    guardianName: 'Peter Alex',
    studentName: 'john',
    guardianNumber: '+91-8793734784',
  },
  {
    slNo: 6,
    class: 'VII-D',
    guardianName: 'Peter Alex',
    studentName: 'Micheal',
    guardianNumber: '+91-8793734784',
  },
  {
    slNo: 7,
    class: 'VII-D',
    guardianName: 'Peter Alex',
    studentName: 'jack',
    guardianNumber: '+91-8793734784',
  },
];

function StudentBlockList() {
  const [showFilter, setShowFilter] = useState(false);

  const getRowKey = useCallback((row: any) => row.examId, []);
  const AuthorizeKeywordsColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'slNo',
        dataKey: 'slNo',
        headerLabel: 'Sl.No',
        sortable: true,
      },
      {
        name: 'studentName',
        headerLabel: 'Student Name',
        renderCell: (row) => {
          return (
            <Stack direction="row" gap={1} alignItems="center">
              <Avatar alt="" src="/static/images/avatar/1.jpg" />
              <Typography variant="subtitle2">{row.studentName}</Typography>
            </Stack>
          );
        },
      },
      {
        name: 'class',
        dataKey: 'class',
        headerLabel: 'Class',
      },
      {
        name: 'guardianName',
        dataKey: 'guardianName',
        headerLabel: 'Guardian Name',
      },

      {
        name: 'guardianNumber',
        dataKey: 'guardianNumber',
        headerLabel: 'Guardian Number',
      },
    ],
    []
  );

  return (
    <Page title="Student Block List">
      <StudentBlockListRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17} width="80%">
              Student Block List
            </Typography>
            <Box pb={1} sx={{ flexShrink: 0 }}>
              <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                <SearchIcon />
              </IconButton>
            </Box>
          </Stack>
          <Divider />

          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={5} pt={1} container spacing={2}>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Autocomplete
                        options={YEAR_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select year" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Autocomplete
                        options={CLASS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select class" />}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item lg={1} xs={12}>
                    <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                      <Button variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                      {/* <Button variant="contained" color="primary" fullWidth>
                          Search
                        </Button> */}
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Paper className="card-table-container" sx={{ marginTop: '.75rem' }}>
              <DataTable
                columns={AuthorizeKeywordsColumns}
                data={data}
                getRowKey={getRowKey}
                fetchStatus="success"
                allowPagination
                PaginationProps={[]}
              />
            </Paper>
          </div>
          <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 2 }}>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="error" sx={{ gap: 1 }} size="medium">
                <img className="icon" src={Pdf} />{' '}
                <Typography color="white" fontSize={12}>
                  Download
                </Typography>
              </Button>
            </Stack>
          </Box>
        </Card>
      </StudentBlockListRoot>
    </Page>
  );
}

export default StudentBlockList;
