/* eslint-disable no-else-return */
/* eslint-disable no-nested-ternary */
import { breakPointsMinwidth } from '@/config/breakpoints';
import styled from 'styled-components';
import Page from '@/components/shared/Page';
import React, { ReactElement, useCallback, useEffect, useRef, useState } from 'react';
import successIcon from '@/assets/MessageIcons/success.json';
import uploadFiles from '@/assets/NotificationIcons/upload.gif';
import { v4 as uuidv4 } from 'uuid';

import {
  FormControlLabel,
  Grid,
  Stack,
  useTheme,
  TextField,
  Select,
  MenuItem,
  Button,
  Typography,
  Box,
  FormControl,
  Tabs,
  Tab,
  AppBar,
  Checkbox,
  Snackbar,
  Alert,
} from '@mui/material';
import DatePickers from '@/components/shared/Selections/DatePicker';
import Success from '@/assets/Registration/success.gif';
import StThereseLogo from '@/assets/SchoolLogos/StThereseLogo.png';
import Fail from '@/assets/Registration/fail.gif';
import Loading from '@/assets/Registration/loading.gif';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { ErrorIcon } from '@/theme/overrides/CustomIcons';
import axios from 'axios';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import { ErrorMessage } from '@/components/shared/Popup/ErrorMessage';
import LoadingPopup from '@/components/shared/Popup/LoadingPopup';
import { LoadingMessage } from '@/components/shared/Popup/LoadingMessage';
import dayjs, { Dayjs } from 'dayjs';
import CheckIcon from '@mui/icons-material/Check';
import { SlUser, SlUserFemale } from 'react-icons/sl';
import { ArrowBack, ArrowForward, CheckCircle } from '@mui/icons-material';
import ImageCropper from '@/components/shared/ImageUploadWithCropper';
import InputFileUpload from '@/components/shared/Selections/FilesUploadTextField';
import Lottie from 'lottie-react';
import LoadingButton from '@mui/lab/LoadingButton';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import { Link, useNavigate } from 'react-router-dom';
import { AdmissionFormTypes } from '@/features/AdmissinFormPrint';
import Popup from '@/components/shared/Popup/Popup';
import typography from '@/theme/card';

const AdmissionFormRoot = styled.div`
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.grey[900]};
  /* min-height: calc(100vh - 160px); */
  /* .MuiOutlinedInput-root {
    border-radius: '10px'; // No border radius
  } */
  /* padding: 1rem; */

  .Card {
    min-height: calc(100vh - 35px);
  }
  /* @media ${breakPointsMinwidth.lg} {
    padding: 2rem;
  } */
`;

const DummyContent =
  ' Lorem Ipsum is simply dummy text of the printing and typesetting industry has been the industry&apos;s standard dummy text ever a type specimen book.';

type ClassSectionArray = {
  sectionId: number;
  sectionName: string;
  DobRangeFrom: string;
  DobRangeTo: string;
  availableSeat: number;
};

export type FileObjTypes = {
  id: number;
  name: string;
  type: string;
  imageUrl?: string;
  originalFile: File;
  fieldName?: string;
};

interface UploadPropsTypes {
  uploadedFile: FileObjTypes[]; // Ensure this expects an array
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = ({ children, value, index }: TabPanelProps) => {
  return value === index && <Box>{children}</Box>;
};

function AdmissionForm() {
  const theme = useTheme();
  const navigate = useNavigate();
  const { confirm } = useConfirm();
  //   const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [expanded, setExpanded] = React.useState(false);
  const maxDate = dayjs(new Date());
  const [activeTab, setActiveTab] = useState(0);
  const [previousTab, setPreviousTab] = useState<number | null>(null);
  const [studentDOB, setStudentDOB] = React.useState<Dayjs | string>('');
  const [studentAadharNo, setStudentAadharNo] = React.useState<'yes' | 'no' | ''>('');
  const [fatherDOB, setFatherDOB] = React.useState<Dayjs | string>('');
  const [motherDOB, setMotherDOB] = React.useState<Dayjs | string>('');
  const [guardianDOB, setGuardianDOB] = React.useState<Dayjs | string>('');
  const [vaccinationDate1, setVaccinationDate1] = React.useState<Dayjs | string>('');
  const [vaccinationDate2, setVaccinationDate2] = React.useState<Dayjs | string>('');
  const [vaccinationDate3, setVaccinationDate3] = React.useState<Dayjs | string>('');
  const [cellValidationError, setCellValidationError] = useState(false);
  const [classSections, setClassSections] = useState<ClassSectionArray[]>([]);
  const [dobRangeFrom, setDobRangeFrom] = useState<string>('');
  const [dobRangeTo, setDobRangeTo] = useState<string>('');
  const [uploaded, setUploaded] = useState<FileObjTypes[]>([]);
  const [uploadedFile, setUploadedFile] = useState<FileObjTypes | undefined | string>('');
  const [isDialogOpen, setIsDialogOpen] = useState<boolean>(false);
  const [availableSeat, setAvailableSeat] = useState<number>(0);
  const [availableSeatError, setAvailableSeatError] = useState(false);
  const tabPanelRef = useRef(null);

  const scrollToTabPanel = () => {
    if (tabPanelRef.current) {
      tabPanelRef.current.scrollIntoView({ top: 100, behavior: 'smooth' });
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    // If we're switching tabs, update previousTab only if it's not the same as activeTab
    if (newValue !== activeTab) {
      setPreviousTab(activeTab); // Store the currently active tab as the previous tab
    }
    setActiveTab(newValue); // Set the new active tab
  };
  const tabLabels = [
    { id: 1, label: 'Details of Candidate : Step 1' },
    { id: 2, label: 'About Father : Step 2' },
    { id: 3, label: 'About Mother : Step 3' },
    { id: 4, label: 'About Guardian : Step 4' },
    { id: 5, label: 'Brother/Sister in this School : Step 5' },
    { id: 6, label: 'Documents : Step 6' },
  ];

  const handleExpandClick = () => {
    setExpanded(!expanded);
  };

  const [submitting, setSubmitting] = useState(false);

  const showConfirmation = useCallback(
    async (successMessage: ReactElement, title: string) => {
      const confirmation = confirm(successMessage, title, {
        okLabel: 'Ok',
        showOnlyOk: true,
      });

      return confirmation;
    },
    [confirm]
  );
  // const onSave =

  useEffect(() => {
    console.log('dobRangeFrom::::----', dayjs(dobRangeFrom).format('MM/DD/YYYY'));
    console.log('SDob::::----', dayjs(studentDOB).format('DD/MM/YYYY'));
    const fetchData = async () => {
      try {
        const response = await axios.get(
          'https://thereseregapi.pasdaily.in/ElixirApi/StudentSet/TakeSectionList?SchoolId=1'
        );

        // Extract SectionNamen and add a random ID to each object
        const sectionNames = response.data.map((item: any) => ({
          sectionId: item.SectionId, // Generate random ID
          sectionName: item.SectionName, // Keep SectionName
          DobRangeFrom: item.DobRangeFrom,
          DobRangeTo: item.DobRangeTo,
          availableSeat: item.AvailableSeat,
        }));

        setClassSections(sectionNames);
        console.log('Section Names:', sectionNames);
        console.log('response data Section Names:', response.data);
      } catch (error) {
        console.error('Error fetching data:', error);
      }
    };

    fetchData();
  }, [dobRangeFrom, studentDOB]);

  const CreateEditMessageTempValidationSchema = Yup.object({
    Syllabus: Yup.string().required('Kindly select the Syllabus.'),
    ClassName: Yup.string().required('Kindly select the Class.'),
    Surname: Yup.string().required('Kindly enter the Surname.'),
    StudentName: Yup.string().required('Kindly enter the Student Name.'),
    FathersName: Yup.string().required('Kindly enter the Father’s Name.'),
    SGender: Yup.string().oneOf(['0', '1'], 'Kindly select a valid Gender.').required('Kindly select the Gender.'),
    SDob: Yup.string().required('Kindly enter the Date of Birth.'),
    SdobInWords: Yup.string().required('Kindly enter the Date of Birth in words.'),
    // MobileNumber: Yup.string().required('Kindly enter the Mobile Number.'),
    SPlaceOfBorth: Yup.string().required('Kindly enter the Place of Birth.'),
    SMotherTongue: Yup.string().required('Kindly enter the Mother Tongue.'),
    SReligion: Yup.string().required('Kindly enter the Religion.'),
    SCaste: Yup.string().required('Kindly enter the Caste.'),
    SNationality: Yup.string().required('Kindly enter the Nationality.'),
    SchoolLastAttended: Yup.string().required('Kindly enter the Last School Attended.'),
    SAddress1: Yup.string().required('Kindly enter Address Line 1.'),
    SAddress2: Yup.string().required('Kindly enter Address Line 2.'),
    SAddress3: Yup.string().required('Kindly enter Address Line 3.'),
    StudentBloodGroup: Yup.string().required('Kindly enter the Student’s Blood Group.'),
    // SAadharNo: Yup.string().required('Kindly enter the Student’s Aadhar Number.'),
    FatherName: Yup.string().required('Kindly enter the Father’s Name.'),
    FMotherTongue: Yup.string().required('Kindly enter the Father’s Mother Tongue.'),
    FDob: Yup.string().required('Kindly enter the Father’s Date of Birth.'),
    FEmail: Yup.string().email('Kindly enter a valid Email Address.').required('Kindly enter the Father’s Email.'),
    FReligion: Yup.string().required('Kindly enter the Father’s Religion.'),
    FCaste: Yup.string().required('Kindly enter the Father’s Caste.'),
    FQualification: Yup.string().required('Kindly enter the Father’s Qualification.'),
    // FOccupation: Yup.string().required('Kindly enter the Father’s Occupation.'),
    FNameOfCompany: Yup.string().required('Kindly enter the Father’s Company Name.'),
    // FCompanyYear: Yup.string().required('Kindly enter the Number of Years in Company.'),
    // FCompanyMonth: Yup.st  ring().required('Kindly enter the Number of Months in Company.'),
    // FOfficeAddress: Yup.string().required('Kindly enter the Father’s Office Address.'),
    FOfficeMobile: Yup.string()
      .required('Kindly enter the Father’s Office Mobile Number.')
      .matches(/^\d{10}$/, 'Mobile number must be exactly 10 digits'),
    // .test('no-duplicate', 'Don’t repeat the same number.', function (value) {
    //   return value !== this.resolve(Yup.ref('MOfficeMobile'));
    // }),
    MotherName: Yup.string().required('Kindly enter the Mother’s Name.'),
    MMotherTongue: Yup.string().required('Kindly enter the Mother’s Mother Tongue.'),
    FAadharNo: Yup.string().required('Kindly enter the Father’s Aadhar Number.'),
    MDob: Yup.string().required('Kindly enter the Mother’s Date of Birth.'),
    // MEmail: Yup.string().email('Kindly enter a valid Email Address.').required('Kindly enter the Mother’s Email.'),
    MReligion: Yup.string().required('Kindly enter the Mother’s Religion.'),
    MCaste: Yup.string().required('Kindly enter the Mother’s Caste.'),
    MQualification: Yup.string().required('Kindly enter the Mother’s Qualification.'),
    // MNameOfCompany: Yup.string().required('Kindly enter the Mother’s Company Name.'),
    // MCompanyYear: Yup.string().required('Kindly enter the Number of Years in Company.'),
    // MCompanyMonth: Yup.string().required('Kindly enter the Number of Months in Company.'),
    // MOfficeAddress: Yup.string().required('Kindly enter the Mother’s Office Address.'),
    MOfficeMobile: Yup.string()
      .required('Kindly enter the Mother’s Office Mobile Number.')
      .matches(/^\d{10}$/, 'Mobile number must be exactly 10 digits'),
    // .test('no-duplicate', 'Don’t repeat the same number.', function (value) {
    //   return value !== this.resolve(Yup.ref('FOfficeMobile'));
    // }),
    MAadharNo: Yup.string().required('Kindly enter the Mother’s Aadhar Number.'),

    // File Upload Validations (Only Images and PDFs)
    passportPhoto: Yup.mixed().required('Passport Photo is required.'),
    // .test('fileType', 'Only image or PDF files are allowed.', (value) =>
    //   value && value.type ? /image\/.*|application\/pdf/.test(value.type) : false
    // ),

    birthCertificate: Yup.mixed().required('Birth Certificate is required.'),
    // .test('fileType', 'Only image or PDF files are allowed.', (value) =>
    //   value && value.type ? /image\/.*|application\/pdf/.test(value.type) : false
    // ),

    // studentAadhar: Yup.mixed().required('Student Aadhar is required.'),
    // .test('fileType', 'Only image or PDF files are allowed.', (value) =>
    //   value && value.type ? /image\/.*|application\/pdf/.test(value.type) : false
    // ),

    parentAadhar: Yup.mixed().required('Parent Aadhar is required.'),
    // .test('fileType', 'Only image or PDF files are allowed.', (value) =>
    //   value && value.type ? /image\/.*|application\/pdf/.test(value.type) : false
    // ),

    proof: Yup.mixed().required('Proof Document is required.'),
    firstSemesterMarkSheet: Yup.mixed().required('Mark Sheet Document is required.'), 
    // .test('fileType', 'Only image or PDF files are allowed.', (value) =>
    //   value && value.type ? /image\/.*|application\/pdf/.test(value.type) : false
    // ),

    // cast: Yup.mixed().required('Caste Certificate is required.'),
    // .test('fileType', 'Only image or PDF files are allowed.', (value) =>
    //   value && value.type ? /image\/.*|application\/pdf/.test(value.type) : false
    // ),
  });

  const {
    values: {
      Syllabus,
      ClassName,
      Surname,
      StudentName,
      FathersName,
      SGender,
      SDob,
      SdobInWords,
      SPlaceOfBorth,
      SMotherTongue,
      SReligion,
      SCaste,
      SNationality,
      SchoolLastAttended,
      SAddress1,
      SAddress2,
      SAddress3,
      StudentBloodGroup,
      SAadharNo,
      FatherName,
      FMotherTongue,
      FDob,
      FEmail,
      FReligion,
      FCaste,
      FQualification,
      FOccupation,
      FNameOfCompany,
      FCompanyYear,
      FCompanyMonth,
      FOfficeAddress,
      FOfficeMobile,
      FOfficeTelephone,
      FAadharNo,
      MotherName,
      MMotherTongue,
      MDob,
      MEmail,
      MReligion,
      MCaste,
      MQualification,
      MOccupation,
      MNameOfCompany,
      MCompanyYear,
      MCompanyMonth,
      MOfficeAddress,
      MOfficeMobile,
      MOfficeTelephone,
      MAadharNo,
      GuardianName,
      GMotherTongue,
      GDob,
      GEmail,
      GReligion,
      GCaste,
      GQualification,
      GOccupation,
      GNameOfCompany,
      GCompanyYear,
      GCompanyMonth,
      GOfficeAddress,
      GOfficeMobile,
      GOfficeTelephone,
      GAadharNo,
      Sibling1,
      Sibling1Std,
      Sibiling2,
      Sibling2Std,
      Sibling3,
      Sibling3Std,
      SchoolTransport,
      StopName,
      Vaccination1,
      Vaccination1Date,
      Vaccination2,
      Vaccination2Date,
      Vaccination3,
      Vaccination3Date,
      passportPhoto,
      birthCertificate, // Store birth certificate image
      studentAadhar, // Store student's Aadhaar
      parentAadhar, // Store parent's Aadhaar
      proof,
      cast,
      firstSemesterMarkSheet,
    },
    handleChange,
    setFieldValue,
    handleBlur,
    handleSubmit,
    touched,
    errors,
  } = useFormik<AdmissionFormTypes>({
    initialValues: {
      Syllabus: 1,
      ClassName: '',
      Surname: '',
      StudentName: '',
      FathersName: '',
      SGender: -1,
      SDob: '',
      SdobInWords: '',
      SPlaceOfBorth: '',
      SMotherTongue: '',
      SReligion: '',
      SCaste: '',
      SNationality: '',
      SchoolLastAttended: '',
      SAddress1: '',
      SAddress2: '',
      SAddress3: '',
      StudentBloodGroup: '',
      SAadharNo: '',
      FatherName: '',
      FMotherTongue: '',
      FDob: '',
      FEmail: '',
      FReligion: '',
      FCaste: '',
      FQualification: '',
      FOccupation: '',
      FNameOfCompany: '',
      FCompanyYear: '',
      FCompanyMonth: '',
      FOfficeAddress: '',
      FOfficeMobile: '',
      FOfficeTelephone: '',
      FAadharNo: '',
      MotherName: '',
      MMotherTongue: '',
      MDob: '',
      MEmail: '',
      MReligion: '',
      MCaste: '',
      MQualification: '',
      MOccupation: '',
      MNameOfCompany: '',
      MCompanyYear: '',
      MCompanyMonth: '',
      MOfficeAddress: '',
      MOfficeTelephone: '',
      MOfficeMobile: '',
      MAadharNo: '',
      GuardianName: '',
      GMotherTongue: '',
      GDob: '',
      GReligion: '',
      GQualification: ' ',
      GOccupation: '',
      GNameOfCompany: '',
      GCompanyYear: '',
      GCompanyMonth: '',
      GOfficeAddress: '',
      GOfficeTelephone: '',
      GOfficeMobile: '',
      GEmail: '',
      GCaste: '',
      GAadharNo: '',
      Sibling1: '',
      Sibling1Std: '',
      Sibiling2: '',
      Sibling2Std: '',
      Sibling3: '',
      Sibling3Std: '',
      SchoolTransport: -1,
      StopName: '',
      Vaccination1: '',
      Vaccination1Date: '',
      Vaccination2: '',
      Vaccination2Date: '',
      Vaccination3: '',
      Vaccination3Date: '',
      passportPhoto: '',
      birthCertificate: '', // Store birth certificate image
      studentAadhar: '', // Store student's Aadhaar
      parentAadhar: '', // Store parent's Aadhaar
      proof: '',
      cast: '',
      firstSemesterMarkSheet: '',
    },
    validationSchema: CreateEditMessageTempValidationSchema,
    onSubmit: async (values) => {
      console.error('values::::', values);
      setActiveTab(4);
      try {
        // Construct the value object including the Dob field
        const value = {
          ...values,
          // SGender: selectedGender,
          // SchoolTransport: selectedTransport,
          SDob: studentDOB ? dayjs(studentDOB).format('DD/MM/YYYY') : '', // Format the date before assigning
          FDob: fatherDOB ? dayjs(fatherDOB).format('DD/MM/YYYY') : '', // Format the date before assigning
          MDob: motherDOB ? dayjs(motherDOB).format('DD/MM/YYYY') : '', // Format the date before assigning
          GDob: guardianDOB ? dayjs(guardianDOB).format('DD/MM/YYYY') : '', // Format the date before assigning
          Vaccination1Date: vaccinationDate1 ? dayjs(vaccinationDate1).format('DD/MM/YYYY') : '', // Format the date before assigning
          Vaccination2Date: vaccinationDate2 ? dayjs(vaccinationDate2).format('DD/MM/YYYY') : '', // Format the date before assigning
          Vaccination3Date: vaccinationDate3 ? dayjs(vaccinationDate3).format('DD/MM/YYYY') : '', // Format the date before assigning
          FileDetails: [
            {
              FileTitle: 'Profile Photo',
              FileName: passportPhoto,
            },
            {
              FileTitle: 'Student Birth Certificate',
              FileName: birthCertificate,
            },
            {
              FileTitle: 'Student Adhaar Card',
              FileName: studentAadhar,
            },
            {
              FileTitle: 'Parent Adhaar Card',
              FileName: parentAadhar,
            },
            {
              FileTitle: 'Address Proof',
              FileName: proof,
            },
            {
              FileTitle: 'Cast Certificate',
              FileName: cast,
            },
            {
              FileTitle: 'First Semester Mark Sheet',
              FileName: firstSemesterMarkSheet,
            },
          ],
        };

        setSubmitting(true);
        console.log('values::::', value);
        const response = await axios.post(
          'https://thereseregapi.pasdaily.in/ElixirApi/StudentSet/OnlineAdmissionTherese',
          value
        );
        console.log('response.data::::', response.data);
        const successMessages = response.data.RESULT === 'SUCCESS';
        const errorMessages = response.data.RESULT === 'FAILED';
        const existMessages = response.data.RESULT === 'EXIST';
        // const POPUPMESSAGE = ;
        setSubmitting(false);
        if (successMessages) {
          await showConfirmation(<SuccessMessage icon={Success} message={response.data.POPUPMESSAGE} />, '');
          navigate('/print-admission-form', { state: { details: response.data.DETAILS } });
        } else if (existMessages) {
          await showConfirmation(<ErrorMessage icon={Fail} message={response.data.POPUPMESSAGE} />, '');
        } else {
          await showConfirmation(<ErrorMessage icon={Fail} message={response.data.POPUPMESSAGE} />, '');
        }
      } catch (error) {
        setSubmitting(false);
        await showConfirmation(
          <ErrorMessage icon={Fail} message="Something went wrong with the registration. Please try again later." />,
          ''
        );
        console.error('Error submitting form:', error);
      } finally {
        setSubmitting(false);
      }
    },
    validateOnBlur: false,
    // validate: (messageVals) => {
    //   const errorObj: any = {};
    //   messageVals.messageContent.forEach(async (classRow, rowIndex, arr) => {
    //     if (arr.some((x, i) => classRow.Class !== '' && x.Class === classRow.Class && i !== rowIndex)) {
    //       if (!errorObj.classes) {
    //         errorObj.classes = [];
    //       }
    //       errorObj.classes[rowIndex] = {};
    //       errorObj.classes[rowIndex].Class = 'Duplicate class name';
    //     }
    //   });
    //   return errorObj;
    // },
  });

  useEffect(() => {
    console.log('passportPhoto::::----', passportPhoto);
  }, [passportPhoto]);

  const [loadingField, setLoadingField] = useState<string | null>(null);
  const [successIconShow, setSuccessIconShow] = useState<{ [key: string]: string }>({});

  const onUpload = async (fieldName: string) => {
    console.log('fieldName', fieldName);
    setLoadingField(fieldName); // Set loading state for the clicked button

    try {
      console.log('uploaded', uploaded);

      const formData = new FormData();
      uploaded.forEach((file) => {
        if (file.fieldName === fieldName) {
          formData.append('files', file.originalFile);
        }
      });
      console.log('formData::::----', formData);

      const uploadResponse = await axios.post(
        'https://thereseregapi.pasdaily.in/ElixirApi/StudentSet/UploadFiles',
        formData
      );

      const uploadedFilesDetails = uploadResponse.data.DETAILS;
      setFieldValue(fieldName, uploadedFilesDetails); // Store the uploaded file details in formik state
      const uploadedFilesResult = uploadResponse.data.RESULT;
      console.log('uploadedFilesDetails', uploadedFilesDetails);

      // Replace upload button with success state
      if (uploadedFilesResult === 'SUCCESS') {
        console.log('successIconShow::::----', successIconShow);

        setSuccessIconShow((prevState) => ({
          ...prevState,
          [fieldName]: 'Success', // Mark this field as successfully uploaded
        }));
      }
      // **Remove uploaded file from state**
      // setUploaded((prevUploaded) => prevUploaded.filter((file) => file.fieldName !== fieldName));
    } catch (error) {
      console.error('Upload failed', error);
    } finally {
      setLoadingField(null); // Reset loading state after upload completes
    }
  };

  const handleGenderSelect = (gender: number) => {
    setFieldValue('SGender', gender); // Store as string to match Yup validation
    handleBlur({ target: { name: 'SGender' } }); // Trigger validation
  };

  const handleTransportSelect = (transport: number) => {
    setFieldValue('SchoolTransport', transport); // Store as string to match Yup validation
    handleBlur({ target: { name: 'SchoolTransport' } }); // Trigger validation
  };

  const isAnyRequiredFieldEmptyStep1 = [
    Surname,
    Syllabus,
    ClassName,
    StudentName,
    FathersName,
    SGender,
    studentDOB,
    SdobInWords,
    SPlaceOfBorth,
    SMotherTongue,
    SReligion,
    SCaste,
    SNationality,
    SchoolLastAttended,
    SAddress1,
    SAddress2,
    SAddress3,
    StudentBloodGroup,
    SAadharNo,
  ].some((field) => field === '' || field === 'Select' || field === -1); // Check if any required field is empty

  const isAnyRequiredFieldEmptyStep2 = [
    FatherName,
    FMotherTongue,
    fatherDOB,
    FEmail,
    FReligion,
    FCaste,
    FQualification,
    FOccupation,
    FNameOfCompany,
    FCompanyYear,
    FCompanyMonth,
    FOfficeAddress,
    FOfficeTelephone,
    FOfficeMobile,
    FAadharNo,
  ].some((field) => field === '' || field === 'Select' || field === -1); // Check if any required field is empty

  const isAnyRequiredFieldEmptyStep3 = [
    MotherName,
    MMotherTongue,
    motherDOB,
    MEmail,
    MReligion,
    MCaste,
    MQualification,
    MOccupation,
    MNameOfCompany,
    MCompanyYear,
    MCompanyMonth,
    MOfficeAddress,
    MOfficeTelephone,
    MOfficeMobile,
    MAadharNo,
  ].some((field) => field === '' || field === 'Select' || field === -1);

  const isAnyRequiredFieldEmptyStep4 = [
    GuardianName,
    GMotherTongue,
    guardianDOB,
    GEmail,
    GReligion,
    GCaste,
    GQualification,
    GOccupation,
    GNameOfCompany,
    GCompanyYear,
    GCompanyMonth,
    GOfficeAddress,
    GOfficeTelephone,
    GOfficeMobile,
    GAadharNo,
  ].some((field) => field === '' || field === 'Select' || field === -1);

  const isAnyRequiredFieldEmptyStep5 = [
    Sibling1,
    Sibling1Std,
    // Sibiling2,
    // Sibling2Std,
    // Sibling3,
    // Sibling3Std,
    SchoolTransport,
    Vaccination1,
    vaccinationDate1,
    // Vaccination2,
    // vaccinationDate2,
    // Vaccination3,
    // vaccinationDate3,
  ].some((field) => field === '' || field === 'Select' || field === -1);

  const isAnyRequiredFieldEmptyStep6 = [
    passportPhoto,
    birthCertificate,
    studentAadhar,
    parentAadhar,
    proof,
    cast,
    firstSemesterMarkSheet,
  ].some((field) => field === '' || field === null);

  const [autoFillGuardian, setAutoFillGuardian] = useState('');

  const handleAutoFillGuardian =
    (guardianType: 'father' | 'mother') => (event: React.ChangeEvent<HTMLInputElement>) => {
      const isChecked = event.target.checked;
      setAutoFillGuardian(isChecked ? guardianType : '');

      if (isChecked) {
        const isFather = guardianType === 'father';
        setGuardianDOB(isFather ? fatherDOB : motherDOB);
        setFieldValue('GuardianName', isFather ? FatherName : MotherName);
        setFieldValue('GMotherTongue', isFather ? FMotherTongue : MMotherTongue);
        setFieldValue('GDob', isFather ? fatherDOB : motherDOB);
        setFieldValue('GEmail', isFather ? FEmail : MEmail);
        setFieldValue('GReligion', isFather ? FReligion : MReligion);
        setFieldValue('GCaste', isFather ? FCaste : MCaste);
        setFieldValue('GQualification', isFather ? FQualification : MQualification);
        setFieldValue('GOccupation', isFather ? FOccupation : MOccupation);
        setFieldValue('GNameOfCompany', isFather ? FNameOfCompany : MNameOfCompany);
        setFieldValue('GCompanyYear', isFather ? FCompanyYear : MCompanyYear);
        setFieldValue('GCompanyMonth', isFather ? FCompanyMonth : MCompanyMonth);
        setFieldValue('GOfficeAddress', isFather ? FOfficeAddress : MOfficeAddress);
        setFieldValue('GOfficeMobile', isFather ? FOfficeMobile : MOfficeMobile);
        setFieldValue('GOfficeTelephone', isFather ? FOfficeTelephone : MOfficeTelephone);
        setFieldValue('GAadharNo', isFather ? FAadharNo : MAadharNo);
      } else {
        setGuardianDOB('');
        setFieldValue('GuardianName', '');
        setFieldValue('GMotherTongue', '');
        setFieldValue('GDob', '');
        setFieldValue('GEmail', '');
        setFieldValue('GReligion', '');
        setFieldValue('GCaste', '');
        setFieldValue('GQualification', '');
        setFieldValue('GOccupation', '');
        setFieldValue('GNameOfCompany', '');
        setFieldValue('GCompanyYear', '');
        setFieldValue('GCompanyMonth', '');
        setFieldValue('GOfficeAddress', '');
        setFieldValue('GOfficeTelephone', '');
        setFieldValue('GOfficeMobile', '');
        setFieldValue('GAadharNo', '');
      }
    };

  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>, fieldName: string) => {
    const fileInput = event.target;
    const file = fileInput.files?.[0];
    if (!file) return;

    // setFieldValue(fieldName, file); // Store file in Formik state

    const randomId = Math.floor(Math.random() * 1_000_000);
    const fileObj: any = {
      id: randomId,
      name: file.name,
      type: file.type,
      originalFile: file,
      fieldName,
    };

    if (file.type.startsWith('image/')) {
      const imgUrl = URL.createObjectURL(file);
      fileObj.imageUrl = imgUrl;

      setUploadedFile(fileObj);
      // setUploaded(() => [fileObj]);
      if (uploadFiles) {
        setIsDialogOpen(true);
      }

      console.log('Uploaded Image:', fileObj);

      // Cleanup object URL to prevent memory leaks
      setTimeout(() => URL.revokeObjectURL(imgUrl), 5000);
    } else if (file.type.startsWith('application/pdf')) {
      // setUploaded(fileObj);
      // setUploaded((prevUploaded) => [fileObj]);
      // setUploaded((prevUploaded) => [...prevUploaded, fileObj]);
      setUploaded((prevUploaded) => [
        ...prevUploaded.filter((f) => f.fieldName !== fieldName), // Remove existing object with same fieldName
        fileObj, // Add new object
      ]);
      console.log('Uploaded PDF:', fileObj);
    }
    // Reset the input field to allow selecting the same file again
    fileInput.value = '';
  };

  // Function to remove a file
  const handleRemoveFile = (fieldName: string) => {
    setUploaded((prevUploaded) => {
      const updatedFiles = prevUploaded.filter((file) => file.fieldName !== fieldName);
      return [...updatedFiles]; // Ensure state updates properly
    });

    setFieldValue(fieldName, ''); // Clear Formik field value
    setSuccessIconShow((prevState) => ({
      ...prevState,
      [fieldName]: '', // Mark this field as successfully uploaded
    }));

    // Reset the file input value
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }

    console.log('Uploaded::::----', uploaded);
  };

  const [showPage, setShowPage] = useState<boolean>(false);

  return showPage ? (
    <Stack height="100%" width="100%" direction="row" alignItems="center" justifyContent="center">
      <Typography variant="h6" textAlign="center" p={2}>
        The application portal will open on March 4th, 2025
      </Typography>
    </Stack>
  ) : (
    <Page title="AdmissionForm">
      <AdmissionFormRoot>
        <Stack direction="row" justifyContent="end" p={2}>
          <Button size="small" color="success" variant="outlined" component={Link} to="/print-admission-form">
            Print Form
          </Button>
        </Stack>

        <Stack alignItems="center" className="container-fluid">
          <img width={100} src={StThereseLogo} alt="StThereseLogo" />
          <Typography
            textAlign="center"
            fontSize={10}
            bgcolor={theme.palette.error.main}
            color={theme.palette.common.white}
            px={1}
            variant="h5"
            fontWeight={600}
          >
            ST. THERESE CONVENT SCHOOL
          </Typography>
          <Typography
            textAlign="center"
            fontSize={10}
            color={theme.palette.error.main}
            fontWeight={600}
            mt={1}
            variant="h5"
          >
            DOMBIVLI
          </Typography>
          <Typography
            textAlign="center"
            fontWeight={600}
            mt={1}
            variant="h4"
            fontSize={{ xs: '20px', sm: '28px', md: '2.125rem' }}
          >
            ST. THERESE CONVENT SCHOOL
          </Typography>
          <Typography
            fontSize={{ xs: '13px', sm: '16px', md: '18px' }}
            textAlign="center"
            fontWeight={600}
            mt={1}
            variant="h6"
          >
            Near Premier Colony, Kolegaon, 1 Dombivli East, Thane (DIst) <br /> Maharashtra-421 204
          </Typography>
        </Stack>
        <Typography
          className="container-fluid"
          bgcolor={theme.palette.grey[300]}
          textAlign="center"
          fontWeight={600}
          mt={5}
          variant="h5"
          py={3}
          fontSize={{ xs: '16px', sm: '18px', md: '1.5rem' }}
        >
          <u> APPLICATION FOR ADMISSION 2025-2026</u>
        </Typography>
        <Stack px={{ xs: 0, md: 10 }}>
          <Box display="flex" justifyContent="space-between" className="container-fluid">
            <Typography fontWeight={600} mt={5} variant="subtitle2">
              SNO.
            </Typography>
            {/* <Typography fontWeight={600} mt={5} variant="subtitle2">
              G.R.NO.
            </Typography> */}
          </Box>
          <Stack className="container-fluid">
            <Typography fontWeight={600} mt={3} variant="subtitle2">
              Please admit my son/daughter/ward, details about whom are given below:
            </Typography>
            <Typography fontWeight={600} variant="subtitle2">
              I declare that the following data are correct
            </Typography>
          </Stack>
          <AppBar position="sticky" color="default" sx={{ top: 0, mt: 2, boxShadow: 0 }}>
            <Tabs
              variant="scrollable"
              value={activeTab}
              onChange={handleTabChange}
              TabIndicatorProps={{
                style: {
                  display: 'none',
                  // height: 50,
                  // backgroundColor:
                  //   activeTab === 0
                  //     ? isAnyRequiredFieldEmptyStep1
                  //       ? theme.palette.warning.main // If any required field is empty, set background to warning color
                  //       : theme.palette.error.main // Otherwise, set background to error color
                  //     : activeTab === 1
                  //     ? isAnyRequiredFieldEmptyStep2
                  //       ? theme.palette.warning.main // If any required field is empty, set background to warning color
                  //       : theme.palette.error.main
                  //     : activeTab === 2
                  //     ? isAnyRequiredFieldEmptyStep3
                  //       ? theme.palette.warning.main // If any required field is empty, set background to warning color
                  //       : theme.palette.error.main
                  //     : activeTab === 3
                  //     ? isAnyRequiredFieldEmptyStep4
                  //       ? theme.palette.warning.main // If any required field is empty, set background to warning color
                  //       : theme.palette.error.main
                  //     : activeTab === 4
                  //     ? isAnyRequiredFieldEmptyStep5
                  //       ? theme.palette.warning.main // If any required field is empty, set background to warning color
                  //       : theme.palette.error.main // Otherwise, set background to error color
                  //     : theme.palette.warning.main,
                  // color: theme.palette.common.white, // Active tab text color

                  // clipPath:
                  //   activeTab === 0
                  //     ? ' polygon(95% 0%, 100% 50%, 95% 100%, 0% 100%, 0% 50%, 0% 0%)'
                  //     : activeTab === 4
                  //     ? 'polygon(100% 0%, 100% 100%, 75% 100%, 0% 100%, 5% 50%, 0% 0%)'
                  //     : 'polygon(94% 0%, 100% 50%, 94% 100%, 0% 100%, 6% 50%, 0% 0%)',
                }, // Hide default underline
              }}
              sx={{
                '& .MuiTabs-flexContainer': {
                  gap: 0, // Ensures no space between Tab components
                },
                '& .MuiTab-root': {
                  textTransform: 'none', // Disable uppercase
                  fontWeight: 'bold', // Bold font
                  px: 3, // Add padding to the tabs
                  py: 1.5, // Add vertical padding
                },
                '& .MuiButtonBase-root.MuiTab-root.Mui-selected': {
                  // border:1,
                  // borderColor:'red',
                  bgcolor:
                    activeTab === 0
                      ? isAnyRequiredFieldEmptyStep1
                        ? theme.palette.warning.main // If any required field is empty, set background to warning color
                        : theme.palette.success.main // Otherwise, set background to error color
                      : activeTab === 1
                      ? isAnyRequiredFieldEmptyStep2
                        ? theme.palette.warning.main // If any required field is empty, set background to warning color
                        : theme.palette.success.main
                      : activeTab === 2
                      ? isAnyRequiredFieldEmptyStep3
                        ? theme.palette.warning.main // If any required field is empty, set background to warning color
                        : theme.palette.success.main
                      : activeTab === 3
                      ? isAnyRequiredFieldEmptyStep4
                        ? theme.palette.warning.main // If any required field is empty, set background to warning color
                        : theme.palette.success.main
                      : activeTab === 4
                      ? isAnyRequiredFieldEmptyStep5
                        ? theme.palette.warning.main // If any required field is empty, set background to warning color
                        : theme.palette.success.main
                      : activeTab === 5
                      ? isAnyRequiredFieldEmptyStep6
                        ? theme.palette.warning.main // If any required field is empty, set background to warning color
                        : theme.palette.success.main // Otherwise, set background to error color
                      : theme.palette.warning.main, // Default background color when no condition is met // Active tab background color (red)
                  color: theme.palette.common.white,
                  // activeTab === 0
                  //   ? isAnyRequiredFieldEmptyStep1
                  //     ? theme.palette.error.main // If any required field is empty, set background to warning color
                  //     : theme.palette.warning.main // Otherwise, set background to error color
                  //   : activeTab === 1
                  //   ? isAnyRequiredFieldEmptyStep2
                  //     ? theme.palette.error.main // If any required field is empty, set background to warning color
                  //     : theme.palette.warning.main
                  //   : activeTab === 2
                  //   ? isAnyRequiredFieldEmptyStep3
                  //     ? theme.palette.error.main // If any required field is empty, set background to warning color
                  //     : theme.palette.warning.main
                  //   : activeTab === 3
                  //   ? isAnyRequiredFieldEmptyStep4
                  //     ? theme.palette.error.main // If any required field is empty, set background to warning color
                  //     : theme.palette.warning.main
                  //   : activeTab === 4
                  //   ? isAnyRequiredFieldEmptyStep5
                  //     ? theme.palette.error.main // If any required field is empty, set background to warning color
                  //     : theme.palette.warning.main // Otherwise, set background to error color
                  //   : theme.palette.warning.main,
                  // Default background color when no condition is met
                },
                // '& .MuiTab-root:not(.Mui-selected)': {
                //   color: '#fff', // Active tab text color
                // },
              }}
            >
              {tabLabels.map((tab, index) => (
                <Tab
                  key={tab.id}
                  label={tab.label}
                  disableRipple
                  sx={{
                    '&:not(:last-of-type)': {
                      marginRight: 0,
                      // zIndex: 1,
                    },
                    '&:hover': {
                      // boxShadow: 'inset 0 0 0 2px red', // Creates an inside border effect
                      bgcolor: theme.palette.grey[300],
                      transition: 'transform 300ms ease-in-out',
                    },
                    // transition: 'transform 300ms ease-in-out',
                    // '&:not(.Mui-selected)': {
                    //   transform:
                    //     index === 0 ? 'translateX(0%)' : activeTab === index ? 'translateX(0%)' : 'translateX(0%)',
                    // },
                    // transform: index === 0 ? 'translateX(100%)' : 'translateX(0%)',
                    // transform: index === 0 ? 'translateX(0%)' : index === 1 ? 'translateX(100%)' : 'translateX(0%)',
                    // transform:
                    //   index === activeTab
                    //     ? 'translateX(0%)'
                    //     : index < activeTab
                    //     ? 'translateX(0%)'
                    //     : 'translateX(0%)',
                    flexGrow: 1, // Spread the tabs evenly
                    textAlign: 'center', // Center-align text
                    minHeight: '48px', // Set height for uniformity
                    // polygon(100% 0%, 100% 100%, 75% 100%, 0% 100%, 5% 50%, 0% 0%)
                    // 100% 0%, 100% 50%, 100% 100%, 0% 100%, 4% 50%, 0% 0%
                    // clipPath:,
                    clipPath:
                      index === 0
                        ? ' polygon(95% 0%, 100% 50%, 95% 100%, 0% 100%, 0% 50%, 0% 0%)'
                        : index === 5
                        ? 'polygon(100% 0%, 100% 100%, 75% 100%, 0% 100%, 10% 50%, 0% 0%)'
                        : 'polygon(94% 0%, 100% 50%, 94% 100%, 0% 100%, 6% 50%, 0% 0%)',
                    bgcolor:
                      index < activeTab
                        ? index === 0 && isAnyRequiredFieldEmptyStep1
                          ? theme.palette.grey[100]
                          : index === 1 && isAnyRequiredFieldEmptyStep2
                          ? theme.palette.grey[100]
                          : index === 2 && isAnyRequiredFieldEmptyStep3
                          ? theme.palette.grey[100]
                          : index === 3 && isAnyRequiredFieldEmptyStep4
                          ? theme.palette.grey[100]
                          : index === 4 && isAnyRequiredFieldEmptyStep5
                          ? theme.palette.grey[100]
                          : index === 5 && isAnyRequiredFieldEmptyStep6
                          ? theme.palette.grey[100]
                          : theme.palette.success.main
                        : theme.palette.grey[100], // Red for current and previous tabs
                    color:
                      index < activeTab
                        ? index === 0 && isAnyRequiredFieldEmptyStep1
                          ? theme.palette.common.black
                          : index === 1 && isAnyRequiredFieldEmptyStep2
                          ? theme.palette.common.black
                          : index === 2 && isAnyRequiredFieldEmptyStep3
                          ? theme.palette.common.black
                          : index === 3 && isAnyRequiredFieldEmptyStep4
                          ? theme.palette.common.black
                          : index === 4 && isAnyRequiredFieldEmptyStep5
                          ? theme.palette.common.black
                          : index === 5 && isAnyRequiredFieldEmptyStep6
                          ? theme.palette.common.black
                          : theme.palette.common.white
                        : theme.palette.common.black, // White text for current/previous tabs
                  }}
                />
              ))}
            </Tabs>
          </AppBar>
          <Stack
            component="form"
            autoComplete="off"
            noValidate
            onSubmit={handleSubmit}
            ref={tabPanelRef}
            className="container-fluid"
          >
            <TabPanel value={activeTab} index={0}>
              <Box sx={{ pt: 10 }}>
                <Grid container columnSpacing={50} rowSpacing={2}>
                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Board *
                      </Typography>
                      <Select
                        size="small"
                        disabled={submitting || availableSeatError}
                        variant="outlined"
                        name="Syllabus"
                        required
                        value={Syllabus}
                        onChange={handleChange}
                        error={touched.Syllabus && !!errors.Syllabus}
                      >
                        <MenuItem value={1}>State</MenuItem>
                      </Select>
                      {touched.Syllabus && !!errors.Syllabus && (
                        <Typography color="red" fontSize="12px" variant="subtitle1">
                          {/* {errors.Syllabus} */}
                        </Typography>
                      )}
                    </FormControl>
                  </Grid>
                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Standard in which admission is sought *
                      </Typography>
                      <Select
                        size="small"
                        disabled={submitting}
                        variant="outlined"
                        name="ClassName"
                        onBlur={handleBlur}
                        required
                        value={ClassName || 'Select'}
                        onChange={(e) => {
                          handleChange(e); // Ensure Formik state updates properly

                          const selectedClass = classSections.find((item) => item.sectionName === e.target.value); // Find the matching class
                          if (selectedClass) {
                            setDobRangeFrom(dayjs(selectedClass.DobRangeFrom).format('MM/DD/YYYY')); // Set dobRangeFrom directly
                            setDobRangeTo(dayjs(selectedClass.DobRangeTo).format('MM/DD/YYYY')); // Set dobRangeFrom directly
                            // Check if availableSeat is 0
                            if (selectedClass.availableSeat === 0 || selectedClass.availableSeat < 0) {
                              setAvailableSeatError(true);
                              showConfirmation(
                                <ErrorMessage
                                  icon={Fail}
                                  message={
                                    <Typography color="red" fontSize="25px" variant="subtitle2">
                                      Admission closed
                                    </Typography>
                                  }
                                />,
                                ''
                              );
                              // setAvailableSeat(0)
                            } else {
                              setAvailableSeatError(false);
                            }
                          } else {
                            setDobRangeFrom(''); // Reset if no match
                            setDobRangeTo(''); // Reset if no match
                          }
                        }}
                        error={touched.ClassName && !!errors.ClassName}
                      >
                        <MenuItem value="Select">Select</MenuItem>
                        {classSections.map((item) => (
                          <MenuItem key={item.sectionId} value={item.sectionName}>
                            {item.sectionName}
                          </MenuItem>
                        ))}
                      </Select>
                      {/* Error message for availableSeat */}
                      {availableSeatError && (
                        <Typography color="red" fontSize="12px" variant="subtitle1">
                          Admission closed
                        </Typography>
                      )}
                      {touched.ClassName && !!errors.ClassName && (
                        <Typography color="red" fontSize="12px" variant="subtitle1">
                          {errors.ClassName}
                        </Typography>
                      )}
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Surname *
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="Surname"
                        value={Surname}
                        onChange={handleChange}
                        error={touched.Surname && !!errors.Surname}
                        helperText={touched.Surname && typeof errors.Surname === 'string' ? errors.Surname : undefined}
                        InputProps={{
                          endAdornment: touched.Surname && !!errors.Surname && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Student's Name *
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="StudentName"
                        value={StudentName}
                        onChange={handleChange}
                        error={touched.StudentName && !!errors.StudentName}
                        helperText={
                          touched.StudentName && typeof errors.StudentName === 'string' ? errors.StudentName : undefined
                        }
                        InputProps={{
                          endAdornment: touched.StudentName && !!errors.StudentName && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Father&apos;s Name *
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="FathersName"
                        value={FathersName}
                        onChange={handleChange}
                        error={touched.FathersName && !!errors.FathersName}
                        helperText={
                          touched.FathersName && typeof errors.FathersName === 'string' ? errors.FathersName : undefined
                        }
                        InputProps={{
                          endAdornment: touched.FathersName && !!errors.FathersName && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Select Gender *
                      </Typography>
                      <Box
                        sx={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          gap: { xs: 10, sm: 20, md: 30, lg: 10, xl: 20, xxl: 30 },
                        }}
                      >
                        <Button
                          disabled={submitting || availableSeatError}
                          fullWidth
                          variant={SGender === 0 ? 'contained' : 'outlined'}
                          color="secondary"
                          onClick={() => handleGenderSelect(0)}
                          startIcon={<SlUser />}
                        >
                          Male
                        </Button>
                        <Button
                          disabled={submitting || availableSeatError}
                          fullWidth
                          variant={SGender === 1 ? 'contained' : 'outlined'}
                          color="secondary"
                          onClick={() => handleGenderSelect(1)}
                          startIcon={<SlUserFemale />}
                        >
                          Female
                        </Button>
                      </Box>
                      {touched.SGender && errors.SGender && typeof errors.SGender === 'string' && (
                        <Typography color="red" fontSize="12px" mt={1} ml={2.5} variant="subtitle1">
                          {errors.SGender}
                        </Typography>
                      )}
                    </FormControl>
                  </Grid>

                  <Grid item xxl={6} xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Date of Birth (In figure) *
                      </Typography>
                      <DatePickers
                        disabled={submitting || availableSeatError}
                        // fullWidth={{
                        //   '@media (min-width: 1300px)': { width: '35.3vw' },
                        //   '@media (min-width: 1300px)': { width: '35.3vw' },
                        // }}
                        fullWidth={{ xs: '100%', xxl: '34vw', xl: '31vw' }}
                        // maxWidth="800px"
                        // width="300px"
                        name="SDob"
                        value={dayjs(SDob, 'DD-MM-YYYY')} // Format the date before passing it
                        onChange={(e) => {
                          setFieldValue('SDob', dayjs(e).format('DD/MM/YYYY')); // Store the selected date in the state
                          const selectedDate = e ? dayjs(e) : ''; // Convert the selected date to dayjs object
                          setStudentDOB(selectedDate); // Store the selected date in the state
                        }}
                        maxDate={maxDate || dayjs(dobRangeTo)}
                        minDate={dayjs(dobRangeFrom)}
                        variant={{ textField: { variant: 'outlined', fullWidth: true } }}
                      />
                      {touched.SDob && errors.SDob && typeof errors.SDob === 'string' && (
                        <Typography color="red" fontSize="12px" mt={1} ml={2.5} variant="subtitle1">
                          {errors.SDob}
                        </Typography>
                      )}
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Date of Birth (In words) *
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="SdobInWords"
                        value={SdobInWords}
                        onChange={handleChange}
                        error={touched.SdobInWords && !!errors.SdobInWords}
                        helperText={
                          touched.SdobInWords && typeof errors.SdobInWords === 'string' ? errors.SdobInWords : undefined
                        }
                        InputProps={{
                          endAdornment: touched.SdobInWords && !!errors.SdobInWords && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Place of Birth *
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="SPlaceOfBorth"
                        value={SPlaceOfBorth}
                        onChange={handleChange}
                        error={touched.SPlaceOfBorth && !!errors.SPlaceOfBorth}
                        helperText={
                          touched.SPlaceOfBorth && typeof errors.SPlaceOfBorth === 'string'
                            ? errors.SPlaceOfBorth
                            : undefined
                        }
                        InputProps={{
                          endAdornment: touched.SPlaceOfBorth && !!errors.SPlaceOfBorth && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Mother Tongue *
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="SMotherTongue"
                        value={SMotherTongue}
                        onChange={handleChange}
                        error={touched.SMotherTongue && !!errors.SMotherTongue}
                        helperText={
                          touched.SMotherTongue && typeof errors.SMotherTongue === 'string'
                            ? errors.SMotherTongue
                            : undefined
                        }
                        InputProps={{
                          endAdornment: touched.SMotherTongue && !!errors.SMotherTongue && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <Grid container columnSpacing={{ xs: 10, sm: 20, md: 30, lg: 10, xl: 20, xxl: 30 }} rowSpacing={2}>
                      <Grid item xl={6} lg={6} md={6} xs={12}>
                        <FormControl fullWidth>
                          <Typography variant="h6" fontSize={13}>
                            Religion *
                          </Typography>
                          <TextField
                            fullWidth
                            size="small"
                            onBlur={handleBlur}
                            disabled={submitting || availableSeatError}
                            color="primary"
                            required
                            variant="outlined"
                            name="SReligion"
                            value={SReligion}
                            onChange={handleChange}
                            error={touched.SReligion && !!errors.SReligion}
                            helperText={
                              touched.SReligion && typeof errors.SReligion === 'string' ? errors.SReligion : undefined
                            }
                            InputProps={{
                              endAdornment: touched.SReligion && !!errors.SReligion && <ErrorIcon color="error" />,
                            }}
                          />
                        </FormControl>
                      </Grid>
                      <Grid item xl={6} lg={6} md={6} xs={12}>
                        <FormControl fullWidth>
                          <Typography variant="h6" fontSize={13}>
                            Caste *
                          </Typography>
                          <TextField
                            fullWidth
                            size="small"
                            onBlur={handleBlur}
                            disabled={submitting || availableSeatError}
                            color="primary"
                            required
                            variant="outlined"
                            name="SCaste"
                            value={SCaste}
                            onChange={handleChange}
                            error={touched.SCaste && !!errors.SCaste}
                            helperText={touched.SCaste && typeof errors.SCaste === 'string' ? errors.SCaste : undefined}
                            InputProps={{
                              endAdornment: touched.SCaste && !!errors.SCaste && <ErrorIcon color="error" />,
                            }}
                          />
                        </FormControl>
                      </Grid>
                    </Grid>
                  </Grid>
                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Nationality
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="SNationality"
                        value={SNationality}
                        onChange={handleChange}
                        error={touched.SNationality && !!errors.SNationality}
                        helperText={
                          touched.SNationality && typeof errors.SNationality === 'string'
                            ? errors.SNationality
                            : undefined
                        }
                        InputProps={{
                          endAdornment: touched.SNationality && !!errors.SNationality && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        School Last Attended *
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="SchoolLastAttended"
                        value={SchoolLastAttended}
                        onChange={handleChange}
                        error={touched.SchoolLastAttended && !!errors.SchoolLastAttended}
                        helperText={
                          touched.SchoolLastAttended && typeof errors.SchoolLastAttended === 'string'
                            ? errors.SchoolLastAttended
                            : undefined
                        }
                        InputProps={{
                          endAdornment: touched.SchoolLastAttended && !!errors.SchoolLastAttended && (
                            <ErrorIcon color="error" />
                          ),
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Full Residential Address *
                      </Typography>
                      <TextField
                        multiline
                        name="SAddress1"
                        value={SAddress1}
                        onChange={handleChange}
                        error={touched.SAddress1 && !!errors.SAddress1}
                        helperText={
                          touched.SAddress1 && typeof errors.SAddress1 === 'string' ? errors.SAddress1 : undefined
                        }
                        disabled={submitting || availableSeatError}
                        fullWidth
                        InputProps={{
                          inputProps: {
                            style: { resize: 'vertical', width: '100%', minHeight: '1.4375em', maxHeight: '100px' },
                          },
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Locality, Post Taluka *
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="SAddress2"
                        value={SAddress2}
                        onChange={handleChange}
                        error={touched.SAddress2 && !!errors.SAddress2}
                        helperText={
                          touched.SAddress2 && typeof errors.SAddress2 === 'string' ? errors.SAddress2 : undefined
                        }
                        InputProps={{
                          endAdornment: touched.SAddress2 && !!errors.SAddress2 && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Pincode *
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="SAddress3"
                        value={SAddress3}
                        onChange={handleChange}
                        error={touched.SAddress3 && !!errors.SAddress3}
                        helperText={
                          touched.SAddress3 && typeof errors.SAddress3 === 'string' ? errors.SAddress3 : undefined
                        }
                        InputProps={{
                          endAdornment: touched.SAddress3 && !!errors.SAddress3 && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Blood Group of the Child *
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="StudentBloodGroup"
                        value={StudentBloodGroup}
                        onChange={handleChange}
                        error={touched.StudentBloodGroup && !!errors.StudentBloodGroup}
                        helperText={
                          touched.StudentBloodGroup && typeof errors.StudentBloodGroup === 'string'
                            ? errors.StudentBloodGroup
                            : undefined
                        }
                        InputProps={{
                          endAdornment: touched.StudentBloodGroup && !!errors.StudentBloodGroup && (
                            <ErrorIcon color="error" />
                          ),
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Candidate Aadhaar Number
                      </Typography>
                      <Box sx={{ display: 'flex', justifyContent: 'start', gap: 2 }}>
                        <Button
                          disabled={submitting || availableSeatError}
                          sx={{ width: 100 }}
                          variant={studentAadharNo === 'yes' ? 'contained' : 'outlined'}
                          color="secondary"
                          onClick={() => setStudentAadharNo('yes')}
                          startIcon={studentAadharNo === 'yes' ? <CheckIcon /> : null}
                        >
                          Yes
                        </Button>
                        <Button
                          disabled={submitting || availableSeatError}
                          sx={{ width: 100 }}
                          variant={studentAadharNo === 'no' ? 'contained' : 'outlined'}
                          color="secondary"
                          onClick={() => setStudentAadharNo('no')}
                          startIcon={studentAadharNo === 'no' ? <CheckIcon /> : null} // Conditionally show CheckIcon
                        >
                          No
                        </Button>
                      </Box>
                    </FormControl>
                    {studentAadharNo === 'yes' && (
                      <Grid item xl={12} lg={12} md={12} xs={12} mt={2}>
                        <FormControl fullWidth>
                          <TextField
                            fullWidth
                            size="small"
                            onBlur={handleBlur}
                            disabled={submitting || availableSeatError}
                            color="primary"
                            required
                            variant="outlined"
                            name="SAadharNo"
                            value={SAadharNo}
                            onChange={handleChange}
                            error={touched.SAadharNo && !!errors.SAadharNo}
                            helperText={
                              touched.SAadharNo && typeof errors.SAadharNo === 'string' ? errors.SAadharNo : undefined
                            }
                            InputProps={{
                              endAdornment: touched.SAadharNo && !!errors.SAadharNo && <ErrorIcon color="error" />,
                            }}
                          />
                        </FormControl>
                      </Grid>
                    )}
                  </Grid>
                </Grid>
              </Box>
            </TabPanel>
            <TabPanel value={activeTab} index={1}>
              <Box sx={{ pt: 10 }}>
                <Grid container columnSpacing={50} rowSpacing={2}>
                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Father Name *
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="FatherName"
                        value={FatherName}
                        onChange={handleChange}
                        error={touched.FatherName && !!errors.FatherName}
                        helperText={
                          touched.FatherName && typeof errors.FatherName === 'string' ? errors.FatherName : undefined
                        }
                        InputProps={{
                          endAdornment: touched.FatherName && !!errors.FatherName && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Mother Tongue *
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="FMotherTongue"
                        value={FMotherTongue}
                        onChange={handleChange}
                        error={touched.FMotherTongue && !!errors.FMotherTongue}
                        helperText={
                          touched.FMotherTongue && typeof errors.FMotherTongue === 'string'
                            ? errors.FMotherTongue
                            : undefined
                        }
                        InputProps={{
                          endAdornment: touched.FMotherTongue && !!errors.FMotherTongue && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Date of Birth (In figure) *
                      </Typography>
                      <DatePickers
                        disabled={submitting || availableSeatError}
                        // fullWidth="35.3vw"
                        fullWidth={{ xs: '100%', xxl: '34vw', xl: '31vw' }}
                        name="FDob"
                        value={dayjs(FDob, 'DD-MM-YYYY')} // Format the date before passing it
                        onChange={(e) => {
                          setFieldValue('FDob', dayjs(e).format('DD/MM/YYYY'));
                          const selectedDate = e ? dayjs(e) : ''; // Convert the selected date to dayjs object
                          setFatherDOB(selectedDate); // Store the selected date in the state
                        }}
                        maxDate={maxDate}
                        variant={{ textField: { variant: 'outlined', fullWidth: true } }}
                      />
                      {touched.FDob && errors.FDob && typeof errors.FDob === 'string' && (
                        <Typography color="red" fontSize="12px" mt={1} ml={2.5} variant="subtitle1">
                          {errors.FDob}
                        </Typography>
                      )}
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Father&apos;s Email *
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="FEmail"
                        value={FEmail}
                        onChange={handleChange}
                        error={touched.FEmail && !!errors.FEmail}
                        helperText={touched.FEmail && typeof errors.FEmail === 'string' ? errors.FEmail : undefined}
                        InputProps={{
                          endAdornment: touched.FEmail && !!errors.FEmail && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Religion *
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="FReligion"
                        value={FReligion}
                        onChange={handleChange}
                        error={touched.FReligion && !!errors.FReligion}
                        helperText={
                          touched.FReligion && typeof errors.FReligion === 'string' ? errors.FReligion : undefined
                        }
                        InputProps={{
                          endAdornment: touched.FReligion && !!errors.FReligion && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Caste *
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="FCaste"
                        value={FCaste}
                        onChange={handleChange}
                        error={touched.FCaste && !!errors.FCaste}
                        helperText={touched.FCaste && typeof errors.FCaste === 'string' ? errors.FCaste : undefined}
                        InputProps={{
                          endAdornment: touched.FCaste && !!errors.FCaste && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Academic Qualification *
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="FQualification"
                        value={FQualification}
                        onChange={handleChange}
                        error={touched.FQualification && !!errors.FQualification}
                        helperText={
                          touched.FQualification && typeof errors.FQualification === 'string'
                            ? errors.FQualification
                            : undefined
                        }
                        InputProps={{
                          endAdornment: touched.FQualification && !!errors.FQualification && (
                            <ErrorIcon color="error" />
                          ),
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Occupation & Designation
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="FOccupation"
                        value={FOccupation}
                        onChange={handleChange}
                        error={touched.FOccupation && !!errors.FOccupation}
                        helperText={
                          touched.FOccupation && typeof errors.FOccupation === 'string' ? errors.FOccupation : undefined
                        }
                        InputProps={{
                          endAdornment: touched.FOccupation && !!errors.FOccupation && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Name of Present Company/Concern Working *
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="FNameOfCompany"
                        value={FNameOfCompany}
                        onChange={handleChange}
                        error={touched.FNameOfCompany && !!errors.FNameOfCompany}
                        helperText={
                          touched.FNameOfCompany && typeof errors.FNameOfCompany === 'string'
                            ? errors.FNameOfCompany
                            : undefined
                        }
                        InputProps={{
                          endAdornment: touched.FNameOfCompany && !!errors.FNameOfCompany && (
                            <ErrorIcon color="error" />
                          ),
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <Grid container columnSpacing={{ xs: 30, lg: 30 }} rowSpacing={2}>
                      <Grid item xl={6} lg={6} md={12} xs={12}>
                        <FormControl fullWidth>
                          <Typography variant="h6" fontSize={13}>
                            Experiance In Years
                          </Typography>
                          <TextField
                            fullWidth
                            size="small"
                            onBlur={handleBlur}
                            disabled={submitting || availableSeatError}
                            color="primary"
                            required
                            variant="outlined"
                            name="FCompanyYear"
                            value={FCompanyYear}
                            onChange={handleChange}
                            error={touched.FCompanyYear && !!errors.FCompanyYear}
                            helperText={
                              touched.FCompanyYear && typeof errors.FCompanyYear === 'string'
                                ? errors.FCompanyYear
                                : undefined
                            }
                            InputProps={{
                              endAdornment: touched.FCompanyYear && !!errors.FCompanyYear && (
                                <ErrorIcon color="error" />
                              ),
                            }}
                          />
                        </FormControl>
                      </Grid>
                      <Grid item xl={6} lg={6} md={12} xs={12}>
                        <FormControl fullWidth>
                          <Typography variant="h6" fontSize={13}>
                            {/* Experiance In Months */}
                            Annual Income
                          </Typography>
                          <TextField
                            fullWidth
                            size="small"
                            onBlur={handleBlur}
                            disabled={submitting || availableSeatError}
                            color="primary"
                            required
                            variant="outlined"
                            name="FCompanyMonth"
                            value={FCompanyMonth}
                            onChange={handleChange}
                            error={touched.FCompanyMonth && !!errors.FCompanyMonth}
                            helperText={
                              touched.FCompanyMonth && typeof errors.FCompanyMonth === 'string'
                                ? errors.FCompanyMonth
                                : undefined
                            }
                            InputProps={{
                              endAdornment: touched.FCompanyMonth && !!errors.FCompanyMonth && (
                                <ErrorIcon color="error" />
                              ),
                            }}
                          />
                        </FormControl>
                      </Grid>
                    </Grid>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Full Office Address
                      </Typography>
                      <TextField
                        multiline
                        name="FOfficeAddress"
                        value={FOfficeAddress}
                        onChange={handleChange}
                        error={touched.FOfficeAddress && !!errors.FOfficeAddress}
                        helperText={
                          touched.FOfficeAddress && typeof errors.FOfficeAddress === 'string'
                            ? errors.FOfficeAddress
                            : undefined
                        }
                        disabled={submitting || availableSeatError}
                        fullWidth
                        InputProps={{
                          inputProps: {
                            style: { resize: 'vertical', width: '100%', minHeight: '1.4375em', maxHeight: '100px' },
                          },
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Mobile Number *
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        type="number"
                        variant="outlined"
                        name="FOfficeMobile"
                        value={FOfficeMobile}
                        onChange={handleChange}
                        error={touched.FOfficeMobile && !!errors.FOfficeMobile}
                        helperText={
                          touched.FOfficeMobile && typeof errors.FOfficeMobile === 'string'
                            ? errors.FOfficeMobile
                            : undefined
                        }
                        InputProps={{
                          endAdornment: touched.FOfficeMobile && !!errors.FOfficeMobile && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Telephone Number
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        type="number"
                        variant="outlined"
                        name="FOfficeTelephone"
                        value={FOfficeTelephone}
                        onChange={handleChange}
                        error={touched.FOfficeTelephone && !!errors.FOfficeTelephone}
                        helperText={
                          touched.FOfficeTelephone && typeof errors.FOfficeTelephone === 'string'
                            ? errors.FOfficeTelephone
                            : undefined
                        }
                        InputProps={{
                          endAdornment: touched.FOfficeTelephone && !!errors.FOfficeTelephone && (
                            <ErrorIcon color="error" />
                          ),
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Father&apos;s Aadhaar Number *
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="FAadharNo"
                        value={FAadharNo}
                        onChange={handleChange}
                        error={touched.FAadharNo && !!errors.FAadharNo}
                        helperText={
                          touched.FAadharNo && typeof errors.FAadharNo === 'string' ? errors.FAadharNo : undefined
                        }
                        InputProps={{
                          endAdornment: touched.FAadharNo && !!errors.FAadharNo && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>
                </Grid>
              </Box>
            </TabPanel>
            <TabPanel value={activeTab} index={2}>
              <Box sx={{ pt: 10 }}>
                <Grid container columnSpacing={50} rowSpacing={2}>
                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Mother Name *
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="MotherName"
                        value={MotherName}
                        onChange={handleChange}
                        error={touched.MotherName && !!errors.MotherName}
                        helperText={
                          touched.MotherName && typeof errors.MotherName === 'string' ? errors.MotherName : undefined
                        }
                        InputProps={{
                          endAdornment: touched.MotherName && !!errors.MotherName && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Mother Tongue *
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="MMotherTongue"
                        value={MMotherTongue}
                        onChange={handleChange}
                        error={touched.MMotherTongue && !!errors.MMotherTongue}
                        helperText={
                          touched.MMotherTongue && typeof errors.MMotherTongue === 'string'
                            ? errors.MMotherTongue
                            : undefined
                        }
                        InputProps={{
                          endAdornment: touched.MMotherTongue && !!errors.MMotherTongue && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Date of Birth (In figure) *
                      </Typography>
                      <DatePickers
                        disabled={submitting || availableSeatError}
                        // fullWidth="35.3vw"
                        fullWidth={{ xs: '100%', xxl: '34vw', xl: '31vw' }}
                        name="MDob"
                        value={dayjs(MDob, 'DD-MM-YYYY')} // Format the date before passing it
                        onChange={(e) => {
                          setFieldValue('MDob', dayjs(e).format('DD/MM/YYYY'));
                          const selectedDate = e ? dayjs(e) : ''; // Convert the selected date to dayjs object
                          setMotherDOB(selectedDate); // Store the selected date in the state
                        }}
                        maxDate={maxDate}
                        variant={{ textField: { variant: 'outlined', fullWidth: true } }}
                      />
                      {touched.MDob && errors.MDob && typeof errors.MDob === 'string' && (
                        <Typography color="red" fontSize="12px" mt={1} ml={2.5} variant="subtitle1">
                          {errors.MDob}
                        </Typography>
                      )}
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Mother&apos;s Email{' '}
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="MEmail"
                        value={MEmail}
                        onChange={handleChange}
                        error={touched.MEmail && !!errors.MEmail}
                        helperText={touched.MEmail && typeof errors.MEmail === 'string' ? errors.MEmail : undefined}
                        InputProps={{
                          endAdornment: touched.MEmail && !!errors.MEmail && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Religion *
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="MReligion"
                        value={MReligion}
                        onChange={handleChange}
                        error={touched.MReligion && !!errors.MReligion}
                        helperText={
                          touched.MReligion && typeof errors.MReligion === 'string' ? errors.MReligion : undefined
                        }
                        InputProps={{
                          endAdornment: touched.MReligion && !!errors.MReligion && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Caste *
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="MCaste"
                        value={MCaste}
                        onChange={handleChange}
                        error={touched.MCaste && !!errors.MCaste}
                        helperText={touched.MCaste && typeof errors.MCaste === 'string' ? errors.MCaste : undefined}
                        InputProps={{
                          endAdornment: touched.MCaste && !!errors.MCaste && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Academic Qualification *
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="MQualification"
                        value={MQualification}
                        onChange={handleChange}
                        error={touched.MQualification && !!errors.MQualification}
                        helperText={
                          touched.MQualification && typeof errors.MQualification === 'string'
                            ? errors.MQualification
                            : undefined
                        }
                        InputProps={{
                          endAdornment: touched.MQualification && !!errors.MQualification && (
                            <ErrorIcon color="error" />
                          ),
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Occupation & Designation
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="MOccupation"
                        value={MOccupation}
                        onChange={handleChange}
                        error={touched.MOccupation && !!errors.MOccupation}
                        helperText={
                          touched.MOccupation && typeof errors.MOccupation === 'string' ? errors.MOccupation : undefined
                        }
                        InputProps={{
                          endAdornment: touched.MOccupation && !!errors.MOccupation && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Name of Present Company/Concern Working
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="MNameOfCompany"
                        value={MNameOfCompany}
                        onChange={handleChange}
                        error={touched.MNameOfCompany && !!errors.MNameOfCompany}
                        helperText={
                          touched.MNameOfCompany && typeof errors.MNameOfCompany === 'string'
                            ? errors.MNameOfCompany
                            : undefined
                        }
                        InputProps={{
                          endAdornment: touched.MNameOfCompany && !!errors.MNameOfCompany && (
                            <ErrorIcon color="error" />
                          ),
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <Grid container columnSpacing={{ xs: 30, lg: 30 }} rowSpacing={2}>
                      <Grid item xl={6} lg={6} md={12} xs={12}>
                        <FormControl fullWidth>
                          <Typography variant="h6" fontSize={13}>
                            Experiance In Years
                          </Typography>
                          <TextField
                            fullWidth
                            size="small"
                            onBlur={handleBlur}
                            disabled={submitting || availableSeatError}
                            color="primary"
                            required
                            variant="outlined"
                            name="MCompanyYear"
                            value={MCompanyYear}
                            onChange={handleChange}
                            error={touched.MCompanyYear && !!errors.MCompanyYear}
                            helperText={
                              touched.MCompanyYear && typeof errors.MCompanyYear === 'string'
                                ? errors.MCompanyYear
                                : undefined
                            }
                            InputProps={{
                              endAdornment: touched.MCompanyYear && !!errors.MCompanyYear && (
                                <ErrorIcon color="error" />
                              ),
                            }}
                          />
                        </FormControl>
                      </Grid>
                      <Grid item xl={6} lg={6} md={12} xs={12}>
                        <FormControl fullWidth>
                          <Typography variant="h6" fontSize={13}>
                            Annual Income
                          </Typography>
                          <TextField
                            fullWidth
                            size="small"
                            onBlur={handleBlur}
                            disabled={submitting || availableSeatError}
                            color="primary"
                            required
                            variant="outlined"
                            name="MCompanyMonth"
                            value={MCompanyMonth}
                            onChange={handleChange}
                            error={touched.MCompanyMonth && !!errors.MCompanyMonth}
                            helperText={
                              touched.MCompanyMonth && typeof errors.MCompanyMonth === 'string'
                                ? errors.MCompanyMonth
                                : undefined
                            }
                            InputProps={{
                              endAdornment: touched.MCompanyMonth && !!errors.MCompanyMonth && (
                                <ErrorIcon color="error" />
                              ),
                            }}
                          />
                        </FormControl>
                      </Grid>
                    </Grid>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Full Office Address
                      </Typography>
                      <TextField
                        multiline
                        name="MOfficeAddress"
                        value={MOfficeAddress}
                        onChange={handleChange}
                        error={touched.MOfficeAddress && !!errors.MOfficeAddress}
                        helperText={
                          touched.MOfficeAddress && typeof errors.MOfficeAddress === 'string'
                            ? errors.MOfficeAddress
                            : undefined
                        }
                        disabled={submitting || availableSeatError}
                        fullWidth
                        InputProps={{
                          inputProps: {
                            style: { resize: 'vertical', width: '100%', minHeight: '1.4375em', maxHeight: '100px' },
                          },
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Mobile Number *
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        type="number"
                        variant="outlined"
                        name="MOfficeMobile"
                        value={MOfficeMobile}
                        onChange={handleChange}
                        error={touched.MOfficeMobile && !!errors.MOfficeMobile}
                        helperText={
                          touched.MOfficeMobile && typeof errors.MOfficeMobile === 'string'
                            ? errors.MOfficeMobile
                            : undefined
                        }
                        InputProps={{
                          endAdornment: touched.MOfficeMobile && !!errors.MOfficeMobile && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Telephone Number
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        type="number"
                        variant="outlined"
                        name="MOfficeTelephone"
                        value={MOfficeTelephone}
                        onChange={handleChange}
                        error={touched.MOfficeTelephone && !!errors.MOfficeTelephone}
                        helperText={
                          touched.MOfficeTelephone && typeof errors.MOfficeTelephone === 'string'
                            ? errors.MOfficeTelephone
                            : undefined
                        }
                        InputProps={{
                          endAdornment: touched.MOfficeTelephone && !!errors.MOfficeTelephone && (
                            <ErrorIcon color="error" />
                          ),
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Mother&apos;s Aadhaar Number *
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="MAadharNo"
                        value={MAadharNo}
                        onChange={handleChange}
                        error={touched.MAadharNo && !!errors.MAadharNo}
                        helperText={
                          touched.MAadharNo && typeof errors.MAadharNo === 'string' ? errors.MAadharNo : undefined
                        }
                        InputProps={{
                          endAdornment: touched.MAadharNo && !!errors.MAadharNo && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>
                </Grid>
              </Box>
            </TabPanel>
            <TabPanel value={activeTab} index={3}>
              <Box sx={{ pt: 10 }}>
                <Stack direction="row" spacing={2} sx={{ mb: 2 }}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        disabled={submitting || availableSeatError}
                        checked={autoFillGuardian === 'father'}
                        onChange={handleAutoFillGuardian('father')}
                        name="autoFillGuardianFather"
                        color="primary"
                      />
                    }
                    label="Same as Father's Details"
                  />
                  <FormControlLabel
                    control={
                      <Checkbox
                        disabled={submitting || availableSeatError}
                        checked={autoFillGuardian === 'mother'}
                        onChange={handleAutoFillGuardian('mother')}
                        name="autoFillGuardianMother"
                        color="primary"
                      />
                    }
                    label="Same as Mother's Details"
                  />
                </Stack>

                <Grid container columnSpacing={50} rowSpacing={2}>
                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Name
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="GuardianName"
                        value={GuardianName}
                        onChange={handleChange}
                        error={touched.GuardianName && !!errors.GuardianName}
                        helperText={
                          touched.GuardianName && typeof errors.GuardianName === 'string'
                            ? errors.GuardianName
                            : undefined
                        }
                        InputProps={{
                          endAdornment: touched.GuardianName && !!errors.GuardianName && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Mother Tongue
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="GMotherTongue"
                        value={GMotherTongue}
                        onChange={handleChange}
                        error={touched.GMotherTongue && !!errors.GMotherTongue}
                        helperText={
                          touched.GMotherTongue && typeof errors.GMotherTongue === 'string'
                            ? errors.GMotherTongue
                            : undefined
                        }
                        InputProps={{
                          endAdornment: touched.GMotherTongue && !!errors.GMotherTongue && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Date of Birth (In figure)
                      </Typography>
                      <DatePickers
                        disabled={submitting || availableSeatError}
                        // fullWidth="35.3vw"
                        fullWidth={{ xs: '100%', xxl: '34vw', xl: '31vw' }}
                        name="GDob"
                        value={dayjs(GDob, 'DD-MM-YYYY')} // Format the date before passing it
                        onChange={(e) => {
                          setFieldValue('GDob', dayjs(e).format('DD/MM/YYYY'));
                          const selectedDate = e ? dayjs(e) : ''; // Convert the selected date to dayjs object
                          setGuardianDOB(selectedDate); // Store the selected date in the state
                        }}
                        maxDate={maxDate}
                        variant={{ textField: { variant: 'outlined', fullWidth: true } }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Guardian&apos;s Email
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="GEmail"
                        value={GEmail}
                        onChange={handleChange}
                        error={touched.GEmail && !!errors.GEmail}
                        helperText={touched.GEmail && typeof errors.GEmail === 'string' ? errors.GEmail : undefined}
                        InputProps={{
                          endAdornment: touched.GEmail && !!errors.GEmail && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <Grid container columnSpacing={{ xs: 10, sm: 20, md: 30, lg: 10, xl: 20, xxl: 30 }} rowSpacing={2}>
                      <Grid item xl={6} lg={6} md={12} xs={12}>
                        <FormControl fullWidth>
                          <Typography variant="h6" fontSize={13}>
                            Religion
                          </Typography>
                          <TextField
                            fullWidth
                            size="small"
                            onBlur={handleBlur}
                            disabled={submitting || availableSeatError}
                            color="primary"
                            required
                            variant="outlined"
                            name="GReligion"
                            value={GReligion}
                            onChange={handleChange}
                            error={touched.GReligion && !!errors.GReligion}
                            helperText={
                              touched.GReligion && typeof errors.GReligion === 'string' ? errors.GReligion : undefined
                            }
                            InputProps={{
                              endAdornment: touched.GReligion && !!errors.GReligion && <ErrorIcon color="error" />,
                            }}
                          />
                        </FormControl>
                      </Grid>
                      <Grid item xl={6} lg={6} md={12} xs={12}>
                        <FormControl fullWidth>
                          <Typography variant="h6" fontSize={13}>
                            Caste
                          </Typography>
                          <TextField
                            fullWidth
                            size="small"
                            onBlur={handleBlur}
                            disabled={submitting || availableSeatError}
                            color="primary"
                            required
                            variant="outlined"
                            name="GCaste"
                            value={GCaste}
                            onChange={handleChange}
                            error={touched.GCaste && !!errors.GCaste}
                            helperText={touched.GCaste && typeof errors.GCaste === 'string' ? errors.GCaste : undefined}
                            InputProps={{
                              endAdornment: touched.GCaste && !!errors.GCaste && <ErrorIcon color="error" />,
                            }}
                          />
                        </FormControl>
                      </Grid>
                    </Grid>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Academic Qualification
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="GQualification"
                        value={GQualification}
                        onChange={handleChange}
                        error={touched.GQualification && !!errors.GQualification}
                        helperText={
                          touched.GQualification && typeof errors.GQualification === 'string'
                            ? errors.GQualification
                            : undefined
                        }
                        InputProps={{
                          endAdornment: touched.GQualification && !!errors.GQualification && (
                            <ErrorIcon color="error" />
                          ),
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Experiance In Years
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="GCompanyYear"
                        value={GCompanyYear}
                        onChange={handleChange}
                        error={touched.GCompanyYear && !!errors.GCompanyYear}
                        helperText={
                          touched.GCompanyYear && typeof errors.GCompanyYear === 'string'
                            ? errors.GCompanyYear
                            : undefined
                        }
                        InputProps={{
                          endAdornment: touched.GCompanyYear && !!errors.GCompanyYear && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Mobile Number
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="GOfficeMobile"
                        value={GOfficeMobile}
                        onChange={handleChange}
                        error={touched.GOfficeMobile && !!errors.GOfficeMobile}
                        helperText={
                          touched.GOfficeMobile && typeof errors.GOfficeMobile === 'string'
                            ? errors.GOfficeMobile
                            : undefined
                        }
                        InputProps={{
                          endAdornment: touched.GOfficeMobile && !!errors.GOfficeMobile && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>
                </Grid>
              </Box>
            </TabPanel>

            <TabPanel value={activeTab} index={4}>
              <Box sx={{ pt: 10 }}>
                <Grid container columnSpacing={50} rowSpacing={2}>
                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Name
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="Sibling1"
                        value={Sibling1}
                        onChange={handleChange}
                        error={touched.Sibling1 && !!errors.Sibling1}
                        helperText={
                          touched.Sibling1 && typeof errors.Sibling1 === 'string' ? errors.Sibling1 : undefined
                        }
                        InputProps={{
                          endAdornment: touched.Sibling1 && !!errors.Sibling1 && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Standard
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="Sibling1Std"
                        value={Sibling1Std}
                        onChange={handleChange}
                        error={touched.Sibling1Std && !!errors.Sibling1Std}
                        helperText={
                          touched.Sibling1Std && typeof errors.Sibling1Std === 'string' ? errors.Sibling1Std : undefined
                        }
                        InputProps={{
                          endAdornment: touched.Sibling1Std && !!errors.Sibling1Std && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Name
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="Sibiling2"
                        value={Sibiling2}
                        onChange={handleChange}
                        error={touched.Sibiling2 && !!errors.Sibiling2}
                        helperText={
                          touched.Sibiling2 && typeof errors.Sibiling2 === 'string' ? errors.Sibiling2 : undefined
                        }
                        InputProps={{
                          endAdornment: touched.Sibiling2 && !!errors.Sibiling2 && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Standard
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="Sibling2Std"
                        value={Sibling2Std}
                        onChange={handleChange}
                        error={touched.Sibling2Std && !!errors.Sibling2Std}
                        helperText={
                          touched.Sibling2Std && typeof errors.Sibling2Std === 'string' ? errors.Sibling2Std : undefined
                        }
                        InputProps={{
                          endAdornment: touched.Sibling2Std && !!errors.Sibling2Std && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Name
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="Sibling3"
                        value={Sibling3}
                        onChange={handleChange}
                        error={touched.Sibling3 && !!errors.Sibling3}
                        helperText={
                          touched.Sibling3 && typeof errors.Sibling3 === 'string' ? errors.Sibling3 : undefined
                        }
                        InputProps={{
                          endAdornment: touched.Sibling3 && !!errors.Sibling3 && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Standard
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="Sibling3Std"
                        value={Sibling3Std}
                        onChange={handleChange}
                        error={touched.Sibling3Std && !!errors.Sibling3Std}
                        helperText={
                          touched.Sibling3Std && typeof errors.Sibling3Std === 'string' ? errors.Sibling3Std : undefined
                        }
                        InputProps={{
                          endAdornment: touched.Sibling3Std && !!errors.Sibling3Std && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Whether your child avail school&apos;s transport
                      </Typography>
                      <Box sx={{ display: 'flex', justifyContent: 'start', gap: 2 }}>
                        <Button
                          disabled={submitting || availableSeatError}
                          sx={{ width: 100 }}
                          variant={SchoolTransport === 1 ? 'contained' : 'outlined'}
                          color="secondary"
                          onClick={() => handleTransportSelect(1)}
                          startIcon={SchoolTransport === 1 ? <CheckIcon /> : null}
                        >
                          Yes
                        </Button>
                        <Button
                          disabled={submitting || availableSeatError}
                          sx={{ width: 100 }}
                          variant={SchoolTransport === 0 ? 'contained' : 'outlined'}
                          color="secondary"
                          onClick={() => handleTransportSelect(0)}
                          startIcon={SchoolTransport === 0 ? <CheckIcon /> : null} // Conditionally show CheckIcon
                        >
                          No
                        </Button>
                      </Box>
                    </FormControl>
                    {SchoolTransport === 1 && (
                      <Grid item xl={12} lg={12} md={12} xs={12} mt={2}>
                        <FormControl fullWidth>
                          <Typography variant="h6" fontSize={13}>
                            Enter Bus Stop Pick Up Point
                          </Typography>
                          <TextField
                            fullWidth
                            size="small"
                            onBlur={handleBlur}
                            disabled={submitting || availableSeatError}
                            color="primary"
                            required
                            variant="outlined"
                            name="StopName"
                            value={StopName}
                            onChange={handleChange}
                            error={touched.StopName && !!errors.StopName}
                            helperText={
                              touched.StopName && typeof errors.StopName === 'string' ? errors.StopName : undefined
                            }
                            InputProps={{
                              endAdornment: touched.StopName && !!errors.StopName && <ErrorIcon color="error" />,
                            }}
                          />
                        </FormControl>
                      </Grid>
                    )}
                  </Grid>

                  <Grid item xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        <u> Last Three Vaccination Taken</u>
                      </Typography>
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Name
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="Vaccination1"
                        value={Vaccination1}
                        onChange={handleChange}
                        error={touched.Vaccination1 && !!errors.Vaccination1}
                        helperText={
                          touched.Vaccination1 && typeof errors.Vaccination1 === 'string'
                            ? errors.Vaccination1
                            : undefined
                        }
                        InputProps={{
                          endAdornment: touched.Vaccination1 && !!errors.Vaccination1 && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Date
                      </Typography>
                      <DatePickers
                        disabled={submitting || availableSeatError}
                        // fullWidth="35.3vw"
                        fullWidth={{ xs: '100%', xxl: '34vw', xl: '31vw' }}
                        name="Vaccination1Date"
                        value={dayjs(Vaccination1Date, 'DD-MM-YYYY')} // Format the date before passing it
                        onChange={(e) => {
                          setFieldValue('Vaccination1Date', dayjs(e).format('DD/MM/YYYY'));
                          const selectedDate = e ? dayjs(e) : ''; // Convert the selected date to dayjs object
                          setVaccinationDate1(selectedDate); // Store the selected date in the state
                        }}
                        maxDate={maxDate}
                        variant={{ textField: { variant: 'outlined', fullWidth: true } }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Name
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="Vaccination2"
                        value={Vaccination2}
                        onChange={handleChange}
                        error={touched.Vaccination2 && !!errors.Vaccination2}
                        helperText={
                          touched.Vaccination2 && typeof errors.Vaccination2 === 'string'
                            ? errors.Vaccination2
                            : undefined
                        }
                        InputProps={{
                          endAdornment: touched.Vaccination2 && !!errors.Vaccination2 && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Date
                      </Typography>
                      <DatePickers
                        disabled={submitting || availableSeatError}
                        // fullWidth="35.3vw"
                        fullWidth={{ xs: '100%', xxl: '34vw', xl: '31vw' }}
                        name="Vaccination2Date"
                        value={dayjs(Vaccination2Date, 'DD-MM-YYYY')} // Format the date before passing it
                        onChange={(e) => {
                          setFieldValue('Vaccination2Date', dayjs(e).format('DD/MM/YYYY'));
                          const selectedDate = e ? dayjs(e) : ''; // Convert the selected date to dayjs object
                          setVaccinationDate2(selectedDate); // Store the selected date in the state
                        }}
                        maxDate={maxDate}
                        variant={{ textField: { variant: 'outlined', fullWidth: true } }}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Name
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="Vaccination3"
                        value={Vaccination3}
                        onChange={handleChange}
                        error={touched.Vaccination3 && !!errors.Vaccination3}
                        helperText={
                          touched.Vaccination3 && typeof errors.Vaccination3 === 'string'
                            ? errors.Vaccination3
                            : undefined
                        }
                        InputProps={{
                          endAdornment: touched.Vaccination3 && !!errors.Vaccination3 && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Date
                      </Typography>
                      <DatePickers
                        disabled={submitting || availableSeatError}
                        // fullWidth="35.3vw"
                        fullWidth={{ xs: '100%', xxl: '34vw', xl: '31vw' }}
                        name="Vaccination3Date"
                        value={dayjs(Vaccination3Date, 'DD-MM-YYYY')} // Format the date before passing it
                        onChange={(e) => {
                          setFieldValue('Vaccination3Date', dayjs(e).format('DD/MM/YYYY'));
                          const selectedDate = e ? dayjs(e) : ''; // Convert the selected date to dayjs object
                          setVaccinationDate3(selectedDate); // Store the selected date in the state
                        }}
                        maxDate={maxDate}
                        variant={{ textField: { variant: 'outlined', fullWidth: true } }}
                      />
                    </FormControl>
                  </Grid>
                </Grid>
              </Box>
            </TabPanel>
            <TabPanel value={activeTab} index={5}>
              <Box sx={{ pt: 10 }}>
                <Grid container columnSpacing={50} rowSpacing={2}>
                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <Stack
                      direction="row"
                      alignItems={
                        successIconShow.passportPhoto === 'Success' ? 'end' : passportPhoto === '' ? 'end' : 'center'
                      }
                      gap={2}
                    >
                      <FormControl fullWidth>
                        <ImageCropper
                          uploadedFile={uploadedFile}
                          setUploaded={setUploaded}
                          uploaded={uploaded}
                          setIsDialogOpen={setIsDialogOpen}
                          isDialogOpen={isDialogOpen}
                          // onImageUpload={handleImageUpload}
                          uploadButton={
                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Passport Size Photo of Child *
                              </Typography>
                              <InputFileUpload
                                // disabled={submitting || availableSeatError}
                                name="upload"
                                uploadedFile={
                                  Array.isArray(uploaded) ? uploaded.filter((f) => f.fieldName === 'passportPhoto') : []
                                }
                                buttonLabel="Choose"
                                accept="image/*,application/pdf"
                                disabledTextfeild="disabled"
                                disabledUploadButton={
                                  successIconShow.passportPhoto === 'Success' || submitting || availableSeatError
                                }
                                fieldName="passportPhoto"
                                handleRemoveFile={() => handleRemoveFile('passportPhoto')}
                                onChange={(event) => handleImageUpload(event, 'passportPhoto')}
                                inputRef={fileInputRef} // Pass the ref to the file input
                              />
                            </FormControl>
                          }
                        />
                      </FormControl>
                      {Array.isArray(uploaded)
                        ? uploaded.find((f) => f.fieldName === 'passportPhoto') &&
                          (successIconShow.passportPhoto === 'Success' ? (
                            <Stack width={120} alignItems="center">
                              <Lottie animationData={successIcon} loop={false} style={{ width: '27px' }} />
                              <Typography variant="subtitle2" fontSize={8} color={theme.palette.success.main}>
                                Uploaded
                              </Typography>
                            </Stack>
                          ) : (
                            <LoadingButton
                              //  loadingPosition='start'
                              loading={loadingField === 'passportPhoto'}
                              startIcon={<CloudUploadIcon />}
                              onClick={() => onUpload('passportPhoto')}
                              variant="outlined"
                              color="success"
                              sx={{ width: 112, fontSize: 10, height: 38 }}
                              disabled={loadingField === 'passportPhoto'} // Disable while uploading
                            >
                              {loadingField === 'passportPhoto' ? 'Uploading...' : 'Upload'}
                            </LoadingButton>
                          ))
                        : []}
                    </Stack>
                    {touched.passportPhoto && errors.passportPhoto && typeof errors.passportPhoto === 'string' && (
                      <Typography color="red" fontSize="12px" mt={1} ml={2.5} variant="subtitle1">
                        {errors.passportPhoto}
                      </Typography>
                    )}
                  </Grid>
                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <Stack
                      direction="row"
                      alignItems={
                        successIconShow.birthCertificate === 'Success'
                          ? 'end'
                          : birthCertificate === ''
                          ? 'end'
                          : 'center'
                      }
                      gap={2}
                    >
                      <FormControl fullWidth>
                        <ImageCropper
                          uploadedFile={uploadedFile}
                          setUploaded={setUploaded}
                          uploaded={uploaded}
                          setIsDialogOpen={setIsDialogOpen}
                          isDialogOpen={isDialogOpen}
                          // onImageUpload={handleImageUpload}
                          uploadButton={
                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Birth Certificate *
                              </Typography>
                              <InputFileUpload
                                name="upload"
                                uploadedFile={
                                  Array.isArray(uploaded)
                                    ? uploaded.filter((f) => f.fieldName === 'birthCertificate')
                                    : ([] as FileObjTypes[])
                                }
                                buttonLabel="Choose"
                                accept="image/*,application/pdf"
                                disabledTextfeild="disabled"
                                disabledUploadButton={
                                  successIconShow.birthCertificate === 'Success' || submitting || availableSeatError
                                }
                                fieldName="birthCertificate"
                                handleRemoveFile={() => handleRemoveFile('birthCertificate')}
                                onChange={(event) => handleImageUpload(event, 'birthCertificate')}
                                inputRef={fileInputRef} // Pass the ref to the file input
                              />
                            </FormControl>
                          }
                        />
                      </FormControl>
                      {Array.isArray(uploaded)
                        ? uploaded.find((f) => f.fieldName === 'birthCertificate') &&
                          (successIconShow.birthCertificate === 'Success' ? (
                            <Stack width={120} alignItems="center">
                              <Lottie animationData={successIcon} loop={false} style={{ width: '27px' }} />
                              <Typography variant="subtitle2" fontSize={8} color={theme.palette.success.main}>
                                Uploaded
                              </Typography>
                            </Stack>
                          ) : (
                            <LoadingButton
                              //  loadingPosition='start'
                              loading={loadingField === 'birthCertificate'}
                              startIcon={<CloudUploadIcon />}
                              onClick={() => onUpload('birthCertificate')}
                              variant="outlined"
                              color="success"
                              sx={{ width: 112, fontSize: 10, height: 38 }}
                              disabled={loadingField === 'birthCertificate'} // Disable while uploading
                            >
                              {loadingField === 'birthCertificate' ? 'Uploading...' : 'Upload'}
                            </LoadingButton>
                          ))
                        : []}
                    </Stack>
                    {touched.birthCertificate &&
                      errors.birthCertificate &&
                      typeof errors.birthCertificate === 'string' && (
                        <Typography color="red" fontSize="12px" mt={1} ml={2.5} variant="subtitle1">
                          {errors.birthCertificate}
                        </Typography>
                      )}
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <Stack
                      direction="row"
                      alignItems={
                        successIconShow.studentAadhar === 'Success' ? 'end' : studentAadhar === '' ? 'end' : 'center'
                      }
                      gap={2}
                    >
                      <FormControl fullWidth>
                        <ImageCropper
                          uploadedFile={uploadedFile}
                          setUploaded={setUploaded}
                          uploaded={uploaded}
                          setIsDialogOpen={setIsDialogOpen}
                          isDialogOpen={isDialogOpen}
                          // onImageUpload={handleImageUpload}
                          uploadButton={
                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Aadhar Card of Student
                              </Typography>
                              <InputFileUpload
                                name="upload"
                                uploadedFile={
                                  Array.isArray(uploaded) ? uploaded.filter((f) => f.fieldName === 'studentAadhar') : []
                                }
                                buttonLabel="Choose"
                                accept="image/*,application/pdf"
                                disabledTextfeild="disabled"
                                disabledUploadButton={
                                  successIconShow.studentAadhar === 'Success' || submitting || availableSeatError
                                }
                                fieldName="studentAadhar"
                                handleRemoveFile={() => handleRemoveFile('studentAadhar')}
                                onChange={(event) => handleImageUpload(event, 'studentAadhar')}
                                inputRef={fileInputRef} // Pass the ref to the file input
                              />
                            </FormControl>
                          }
                        />
                      </FormControl>

                      {Array.isArray(uploaded)
                        ? uploaded.find((f) => f.fieldName === 'studentAadhar') &&
                          (successIconShow.studentAadhar === 'Success' ? (
                            <Stack width={120} alignItems="center">
                              <Lottie animationData={successIcon} loop={false} style={{ width: '27px' }} />
                              <Typography variant="subtitle2" fontSize={8} color={theme.palette.success.main}>
                                Uploaded
                              </Typography>
                            </Stack>
                          ) : (
                            <LoadingButton
                              //  loadingPosition='start'
                              loading={loadingField === 'studentAadhar'}
                              startIcon={<CloudUploadIcon />}
                              onClick={() => onUpload('studentAadhar')}
                              variant="outlined"
                              color="success"
                              sx={{ width: 112, fontSize: 10, height: 38 }}
                              disabled={loadingField === 'studentAadhar'} // Disable while uploading
                            >
                              {loadingField === 'studentAadhar' ? 'Uploading...' : 'Upload'}
                            </LoadingButton>
                          ))
                        : []}
                    </Stack>
                    {touched.studentAadhar && errors.studentAadhar && typeof errors.studentAadhar === 'string' && (
                      <Typography color="red" fontSize="12px" mt={1} ml={2.5} variant="subtitle1">
                        {errors.studentAadhar}
                      </Typography>
                    )}
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <Stack
                      direction="row"
                      alignItems={
                        successIconShow.parentAadhar === 'Success' ? 'end' : parentAadhar === '' ? 'end' : 'center'
                      }
                      gap={2}
                    >
                      <FormControl fullWidth>
                        <ImageCropper
                          uploadedFile={uploadedFile}
                          setUploaded={setUploaded}
                          uploaded={uploaded}
                          setIsDialogOpen={setIsDialogOpen}
                          isDialogOpen={isDialogOpen}
                          // onImageUpload={handleImageUpload}
                          uploadButton={
                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Aadhar Card of Parent *
                              </Typography>
                              <InputFileUpload
                                name="upload"
                                uploadedFile={
                                  Array.isArray(uploaded) ? uploaded.filter((f) => f.fieldName === 'parentAadhar') : []
                                }
                                buttonLabel="Choose"
                                accept="image/*,application/pdf"
                                disabledTextfeild="disabled"
                                disabledUploadButton={
                                  successIconShow.parentAadhar === 'Success' || submitting || availableSeatError
                                }
                                fieldName="parentAadhar"
                                handleRemoveFile={() => handleRemoveFile('parentAadhar')}
                                onChange={(event) => handleImageUpload(event, 'parentAadhar')}
                                inputRef={fileInputRef} // Pass the ref to the file input
                              />
                            </FormControl>
                          }
                        />
                      </FormControl>
                      {Array.isArray(uploaded)
                        ? uploaded.find((f) => f.fieldName === 'parentAadhar') &&
                          (successIconShow.parentAadhar === 'Success' ? (
                            <Stack width={120} alignItems="center">
                              <Lottie animationData={successIcon} loop={false} style={{ width: '27px' }} />
                              <Typography variant="subtitle2" fontSize={8} color={theme.palette.success.main}>
                                Uploaded
                              </Typography>
                            </Stack>
                          ) : (
                            <LoadingButton
                              //  loadingPosition='start'
                              loading={loadingField === 'parentAadhar'}
                              startIcon={<CloudUploadIcon />}
                              onClick={() => onUpload('parentAadhar')}
                              variant="outlined"
                              color="success"
                              sx={{ width: 112, fontSize: 10, height: 38 }}
                              disabled={loadingField === 'parentAadhar'} // Disable while uploading
                            >
                              {loadingField === 'parentAadhar' ? 'Uploading...' : 'Upload'}
                            </LoadingButton>
                          ))
                        : []}
                    </Stack>
                    {touched.parentAadhar && errors.parentAadhar && typeof errors.parentAadhar === 'string' && (
                      <Typography color="red" fontSize="12px" mt={1} ml={2.5} variant="subtitle1">
                        {errors.parentAadhar}
                      </Typography>
                    )}
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <Stack
                      direction="row"
                      alignItems={successIconShow.proof === 'Success' ? 'end' : proof === '' ? 'end' : 'center'}
                      gap={2}
                    >
                      <FormControl fullWidth>
                        <ImageCropper
                          uploadedFile={uploadedFile}
                          setUploaded={setUploaded}
                          uploaded={uploaded}
                          setIsDialogOpen={setIsDialogOpen}
                          isDialogOpen={isDialogOpen}
                          // onImageUpload={handleImageUpload}
                          uploadButton={
                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Address Proof *
                              </Typography>
                              <InputFileUpload
                                name="upload"
                                uploadedFile={
                                  Array.isArray(uploaded) ? uploaded.filter((f) => f.fieldName === 'proof') : []
                                }
                                buttonLabel="Choose"
                                accept="image/*,application/pdf"
                                disabledTextfeild="disabled"
                                disabledUploadButton={
                                  successIconShow.proof === 'Success' || submitting || availableSeatError
                                }
                                fieldName="proof"
                                handleRemoveFile={() => handleRemoveFile('proof')}
                                onChange={(event) => handleImageUpload(event, 'proof')}
                                inputRef={fileInputRef} // Pass the ref to the file input
                              />
                            </FormControl>
                          }
                        />
                      </FormControl>
                      {Array.isArray(uploaded)
                        ? uploaded.find((f) => f.fieldName === 'proof') &&
                          (successIconShow.proof === 'Success' ? (
                            <Stack width={120} alignItems="center">
                              <Lottie animationData={successIcon} loop={false} style={{ width: '27px' }} />
                              <Typography variant="subtitle2" fontSize={8} color={theme.palette.success.main}>
                                Uploaded
                              </Typography>
                            </Stack>
                          ) : (
                            <LoadingButton
                              //  loadingPosition='start'
                              loading={loadingField === 'proof'}
                              startIcon={<CloudUploadIcon />}
                              onClick={() => onUpload('proof')}
                              variant="outlined"
                              color="success"
                              sx={{ width: 112, fontSize: 10, height: 38 }}
                              disabled={loadingField === 'proof'} // Disable while uploading
                            >
                              {loadingField === 'proof' ? 'Uploading...' : 'Upload'}
                            </LoadingButton>
                          ))
                        : []}
                    </Stack>
                    {touched.proof && errors.proof && typeof errors.proof === 'string' && (
                      <Typography color="red" fontSize="12px" mt={1} ml={2.5} variant="subtitle1">
                        {errors.proof}
                      </Typography>
                    )}
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <Stack
                      direction="row"
                      alignItems={successIconShow.cast === 'Success' ? 'end' : cast === '' ? 'end' : 'center'}
                      gap={2}
                    >
                      <FormControl fullWidth>
                        <ImageCropper
                          uploadedFile={uploadedFile}
                          setUploaded={setUploaded}
                          uploaded={uploaded}
                          setIsDialogOpen={setIsDialogOpen}
                          isDialogOpen={isDialogOpen}
                          // onImageUpload={handleImageUpload}
                          uploadButton={
                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Caste Certificate (Father)
                              </Typography>
                              <InputFileUpload
                                name="upload"
                                uploadedFile={
                                  Array.isArray(uploaded) ? uploaded.filter((f) => f.fieldName === 'cast') : []
                                }
                                buttonLabel="Choose"
                                accept="image/*,application/pdf"
                                disabledTextfeild="disabled"
                                disabledUploadButton={
                                  successIconShow.cast === 'Success' || submitting || availableSeatError
                                }
                                fieldName="cast"
                                handleRemoveFile={() => handleRemoveFile('cast')}
                                onChange={(event) => handleImageUpload(event, 'cast')}
                                inputRef={fileInputRef} // Pass the ref to the file input
                              />
                            </FormControl>
                          }
                        />
                      </FormControl>
                      {Array.isArray(uploaded)
                        ? uploaded.find((f) => f.fieldName === 'cast') &&
                          (successIconShow.cast === 'Success' ? (
                            <Stack width={120} alignItems="center">
                              <Lottie animationData={successIcon} loop={false} style={{ width: '27px' }} />
                              <Typography variant="subtitle2" fontSize={8} color={theme.palette.success.main}>
                                Uploaded
                              </Typography>
                            </Stack>
                          ) : (
                            <LoadingButton
                              //  loadingPosition='start'
                              loading={loadingField === 'cast'}
                              startIcon={<CloudUploadIcon />}
                              onClick={() => onUpload('cast')}
                              variant="outlined"
                              color="success"
                              sx={{ width: 112, fontSize: 10, height: 38 }}
                              disabled={loadingField === 'cast'} // Disable while uploading
                            >
                              {loadingField === 'cast' ? 'Uploading...' : 'Upload'}
                            </LoadingButton>
                          ))
                        : []}
                    </Stack>
                    {touched.cast && errors.cast && typeof errors.cast === 'string' && (
                      <Typography color="red" fontSize="12px" mt={1} ml={2.5} variant="subtitle1">
                        {errors.cast}
                      </Typography>
                    )}
                  </Grid>
                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <Stack
                      direction="row"
                      alignItems={
                        successIconShow.firstSemesterMarkSheet === 'Success'
                          ? 'end'
                          : firstSemesterMarkSheet === ''
                          ? 'end'
                          : 'center'
                      }
                      gap={2}
                    >
                      <FormControl fullWidth>
                        <ImageCropper
                          uploadedFile={uploadedFile}
                          setUploaded={setUploaded}
                          uploaded={uploaded}
                          setIsDialogOpen={setIsDialogOpen}
                          isDialogOpen={isDialogOpen}
                          // onImageUpload={handleImageUpload}
                          uploadButton={
                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                First Semester Mark Sheet * 
                              </Typography>
                              <InputFileUpload
                                name="upload"
                                uploadedFile={
                                  Array.isArray(uploaded)
                                    ? uploaded.filter((f) => f.fieldName === 'firstSemesterMarkSheet')
                                    : []
                                }
                                buttonLabel="Choose"
                                accept="image/*,application/pdf"
                                disabledTextfeild="disabled"
                                disabledUploadButton={
                                  successIconShow.firstSemesterMarkSheet === 'Success' ||
                                  submitting ||
                                  availableSeatError
                                }
                                fieldName="firstSemesterMarkSheet"
                                handleRemoveFile={() => handleRemoveFile('firstSemesterMarkSheet')}
                                onChange={(event) => handleImageUpload(event, 'firstSemesterMarkSheet')}
                                inputRef={fileInputRef} // Pass the ref to the file input
                              />
                            </FormControl>
                          }
                        />
                      </FormControl>
                      {Array.isArray(uploaded)
                        ? uploaded.find((f) => f.fieldName === 'firstSemesterMarkSheet') &&
                          (successIconShow.firstSemesterMarkSheet === 'Success' ? (
                            <Stack width={120} alignItems="center">
                              <Lottie animationData={successIcon} loop={false} style={{ width: '27px' }} />
                              <Typography variant="subtitle2" fontSize={8} color={theme.palette.success.main}>
                                Uploaded
                              </Typography>
                            </Stack>
                          ) : (
                            <LoadingButton
                              //  loadingPosition='start'
                              loading={loadingField === 'firstSemesterMarkSheet'}
                              startIcon={<CloudUploadIcon />}
                              onClick={() => onUpload('firstSemesterMarkSheet')}
                              variant="outlined"
                              color="success"
                              sx={{ width: 112, fontSize: 10, height: 38 }}
                              disabled={loadingField === 'firstSemesterMarkSheet'} // Disable while uploading
                            >
                              {loadingField === 'firstSemesterMarkSheet' ? 'Uploading...' : 'Upload'}
                            </LoadingButton>
                          ))
                        : []}
                    </Stack>
                    {touched.firstSemesterMarkSheet &&
                      errors.firstSemesterMarkSheet &&
                      typeof errors.firstSemesterMarkSheet === 'string' && (
                        <Typography color="red" fontSize="12px" mt={1} ml={2.5} variant="subtitle1">
                          {errors.firstSemesterMarkSheet}
                        </Typography>
                      )}
                  </Grid>
                </Grid>
              </Box>
            </TabPanel>
            <Box sx={{ display: 'flex', gap: 2, my: 5, justifyContent: 'center', textAlign: 'center' }}>
              {activeTab !== 0 && (
                <Button
                  variant="outlined"
                  color="secondary"
                  sx={{ my: 3, width: 100 }}
                  onClick={() => {
                    scrollToTabPanel();
                    setActiveTab((prev) => (prev > 0 ? prev - 1 : prev));
                  }} // Decrement tab
                  startIcon={<ArrowBack />} // ⬅️ Back Arrow Icon
                >
                  Back
                </Button>
              )}
              <Button
                variant="outlined"
                color="info"
                sx={{ my: 3, width: 100 }}
                type={activeTab === 5 ? 'submit' : 'button'}
                onClick={() => {
                  if (
                    (isAnyRequiredFieldEmptyStep1 && activeTab === 5) ||
                    (isAnyRequiredFieldEmptyStep2 && activeTab === 5)
                  ) {
                    setCellValidationError(true);
                  }
                  scrollToTabPanel();
                  setActiveTab((prev) => (prev < 5 ? prev + 1 : prev));
                }} // Increment tab
                endIcon={activeTab === 5 ? <CheckCircle /> : <ArrowForward />} // ➡️ Next Arrow Icon
              >
                {activeTab === 5 ? 'Submit' : 'Next'}
              </Button>
            </Box>
          </Stack>
        </Stack>
      </AdmissionFormRoot>
      {submitting && (
        <LoadingPopup
          // title="Message Creating"
          popupContent={<LoadingMessage icon={Loading} message="Enquiry Processing Please Wait..." />}
        />
      )}
      {/* <Popup
        size="xs"
        state={availableSeatError}
        // onClose={() => setVideoPopup(false)}
        popupContent={
          <Stack px={3} alignItems="center">
            <Typography color="red" fontSize="16px" mt={1} variant="subtitle1">
             Admission closed
            </Typography>
            <Button
              variant="outlined"
              color="secondary"
              sx={{ my: 3, width: 100 }}
              onClick={() => {
                setAvailableSeatError(false);
              }} // Decrement tab
            >
              Ok
            </Button>
          </Stack>
        }
      /> */}
      <Snackbar
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
        open={cellValidationError}
        autoHideDuration={3000}
        onClose={() => setCellValidationError(false)}
        // message="Receipt number already exist"
      >
        <Alert severity="error" variant="filled">
          <Typography variant="subtitle2" display="flex" alignItems="center">
            Fill the required field
          </Typography>
        </Alert>
      </Snackbar>
    </Page>
  );
}

export default AdmissionForm;
