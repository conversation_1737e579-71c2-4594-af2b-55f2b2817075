/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Box,
  Chip,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  Collapse,
  FormControl,
} from '@mui/material';
import styled from 'styled-components';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import SearchIcon from '@mui/icons-material/Search';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import DeleteIcon from '@mui/icons-material/Delete';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import { STATUS_SELECT, YEAR_SELECT } from '@/config/Selection';
import typography from '@/theme/typography';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import DateSelect from '@/components/shared/Selections/DateSelect';

const ManageHolidaysRoot = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 240px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 50px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

export const data = [
  {
    SlNo: 1,
    Year: '2023-24',
    Date: '31/02/2023',
    Description: 'Holiday Name',
    Status: 1,
  },
  {
    SlNo: 2,
    Year: '2023-24',
    Date: '31/02/2023',
    Description: 'Holiday Name',
    Status: 0,
  },
  {
    SlNo: 3,
    Year: '2023-24',
    Date: '31/02/2023',
    Description: 'Holiday Name',
    Status: 1,
  },
  {
    SlNo: 4,
    Year: '2023-24',
    Date: '31/02/2023',
    Description: 'Holiday Name',
    Status: 1,
  },
  {
    SlNo: 5,
    Year: '2023-24',
    Date: '31/02/2023',
    Description: 'Holiday Name',
    Status: 0,
  },
  {
    SlNo: 6,
    Year: '2023-24',
    Date: '31/02/2023',
    Description: 'Holiday Name',
    Status: 1,
  },
  {
    SlNo: 7,
    Year: '2023-24',
    Date: '31/02/2023',
    Description: 'Holiday Name',
    Status: 0,
  },
  {
    SlNo: 8,
    Year: '2023-24',
    Date: '31/02/2023',
    Description: 'Holiday Name',
    Status: 1,
  },
  {
    SlNo: 9,
    Year: '2023-24',
    Date: '31/02/2023',
    Description: 'Holiday Name',
    Status: 1,
  },
  {
    SlNo: 10,
    Year: '2023-24',
    Date: '31/02/2023',
    Description: 'Holiday Name',
    Status: 1,
  },
  {
    SlNo: 11,
    Year: '2023-24',
    Date: '31/02/2023',
    Description: 'Holiday Name',
    Status: 0,
  },
  {
    SlNo: 12,
    Year: '2023-24',
    Date: '31/02/2023',
    Description: 'Holiday Name',
    Status: 1,
  },
  {
    SlNo: 13,
    Year: '2023-24',
    Date: '31/02/2023',
    Description: 'Holiday Name',
    Status: 1,
  },
];

function ManageHolidays() {
  const [drawerOpen, setDrawerOpen] = React.useState<boolean>(false);
  const [showFilter, setShowFilter] = useState(false);
  const [showSend, setShowSend] = useState(false);

  const handleOpen = () => {
    setDrawerOpen(true);
  };

  const toggleDrawerClose = useCallback(() => setDrawerOpen(false), []);

  const getRowKey = useCallback((row: any) => row.examId, []);
  const ManageHolidaysColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'slNo',
        dataKey: 'SlNo',
        headerLabel: 'Sl.No',
        sortable: true,
      },
      {
        name: 'year',
        dataKey: 'Year',
        headerLabel: 'Year',
      },
      {
        name: 'date',
        dataKey: 'Date',
        headerLabel: 'Date',
      },
      {
        name: 'description',
        dataKey: 'Description',
        headerLabel: 'Description',
      },
      {
        name: 'status',
        dataKey: 'Status',
        headerLabel: 'Status',
        renderCell: (row) => {
          return (
            <Chip
              size="small"
              label={row.yearStatus === 0 ? 'Unpublished' : 'Published'}
              variant="outlined"
              color={row.yearStatus === 0 ? 'error' : 'success'}
            />
          );
        },
      },
      {
        name: 'actions',
        headerLabel: 'Actions',
        renderCell: () => {
          return (
            <Stack direction="row" gap={1}>
              <IconButton size="small" onClick={handleOpen} sx={{ padding: 0.5 }}>
                <ModeEditIcon />
              </IconButton>
              <IconButton size="small" color="error" onClick={handleOpen} sx={{ padding: 0.5 }}>
                <DeleteIcon />
              </IconButton>
            </Stack>
          );
        },
      },
    ],
    []
  );

  return (
    <Page title="List">
      <ManageHolidaysRoot>
        <Card elevation={1} sx={{ px: { xs: 3, md: 5 }, py: 2, mb: 3 }}>
          <Stack direction="row" spacing={2} alignItems="center">
            <Typography variant="h6" fontSize={17}>
              Add New Holiday
            </Typography>
            <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowSend((x) => !x)}>
              {showSend ? <ExpandLessIcon /> : <KeyboardArrowRightIcon />}
            </IconButton>
          </Stack>
          <Divider />
          <Collapse in={showSend}>
            <form noValidate>
              <Grid pb={4} pt={2} container rowSpacing={1} columnSpacing={3}>
                <Grid item lg={6} xs={12}>
                  <FormControl sx={{ minWidth: { xs: '100%' } }}>
                    <Typography variant="h6" fontSize={14}>
                      Date
                    </Typography>
                    <DateSelect />
                  </FormControl>
                </Grid>
                <Grid item lg={6} xs={12} mt="auto" pb="1px">
                  <FormControl sx={{ minWidth: { xs: '100%' } }}>
                    <Typography variant="h6" fontSize={14}>
                      Academic Year
                    </Typography>
                    <Autocomplete
                      options={YEAR_SELECT}
                      renderInput={(params) => <TextField {...params} placeholder="Select Year" />}
                    />
                  </FormControl>
                </Grid>
                <Grid item lg={6} xs={12} mt="auto" pb="1px">
                  <FormControl sx={{ minWidth: { xs: '100%' } }}>
                    <Typography variant="h6" fontSize={14}>
                      Type
                    </Typography>
                    <Autocomplete
                      options={['Working Day', 'Holiday']}
                      renderInput={(params) => <TextField {...params} placeholder="Select Type" />}
                    />
                  </FormControl>
                </Grid>
                <Grid item xs={12}>
                  <FormControl sx={{ minWidth: { xs: '100%' } }}>
                    <Typography variant="h6" fontSize={14}>
                      Description
                    </Typography>
                    <TextField placeholder="Enter Description" multiline minRows={2} />
                  </FormControl>
                </Grid>
              </Grid>
            </form>
            <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' } }}>
              <Stack spacing={2} direction="row">
                <Button variant="contained" color="secondary" sx={{ fontFamily: typography.fontFamily }}>
                  Cancel
                </Button>
                <Button variant="contained" color="primary">
                  Add
                </Button>
              </Stack>
            </Box>
          </Collapse>
        </Card>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: 2 }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={19}>
              Holidays List
            </Typography>
            <Box sx={{ flexShrink: 0 }}>
              <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                <SearchIcon />
              </IconButton>
            </Box>
          </Stack>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={3} alignItems="end">
                  <Grid item lg="auto" md={6} xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', lg: 250 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Holiday Date
                      </Typography>
                      <DateSelect />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" md={6} xs={12}>
                    <Typography variant="h6" fontSize={14}>
                      Academic Year
                    </Typography>
                    <Autocomplete
                      options={YEAR_SELECT}
                      renderInput={(params) => <TextField {...params} placeholder="Select Year" />}
                    />
                  </Grid>
                  <Grid item lg="auto" md={6} xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', lg: 250 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Status
                      </Typography>
                      <Autocomplete
                        options={STATUS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select" />}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item lg="auto" xs={12}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Paper className="card-table-container" sx={{ marginTop: '5px' }}>
              <DataTable columns={ManageHolidaysColumns} data={data} getRowKey={getRowKey} fetchStatus="success" />
            </Paper>
          </div>
          {/* <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 2 }}>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="secondary">
                Cancel
              </Button>
              <Button onClick={handleOpen} variant="contained" color="primary">
                Save
              </Button>
            </Stack>
          </Box> */}
        </Card>
      </ManageHolidaysRoot>

      <TemporaryDrawer onClose={toggleDrawerClose} Title="Upload Details" state={drawerOpen} DrawerContent="" />
    </Page>
  );
}

export default ManageHolidays;
