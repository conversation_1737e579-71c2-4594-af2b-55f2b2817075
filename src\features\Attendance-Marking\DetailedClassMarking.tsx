/* eslint-disable jsx-a11y/alt-text */
import React, { FormEvent, useCallback, useEffect, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  Button,
  Typography,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Card,
  IconButton,
  Collapse,
  FormControl,
  Chip,
  Select,
  MenuItem,
  Tooltip,
  useTheme,
} from '@mui/material';
import styled from 'styled-components';
import NoData from '@/assets/no-datas.png';
import { sessionDetailedHead } from '@/config/TableData';
import { CloseIcon, SuccessIcon } from '@/theme/overrides/CustomIcons';
import RemoveCircleOutlineIcon from '@mui/icons-material/RemoveCircleOutline';
import Error from '@/assets/AttendanceIcons/error.gif';
import Present from '@/assets/AttendanceIcons/present.gif';
import Presented from '@/assets/AttendanceIcons/presented.gif';
import Loading from '@/assets/AttendanceIcons/loading.gif';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import { IoIosArrowUp } from 'react-icons/io';
import { AttendanceListInfo, AttendanceListRequest, MarkAttendance } from '@/types/AttendanceMarking';
import { useAppSelector } from '@/hooks/useAppSelector';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import SearchIcon from '@mui/icons-material/Search';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import useAuth from '@/hooks/useAuth';
import {
  getAttendanceListData,
  getAttendanceListStatus,
  getAttendanceListSubmitting,
  getClassData,
  getYearData,
} from '@/config/storeSelectors';
import { fetchClassList, fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import { MarkAbsentees, MarkPresent, fetchAttendanceList } from '@/store/AttendanceMarking/attendanceMarking.thunks';
import dayjs, { Dayjs } from 'dayjs';
import { ErrorMessage } from '@/components/shared/Popup/ErrorMessage';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import DatePickers from '@/components/shared/Selections/DatePicker';
import LoadingPopup from '@/components/shared/Popup/LoadingPopup';
import { LoadingMessage } from '@/components/shared/Popup/LoadingMessage';
import LoadingButton from '@mui/lab/LoadingButton';
import DateSelect from '@/components/shared/Selections/DateSelect';

const DetailedClassMarkingRoot = styled.div`
  padding: 1rem;
  @media screen and (max-width: 576px) {
    padding: 0.5rem;
  }
  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 1300px) {
      height: calc(100vh - 30px);
    }
    @media screen and (max-width: 1180px) {
      height: 100%;
    }
    .main-card-table-container {
      flex-grow: 1;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: row;
      overflow: auto;
      /* height: calc(100vh - 390px); */
      @media screen and (max-width: 1180px) {
        /* height: calc(100vh - 250px); */
        flex-direction: column;
      }
      @media screen and (max-width: 768px) {
        /* height: calc(100vh - 235px); */
      }
    }
    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 30px);
      flex-grow: 1;
      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: auto;
        height: calc(100vh - 420px);
        @media screen and (max-width: 1180px) {
          height: calc(100vh - 300px);
        }

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
      .card-absenttable-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: auto;
        height: calc(100vh - 420px);
        @media screen and (max-width: 1180px) {
          height: calc(100vh - 300px);
        }
        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
  .student_name {
    width: 200px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    font-size: 14px;
    font-weight: 600;
  }
  .student_name2 {
    width: 110px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    font-size: 14px;
    font-weight: 600;
  }
`;

// interface EnhancedTableProps {
//   handleSelectAllClick: (event: React.ChangeEvent<HTMLInputElement>) => void;
//   selected: AttendanceListInfo[];
// }
// function EnhancedTableHead(props: EnhancedTableProps) {
//   const { handleSelectAllClick, selected } = props;
//   return (
//     <TableHead>
//       <TableRow>
//         <TableCell padding="checkbox">
//           <Checkbox
//             indeterminate={selected.length > 0 && selected.length < studentsDetails.length}
//             checked={selected.length === studentsDetails.length}
//             onChange={handleSelectAllClick}
//             inputProps={{ 'aria-label': 'select all rows' }}
//           />
//         </TableCell>
//         {sessionDetailedHead.map((headCell) => (
//           <TableCell key={headCell.id}>{headCell.label}</TableCell>
//         ))}
//       </TableRow>
//     </TableHead>
//   );
// }

function DetailedClassMarking() {
  const theme = useTheme();
  const dispatch = useAppDispatch();
  const { user } = useAuth();
  const { confirm } = useConfirm();
  const adminId: number = user ? user.accountId : 0;
  const [showFilter, setShowFilter] = useState(true);
  const [yearFilter, setYearFilter] = useState(11);
  const [classFilter, setClassFilter] = useState(0);
  const [dateFilter, setDateFilter] = useState<Dayjs | string>(dayjs(new Date()).format('DD/MM/YYYY'));
  // const [selectedDate, setSelectedDate] = useState<Dayjs | string>(dayjs(new Date()).format('DD/MM/YYYY'));
  // const [selectedDate, setSelectedDate] = useState<Dayjs | string>(dayjs(new Date()).format('DD/MM/YYYY'));
  const YearData = useAppSelector(getYearData);
  const ClassData = useAppSelector(getClassData);
  const [markPresent, setMarkPresent] = useState<Record<string, boolean>>({});
  const AttendanceListData = useAppSelector(getAttendanceListData);
  const [isLoading, setIsloading] = useState<boolean>(false);
  // Get the current date
  const currentDate = dayjs();

  // Calculate the max selectable date (current date)
  const maxDate = currentDate;
  // const handleDateChange = (date: Dayjs | null) => {
  //   if (date) {
  //     setDateFilter(date.format('DD/MM/YYYY')); // Format date as string when setting the state
  //   } else {
  //     setDateFilter(''); // Handle case when no date is selected
  //   }
  // };

  console.log(
    'attendanceListData',
    AttendanceListData.filter((item) => item.attendanceId !== 0)
  );
  const AttendanceListStatus = useAppSelector(getAttendanceListStatus);
  const isSubmitting = useAppSelector(getAttendanceListSubmitting);
  // const [popup, setPopup] = React.useState(false);
  const [selectedABS, setSelectedABS] = useState<AttendanceListInfo[]>([]);

  const currentAttendanceListRequest: AttendanceListRequest = React.useMemo(
    () => ({
      accademicId: yearFilter,
      adminId,
      classId: classFilter,
      absentDate: dateFilter,
    }),
    [adminId, yearFilter, classFilter, dateFilter]
  );
  // useEffect(() => {
  //   const data = JSON.parse(localStorage.getItem('absenteesData') || '[]');
  //   setSelectedABS(data);
  //   console.log('abssss', selectedABS);
  // }, [selectedABS]);

  const loadAttendanceList = useCallback(
    async (request: AttendanceListRequest) => {
      try {
        // const data = await Promise.all([dispatch(fetchAttendanceList(request)), setSelectedABS([])]);
        const data = await dispatch(fetchAttendanceList(request));
        // setSelectedABS([]);
        console.log(data);
      } catch (error) {
        console.error('Error loading list:', error);
      }
    },
    [dispatch]
  );
  React.useEffect(() => {
    console.log('dateFilter', dateFilter);
    dispatch(fetchClassList(adminId));
    dispatch(fetchYearList(adminId));
    if (AttendanceListStatus === 'idle') {
      loadAttendanceList(currentAttendanceListRequest);
    }
    // loadAttendanceList(currentAttendanceListRequest);
    // dispatch(
    //   fetchAttendanceList({
    //     accademicId: 10,
    //     adminId,
    //     classId: 1,
    //     absentDate: dateFilter,
    //   })
    // );
  }, [dispatch, adminId, currentAttendanceListRequest, AttendanceListStatus, dateFilter, loadAttendanceList]);

  // const handleClickOpen = () => setPopup(true);
  // const handleClickClose = () => setPopup(false);

  // const handleSearch = () => {
  //   const findSudents: AttendanceListInfo[] | undefined =
  //     studentsDetails && studentsDetails?.length > 0
  //       ? studentsDetails?.filter((list) => list?.year === year && list?.class === Class)
  //       : undefined;
  //   setStudentData(findSudents);
  //   console.log('filterdata', findSudents);
  // };

  // const handleSelectAllClick = (event: React.ChangeEvent<HTMLInputElement>) => {
  //   if (event.target.checked) {
  //     setSelectedABS(studentsDetails);
  //   } else {
  //     setSelectedABS([]);
  //   }
  // };

  // const handleRowClick = (row: AttendanceListInfo) => {
  //   const selectedIndex = selected.indexOf(row);
  //   let newSelected: AttendanceListInfo[] = [];

  //   if (selectedIndex === -1) {
  //     newSelected = newSelected.concat(selected, row);
  //   } else if (selectedIndex === 0) {
  //     newSelected = newSelected.concat(selected.slice(1));
  //   } else if (selectedIndex === selected.length - 1) {
  //     newSelected = newSelected.concat(selected.slice(0, -1));
  //   } else if (selectedIndex > 0) {
  //     newSelected = newSelected.concat(selected.slice(0, selectedIndex), selected.slice(selectedIndex + 1));
  //   }
  //   setSelected(newSelected);
  // };

  const handleRowClick = (row: AttendanceListInfo) => {
    const selectedIndex = selectedABS.indexOf(row);
    let newSelected: AttendanceListInfo[] = [];

    if (selectedIndex === -1) {
      newSelected = newSelected.concat(selectedABS, row);
    } else if (selectedIndex === 0) {
      newSelected = newSelected.concat(selectedABS.slice(1));
    } else if (selectedIndex === selectedABS.length - 1) {
      newSelected = newSelected.concat(selectedABS.slice(0, -1));
    } else if (selectedIndex > 0) {
      newSelected = newSelected.concat(selectedABS.slice(0, selectedIndex), selectedABS.slice(selectedIndex + 1));
    }

    setSelectedABS(newSelected);
  };

  // const isSelected = (row: AttendanceListInfo) => selectedABS.indexOf(row) !== -1;

  const isButtonDisabled = selectedABS.length === 0;
  const isCancell = () => setSelectedABS([]);

  const handleSubmit = useCallback(async () => {
    setIsloading(true);
    try {
      const markAbsentRequest: MarkAttendance[] = await Promise.all(
        selectedABS?.map(async (item) => {
          const {
            studentName,
            studentImage,
            classId,
            gender,
            dateOfBirth,
            voiceId,
            absents,
            attendanceId,
            rollNo,
            ...rest
          } = item;
          const sendReq = {
            accademicId: yearFilter,
            absentDate: dateFilter,
            adminId,
            classId,
            absentStatus: '',
            rollNo: parseInt(rollNo, 10),
            ...rest,
          };
          return sendReq;
        }) || []
      );
      const actionResult = await dispatch(MarkAbsentees(markAbsentRequest));
      console.log('actionResult', actionResult);
      if (actionResult && Array.isArray(actionResult.payload)) {
        // setSendResults(results);
        // setSelectedABS([...selectedABS, ...results]);
        const successMessage = (
          <SuccessMessage icon={Presented} message="Attendance Marked and Message sent to Parents Successfully" />
        );
        setSelectedABS([]);
        await confirm(successMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        loadAttendanceList(currentAttendanceListRequest);
      } else {
        const errorMessage = (
          <ErrorMessage icon={Error} message="Error while marking absents and sending messages, Please Try again" />
        );

        await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
      }
    } catch (error) {
      console.error('Error mark absent:', error);
      const errorMessage = <ErrorMessage icon={Error} message="Error something went wrong, Please Try again" />;
      await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
    }
  }, [
    confirm,
    dispatch,
    selectedABS,
    adminId,
    dateFilter,
    yearFilter,
    loadAttendanceList,
    currentAttendanceListRequest,
  ]);

  const markToPresent = useCallback(
    async (obj: AttendanceListInfo) => {
      setIsloading(false);
      if (markPresent[obj.classId]) {
        return;
      }

      const sendConfirmMessage = (
        <DeleteMessage
          icon={Present}
          message={<div>Are you sure you want to Mark &quot;{obj.studentName}&quot; as Present ?</div>}
        />
      );
      if (await confirm(sendConfirmMessage, 'Mark To Present?', { okLabel: 'Yes', cancelLabel: 'Cancel' })) {
        // Set loading state for the specific row
        setMarkPresent((prevMap) => ({ ...prevMap, [obj.studentId]: true }));

        const response = await dispatch(
          MarkPresent({ adminId, studentId: obj.studentId, classId: obj.classId, attendanceId: obj.attendanceId })
        ).unwrap();
        console.log('response', response);
        // Set loading state for the specific row
        setMarkPresent((prevMap) => ({ ...prevMap, [obj.studentId]: false }));

        if (response.result) {
          const deleteDoneMessage = <SuccessMessage icon={Presented} message="Attendance Changed successfully." />;
          await confirm(deleteDoneMessage, 'Marked to Present', { okLabel: 'Ok', showOnlyOk: true });
          loadAttendanceList(currentAttendanceListRequest);
        } else {
          const deleteErrorMessage = <DeleteMessage message="Attendance not changed, Try Again" />;
          await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
        }
      }
    },
    [confirm, dispatch, loadAttendanceList, adminId, currentAttendanceListRequest, markPresent]
  );

  const handleReset = useCallback(
    async (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      setSelectedABS([]);
      // setClassFilter(ClassData[0].classId);
      setClassFilter(1);
      setYearFilter(parseInt(YearData[0].accademicId, 10));
      // setDateFilter(dayjs(new Date()).format('DD/MM/YYYY'));
      await dispatch(
        fetchAttendanceList({
          accademicId: parseInt(YearData[0].accademicId, 10),
          adminId,
          classId: 1,
          absentDate: dayjs(new Date()).format('DD/MM/YYYY'),
        })
      );
    },
    [YearData, adminId, dispatch]
  );

  // const getRowStyle = useCallback(
  //   (row: AttendanceListInfo) => ({
  //     backgroundColor: selectedABS.some((selectedRow) => selectedRow.attendanceId === row.attendanceId) ? '#e0f3ff' : '#fff',
  //   }),
  //   [selectedABS]
  // );

  const getRowKey = useCallback((row: AttendanceListInfo) => row.studentId, []);

  // const handleSort = useCallback(
  //   (column: string) => {
  //     const newReq = { ...currentClassListRequest };
  //     if (column === sortColumn) {
  //       console.log('Toggling direction');
  //       const sortDirectionNew = sortDirection === 'asc' ? 'desc' : 'asc';
  //       newReq.pageNumber = 1;
  //       newReq.sortDirection = sortDirectionNew;
  //       dispatch(setSortDirection(sortDirectionNew));
  //     } else {
  //       newReq.pageNumber = 1;
  //       newReq.sortColumn = column;
  //       dispatch(setSortColumn(column));
  //     }

  //     loadClassList(newReq);
  //   },
  //   [currentClassListRequest, dispatch, loadClassList, sortColumn, sortDirection]
  // );
  const classListColumns: DataTableColumn<AttendanceListInfo>[] = useMemo(
    () => [
      {
        name: 'rollNo',
        headerLabel: 'Roll No',
        dataKey: 'rollNo',
        sortable: true,
      },
      {
        name: 'studentName',
        dataKey: 'studentName',
        headerLabel: 'Student Name',
      },
      {
        name: 'attendanceStatus',
        headerLabel: 'Status',
        sortable: true,
        renderCell: (row) => {
          return row.attendanceId > 0 ? (
            <Chip size="small" icon={<CloseIcon />} label="Absent" variant="filled" color="error" />
          ) : (
            <Chip icon={<SuccessIcon />} size="small" label="Present" variant="filled" color="success" />
          );
        },
      },
    ],
    []
  );

  // useEffect(() => {
  //   // Set the initial value of selectedDate to the current date
  //   const currentDate = dayjs();
  //   const formattedCurrentDate = currentDate.format('DD/MM/YYYY');
  //   setSelectedDate(formattedCurrentDate);
  // }, []);
  // const handleDateChange = (date: Dayjs | string | null) => {
  //   setSelectedDate(date);
  //   console.log('date', date);
  //   console.log('selectedDate', selectedDate);
  //   loadAttendanceList({ ...currentAttendanceListRequest, absentDate: selectedDate });
  // };

  return (
    <Page title="Detailed Class Marking">
      <DetailedClassMarkingRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17}>
              Detailed Class Marking
            </Typography>
            <Box pb={1} sx={{ flexShrink: 0 }}>
              {showFilter === false ? (
                <Tooltip title="Search">
                  <IconButton
                    aria-label="delete"
                    color="primary"
                    sx={{ mr: { xs: 0, sm: 1 } }}
                    onClick={() => setShowFilter((x) => !x)}
                  >
                    <SearchIcon />
                  </IconButton>
                </Tooltip>
              ) : (
                <IconButton
                  aria-label="delete"
                  color="primary"
                  sx={{ mr: { xs: 0, sm: 1 } }}
                  onClick={() => setShowFilter((x) => !x)}
                >
                  <IoIosArrowUp />
                </IconButton>
              )}
            </Box>
          </Stack>
          <Divider sx={{ marginBottom: 1 }} />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate onReset={handleReset}>
                <Grid pb={4} container spacing={2} alignItems="end">
                  <Grid item xxl={2} xl={2.5} lg={3} md={6} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Select Year
                      </Typography>
                      <Select
                        name="yearFilter"
                        value={yearFilter.toString()}
                        onChange={(e) => {
                          setSelectedABS([]);
                          setYearFilter(
                            parseInt(YearData.filter((item) => item.accademicId === e.target.value)[0].accademicId, 10)
                          );
                          loadAttendanceList({
                            ...currentAttendanceListRequest,
                            accademicId: parseInt(e.target.value, 10),
                          });
                        }}
                      >
                        {YearData.map((opt) => (
                          <MenuItem key={opt.accademicId} value={opt.accademicId}>
                            {opt.accademicTime}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={3} md={6} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Select Class
                      </Typography>
                      <Select
                        name="classFilter"
                        value={classFilter.toString()}
                        MenuProps={{
                          PaperProps: {
                            style: {
                              maxHeight: '270px', // Adjust the value to your desired height
                            },
                          },
                        }}
                        onChange={(e) => {
                          setSelectedABS([]);
                          setClassFilter(
                            ClassData.filter((item) => item.classId === parseInt(e.target.value, 10))[0].classId
                          );
                          loadAttendanceList({
                            ...currentAttendanceListRequest,
                            classId: parseInt(e.target.value, 10),
                          });
                        }}
                      >
                        <MenuItem value={0}>Select class</MenuItem>
                        {ClassData.map((opt) => (
                          <MenuItem sx={{ fontSize: '14px' }} key={opt.classId} value={opt.classId}>
                            {opt.className}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={3} md={6} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Select Date
                      </Typography>
                      <DatePickers
                        maxDate={maxDate}
                        name="dateFilter"
                        value={dayjs(dateFilter, 'DD/MM/YYYY')}
                        onChange={(e) => {
                          const formattedDate = e ? e.format('DD/MM/YYYY') : '';
                          setDateFilter(formattedDate);
                          console.log('date::::', formattedDate);
                          // const formattedDate = e ? e.format('YYYY-MM-DD') : '';
                          // setSelectedDate(formattedDate);
                          loadAttendanceList({ ...currentAttendanceListRequest, absentDate: formattedDate });
                        }}
                      />
                    </FormControl>
                    {/* <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Select Date
                      </Typography>
                      <TextField
                        type="date"
                        name="dateFilter"
                        value={dayjs(selectedDate).format('YYYY-DD-MM')}
                        onChange={(e) => {
                          setSelectedABS([]);
                          const inputDate = e.target.value;
                          const formattedDate = inputDate ? dayjs(inputDate).format('DD/MM/YYYY') : '';
                          console.log('formattedDate', formattedDate);
                          console.log('inputDate', inputDate);
                          setSelectedDate(formattedDate);
                          loadAttendanceList({ ...currentAttendanceListRequest, absentDate: formattedDate });
                        }}
                        inputProps={{ max: dayjs().format('YYYY-MM-DD') }} // Set the max attribute to current date
                      />
                    </FormControl> */}
                  </Grid>
                  <Grid item lg="auto" width={{ xs: '100%', md: '' }}>
                    <Stack
                      direction="row"
                      sx={{
                        marginBottom: '2px',
                        pt: { xs: 0, lg: 3.79 },
                        justifyContent: { xs: 'flex-end', md: 'start' },
                        flex: 1,
                      }}
                    >
                      <Button type="reset" variant="contained" color="secondary">
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>
            {/* {studentData && studentData?.length === 0 && (
              <Stack
                direction="row"
                display="flex"
                justifyContent="center"
                alignItems="center"
                height="calc(100vh - 390px)"
                width="100%"
              >
                <Box>
                  <img className="Notsuccess_img_bg" src={NotSuccess} alt="" width={150} />
                  <Typography textAlign="center" pt={2} variant="body2">
                    No data found.
                  </Typography>
                </Box>
              </Stack>
            )} */}
            <Stack className="main-card-table-container" sx={{ width: '100%' }} gap={3}>
              <Stack width="100%">
                <Typography variant="h6" fontSize={16}>
                  Present List
                </Typography>
                <Paper
                  className="card-table-container"
                  sx={{
                    '&::-webkit-scrollbar': {
                      width: 0,
                    },
                  }}
                >
                  <DataTable
                    tableStyles={{ width: { xs: '450px', sm: '100%' } }}
                    columns={classListColumns}
                    data={AttendanceListData.filter((item) => item.attendanceId === 0)}
                    getRowKey={getRowKey}
                    fetchStatus="success"
                    // deletingRecords={deletingRecords}
                    // sortColumn={sortColumn}
                    // sortDirection={sortDirection}
                    // onSort={handleSort}
                    // handleRowClick={handleRowClick}
                    // isSelected={isSelected}
                    allowSorting
                    ShowCheckBox
                    selectedRows={selectedABS}
                    setSelectedRows={setSelectedABS}
                    RowSelected
                    isCancell={isCancell}
                  />
                </Paper>
              </Stack>
              <Stack width="100%">
                <Typography variant="h6" fontSize={16}>
                  Absentees List
                </Typography>
                <Paper sx={{ border: 1, borderColor: theme.palette.grey[300] }} className="card-absenttable-container">
                  {AttendanceListData.filter((item) => item.attendanceId > 0).length > 0 || selectedABS?.length > 0 ? (
                    <TableContainer
                      sx={{
                        height: 'calc(100vh - 376px)',
                      }}
                    >
                      <Table stickyHeader sx={{ width: { xs: '550px', sm: '100%' } }}>
                        <TableHead>
                          <TableRow>
                            {sessionDetailedHead.map((headCell) => (
                              <TableCell key={headCell.id}>{headCell.label}</TableCell>
                            ))}
                            <TableCell> </TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {AttendanceListData.filter((item) => item.attendanceId > 0).map((row) => (
                            <TableRow sx={{}}>
                              <TableCell sx={{ fontWeight: 600 }}>{row.rollNo}</TableCell>
                              <TableCell>
                                <Typography className="student_name2"> {row.studentName}</Typography>
                              </TableCell>
                              <TableCell sx={{ color: 'red', fontWeight: 600 }}>
                                <Chip icon={<CloseIcon />} size="small" label="Absent" variant="filled" color="error" />
                              </TableCell>
                              <TableCell align="center">
                                <LoadingButton
                                  onClick={() => markToPresent(row)}
                                  variant="outlined"
                                  size="small"
                                  color="success"
                                  disabled={isSubmitting}
                                  loading={markPresent[row.studentId]}
                                >
                                  Mark to Present
                                </LoadingButton>
                                {/* <Button variant="outlined" color="error" onClick={() => markToPresent(row)}>
                                Mark to Present
                              </Button> */}
                              </TableCell>
                            </TableRow>
                          ))}
                          {selectedABS?.map((row) => (
                            <TableRow>
                              <TableCell sx={{ fontWeight: 600 }}>{row.rollNo}</TableCell>
                              <TableCell>
                                <Typography className="student_name2"> {row.studentName}</Typography>
                              </TableCell>
                              <TableCell sx={{ color: 'red', fontWeight: 600 }}>
                                <Chip icon={<CloseIcon />} size="small" label="Absent" variant="filled" color="error" />
                              </TableCell>
                              <TableCell align="center">
                                <IconButton size="small" onClick={() => handleRowClick(row)}>
                                  <RemoveCircleOutlineIcon sx={{ color: 'red' }} />
                                </IconButton>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  ) : (
                    <Stack
                      direction="column"
                      display="flex"
                      justifyContent="center"
                      alignItems="center"
                      width="100%"
                      height="100%"
                    >
                      <Stack direction="column" alignItems="center">
                        <img src={NoData} width="150px" alt="" />
                        <Typography variant="subtitle2" mt={2} color="GrayText">
                          Not selected students.
                        </Typography>
                      </Stack>
                    </Stack>
                  )}
                </Paper>
              </Stack>
            </Stack>
            {/* ----------------------- Absentees List Table ------------------------------------------ */}
          </div>
          {/* {selectedABS && selectedABS?.length > 0 && ( */}
          <Box display="flex" sx={{ justifyContent: { xs: 'right' }, pr: { lg: '5' }, pt: 3 }}>
            <Stack spacing={2} direction="row" justifyContent={{ xs: 'ceneter' }} width={{ xs: '100%', sm: 'auto' }}>
              <Button variant="contained" onClick={isCancell} disabled={isButtonDisabled} color="secondary" fullWidth>
                Cancel
              </Button>
              <Button onClick={handleSubmit} disabled={isButtonDisabled} variant="contained" color="primary" fullWidth>
                Confirm
              </Button>
            </Stack>
          </Box>
          {/* )} */}
        </Card>

        {/* <Popup
          size="xs"
          state={popup}
          onClose={handleClickClose}
          popupContent={<SuccessMessage message=" Passed Absentees Marked Successfully" />}
        /> */}
        {isSubmitting && isLoading && (
          <LoadingPopup popupContent={<LoadingMessage icon={Loading} message="Marking absents please wait." />} />
        )}
      </DetailedClassMarkingRoot>
    </Page>
  );
}

export default DetailedClassMarking;
