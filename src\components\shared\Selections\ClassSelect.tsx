import { Autocomplete, TextField } from '@mui/material';
import React, { useCallback, useEffect } from 'react';
import { useAppSelector } from '@/hooks/useAppSelector';
import { getClassListData, getClassListPageInfo, getClassListStatus } from '@/config/storeSelectors';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { fetchClassList } from '@/store/Academics/ClassManagement/classManagement.thunks';
import { ClassListInfo, ClassListRequest } from '@/types/AcademicManagement';

const ClassSelect = () => {
  const dispatch = useAppDispatch();
  const classListStatus = useAppSelector(getClassListStatus);
  const paginationInfo = useAppSelector(getClassListPageInfo);
  console.log(paginationInfo);
  const classData = useAppSelector(getClassListData);
  const classListData = classData.map((item: ClassListInfo) => item.className);

  const loadClassList = useCallback(
    (request: ClassListRequest) => {
      dispatch(fetchClassList(request));
    },
    [dispatch]
  );

  useEffect(() => {
    if (classListStatus === 'idle') {
      loadClassList({
        pageNumber: 1,
        pageSize: 200,
        filters: {
          classStatus: 1,
        },
      });
    }
  }, [loadClassList, classListStatus]);

  return (
    <div>
      <Autocomplete
        options={classListData}
        renderInput={(params) => <TextField {...params} placeholder="Select Class" />}
      />
    </div>
  );
};

export default ClassSelect;
