/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  useTheme,
  IconButton,
  Collapse,
  Tooltip,
  FormControl,
  Chip,
  Snackbar,
  Alert,
  LinearProgress,
  linearProgressClasses,
} from '@mui/material';
import styled from 'styled-components';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import SearchIcon from '@mui/icons-material/Search';
import Popup from '@/components/shared/Popup/Popup';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import Physics from '@/Parent-Side/assets/liveclass/Icons/Physics.svg';
import { MdAdd, MdContentCopy } from 'react-icons/md';
import { CloseIcon } from '@/theme/overrides/CustomIcons';
import { Student, StudentInfoData } from '@/config/StudentDetails';
import useSettings from '@/hooks/useSettings';
import AutorenewOutlinedIcon from '@mui/icons-material/AutorenewOutlined';
import { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import DateSelect from '@/components/shared/Selections/DateSelect';
import MenuEditDelete from '@/components/shared/Selections/MenuEditDelete';
import CreateScheduleList from '@/features/LiveClass/ScheduleList/CreateScheduleList';
import InsertDriveFileIcon from '@mui/icons-material/InsertDriveFile';
import { Select } from '@mui/material';
import { MenuItem } from '@mui/material';

export const data = [
  {
    createdBy: 'Passdaily',
    class: 'VII-B',
    meetingTitle: 'Daily Class',
    meetingID: 'IDGHDGB',
    academicYear: '2023-2024',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
  {
    createdBy: 'Passdaily',
    class: 'VI-C',
    academicYear: '2023-2024',
    meetingID: 'IDGHDGB',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
  {
    createdBy: 'Passdaily',
    class: 'XII-B',
    meetingTitle: 'Peter',
    academicYear: '2023-2024',
    meetingID: 'IDGHDGB',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
  {
    createdBy: 'Passdaily',
    class: 'V-A',
    meetingTitle: 'Daily Class Mic',
    academicYear: '2023-2024',
    meetingID: 'IDGHDGB',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
  {
    createdBy: 'Passdaily',
    class: 'II-B',
    meetingTitle: 'john',
    academicYear: '2023-2024',
    meetingID: 'IDGHDGB',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
  {
    createdBy: 'Passdaily',
    class: 'XII-C',
    meetingTitle: 'Micheal',
    academicYear: '2023-2024',
    meetingID: 'IDGHDGB',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
  {
    createdBy: 'Passdaily',
    class: 'I-B',
    meetingTitle: 'jack',
    academicYear: '2023-2024',
    meetingID: 'IDGHDGB',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
  {
    createdBy: 'Passdaily',
    class: 'VII-A',
    meetingTitle: 'Daily Class',
    academicYear: '2023-2024',
    meetingID: 'IDGHDGB',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
  {
    createdBy: 'Passdaily',
    class: 'VI-C',
    meetingTitle: 'Daily Class',
    academicYear: '2023-2024',
    meetingID: 'IDGHDGB',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
  {
    createdBy: 'Passdaily',
    class: 'X-B',
    meetingTitle: 'Daily Class',
    academicYear: '2023-2024',
    meetingID: 'IDGHDGB',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
];

const BorderLinearProgress = styled(LinearProgress)(({ theme }) => ({
  height: 10,
  borderRadius: 5,
  [`&.${linearProgressClasses.colorPrimary}`]: {
    backgroundColor: theme.palette.grey[theme.palette.mode === 'light' ? 300 : 700],
  },
  [`& .${linearProgressClasses.bar}`]: {
    borderRadius: 5,
    backgroundColor: theme.palette.mode === 'light' ? theme.palette.common.black : '#fff',
  },
}));
const ProgressReportLPUPRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  .student_card {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  }

  .Card {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    min-height: calc(100vh - 110px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 40px);
      flex-grow: 1;

      .card-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: auto;
        ::-webkit-scrollbar {
          width: 0px;
        }

        .MuiTableContainer-root {
          max-height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

function ProgressReportLPUP() {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const [changeView, setChangeView] = useState(false);
  const [showFilter, setShowFilter] = useState(false);
  const [Delete, setDelete] = useState(false);
  const [createPopup, setCreatePopup] = useState(false);

  const [updatedData, setData] = useState(data);

  const ProgressReportLPUPColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'class',
        dataKey: 'class',
        headerLabel: 'Class',
      },
      {
        name: 'startTime',
        dataKey: 'startTime',
        headerLabel: 'Start Time',
      },
      {
        name: 'teacher',
        dataKey: 'teacher',
        headerLabel: 'Teacher',
      },
      {
        name: 'student',
        headerLabel: 'Student',
        renderCell: (row) => {
          return (
            <Stack direction="row" alignItems="center" maxWidth="90%">
              <Tooltip title={`${row.meetingLink}`} arrow>
                <Chip
                  size="small"
                  label="40"
                  variant="outlined"
                  sx={{
                    border: '0px',
                    mr: '1px',
                    mb: 2,
                    backgroundColor: isLight ? theme.palette.info.lighter : theme.palette.grey[700],
                    color: isLight ? theme.palette.info.dark : theme.palette.grey[100],
                    width: 'auto',
                  }}
                />
              </Tooltip>
            </Stack>
          );
        },
      },
    ],
    [theme, isLight]
  );

  return (
    <Page title="Schedule List">
      <ProgressReportLPUPRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 1, md: 2 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={20} width="100%">
              Progress Report LP/UP
            </Typography>
          </Stack>
          <Stack>
            <Divider />
            <FormControl fullWidth sx={{ width: { xs: '100%', lg: 180 } }}>
              <Typography variant="subtitle1" fontSize={12} color="GrayText">
                Select Term
              </Typography>
              <Select
                labelId="messageTypeFilter"
                id="messageTypeFilterSelect"
                // value={messageTypeFilter?.toString() || '-1'}
                // onChange={handleTypeChange}
              >
                <MenuItem>Term 1</MenuItem>
                <MenuItem>Term 2</MenuItem>
                <MenuItem>Term 3</MenuItem>
              </Select>
            </FormControl>
          </Stack>
          <div className="card-main-body">
            <Box
              className="card-container"
              border={1}
              bgcolor={isLight ? '#fcfcfc' : theme.palette.grey[900]}
              borderColor={theme.palette.grey[300]}
              borderRadius={2}
              px={3}
              py={2}
              my={2}
            >
              <Box mb={2} display="flex" justifyContent="space-between">
                <Stack>
                  <Typography variant="subtitle2">Student Name : Alice Reynolds</Typography>
                  <Typography variant="subtitle2" fontSize={12} color="GrayText">
                    Parent Name : Bobby Henerson
                  </Typography>
                </Stack>
                <Stack>
                  <Typography variant="subtitle2">Class : X-A</Typography>
                  <Typography variant="subtitle2" fontSize={12} color="GrayText">
                    Mobile No. : +91 8654756232
                  </Typography>
                </Stack>
              </Box>
              <Grid container spacing={2}>
                {updatedData.map((student, rowIndex) => (
                  <Grid item xl={3} lg={6} md={12} sm={6} xs={12}>
                    <Card
                      className="student_card"
                      sx={{ p: 2, boxShadow: 0, border: `1px solid ${theme.palette.grey[300]}` }}
                    >
                      <Box display="flex" alignItems="center" justifyContent="space-between">
                        <Stack direction="row" alignItems="center" gap={1}>
                          <div>
                            <img width={40} src={Physics} alt="" />
                          </div>
                          <Typography mb={1} fontSize={13} variant="subtitle1" color="GrayText">
                            Physics
                          </Typography>
                        </Stack>
                        <Stack>
                          <Chip
                            sx={{ color: theme.palette.common.white }}
                            variant=""
                            size="small"
                            label="Grade : B1"
                            color="secondary"
                          />
                        </Stack>
                      </Box>
                      <Box mt={1} display="flex" alignItems="center" justifyContent="space-between">
                        <Stack direction="row" flexWrap="wrap" gap={1}>
                          {[1, 2, 3, 4].map((m) => (
                            <Chip
                              sx={{
                                fontSize: 10,
                                backgroundColor: theme.palette.grey[100],
                                color: theme.palette.common.black,
                              }}
                              variant="outlined"
                              size="small"
                              label="PF:3.0"
                            />
                          ))}
                        </Stack>
                        <Stack borderLeft={2} borderColor={theme.palette.grey[300]} pl={2}>
                          <Typography fontSize={13} variant="subtitle1" color="GrayText">
                            Total
                          </Typography>
                          <Typography variant="subtitle2" fontSize={12}>
                            77
                          </Typography>
                        </Stack>
                      </Box>
                    </Card>
                  </Grid>
                ))}
              </Grid>
              <Box mt={2} display="flex" alignItems="center" justifyContent="space-between">
                <Typography variant="subtitle2">Total : 400.3</Typography>
                <Chip
                  sx={{ px: 2, color: theme.palette.common.white }}
                  variant=""
                  size="small"
                  label="Pass"
                  color="success"
                />
              </Box>
              <Box mt={1} sx={{ display: 'flex', alignItems: 'center' }}>
                <Stack sx={{ width: '100%', mr: 1 }}>
                  <BorderLinearProgress variant="determinate" value={50} />
                </Stack>
                <Typography variant="subtitle2" color="text.secondary">
                  60%
                </Typography>
              </Box>
              <Stack direction="row" justifyContent="space-between">
                <Typography variant="subtitle2">No.of Pass:6</Typography>
                <Typography variant="subtitle2">No.of Fail:6</Typography>
                <Typography variant="subtitle2">No.of A1:6</Typography>
              </Stack>
              <Typography mt={2} variant="subtitle2">
                In Charge Name : Natasha
              </Typography>
            </Box>
          </div>
        </Card>
      </ProgressReportLPUPRoot>
      {/* <Popup
        size="xs"
        state={Delete}
        onClose={handleClickCloseDelete}
        popupContent={<DeleteMessage message="Are you sure want to delete?" />}
      /> */}
      {/* <Popup
        size="sm"
        title="Assignment Details"
        state={createPopup}
        onClose={() => setCreatePopup(false)}
        popupContent={<View />}
      /> */}
    </Page>
  );
}

export default ProgressReportLPUP;
