import api from '@/api';
import { CreateResponse, DeleteResponse, SendResponse, UpdateResponse } from '@/types/Common';
import {
  ClassDivisionDataType,
  ClassSectionDataType,
  ConveyorDataType,
  DeleteMultipleMessageTemplateType,
  GroupMembersDataType,
  GroupWiseDataType,
  GroupsDataType,
  MessageTempCreateRequest,
  MessageTempDataType,
  MessageTempRequest,
  ParentsDataType,
  PtaDataListRequest,
  PtaDataType,
  PublicGroupMembersDataType,
  PublicGroupWiseDataType,
  PublicGroupsDataType,
  SendToClassDivisionType,
  SendToClassSectionType,
  SendToConveyorType,
  SendToGroupMembersType,
  SendToGroupWiseType,
  SendToParentsRequest,
  SendToParentsResponse,
  SendToPtaType,
  SendToPublicGroupMembersType,
  SendToPublicGroupWiseType,
  SendToStaffType,
  StaffDataListRequest,
  StaffDataType,
  UplaodFilesType,
} from '@/types/MessageBox';
import { createAsyncThunk } from '@reduxjs/toolkit';

export const fetchMessageTempList = createAsyncThunk<
  MessageTempDataType[],
  MessageTempRequest,
  { rejectValue: string }
>('messageBox/templateList', async (request, { rejectWithValue }) => {
  try {
    const response = await api.MessageBox.GetMessageTempList(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching Message Template List');
  }
});

export const createMessageTempList = createAsyncThunk<
  CreateResponse,
  MessageTempCreateRequest,
  { rejectValue: string }
>('messageBox/create', async (request, { rejectWithValue }) => {
  try {
    const response = await api.MessageBox.CreateNewMessage(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in creating Message Template');
  }
});

export const EditMessageTemplate = createAsyncThunk<UpdateResponse, MessageTempDataType, { rejectValue: string }>(
  'messageBox/update',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.MessageBox.UpdateMessageTemplate(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in updating Message Template');
    }
  }
);
export const DeleteMessageTemplate = createAsyncThunk<DeleteResponse, number, { rejectValue: string }>(
  'messageBox/delete',
  async (messageId, { rejectWithValue }) => {
    try {
      const response = await api.MessageBox.DeleteMessageTemplate(messageId);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in deleting Message Template');
    }
  }
);

export const DeleteMultipleMessageTemplate = createAsyncThunk<
  DeleteResponse,
  DeleteMultipleMessageTemplateType[],
  { rejectValue: string }
>('messageBox/deleteMultiple', async (request, { rejectWithValue }) => {
  try {
    const response = await api.MessageBox.DeleteMultipleMessageTemplate(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in deleting Message Template');
  }
});
// -----
export const fetchParentsList = createAsyncThunk<
  ParentsDataType[],
  { adminId: number | undefined; academicId: number | undefined; classId: number | undefined },
  { rejectValue: string }
>('MessageBox/ParentsList', async ({ adminId, academicId, classId }, { rejectWithValue }) => {
  try {
    const response = await api.MessageBox.GetParentsList(adminId, academicId, classId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching MessageBox Parents List');
  }
});
export const messageSendToParents = createAsyncThunk<SendResponse, SendToParentsRequest, { rejectValue: string }>(
  'messageBox/SendToParents',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.MessageBox.SendToParents(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in sending Message To Parents');
    }
  }
);
export const messageSendToParentsList = createAsyncThunk<
  SendToParentsResponse,
  SendToParentsRequest[],
  { rejectValue: string }
>('messageBox/SendToParentsList', async (request, { rejectWithValue }) => {
  try {
    const response = await api.MessageBox.SendToParentsList(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in sending Message To Parents List');
  }
});

export const fetchStaffList = createAsyncThunk<StaffDataType[], StaffDataListRequest, { rejectValue: string }>(
  'messageBox/StaffList',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.MessageBox.GetStaffList(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in fetching Staff List');
    }
  }
);
export const messageSendToStaffList = createAsyncThunk<SendToStaffType, SendToStaffType[], { rejectValue: string }>(
  'messageBox/SendToStaffList',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.MessageBox.SendToStaffList(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in sending Message To Staff List');
    }
  }
);
export const fetchPtaList = createAsyncThunk<PtaDataType[], PtaDataListRequest, { rejectValue: string }>(
  'messageBox/PtaList',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.MessageBox.GetPtaList(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in fetching Pta List');
    }
  }
);
export const messageSendToPtaList = createAsyncThunk<SendToPtaType, SendToPtaType[], { rejectValue: string }>(
  'messageBox/SendToPtaList',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.MessageBox.SendToPtaList(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in sending Message To Pta List');
    }
  }
);

export const fetchConveyorList = createAsyncThunk<ConveyorDataType[], number, { rejectValue: string }>(
  'MessageBox/ConveyorList',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.MessageBox.GetConveyorList(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in fetching MessageBox Conveyor List');
    }
  }
);
export const messageSendToConveyorList = createAsyncThunk<
  SendToConveyorType,
  SendToConveyorType[],
  { rejectValue: string }
>('messageBox/SendToConveyorList', async (request, { rejectWithValue }) => {
  try {
    const response = await api.MessageBox.SendToConveyorList(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in sending Message To Conveyor List');
  }
});

export const fetchGroupsList = createAsyncThunk<
  GroupsDataType[],
  { adminId: number | undefined; academicId: number | undefined },
  { rejectValue: string }
>('MessageBox/GroupsList', async ({ adminId, academicId }, { rejectWithValue }) => {
  try {
    const response = await api.MessageBox.GetGroupsList(adminId, academicId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching MessageBox Groups List');
  }
});
export const fetchGroupMembersList = createAsyncThunk<
  GroupMembersDataType[],
  { adminId: number | undefined; academicId: number | undefined; groupId: number | undefined },
  { rejectValue: string }
>('MessageBox/GroupMembersList', async ({ adminId, academicId, groupId }, { rejectWithValue }) => {
  try {
    const response = await api.MessageBox.GetGroupMembersList(adminId, academicId, groupId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching MessageBox Group Members List');
  }
});
export const messageSendToGroupMembersList = createAsyncThunk<
  SendToGroupMembersType,
  SendToGroupMembersType[],
  { rejectValue: string }
>('messageBox/SendToGroupMembersList', async (request, { rejectWithValue }) => {
  try {
    const response = await api.MessageBox.SendToGroupMembersList(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in sending Message To Group Members List');
  }
});

export const fetchClassDivision = createAsyncThunk<
  ClassDivisionDataType[],
  { adminId: number | undefined; academicId: number | undefined },
  { rejectValue: string }
>('MessageBox/ClassDivisionList', async ({ adminId, academicId }, { rejectWithValue }) => {
  try {
    const response = await api.MessageBox.GetClassDivision(adminId, academicId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching MessageBox ClassDivision List');
  }
});
export const messageSendToClassDivision = createAsyncThunk<
  SendToClassDivisionType,
  SendToClassDivisionType[],
  { rejectValue: string }
>('messageBox/SendToClassDivision', async (request, { rejectWithValue }) => {
  try {
    const response = await api.MessageBox.SendToClassDivision(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in sending Message To ClassDivision List');
  }
});

export const fetchClassSection = createAsyncThunk<
  ClassSectionDataType[],
  { adminId: number | undefined; academicId: number | undefined },
  { rejectValue: string }
>('MessageBox/ClassSectionList', async ({ adminId, academicId }, { rejectWithValue }) => {
  try {
    const response = await api.MessageBox.GetClassSection(adminId, academicId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching MessageBox ClassSection List');
  }
});
export const messageSendToClassSection = createAsyncThunk<
  SendToClassSectionType,
  SendToClassSectionType[],
  { rejectValue: string }
>('messageBox/SendToClassSection', async (request, { rejectWithValue }) => {
  try {
    const response = await api.MessageBox.SendToClassSection(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in sending Message To ClassSection List');
  }
});

export const fetchGroupWise = createAsyncThunk<
  GroupWiseDataType[],
  { adminId: number | undefined; academicId: number | undefined },
  { rejectValue: string }
>('MessageBox/GroupWiseList', async ({ adminId, academicId }, { rejectWithValue }) => {
  try {
    const response = await api.MessageBox.GetGroupWise(adminId, academicId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching MessageBox Group Wise List');
  }
});
export const messageSendToGroupWise = createAsyncThunk<
  SendToGroupWiseType,
  SendToGroupWiseType[],
  { rejectValue: string }
>('messageBox/SendToGroupWise', async (request, { rejectWithValue }) => {
  try {
    const response = await api.MessageBox.SendToGroupWise(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in sending Message To Group Wise List');
  }
});

//  Public Group
export const fetchPublicGroupsList = createAsyncThunk<
  PublicGroupsDataType[],
  { adminId: number | undefined; academicId: number | undefined },
  { rejectValue: string }
>('MessageBox/PulblicGroupsList', async ({ adminId, academicId }, { rejectWithValue }) => {
  try {
    const response = await api.MessageBox.GetPublicGroupList(adminId, academicId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching MessageBox Public Groups List');
  }
});
export const fetchPublicGroupMembersList = createAsyncThunk<
  PublicGroupMembersDataType[],
  { adminId: number | undefined; academicId: number | undefined; gmemberId: number | undefined },
  { rejectValue: string }
>('MessageBox/PublicGroupMembersList', async ({ adminId, academicId, gmemberId }, { rejectWithValue }) => {
  try {
    const response = await api.MessageBox.GetPublicGroupMembersList(adminId, academicId, gmemberId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching MessageBox Public Group Members List');
  }
});
export const messageSendToPublicGroupMembersList = createAsyncThunk<
  SendToPublicGroupMembersType,
  SendToPublicGroupMembersType[],
  { rejectValue: string }
>('messageBox/SendToPublicGroupMembersList', async (request, { rejectWithValue }) => {
  try {
    const response = await api.MessageBox.SendToPublicGroupMembersList(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in sending Message To Public Group Members List');
  }
});

// Public group wise //
export const fetchPublicGroupWiseList = createAsyncThunk<
  PublicGroupWiseDataType[],
  { adminId: number | undefined; academicId: number | undefined; groupId: number | undefined },
  { rejectValue: string }
>('MessageBox/PublicGroupWiseList', async ({ adminId, academicId, groupId }, { rejectWithValue }) => {
  try {
    const response = await api.MessageBox.GetPublicGroupWiseList(adminId, academicId, groupId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching MessageBox Public Group Wise List');
  }
});
export const messageSendToPublicGroupWiseList = createAsyncThunk<
  SendToPublicGroupWiseType,
  SendToPublicGroupWiseType[],
  { rejectValue: string }
>('messageBox/SendToPublicGroupWiseList', async (request, { rejectWithValue }) => {
  try {
    const response = await api.MessageBox.SendToPublicGroupWiseList(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in sending Messages To Public Group Wise   List');
  }
});

export const messageSendToAllParents = createAsyncThunk<
  SendResponse,
  { adminId: number | undefined; academicId: number | undefined; messageId: number | undefined },
  { rejectValue: string }
>('MessageBox/SendToAllParents', async ({ adminId, academicId, messageId }, { rejectWithValue }) => {
  try {
    const response = await api.MessageBox.SendToAllParents(adminId, academicId, messageId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in sending Messages To All Parents');
  }
});
export const messageSendToAllStaff = createAsyncThunk<
  SendResponse,
  { adminId: number | undefined; academicId: number | undefined; messageId: number | undefined },
  { rejectValue: string }
>('MessageBox/SendToAllStaff', async ({ adminId, academicId, messageId }, { rejectWithValue }) => {
  try {
    const response = await api.MessageBox.SendToAllStaff(adminId, academicId, messageId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in sending Messages To All Staff');
  }
});
export const messageSendToAllPta = createAsyncThunk<
  SendResponse,
  { adminId: number | undefined; academicId: number | undefined; messageId: number | undefined },
  { rejectValue: string }
>('MessageBox/SendToAllPta', async ({ adminId, academicId, messageId }, { rejectWithValue }) => {
  try {
    const response = await api.MessageBox.SendToAllPta(adminId, academicId, messageId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in sending Messages To All Pta');
  }
});
export const messageSendToAllConveyors = createAsyncThunk<
  SendResponse,
  { adminId: number | undefined; academicId: number | undefined; messageId: number | undefined },
  { rejectValue: string }
>('MessageBox/SendToAllConveyors', async ({ adminId, academicId, messageId }, { rejectWithValue }) => {
  try {
    const response = await api.MessageBox.SendToAllConveyors(adminId, academicId, messageId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in sending Messages To All Conveyors');
  }
});
export const fetchMessageTemplate = createAsyncThunk<MessageTempDataType, number | undefined, { rejectValue: string }>(
  'MessageBox/FetchMessageTemplate',
  async (messageId, { rejectWithValue }) => {
    try {
      const response = await api.MessageBox.FetchMessageTemplate(messageId);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in fecting Messages');
    }
  }
);

//  Upload Files

export const fileUpload = createAsyncThunk<any, { files: File[] }, { rejectValue: string }>(
  'messageBox/FileUpload',
  async ({ files }, { rejectWithValue }) => {
    try {
      // Assuming your API method expects FormData for file upload
      const response = await api.MessageBox.FileUpload(files);

      return response;
    } catch {
      return rejectWithValue('Something went wrong in Upload files');
    }
  }
);
// export const fileUpload = createAsyncThunk<any, File[], { rejectValue: string }>(
//   'messageBox/FileUpload',
//   async (urls, { rejectWithValue }) => {
//     try {
//       // Assuming your API method expects an array of file URLs
//       const response = await api.MessageBox.FileUpload(urls);
//       console.log('response', response);
//       return response;
//     } catch {
//       return rejectWithValue('Something went wrong in Upload files');
//     }
//   }
// );
