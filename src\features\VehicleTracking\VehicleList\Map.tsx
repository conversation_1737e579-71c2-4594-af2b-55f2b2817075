import React from 'react';
import { GoogleMap, LoadScript } from '@react-google-maps/api';

const Map: React.FC = ({ zoom }: number) => {
  const containerStyle = {
    width: '100%',
    height: `calc(100vh - 300px)`,
  };

  const center = {
    lat: 51.505,
    lng: -0.09,
  };

  return (
    <LoadScript googleMapsApiKey="">
      <GoogleMap mapContainerStyle={containerStyle} center={center} zoom={zoom} />
    </LoadScript>
  );
};

export default Map;
