import React from 'react';
import styled from 'styled-components';
import { Box, Button, Stack, TextField, Typography } from '@mui/material';
import typography from '@/theme/typography';

const EnrollRoot = styled.div`
  width: 100%;
  padding: 0.5rem 1.5rem;
`;
export const Enroll = () => {
  return (
    <EnrollRoot>
      <Box>
        <Typography variant="h6" fontSize={17} mb={3}>
          Enroll
        </Typography>
        <Box>
          <Typography variant="body2" fontSize={14} fontWeight={600}>
            Fee Title
          </Typography>
          <TextField fullWidth placeholder="Enter Title" sx={{ mb: 2 }} />
          <Typography variant="body2" fontSize={14} fontWeight={600}>
            Description
          </Typography>
          <TextField
            sx={{ mb: 2 }}
            multiline
            fullWidth
            minRows={6}
            InputProps={{ inputProps: { style: { resize: 'both' } } }}
            placeholder="Enter Message Content..."
          />
          <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' } }}>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="secondary" sx={{ fontFamily: typography.fontFamily }}>
                Cancel
              </Button>
              <Button variant="contained" color="primary">
                Save
              </Button>
            </Stack>
          </Box>
        </Box>
      </Box>
    </EnrollRoot>
  );
};
