import { ExamCenterState } from '@/types/ExamCenter';
import { PayloadAction, createSlice } from '@reduxjs/toolkit';
import {
  addMarkRegisterCBSE,
  addMarkRegisterCBSEwithCE,
  fetchMarkRegisterDetails,
  fetchMarkRegisterDetailsWithCE,
  fetchMarkRegisterFilter,
  fetchMarkRegisterSubjectFilter,
} from './examCenter.thunks';
import { flushStore } from '../flush.slice';

const initialState: ExamCenterState = {
  examList: {
    status: 'idle',
    data: [],
    error: null,
    pageInfo: {
      totalrecords: 0,
      pagesize: 20,
      pagenumber: 1,
      totalpages: 0,
      remainingpages: 0,
    },
    sortColumn: 'classId',
    sortDirection: 'asc',
  },
  markRegisterFilters: {
    status: 'idle',
    data: {
      currentAcademicId: 0,
      yearList: [{ academicId: -2, academicTime: 'Select' }],
      classList: [{ classId: -2, className: 'Select' }],
      examList: [{ examId: -2, examName: 'Select' }],
    },
    error: null,
  },
  markRegisterSubjectFilters: {
    status: 'idle',
    data: [],
    error: null,
  },
  markRegisterData: {
    status: 'idle',
    data: [],
    error: null,
  },
  markRegisterDataWithCE: {
    status: 'idle',
    data: [],
    error: null,
  },
  submitting: false,
  deletingRecords: {},
  error: null,
};

export const examCenterSlice = createSlice({
  name: 'examCenter',
  initialState,
  reducers: {
    setExamListPage: (state, action: PayloadAction<number>) => {
      state.examList.pageInfo.pagenumber = action.payload;
    },
    setExamListPageSize: (state, action: PayloadAction<number>) => {
      state.examList.pageInfo.pagenumber = 1;
      state.examList.pageInfo.pagesize = action.payload;
    },
    setSortColumn: (state, action: PayloadAction<string>) => {
      state.examList.pageInfo.pagenumber = 1;
      state.examList.sortColumn = action.payload;
      state.examList.sortDirection = 'asc';
    },
    setSortDirection: (state, action: PayloadAction<'asc' | 'desc'>) => {
      state.examList.pageInfo.pagenumber = 1;
      state.examList.sortDirection = action.payload;
    },
    setSortColumnAndDirection: (state, action: PayloadAction<{ column: string; direction: 'asc' | 'desc' }>) => {
      state.examList.pageInfo.pagenumber = 1;
      state.examList.sortColumn = action.payload.column;
      state.examList.sortDirection = action.payload.direction;
    },
    setSubmitting: (state, action: PayloadAction<boolean>) => {
      state.submitting = action.payload;
    },
    setDeletingRecords: (state, action: PayloadAction<{ recordId: number }>) => {
      state.deletingRecords[action.payload.recordId] = true;
    },
    clearDeletingRecords: (state, action: PayloadAction<{ recordId: number }>) => {
      delete state.deletingRecords[action.payload.recordId];
    },
  },
  extraReducers(builder) {
    builder
      // .addCase(fetchExamList.pending, (state) => {
      //   state.examList.status = 'loading';
      //   state.examList.error = null;
      // })
      // .addCase(fetchExamList.fulfilled, (state, action) => {
      //   const { data, ...pageInfo } = action.payload;
      //   state.examList.status = 'success';
      //   state.examList.data = data;
      //   state.examList.error = null;
      //   state.examList.pageInfo = pageInfo;
      // })
      // .addCase(fetchExamList.rejected, (state, action) => {
      //   state.examList.status = 'error';
      //   state.examList.error = action.payload || 'Unknown error in fetching class list';
      // })

      .addCase(fetchMarkRegisterFilter.pending, (state) => {
        state.markRegisterFilters.status = 'loading';
        state.markRegisterFilters.error = null;
      })
      .addCase(fetchMarkRegisterFilter.fulfilled, (state, action) => {
        state.markRegisterFilters.status = 'success';
        state.markRegisterFilters.data = action.payload;
        state.markRegisterFilters.error = null;
      })
      .addCase(fetchMarkRegisterFilter.rejected, (state, action) => {
        state.markRegisterFilters.status = 'error';
        state.markRegisterFilters.error = action.payload || 'Unknown error in fetching mark register filter';
      })

      .addCase(fetchMarkRegisterSubjectFilter.pending, (state) => {
        state.markRegisterSubjectFilters.status = 'loading';
        state.markRegisterSubjectFilters.error = null;
      })
      .addCase(fetchMarkRegisterSubjectFilter.fulfilled, (state, action) => {
        state.markRegisterSubjectFilters.status = 'success';
        state.markRegisterSubjectFilters.data = action.payload;
        state.markRegisterSubjectFilters.error = null;
      })
      .addCase(fetchMarkRegisterSubjectFilter.rejected, (state, action) => {
        state.markRegisterSubjectFilters.status = 'error';
        state.markRegisterSubjectFilters.error =
          action.payload || 'Unknown error in fetching mark register subject filter';
      })

      .addCase(fetchMarkRegisterDetails.pending, (state) => {
        state.markRegisterData.status = 'loading';
        state.markRegisterData.error = null;
      })
      .addCase(fetchMarkRegisterDetails.fulfilled, (state, action) => {
        state.markRegisterData.status = 'success';
        state.markRegisterData.data = action.payload;
        state.markRegisterData.error = null;
      })
      .addCase(fetchMarkRegisterDetails.rejected, (state, action) => {
        state.markRegisterData.status = 'error';
        state.markRegisterData.error = action.payload || 'Unknown error in fetching mark register details';
      })

      .addCase(addMarkRegisterCBSE.pending, (state) => {
        state.submitting = true;
        state.error = null;
      })
      .addCase(addMarkRegisterCBSE.fulfilled, (state) => {
        state.submitting = false;
        state.error = null;
      })
      .addCase(addMarkRegisterCBSE.rejected, (state, action) => {
        state.submitting = false;
        state.error = action.error.message;
      })

      .addCase(fetchMarkRegisterDetailsWithCE.pending, (state) => {
        state.markRegisterDataWithCE.status = 'loading';
        state.markRegisterDataWithCE.error = null;
      })
      .addCase(fetchMarkRegisterDetailsWithCE.fulfilled, (state, action) => {
        state.markRegisterDataWithCE.status = 'success';
        state.markRegisterDataWithCE.data = action.payload;
        state.markRegisterDataWithCE.error = null;
      })
      .addCase(fetchMarkRegisterDetailsWithCE.rejected, (state, action) => {
        state.markRegisterDataWithCE.status = 'error';
        state.markRegisterDataWithCE.error =
          action.payload || 'Unknown error in fetching mark register details with CE';
      })

      .addCase(addMarkRegisterCBSEwithCE.pending, (state) => {
        state.submitting = true;
        state.error = null;
      })
      .addCase(addMarkRegisterCBSEwithCE.fulfilled, (state) => {
        state.submitting = false;
        state.error = null;
      })
      .addCase(addMarkRegisterCBSEwithCE.rejected, (state, action) => {
        state.submitting = false;
        state.error = action.error.message;
      })
      .addCase(flushStore, () => initialState);

    //   .addCase(addNewExam.pending, (state) => {
    //     state.submitting = true;
    //     state.error = null;
    //   })
    //   .addCase(addNewExam.fulfilled, (state) => {
    //     state.submitting = false;
    //     state.error = null;
    //   })
    //   .addCase(addNewExam.rejected, (state, action) => {
    //     state.submitting = false;
    //     state.error = action.error.message;
    //   })

    //   .addCase(updateExam.pending, (state) => {
    //     state.submitting = true;
    //     state.error = null;
    //   })
    //   .addCase(updateExam.fulfilled, (state, action) => {
    //     state.submitting = false;
    //     state.error = null;
    //     const dataIndex = state.examList.data.findIndex((x) => x.examId === action.meta.arg.examId);
    //     if (dataIndex > -1) {
    //       state.examList.data[dataIndex] = action.meta.arg;
    //     }
    //   })
    //   .addCase(updateExam.rejected, (state, action) => {
    //     state.submitting = false;
    //     state.error = action.error.message;
    //   })

    //   .addCase(deleteExam.pending, (state, action) => {
    //     state.deletingRecords[action.meta.arg] = true;
    //     state.error = null;
    //   })
    //   .addCase(deleteExam.fulfilled, (state, action) => {
    //     if (action.payload.deleted) {
    //       const { [action.meta.arg]: deletedRecord, ...restDeletingRecords } = state.deletingRecords;
    //       state.deletingRecords = restDeletingRecords;
    //     }
    //     state.error = null;
    //   })
    //   .addCase(deleteExam.rejected, (state, action) => {
    //     state.deletingRecords = { ...initialState.deletingRecords };
    //     state.error = action.error.message;
    //   });
  },
});

export const {
  setExamListPage,
  setExamListPageSize,
  setSortColumn,
  setSortDirection,
  setSortColumnAndDirection,
  setSubmitting,
  setDeletingRecords,
  clearDeletingRecords,
} = examCenterSlice.actions;

// export const getExamListStatus = (state) => state.examCenter.examList.status;
// export const getExamListData = (state) => state.examCenter.examList.data;
// export const getExamListError = (state) => state.examCenter.examList.error;
// export const getExamListPageInfo = (state) => state.examCenter.examList.pageInfo;
// export const getSortColumn = (state) => state.examCenter.examList.sortColumn;
// export const getSortDirection = (state) => state.examCenter.examList.sortDirection;
// export const getSubmitting = (state) => state.examCenter.submitting;
// export const getDeletingRecords = (state) => {
//   const keys = Object.keys(state.examCenter.deletingRecords);
//   return keys.length > 0 ? keys.map(Number) : [];
// };

export default examCenterSlice.reducer;
