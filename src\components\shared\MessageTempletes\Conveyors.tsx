/* eslint-disable jsx-a11y/alt-text */
import React, { FormEvent, useCallback, useState } from 'react';
import {
  Divider,
  Grid,
  Paper,
  Stack,
  Button,
  Typography,
  Box,
  FormControl,
  Select,
  MenuItem,
  SelectChangeEvent,
  useTheme,
  Tooltip,
  IconButton,
} from '@mui/material';
import styled from 'styled-components';
import { ConveyorDataType, SendToConveyorType } from '@/types/MessageBox';
import { fetchConveyorList, messageSendToConveyorList } from '@/store/MessageBox/messageBox.thunks';
import useAuth from '@/hooks/useAuth';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import {
  getClassData,
  getConveyorListData,
  getConveyorListStatus,
  getMessageTempSubmitting,
} from '@/config/storeSelectors';
import { useAppSelector } from '@/hooks/useAppSelector';
import LoadingButton from '@mui/lab/LoadingButton';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import SendIcon from '@mui/icons-material/Send';
import { fetchClassList } from '@/store/Dashboard/dashboard.thunks';
import ErrorMsg from '@/assets/MessageIcons/error-message.gif';
import ErrorMsg1 from '@/assets/MessageIcons/error-message1.gif';
import ErrorMsg2 from '@/assets/MessageIcons/error-message2.gif';
import LoadingMsg from '@/assets/MessageIcons/loading-message.gif';
import Lottie from 'lottie-react';
import SuccessMsg from '@/assets/MessageIcons/message-success.gif';
import successIcon from '@/assets/MessageIcons/success.json';
import errorIcon from '@/assets/MessageIcons/error-message.json';
import { breakPointsMaxwidth } from '@/config/breakpoints';
import { SuccessMessage } from '../Popup/SuccessMessage';
import { ErrorMessage } from '../Popup/ErrorMessage';
import LoadingPopup from '../Popup/LoadingPopup';
import { LoadingMessage } from '../Popup/LoadingMessage';
import { useConfirm } from '../Popup/Confirmation';
import DataTable, { DataTableColumn } from '../TableComponents/DataTable';
import { SendPopupProps } from '@/types/Common';
import { voiceMessageSendToConveyorList } from '@/store/VoiceMessage/voiceMessage.thunk';
import { SendVoiceToConveyorRequest } from '@/types/VoiceMessage';

const ConveyorsRoot = styled.div`
  width: 100%;
  padding: 0.5rem 1.5rem;
  .delivery-report-btn {
    @media ${breakPointsMaxwidth.xs} {
      width: 100%;
    }
  }
  .send-btn {
    @media ${breakPointsMaxwidth.sm} {
      width: 50%;
    }
    @media ${breakPointsMaxwidth.xs} {
      width: 100%;
    }
  }
  .cancel-btn {
    @media ${breakPointsMaxwidth.xs} {
      width: 25%;
    }
    @media ${breakPointsMaxwidth.sm} {
      width: 50%;
    }
  }
`;

interface ConveyorDataWithStatus extends ConveyorDataType {
  sendStatus: 'Success' | 'Failed' | '' | undefined;
}

function Conveyors({ messageId, voiceId, isSubmitting, templateId }: SendPopupProps) {
  const { user } = useAuth();
  const theme = useTheme();
  const dispatch = useAppDispatch();
  const { confirm } = useConfirm();
  const adminId: number = user ? user.accountId : 0;
  const [classNameFilter, setClassNameFilter] = useState(-1);
  const [selectedRows, setSelectedRows] = useState<ConveyorDataWithStatus[]>([]);
  // const [sendResults, setSendResults] = useState<SendToConveyorType[] | null>([]);
  const ConveyorListData = useAppSelector(getConveyorListData);
  const ConveyorListStatus = useAppSelector(getConveyorListStatus);
  const ClassData = useAppSelector(getClassData);
  const [view, setView] = useState<'main' | 'report'>('main');
  const [sendResults, setSendResults] = useState<ConveyorDataWithStatus[]>([]);
  const [selectedData, setSelectedData] = useState<ConveyorDataWithStatus[]>([]);
  const [disabledCheckBoxes, setDisabledCheckBoxes] = useState<boolean[]>([]);
  const [errMsgTooltip, setErrMsgTooltip] = useState<React.ReactNode>(undefined);

  interface ConveyorListRequest {
    classId: number;
  }
  const initialConveyorListRequest = React.useMemo(
    () => ({
      adminId,
      classId: -1,
    }),
    [adminId]
  );
  const currentConveyorListRequest = React.useMemo(
    () => ({
      adminId,
      classId: classNameFilter,
    }),
    [adminId, classNameFilter]
  );
  // const

  const loadConveyorList = useCallback(
    async (request: ConveyorListRequest) => {
      try {
        const data = await dispatch(fetchConveyorList(request.classId)).unwrap();
        const dataWithStatus = data.map((item) => ({
          ...item,
          sendStatus: sendResults.find((i) => i.conveyorsMobile === item.conveyorsMobile)?.sendStatus,
        }));
        console.log(dataWithStatus);
        setSelectedData(dataWithStatus);
        setSelectedRows([]);
      } catch (error) {
        console.error('Error loading conveyor list:', error);
      }
    },
    [dispatch, sendResults]
  );

  React.useEffect(() => {
    dispatch(fetchClassList(adminId));
    // dispatch(fetchConveyorList(currentConveyorListRequest.classId));
    // loadConveyorList(currentConveyorListRequest);

    console.log('classNameFilter', classNameFilter);
  }, [dispatch, adminId, classNameFilter, loadConveyorList, currentConveyorListRequest]);

  const handleSubmit = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      loadConveyorList(currentConveyorListRequest);
      setSelectedRows([]);
    },
    [loadConveyorList, currentConveyorListRequest]
  );

  const handleReset = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      setClassNameFilter(-1);
      setSelectedRows([]);
      loadConveyorList(initialConveyorListRequest);
      // loadConveyorList(currentConveyorListRequest);
    },
    [loadConveyorList, initialConveyorListRequest]
  );

  const handleCancel = useCallback(() => {
    setSelectedRows([]);
    loadConveyorList(currentConveyorListRequest);
    setDisabledCheckBoxes([]);
  }, [currentConveyorListRequest, loadConveyorList]);

  const handleBack = useCallback(() => {
    setView('main');
    // setSendResults(null);
    setSelectedRows([]);
    loadConveyorList(currentConveyorListRequest);
  }, [loadConveyorList, currentConveyorListRequest]);

  const handleClassChange = (e: SelectChangeEvent) => {
    setClassNameFilter(ClassData.filter((item) => item.classId === parseInt(e.target.value, 10))[0].classId);
    loadConveyorList({ ...currentConveyorListRequest, classId: parseInt(e.target.value, 10) });
    setSelectedRows([]);
  };

  const handleSendMessage = useCallback(async () => {
    try {
      if (!selectedRows || selectedRows.length === 0) {
        const errorMessage = <ErrorMessage icon={ErrorMsg1} message="Please Select Atleast one parent to sent" />;

        await confirm(errorMessage, 'Parent not selected', { okLabel: 'Ok', showOnlyOk: true });
        return;
      }
      const sendRequests: SendToConveyorType[] = await Promise.all(
        selectedRows?.map(async (item) => {
          const { conveyorsName, conveyorsMobile } = item;
          const sendReq = { messageId, adminId, sendStatus: '', conveyorsName, conveyorsMobile };
          return sendReq;
        }) || []
      );
      const actionResult = await dispatch(messageSendToConveyorList(sendRequests));

      if (actionResult && Array.isArray(actionResult.payload)) {
        const results: ConveyorDataWithStatus[] = actionResult.payload;
        // setSendResults(results);
        const errorMessages = results.find((result) => result.sendStatus === 'Failed');
        const successMessages = results.find((result) => result.sendStatus === 'Success');

        if (!errorMessages) {
          const successMessage = (
            <SuccessMessage icon={SuccessMsg} message="Message sent to all selected parents Successfully" />
          );
          await confirm(successMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        } else if (!successMessages) {
          setErrMsgTooltip(
            <Typography variant="subtitle2" fontSize={12} style={{ color: theme.palette.error.main }}>
              No messages sent to parent, Please check the numbers and Try again
            </Typography>
          );
          const errorMessage = (
            <ErrorMessage
              icon={ErrorMsg2}
              message="No messages sent to any parents, Please check the numbers and Try again"
            />
          );

          await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        } else {
          setErrMsgTooltip(
            <Typography variant="subtitle2" fontSize={12} style={{ color: theme.palette.error.main }}>
              Error sending messages to parent, Please recheck and Try again
            </Typography>
          );
          const errorMessage = (
            <ErrorMessage
              icon={ErrorMsg}
              message="Error sending messages to some parents, Please recheck and Try again"
            />
          );

          await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        }
        // const updatedSelectedData = selectedData.map((rowData) => {
        //   const selectedRow = selectedRows.find((sr) => sr.conveyorsMobile === rowData.conveyorsMobile);
        //   console.log('selectedRow::::', selectedRow);
        //   if (selectedRow) {
        //     const result = results.find((r) => r.conveyorsMobile === selectedRow.conveyorsMobile);

        //     if (result) {
        //       return { ...rowData, status: result.sendStatus || '' };
        //     }
        //   }
        //   return rowData;
        // });
        // // setSelectedRows([]);
        // setSelectedData(updatedSelectedData);
        setSendResults([...sendResults, ...results]);
        const dataResult = selectedData.map((item) => ({
          ...item,
          sendStatus:
            item.sendStatus !== 'Success' && item.sendStatus !== 'Failed'
              ? results.find((result) => result.conveyorsMobile === item.conveyorsMobile)?.sendStatus
              : item.sendStatus,
        }));

        // setSelectedRows([]);
        setSelectedData(dataResult);
      } else {
        const errorMessage = <ErrorMessage message="Error while sending messages, Please Try again" />;

        await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
      }
      setSelectedRows([]);
      const updatedDisabledCheckBoxes = selectedData.map((selectedItem) =>
        selectedRows.includes(selectedItem)
          ? !disabledCheckBoxes[selectedData.indexOf(selectedItem)]
          : disabledCheckBoxes[selectedData.indexOf(selectedItem)]
      );

      setDisabledCheckBoxes(updatedDisabledCheckBoxes);
      // loadConveyorList(currentConveyorListRequest);
    } catch (error) {
      console.error('Error sending messages:', error);
    }
  }, [confirm, dispatch, selectedRows, messageId, selectedData, adminId, disabledCheckBoxes, theme, sendResults]);

  const handleSendVoiceMessage = useCallback(async () => {
    try {
      if (!selectedRows || selectedRows.length === 0) {
        const errorMessage = <ErrorMessage icon={ErrorMsg1} message="Please Select Atleast one parent to sent" />;

        await confirm(errorMessage, 'Parent not selected', { okLabel: 'Ok', showOnlyOk: true });
        return;
      }
      const sendRequests: SendVoiceToConveyorRequest[] = await Promise.all(
        selectedRows?.map(async (item) => {
          const { conveyorsName, conveyorsMobile } = item;
          const sendReq = { voiceId, adminId, templateId, sendStatus: '', conveyorsName, conveyorsMobile };
          return sendReq;
        }) || []
      );
      const actionResult = await dispatch(voiceMessageSendToConveyorList(sendRequests));

      if (actionResult && Array.isArray(actionResult.payload)) {
        const results: ConveyorDataWithStatus[] = actionResult.payload;
        // setSendResults(results);
        const errorMessages = results.find((result) => result.sendStatus === 'Failed');
        const successMessages = results.find((result) => result.sendStatus === 'Success');

        if (!errorMessages) {
          const successMessage = (
            <SuccessMessage icon={SuccessMsg} message="Message sent to all selected parents Successfully" />
          );
          await confirm(successMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        } else if (!successMessages) {
          setErrMsgTooltip(
            <Typography variant="subtitle2" fontSize={12} style={{ color: theme.palette.error.main }}>
              No messages sent to parent, Please check the numbers and Try again
            </Typography>
          );
          const errorMessage = (
            <ErrorMessage
              icon={ErrorMsg2}
              message="No messages sent to any parents, Please check the numbers and Try again"
            />
          );

          await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        } else {
          setErrMsgTooltip(
            <Typography variant="subtitle2" fontSize={12} style={{ color: theme.palette.error.main }}>
              Error sending messages to parent, Please recheck and Try again
            </Typography>
          );
          const errorMessage = (
            <ErrorMessage
              icon={ErrorMsg}
              message="Error sending messages to some parents, Please recheck and Try again"
            />
          );

          await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        }
        // const updatedSelectedData = selectedData.map((rowData) => {
        //   const selectedRow = selectedRows.find((sr) => sr.conveyorsMobile === rowData.conveyorsMobile);
        //   console.log('selectedRow::::', selectedRow);
        //   if (selectedRow) {
        //     const result = results.find((r) => r.conveyorsMobile === selectedRow.conveyorsMobile);

        //     if (result) {
        //       return { ...rowData, status: result.sendStatus || '' };
        //     }
        //   }
        //   return rowData;
        // });
        // // setSelectedRows([]);
        // setSelectedData(updatedSelectedData);
        setSendResults([...sendResults, ...results]);
        const dataResult = selectedData.map((item) => ({
          ...item,
          sendStatus:
            item.sendStatus !== 'Success' && item.sendStatus !== 'Failed'
              ? results.find((result) => result.conveyorsMobile === item.conveyorsMobile)?.sendStatus
              : item.sendStatus,
        }));

        // setSelectedRows([]);
        setSelectedData(dataResult);
      } else {
        const errorMessage = <ErrorMessage message="Error while sending messages, Please Try again" />;

        await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
      }
      setSelectedRows([]);
      const updatedDisabledCheckBoxes = selectedData.map((selectedItem) =>
        selectedRows.includes(selectedItem)
          ? !disabledCheckBoxes[selectedData.indexOf(selectedItem)]
          : disabledCheckBoxes[selectedData.indexOf(selectedItem)]
      );

      setDisabledCheckBoxes(updatedDisabledCheckBoxes);
      // loadConveyorList(currentConveyorListRequest);
    } catch (error) {
      console.error('Error sending messages:', error);
    }
  }, [confirm, dispatch, selectedRows, messageId, selectedData, adminId, disabledCheckBoxes, theme, sendResults]);

  const getRowKey = useCallback((row: ConveyorDataWithStatus) => row.conveyorsId, []);

  const getStatusIcon = (row: ConveyorDataWithStatus) => {
    let iconComponent;

    if (row.sendStatus === 'Failed') {
      iconComponent = (
        <Tooltip title={errMsgTooltip} placement="left">
          <Stack>
            <Lottie animationData={errorIcon} loop={false} style={{ width: '30px' }} />
          </Stack>
        </Tooltip>
      );
    } else if (row.sendStatus === 'Success') {
      iconComponent = (
        <Stack>
          <Lottie animationData={successIcon} loop={false} style={{ width: '30px' }} />
        </Stack>
      );
    } else if (row.sendStatus === null) {
      iconComponent = 'empty';
    } else {
      iconComponent = null;
    }

    return <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>{iconComponent}</Box>;
  };
  const conveyorListColumns: DataTableColumn<ConveyorDataWithStatus>[] = [
    {
      name: 'status',
      renderCell: getStatusIcon,
    },
    {
      name: 'conveyorsId',
      dataKey: 'conveyorsId',
      headerLabel: 'Sl.No',
      sortable: true,
    },
    {
      name: 'conveyorsName',
      headerLabel: 'Conveyors Name',
      renderCell: (row) => {
        return (
          <Stack direction="row" gap={1} alignItems="center">
            {/* <Avatar /> */}
            <Typography variant="subtitle2">{row.conveyorsName}</Typography>
          </Stack>
        );
      },
    },
    {
      name: 'conveyorsMobile',
      dataKey: 'conveyorsMobile',
      headerLabel: 'Mobile Number',
    },
    {
      name: 'vehicleNo',
      dataKey: 'vehicleNo',
      headerLabel: 'Vehicle No',
    },
  ];
  return (
    <ConveyorsRoot>
      <Divider />
      {view === 'main' ? (
        <form noValidate onReset={handleReset} onSubmit={handleSubmit}>
          <Grid pb={2} pt={1} container spacing={3} alignItems="end">
            <Grid item lg={3} md={4} sm={6} xs={12}>
              <FormControl fullWidth>
                <Typography variant="h6" fontSize={14}>
                  Select Class
                </Typography>
                <Select
                  labelId="classNameFilter"
                  id="classNameFilterSelect"
                  value={classNameFilter.toString()}
                  onChange={handleClassChange}
                  placeholder="Select Class"
                  MenuProps={{
                    PaperProps: {
                      style: {
                        maxHeight: '300px', // Adjust the value to your desired height
                      },
                    },
                  }}
                >
                  <MenuItem value="-1" sx={{ display: 'none' }}>
                    All Class
                  </MenuItem>
                  {ClassData.map((opt) => (
                    <MenuItem key={opt.classId} value={opt.classId}>
                      {opt.className}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item lg={2} md={4} sm={6} xs={12}>
              <FormControl fullWidth>
                <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                  <Button variant="contained" color="secondary" type="reset" fullWidth>
                    Reset
                  </Button>
                  <Button variant="contained" color="primary" type="submit" fullWidth>
                    Search
                  </Button>
                </Stack>
              </FormControl>
            </Grid>
          </Grid>
        </form>
      ) : (
        <Stack direction="row" alignItems="center" gap={1}>
          <IconButton onClick={handleBack} color="primary" size="small">
            <ArrowBackIcon fontSize="small" />
          </IconButton>
          <Typography variant="subtitle2" fontSize={17} py={2}>
            DELIVERY REPORT
          </Typography>
        </Stack>
      )}
      <Paper
        sx={{
          border: selectedData?.length === 0 ? `0px` : `1px solid ${theme.palette.grey[300]}`,
          width: '100%',
          overflow: 'auto',
        }}
      >
        <Box
          sx={{
            height: 'calc(100vh - 23.7rem)',
            width: { xs: selectedData?.length !== 0 ? '43.75rem' : '100%', md: '100%' },
          }}
        >
          {view === 'main' ? (
            <DataTable
              ShowCheckBox
              disabledCheckBox={disabledCheckBoxes}
              RowSelected
              setSelectedRows={setSelectedRows}
              selectedRows={selectedRows}
              columns={conveyorListColumns}
              data={selectedData}
              getRowKey={getRowKey}
              fetchStatus={ConveyorListStatus}
            />
          ) : (
            <DataTable
              setSelectedRows={setSelectedRows}
              selectedRows={selectedRows}
              columns={conveyorListColumns}
              data={sendResults}
              getRowKey={getRowKey}
              fetchStatus={ConveyorListStatus}
            />
          )}
        </Box>
      </Paper>
      <Box
        display="flex"
        sx={{
          justifyContent: {
            xs: 'right',
          },
          pr: {
            lg: '5',
          },
          pt: 3,
        }}
      >
        {view === 'main' && selectedData.length !== 0 && (
          <Stack
            spacing={2}
            direction={{ xs: 'column', sm: 'row' }}
            justifyContent={{ xs: 'ceneter', sm: 'space-between' }}
            width={sendResults.length > 0 ? '100%' : 'auto'}
          >
            {sendResults && sendResults.length > 0 && (
              <Button
                className="delivery-report-btn"
                sx={{ width: { xs: '48.5%', sm: 'auto' } }}
                color="warning"
                onClick={() => setView('report')}
                variant="contained"
              >
                Delivery Report
              </Button>
            )}
            <Stack spacing={2} direction="row">
              <Button
                className="cancel-btn"
                // sx={{ width: { xs: '25%', sm: 'auto' } }}
                onClick={handleCancel}
                variant="contained"
                color="secondary"
              >
                Cancel
              </Button>
              <LoadingButton
                fullWidth
                className="send-btn"
                loadingPosition="start"
                endIcon={<SendIcon />}
                // sx={{ width: { sm: 'auto' } }}
                onClick={voiceId && voiceId !== 0 ? handleSendVoiceMessage : handleSendMessage}
                loading={isSubmitting}
                variant="contained"
                color="primary"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Sending...' : 'Send'}
              </LoadingButton>
            </Stack>
          </Stack>
        )}
      </Box>
      {isSubmitting ? (
        <LoadingPopup popupContent={<LoadingMessage icon={LoadingMsg} message="Sending messages please wait." />} />
      ) : null}
    </ConveyorsRoot>
  );
}

export default Conveyors;
