import Loadable from '@/components/shared/Loadable';
import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';

const PublishResult = Loadable(lazy(() => import('@/pages/PublishResult')));
const UpdateResult = Loadable(lazy(() => import('@/features/PublishResult/UpdateResult')));
const ResultPublish = Loadable(lazy(() => import('@/features/PublishResult/ResultPublish')));

export const publishResultRoutes: RouteObject[] = [
  {
    path: '/publish-result',
    element: <PublishResult />,
    children: [
      {
        path: 'update',
        element: <UpdateResult />,
      },
      {
        path: 'publish',
        element: <ResultPublish />,
      },
    ],
  },
];
