/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/alt-text */
import React, { ChangeEvent, useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  Button,
  Typography,
  Card,
  Tooltip,
  IconButton,
  Collapse,
  FormControl,
  SelectChangeEvent,
  MenuItem,
  Select,
  useTheme,
} from '@mui/material';
import styled from 'styled-components';
import SearchIcon from '@mui/icons-material/Search';
import { IoIosArrowUp } from 'react-icons/io';
import Lot<PERSON> from 'lottie-react';
import successIcon from '@/assets/MessageIcons/success.json';
import { MdAdd } from 'react-icons/md';
import ErrorIcon from '@/assets/ManageFee/ErrorIcon.json';
import Success from '@/assets/ManageFee/Success.json';
import deleteBin from '@/assets/ManageFee/deleteBin.json';
import deleteSuccess from '@/assets/ManageFee/deleteSuccess.json';
import errorIcon from '@/assets/ManageFee/Error.json';
import DeleteIcon from '@mui/icons-material/Delete';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import { ROLL_OPTIONS } from '@/config/Selection';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import typography from '@/theme/typography';
import useSettings from '@/hooks/useSettings';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import useAuth from '@/hooks/useAuth';
import {
  CTSAllocationMappedInfo,
  CTSAllocationMappedResponseInfo,
  CTSAllocationMappingFilter,
  DeleteCTSAllocationRequest,
} from '@/types/StaffManagement';
import {
  AddCTSAllocationMap,
  deleteCTS,
  fetchCTSFilter,
  fetchCTSMapping,
} from '@/store/StaffMangement/StaffMangement/staffMangement.thunks';
import {
  getCTSClassList,
  getCTSDeletingRecords,
  getCTSDetailsListPageInfo,
  getCTSFilterListStatus,
  getCTSMappingListData,
  getCTSMappingListStatus,
  getCTSSortColumn,
  getCTSSortDirection,
  getCTSStaffListData,
  getCTSYearList,
  getStaffSubmitting,
} from '@/config/storeSelectors';
import { useAppSelector } from '@/hooks/useAppSelector';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { ErrorMessage } from '@/components/shared/Popup/ErrorMessage';
import LoadingButton from '@mui/lab/LoadingButton';
import { TextField } from '@mui/material';
import { Autocomplete } from '@mui/material';
import { Checkbox } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import BackButton from '@/components/shared/BackButton';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';

const CtsAllocationClassWiseRoot = styled.div`
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 576px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 100px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid ${(props) => props.theme.palette.grey[200]};
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
        /* .MuiTableCell-root:first-child {
          padding: 5px;
        } */
      }
    }
  }
`;
type CtsPropsTypes = {
  onBackClick?: () => void;
};

function CtsAllocationClassWise({ onBackClick }: CtsPropsTypes) {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const dispatch = useAppDispatch();
  const { confirm } = useConfirm();
  const { user } = useAuth();
  const adminId: number = user ? user.accountId : 0;

  const [showFilter, setShowFilter] = useState(true);
  const [academicYearFilter, setAcademicYearFilter] = useState(0);
  const [classFilter, setClassFilter] = useState(0);
  const [selectedRows, setSelectedRows] = useState<Record<number, boolean>>({});
  const [individualLoadingMap, setIndividualLoadingMap] = useState<Record<string, boolean>>({});
  const sortColumn = useAppSelector(getCTSSortColumn);
  const sortDirection = useAppSelector(getCTSSortDirection);
  const paginationInfo = useAppSelector(getCTSDetailsListPageInfo);
  const CTSYearList = useAppSelector(getCTSYearList);
  const CTSClassList = useAppSelector(getCTSClassList);
  const CTSFilterListStatus = useAppSelector(getCTSFilterListStatus);
  const CTSMappingListStatus = useAppSelector(getCTSMappingListStatus);
  const CTSMappingListData = useAppSelector(getCTSMappingListData);
  const CTSStaffListData = useAppSelector(getCTSStaffListData);
  const deletingRecords = useAppSelector(getCTSDeletingRecords);
  const isSubmitting = useAppSelector(getStaffSubmitting);
  const [dbStatus, setDbStatus] = useState<'Success' | 'Exist' | 'Failed' | ''>('');
  const [editedRows, setEditedRows] = useState<CTSAllocationMappedInfo[]>([]);
  const [isAllSelected, setIsAllSelected] = useState<boolean>(false);
  const navigate = useNavigate();

  const { pagenumber, pagesize, totalrecords } = paginationInfo;

  const currentCTSClassWiseListRequest: CTSAllocationMappingFilter = React.useMemo(
    () => ({
      adminId,
      academicId: academicYearFilter,
      classId: classFilter,
    }),
    [adminId, academicYearFilter, classFilter]
  );

  const loadCTSClassWiseList = React.useCallback(
    async (request: CTSAllocationMappingFilter) => {
      try {
        const data = await dispatch(fetchCTSMapping(request));
        console.log('CTSStaffListData', CTSStaffListData);
      } catch (error) {
        console.error('Error loading list:', error);
      }
    },
    [dispatch, CTSStaffListData]
  );

  React.useEffect(() => {
    // if (YearStatus === 'idle') {
    //   setAcademicYearFilter(defualtYear);
    // }
    if (CTSMappingListStatus === 'idle') {
      loadCTSClassWiseList(currentCTSClassWiseListRequest);
    }
    if (CTSFilterListStatus === 'idle') {
      const CtsFilerDatas = dispatch(fetchCTSFilter(adminId));
      console.log('CtsFilerDatas', CtsFilerDatas);
    }
  }, [
    dispatch,
    adminId,
    CTSFilterListStatus,
    CTSMappingListStatus,
    loadCTSClassWiseList,
    currentCTSClassWiseListRequest,
  ]);

  const handleYearChange = (e: SelectChangeEvent) => {
    const selectedAcademicId = e.target.value;
    setAcademicYearFilter(parseInt(selectedAcademicId, 10));

    loadCTSClassWiseList({
      ...currentCTSClassWiseListRequest,
      academicId: parseInt(selectedAcademicId, 10),
    });
  };

  const handleClassChange = (e: SelectChangeEvent) => {
    const selectedClass = parseInt(e.target.value, 10);
    if (selectedClass) {
      setClassFilter(selectedClass);
    }
    loadCTSClassWiseList({
      ...currentCTSClassWiseListRequest,
      classId: selectedClass || -1,
    });
  };

  const handleReset = React.useCallback(
    (e: any) => {
      e.preventDefault();
      setAcademicYearFilter(0);
      setClassFilter(0);
      setEditedRows([]);
      loadCTSClassWiseList({
        adminId,
        academicId: 0,
        classId: 0,
      });
    },
    [loadCTSClassWiseList, adminId]
  );

  const handleIndividualMapCTS = useCallback(
    async (row: CTSAllocationMappedInfo) => {
      try {
        // Set loading state for the current subject
        setIndividualLoadingMap((prevMap) => ({ ...prevMap, [row.subjectId]: true }));

        // Map over selected rows and create promises
        const promises = editedRows.map(async (r) => {
          const req = [
            {
              ...row,
              staffId: r.staffId,
              staffRole: r.staffRole,
              adminId,
              academicId: academicYearFilter,
              classId: classFilter,
              dbResult: 'string',
              id: 0,
            },
          ];

          console.log('Request:', req);

          try {
            const response = await dispatch(AddCTSAllocationMap(req)).unwrap();
            console.log('Response:', response);

            // Process each object in the response
            await Promise.all(
              response.map(async (resObj: CTSAllocationMappedResponseInfo) => {
                console.log('Processing dbResult:', resObj.dbResult);
                setDbStatus((prevMap) => ({ ...prevMap, [row.subjectId]: resObj.dbResult }));

                if (resObj.dbResult === 'Success') {
                  loadCTSClassWiseList({ ...currentCTSClassWiseListRequest });
                }
              })
            );

            return response;
          } catch (err) {
            console.error('Error processing request:', req, err);
            return [{ dbResult: 'Failed' }]; // Return a fallback response
          }
        });

        // Wait for all promises to complete
        const allResponses = await Promise.all(promises);
        setEditedRows([]);
        console.log('All Responses:', allResponses.flat());
      } catch (error) {
        const errorMessage = (
          <ErrorMessage loop={false} jsonIcon={ErrorIcon} message="Something went wrong, please try again" />
        );
        await confirm(errorMessage, 'CTS Map', { okLabel: 'Ok', showOnlyOk: true });
        console.error('Error mapping CTS:', error);
      } finally {
        // Reset statuses
        setTimeout(() => {
          setDbStatus((prevMap) => ({ ...prevMap, [row.subjectId]: '' }));
        }, 5000);
        setIndividualLoadingMap((prevMap) => ({ ...prevMap, [row.subjectId]: false }));
      }
    },
    [
      dispatch,
      academicYearFilter,
      confirm,
      loadCTSClassWiseList,
      adminId,
      currentCTSClassWiseListRequest,
      classFilter,
      editedRows,
    ]
  );
  const getRowKey = useCallback((row: CTSAllocationMappedInfo) => row.subjectId, []);

  const SelectedColumns: DataTableColumn<CTSAllocationMappedInfo>[] = useMemo(
    () => [
      {
        name: 'subjectId',
        headerLabel: 'Sl.No',
        dataKey: 'subjectId',
      },
      {
        name: 'subjectName',
        headerLabel: 'Subject',
        dataKey: 'subjectName',
      },
      {
        name: 'staff',
        headerLabel: 'Teacher',
        renderCell: (row) => {
          const currentStaff = CTSStaffListData.find((staff) => staff.staffId === row.staffId);
          const staffName = currentStaff?.staffName;

          return <Typography variant="subtitle2">{staffName}</Typography>;
        },
      },
      {
        name: 'staffRole',
        headerLabel: 'Role',
        width: '200px',
        renderCell: (row) => {
          const currentStaff = ROLL_OPTIONS.find((staff) => staff.id === row.staffRole);
          const staffName = currentStaff?.name;

          return <Typography variant="subtitle2">{staffName}</Typography>;
        },
      },
    ],
    [CTSStaffListData]
  );

  const handleMapCTS = useCallback(async () => {
    try {
      const ConfirmMessage = (
        <SuccessMessage
          message={
            <Card sx={{ my: 1, boxShadow: 0, width: 500, border: 1, borderColor: theme.palette.grey[300] }}>
              <DataTable columns={SelectedColumns} data={editedRows} getRowKey={getRowKey} fetchStatus="success" />
            </Card>
          }
        />
      );
      if (await confirm(ConfirmMessage, 'Map Rows?', { okLabel: 'Confirm', cancelLabel: 'Cancel' })) {
        const promises = editedRows.map(async (row) => {
          // Convert startDate and endDate to DD/MM/YYYY format
          const req = [
            {
              ...row,
              adminId,
              academicId: academicYearFilter,
              classId: classFilter,
              dbResult: 'string',
              id: 0,
            },
          ];
          console.log('updateReq::::----', req);
          const response = await dispatch(AddCTSAllocationMap(req)).unwrap();
          console.log('response::::----', response);

          return response;
        });

        const responses = await Promise.all(promises);

        // Check if all updates were successful
        const isSuccess = responses.every((response) => response);
        const isExist = responses.every((response) => response.dbResult === 'Exist');
        // const isSuccess = responses.every((response) => response.dbResult === 'Success');
        // const isExist = responses.every((response) => response.dbResult === 'Exist');
        console.log('isSuccess::::----', isSuccess);
        console.log('promises::::----', promises);

        if (isSuccess) {
          setEditedRows([]);
          loadCTSClassWiseList({ ...currentCTSClassWiseListRequest });
          const successMessage = <SuccessMessage loop={false} jsonIcon={Success} message="CTS Map successfully" />;
          await confirm(successMessage, 'CTS Map', { okLabel: 'Ok', showOnlyOk: true });
        } else if (isExist) {
          const errorMessage = <ErrorMessage loop={false} jsonIcon={ErrorIcon} message="CTS already created" />;
          await confirm(errorMessage, 'CTS Map', { okLabel: 'Ok', showOnlyOk: true });
        } else {
          const errorMessage = <ErrorMessage loop={false} jsonIcon={ErrorIcon} message="CTS mapped failed" />;
          await confirm(errorMessage, 'CTS Map', { okLabel: 'Ok', showOnlyOk: true });
        }
      }
    } catch (error) {
      const errorMessage = (
        <ErrorMessage loop={false} jsonIcon={ErrorIcon} message="Something went wrong please try again " />
      );
      await confirm(errorMessage, 'CTS Map', { okLabel: 'Ok', showOnlyOk: true });
      // Handle error
      console.error('Error mapping cts :', error);
    }
  }, [
    dispatch,
    academicYearFilter,
    confirm,
    loadCTSClassWiseList,
    adminId,
    currentCTSClassWiseListRequest,
    classFilter,
    editedRows,
    SelectedColumns,
    getRowKey,
    theme,
  ]);

  const handleDeleteCtsList = useCallback(
    async (ctsObj: CTSAllocationMappedInfo) => {
      const { cteacherId } = ctsObj;
      const deleteConfirmMessage = (
        <DeleteMessage jsonIcon={deleteBin} message={<div>Are you sure you want to delete the CTS Row ?</div>} />
      );
      if (await confirm(deleteConfirmMessage, 'Delete Row?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
        const sendReq = [{ adminId, cteacherId, dbResult: 'string', id: 0 }];

        console.log('sendReq', sendReq);

        const deleteResponse = await dispatch(deleteCTS(sendReq));
        console.log('deleteResponse::::----', deleteResponse);
        if (deleteResponse && Array.isArray(deleteResponse.payload)) {
          const results: DeleteCTSAllocationRequest[] = deleteResponse.payload;
          const errorMessages = results.find((result) => result.dbResult === 'Failed');
          const successMessages = results.find((result) => result.dbResult === 'Success');
          // Reload only the specific message template with the deleted messageId

          if (!errorMessages) {
            const deleteSuccessMessage = (
              <SuccessMessage loop={false} jsonIcon={deleteSuccess} message="Delete successfully" />
            );
            await confirm(deleteSuccessMessage, 'Success', { okLabel: 'Ok', showOnlyOk: true });
          } else if (!successMessages) {
            const deleteErrorMessage = <DeleteMessage jsonIcon={errorIcon} message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          } else {
            const deleteErrorMessage = <DeleteMessage jsonIcon={errorIcon} message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          }
          loadCTSClassWiseList({ ...currentCTSClassWiseListRequest });

          // setSnackBar(true);
          // setBasicFeeData((prevDetails) => prevDetails.filter((item) => item.feeId !== feeId));
        }
      }
    },
    [dispatch, adminId, confirm, loadCTSClassWiseList, currentCTSClassWiseListRequest]
  );

  const handleDeleteMultiple = useCallback(async () => {
    const sendConfirmMessage = (
      <DeleteMessage jsonIcon={deleteBin} message={<div>Are you sure you want to delete all ?</div>} />
    );
    if (await confirm(sendConfirmMessage, 'Delete Basic Fee?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
      const sendRequests: DeleteCTSAllocationRequest[] = await Promise.all(
        editedRows?.map(async (row) => {
          const sendReq = {
            adminId,
            cteacherId: row.cteacherId,
            dbResult: '',
            id: 0,
          };
          return sendReq;
        }) || []
      );
      const deleteResponse = await dispatch(deleteCTS(sendRequests));

      console.log('deleteResponse', deleteResponse);
      if (deleteResponse && Array.isArray(deleteResponse.payload)) {
        const results: DeleteCTSAllocationRequest[] = deleteResponse.payload;
        const errorMessages = results.find((result) => result.dbResult === 'Failed');
        const successMessages = results.find((result) => result.dbResult === 'Success');

        if (!errorMessages) {
          const deleteSuccessMessage = (
            <SuccessMessage loop={false} jsonIcon={deleteSuccess} message="Delete All Successfully" />
          );
          await confirm(deleteSuccessMessage, 'Success', { okLabel: 'Ok', showOnlyOk: true });
        } else if (!successMessages) {
          const deleteErrorMessage = <DeleteMessage loop={false} jsonIcon={errorIcon} message="Delete Failed" />;
          await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
        } else {
          const deleteErrorMessage = (
            <DeleteMessage loop={false} jsonIcon={errorIcon} message="Something went wrong please try again" />
          );
          await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
        }
        loadCTSClassWiseList({ ...currentCTSClassWiseListRequest });
        setIsAllSelected(false);
        setSelectedRows({});
        setEditedRows([]);
        // setBasicFeeData((prevDetails) =>
        //   prevDetails.filter((item: CreateBasicFeeSettingTitleDataType) => !selectedRows.includes(item.feeId))
        // );
      }
    }
  }, [confirm, dispatch, editedRows, adminId, loadCTSClassWiseList, currentCTSClassWiseListRequest]);

  const handleRowAllClick = useCallback(
    (event: ChangeEvent<HTMLInputElement>) => {
      const isChecked = event.target.checked;
      setIsAllSelected(isChecked);

      // Delay the heavy update logic to the next paint cycle
      // requestAnimationFrame(() => {
      const newSelectedRows = isChecked
        ? CTSMappingListData.reduce((acc, item) => ({ ...acc, [item.subjectId]: true }), {})
        : {};
      const newEditedRows = isChecked ? CTSMappingListData : [];

      setSelectedRows(newSelectedRows);
      setEditedRows(newEditedRows);
      // });
    },
    [CTSMappingListData]
  );

  const handleRowClick = (event: React.ChangeEvent<HTMLInputElement>, row: CTSAllocationMappedInfo) => {
    const { checked } = event.target;

    requestAnimationFrame(() => {
      setSelectedRows((prev) => ({
        ...prev,
        [row.subjectId]: checked,
      }));

      setEditedRows((prev) => {
        if (checked) {
          return prev.some((item) => item.subjectId === row.subjectId) ? prev : [...prev, row];
        }
        return prev.filter((item) => item.subjectId !== row.subjectId);
      });
    });
  };

  const CTSAllocationMappingColumns: DataTableColumn<CTSAllocationMappedInfo>[] = useMemo(
    () => [
      {
        name: 'checkBox',
        renderHeader: () => {
          return (
            <Checkbox
              disabled={isSubmitting}
              color="primary"
              onChange={handleRowAllClick}
              indeterminate={
                CTSMappingListData.length > 0 &&
                Object.values(selectedRows).some((checked) => checked) &&
                !isAllSelected
              }
              // indeterminate={
              //   Object.keys(selectedRows).length > 0 && Object.keys(selectedRows).length < qucikUpdateStudentListData.length
              // }
              checked={isAllSelected}
            />
          );
        },
        renderCell: (row) => {
          const isRowSelected = selectedRows[row.subjectId] || false;
          return (
            <Checkbox
              disabled={isSubmitting}
              color="primary"
              checked={isRowSelected}
              onChange={(e) => {
                handleRowClick(e, row);
              }}
              inputProps={{ 'aria-labelledby': `checkbox-${row}` }}
            />
          );
        },
      },
      {
        name: 'subjectId',
        headerLabel: 'Sl.No',
        dataKey: 'subjectId',
      },
      {
        name: 'subjectName',
        headerLabel: 'Subject',
        dataKey: 'subjectName',
        sortable: true,
      },
      {
        name: 'staff',
        headerLabel: 'Teacher',
        width: '200px',
        renderCell: (row) => {
          const editedRow = editedRows.find((r) => r.subjectId === row.subjectId);
          // const isSelected = Boolean(selectedRow);
          const isSelected = selectedRows[row.subjectId] || false;
          const isSelectedStaffId = editedRow?.staffId ?? -1;
          const currentStaff = CTSStaffListData.find((staff) => staff.staffId === row.staffId);
          const staffName = currentStaff?.staffName;

          if (!isSelected && staffName) {
            return <Typography variant="subtitle2">{staffName}</Typography>;
          }

          return (
            <Autocomplete
              value={isSelected ? CTSStaffListData.find((opt) => opt.staffId === isSelectedStaffId) || null : null}
              options={CTSStaffListData}
              getOptionLabel={(option) => option.staffName || ''}
              isOptionEqualToValue={(option, value) => option.staffId === value.staffId}
              disabled={!isSelected}
              onChange={(event, newValue) => {
                const selectedStaffId = newValue ? newValue.staffId : -1;
                setEditedRows((prevRows) =>
                  prevRows.map((item) =>
                    item.subjectId === row.subjectId ? { ...item, staffId: selectedStaffId } : item
                  )
                );
              }}
              renderInput={(params) => (
                <TextField
                  {...params}
                  placeholder="Select Teacher"
                  sx={{
                    '& .MuiInputBase-root': {
                      padding: '6px', // Adjust padding for the input field
                      backgroundColor: !isSelected
                        ? isLight
                          ? theme.palette.grey[300]
                          : theme.palette.grey[900]
                        : 'transparent',
                    },
                    '& .MuiAutocomplete-endAdornment': {
                      right: '4px', // Adjust the position of the dropdown icon
                    },
                  }}
                />
              )}
              ListboxProps={{ style: { maxHeight: '250px' } }} // Adjust dropdown height
            />
          );
        },
      },
      {
        name: 'staffRole',
        headerLabel: 'Role',
        width: '200px',
        renderCell: (row) => {
          const editedRow = editedRows.find((r) => r.subjectId === row.subjectId);
          // const isSelected = Boolean(selectedRow);
          const isSelected = selectedRows[row.subjectId] || false;
          const isSelectedStaffRole = editedRow?.staffRole ?? -1;
          const currentStaff = ROLL_OPTIONS.find((staff) => staff.id === row.staffRole);
          const staffName = currentStaff?.name;

          // Check if staffRole id 1 exists in CTSMappingListData
          const isRoleOnePresent = CTSMappingListData.some((data) => data.staffRole === 1);

          // Modify ROLL_OPTIONS to include disabled property
          const optionsWithDisabled = ROLL_OPTIONS.map((option) => ({
            ...option,
            disabled: option.id === 1 && isRoleOnePresent,
          }));

          if (!isSelected && staffName) {
            return <Typography variant="subtitle2">{staffName}</Typography>;
          }

          return (
            <Autocomplete
              value={isSelected ? ROLL_OPTIONS.find((opt) => opt.id === isSelectedStaffRole) || null : null}
              options={optionsWithDisabled}
              getOptionLabel={(option) => option.name || ''}
              isOptionEqualToValue={(option, value) => option.id === value.id}
              disabled={!isSelected}
              onChange={(event, newValue) => {
                const selectedStaffRole = newValue ? newValue.id : -1;
                setEditedRows((prevRows) =>
                  prevRows.map((item) =>
                    item.subjectId === row.subjectId ? { ...item, staffRole: selectedStaffRole } : item
                  )
                );
              }}
              renderInput={(params) => (
                <TextField
                  {...params}
                  placeholder="Select Role"
                  sx={{
                    '& .MuiInputBase-root': {
                      padding: '6px', // Adjust padding for the input field
                      backgroundColor: !isSelected
                        ? isLight
                          ? theme.palette.grey[300]
                          : theme.palette.grey[900]
                        : 'transparent', // Change background when disabled
                    },
                    '& .MuiAutocomplete-endAdornment': {
                      right: '4px', // Adjust the position of the dropdown icon
                    },
                  }}
                />
              )}
              ListboxProps={{
                style: { maxHeight: '250px' }, // Adjust dropdown height
              }}
              renderOption={(props, option) => (
                <li {...props} aria-disabled={option.disabled} style={{ opacity: option.disabled ? 0.5 : 1 }}>
                  {option.name}
                </li>
              )}
            />
          );
        },
      },

      {
        name: 'actions',
        headerLabel: 'Actions',
        renderCell: (row) => {
          const disabledDelete = row.cteacherId > 0;
          // const editedRow = editedRows.find((r) => r.subjectId === row.subjectId);
          // const isSelected = Boolean(selectedRow);
          const isSelected = selectedRows[row.subjectId] || false;
          return (
            <Stack direction="row" gap={1}>
              <IconButton
                onClick={() => handleDeleteCtsList(row)}
                disabled={!disabledDelete}
                size="small"
                color="error"
                sx={{ padding: 0.5 }}
              >
                <DeleteIcon />
              </IconButton>
              {dbStatus[row.subjectId] === 'Success' ? (
                <Stack>
                  <Lottie animationData={successIcon} loop={false} style={{ width: '35px' }} />
                </Stack>
              ) : dbStatus[row.subjectId] === 'Exist' ? (
                <Stack>
                  <Lottie animationData={errorIcon} loop={false} style={{ width: '30px' }} />
                </Stack>
              ) : dbStatus[row.subjectId] === 'Failed' ? (
                <Stack>
                  <Lottie animationData={errorIcon} loop={false} style={{ width: '30px' }} />
                </Stack>
              ) : (
                <LoadingButton
                  disabled={!isSelected}
                  loading={individualLoadingMap[row.subjectId]}
                  variant="contained"
                  size="small"
                  color={!isSelected ? 'secondary' : 'primary'}
                  sx={{ p: 0.5 }}
                  onClick={() => handleIndividualMapCTS(row)}
                >
                  {individualLoadingMap[row.subjectId] ? '' : 'Map'}
                </LoadingButton>
              )}
            </Stack>
          );
        },
      },
    ],
    [
      selectedRows,
      dbStatus,
      CTSStaffListData,
      isLight,
      handleIndividualMapCTS,
      CTSMappingListData,
      individualLoadingMap,
      editedRows,
      theme,
      handleRowAllClick,
      handleDeleteCtsList,
      isAllSelected,
      isSubmitting,
    ]
  );

  return (
    <Page title="CTS Allocation ClassWise">
      <CtsAllocationClassWiseRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack
            direction="row"
            alignItems="start"
            justifyContent="space-between"
            flex={{ xs: 1, sm: 0 }}
            flexWrap="nowrap"
          >
            <Stack direction="row" alignItems="center">
              <BackButton
                onBackClick={() => {
                  onBackClick();
                  navigate('/staff-management/allocation-list');
                }}
              />
              <Typography variant="h6" fontSize={17} width="100%">
                CTS Allocation Class Wise
              </Typography>
            </Stack>
            <Box pb={1} sx={{ flexShrink: 0 }}>
              {editedRows.length > 0 && (
                <Tooltip title="Delete All">
                  <IconButton
                    sx={{ visibility: { xs: 'hidden', sm: 'visible' }, mr: 1 }}
                    aria-label="delete"
                    color="error"
                    onClick={handleDeleteMultiple}
                  >
                    <DeleteIcon />
                  </IconButton>
                </Tooltip>
              )}

              {showFilter === false ? (
                <Tooltip title="Search">
                  <IconButton
                    aria-label="delete"
                    color="primary"
                    sx={{ mr: { xs: 0, sm: 1 } }}
                    onClick={() => setShowFilter((x) => !x)}
                  >
                    <SearchIcon />
                  </IconButton>
                </Tooltip>
              ) : (
                <IconButton
                  aria-label="delete"
                  color="primary"
                  sx={{ mr: { xs: 0, sm: 1 } }}
                  onClick={() => setShowFilter((x) => !x)}
                >
                  <IoIosArrowUp />
                </IconButton>
              )}
            </Box>
          </Stack>
          <Stack>
            <Divider />
          </Stack>
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate onReset={handleReset}>
                <Grid pb={5} pt={1} container spacing={2}>
                  <Grid item xxl={2} xl={2.5} lg={2.5} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Select
                        labelId="academicYearFilter"
                        id="academicYearFilterSelect"
                        value={academicYearFilter?.toString()}
                        onChange={handleYearChange}
                        placeholder="Select Year"
                      >
                        <MenuItem value={0}>Select</MenuItem>
                        {CTSYearList.map((opt) => (
                          <MenuItem key={opt.academicId} value={opt.academicId}>
                            {opt.academicTime}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={2.5} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Select Class
                      </Typography>
                      <Select
                        labelId="classFilter"
                        id="classFilterSelect"
                        value={classFilter?.toString()}
                        onChange={handleClassChange}
                        placeholder="Select Class"
                        MenuProps={{
                          PaperProps: {
                            style: {
                              maxHeight: '250px', // Adjust the value to your desired height
                            },
                          },
                        }}
                      >
                        <MenuItem value={0}>Select</MenuItem>
                        {CTSClassList.map((opt) => (
                          <MenuItem key={opt.classId} value={opt.classId}>
                            {opt.className}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item sm={1} xs={12}>
                    <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, sm: 3.79 } }}>
                      <Button type="reset" variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>
            <Paper className="card-table-container">
              <DataTable
                showHorizontalScroll
                // ShowCheckBox
                // selectedRows={selectedRows}
                // setSelectedRows={setSelectedRows}
                // RowSelected
                columns={CTSAllocationMappingColumns}
                tableStyles={{ minWidth: { xs: '1000px' } }}
                data={CTSMappingListData}
                getRowKey={getRowKey}
                fetchStatus="success"
                // fetchStatus={CTSMappingListStatus}
                deletingRecords={deletingRecords}
              />
            </Paper>
          </div>
          <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 3 }}>
            <Stack spacing={2} direction="row">
              <Button
                disabled={CTSMappingListData.length === 0}
                variant="contained"
                color="secondary"
                sx={{ fontFamily: typography.fontFamily }}
              >
                Cancel
              </Button>
              <LoadingButton
                sx={{ width: 100 }}
                // loading={isSubmitting}
                onClick={handleMapCTS}
                variant="contained"
                color={editedRows.length === 0 ? 'secondary' : 'primary'}
                disabled={editedRows.length === 0}
              >
                Map All
              </LoadingButton>
            </Stack>
          </Box>
        </Card>
      </CtsAllocationClassWiseRoot>
    </Page>
  );
}

export default CtsAllocationClassWise;
