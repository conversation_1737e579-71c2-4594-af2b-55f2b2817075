/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  Collapse,
  FormControl,
} from '@mui/material';
import styled from 'styled-components';
import SearchIcon from '@mui/icons-material/Search';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { CLASS_SELECT } from '@/config/Selection';
import { Student } from '@/config/StudentDetails';
import Popup from '@/components/shared/Popup/Popup';
import CreateTC from './CreateTC';

const TClistRoot = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      height: calc(100% - 45px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;
export const data = [
  {
    slNo: 1,
    admissionNumber: 1000,
    class: 'VII-B',
    studentName: 'Alex',
    phoneNumber: '+91-9856773663',
    academicYear: '2023-2024',
    gender: 'Male',
    bloodGroup: 'AB+',
    DOB: '10-05-2000',
    parentName: 'Jack Peter',
  },
  {
    slNo: 2,
    admissionNumber: 1001,
    class: 'VII-B',
    studentName: 'Mathew',
    academicYear: '2023-2024',
    phoneNumber: '+91-9856773663',
    gender: 'Female',
    bloodGroup: 'A+',
    DOB: '10-05-2000',
    parentName: 'Jack Peter',
  },
  {
    slNo: 3,
    admissionNumber: 1002,
    class: 'VII-B',
    studentName: 'Peter',
    academicYear: '2023-2024',
    phoneNumber: '+91-9856773663',
    gender: 'Male',
    bloodGroup: 'A',
    DOB: '10-05-2000',
    parentName: 'Jack Peter',
  },
  {
    slNo: 4,
    admissionNumber: 1013,
    class: 'VII-B',
    studentName: 'Alex Mic',
    academicYear: '2023-2024',
    phoneNumber: '+91-9856773663',
    gender: 'Male',
    bloodGroup: 'A-ve',
    DOB: '10-05-2000',
    parentName: 'Jack Peter',
  },
  {
    slNo: 5,
    admissionNumber: 1014,
    class: 'VII-B',
    studentName: 'john',
    academicYear: '2023-2024',
    phoneNumber: '+91-9856773663',
    gender: 'Male',
    bloodGroup: 'AB-ve',
    DOB: '10-05-2000',
    parentName: 'Jack Peter',
  },
  {
    slNo: 6,
    admissionNumber: 1003,
    class: 'VII-B',
    studentName: 'Micheal',
    academicYear: '2023-2024',
    phoneNumber: '+91-9856773663',
    gender: 'Male',
    bloodGroup: 'O-ve',
    DOB: '10-05-2000',
    parentName: 'Jack Peter',
  },
  {
    slNo: 7,
    admissionNumber: 1004,
    class: 'VII-B',
    studentName: 'jack',
    academicYear: '2023-2024',
    phoneNumber: '+91-9856773663',
    gender: 'Male',
    bloodGroup: 'O-ve',
    DOB: '10-05-2000',
    parentName: 'Jack Peter',
  },
  {
    slNo: 1,
    admissionNumber: 1005,
    class: 'VII-B',
    studentName: 'Alex',
    academicYear: '2023-2024',
    phoneNumber: '+91-9856773663',
    gender: 'Male',
    bloodGroup: 'AB',
    DOB: '10-05-2000',
    parentName: 'Jack Peter',
  },
  {
    slNo: 1,
    admissionNumber: 1006,
    class: 'VII-B',
    studentName: 'Alex',
    academicYear: '2023-2024',
    phoneNumber: '+91-9856773663',
    gender: 'Male',
    bloodGroup: 'AB+',
    DOB: '10-05-2000',
    parentName: 'Jack Peter',
  },
  {
    slNo: 1,
    admissionNumber: 1007,
    class: 'VII-B',
    studentName: 'Alex',
    academicYear: '2023-2024',
    phoneNumber: '+91-9856773663',
    gender: 'Male',
    bloodGroup: 'A+',
    DOB: '10-05-2000',
    parentName: 'Jack Peter',
  },
  {
    slNo: 1,
    admissionNumber: 1008,
    class: 'VII-B',
    studentName: 'Alex',
    academicYear: '2023-2024',
    phoneNumber: '+91-9856773663',
    gender: 'Male',
    bloodGroup: 'O+',
    DOB: '10-05-2000',
    parentName: 'Jack Peter',
  },
  {
    slNo: 1,
    admissionNumber: 1010,
    class: 'VII-B',
    studentName: 'Alex',
    academicYear: '2023-2024',
    phoneNumber: '+91-9856773663',
    gender: 'Male',
    bloodGroup: 'B+',
    DOB: '10-05-2000',
    parentName: 'Jack Peter',
  },
  {
    slNo: 1,
    admissionNumber: 1011,
    class: 'VII-B',
    studentName: 'Alex',
    academicYear: '2023-2024',
    phoneNumber: '+91-9856773663',
    gender: 'Male',
    bloodGroup: 'AB+',
    DOB: '10-05-2000',
    parentName: 'Jack Peter',
  },
  {
    slNo: 1,
    admissionNumber: 1012,
    class: 'VII-B',
    studentName: 'Alex',
    academicYear: '2023-2024',
    phoneNumber: '+91-9856773663',
    gender: 'Male',
    bloodGroup: 'AB+',
    DOB: '10-05-2000',
    parentName: 'Jack Peter',
  },
];

function TClist() {
  const [showFilter, setShowFilter] = useState(false);
  const [open, setOpen] = React.useState(false);
  const [studentSelected, setStudentSelected] = React.useState<Student | null>(null);

  const handleClose = () => {
    setOpen(false);
  };
  const handleCreate = (student: Student) => {
    setStudentSelected(student);
    setOpen(true);
  };

  const getRowKey = useCallback((row: any) => row.examId, []);
  const TClistColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'slNo',
        dataKey: 'slNo',
        headerLabel: 'Sl.No',
        sortable: true,
      },
      {
        name: 'name',
        dataKey: 'studentName',
        headerLabel: 'Student Name',
      },
      {
        name: 'class',
        dataKey: 'class',
        headerLabel: 'Class',
      },
      {
        name: 'parentName',
        dataKey: 'parentName',
        headerLabel: 'Gaurdian Name',
      },
      {
        name: 'mobile',
        dataKey: 'phoneNumber',
        headerLabel: 'Mobile Number',
      },
      {
        name: 'actions',
        headerLabel: 'Actions',
        renderCell: (row) => {
          return (
            <Stack direction="row" gap={1}>
              <Button size="small" variant="contained" onClick={() => handleCreate(row)}>
                Create
              </Button>
            </Stack>
          );
        },
      },
    ],
    []
  );

  return (
    <Page title="Fee Alert Pending">
      <TClistRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17}>
              Transfer Certificates
            </Typography>
            <Box pb={1} sx={{ flexShrink: 0 }}>
              <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                <SearchIcon />
              </IconButton>
            </Box>
          </Stack>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={3} alignItems="end">
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 180 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Student Name
                      </Typography>
                      <TextField placeholder="Enter Name" />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 180 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Autocomplete
                        options={CLASS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select Class" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 180 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Guardian Name
                      </Typography>
                      <TextField placeholder="Enter Name" />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 180 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Mobile
                      </Typography>
                      <TextField placeholder="Enter No." />
                    </FormControl>
                  </Grid>

                  <Grid item lg="auto" xs={12} sm={6}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Paper className="card-table-container" sx={{ marginTop: '12px' }}>
              <DataTable columns={TClistColumns} data={data} getRowKey={getRowKey} fetchStatus="success" />
            </Paper>
          </div>
        </Card>
      </TClistRoot>
      <Popup
        size="md"
        state={open}
        onClose={handleClose}
        popupContent={studentSelected && <CreateTC student={studentSelected} onCancel={handleClose} />}
      />
    </Page>
  );
}

export default TClist;
