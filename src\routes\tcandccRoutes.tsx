/* eslint-disable prettier/prettier */
import Loadable from '@/components/shared/Loadable';
import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';

const TcandCc = Loadable(lazy(() => import('@/pages/Tc&Cc')));
const TcList = Loadable(lazy(() => import('@/features/Tc&Cc/TcList/TcList')));
const CcList = Loadable(lazy(() => import('@/features/Tc&Cc/CcList/CcList')));
export const tcandCcRoutes: RouteObject[] = [
  {
    path: '/tc-cc',
    element: <TcandCc />,
    children: [
      {
        path: 'tc-list',
        element: <TcList />,
      },
      {
        path: 'cc-list',
        element: <CcList />,
      },
    ],
  },
];
