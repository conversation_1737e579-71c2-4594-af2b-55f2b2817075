import styled from 'styled-components';
import { Box, IconButton, Typography } from '@mui/material';
import BirthdayList from '@/Parent-Side/features/Dashboard/Birthday/BirthdayList';
import CloseIcon from '@mui/icons-material/Close';

const BirthdayRoot = styled.div`
  padding: 1rem;
  min-height: calc(100vh - 540px);

  .tittle {
    text-align: start;
    /* font-family: Poppins semibold; */
  }
`;

function Birthday({ closeClick }: any) {
  return (
    <BirthdayRoot>
      <Box sx={{ paddingBottom: '1rem' }} className="d-block d-lg-none">
        <IconButton
          aria-label="close"
          onClick={closeClick}
          sx={{
            position: 'absolute',
            right: 10,
            top: 0,
            color: (theme) => theme.palette.grey[500],
          }}
        >
          <CloseIcon />
        </IconButton>
      </Box>
      <Typography variant="h6" fontSize={18} className="tittle">
        Birthdays
      </Typography>
      <BirthdayList />
    </BirthdayRoot>
  );
}

export default Birthday;
