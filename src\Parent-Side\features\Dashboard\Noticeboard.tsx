import { Box, List, <PERSON>Item, Avatar, ListItemAvatar, Stack, Typography } from '@mui/material';
// import List from '@/theme/overrides/List';
import birthdayIcon from '@/assets/birthday/birthday.png';
import birthday from '@/assets/birthday/birthday-cake.png';
import styled from 'styled-components';
import AccessAlarmsOutlinedIcon from '@mui/icons-material/AccessAlarmsOutlined';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useAppSelector } from '@/hooks/useAppSelector';
import { getdashboardBdayListData, getdashboardBdayListStatus } from '@/config/storeSelectors';
import { fetchDashboardBday } from '@/store/Dashboard/dashboard.thunks';
import { useEffect } from 'react';
import useAuth from '@/hooks/useAuth';
import { useTheme } from '@mui/material';

// const dummyArray = [
//   {
//     id: '01',
//     label: '<PERSON>',
//     class: 'VIII-B',
//     rollNo: '1',
//     image:
//       'https://images.unsplash.com/photo-1633332755192-727a05c4013d?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=580&q=80',
//   },
//   {
//     id: '02',
//     label: 'Sara Mary Lynch',
//     class: 'VIII-B',
//     rollNo: '1',
//     image: '',
//   },
// ];

const NoticeboardRoot = styled.div`
  /* min-height: calc(100% - 50px); */
  overflow-y: scroll;
  padding: 1rem;
  height: 100%;
  .text {
    font-size: 10px;
    font-weight: 600;
  }
  .list {
    overflow-y: scroll;
  }
  .list::-webkit-scrollbar {
    display: none;
  }
  .List-item {
    border-bottom: 1px solid
      ${(props) => (props.theme.themeMode === 'light' ? props.theme.palette.grey[200] : props.theme.palette.grey[800])};
    padding: 0.59rem 0;
  }
  .List-item:last-child {
    border-bottom: none;
  }
  .bday_image {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.info.lighter : props.theme.palette.grey[700]};
  }
`;

function Noticeboard() {
  const theme = useTheme();
  const { user } = useAuth();
  const adminId: number | undefined = user?.accountId;
  const dispatch = useAppDispatch();
  const dashboardBdayListStatus = useAppSelector(getdashboardBdayListStatus);
  const dashboardBdayListData = useAppSelector(getdashboardBdayListData);

  // useEffect(() => {
  //   if (dashboardBdayListStatus === 'idle') {
  //     dispatch(fetchDashboardBday(adminId));
  //   }
  // }, [dispatch, dashboardBdayListStatus, adminId]);
  return (
    <NoticeboardRoot>
      <Stack direction="row" justifyContent="space-between" alignItems="center">
        <Typography variant="subtitle2" fontSize={18}>
          Noticeboard
        </Typography>
        <Typography variant="subtitle2" fontSize={10} color={theme.palette.success.main}>
          See All
        </Typography>
      </Stack>
      {dashboardBdayListData.length === 0 ? (
        <List dense className="list">
          {[1, 2, 3, 4, 5, 6]?.map((list) => {
            return (
              <ListItem key={list.studentId} className="List-item" disablePadding>
                {/* <ListItemButton> */}
                <ListItemAvatar>
                  <Avatar
                    alt=""
                    variant="rounded"
                    sx={{ width: 80, height: 80 }}
                    src="https://images.unsplash.com/photo-1560272564-c83b66b1ad12?q=80&w=1949&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                  />
                </ListItemAvatar>
                <Stack direction="column" flexGrow={1}>
                  <Typography variant="subtitle2">Sports Day</Typography>
                  <Typography
                    sx={{
                      maxWidth: '150px',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      fontSize: '12px',
                      WebkitBoxOrient: 'vertical',
                      WebkitLineClamp: 2, // Set the number of lines to display
                    }}
                  >
                    Lorem ipsum dolor, sit amet consectetur adipisicing elit. Dolores magni, quia similique corrupti,
                    odio dolor minus quos cum adipisci facilis aperiam neque dolore dicta vitae! Aliquam eos libero nisi
                    officia.
                  </Typography>
                  <Box display="flex" alignItems="center">
                    <AccessAlarmsOutlinedIcon color="secondary" sx={{ fontSize: '11px', marginRight: '5px' }} />
                    <Typography
                      sx={{ fontFamily: 'Poppins medium', fontWeight: '500', fontSize: '10px', color: 'gray' }}
                    >
                      Today 01:00 PM
                    </Typography>
                  </Box>
                </Stack>
              </ListItem>
            );
          })}
        </List>
      ) : (
        <Box
          sx={{
            minHeight: 'calc(100vh - 600px)',
            // minHeight: '30vh',
            // overflow: 'auto',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            flexGrow: 1,
            '&::-webkit-scrollbar': {
              width: '0px', // Adjust the width of the scrollbar as per your requirement
            },
          }}
        >
          <Stack alignItems="center" justifyContent="center" height="100%" spacing={1}>
            <img src={birthday} alt="" height={100} style={{ opacity: 0.6 }} />
            <Typography variant="subtitle2" color="GrayText">
              No Birthdays Today
            </Typography>
          </Stack>
        </Box>
      )}
    </NoticeboardRoot>
  );
}

export default Noticeboard;
