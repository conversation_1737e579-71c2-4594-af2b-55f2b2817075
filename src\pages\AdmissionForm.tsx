/* eslint-disable no-nested-ternary */
import LoginCarousel from '@/features/login/LoginCarousel';
import { breakPointsMinwidth } from '@/config/breakpoints';
import styled from 'styled-components';
import Page from '@/components/shared/Page';
import LoginForm from '@/features/login/LoginForm';
import InfoCard from '@/features/login/InfoCard';
import logo from '@/assets/SchoolLogos/HolyLogo.jpeg';
import Pdaily<PERSON>ogo from '@/assets/SchoolLogos/logo-small.svg';
import bgPattern from '@/assets/bg-pattern.jpeg';
import React, { ReactElement, useCallback, useEffect, useRef, useState } from 'react';
import PlaceIcon from '@mui/icons-material/Place';
import LanguageRoundedIcon from '@mui/icons-material/LanguageRounded';
import EmailRoundedIcon from '@mui/icons-material/EmailRounded';
import PhoneRoundedIcon from '@mui/icons-material/PhoneRounded';
import InfoIcon from '@mui/icons-material/Info';
import ParentLogin from '@/Parent-Side/pages/ParentLogin';
import MaleIcon from '@mui/icons-material/Male';
import FemaleIcon from '@mui/icons-material/Female';
import {
  Switch,
  FormControlLabel,
  Grid,
  Stack,
  useTheme,
  Card,
  TextField,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Typography,
  Box,
  Divider,
  FormControl,
  IconButton,
  Collapse,
  useMediaQuery,
  Tabs,
  Tab,
  RadioGroup,
  Radio,
} from '@mui/material';
import { colorPresets, purplePreset } from '@/utils/Colors';
import DateSelect from '@/components/shared/Selections/DateSelect';
import DatePickers from '@/components/shared/Selections/DatePicker';
import Success from '@/assets/Registration/success.gif';
import StThereseLogo from '@/assets/SchoolLogos/StThereseLogo.png';
import Fail from '@/assets/Registration/fail.gif';
import Loading from '@/assets/Registration/loading.gif';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { ErrorIcon } from '@/theme/overrides/CustomIcons';
import axios from 'axios';
import Popup from '@/components/shared/Popup/Popup';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import { ErrorMessage } from '@/components/shared/Popup/ErrorMessage';
import LoadingPopup from '@/components/shared/Popup/LoadingPopup';
import { LoadingMessage } from '@/components/shared/Popup/LoadingMessage';
import dayjs, { Dayjs } from 'dayjs';
import { AppBar } from '@mui/material';
import CheckIcon from '@mui/icons-material/Check';
import { SlUser } from 'react-icons/sl';
import { SlUserFemale } from 'react-icons/sl';
import { SelectChangeEvent } from '@mui/material';
import { useAppSelector } from '@/hooks/useAppSelector';
import { getClassData } from '@/config/storeSelectors';
import { fetchClassList } from '@/store/Dashboard/dashboard.thunks';
import useAuth from '@/hooks/useAuth';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { ClassListInfo } from '@/types/AcademicManagement';
import TabComponent from './TabsTest';
import { Checkbox } from '@mui/material';

const AdmissionFormRoot = styled.div`
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.grey[900]};
  /* min-height: calc(100vh - 160px); */
  /* .MuiOutlinedInput-root {
    border-radius: '10px'; // No border radius
  } */
  /* padding: 1rem; */

  .Card {
    min-height: calc(100vh - 35px);
  }
  /* @media ${breakPointsMinwidth.lg} {
    padding: 2rem;
  } */
`;

const DummyContent =
  ' Lorem Ipsum is simply dummy text of the printing and typesetting industry has been the industry&apos;s standard dummy text ever a type specimen book.';

const TabPanel = ({ children, value, index }) => {
  return value === index && <Box>{children}</Box>;
};

function AdmissionForm() {
  const { user } = useAuth();
  const theme = useTheme();
  const adminId: number | undefined = user?.accountId;
  const { confirm } = useConfirm();
  const dispatch = useAppDispatch();

  //   const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [expanded, setExpanded] = React.useState(false);
  const [popup, setPopup] = React.useState(false);
  const [studentDOB, setStudentDOB] = React.useState<Dayjs | string>('');
  const [fatherDOB, setFatherDOB] = React.useState<Dayjs | string>('');
  const [motherDOB, setMotherDOB] = React.useState<Dayjs | string>('');
  const [guardianDOB, setGuardianDOB] = React.useState<Dayjs | string>('');
  const [vaccinationDate1, setVaccinationDate1] = React.useState<Dayjs | string>('');
  const [vaccinationDate2, setVaccinationDate2] = React.useState<Dayjs | string>('');
  const [vaccinationDate3, setVaccinationDate3] = React.useState<Dayjs | string>('');
  const [date, setMotheDOB] = React.useState<Dayjs | string>('');
  const maxDate = dayjs(new Date());
  const [activeTab, setActiveTab] = useState(0);
  const [previousTab, setPreviousTab] = useState<number | null>(null);
  const [selectedGender, setSelectedGender] = useState(-1);
  const [selectedTransport, setSelectedTransport] = useState(-1);
  const ClassData = useAppSelector(getClassData);
  const AllClassOption = {
    classId: -1,
    className: 'All Class',
    classDescription: 'string',
    classStatus: 1,
  };

  const classDataWithAllClass = [AllClassOption, ...ClassData];
  const [classOptions, setClassOptions] = useState(classDataWithAllClass[0]);
  const { classId, className } = classOptions || {};

  // const handleChange = (event: SelectChangeEvent) => {
  //   const selectedClass = classDataWithAllClass.find((item) => item.className === event.target.value);
  //   if (selectedClass) {
  //     setClassOptions(selectedClass);
  //   }
  // };

  useEffect(() => {
    dispatch(fetchClassList(adminId));
  }, [dispatch, adminId]);

  const tabPanelRef = useRef(null);

  const handleGenderSelect = (gender) => {
    setSelectedGender(gender);
  };

  const handleTransportSelect = (transport) => {
    setSelectedTransport(transport);
  };
  const scrollToTabPanel = () => {
    if (tabPanelRef.current) {
      tabPanelRef.current.scrollIntoView({ top: 100, behavior: 'smooth' });
    }
  };

  // const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
  //   // If we're switching tabs, update previousTab only if it's not the same as activeTab
  //   if (newValue !== activeTab) {
  //     setPreviousTab(activeTab); // Store the currently active tab as the previous tab
  //   }
  //   setActiveTab(newValue); // Set the new active tab
  // };
  const tabLabels = [
    'Details of Candidate : Step 1',
    'About Father : Step 2',
    'About Mother : Step 3',
    'About Guardian : Step 4',
    'Brother/Sister in this School : Step 5',
  ];
  const sections = ['details', 'father', 'mother', 'guardian', 'bro', 'whether', 'blood', 'vaccination'];
  const sectionRefs = useRef<HTMLElement[]>([]);
  const isScrollingRef = useRef(false);

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
    isScrollingRef.current = true;

    const section = sectionRefs.current[newValue];
    if (section) {
      window.scrollTo({ top: section.offsetTop - 80, behavior: 'smooth' });

      setTimeout(() => {
        isScrollingRef.current = false;
      }, 800);
    }
  };

  // Detect active section on scroll
  useEffect(() => {
    const handleScroll = () => {
      if (isScrollingRef.current) return;

      let newActiveTab = 0;
      const scrollPosition = window.scrollY + 100;

      sectionRefs.current.forEach((section, index) => {
        if (section && scrollPosition >= section.offsetTop) {
          newActiveTab = index;
        }
      });

      setActiveTab(newActiveTab);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleExpandClick = () => {
    setExpanded(!expanded);
  };

  const [submitting, setSubmitting] = useState(false);

  const showConfirmation = useCallback(
    async (successMessage: ReactElement, title: string) => {
      const confirmation = confirm(successMessage, title, {
        okLabel: 'Ok',
        showOnlyOk: true,
      });

      return confirmation;
    },
    [confirm]
  );
  const onSave = async (values) => {
    // console.error('values::::', values);
    try {
      // Construct the value object including the Dob field
      const value = {
        ...values,
        SGender: selectedGender,
        SchoolTransport: selectedTransport,
        SDob: studentDOB ? dayjs(studentDOB).format('DD-MM-YYYY') : '', // Format the date before assigning
        FDob: fatherDOB ? dayjs(fatherDOB).format('DD-MM-YYYY') : '', // Format the date before assigning
        MDob: motherDOB ? dayjs(motherDOB).format('DD-MM-YYYY') : '', // Format the date before assigning
        GDob: guardianDOB ? dayjs(guardianDOB).format('DD-MM-YYYY') : '', // Format the date before assigning
        Vaccination1Date: vaccinationDate1 ? dayjs(vaccinationDate1).format('DD-MM-YYYY') : '', // Format the date before assigning
        Vaccination2Date: vaccinationDate2 ? dayjs(vaccinationDate2).format('DD-MM-YYYY') : '', // Format the date before assigning
        Vaccination3Date: vaccinationDate1 ? dayjs(vaccinationDate1).format('DD-MM-YYYY') : '', // Format the date before assigning
        Status: '1',
      };
      setSubmitting(true);
      console.log('values::::', value);
      const response = await axios.post(
        'https://thereseregapi.pasdaily.in/ElixirApi/StudentSet/OnlineAdmissionTherese',
        value
      );
      console.log('response.data::::', response.data);
      const successMessages = response.data.RESULT === 'SUCCESS';
      const errorMessages = response.data.RESULT === 'FAILED';
      const existMessages = response.data.RESULT === 'EXIST';
      console.log('existMessages.data::::', existMessages);

      setSubmitting(false);
      if (successMessages) {
        await showConfirmation(<SuccessMessage icon={Success} message="Enquiry Submitted SuccessFully" />, '');
      } else if (existMessages) {
        await showConfirmation(<ErrorMessage icon={Fail} message="Enquiry Submission Already Exist" />, '');
      } else {
        await showConfirmation(
          <ErrorMessage icon={Fail} message="Enquiry Submission Failed, Please try again later" />,
          ''
        );
      }
    } catch (error) {
      setSubmitting(false);
      await showConfirmation(<ErrorMessage icon={Fail} message="Something went wrong , Please try again later" />, '');
      console.error('Error submitting form:', error);
    } finally {
      setSubmitting(false);
    }
  };
  const CreateEditMessageTempValidationSchema = Yup.object({
    AcademicYear: Yup.string().required('Kindly select the Academic Year.'),
    Class: Yup.string().required('Kindly select the Class.'),
    StudentName: Yup.string().required('Kindly insert the Name.'),
    MotherName: Yup.string().required('Kindly insert the Mother Name.'),
    FatherName: Yup.string().required('Kindly insert the Father Name.'),
    // Dob: Yup.string().required('Kindly insert the Date of Birth.'),
    MobileNumber: Yup.string().required('Kindly insert the Mobile No.'),
    Gender: Yup.string().required('Kindly select the Gender.'),
    SourceOfInformation: Yup.string().required('Kindly select the Source Of Information.'),
  });

  const {
    values: {
      Syllabus,
      ClassName,
      Surname,
      StudentName,
      FathersName,
      SGender,
      SDob,
      SdobInWords,
      SPlaceOfBorth,
      SMotherTongue,
      SReligion,
      SCaste,
      SNationality,
      SchoolLastAttended,
      SAddress1,
      SAddress2,
      SAddress3,
      SEmail,
      SAadharNo,
      FatherName,
      FMotherTongue,
      FDob,
      FReligion,
      FQualification,
      FOccupation,
      FNameOfCompany,
      FCompanyYear,
      FCompanyMonth,
      FOfficeAddress,
      FOfficeTelephone,
      FOfficeMobile,
      MotherName,
      MMotherTongue,
      MDob,
      MReligion,
      MQualification,
      MOccupation,
      MNameOfCompany,
      MCompanyYear,
      MCompanyMonth,
      MOfficeAddress,
      MOfficeTelephone,
      MOfficeMobile,
      GuardianName,
      GMotherTongue,
      GDob,
      GReligion,
      GQualification,
      GOccupation,
      GNameOfCompany,
      GCompanyYear,
      GCompanyMonth,
      GOfficeAddress,
      GOfficeTelephone,
      GOfficeMobile,
      Sibling1,
      Sibling1Std,
      Sibiling2,
      Sibling2Std,
      Sibling3,
      Sibling3Std,
      SchoolTransport,
      StopName,
      StudentBloodGroup,
      Vaccination1,
      Vaccination1Date,
      Vaccination2,
      Vaccination2Date,
      Vaccination3,
      Vaccination3Date,
    },
    handleChange,
    handleBlur,
    handleSubmit,
    touched,
    errors,
  } = useFormik<any>({
    initialValues: {
      Syllabus: -1,
      ClassName: '',
      Surname: '',
      StudentName: '',
      FathersName: '',
      SGender: -1,
      SDob: '',
      SdobInWords: '',
      SPlaceOfBorth: '',
      SMotherTongue: '',
      SReligion: '',
      SCaste: '',
      SNationality: '',
      SchoolLastAttended: '',
      SAddress1: '',
      SAddress2: '',
      SAddress3: '',
      SEmail: '',
      SAadharNo: '',
      FatherName: '',
      FMotherTongue: '',
      FDob: '',
      FReligion: '',
      FQualification: '',
      FOccupation: '',
      FNameOfCompany: '',
      FCompanyYear: '',
      FCompanyMonth: '',
      FOfficeAddress: '',
      FOfficeTelephone: '',
      FOfficeMobile: '',
      MotherName: '',
      MMotherTongue: '',
      MDob: '',
      MReligion: '',
      MQualification: '',
      MOccupation: '',
      MNameOfCompany: '',
      MCompanyYear: '',
      MCompanyMonth: '',
      MOfficeAddress: '',
      MOfficeTelephone: '',
      MOfficeMobile: '',
      GuardianName: '',
      GMotherTongue: '',
      GDob: '',
      GReligion: '',
      GQualification: ' ',
      GOccupation: '',
      GNameOfCompany: '',
      GCompanyYear: '',
      GCompanyMonth: '',
      GOfficeAddress: '',
      GOfficeTelephone: '',
      GOfficeMobile: '',
      Sibling1: '',
      Sibling1Std: '',
      Sibiling2: '',
      Sibling2Std: '',
      Sibling3: '',
      Sibling3Std: '',
      SchoolTransport: -1,
      StopName: '',
      StudentBloodGroup: '',
      Vaccination1: '',
      Vaccination1Date: '',
      Vaccination2: '',
      Vaccination2Date: '',
      Vaccination3: '',
      Vaccination3Date: '',
    },
    // validationSchema: CreateEditMessageTempValidationSchema,
    onSubmit: (values) => {
      onSave(values);
    },
    validateOnBlur: false,
    // validate: (messageVals) => {
    //   const errorObj: any = {};
    //   messageVals.messageContent.forEach(async (classRow, rowIndex, arr) => {
    //     if (arr.some((x, i) => classRow.Class !== '' && x.Class === classRow.Class && i !== rowIndex)) {
    //       if (!errorObj.classes) {
    //         errorObj.classes = [];
    //       }
    //       errorObj.classes[rowIndex] = {};
    //       errorObj.classes[rowIndex].Class = 'Duplicate class name';
    //     }
    //   });
    //   return errorObj;
    // },
  });

  const isAnyRequiredFieldEmptyStep1 = [
    Surname,
    Syllabus,
    ClassName,
    StudentName,
    FathersName,
    selectedGender,
    studentDOB,
    SdobInWords,
    SPlaceOfBorth,
    SMotherTongue,
    SReligion,
    SCaste,
    SNationality,
    SchoolLastAttended,
    SAddress1,
    SAddress2,
    SAddress3,
    SEmail,
    SAadharNo,
  ].some((field) => field === '' || field === 'Select' || field === -1); // Check if any required field is empty

  const isAnyRequiredFieldEmptyStep2 = [
    FatherName,
    FMotherTongue,
    fatherDOB,
    FReligion,
    FQualification,
    FOccupation,
    FNameOfCompany,
    FCompanyYear,
    FCompanyMonth,
    FOfficeAddress,
    FOfficeTelephone,
    FOfficeMobile,
  ].some((field) => field === '' || field === 'Select' || field === -1); // Check if any required field is empty

  const isAnyRequiredFieldEmptyStep3 = [
    MotherName,
    MMotherTongue,
    motherDOB,
    MReligion,
    MQualification,
    MOccupation,
    MNameOfCompany,
    MCompanyYear,
    MCompanyMonth,
    MOfficeAddress,
    MOfficeTelephone,
    MOfficeMobile,
  ].some((field) => field === '' || field === 'Select' || field === -1);

  const isAnyRequiredFieldEmptyStep4 = [
    GuardianName,
    GMotherTongue,
    guardianDOB,
    GReligion,
    GQualification,
    GOccupation,
    GNameOfCompany,
    GCompanyYear,
    GCompanyMonth,
    GOfficeAddress,
    GOfficeTelephone,
    GOfficeMobile,
  ].some((field) => field === '' || field === 'Select' || field === -1);

  const isAnyRequiredFieldEmptyStep5 = [
    Sibling1,
    Sibling1Std,
    // Sibiling2,
    // Sibling2Std,
    // Sibling3,
    // Sibling3Std,
    selectedTransport,
    StudentBloodGroup,
    Vaccination1,
    vaccinationDate1,
    // Vaccination2,
    // vaccinationDate2,
    // Vaccination3,
    // vaccinationDate3,
  ].some((field) => field === '' || field === 'Select' || field === -1);

  const [autoFillGuardian, setAutoFillGuardian] = useState(false);

  const handleAutoFillGuardian = (event: React.ChangeEvent<HTMLInputElement>) => {
    const isChecked = event.target.checked;
    setAutoFillGuardian(isChecked);

    if (isChecked) {
      const fDOB = fatherDOB ? dayjs(fatherDOB).format('DD-MM-YYYY') : '';
      console.log('fatherDOB::::----', fDOB);
      setGuardianDOB(fDOB);
      // Auto-fill guardian fields with father's details
      setFieldValue('GuardianName', FatherName);
      setFieldValue('GMotherTongue', FMotherTongue);
      setFieldValue('GDob', fDOB);
      setFieldValue('GReligion', FReligion);
      setFieldValue('GQualification', FQualification);
      setFieldValue('GOccupation', FOccupation);
      setFieldValue('GNameOfCompany', FNameOfCompany);
      setFieldValue('GCompanyYear', FCompanyYear);
      setFieldValue('GCompanyMonth', FCompanyMonth);
      setFieldValue('GOfficeAddress', FOfficeAddress);
      setFieldValue('GOfficeTelephone', FOfficeTelephone);
      setFieldValue('GOfficeMobile', FOfficeMobile);
    } else {
      // Clear guardian fields when unchecked
      setGuardianDOB('');
      setFieldValue('GuardianName', '');
      setFieldValue('GMotherTongue', '');
      setFieldValue('GDob', '');
      setFieldValue('GReligion', '');
      setFieldValue('GQualification', '');
      setFieldValue('GOccupation', '');
      setFieldValue('GNameOfCompany', '');
      setFieldValue('GCompanyYear', '');
      setFieldValue('GCompanyMonth', '');
      setFieldValue('GOfficeAddress', '');
      setFieldValue('GOfficeTelephone', '');
      setFieldValue('GOfficeMobile', '');
    }
  };

  const [parentLogin, setParentLogin] = useState(false);
  return (
    <Page title="AdmissionForm">
      <AdmissionFormRoot>
        <Stack alignItems="center" className="container-fluid">
          <img width={100} src={StThereseLogo} alt="StThereseLogo" />
          <Typography
            textAlign="center"
            fontSize={10}
            bgcolor={theme.palette.error.main}
            color={theme.palette.common.white}
            px={1}
            variant="h5"
            fontWeight={600}
          >
            ST. THERESE CONVENT SCHOOL
          </Typography>
          <Typography
            textAlign="center"
            fontSize={10}
            color={theme.palette.error.main}
            fontWeight={600}
            mt={1}
            variant="h5"
          >
            DOMBIVLI
          </Typography>
          <Typography
            textAlign="center"
            fontWeight={600}
            mt={1}
            variant="h4"
            fontSize={{ xs: '20px', sm: '28px', md: '2.125rem' }}
          >
            ST. THERESE CONVENT SCHOOL
          </Typography>
          <Typography
            fontSize={{ xs: '13px', sm: '16px', md: '18px' }}
            textAlign="center"
            fontWeight={600}
            mt={1}
            variant="h6"
          >
            Near Premier Colony, Kolegaon, 1 Dombivli East, Thane (DIst) <br /> Maharashtra-421 204
          </Typography>
        </Stack>
        <Typography
          className="container-fluid"
          bgcolor={theme.palette.grey[300]}
          textAlign="center"
          fontWeight={600}
          mt={5}
          variant="h5"
          py={3}
          fontSize={{ xs: '16px', sm: '18px', md: '1.5rem' }}
        >
          <u>APPLICATION FOR ADMISSION 2025-2026</u>
        </Typography>
        <Stack px={{ xs: 0, md: 10 }}>
          <Box display="flex" justifyContent="space-between" className="container-fluid">
            <Typography fontWeight={600} mt={5} variant="subtitle2">
              SNO.
            </Typography>
            <Typography fontWeight={600} mt={5} variant="subtitle2">
              G.R.NO.
            </Typography>
          </Box>
          <Stack className="container-fluid">
            <Typography fontWeight={600} mt={3} variant="subtitle2">
              Please admit my son/daughter/ward, details about whom are given below:
            </Typography>
            <Typography fontWeight={600} mt={1} variant="subtitle2">
              I declare that the following data are correct
            </Typography>
          </Stack>
          <AppBar position="sticky" color="default" sx={{ top: 0, mt: 2, boxShadow: 0 }}>
            <Tabs
              variant="scrollable"
              value={activeTab}
              onChange={handleTabChange}
              TabIndicatorProps={{
                style: { display: 'none' }, // Hide default underline
              }}
              sx={{
                '& .MuiTabs-flexContainer': {
                  gap: 0, // Ensures no space between Tab components
                },
                '& .MuiTab-root': {
                  textTransform: 'none', // Disable uppercase
                  fontWeight: 'bold', // Bold font
                  px: 3, // Add padding to the tabs
                  py: 1.5, // Add vertical padding
                },
                '& .MuiButtonBase-root.MuiTab-root.Mui-selected': {
                  bgcolor:
                    activeTab === 0
                      ? isAnyRequiredFieldEmptyStep1
                        ? theme.palette.warning.main // If any required field is empty, set background to warning color
                        : theme.palette.success.main // Otherwise, set background to error color
                      : activeTab === 1
                      ? isAnyRequiredFieldEmptyStep2
                        ? theme.palette.warning.main // If any required field is empty, set background to warning color
                        : theme.palette.success.main
                      : activeTab === 2
                      ? isAnyRequiredFieldEmptyStep3
                        ? theme.palette.warning.main // If any required field is empty, set background to warning color
                        : theme.palette.success.main
                      : activeTab === 3
                      ? isAnyRequiredFieldEmptyStep4
                        ? theme.palette.warning.main // If any required field is empty, set background to warning color
                        : theme.palette.success.main
                      : activeTab === 4
                      ? isAnyRequiredFieldEmptyStep5
                        ? theme.palette.warning.main // If any required field is empty, set background to warning color
                        : theme.palette.success.main // Otherwise, set background to error color
                      : theme.palette.warning.main, // Default background color when no condition is met // Active tab background color (red)
                  color: theme.palette.common.white, // Active tab text color
                },
                // '& .MuiTab-root:not(.Mui-selected)': {
                //   color: '#fff', // Active tab text color
                // },
              }}
            >
              {tabLabels.map((tab, index) => (
                <Tab
                  key={index}
                  label={tab}
                  disableRipple
                  sx={{
                    '&:not(:last-of-type)': {
                      marginRight: 0,
                    },
                    // transition: 'transform 300ms ease-in-out',
                    // '&:not(.Mui-selected)': {
                    //   transform:
                    //     index === 0 ? 'translateX(0%)' : activeTab === index ? 'translateX(0%)' : 'translateX(0%)',
                    // },
                    // transform: index === 0 ? 'translateX(100%)' : 'translateX(0%)',
                    // transform: index === 0 ? 'translateX(0%)' : index === 1 ? 'translateX(100%)' : 'translateX(0%)',
                    // transform:
                    //   index === activeTab
                    //     ? 'translateX(0%)'
                    //     : index < activeTab
                    //     ? 'translateX(0%)'
                    //     : 'translateX(0%)',
                    flexGrow: 1, // Spread the tabs evenly
                    textAlign: 'center', // Center-align text
                    minHeight: '48px', // Set height for uniformity
                    // polygon(100% 0%, 100% 100%, 75% 100%, 0% 100%, 5% 50%, 0% 0%)
                    // 100% 0%, 100% 50%, 100% 100%, 0% 100%, 4% 50%, 0% 0%
                    // clipPath:,
                    clipPath:
                      index === 0
                        ? ' polygon(95% 0%, 100% 50%, 95% 100%, 0% 100%, 0% 50%, 0% 0%)'
                        : index === 4
                        ? 'polygon(100% 0%, 100% 100%, 75% 100%, 0% 100%, 5% 50%, 0% 0%)'
                        : 'polygon(94% 0%, 100% 50%, 94% 100%, 0% 100%, 6% 50%, 0% 0%)',
                    bgcolor:
                      index < activeTab
                        ? index === 0 && isAnyRequiredFieldEmptyStep1
                          ? theme.palette.grey[100]
                          : index === 1 && isAnyRequiredFieldEmptyStep2
                          ? theme.palette.grey[100]
                          : index === 2 && isAnyRequiredFieldEmptyStep3
                          ? theme.palette.grey[100]
                          : index === 3 && isAnyRequiredFieldEmptyStep4
                          ? theme.palette.grey[100]
                          : index === 4 && isAnyRequiredFieldEmptyStep5
                          ? theme.palette.grey[100]
                          : theme.palette.success.main
                        : theme.palette.grey[100], // Red for current and previous tabs
                    color:
                      index < activeTab
                        ? index === 0 && isAnyRequiredFieldEmptyStep1
                          ? theme.palette.common.black
                          : index === 1 && isAnyRequiredFieldEmptyStep2
                          ? theme.palette.common.black
                          : index === 2 && isAnyRequiredFieldEmptyStep3
                          ? theme.palette.common.black
                          : index === 3 && isAnyRequiredFieldEmptyStep4
                          ? theme.palette.common.black
                          : index === 4 && isAnyRequiredFieldEmptyStep5
                          ? theme.palette.common.black
                          : theme.palette.common.white
                        : theme.palette.common.black, // White text for current/previous tabs
                  }}
                />
              ))}
            </Tabs>
          </AppBar>
          <Stack className="container-fluid" sx={{ backgroundColor: theme.palette.grey[50], mt: 1 }}>
            <form onSubmit={handleSubmit} noValidate>
              {sections.map((section, index) => (
                <Box key={index} ref={(el) => (sectionRefs.current[index] = el as HTMLElement)}>
                  {index === 0 && (
                    <>
                      <Typography ref={(el) => (sectionRefs.current.details = el)} pt={10} variant="h6" fontSize={15}>
                        1. Details of Candidate
                      </Typography>
                      <Divider sx={{ border: 1 }} />
                      <Grid container direction="row" justifyContent="center">
                        <Grid item lg={5} xs={12}>
                          <Box
                            component="form"
                            noValidate
                            autoComplete="off"
                            display="flex"
                            flexDirection="column"
                            justifyContent="center"
                            alignItems="center"
                            gap={2}
                            sx={{ mt: 2 }}
                          >
                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Board
                              </Typography>
                              <Select
                                size="small"
                                disabled={submitting}
                                variant="outlined"
                                name="Syllabus"
                                required
                                value={Syllabus}
                                onChange={handleChange}
                                error={touched.Syllabus && !!errors.Syllabus}
                              >
                                <MenuItem value="State">State</MenuItem>
                              </Select>
                              {touched.Syllabus && !!errors.Syllabus && (
                                <Typography color="red" fontSize="12px" mt={1} variant="subtitle1">
                                  {/* {errors.Syllabus} */}
                                </Typography>
                              )}
                            </FormControl>

                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Standard in which admission is sought
                              </Typography>
                              <Select
                                size="small"
                                disabled={submitting}
                                variant="outlined"
                                name="ClassName"
                                required
                                value={ClassName}
                                onChange={handleChange}
                                error={touched.className && !!errors.className}
                              >
                                {/* {classDataWithAllClass?.map((item: ClassListInfo) => (
                          <MenuItem sx={{ fontSize: '13px' }} key={item.classId} value={item.className}>
                            {item.className}
                          </MenuItem>
                        ))} */}
                                <MenuItem value="Select">Select</MenuItem>
                                <MenuItem value="Nursery">Nursery</MenuItem>
                                <MenuItem value="Junior KG">Junior KG</MenuItem>
                                <MenuItem value="Senior KG">Senior KG</MenuItem>
                                <MenuItem value="Class 1">Class 1</MenuItem>
                                <MenuItem value="Class 2">Class 2</MenuItem>
                                <MenuItem value="Class 3">Class 3</MenuItem>
                                <MenuItem value="Class 4">Class 4</MenuItem>
                                <MenuItem value="Class 5">Class 5</MenuItem>
                                <MenuItem value="Class 6">Class 6</MenuItem>
                                <MenuItem value="Class 7">Class 7</MenuItem>
                                <MenuItem value="Class 8">Class 8</MenuItem>
                                <MenuItem value="Class 9">Class 9</MenuItem>
                              </Select>
                              {touched.ClassName && !!errors.ClassName && (
                                <Typography color="red" fontSize="12px" mt={1} variant="subtitle1">
                                  {/* {errors.ClassName} */}
                                </Typography>
                              )}
                            </FormControl>

                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Surname
                              </Typography>
                              <TextField
                                fullWidth
                                size="small"
                                onBlur={handleBlur}
                                disabled={submitting}
                                color="primary"
                                required
                                variant="outlined"
                                name="Surname"
                                value={Surname}
                                onChange={handleChange}
                                error={touched.Surname && !!errors.Surname}
                                helperText={errors.Surname}
                                InputProps={{
                                  endAdornment: touched.Surname && !!errors.Surname && <ErrorIcon color="error" />,
                                }}
                              />
                            </FormControl>

                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Name
                              </Typography>
                              <TextField
                                fullWidth
                                size="small"
                                onBlur={handleBlur}
                                disabled={submitting}
                                color="primary"
                                required
                                variant="outlined"
                                name="StudentName"
                                value={StudentName}
                                onChange={handleChange}
                                error={touched.StudentName && !!errors.StudentName}
                                helperText={errors.StudentName}
                                InputProps={{
                                  endAdornment: touched.StudentName && !!errors.StudentName && (
                                    <ErrorIcon color="error" />
                                  ),
                                }}
                              />
                            </FormControl>

                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Father's Name
                              </Typography>
                              <TextField
                                fullWidth
                                size="small"
                                onBlur={handleBlur}
                                disabled={submitting}
                                color="primary"
                                required
                                variant="outlined"
                                name="FathersName"
                                value={FathersName}
                                onChange={handleChange}
                                error={touched.FathersName && !!errors.FathersName}
                                helperText={errors.FathersName}
                                InputProps={{
                                  endAdornment: touched.FathersName && !!errors.FathersName && (
                                    <ErrorIcon color="error" />
                                  ),
                                }}
                              />
                            </FormControl>

                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Select Gender
                              </Typography>
                              <Box
                                sx={{
                                  display: 'flex',
                                  justifyContent: 'space-between',
                                  gap: { xs: 10, sm: 20, md: 30 },
                                }}
                              >
                                <Button
                                  fullWidth
                                  variant={selectedGender === 0 ? 'contained' : 'outlined'}
                                  color="secondary"
                                  onClick={() => handleGenderSelect(0)}
                                  startIcon={<SlUser />}
                                >
                                  Male
                                </Button>
                                <Button
                                  fullWidth
                                  variant={selectedGender === 1 ? 'contained' : 'outlined'}
                                  color="secondary"
                                  onClick={() => handleGenderSelect(1)}
                                  startIcon={<SlUserFemale />}
                                >
                                  Female
                                </Button>
                              </Box>
                            </FormControl>

                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Date of Birth (In figure)
                              </Typography>
                              <DatePickers
                                fullWidth={{ xs: '100%', lg: '570px' }}
                                name="SDob"
                                value={dayjs(studentDOB, 'DD-MM-YYYY')} // Format the date before passing it
                                onChange={(e) => {
                                  const selectedDate = e ? dayjs(e) : ''; // Convert the selected date to dayjs object
                                  setStudentDOB(selectedDate); // Store the selected date in the state
                                }}
                                maxDate={maxDate}
                                variant={{ textField: { variant: 'outlined', fullWidth: true } }}
                              />
                            </FormControl>

                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Date of Birth (In words)
                              </Typography>
                              <TextField
                                fullWidth
                                size="small"
                                onBlur={handleBlur}
                                disabled={submitting}
                                color="primary"
                                required
                                variant="outlined"
                                name="SdobInWords"
                                value={SdobInWords}
                                onChange={handleChange}
                                error={touched.SdobInWords && !!errors.SdobInWords}
                                helperText={errors.SdobInWords}
                                InputProps={{
                                  endAdornment: touched.SdobInWords && !!errors.SdobInWords && (
                                    <ErrorIcon color="error" />
                                  ),
                                }}
                              />
                            </FormControl>

                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Place of Birth
                              </Typography>
                              <TextField
                                fullWidth
                                size="small"
                                onBlur={handleBlur}
                                disabled={submitting}
                                color="primary"
                                required
                                variant="outlined"
                                name="SPlaceOfBorth"
                                value={SPlaceOfBorth}
                                onChange={handleChange}
                                error={touched.SPlaceOfBorth && !!errors.SPlaceOfBorth}
                                helperText={errors.SPlaceOfBorth}
                                InputProps={{
                                  endAdornment: touched.SPlaceOfBorth && !!errors.SPlaceOfBorth && (
                                    <ErrorIcon color="error" />
                                  ),
                                }}
                              />
                            </FormControl>

                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Mother Tongue
                              </Typography>
                              <TextField
                                fullWidth
                                size="small"
                                onBlur={handleBlur}
                                disabled={submitting}
                                color="primary"
                                required
                                variant="outlined"
                                name="SMotherTongue"
                                value={SMotherTongue}
                                onChange={handleChange}
                                error={touched.SMotherTongue && !!errors.SMotherTongue}
                                helperText={errors.SMotherTongue}
                                InputProps={{
                                  endAdornment: touched.SMotherTongue && !!errors.SMotherTongue && (
                                    <ErrorIcon color="error" />
                                  ),
                                }}
                              />
                            </FormControl>

                            <Box
                              sx={{ display: 'flex', justifyContent: 'space-between', gap: { xs: 10, sm: 20, md: 30 } }}
                            >
                              <FormControl fullWidth>
                                <Typography variant="h6" fontSize={13}>
                                  Religion
                                </Typography>
                                <TextField
                                  fullWidth
                                  size="small"
                                  onBlur={handleBlur}
                                  disabled={submitting}
                                  color="primary"
                                  required
                                  variant="outlined"
                                  name="SReligion"
                                  value={SReligion}
                                  onChange={handleChange}
                                  error={touched.SReligion && !!errors.SReligion}
                                  helperText={errors.SReligion}
                                  InputProps={{
                                    endAdornment: touched.SReligion && !!errors.SReligion && (
                                      <ErrorIcon color="error" />
                                    ),
                                  }}
                                />
                              </FormControl>

                              <FormControl fullWidth>
                                <Typography variant="h6" fontSize={13}>
                                  Caste
                                </Typography>
                                <TextField
                                  fullWidth
                                  size="small"
                                  onBlur={handleBlur}
                                  disabled={submitting}
                                  color="primary"
                                  required
                                  variant="outlined"
                                  name="SCaste"
                                  value={SCaste}
                                  onChange={handleChange}
                                  error={touched.SCaste && !!errors.SCaste}
                                  helperText={errors.SCaste}
                                  InputProps={{
                                    endAdornment: touched.SCaste && !!errors.SCaste && <ErrorIcon color="error" />,
                                  }}
                                />
                              </FormControl>
                            </Box>

                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Nationality
                              </Typography>
                              <TextField
                                fullWidth
                                size="small"
                                onBlur={handleBlur}
                                disabled={submitting}
                                color="primary"
                                required
                                variant="outlined"
                                name="SNationality"
                                value={SNationality}
                                onChange={handleChange}
                                error={touched.SNationality && !!errors.SNationality}
                                helperText={errors.SNationality}
                                InputProps={{
                                  endAdornment: touched.SNationality && !!errors.SNationality && (
                                    <ErrorIcon color="error" />
                                  ),
                                }}
                              />
                            </FormControl>

                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                School Last Attended
                              </Typography>
                              <TextField
                                fullWidth
                                size="small"
                                onBlur={handleBlur}
                                disabled={submitting}
                                color="primary"
                                required
                                variant="outlined"
                                name="SchoolLastAttended"
                                value={SchoolLastAttended}
                                onChange={handleChange}
                                error={touched.SchoolLastAttended && !!errors.SchoolLastAttended}
                                helperText={errors.SchoolLastAttended}
                                InputProps={{
                                  endAdornment: touched.SchoolLastAttended && !!errors.SchoolLastAttended && (
                                    <ErrorIcon color="error" />
                                  ),
                                }}
                              />
                            </FormControl>

                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Full Residential Address
                              </Typography>
                              <TextField
                                multiline
                                name="SAddress1"
                                value={SAddress1}
                                onChange={handleChange}
                                error={touched.SAddress1 && !!errors.SAddress1}
                                helperText={errors.SAddress1}
                                disabled={submitting}
                                fullWidth
                                placeholder="Enter address"
                                InputProps={{
                                  inputProps: {
                                    style: { resize: 'vertical', width: '100%', minHeight: '50px', maxHeight: '100px' },
                                  },
                                }}
                              />
                            </FormControl>

                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Locality, Post Taluka
                              </Typography>
                              <TextField
                                fullWidth
                                size="small"
                                onBlur={handleBlur}
                                disabled={submitting}
                                color="primary"
                                required
                                variant="outlined"
                                name="SAddress2"
                                value={SAddress2}
                                onChange={handleChange}
                                error={touched.SAddress2 && !!errors.SAddress2}
                                helperText={errors.SAddress2}
                                InputProps={{
                                  endAdornment: touched.SAddress2 && !!errors.SAddress2 && <ErrorIcon color="error" />,
                                }}
                              />
                            </FormControl>

                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Pincode
                              </Typography>
                              <TextField
                                fullWidth
                                size="small"
                                onBlur={handleBlur}
                                disabled={submitting}
                                color="primary"
                                required
                                variant="outlined"
                                name="SAddress3"
                                value={SAddress3}
                                onChange={handleChange}
                                error={touched.SAddress3 && !!errors.SAddress3}
                                helperText={errors.SAddress3}
                                InputProps={{
                                  endAdornment: touched.SAddress3 && !!errors.SAddress3 && <ErrorIcon color="error" />,
                                }}
                              />
                            </FormControl>
                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Email
                              </Typography>
                              <TextField
                                fullWidth
                                size="small"
                                onBlur={handleBlur}
                                disabled={submitting}
                                color="primary"
                                required
                                variant="outlined"
                                name="SEmail"
                                value={SEmail}
                                onChange={handleChange}
                                error={touched.SEmail && !!errors.SEmail}
                                helperText={errors.SEmail}
                                InputProps={{
                                  endAdornment: touched.SEmail && !!errors.SEmail && <ErrorIcon color="error" />,
                                }}
                              />
                            </FormControl>
                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Candidate Aadhaar Number
                              </Typography>
                              <TextField
                                fullWidth
                                size="small"
                                onBlur={handleBlur}
                                disabled={submitting}
                                color="primary"
                                required
                                variant="outlined"
                                name="SAadharNo"
                                value={SAadharNo}
                                onChange={handleChange}
                                error={touched.SAadharNo && !!errors.SAadharNo}
                                helperText={errors.SAadharNo}
                                InputProps={{
                                  endAdornment: touched.SAadharNo && !!errors.SAadharNo && <ErrorIcon color="error" />,
                                }}
                              />
                            </FormControl>
                          </Box>
                        </Grid>
                      </Grid>
                    </>
                  )}
                  {index === 1 && (
                    <>
                      <Typography
                        ref={(el) => (sectionRefs.current.father = el)}
                        textAlign="start"
                        pt={10}
                        variant="h6"
                        fontSize={15}
                      >
                        2. About father of the student whose admission is sought
                      </Typography>
                      <Divider sx={{ border: 1 }} />
                      <Grid container direction="row" justifyContent="center">
                        <Grid item lg={5} xs={12}>
                          <Box
                            component="form"
                            noValidate
                            autoComplete="off"
                            display="flex"
                            flexDirection="column"
                            justifyContent="center"
                            alignItems="center"
                            gap={2}
                            sx={{ mt: 2 }}
                          >
                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Name
                              </Typography>
                              <TextField
                                fullWidth
                                size="small"
                                onBlur={handleBlur}
                                disabled={submitting}
                                color="primary"
                                required
                                variant="outlined"
                                name="FatherName"
                                value={FatherName}
                                onChange={handleChange}
                                error={touched.FatherName && !!errors.FatherName}
                                helperText={errors.FatherName}
                                InputProps={{
                                  endAdornment: touched.FatherName && !!errors.FatherName && (
                                    <ErrorIcon color="error" />
                                  ),
                                }}
                              />
                            </FormControl>

                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Mother Tongue
                              </Typography>
                              <TextField
                                fullWidth
                                size="small"
                                onBlur={handleBlur}
                                disabled={submitting}
                                color="primary"
                                required
                                variant="outlined"
                                name="FMotherTongue"
                                value={FMotherTongue}
                                onChange={handleChange}
                                error={touched.FMotherTongue && !!errors.FMotherTongue}
                                helperText={errors.FMotherTongue}
                                InputProps={{
                                  endAdornment: touched.FMotherTongue && !!errors.FMotherTongue && (
                                    <ErrorIcon color="error" />
                                  ),
                                }}
                              />
                            </FormControl>

                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Date of Birth (In figure)
                              </Typography>
                              <DatePickers
                                fullWidth={{ xs: '100%', lg: '570px' }}
                                name="FDob"
                                value={dayjs(fatherDOB, 'DD-MM-YYYY')} // Format the date before passing it
                                onChange={(e) => {
                                  const selectedDate = e ? dayjs(e) : ''; // Convert the selected date to dayjs object
                                  setFatherDOB(selectedDate); // Store the selected date in the state
                                }}
                                maxDate={maxDate}
                                variant={{ textField: { variant: 'outlined', fullWidth: true } }}
                              />
                            </FormControl>

                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Religion
                              </Typography>
                              <TextField
                                fullWidth
                                size="small"
                                onBlur={handleBlur}
                                disabled={submitting}
                                color="primary"
                                required
                                variant="outlined"
                                name="FReligion"
                                value={FReligion}
                                onChange={handleChange}
                                error={touched.FReligion && !!errors.FReligion}
                                helperText={errors.FReligion}
                                InputProps={{
                                  endAdornment: touched.FReligion && !!errors.FReligion && <ErrorIcon color="error" />,
                                }}
                              />
                            </FormControl>

                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Academic Qualification
                              </Typography>
                              <TextField
                                fullWidth
                                size="small"
                                onBlur={handleBlur}
                                disabled={submitting}
                                color="primary"
                                required
                                variant="outlined"
                                name="FQualification"
                                value={FQualification}
                                onChange={handleChange}
                                error={touched.FQualification && !!errors.FQualification}
                                helperText={errors.FQualification}
                                InputProps={{
                                  endAdornment: touched.FQualification && !!errors.FQualification && (
                                    <ErrorIcon color="error" />
                                  ),
                                }}
                              />
                            </FormControl>

                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Occupation & Designation
                              </Typography>
                              <TextField
                                fullWidth
                                size="small"
                                onBlur={handleBlur}
                                disabled={submitting}
                                color="primary"
                                required
                                variant="outlined"
                                name="FOccupation"
                                value={FOccupation}
                                onChange={handleChange}
                                error={touched.FOccupation && !!errors.FOccupation}
                                helperText={errors.FOccupation}
                                InputProps={{
                                  endAdornment: touched.FOccupation && !!errors.FOccupation && (
                                    <ErrorIcon color="error" />
                                  ),
                                }}
                              />
                            </FormControl>

                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Name of Present Company/Concern Working
                              </Typography>
                              <TextField
                                fullWidth
                                size="small"
                                onBlur={handleBlur}
                                disabled={submitting}
                                color="primary"
                                required
                                variant="outlined"
                                name="FNameOfCompany"
                                value={FNameOfCompany}
                                onChange={handleChange}
                                error={touched.FNameOfCompany && !!errors.FNameOfCompany}
                                helperText={errors.FNameOfCompany}
                                InputProps={{
                                  endAdornment: touched.FNameOfCompany && !!errors.FNameOfCompany && (
                                    <ErrorIcon color="error" />
                                  ),
                                }}
                              />
                            </FormControl>

                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                In Years
                              </Typography>
                              <TextField
                                fullWidth
                                size="small"
                                onBlur={handleBlur}
                                disabled={submitting}
                                color="primary"
                                required
                                variant="outlined"
                                name="FCompanyYear"
                                value={FCompanyYear}
                                onChange={handleChange}
                                error={touched.FCompanyYear && !!errors.FCompanyYear}
                                helperText={errors.FCompanyYear}
                                InputProps={{
                                  endAdornment: touched.FCompanyYear && !!errors.FCompanyYear && (
                                    <ErrorIcon color="error" />
                                  ),
                                }}
                              />
                            </FormControl>

                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                In Months
                              </Typography>
                              <TextField
                                fullWidth
                                size="small"
                                onBlur={handleBlur}
                                disabled={submitting}
                                color="primary"
                                required
                                variant="outlined"
                                name="FCompanyMonth"
                                value={FCompanyMonth}
                                onChange={handleChange}
                                error={touched.FCompanyMonth && !!errors.FCompanyMonth}
                                helperText={errors.FCompanyMonth}
                                InputProps={{
                                  endAdornment: touched.FCompanyMonth && !!errors.FCompanyMonth && (
                                    <ErrorIcon color="error" />
                                  ),
                                }}
                              />
                            </FormControl>

                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Full Office Address
                              </Typography>
                              <TextField
                                multiline
                                name="FOfficeAddress"
                                value={FOfficeAddress}
                                onChange={handleChange}
                                error={touched.FOfficeAddress && !!errors.FOfficeAddress}
                                helperText={errors.FOfficeAddress}
                                disabled={submitting}
                                fullWidth
                                placeholder="Enter address"
                                InputProps={{
                                  inputProps: {
                                    style: { resize: 'vertical', width: '100%', minHeight: '50px', maxHeight: '100px' },
                                  },
                                }}
                              />
                            </FormControl>

                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Mobile Number
                              </Typography>
                              <TextField
                                fullWidth
                                size="small"
                                onBlur={handleBlur}
                                disabled={submitting}
                                color="primary"
                                required
                                type="number"
                                variant="outlined"
                                name="FOfficeTelephone"
                                value={FOfficeTelephone}
                                onChange={handleChange}
                                error={touched.FOfficeTelephone && !!errors.FOfficeTelephone}
                                helperText={errors.FOfficeTelephone}
                                InputProps={{
                                  endAdornment: touched.FOfficeTelephone && !!errors.FOfficeTelephone && (
                                    <ErrorIcon color="error" />
                                  ),
                                }}
                              />
                            </FormControl>

                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Telephone Number
                              </Typography>
                              <TextField
                                fullWidth
                                size="small"
                                onBlur={handleBlur}
                                disabled={submitting}
                                color="primary"
                                required
                                type="number"
                                variant="outlined"
                                name="FOfficeMobile"
                                value={FOfficeMobile}
                                onChange={handleChange}
                                error={touched.FOfficeMobile && !!errors.FOfficeMobile}
                                helperText={errors.FOfficeMobile}
                                InputProps={{
                                  endAdornment: touched.FOfficeMobile && !!errors.FOfficeMobile && (
                                    <ErrorIcon color="error" />
                                  ),
                                }}
                              />
                            </FormControl>
                          </Box>
                        </Grid>
                      </Grid>
                    </>
                  )}
                  {index === 2 && (
                    <>
                      <Typography
                        ref={(el) => (sectionRefs.current.mother = el)}
                        textAlign="start"
                        pt={10}
                        variant="h6"
                        fontSize={15}
                      >
                        3. About mother of the student whose admission is sought
                      </Typography>
                      <Divider sx={{ border: 1 }} />
                      <Grid container direction="row" justifyContent="center">
                        <Grid item lg={5} xs={12}>
                          <Box
                            component="form"
                            noValidate
                            autoComplete="off"
                            display="flex"
                            flexDirection="column"
                            justifyContent="center"
                            alignItems="center"
                            gap={2}
                            sx={{ mt: 2 }}
                          >
                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Name
                              </Typography>
                              <TextField
                                fullWidth
                                size="small"
                                onBlur={handleBlur}
                                disabled={submitting}
                                color="primary"
                                required
                                variant="outlined"
                                name="MotherName"
                                value={MotherName}
                                onChange={handleChange}
                                error={touched.MotherName && !!errors.MotherName}
                                helperText={errors.MotherName}
                                InputProps={{
                                  endAdornment: touched.MotherName && !!errors.MotherName && (
                                    <ErrorIcon color="error" />
                                  ),
                                }}
                              />
                            </FormControl>

                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Mother Tongue
                              </Typography>
                              <TextField
                                fullWidth
                                size="small"
                                onBlur={handleBlur}
                                disabled={submitting}
                                color="primary"
                                required
                                variant="outlined"
                                name="MMotherTongue"
                                value={MMotherTongue}
                                onChange={handleChange}
                                error={touched.MMotherTongue && !!errors.MMotherTongue}
                                helperText={errors.MMotherTongue}
                                InputProps={{
                                  endAdornment: touched.MMotherTongue && !!errors.MMotherTongue && (
                                    <ErrorIcon color="error" />
                                  ),
                                }}
                              />
                            </FormControl>

                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Date of Birth (In figure)
                              </Typography>
                              <DatePickers
                                fullWidth={{ xs: '100%', lg: '570px' }}
                                name="MDob"
                                value={dayjs(motherDOB, 'DD-MM-YYYY')} // Format the date before passing it
                                onChange={(e) => {
                                  const selectedDate = e ? dayjs(e) : ''; // Convert the selected date to dayjs object
                                  setMotherDOB(selectedDate); // Store the selected date in the state
                                }}
                                maxDate={maxDate}
                                variant={{ textField: { variant: 'outlined', fullWidth: true } }}
                              />
                            </FormControl>

                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Religion
                              </Typography>
                              <TextField
                                fullWidth
                                size="small"
                                onBlur={handleBlur}
                                disabled={submitting}
                                color="primary"
                                required
                                variant="outlined"
                                name="MReligion"
                                value={MReligion}
                                onChange={handleChange}
                                error={touched.MReligion && !!errors.MReligion}
                                helperText={errors.MReligion}
                                InputProps={{
                                  endAdornment: touched.MReligion && !!errors.MReligion && <ErrorIcon color="error" />,
                                }}
                              />
                            </FormControl>

                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Academic Qualification
                              </Typography>
                              <TextField
                                fullWidth
                                size="small"
                                onBlur={handleBlur}
                                disabled={submitting}
                                color="primary"
                                required
                                variant="outlined"
                                name="MQualification"
                                value={MQualification}
                                onChange={handleChange}
                                error={touched.MQualification && !!errors.MQualification}
                                helperText={errors.MQualification}
                                InputProps={{
                                  endAdornment: touched.MQualification && !!errors.MQualification && (
                                    <ErrorIcon color="error" />
                                  ),
                                }}
                              />
                            </FormControl>

                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Occupation & Designation
                              </Typography>
                              <TextField
                                fullWidth
                                size="small"
                                onBlur={handleBlur}
                                disabled={submitting}
                                color="primary"
                                required
                                variant="outlined"
                                name="MOccupation"
                                value={MOccupation}
                                onChange={handleChange}
                                error={touched.MOccupation && !!errors.MOccupation}
                                helperText={errors.MOccupation}
                                InputProps={{
                                  endAdornment: touched.MOccupation && !!errors.MOccupation && (
                                    <ErrorIcon color="error" />
                                  ),
                                }}
                              />
                            </FormControl>

                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Name of Present Company/Concern Working
                              </Typography>
                              <TextField
                                fullWidth
                                size="small"
                                onBlur={handleBlur}
                                disabled={submitting}
                                color="primary"
                                required
                                variant="outlined"
                                name="MNameOfCompany"
                                value={MNameOfCompany}
                                onChange={handleChange}
                                error={touched.MNameOfCompany && !!errors.MNameOfCompany}
                                helperText={errors.MNameOfCompany}
                                InputProps={{
                                  endAdornment: touched.MNameOfCompany && !!errors.MNameOfCompany && (
                                    <ErrorIcon color="error" />
                                  ),
                                }}
                              />
                            </FormControl>

                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                In Years
                              </Typography>
                              <TextField
                                fullWidth
                                size="small"
                                onBlur={handleBlur}
                                disabled={submitting}
                                color="primary"
                                required
                                variant="outlined"
                                name="MCompanyYear"
                                value={MCompanyYear}
                                onChange={handleChange}
                                error={touched.MCompanyYear && !!errors.MCompanyYear}
                                helperText={errors.MCompanyYear}
                                InputProps={{
                                  endAdornment: touched.MCompanyYear && !!errors.MCompanyYear && (
                                    <ErrorIcon color="error" />
                                  ),
                                }}
                              />
                            </FormControl>

                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                In Months
                              </Typography>
                              <TextField
                                fullWidth
                                size="small"
                                onBlur={handleBlur}
                                disabled={submitting}
                                color="primary"
                                required
                                variant="outlined"
                                name="MCompanyMonth"
                                value={MCompanyMonth}
                                onChange={handleChange}
                                error={touched.MCompanyMonth && !!errors.MCompanyMonth}
                                helperText={errors.MCompanyMonth}
                                InputProps={{
                                  endAdornment: touched.MCompanyMonth && !!errors.MCompanyMonth && (
                                    <ErrorIcon color="error" />
                                  ),
                                }}
                              />
                            </FormControl>

                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Full Office Address
                              </Typography>
                              <TextField
                                multiline
                                name="MOfficeAddress"
                                value={MOfficeAddress}
                                onChange={handleChange}
                                error={touched.MOfficeAddress && !!errors.MOfficeAddress}
                                helperText={errors.MOfficeAddress}
                                disabled={submitting}
                                fullWidth
                                placeholder="Enter address"
                                InputProps={{
                                  inputProps: {
                                    style: { resize: 'vertical', width: '100%', minHeight: '50px', maxHeight: '100px' },
                                  },
                                }}
                              />
                            </FormControl>

                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Mobile Number
                              </Typography>
                              <TextField
                                fullWidth
                                size="small"
                                onBlur={handleBlur}
                                disabled={submitting}
                                color="primary"
                                required
                                type="number"
                                variant="outlined"
                                name="MOfficeTelephone"
                                value={MOfficeTelephone}
                                onChange={handleChange}
                                error={touched.MOfficeTelephone && !!errors.MOfficeTelephone}
                                helperText={errors.MOfficeTelephone}
                                InputProps={{
                                  endAdornment: touched.MOfficeTelephone && !!errors.MOfficeTelephone && (
                                    <ErrorIcon color="error" />
                                  ),
                                }}
                              />
                            </FormControl>

                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Telephone Number
                              </Typography>
                              <TextField
                                fullWidth
                                size="small"
                                onBlur={handleBlur}
                                disabled={submitting}
                                color="primary"
                                required
                                type="number"
                                variant="outlined"
                                name="MOfficeMobile"
                                value={MOfficeMobile}
                                onChange={handleChange}
                                error={touched.MOfficeMobile && !!errors.MOfficeMobile}
                                helperText={errors.MOfficeMobile}
                                InputProps={{
                                  endAdornment: touched.MOfficeMobile && !!errors.MOfficeMobile && (
                                    <ErrorIcon color="error" />
                                  ),
                                }}
                              />
                            </FormControl>
                          </Box>
                        </Grid>
                      </Grid>
                    </>
                  )}
                  {index === 3 && (
                    <>
                     
                      <Typography
                        ref={(el) => (sectionRefs.current.guardian = el)}
                        textAlign="start"
                        pt={10}
                        variant="h6"
                        fontSize={15}
                      >
                        4. About Guardian of the student whose admission is sought
                      </Typography>
                      <Divider sx={{ border: 1 }} />

                      <Stack direction="row"  mt={1} justifyContent="end">
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={autoFillGuardian} // This will be the state to manage checkbox status
                              onChange={handleAutoFillGuardian} // This will handle the auto-fill logic
                              name="autoFillGuardian"
                              color="primary"
                            />
                          }
                          label="Same as Father's Details"
                        />
                      </Stack>
                      <Grid container direction="row" justifyContent="center">
                        <Grid item lg={5} xs={12}>
                          <Box
                            component="form"
                            noValidate
                            autoComplete="off"
                            display="flex"
                            flexDirection="column"
                            justifyContent="center"
                            alignItems="center"
                            gap={2}
                            sx={{ mt: 2 }}
                          >
                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Name
                              </Typography>
                              <TextField
                                fullWidth
                                size="small"
                                onBlur={handleBlur}
                                disabled={submitting}
                                color="primary"
                                required
                                variant="outlined"
                                name="GuardianName"
                                value={GuardianName}
                                onChange={handleChange}
                                error={touched.GuardianName && !!errors.GuardianName}
                                helperText={errors.GuardianName}
                                InputProps={{
                                  endAdornment: touched.GuardianName && !!errors.GuardianName && (
                                    <ErrorIcon color="error" />
                                  ),
                                }}
                              />
                            </FormControl>

                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Mother Tongue
                              </Typography>
                              <TextField
                                fullWidth
                                size="small"
                                onBlur={handleBlur}
                                disabled={submitting}
                                color="primary"
                                required
                                variant="outlined"
                                name="GMotherTongue"
                                value={GMotherTongue}
                                onChange={handleChange}
                                error={touched.GMotherTongue && !!errors.GMotherTongue}
                                helperText={errors.GMotherTongue}
                                InputProps={{
                                  endAdornment: touched.GMotherTongue && !!errors.GMotherTongue && (
                                    <ErrorIcon color="error" />
                                  ),
                                }}
                              />
                            </FormControl>

                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Date of Birth (In figure)
                              </Typography>
                              <DatePickers
                                fullWidth={{ xs: '100%', lg: '570px' }}
                                name="GDob"
                                value={dayjs(guardianDOB, 'DD-MM-YYYY')} // Format the date before passing it
                                onChange={(e) => {
                                  const selectedDate = e ? dayjs(e) : ''; // Convert the selected date to dayjs object
                                  setGuardianDOB(selectedDate); // Store the selected date in the state
                                }}
                                maxDate={maxDate}
                                variant={{ textField: { variant: 'outlined', fullWidth: true } }}
                              />
                            </FormControl>

                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Religion
                              </Typography>
                              <TextField
                                fullWidth
                                size="small"
                                onBlur={handleBlur}
                                disabled={submitting}
                                color="primary"
                                required
                                variant="outlined"
                                name="GReligion"
                                value={GReligion}
                                onChange={handleChange}
                                error={touched.GReligion && !!errors.GReligion}
                                helperText={errors.GReligion}
                                InputProps={{
                                  endAdornment: touched.GReligion && !!errors.GReligion && <ErrorIcon color="error" />,
                                }}
                              />
                            </FormControl>

                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Academic Qualification
                              </Typography>
                              <TextField
                                fullWidth
                                size="small"
                                onBlur={handleBlur}
                                disabled={submitting}
                                color="primary"
                                required
                                variant="outlined"
                                name="GQualification"
                                value={GQualification}
                                onChange={handleChange}
                                error={touched.GQualification && !!errors.GQualification}
                                helperText={errors.GQualification}
                                InputProps={{
                                  endAdornment: touched.GQualification && !!errors.GQualification && (
                                    <ErrorIcon color="error" />
                                  ),
                                }}
                              />
                            </FormControl>

                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Occupation & Designation
                              </Typography>
                              <TextField
                                fullWidth
                                size="small"
                                onBlur={handleBlur}
                                disabled={submitting}
                                color="primary"
                                required
                                variant="outlined"
                                name="GOccupation"
                                value={GOccupation}
                                onChange={handleChange}
                                error={touched.GOccupation && !!errors.GOccupation}
                                helperText={errors.GOccupation}
                                InputProps={{
                                  endAdornment: touched.GOccupation && !!errors.GOccupation && (
                                    <ErrorIcon color="error" />
                                  ),
                                }}
                              />
                            </FormControl>

                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Name of Present Company/Concern Working
                              </Typography>
                              <TextField
                                fullWidth
                                size="small"
                                onBlur={handleBlur}
                                disabled={submitting}
                                color="primary"
                                required
                                variant="outlined"
                                name="GNameOfCompany"
                                value={GNameOfCompany}
                                onChange={handleChange}
                                error={touched.GNameOfCompany && !!errors.GNameOfCompany}
                                helperText={errors.GNameOfCompany}
                                InputProps={{
                                  endAdornment: touched.GNameOfCompany && !!errors.GNameOfCompany && (
                                    <ErrorIcon color="error" />
                                  ),
                                }}
                              />
                            </FormControl>

                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                In Years
                              </Typography>
                              <TextField
                                fullWidth
                                size="small"
                                onBlur={handleBlur}
                                disabled={submitting}
                                color="primary"
                                required
                                variant="outlined"
                                name="GCompanyYear"
                                value={GCompanyYear}
                                onChange={handleChange}
                                error={touched.GCompanyYear && !!errors.GCompanyYear}
                                helperText={errors.GCompanyYear}
                                InputProps={{
                                  endAdornment: touched.GCompanyYear && !!errors.GCompanyYear && (
                                    <ErrorIcon color="error" />
                                  ),
                                }}
                              />
                            </FormControl>

                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                In Months
                              </Typography>
                              <TextField
                                fullWidth
                                size="small"
                                onBlur={handleBlur}
                                disabled={submitting}
                                color="primary"
                                required
                                variant="outlined"
                                name="GCompanyMonth"
                                value={GCompanyMonth}
                                onChange={handleChange}
                                error={touched.GCompanyMonth && !!errors.GCompanyMonth}
                                helperText={errors.GCompanyMonth}
                                InputProps={{
                                  endAdornment: touched.GCompanyMonth && !!errors.GCompanyMonth && (
                                    <ErrorIcon color="error" />
                                  ),
                                }}
                              />
                            </FormControl>

                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Full Office Address
                              </Typography>
                              <TextField
                                multiline
                                name="GOfficeAddress"
                                value={GOfficeAddress}
                                onChange={handleChange}
                                error={touched.GOfficeAddress && !!errors.GOfficeAddress}
                                helperText={errors.GOfficeAddress}
                                disabled={submitting}
                                fullWidth
                                placeholder="Enter address"
                                InputProps={{
                                  inputProps: {
                                    style: { resize: 'vertical', width: '100%', minHeight: '50px', maxHeight: '100px' },
                                  },
                                }}
                              />
                            </FormControl>

                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Mobile Number
                              </Typography>
                              <TextField
                                fullWidth
                                size="small"
                                onBlur={handleBlur}
                                disabled={submitting}
                                color="primary"
                                required
                                type="number"
                                variant="outlined"
                                name="GOfficeTelephone"
                                value={GOfficeTelephone}
                                onChange={handleChange}
                                error={touched.GOfficeTelephone && !!errors.GOfficeTelephone}
                                helperText={errors.GOfficeTelephone}
                                InputProps={{
                                  endAdornment: touched.GOfficeTelephone && !!errors.GOfficeTelephone && (
                                    <ErrorIcon color="error" />
                                  ),
                                }}
                              />
                            </FormControl>

                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Telephone Number
                              </Typography>
                              <TextField
                                fullWidth
                                size="small"
                                onBlur={handleBlur}
                                disabled={submitting}
                                color="primary"
                                required
                                type="number"
                                variant="outlined"
                                name="GOfficeMobile"
                                value={GOfficeMobile}
                                onChange={handleChange}
                                error={touched.GOfficeMobile && !!errors.GOfficeMobile}
                                helperText={errors.GOfficeMobile}
                                InputProps={{
                                  endAdornment: touched.GOfficeMobile && !!errors.GOfficeMobile && (
                                    <ErrorIcon color="error" />
                                  ),
                                }}
                              />
                            </FormControl>
                          </Box>
                        </Grid>
                      </Grid>
                    </>
                  )}

                  {index === 4 && (
                    <>
                      {' '}
                      <Typography
                        ref={(el) => (sectionRefs.current.bro = el)}
                        textAlign="start"
                        pt={10}
                        variant="h6"
                        fontSize={15}
                      >
                        5. Brothers/Sisters studying in this school[attach photocopies of ID cards]
                      </Typography>
                      <Divider sx={{ border: 1 }} />
                      <Grid container direction="row" justifyContent="center">
                        <Grid item lg={5} xs={12}>
                          <Box
                            component="form"
                            noValidate
                            autoComplete="off"
                            display="flex"
                            flexDirection="column"
                            justifyContent="center"
                            alignItems="center"
                            gap={2}
                            sx={{ mt: 2 }}
                          >
                            <Grid container columnSpacing={10} rowSpacing={1} direction="row" justifyContent="center">
                              <Grid item lg={6} xs={12}>
                                <FormControl fullWidth>
                                  <Typography variant="h6" fontSize={13}>
                                    Name
                                  </Typography>
                                  <TextField
                                    fullWidth
                                    size="small"
                                    onBlur={handleBlur}
                                    disabled={submitting}
                                    color="primary"
                                    required
                                    variant="outlined"
                                    name="Sibling1"
                                    value={Sibling1}
                                    onChange={handleChange}
                                    error={touched.Sibling1 && !!errors.Sibling1}
                                    helperText={errors.Sibling1}
                                    InputProps={{
                                      endAdornment: touched.Sibling1 && !!errors.Sibling1 && (
                                        <ErrorIcon color="error" />
                                      ),
                                    }}
                                  />
                                </FormControl>
                              </Grid>
                              <Grid item lg={6} xs={12}>
                                <FormControl fullWidth>
                                  <Typography variant="h6" fontSize={13}>
                                    Standard
                                  </Typography>
                                  <TextField
                                    fullWidth
                                    size="small"
                                    onBlur={handleBlur}
                                    disabled={submitting}
                                    color="primary"
                                    required
                                    variant="outlined"
                                    name="Sibling1Std"
                                    value={Sibling1Std}
                                    onChange={handleChange}
                                    error={touched.Sibling1Std && !!errors.Sibling1Std}
                                    helperText={errors.Sibling1Std}
                                    InputProps={{
                                      endAdornment: touched.Sibling1Std && !!errors.Sibling1Std && (
                                        <ErrorIcon color="error" />
                                      ),
                                    }}
                                  />
                                </FormControl>
                              </Grid>
                              <Grid item md={6} xs={12}>
                                <FormControl fullWidth>
                                  <Typography variant="h6" fontSize={13}>
                                    Name
                                  </Typography>
                                  <TextField
                                    fullWidth
                                    size="small"
                                    onBlur={handleBlur}
                                    disabled={submitting}
                                    color="primary"
                                    required
                                    variant="outlined"
                                    name="Sibiling2"
                                    value={Sibiling2}
                                    onChange={handleChange}
                                    error={touched.Sibiling2 && !!errors.Sibiling2}
                                    helperText={errors.Sibiling2}
                                    InputProps={{
                                      endAdornment: touched.Sibiling2 && !!errors.Sibiling2 && (
                                        <ErrorIcon color="error" />
                                      ),
                                    }}
                                  />
                                </FormControl>
                              </Grid>
                              <Grid item md={6} xs={12}>
                                <FormControl fullWidth>
                                  <Typography variant="h6" fontSize={13}>
                                    Standard
                                  </Typography>
                                  <TextField
                                    fullWidth
                                    size="small"
                                    onBlur={handleBlur}
                                    disabled={submitting}
                                    color="primary"
                                    required
                                    variant="outlined"
                                    name="Sibling2Std"
                                    value={Sibling2Std}
                                    onChange={handleChange}
                                    error={touched.Sibling2Std && !!errors.Sibling2Std}
                                    helperText={errors.Sibling2Std}
                                    InputProps={{
                                      endAdornment: touched.Sibling2Std && !!errors.Sibling2Std && (
                                        <ErrorIcon color="error" />
                                      ),
                                    }}
                                  />
                                </FormControl>
                              </Grid>
                              <Grid item md={6} xs={12}>
                                <FormControl fullWidth>
                                  <Typography variant="h6" fontSize={13}>
                                    Name
                                  </Typography>
                                  <TextField
                                    fullWidth
                                    size="small"
                                    onBlur={handleBlur}
                                    disabled={submitting}
                                    color="primary"
                                    required
                                    variant="outlined"
                                    name="Sibling3"
                                    value={Sibling3}
                                    onChange={handleChange}
                                    error={touched.Sibling3 && !!errors.Sibling3}
                                    helperText={errors.Sibling3}
                                    InputProps={{
                                      endAdornment: touched.Sibling3 && !!errors.Sibling3 && (
                                        <ErrorIcon color="error" />
                                      ),
                                    }}
                                  />
                                </FormControl>
                              </Grid>
                              <Grid item md={6} xs={12}>
                                <FormControl fullWidth>
                                  <Typography variant="h6" fontSize={13}>
                                    Standard
                                  </Typography>
                                  <TextField
                                    fullWidth
                                    size="small"
                                    onBlur={handleBlur}
                                    disabled={submitting}
                                    color="primary"
                                    required
                                    variant="outlined"
                                    name="Sibling3Std"
                                    value={Sibling3Std}
                                    onChange={handleChange}
                                    error={touched.Sibling3Std && !!errors.Sibling3Std}
                                    helperText={errors.Sibling3Std}
                                    InputProps={{
                                      endAdornment: touched.Sibling3Std && !!errors.Sibling3Std && (
                                        <ErrorIcon color="error" />
                                      ),
                                    }}
                                  />
                                </FormControl>
                              </Grid>
                            </Grid>
                          </Box>
                        </Grid>
                      </Grid>
                      <Typography
                        textAlign="start"
                        ref={(el) => (sectionRefs.current.whether = el)}
                        pt={10}
                        variant="h6"
                        fontSize={15}
                      >
                        6. Whether your child avail school's transport
                      </Typography>
                      <Divider sx={{ border: 1 }} />
                      <Grid container direction="row" justifyContent="center">
                        <Grid item lg={5} xs={12}>
                          <Box
                            component="form"
                            noValidate
                            autoComplete="off"
                            display="flex"
                            flexDirection="column"
                            justifyContent="center"
                            alignItems="center"
                            gap={2}
                            sx={{ mt: 2 }}
                          >
                            <FormControl fullWidth>
                              <Box sx={{ display: 'flex', justifyContent: 'start', gap: 2 }}>
                                <Button
                                  sx={{ width: 100 }}
                                  variant={selectedTransport === 1 ? 'contained' : 'outlined'}
                                  color="secondary"
                                  onClick={() => handleTransportSelect(1)}
                                  startIcon={selectedTransport === 1 ? <CheckIcon /> : null}
                                >
                                  Yes
                                </Button>
                                <Button
                                  sx={{ width: 100 }}
                                  variant={selectedTransport === 0 ? 'contained' : 'outlined'}
                                  color="secondary"
                                  onClick={() => handleTransportSelect(0)}
                                  startIcon={selectedTransport === 0 ? <CheckIcon /> : null} // Conditionally show CheckIcon
                                >
                                  No
                                </Button>
                              </Box>
                            </FormControl>
                            {selectedTransport === 1 && (
                              <FormControl fullWidth>
                                <Typography variant="h6" fontSize={13}>
                                  Enter Stop Name
                                </Typography>
                                <TextField
                                  fullWidth
                                  size="small"
                                  onBlur={handleBlur}
                                  disabled={submitting}
                                  color="primary"
                                  required
                                  variant="outlined"
                                  name="StopName"
                                  value={StopName}
                                  onChange={handleChange}
                                  error={touched.StopName && !!errors.StopName}
                                  helperText={errors.StopName}
                                  InputProps={{
                                    endAdornment: touched.StopName && !!errors.StopName && <ErrorIcon color="error" />,
                                  }}
                                />
                              </FormControl>
                            )}
                          </Box>
                        </Grid>
                      </Grid>
                      <Typography
                        textAlign="start"
                        ref={(el) => (sectionRefs.current.whether = el)}
                        pt={10}
                        variant="h6"
                        fontSize={15}
                      >
                        7. Blood group of the child
                      </Typography>
                      <Divider sx={{ border: 1 }} />
                      <Grid container direction="row" justifyContent="center">
                        <Grid item lg={5} xs={12}>
                          <Box
                            component="form"
                            noValidate
                            autoComplete="off"
                            display="flex"
                            flexDirection="column"
                            justifyContent="center"
                            alignItems="center"
                            gap={2}
                            sx={{ mt: 2 }}
                          >
                            <FormControl fullWidth>
                              <TextField
                                fullWidth
                                size="small"
                                onBlur={handleBlur}
                                disabled={submitting}
                                color="primary"
                                required
                                variant="outlined"
                                name="StudentBloodGroup"
                                value={StudentBloodGroup}
                                onChange={handleChange}
                                error={touched.StudentBloodGroup && !!errors.StudentBloodGroup}
                                helperText={errors.StudentBloodGroup}
                                InputProps={{
                                  endAdornment: touched.StudentBloodGroup && !!errors.StudentBloodGroup && (
                                    <ErrorIcon color="error" />
                                  ),
                                }}
                              />
                            </FormControl>
                          </Box>
                        </Grid>
                      </Grid>
                      <Typography
                        ref={(el) => (sectionRefs.current.whether = el)}
                        textAlign="start"
                        pt={10}
                        variant="h6"
                        fontSize={15}
                      >
                        8. Last three vaccination taken
                      </Typography>
                      <Divider sx={{ border: 1 }} />
                      <Grid container direction="row" justifyContent="center">
                        <Grid item lg={5} xs={12}>
                          <Box
                            component="form"
                            noValidate
                            autoComplete="off"
                            display="flex"
                            flexDirection="column"
                            justifyContent="center"
                            alignItems="center"
                            gap={2}
                            sx={{ mt: 2 }}
                          >
                            <Grid container columnSpacing={10} rowSpacing={1} direction="row" justifyContent="center">
                              <Grid item lg={6} xs={12}>
                                <FormControl fullWidth>
                                  <Typography variant="h6" fontSize={13}>
                                    Name
                                  </Typography>
                                  <TextField
                                    fullWidth
                                    size="small"
                                    onBlur={handleBlur}
                                    disabled={submitting}
                                    color="primary"
                                    required
                                    variant="outlined"
                                    name="Vaccination1"
                                    value={Vaccination1}
                                    onChange={handleChange}
                                    error={touched.Vaccination1 && !!errors.Vaccination1}
                                    helperText={errors.Vaccination1}
                                    InputProps={{
                                      endAdornment: touched.Vaccination1 && !!errors.Vaccination1 && (
                                        <ErrorIcon color="error" />
                                      ),
                                    }}
                                  />
                                </FormControl>
                              </Grid>
                              <Grid item lg={6} xs={12}>
                                <FormControl fullWidth>
                                  <Typography variant="h6" fontSize={13}>
                                    Date
                                  </Typography>
                                  <DatePickers
                                    name="Vaccination1Date"
                                    value={dayjs(vaccinationDate1, 'DD-MM-YYYY')} // Format the date before passing it
                                    onChange={(e) => {
                                      const selectedDate = e ? dayjs(e) : ''; // Convert the selected date to dayjs object
                                      setVaccinationDate1(selectedDate); // Store the selected date in the state
                                    }}
                                    maxDate={maxDate}
                                    variant={{ textField: { variant: 'outlined', fullWidth: true } }}
                                  />
                                </FormControl>
                              </Grid>
                              <Grid item lg={6} xs={12}>
                                <FormControl fullWidth>
                                  <Typography variant="h6" fontSize={13}>
                                    Name
                                  </Typography>
                                  <TextField
                                    fullWidth
                                    size="small"
                                    onBlur={handleBlur}
                                    disabled={submitting}
                                    color="primary"
                                    required
                                    variant="outlined"
                                    name="Vaccination2"
                                    value={Vaccination2}
                                    onChange={handleChange}
                                    error={touched.Vaccination2 && !!errors.Vaccination2}
                                    helperText={errors.Vaccination2}
                                    InputProps={{
                                      endAdornment: touched.Vaccination2 && !!errors.Vaccination2 && (
                                        <ErrorIcon color="error" />
                                      ),
                                    }}
                                  />
                                </FormControl>
                              </Grid>
                              <Grid item lg={6} xs={12}>
                                <FormControl fullWidth>
                                  <Typography variant="h6" fontSize={13}>
                                    Date
                                  </Typography>
                                  <DatePickers
                                    name="Vaccination2Date"
                                    value={dayjs(vaccinationDate2, 'DD-MM-YYYY')} // Format the date before passing it
                                    onChange={(e) => {
                                      const selectedDate = e ? dayjs(e) : ''; // Convert the selected date to dayjs object
                                      setVaccinationDate2(selectedDate); // Store the selected date in the state
                                    }}
                                    maxDate={maxDate}
                                    variant={{ textField: { variant: 'outlined', fullWidth: true } }}
                                  />
                                </FormControl>
                              </Grid>
                              <Grid item lg={6} xs={12}>
                                <FormControl fullWidth>
                                  <Typography variant="h6" fontSize={13}>
                                    Name
                                  </Typography>
                                  <TextField
                                    fullWidth
                                    size="small"
                                    onBlur={handleBlur}
                                    disabled={submitting}
                                    color="primary"
                                    required
                                    variant="outlined"
                                    name="Vaccination3"
                                    value={Vaccination3}
                                    onChange={handleChange}
                                    error={touched.Vaccination3 && !!errors.Vaccination3}
                                    helperText={errors.Vaccination3}
                                    InputProps={{
                                      endAdornment: touched.Vaccination3 && !!errors.Vaccination3 && (
                                        <ErrorIcon color="error" />
                                      ),
                                    }}
                                  />
                                </FormControl>
                              </Grid>
                              <Grid item lg={6} xs={12}>
                                <FormControl fullWidth>
                                  <Typography variant="h6" fontSize={13}>
                                    Date
                                  </Typography>
                                  <DatePickers
                                    name="Vaccination3Date"
                                    value={dayjs(vaccinationDate3, 'DD-MM-YYYY')} // Format the date before passing it
                                    onChange={(e) => {
                                      const selectedDate = e ? dayjs(e) : ''; // Convert the selected date to dayjs object
                                      setVaccinationDate3(selectedDate); // Store the selected date in the state
                                    }}
                                    maxDate={maxDate}
                                    variant={{ textField: { variant: 'outlined', fullWidth: true } }}
                                  />
                                </FormControl>
                              </Grid>
                            </Grid>
                          </Box>
                          <Box my={5} sx={{ display: 'flex', gap: 2, justifyContent: 'center', textAlign: 'center' }}>
                            <Button type="submit" variant="outlined" sx={{ my: 3, width: 100 }}>
                              Submit
                            </Button>
                          </Box>
                        </Grid>
                      </Grid>
                    </>
                  )}
                </Box>
              ))}
            </form>
          </Stack>
        </Stack>
      </AdmissionFormRoot>
      {submitting && (
        <LoadingPopup
          // title="Message Creating"
          popupContent={<LoadingMessage icon={Loading} message="Enquiry Processing Please Wait..." />}
        />
      )}
    </Page>
  );
}

export default AdmissionForm;
