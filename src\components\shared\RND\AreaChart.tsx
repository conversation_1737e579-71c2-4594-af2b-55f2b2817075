import React, { useEffect, useState } from 'react';
import Chart from 'react-apexcharts';
import { Card, CardContent, MenuItem, Select, SelectChangeEvent, Stack, Typography, useTheme } from '@mui/material';
import useAuth from '@/hooks/useAuth';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useAppSelector } from '@/hooks/useAppSelector';
import { getClassData, getdashboardFeeChartData } from '@/config/storeSelectors';
import { fetchClassList, fetchDashboardFeeChart } from '@/store/Dashboard/dashboard.thunks';
import typography from '@/theme/typography';
import { ClassListInfo } from '@/types/AcademicManagement';

const AreaChartComponent = () => {
  const { user } = useAuth();
  const theme = useTheme();
  const adminId: number | undefined = user?.accountId;
  const academicId = 10;
  const dispatch = useAppDispatch();

  const dashboardFeeChartData = useAppSelector(getdashboardFeeChartData);
  const ClassData = useAppSelector(getClassData);

  const AllClassOption = {
    classId: -1,
    className: 'All Class',
    classDescription: 'string',
    classStatus: 1,
  };

  const classDataWithAllClass = [AllClassOption, ...ClassData];
  const [classOptions, setClassOptions] = useState(classDataWithAllClass[0]);
  const { classId, className } = classOptions || {};

  const handleChange = (event: SelectChangeEvent) => {
    const selectedClass = classDataWithAllClass.find((item) => item.className === event.target.value);
    if (selectedClass) {
      setClassOptions(selectedClass);
    }
  };

  useEffect(() => {
    dispatch(fetchClassList(adminId));
    dispatch(fetchDashboardFeeChart({ adminId, academicId, classId }));
  }, [dispatch, adminId, classId]);

  // **Filter dashboardFeeChartData by Selected Class**
  const selectedClassFeeData = dashboardFeeChartData.filter((item) => item.classId === classId);

  // **Find the Maximum Fee Collected in the Selected Class**
  const maxCollectedFee = dashboardFeeChartData.length
    ? Math.max(...dashboardFeeChartData.map((item) => parseFloat(item.totalFeeCollected)))
    : 0;

  // **Determine Chart Color (Red if Max Fee is below 600000)**
  const isBelowThreshold = maxCollectedFee < 600000;

  const barChartDataFeeStatistic = [
    {
      name: 'Current Year',
      data: dashboardFeeChartData.map((item) => parseFloat(item.totalFeeCollected)),
    },
    {
      name: 'Previous Year',
      data: [0, 200000, 0, 700000, 0, 0, 100000, 0, 0, 400000, 0, 0],
    },
  ];

  const chartOptions = {
    chart: {
      type: 'area',
      height: 350,
      zoom: { enabled: false },
    },
    dataLabels: { enabled: false },
    stroke: { curve: 'smooth' },
    xaxis: {
      categories: dashboardFeeChartData.map((item) => item.monthName.substring(0, 3)),
      axisBorder: { show: true },
      axisTicks: { show: true },
      crosshairs: {
        stroke: {
          dashArray: 5, // Makes x-axis line dashed
        },
      },
    },
    colors: [isBelowThreshold ? theme.palette.warning.main : theme.palette.chart.violet[0], theme.palette.primary.main], // Change color of the area chart
  };

  return (
    <Card sx={{ maxWidth: '100%', margin: 'auto', mt: 5, p: 2 }}>
      <CardContent>
        <Stack direction="row" justifyContent="space-between">
          <Typography variant="h6" fontSize={20} sx={{ fontFamily: typography.fontFamily }}>
            Fee Report Statistics
          </Typography>
          <Stack direction={{ xs: 'column', sm: 'row' }}>
            <Select
              sx={{
                backgroundColor: theme.palette.grey[100],
                color: theme.palette.common.black,
                height: 30,
              }}
              value={className}
              onChange={handleChange}
              displayEmpty
              labelId="demo-dialog-select-label"
              id="demo-dialog-select"
              inputProps={{ 'aria-label': 'Without label' }}
              MenuProps={{
                PaperProps: {
                  style: {
                    maxHeight: '250px', // Adjust the value to your desired height
                  },
                },
              }}
            >
              {classDataWithAllClass?.map((item: ClassListInfo) => (
                <MenuItem sx={{ fontSize: '13px' }} key={item.classId} value={item.className}>
                  {item.className}
                </MenuItem>
              ))}
            </Select>
          </Stack>
        </Stack>
        <Chart options={chartOptions} series={barChartDataFeeStatistic} type="area" height={350} />
      </CardContent>
    </Card>
  );
};

export default AreaChartComponent;
