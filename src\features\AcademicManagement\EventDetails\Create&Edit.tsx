import { STATUS_CT, YEAR_SELECT } from '@/config/Selection';
import { Button, Autocomplete, TextField, Typography, Box, Stack, IconButton } from '@mui/material';
import React, { useState } from 'react';
import { TbCloudUpload } from 'react-icons/tb';
import TextareaField from '@/components/shared/Selections/TextareaField';
import DeleteIcon from '@mui/icons-material/Delete';
import { CreateProps } from '@/types/Common';

export const Create = ({ open, onClose }: CreateProps) => {
  const [image, setImage] = useState(null);
  // const [fileName, setFileName] = useState();

  // const FileUpload = (event) => {
  //   const file = event.target.files[0];
  //   const formdata = new FormData();
  //   formdata.append('file', file);
  //   setImage();
  // };
  return (
    <Box mt={3}>
      <Stack
        sx={{
          height: 'calc(100vh - 150px)',
          overflow: 'auto',
          '&::-webkit-scrollbar': {
            width: 0,
          },
        }}
      >
        <Typography variant="h6" fontSize={14}>
          Academic Year
        </Typography>
        <Autocomplete
          // defaultValue={datas.year}
          fullWidth
          options={YEAR_SELECT}
          renderInput={(params) => <TextField {...params} placeholder="Select year" />}
        />
        <Typography mt={2} variant="h6" fontSize={14}>
          Event Type
        </Typography>
        <Autocomplete
          fullWidth
          // value={datas.type}
          options={STATUS_SELECT}
          renderInput={(params) => <TextField {...params} placeholder="Select event" />}
        />
        <Typography mt={2} variant="h6" fontSize={14}>
          Title
        </Typography>
        <TextField
          // value={datas.title}
          placeholder="Title name"
        />
        <Typography mt={2} variant="h6" fontSize={14}>
          Description
        </Typography>
        <TextareaField />
        {/* {datas && datas ? ( */}
        {/* <Box mt={3} position="relative">
            <IconButton sx={{ position: 'absolute', top: 6, right: 6 }} size="small">
              <ModeEditIcon sx={{ color: 'white' }} />
            </IconButton>
            <img
              src={datas.thumbnail}
              width={350}
              height={150}
              style={{ objectFit: 'cover', borderRadius: 6 }}
              alt=""
            />
          </Box> */}
        {/* ) : ( */}
        {image ? (
          <Box mt={3}>
            <Stack direction="row" justifyContent="end">
              <IconButton size="small">
                <DeleteIcon onClick={() => setImage(null)} sx={{ cursor: 'pointer' }} />
              </IconButton>
            </Stack>
            <img src={image} width={350} alt="" />
          </Box>
        ) : (
          <Box
            sx={{
              mt: 3,
              px: 3,
              height: 150,
              border: '2px dashed #e8e8e9',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <Stack>
              <IconButton
                sx={{
                  '&.MuiIconButton-root:hover': {
                    backgroundColor: 'transparent',
                  },
                }}
                color="primary"
                aria-label="upload picture"
                component="label"
                disableTouchRipple
              >
                <input
                  hidden
                  accept="image/*"
                  // onChange={({ target: { files } }) => {
                  //   setFileName(files[0].name);
                  //   if (files) {
                  //     setImage(URL.createObjectURL(files[0]));
                  //   }
                  // }}
                  type="file"
                />
                <Stack alignItems="center">
                  <TbCloudUpload fontSize={30} />
                  <Typography variant="h6" fontSize={12}>
                    Browse File
                  </Typography>
                  <Typography variant="h6" fontSize={13} color="secondary">
                    or drag and drop
                  </Typography>
                </Stack>
              </IconButton>
            </Stack>
          </Box>
        )}

        {/* )} */}
      </Stack>
      <Box sx={{ justifyContent: 'center', pt: 3, position: 'relative' }}>
        <Stack spacing={2} direction="row" sx={{}}>
          <Button onClick={onClose} fullWidth variant="contained" color="secondary">
            Cancel
          </Button>
          <Button
            onClick={open}
            // onKeyDown={toggleDrawer(false)}
            fullWidth
            variant="contained"
            color="primary"
          >
            Update
            {/* {datas ? 'Update' : 'Upload'} */}
          </Button>
        </Stack>
      </Box>
    </Box>
  );
};
