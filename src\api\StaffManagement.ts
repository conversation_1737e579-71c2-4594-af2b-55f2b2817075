import ApiUrls from '@/config/ApiUrls';
import { CreateResponse, CreateResponseMulti, DeleteResponse, UpdateResponse } from '@/types/Common';
import { privateApi } from '@/api/base/api';
import { APIResponse } from '@/api/base/types';
import {
  AddCTSAllocationMapInfo,
  CTSAllocationCreateUpdateInfo,
  CTSAllocationMappedInfo,
  CTSDetailsListPagedData,
  CTSDetailsListRequest,
  CTSFilterInfo,
  DeleteCTSAllocationRequest,
  StaffCreateRequest,
  StaffCreateRow,
  StaffListInfo,
  StaffListPagedData,
  StaffListRequest,
} from '@/types/StaffManagement';

async function GetStaffDataList(request: StaffListRequest): Promise<APIResponse<StaffListPagedData>> {
  const response = await privateApi.post<StaffListPagedData>(ApiUrls.GetStaffDataList, request);
  return response;
}

async function GetStaff(staffId: number | undefined): Promise<APIResponse<StaffListPagedData[]>> {
  if (staffId === undefined) {
    throw new Error('staffId is required');
  }

  const url = ApiUrls.GetStaff.replace('{staffId}', staffId.toString());
  const response = await privateApi.get<any>(url);
  return response;
}

async function AddNewStaff(request: StaffCreateRequest): Promise<APIResponse<CreateResponse>> {
  const response = await privateApi.post<CreateResponse>(ApiUrls.AddStaff, request);
  return response;
}

async function AddMultipleStaff(request: StaffCreateRow[]): Promise<APIResponse<CreateResponseMulti>> {
  const response = await privateApi.post<CreateResponseMulti>(ApiUrls.AddMultipleStaff, request);
  return response;
}

async function UpdateStaff(request: StaffListInfo): Promise<APIResponse<UpdateResponse>> {
  const response = await privateApi.post<UpdateResponse>(ApiUrls.UpdateStaff, request);
  return response;
}

async function DeleteStaff(staffId: number): Promise<APIResponse<DeleteResponse>> {
  const url = ApiUrls.DeleteStaff.replace('{staffId}', staffId.toString());
  const response = await privateApi.get<DeleteResponse>(url);
  return response;
}

async function StaffCodeExists(StaffCode: string): Promise<APIResponse<boolean>> {
  const url = `${ApiUrls.StaffCodeExists}?q=${StaffCode}`;
  const response = await privateApi.get<boolean>(url);
  return response;
}

async function GetCTSFilter(adminId: number | undefined): Promise<APIResponse<CTSFilterInfo>> {
  if (adminId === undefined) {
    throw new Error('adminId is required');
  }

  const url = ApiUrls.GetCTSFilter.replace('{adminId}', adminId.toString());
  const response = await privateApi.get<any>(url);
  return response;
}

async function GetCTSList(request: CTSDetailsListRequest): Promise<APIResponse<CTSDetailsListPagedData>> {
  const response = await privateApi.post<CTSDetailsListPagedData>(ApiUrls.GetCTSList, request);
  return response;
}

async function AddCTSAllocationSingle(request: CTSAllocationCreateUpdateInfo): Promise<APIResponse<CreateResponse>> {
  const response = await privateApi.post<CreateResponse>(ApiUrls.AddCTSAllocationSingle, request);
  return response;
}

async function GetCTSUpdateDetail(
  adminId: number | undefined,
  cteacherId: number | undefined
): Promise<APIResponse<CTSAllocationCreateUpdateInfo>> {
  if (adminId === undefined || cteacherId === undefined) {
    throw new Error('adminId and cteacherId is required');
  }

  const url = ApiUrls.GetCTSUpdateDetail.replace('{adminId}', adminId.toString()).replace(
    '{cteacherId}',
    cteacherId.toString()
  );
  const response = await privateApi.get<any>(url);
  return response;
}

async function UpdateCTSAllocationSingle(request: CTSAllocationCreateUpdateInfo): Promise<APIResponse<UpdateResponse>> {
  const response = await privateApi.post<UpdateResponse>(ApiUrls.UpdateCTSAllocationSingle, request);
  return response;
}

async function GetCTSMapping(
  adminId: number | undefined,
  academicId: number | undefined,
  classId: number | undefined
): Promise<APIResponse<CTSAllocationMappedInfo[]>> {
  if (adminId === undefined || academicId === undefined || classId === undefined) {
    throw new Error('adminId , academicId and classId is required');
  }

  const url = ApiUrls.GetCTSMapping.replace('{adminId}', adminId.toString())
    .replace('{academicId}', academicId.toString())
    .replace('{classId}', classId.toString());
  const response = await privateApi.get<any>(url);
  return response;
}

async function AddCTSAllocationMap(request: AddCTSAllocationMapInfo[]): Promise<APIResponse<CreateResponseMulti>> {
  const response = await privateApi.post<CreateResponseMulti>(ApiUrls.AddCTSAllocationMap, request);
  return response;
}

async function GetCTSTeacherWiseMapiing(
  adminId: number | undefined,
  academicId: number | undefined,
  staffId: number | undefined
): Promise<APIResponse<CTSAllocationMappedInfo[]>> {
  if (adminId === undefined || academicId === undefined || staffId === undefined) {
    throw new Error('adminId , academicId and staffId is required');
  }

  const url = ApiUrls.GetCTSTeacherWiseMapiing.replace('{adminId}', adminId.toString())
    .replace('{academicId}', academicId.toString())
    .replace('{staffId}', staffId.toString());
  const response = await privateApi.get<any>(url);
  return response;
}

async function DeleteCTS(request: DeleteCTSAllocationRequest[]): Promise<APIResponse<DeleteResponse>> {
  const response = await privateApi.post<DeleteResponse>(ApiUrls.DeleteCTS, request);
  return response;
}

const methods = {
  GetStaffDataList,
  AddNewStaff,
  AddMultipleStaff,
  UpdateStaff,
  DeleteStaff,
  StaffCodeExists,
  GetStaff,
  GetCTSFilter,
  GetCTSList,
  AddCTSAllocationSingle,
  UpdateCTSAllocationSingle,
  GetCTSUpdateDetail,
  GetCTSMapping,
  AddCTSAllocationMap,
  GetCTSTeacherWiseMapiing,DeleteCTS
};

export default methods;
