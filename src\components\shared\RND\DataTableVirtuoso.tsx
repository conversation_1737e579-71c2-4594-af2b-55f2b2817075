/* eslint-disable react/no-unstable-nested-components */
/* eslint-disable no-nested-ternary */
import * as React from 'react';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import NoData from '@/assets/no-datas.png';
import { TableVirtuoso, TableComponents } from 'react-virtuoso';
import { useTheme, TextField, Checkbox, Box, Skeleton, Stack, Typography, TablePagination } from '@mui/material';
import { FetchStatus } from '@/types/Common';
import SortableTableCell from '../TableComponents/SortableTableCell';

export type DataTableColumn<D> = {
  name: string;
  headerLabel?: string;
  renderHeader?: () => React.ReactNode;
  sortable?: boolean;
  dataKey?: string;
  renderCell?: (row: D, rowIndex?: number) => React.ReactNode;
  width?: string;
  align?: string;
  renderFooter?: () => React.ReactNode;
  footerLabel?: string;
};

export type DataTablePaginationProps = {
  rowsPerPageOptions?: number[];
  totalRecords: number;
  pageNumber: number;
  pageSize: number;
  onPageChange: (event: React.MouseEvent<HTMLButtonElement> | null, page: number) => void;
  onRowsPerPageChange?: React.ChangeEventHandler<HTMLTextAreaElement | HTMLInputElement>;
  showPagination?: boolean;
  showFirstButton?: boolean;
  showLastButton?: boolean;
};

type ArrayStateProps = {
  feeId: number;
  termId: number;
  value: string;
};

export type DataTableProps<D> = {
  columns: DataTableColumn<D>[];
  data: D[];
  allowPagination?: boolean;
  allowSorting?: boolean;
  sortColumn?: string;
  sortDirection?: 'asc' | 'desc';
  onSort?: (columnName: string) => void;
  headerClassName?: string;
  PaginationProps?: DataTablePaginationProps;
  fetchStatus?: FetchStatus;
  getRowKey: (item: D, index?: number) => React.Key | null | undefined;
  deletingRecords?: (string | number)[];
  onCheckboxClick?: any;
  ShowCheckBox?: boolean | undefined;
  setSelectedRows?: any;
  selectedRows?: any;
  RowSelected?: boolean;
  isCancell?: any;
  textAlign?: string;
  tableWidth?: number;
  tableStyles?: {} | undefined;
  tableCellStyles?: {} | undefined;
  respnseResults?: {} | undefined;
  tableRowSelect?: boolean;
  disabledCheckBox?: any[];
  isSubmitting?: boolean;
  footerColumn?: any;
  showHorizontalScroll?: boolean;
  tableCellSkeltonStyle?: {} | undefined;
  isDisabled?: any;
  disabledCheckBoxBoolean?: boolean;
  hoverDisable?: boolean;
};

const VirtuosoTableComponents = (hoverDisable: boolean | undefined): TableComponents<any> => ({
  Scroller: React.forwardRef<HTMLDivElement>((props, ref) => (
    <TableContainer
      sx={{
        '&::-webkit-scrollbar': {
          height: '15px',
        },
        '&::-webkit-scrollbar-thumb': {
          backgroundColor: (theme) => (theme.palette.mode === 'light' ? '' : theme.palette.common.white),
          borderRadius: '8px', // Optional: round the corners
        },
        '&::-webkit-scrollbar-track': {
          backgroundColor: (theme) =>
            theme.palette.mode === 'light' ? theme.palette.grey[50] : theme.palette.grey[700],
        },
      }}
      component={Paper}
      {...props}
      ref={ref}
    />
  )),
  Table: (props) => <Table {...props} />,
  TableHead: (props) => (
    <TableHead
      sx={{
        backgroundColor: (theme) =>
          theme.palette.mode === 'light' ? theme.palette.common.white : theme.palette.grey[800],
      }}
      {...props}
    />
  ),
  TableRow: ({ item, ...props }: any) => <TableRow hover={hoverDisable ?? true} {...props} />,
  TableBody: React.forwardRef<HTMLTableSectionElement>((props, ref) => (
    <TableBody sx={{ height: '100%' }} {...props} ref={ref} />
  )),
});

function DTVirtuoso<D>({
  columns,
  data,
  getRowKey,
  allowPagination = false,
  allowSorting = false,
  ShowCheckBox = false,
  sortColumn,
  sortDirection = 'asc',
  onSort,
  headerClassName,
  fetchStatus,
  PaginationProps,
  deletingRecords,
  setSelectedRows,
  selectedRows,
  RowSelected,
  textAlign,
  tableWidth,
  tableStyles,
  tableCellStyles,
  tableRowSelect,
  disabledCheckBox,
  disabledCheckBoxBoolean,
  isSubmitting,
  footerColumn,
  showHorizontalScroll,
  onCheckboxClick,
  tableCellSkeltonStyle,
  isDisabled,
  hoverDisable,
}: DataTableProps<D>) {
  const tableRef = React.useRef<any>(null);
  const theme = useTheme();
  // const [selected, setSelected] = useState<D[]>(selectedRows);
  const isSelected = (row: D) => selectedRows?.indexOf(row) !== -1;

  const handleRowClick = (row: D) => {
    // Check if the clicked row is disabled
    const rowIndex = data.findIndex((item) => item === row);
    const isRowDisabled = disabledCheckBox && disabledCheckBox[rowIndex];

    if (!isRowDisabled || disabledCheckBoxBoolean) {
      const selectedIndex = selectedRows?.indexOf(row);
      let newSelected: D[] = [];

      if (selectedIndex === -1) {
        newSelected = newSelected.concat(selectedRows, row);
      } else if (selectedIndex === 0) {
        newSelected = newSelected.concat(selectedRows.slice(1));
      } else if (selectedIndex === selectedRows.length - 1) {
        newSelected = newSelected.concat(selectedRows.slice(0, -1));
      } else if (selectedIndex > 0) {
        newSelected = newSelected.concat(selectedRows.slice(0, selectedIndex), selectedRows.slice(selectedIndex + 1));
      }

      setSelectedRows(newSelected);

      if (onCheckboxClick) {
        onCheckboxClick(newSelected);
      }
    }
  };
  const handleRowAllClick = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      // Filter out disabled rows before setting selected rows
      const enabledRows = data.filter((row, index) => !disabledCheckBox || !disabledCheckBox[index]);
      setSelectedRows(enabledRows);
      if (onCheckboxClick) {
        onCheckboxClick(enabledRows);
      }
    } else {
      setSelectedRows([]);
      if (onCheckboxClick) {
        onCheckboxClick([]);
      }
    }
  };
  localStorage.setItem('absenteesData', JSON.stringify(selectedRows));

  React.useEffect(() => {
    if (tableRef.current) {
      tableRef.current.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    }
  }, [PaginationProps?.pageNumber, PaginationProps?.pageSize]);

  const handleSort = React.useCallback(
    (col: string) => {
      onSort?.(col);
    },
    [onSort]
  );

  if (allowPagination && !PaginationProps) {
    throw new Error("PaginationProps must be passed when 'allowPagination' is set to true");
  }

  const fixedHeaderContent = () => (
    <TableRow>
      {ShowCheckBox && (
        <TableCell>
          <Checkbox
            color="primary"
            onChange={handleRowAllClick}
            indeterminate={selectedRows?.length > 0 && selectedRows.length < data.length}
            checked={selectedRows?.length > 0 && selectedRows.length === data.length}
          />
        </TableCell>
      )}
      {columns.map((column) => {
        if (allowSorting && column.sortable) {
          return (
            <SortableTableCell
              columnName={column.name}
              active={sortColumn === column.name}
              direction={sortDirection}
              onClick={handleSort}
              key={column.name}
            >
              {column.renderHeader ? column.renderHeader() : column.headerLabel}
            </SortableTableCell>
          );
        }
        return (
          <TableCell
            className={textAlign}
            key={column.name}
            // sx={{ backgroundColor: tableBgColor, tableStyles }}
            sx={{ ...tableCellStyles, whiteSpace: 'nowrap' }}
          >
            {column.renderHeader ? column.renderHeader() : column.headerLabel}
          </TableCell>
        );
      })}
    </TableRow>
  );

  let rowContent: (index: number, row: D) => React.ReactNode = () => null;

  if (fetchStatus === 'loading') {
    const rowCount = allowPagination ? PaginationProps?.pageSize : data.length;
    rowContent = (index) => (
      <>
        {ShowCheckBox && (
          <TableCell padding="checkbox">
            <Skeleton variant="text" />
          </TableCell>
        )}
        {columns.map((column) => (
          <TableCell key={`Row${index}_${column.name}`}>
            <Skeleton variant="text" />
          </TableCell>
        ))}
      </>
    );
  }

  if (fetchStatus === 'success') {
    rowContent = (index, row) => {
      // const { termFeeDetails } = row;
      // const { termFeeMapped } = termFeeDetails || {};
      const rowKey = getRowKey(row, index);
      let isDeleting = false;
      if (rowKey && deletingRecords && deletingRecords.length > 0) {
        isDeleting = deletingRecords.includes(rowKey);
      }
      const isRowSelected = isSelected(row);

      // const isDisable = termFeeMapped?.length === 0;
      // console.log('isDisable::::----', isDisable);

      return (
        <>
          {ShowCheckBox && (
            <TableCell sx={{ width: 90 }}>
              <Checkbox
                disabled={isSubmitting ? true : disabledCheckBox?.[index] || disabledCheckBoxBoolean}
                color="primary"
                checked={isRowSelected}
                onClick={() => handleRowClick(row)}
                inputProps={{ 'aria-labelledby': `checkbox-${row}` }}
                sx={{
                  opacity: isDeleting ? 0.5 : 1,
                  cursor: 'pointer',
                }}
              />
            </TableCell>
          )}
          {columns.map((column) => {
            let cellData = null;
            if (column.renderCell) {
              cellData = column.renderCell(row, index);
            } else if (column.dataKey === 'TextField') {
              cellData = <TextField disabled={!isRowSelected} placeholder={column.name} />;
            } else if (column.dataKey) {
              cellData = (row as Record<string, any>)[column.dataKey];
            }
            return (
              <TableCell
                sx={{
                  opacity: isDeleting ? 0.5 : 1,
                  cursor: 'pointer',
                  textAlign: column.align || 'start',
                }}
                onClick={
                  column.dataKey === 'TextField'
                    ? undefined
                    : column.dataKey === 'Button'
                    ? undefined
                    : () => tableRowSelect && handleRowClick(row)
                }
                className={textAlign}
                key={`Row_${rowKey}_${column.name}`}
                width={column.width ?? 90}
              >
                {cellData}
              </TableCell>
            );
          })}
        </>
      );
    };
  }

  return (
    <Paper className="card-table-container" style={{ height: 'calc(100vh - 270px)', width: '100%' }}>
      {data.length !== 0 ? (
        <TableVirtuoso
          data={data}
          // components={{
          //   ...VirtuosoTableComponents,
          //   TableRow: (props) => <TableRow hover={hoverDisable ?? true} {...props} />,
          // }}
          components={VirtuosoTableComponents(hoverDisable)}
          fixedHeaderContent={fixedHeaderContent}
          itemContent={(index, row) => rowContent(index, row)}
          fixedFooterContent={() => footerColumn}
        />
      ) : (
        <Box
          display="flex"
          alignItems="center"
          justifyContent="center"
          width="100%"
          // height={{ xs: 'calc(100vh - 400px)', sm: 'calc(100vh - 400px)', lg: 'calc(100vh - 350px)' }}
          height={{ xs: '100%', sm: '100%', lg: '100%' }}
        >
          <Stack direction="column" alignItems="center">
            <img src={NoData} width="150px" alt="" />
            <Typography variant="subtitle2" mt={2} color="GrayText">
              No data found!
            </Typography>
          </Stack>
        </Box>
      )}
      {!!PaginationProps && allowPagination && (
        <TablePagination
          rowsPerPageOptions={PaginationProps.rowsPerPageOptions}
          component="div"
          count={PaginationProps.totalRecords}
          rowsPerPage={PaginationProps.pageSize}
          page={PaginationProps.pageNumber}
          onPageChange={PaginationProps.onPageChange}
          onRowsPerPageChange={PaginationProps.onRowsPerPageChange}
          showFirstButton
          showLastButton
        />
      )}
    </Paper>
  );
}

export default DTVirtuoso;
