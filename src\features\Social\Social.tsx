/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  Collapse,
  FormControl,
  Checkbox,
  FormGroup,
  FormControlLabel,
  useTheme,
  Chip,
  Avatar,
  InputAdornment,
  Badge,
  MenuItem,
  Select,
  CardMedia,
  ButtonGroup,
} from '@mui/material';
import styled from 'styled-components';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import { MdAdd, MdDelete } from 'react-icons/md';
import CollectionsIcon from '@mui/icons-material/Collections';
import SearchIcon from '@mui/icons-material/Search';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import DeleteIcon from '@mui/icons-material/Delete';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import DateSelect from '@/components/shared/Selections/DateSelect';
import { STATUS_SELECT, YEAR_SELECT, YEAR_SELECT_OPTIONS } from '@/config/Selection';
import typography from '@/theme/typography';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import MenuEditDeleteView from '@/components/shared/Selections/MenuEditDeleteView';
import Popup from '@/components/shared/Popup/Popup';
import { BsChat } from 'react-icons/bs';
import { FaRegEye } from 'react-icons/fa6';
import like from '@/assets/socialIcons/like.json';
import ThumbUpAltIcon from '@mui/icons-material/ThumbUpAlt';
import VisibilityIcon from '@mui/icons-material/Visibility';
import ThumbUpOffAltIcon from '@mui/icons-material/ThumbUpOffAlt';
import MenuEditDelete from '@/components/shared/Selections/MenuEditDelete';
import CreatePost from './CreatePost';
import LikeViewComment from './LikeViewComment';

const SocialRoot = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    /* height: 100%; */
    height: calc(100vh - 115px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 10px);
      flex-grow: 1;

      .card-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        /* border: 1px solid #e8e8e9; */
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

export const data = [
  {
    slNo: 1,
    studentName: 'Alex Peter',
    guardian: 'Jack',
    admissionNo: '12345',
    gender: 'Male',
  },
  {
    slNo: 2,
    studentName: 'Alex Peter',
    guardian: 'Jack',
    admissionNo: '12345',
    gender: 'Male',
  },
  {
    slNo: 3,
    studentName: 'Alex Peter',
    guardian: 'Jack',
    admissionNo: '12345',
    gender: 'Male',
  },
  {
    slNo: 4,
    studentName: 'Alex Peter',
    guardian: 'Jack',
    admissionNo: '12345',
    gender: 'Male',
  },
  {
    slNo: 5,
    studentName: 'Alex Peter',
    guardian: 'Jack',
    admissionNo: '12345',
    gender: 'Male',
  },
];

export const MyPost = [
  {
    id: 1,
    img: 'https://images.unsplash.com/flagged/photo-1573740144655-bbb6e88fb18a?q=80&w=1935&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    id: 2,
    img: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    id: 3,
    img: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?q=80&w=1887&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    id: 4,
    img: 'https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?q=80&w=1887&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    id: 5,
    img: 'https://images.unsplash.com/photo-1506634572416-48cdfe530110?q=80&w=1885&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    id: 6,
    img: 'https://images.unsplash.com/photo-1463453091185-61582044d556?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    id: 7,
    img: 'https://images.unsplash.com/photo-1558203728-00f45181dd84?q=80&w=2074&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    id: 8,
    img: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?q=80&w=1887&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    id: 9,
    img: 'https://images.unsplash.com/photo-1558203728-00f45181dd84?q=80&w=2074&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    id: 10,
    img: 'https://images.unsplash.com/photo-1543807535-eceef0bc6599?q=80&w=1887&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    id: 11,
    img: 'https://images.unsplash.com/photo-1591980607210-8ea99bee96f0?q=80&w=2071&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    id: 11,
    img: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    id: 12,
    img: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?q=80&w=1887&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    id: 13,
    img: 'https://images.unsplash.com/photo-1506634572416-48cdfe530110?q=80&w=1885&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    id: 14,
    img: 'https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?q=80&w=1887&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    id: 15,
    img: 'https://images.unsplash.com/photo-1463453091185-61582044d556?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    id: 1,
    img: 'https://images.unsplash.com/flagged/photo-1573740144655-bbb6e88fb18a?q=80&w=1935&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    id: 2,
    img: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    id: 3,
    img: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?q=80&w=1887&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    id: 4,
    img: 'https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?q=80&w=1887&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    id: 5,
    img: 'https://images.unsplash.com/photo-1506634572416-48cdfe530110?q=80&w=1885&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    id: 6,
    img: 'https://images.unsplash.com/photo-1463453091185-61582044d556?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    id: 7,
    img: 'https://images.unsplash.com/photo-1558203728-00f45181dd84?q=80&w=2074&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    id: 8,
    img: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?q=80&w=1887&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    id: 9,
    img: 'https://images.unsplash.com/photo-1558203728-00f45181dd84?q=80&w=2074&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    id: 10,
    img: 'https://images.unsplash.com/photo-1543807535-eceef0bc6599?q=80&w=1887&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    id: 11,
    img: 'https://images.unsplash.com/photo-1591980607210-8ea99bee96f0?q=80&w=2071&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    id: 11,
    img: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    id: 12,
    img: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?q=80&w=1887&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    id: 13,
    img: 'https://images.unsplash.com/photo-1506634572416-48cdfe530110?q=80&w=1885&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    id: 14,
    img: 'https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?q=80&w=1887&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    id: 15,
    img: 'https://images.unsplash.com/photo-1463453091185-61582044d556?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
];

function Social() {
  const [postCreate, setPostCreate] = React.useState<boolean>(false);
  const [postReview, setPostReview] = React.useState<boolean>(false);
  const [social, setSocial] = React.useState('All Post');
  const [showFilter, setShowFilter] = useState(false);
  const [like, setLike] = useState(false);
  const [showSend, setShowSend] = useState(true);
  const theme = useTheme();

  const getRowKey = useCallback((row: any) => row.examId, []);
  const SocialColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'studentName',
        headerLabel: 'Student Name',
        renderCell: (row) => {
          return (
            <Stack direction="row" spacing={1} alignItems="center">
              <Avatar alt="Remy Sharp" src="/static/images/avatar/1.jpg" />
              <Typography variant="subtitle2">{row.studentName}</Typography>
            </Stack>
          );
        },
      },
      {
        name: 'guardian',
        headerLabel: 'Guardian',
        dataKey: 'guardian',
      },
      {
        name: 'admissionNo',
        headerLabel: 'Admission No',
        renderCell: (row) => {
          return (
            <Box display="flex" justifyContent="space-between" flex={1}>
              <Chip size="small" sx={{ borderRadius: 1 }} color="info" label={row.admissionNo} />
            </Box>
          );
        },
      },
      {
        name: 'gender',
        headerLabel: 'Gender',
        dataKey: 'gender',
      },
    ],
    []
  );

  return (
    <Page title="List">
      <SocialRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: 1 }}>
          <Stack direction="row" justifyContent="space-between" alignItems="center">
            <Typography variant="h6" fontSize={19}>
              Social
            </Typography>
            <Box sx={{ flexShrink: 0 }}>
              <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                <SearchIcon />
              </IconButton>
              <ButtonGroup
                size="small"
                color="secondary"
                variant="outlined"
                aria-label="Basic button group"
                sx={{ p: 1 }}
              >
                <Button
                  variant={social === 'All Post' ? 'contained' : 'outlined'}
                  color={social === 'All Post' ? 'primary' : 'secondary'}
                  onClick={() => setSocial('All Post')}
                >
                  All Post
                </Button>
                <Button
                  variant={social === 'Waiting Post' ? 'contained' : 'outlined'}
                  color={social === 'Waiting Post' ? 'primary' : 'secondary'}
                  onClick={() => setSocial('Waiting Post')}
                >
                  Waiting Post
                </Button>
              </ButtonGroup>
            </Box>
          </Stack>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid container spacing={3} alignItems="end">
                  <Grid item lg="auto" md={6} xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', lg: 250 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Year
                      </Typography>
                      <Select name="notificationStatus" value="">
                        {YEAR_SELECT_OPTIONS.map((opt) => (
                          <MenuItem key={opt.id} value={opt.id}>
                            {opt.year}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            {social === 'All Post' ? (
              <Box className="card-container">
                <Grid container>
                  <Grid item xxl={6} px={5}>
                    <Box my={2} mb={4} height="100%" sx={{ overflow: 'auto', height: 'calc(100vh - 170px)' }}>
                      <Card
                        sx={{
                          p: 2,
                          backgroundColor: theme.palette.grey[100],
                          boxShadow: 0,
                          border: `1px solid ${theme.palette.grey[200]}`,
                        }}
                      >
                        <Box display="flex" alignItems="center" gap={1}>
                          <Avatar src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?q=80&w=1780&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" />
                          <TextField
                            id=""
                            fullWidth
                            size="small"
                            label=""
                            placeholder="Write your comment..."
                            value=""
                            sx={{
                              backgroundColor: theme.palette.common.white,
                              borderRadius: 1,
                              //   borderColor: theme.palette.common.white,
                              //   border: 'none',
                            }}
                            InputProps={{
                              style: {
                                borderColor: theme.palette.common.white,
                              },
                              endAdornment: (
                                <InputAdornment position="end">
                                  <IconButton size="small" aria-label="" onClick={() => setPostCreate(true)}>
                                    <CollectionsIcon />
                                  </IconButton>
                                </InputAdornment>
                              ),
                            }}
                          />
                        </Box>
                      </Card>
                      <Box>
                        {[1, 2, 3, 4].map(() => (
                          <Card sx={{ p: 2, boxShadow: 0, my: 2, border: `1px solid ${theme.palette.grey[200]}` }}>
                            <Box display="flex" justifyContent="space-between">
                              <Stack direction="row" alignItems="center" gap={1}>
                                <Avatar
                                  src="https://images.unsplash.com/photo-1633332755192-727a05c4013d?q=80&w=1780&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                                  sx={{ width: '35px', height: '35px' }}
                                />
                                <Stack>
                                  <Typography variant="subtitle2" fontSize={12}>
                                    Alex Peter
                                  </Typography>
                                  <Typography variant="subtitle1" fontSize={10} color="secondary">
                                    VII-A
                                  </Typography>
                                </Stack>
                              </Stack>
                              <Stack>
                                <MenuEditDelete Edit={() => setPostCreate(true)} />
                              </Stack>
                            </Box>
                            <Typography variant="subtitle2" fontSize={11} my={1}>
                              Lorem, ipsum dolor sit amet consectetur adipisicing elit. Suscipit iste quidem tempora
                              architecto? Omnis quis esse reprehenderit repreh end erit enderit repreh...
                            </Typography>
                            <CardMedia
                              component="img"
                              //   height="300"
                              sx={{ borderRadius: 2, border: `1px solid ${theme.palette.grey[50]}` }}
                              image="https://images.unsplash.com/photo-1678220156913-7c225f31e5b0?q=80&w=2071&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                              alt="Paella dish"
                            />
                            <Box display="flex" gap={2} alignItems="center">
                              <Stack direction="row" alignItems="center" gap={0.5} mt={1}>
                                <IconButton
                                  aria-label="delete"
                                  size="small"
                                  color={like === false ? 'secondary' : 'info'}
                                  onClick={() => setLike((i) => !i)}
                                >
                                  {like === false ? <ThumbUpOffAltIcon /> : <ThumbUpAltIcon />}
                                </IconButton>
                                <Typography variant="subtitle1" fontSize={12}>
                                  1k Like
                                </Typography>
                              </Stack>
                              <Stack direction="row" alignItems="center" gap={0.5} mt={1}>
                                <IconButton onClick={() => setPostReview(true)} aria-label="delete" size="small">
                                  <VisibilityIcon />
                                </IconButton>
                                <Typography variant="subtitle1" fontSize={12}>
                                  1.4k View
                                </Typography>
                              </Stack>
                              <Stack direction="row" alignItems="center" gap={0.5} mt={1}>
                                <IconButton onClick={() => setPostReview(true)} aria-label="delete" size="small">
                                  <BsChat />
                                </IconButton>
                                <Typography variant="subtitle1" fontSize={12}>
                                  5k Comment
                                </Typography>
                              </Stack>
                            </Box>
                            <Box display="flex" alignItems="center" gap={1} my={1}>
                              <Avatar src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?q=80&w=1780&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" />
                              <TextField
                                id=""
                                fullWidth
                                size="small"
                                label=""
                                placeholder="Write your comment..."
                                value=""
                                sx={{
                                  backgroundColor: theme.palette.common.white,
                                  borderRadius: 1,
                                  //   borderColor: theme.palette.common.white,
                                  //   border: 'none',
                                }}
                              />
                            </Box>
                          </Card>
                        ))}
                      </Box>
                    </Box>
                  </Grid>
                  <Grid item xxl={6} sx={{ border: 1, p: 2, borderColor: theme.palette.grey[200] }}>
                    <Box>
                      <Typography variant="h6" fontSize={18}>
                        My Post
                      </Typography>
                      <Box display="flex" alignItems="center" justifyContent="space-between">
                        <Stack direction="row" alignItems="center" gap={1} mb={2}>
                          <Avatar
                            src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?q=80&w=1780&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                            sx={{ width: '35px', height: '35px' }}
                          />
                          <Stack>
                            <Typography variant="subtitle2" fontSize={12}>
                              Alex Peter
                            </Typography>
                            <Typography variant="subtitle1" fontSize={10} color="secondary">
                              VII-A
                            </Typography>
                          </Stack>
                        </Stack>
                        <Stack>
                          <Badge
                            color="success"
                            badgeContent=""
                            sx={{ '& .MuiBadge-badge': { height: 10, width: 10 } }}
                            variant="dot"
                          >
                            <Typography
                              variant="subtitle2"
                              fontSize={8}
                              bgcolor={theme.palette.success.lighter}
                              color={theme.palette.success.main}
                              px={1}
                              borderRadius={10}
                            >
                              Active Now
                            </Typography>
                          </Badge>
                        </Stack>
                      </Box>
                      <Divider />
                      {/*  */}
                      <Box sx={{ overflow: 'auto', height: 'calc(100vh - 250px)' }}>
                        <Grid container my={2}>
                          {MyPost.map((post) => (
                            <Grid key={post.id} item lg={3}>
                              <CardMedia
                                component="img"
                                height="130"
                                sx={{ p: 0.5, borderRadius: 2 }}
                                image={post.img}
                                alt="Paella dish"
                              />
                            </Grid>
                          ))}
                        </Grid>
                      </Box>
                      {/*  */}
                    </Box>
                  </Grid>
                </Grid>
              </Box>
            ) : (
              <Box className="card-container" my={2}>
                <Grid container spacing={2}>
                  {[1, 2, 3, 4].map((student, rowIndex) => (
                    <Grid item xl={3} lg={6} md={12} sm={6} xs={12}>
                      <Card
                        className="student_card"
                        sx={{ p: 2, boxShadow: 0, border: `1px solid ${theme.palette.grey[300]}` }}
                      >
                        <Box display="flex" justifyContent="space-between">
                          <Stack direction="row" alignItems="center" gap={1}>
                            <Avatar
                              src="https://images.unsplash.com/photo-1633332755192-727a05c4013d?q=80&w=1780&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                              sx={{ width: '35px', height: '35px' }}
                            />
                            <Stack>
                              <Typography variant="subtitle2" fontSize={12}>
                                Alex Peter
                              </Typography>
                              <Typography variant="subtitle1" fontSize={10} color="secondary">
                                VII-A
                              </Typography>
                            </Stack>
                          </Stack>
                          <Stack>
                            <MenuEditDelete Edit={() => setPostCreate(true)} />
                          </Stack>
                        </Box>

                        <Typography variant="subtitle2" fontSize={10}>
                          Lorem ipsum dolor sit amet consectetur adipisicing elit. Porro commodi ea optio hic ipsam
                          expedita.
                        </Typography>
                        <CardMedia
                          component="img"
                          //   height="300"
                          sx={{ borderRadius: 2, border: `1px solid ${theme.palette.grey[50]}` }}
                          image="https://images.unsplash.com/photo-1678220156913-7c225f31e5b0?q=80&w=2071&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                          alt="Paella dish"
                        />
                        <Box display="flex" gap={3} mt={1}>
                          <Button
                            fullWidth
                            color="error"
                            variant="contained"
                            sx={{ backgroundColor: theme.palette.error.lighter, color: theme.palette.error.main }}
                            size="small"
                          >
                            Rejected
                          </Button>
                          <Button
                            sx={{ backgroundColor: theme.palette.success.lighter, color: theme.palette.success.main }}
                            fullWidth
                            variant="contained"
                            color="success"
                            size="small"
                          >
                            Approve
                          </Button>
                        </Box>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </Box>
            )}
          </div>
        </Card>
      </SocialRoot>

      <Popup
        size="sm"
        state={postCreate}
        title="Create Post"
        onClose={() => setPostCreate(false)}
        popupContent={<CreatePost />}
      />
      <Popup size="xs" state={postReview} onClose={() => setPostReview(false)} popupContent={<LikeViewComment />} />
    </Page>
  );
}

export default Social;
