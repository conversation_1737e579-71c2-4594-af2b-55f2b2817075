import * as React from 'react';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Comment from './Comment';
import View from './View';
import Like from './Like';
import { useTheme } from '@mui/material';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function CustomTabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ pt: 6 }}>{children}</Box>}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`,
  };
}

export default function LikeViewComment() {
  const theme = useTheme();
  const [value, setValue] = React.useState(0);

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  return (
    <Box sx={{ width: '100%' }}>
      <Box
        sx={{
          borderBottom: 1,
          borderColor: 'divider',
          px: 5,
          position: 'absolute',
          top: '20px',
          width: '100%',
          zIndex: 1,
          backgroundColor: theme.palette.common.white,
        }}
      >
        <Tabs value={value} onChange={handleChange} aria-label="basic tabs example">
          <Tab sx={{ fontSize: 18 }} label="Like" {...a11yProps(0)} />
          <Tab sx={{ fontSize: 18 }} label="View" {...a11yProps(1)} />
          <Tab sx={{ fontSize: 18 }} label="Comment" {...a11yProps(2)} />
        </Tabs>
      </Box>
      <CustomTabPanel value={value} index={0}>
        <Like />
      </CustomTabPanel>
      <CustomTabPanel value={value} index={1}>
        <View />
      </CustomTabPanel>
      <CustomTabPanel value={value} index={2}>
        <Comment />
      </CustomTabPanel>
    </Box>
  );
}
