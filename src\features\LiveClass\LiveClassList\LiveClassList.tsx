/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  useTheme,
  IconButton,
  Collapse,
  Tooltip,
  FormControl,
  Chip,
} from '@mui/material';
import styled from 'styled-components';
import { CLASS_SELECT, STATUS_SELECT, SUBJECT_SELECT, YEAR_SELECT } from '@/config/Selection';
import SearchIcon from '@mui/icons-material/Search';
import Popup from '@/components/shared/Popup/Popup';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import { RiLiveLine } from 'react-icons/ri';
import { Student, StudentInfoData } from '@/config/StudentDetails';
import useSettings from '@/hooks/useSettings';
import { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import DateSelect from '@/components/shared/Selections/DateSelect';
import MenuEditDelete from '@/components/shared/Selections/MenuEditDelete';
import CreateLiveClass from './CreateLiveClass';

export const data = [
  {
    ClassTakenBy: 'Passdaily',
    class: 'VII-B',
    subject: 'Test Subject',
    attended: '0',
    total: '25',
    start: '12/Nov/2023 12:00PM',
    end: '',
    status: 'Active',
  },
  {
    ClassTakenBy: 'Passdaily',
    class: 'VI-C',
    academicYear: '2023-2024',
    attended: '0',
    total: '25',
    start: '12/Nov/2023 12:00PM',
    end: '12/Nov/2023 01:30PM',
    status: 'Ended',
  },
  {
    ClassTakenBy: 'Passdaily',
    class: 'XII-B',
    subject: 'Test Subject',
    attended: '0',
    total: '25',
    start: '12/Nov/2023 12:00PM',
    end: '',
    status: 'Active',
  },
  {
    ClassTakenBy: 'Passdaily',
    class: 'V-A',
    subject: 'Test Subject',
    attended: '0',
    total: '25',
    start: '12/Nov/2023 12:00PM',
    end: '12/Nov/2023 01:30PM',
    status: 'Ended',
  },
  {
    ClassTakenBy: 'Passdaily',
    class: 'II-B',
    subject: 'Test Subject',
    attended: '0',
    total: '25',
    start: '12/Nov/2023 12:00PM',
    end: '',
    status: 'Active',
  },
  {
    ClassTakenBy: 'Passdaily',
    class: 'XII-C',
    subject: 'Test Subject',
    attended: '0',
    total: '25',
    start: '12/Nov/2023 12:00PM',
    end: '12/Nov/2023 01:30PM',
    status: 'Ended',
  },
  {
    ClassTakenBy: 'Passdaily',
    class: 'I-B',
    subject: 'Test Subject',
    attended: '0',
    total: '25',
    start: '12/Nov/2023 12:00PM',
    end: '12/Nov/2023 01:30PM',
    status: 'Ended',
  },
  {
    ClassTakenBy: 'Passdaily',
    class: 'VII-A',
    subject: 'Test Subject',
    attended: '0',
    total: '25',
    start: '12/Nov/2023 12:00PM',
    end: '12/Nov/2023 01:30PM',
    status: 'Ended',
  },
  {
    ClassTakenBy: 'Passdaily',
    class: 'VI-C',
    subject: 'Test Subject',
    attended: '0',
    total: '25',
    start: '12/Nov/2023 12:00PM',
    end: '12/Nov/2023 01:30PM',
    status: 'Ended',
  },
  {
    ClassTakenBy: 'Passdaily',
    class: 'X-B',
    subject: 'Test Subject',
    attended: '0',
    total: '25',
    start: '12/Nov/2023 12:00PM',
    end: '12/Nov/2023 01:30PM',
    status: 'Ended',
  },
];

const LiveClassListRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  .student_card {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  }

  .Card {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 40px);
      flex-grow: 1;

      .card-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: auto;
        ::-webkit-scrollbar {
          width: 0px;
        }

        .MuiTableContainer-root {
          max-height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

function LiveClassList() {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const [changeView, setChangeView] = useState(false);
  const [showFilter, setShowFilter] = useState(false);
  const [Delete, setDelete] = useState(false);

  const handleToggle = () => {
    setChangeView((prevChangeView) => !prevChangeView);
  };

  const handleClickDelete = () => setDelete(true);
  const handleClickCloseDelete = () => setDelete(false);

  const [students, setStudents] = useState(StudentInfoData);
  const [open, setOpen] = useState(false);
  const [listPopup, setListPopup] = useState(false);
  const [showNewMultiple, setShowNewMultiple] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState(null);

  const handlePageChange = useCallback((event: unknown, newPage: number) => {
    // loadClassList({ ...currentClassListRequest, pageNumber: newPage + 1 });
  }, []);

  const handleChangeRowsPerPage = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const newPageSize = +event.target.value;
    // loadClassList({ ...currentClassListRequest, pageNumber: 1, pageSize: newPageSize });
  }, []);

  const pageProps = useMemo(
    () => ({
      rowsPerPageOptions: [10, 20, 30],
      pageNumber: 1,
      pageSize: 10,
      totalRecords: 100,
      onPageChange: handlePageChange,
      onRowsPerPageChange: handleChangeRowsPerPage,
    }),
    [handleChangeRowsPerPage, handlePageChange]
  );

  const handleOpen = (student: Student) => {
    setSelectedStudent(student);
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };
  const handleToggleNewMultiple = () => {
    setShowNewMultiple((prevState) => !prevState);
  };

  const handleUpdate = (id: number, updatedData: Student) => {
    const updatedStudents = students.map((student) => (student.id === id ? { ...student, ...updatedData } : student));
    setStudents(updatedStudents);
    setOpen(false);
  };

  const getRowKey = useCallback((row: any) => row.examId, []);

  const handleCreate = (newStudents) => {
    const updatedStudents = [...students, newStudents];
    setStudents(updatedStudents);
    setOpenNew(false);
  };

  const LiveClassListColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'class',
        dataKey: 'class',
        headerLabel: 'Class',
      },
      {
        name: 'subject',
        dataKey: 'subject',
        headerLabel: 'Subject',
      },
      {
        name: 'ClassTakenBy',
        dataKey: 'ClassTakenBy',
        headerLabel: 'Class Taken By',
      },
      {
        name: 'ClassTakenBy',
        headerLabel: 'Start Time',
        renderCell: (row) => {
          return `${row.start}`;
        },
      },
      {
        name: 'ClassTakenBy',
        headerLabel: 'End Time',
        renderCell: (row) => {
          return `${row.end}`;
        },
      },
      {
        name: 'attended',
        headerLabel: 'Attendance Status',
        renderCell: (row) => {
          return (
            <Chip
              size="small"
              label={`${row.attended}/${row.total}`}
              variant="outlined"
              sx={{
                border: '1px solid',
                mt: 0.5,
                color: `${row.status !== 'Active' ? theme.palette.info.main : theme.palette.primary.main}`,
              }}
            />
          );
        },
      },
    ],
    [theme]
  );

  //   const renderContent = () => {
  //     return students.map((student, rowIndex) => (
  //       <Card key={student.id}>
  //         {LiveClassListColumns.map((column) => (
  //           <Typography key={column.name}>{student[column.dataKey]}</Typography>
  //         ))}
  //       </Card>
  //     ));
  //   };

  // const content: ReactNode = data.map((item, rowIndex: number) => {
  //   let cellData: ReactNode = null;

  //   return (
  //     <Typography key={column.name} variant="subtitle1" mb={1} fontSize={13}>
  //       <b>{cellData}</b>
  //     </Typography>
  //   );
  // });

  // Content = content;

  return (
    <Page title="Schedule List">
      <LiveClassListRoot>
        <Card className="Card" elevation={1} sx={{ pb: { xs: 2, md: 3 } }}>
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
            sx={{ px: { xs: 3, md: 5 }, pt: { xs: 2, md: 3 } }}
          >
            <Typography variant="h6" fontSize={20} width="100%">
              Live Class Report
            </Typography>
            <Box sx={{ flexShrink: 0 }}>
              <Tooltip title="Search">
                <IconButton aria-label="" color="primary" sx={{ mr: 1 }} onClick={() => setShowFilter((x) => !x)}>
                  <SearchIcon />
                </IconButton>
              </Tooltip>
              <Button
                sx={{ borderRadius: '20px', borderWidth: 2 }}
                size="small"
                variant="outlined"
                onClick={() => setListPopup(true)}
              >
                <RiLiveLine size="20px" /> Go Live
              </Button>
            </Box>
          </Stack>
          <Stack sx={{ px: { xs: 3, md: 5 } }}>
            <Divider />
          </Stack>
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={2} sx={{ px: { xs: 3, md: 5 }, pb: { xs: 2, md: 5 } }}>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Autocomplete
                        options={YEAR_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select year" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Autocomplete
                        options={CLASS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select class" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Subject
                      </Typography>
                      <Autocomplete
                        options={SUBJECT_SELECT}
                        renderInput={(params) => <TextField {...params} fullWidth placeholder="Enter Subject" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Start Date
                      </Typography>
                      <DateSelect />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" md={6} xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', lg: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Status
                      </Typography>
                      <Autocomplete
                        options={STATUS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select" />}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item lg={1} xs={12}>
                    <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                      <Button variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                      {/* <Button variant="contained" color="primary" fullWidth>
                          Search
                        </Button> */}
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Box className="card-container">
              <Grid container spacing={2} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
                {data.map((student) => (
                  <Grid item xl={4} lg={6} md={12} sm={6} xs={12}>
                    <Card
                      className="student_card"
                      sx={{ p: 2, boxShadow: 0, border: `1px solid ${theme.palette.grey[300]}` }}
                    >
                      <Box display="flex" flexDirection="column">
                        <Box display="flex" alignItems="center" justifyContent="flex-end">
                          {/* <Chip
                            size="small"
                            label="Date: 12-Oct-2023"
                            sx={{
                              backgroundColor: isLight ? theme.palette.info.lighter : theme.palette.grey[700],
                            }}
                          /> */}

                          <Stack direction="row" alignItems="center" ml={1}>
                            <Chip
                              size="small"
                              label={student.status}
                              variant="outlined"
                              color={student.status === 'Ended' ? 'secondary' : 'success'}
                            />
                            <MenuEditDelete
                              Edit={() => {
                                return 0;
                              }}
                              Delete={handleClickDelete}
                            />
                          </Stack>
                        </Box>

                        {LiveClassListColumns.map((item, rowIndex) => (
                          <Stack direction="row" ml={1}>
                            <Grid container>
                              <Grid item lg={6}>
                                <Typography key={item.name} variant="subtitle1" fontSize={13} mr={2}>
                                  {item.headerLabel}
                                </Typography>
                              </Grid>
                              <Grid item lg={5} mb={0} mt="auto">
                                <Typography
                                  variant="h6"
                                  fontSize={13}
                                  color={item.dataKey === 'meetingLink' ? theme.palette.info.main : ''}
                                >
                                  :{' '}
                                  {item.dataKey
                                    ? (student as { [key: string]: any })[item.dataKey ?? '']
                                    : item && item.renderCell && item.renderCell(student, rowIndex)}
                                </Typography>
                              </Grid>
                            </Grid>
                          </Stack>
                        ))}
                      </Box>

                      <Stack direction="row" my={3} width="100%" justifyContent="space-around">
                        <Button color="info" variant="contained" size="small">
                          Present List
                        </Button>
                        <Button color="secondary" variant="contained" size="small">
                          Absent List
                        </Button>
                        {student.status === 'Active' && (
                          <Button color="error" variant="contained" size="small">
                            End Live
                          </Button>
                        )}
                      </Stack>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Box>
          </div>
        </Card>
      </LiveClassListRoot>
      <Popup
        size="xs"
        state={Delete}
        onClose={handleClickCloseDelete}
        popupContent={<DeleteMessage message="Are you sure want to delete?" />}
      />
      <Popup
        size="md"
        title="Schedule New Live Class"
        state={listPopup}
        onClose={() => setListPopup(false)}
        popupContent={<CreateLiveClass />}
      />
    </Page>
  );
}

export default LiveClassList;
