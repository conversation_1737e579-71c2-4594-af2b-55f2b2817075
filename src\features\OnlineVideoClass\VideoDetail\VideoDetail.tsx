/* eslint-disable jsx-a11y/alt-text */
import React, { useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  useTheme,
  IconButton,
  Collapse,
  Tooltip,
  FormControl,
  Chip,
} from '@mui/material';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import styled from 'styled-components';
import { CLASS_SELECT, YEAR_SELECT } from '@/config/Selection';
import { MdAdd } from 'react-icons/md';
import SearchIcon from '@mui/icons-material/Search';
import AutorenewOutlinedIcon from '@mui/icons-material/AutorenewOutlined';
import useSettings from '@/hooks/useSettings';
import { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import Videoplayer from '@/components/Dashboard/Videoplayer';
import MenuEditDelete from '@/components/shared/Selections/MenuEditDelete';
import Popup from '@/components/shared/Popup/Popup';
import CreateVideo from './CreateVideo';
import MapOGFile from './MapOGFile';
import MenuEditDeleteView from '@/components/shared/Selections/MenuEditDeleteView';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import ViewOnlineExam from '@/features/OnlineExam/OnlineExamList/ViewOnlineExam';
import ViewList from './ViewList';

export const data = [
  {
    class: 'VII-B',
    academicYear: '2023-2024',
    subject: 'English',
    chapter: '03',
    status: 'Published',
    title: 'The Framers',
    description: 'Farming Industry...',
    date: '10-12-2023',
    created: 'Passdaily',
    video: 'https://youtu.be/gHESEeS1kGk',
    thumbnail:
      'https://images.unsplash.com/photo-1635192592106-77a5aacbe1a3?q=80&w=1969&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    class: 'VII-B',
    academicYear: '2023-2024',
    subject: 'English',
    chapter: '03',
    status: 'Published',
    title: 'The Framers',
    description: 'Farming Industry...',
    date: '10-12-2023',
    created: 'Passdaily',
    video: 'https://youtu.be/havBDwFgW24',
    thumbnail:
      'https://images.unsplash.com/photo-1592843997881-cab3860b1067?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    class: 'VII-B',
    academicYear: '2023-2024',
    subject: 'English',
    chapter: '03',
    status: 'Published',
    title: 'The Framers',
    description: 'Farming Industry...',
    date: '10-12-2023',
    created: 'Passdaily',
    video: 'https://youtu.be/DlHC7I9dBNU',
    thumbnail:
      'https://images.unsplash.com/photo-1482517967863-00e15c9b44be?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    class: 'VII-B',
    academicYear: '2023-2024',
    subject: 'English',
    chapter: '03',
    status: 'Published',
    title: 'The Framers',
    description: 'Farming Industry...',
    date: '10-12-2023',
    created: 'Passdaily',
    video: 'https://youtu.be/gHESEeS1kGk',
    thumbnail:
      'https://images.unsplash.com/photo-1635192592106-77a5aacbe1a3?q=80&w=1969&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    class: 'VII-B',
    academicYear: '2023-2024',
    subject: 'English',
    chapter: '03',
    status: 'Published',
    title: 'The Framers',
    description: 'Farming Industry...',
    date: '10-12-2023',
    created: 'Passdaily',
    video: 'https://youtu.be/havBDwFgW24',
    thumbnail:
      'https://images.unsplash.com/photo-1592843997881-cab3860b1067?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    class: 'VII-B',
    academicYear: '2023-2024',
    subject: 'English',
    chapter: '03',
    status: 'Published',
    title: 'The Framers',
    description: 'Farming Industry...',
    date: '10-12-2023',
    created: 'Passdaily',
    video: 'https://youtu.be/DlHC7I9dBNU',
    thumbnail:
      'https://images.unsplash.com/photo-1482517967863-00e15c9b44be?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    class: 'VII-B',
    academicYear: '2023-2024',
    subject: 'English',
    chapter: '03',
    status: 'Published',
    title: 'The Framers',
    description: 'Farming Industry...',
    date: '10-12-2023',
    created: 'Passdaily',
    video: 'https://youtu.be/gHESEeS1kGk',
    thumbnail:
      'https://images.unsplash.com/photo-1635192592106-77a5aacbe1a3?q=80&w=1969&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    class: 'VII-B',
    academicYear: '2023-2024',
    subject: 'English',
    chapter: '03',
    status: 'Published',
    title: 'The Framers',
    description: 'Farming Industry...',
    date: '10-12-2023',
    created: 'Passdaily',
    video: 'https://youtu.be/havBDwFgW24',
    thumbnail:
      'https://images.unsplash.com/photo-1592843997881-cab3860b1067?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    class: 'VII-B',
    academicYear: '2023-2024',
    subject: 'English',
    chapter: '03',
    status: 'Published',
    title: 'The Framers',
    description: 'Farming Industry...',
    date: '10-12-2023',
    created: 'Passdaily',
    video: 'https://youtu.be/DlHC7I9dBNU',
    thumbnail:
      'https://images.unsplash.com/photo-1482517967863-00e15c9b44be?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
];

const VideoDetailRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;
  .Card {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 40px);
      flex-grow: 1;

      .card-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: auto;
        ::-webkit-scrollbar {
          width: 0px;
        }
      }
    }
  }
`;

function VideoDetail() {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const [showFilter, setShowFilter] = useState(false);
  const [createPopup, setCreatePopup] = useState(false);
  const [mapOGFilePopup, setMapOGFilePopup] = useState(false);
  const [updatedData, setData] = useState(data);
  const [view, setView] = useState(false);
  const [Delete, setDelete] = useState(false);

  const handleClickView = () => setView(true);
  const handleClickCloseView = () => setView(false);
  const handleClickDelete = () => setDelete(true);
  const handleClickCloseDelete = () => setDelete(false);

  const handleStatusChange = (index) => {
    const copyData = [...updatedData];
    copyData[index].status = copyData[index].status === 'Published' ? 'Unpublished' : 'Published';
    setData(copyData);
  };

  const VideoDetailColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'studentName',
        dataKey: 'studentName',
        headerLabel: 'Student Name',
      },

      {
        name: 'class',
        dataKey: 'class',
        headerLabel: 'Class',
      },
      {
        name: 'admission',
        dataKey: 'admission',
        headerLabel: 'Admission',
      },
      {
        name: 'academicYear',
        dataKey: 'academicYear',
        headerLabel: 'Academic',
      },
    ],
    []
  );

  return (
    <Page title="Student Device Details">
      <VideoDetailRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17} width="100%">
              Video Detail
            </Typography>
            <Box pb={1} sx={{ flexShrink: 0 }}>
              <Tooltip title="Search">
                <IconButton aria-label="" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                  <SearchIcon />
                </IconButton>
              </Tooltip>
              <Button
                sx={{ borderRadius: '20px', mr: 2 }}
                size="small"
                variant="outlined"
                onClick={() => setMapOGFilePopup(true)}
              >
                Map OG File
              </Button>
              <Button
                sx={{ borderRadius: '20px', mr: 2 }}
                size="small"
                variant="outlined"
                onClick={() => setMapOGFilePopup(true)}
              >
                Map Video
              </Button>
              <Button
                sx={{ borderRadius: '20px' }}
                size="small"
                variant="outlined"
                onClick={() => setCreatePopup(true)}
              >
                <MdAdd size="20px" /> Create
              </Button>
            </Box>
          </Stack>
          <Stack>
            <Divider />
          </Stack>
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={5} pt={1} container spacing={2}>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 200 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Autocomplete
                        options={YEAR_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select year" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 200 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Autocomplete
                        options={CLASS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select class" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 200 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Subject
                      </Typography>
                      <Autocomplete
                        options={CLASS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select subject" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 200 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Chapter
                      </Typography>
                      <Autocomplete
                        options={CLASS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select chapter" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 200 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Title
                      </Typography>
                      <Autocomplete
                        options={CLASS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select class" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg={1} xs={12}>
                    <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                      <Button variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                      {/* <Button variant="contained" color="primary" fullWidth>
                          Search
                        </Button> */}
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Box className="card-container" mt={2}>
              <Grid container spacing={2}>
                {data.map((student, cardIndex) => (
                  <Grid item xl={12} lg={12} md={12} sm={12} xs={12}>
                    <Card sx={{ p: 2, m: 1, bgcolor: isLight ? theme.palette.common.white : theme.palette.grey[900] }}>
                      <Grid container spacing={2}>
                        <Grid item lg={2} md={6} xs={6}>
                          <Card sx={{ height: '120px', boxShadow: '0' }}>
                            <Videoplayer url={student.video} thumbnail={student.thumbnail} />
                          </Card>
                        </Grid>
                        <Grid item lg={10} md={6} xs={12}>
                          <Stack direction="row" alignItems="center" justifyContent="space-between">
                            <Chip
                              icon={<AutorenewOutlinedIcon sx={{ transform: 'rotate(180deg)' }} />}
                              size="small"
                              label={student.status === 'Published' ? 'Click to Unpublish' : 'Click to Publish'}
                              variant="outlined"
                              color="primary"
                              clickable
                              onClick={() => handleStatusChange(cardIndex)}
                            />
                            <MenuEditDeleteView
                              Edit={() => setCreatePopup(true)}
                              Delete={handleClickDelete}
                              View={handleClickView}
                            />
                          </Stack>
                          <Grid container mt={0.5} spacing={1.5}>
                            <Grid item lg={1.5} xs={6}>
                              <Typography variant="subtitle1" color="GrayText" fontSize={13} mt={1}>
                                Year
                              </Typography>
                              <Typography variant="subtitle1" color="GrayText" fontSize={13} mt={1}>
                                Class
                              </Typography>
                              <Typography variant="subtitle1" color="GrayText" fontSize={13} mt={1}>
                                Subject
                              </Typography>
                            </Grid>
                            <Grid item lg={2.5} xs={6} borderRight={2} borderColor={theme.palette.grey[300]}>
                              <Typography variant="h6" fontSize={13} mt={1.5}>
                                : {student.academicYear}
                              </Typography>
                              <Typography variant="h6" fontSize={13} mt={1}>
                                : {student.class}
                              </Typography>
                              <Typography variant="h6" fontSize={13} mt={1}>
                                : {student.subject}
                              </Typography>
                            </Grid>
                            <Grid item lg={1.5} xs={6}>
                              <Typography variant="subtitle1" color="GrayText" fontSize={13} mt={1}>
                                Chapter
                              </Typography>
                              <Typography variant="subtitle1" color="GrayText" fontSize={13} mt={1}>
                                Status
                              </Typography>
                              <Typography variant="subtitle1" color="GrayText" fontSize={13} mt={1}>
                                Title
                              </Typography>
                            </Grid>
                            <Grid item lg={2.5} xs={6} borderRight={2} borderColor={theme.palette.grey[300]}>
                              <Typography variant="h6" fontSize={13} mt={1}>
                                : {student.chapter}
                              </Typography>
                              <Typography variant="h6" fontSize={13} mt={1}>
                                :
                                <Chip
                                  sx={{ ml: 0.5, border: '0px' }}
                                  size="small"
                                  label={student.status === 'Published' ? ' Published' : 'Unpublished'}
                                  variant="outlined"
                                  color={student.status === 'Published' ? 'success' : 'error'}
                                />
                              </Typography>
                              <Typography variant="h6" fontSize={13} mt={1}>
                                :
                                <Chip
                                  sx={{
                                    ml: 0.5,
                                    border: '0px',
                                    bgcolor: theme.palette.info.lighter,
                                    color: theme.palette.info.dark,
                                  }}
                                  size="small"
                                  label={student.title}
                                  variant="outlined"
                                />
                              </Typography>
                            </Grid>
                            <Grid item lg={1.5} xs={6}>
                              <Typography variant="subtitle1" color="GrayText" fontSize={13} mt={1}>
                                Description
                              </Typography>
                              <Typography variant="subtitle1" color="GrayText" fontSize={13} mt={1}>
                                Date
                              </Typography>
                              <Typography variant="subtitle1" color="GrayText" fontSize={13} mt={1}>
                                Created
                              </Typography>
                            </Grid>
                            <Grid item lg={2.5} xs={6}>
                              <Typography variant="h6" fontSize={13} mt={1}>
                                : {student.description}
                              </Typography>
                              <Typography variant="h6" fontSize={13} mt={1}>
                                :{' '}
                                <span>
                                  <CalendarTodayIcon fontSize="small" color="secondary" sx={{ marginRight: '5px' }} />
                                </span>
                                <span>{student.date}</span>
                              </Typography>
                              <Typography variant="h6" fontSize={13} mt={1}>
                                : {student.created}
                              </Typography>
                            </Grid>
                          </Grid>
                        </Grid>

                        {/* <Grid item lg={3.1} xs={6} mt={4.5} borderRight={1} borderColor={theme.palette.grey[300]}>
                          {VideoDetailColumns.map((item) => (
                            <Grid container mt={2}>
                              <Grid item lg={6.1} xs={6}>
                                <Typography key={item.name} variant="subtitle1" color="GrayText" fontSize={13} mr={2}>
                                  {item.headerLabel}
                                </Typography>
                              </Grid>
                              <Grid item lg={5} xs={6} mb={0} mt="auto">
                                <Typography
                                  variant="h6"
                                  fontSize={13}
                                  color={item.dataKey === 'meetingLink' ? theme.palette.info.main : ''}
                                >
                                  : {(student as { [key: string]: string })[item.dataKey ?? '']}
                                </Typography>
                              </Grid>
                            </Grid>
                          ))}
                        </Grid>
                        <Grid item lg={3.1} xs={6} mt={4.5} borderRight={1} borderColor={theme.palette.grey[300]}>
                          {VideoDetailColumns.map((item) => (
                            <Grid container mt={2}>
                              <Grid item lg={6.1} xs={6}>
                                <Typography key={item.name} variant="subtitle1" color="GrayText" fontSize={13} mr={2}>
                                  {item.headerLabel}
                                </Typography>
                              </Grid>
                              <Grid item lg={5} xs={6} mb={0} mt="auto">
                                <Typography
                                  variant="h6"
                                  fontSize={13}
                                  color={item.dataKey === 'meetingLink' ? theme.palette.info.main : ''}
                                >
                                  : {(student as { [key: string]: string })[item.dataKey ?? '']}
                                </Typography>
                              </Grid>
                            </Grid>
                          ))}
                        </Grid>
                        <Grid item lg={3.1} xs={6} mt={4.5}>
                          {VideoDetailColumns.map((item) => (
                            <Grid container mt={2}>
                              <Grid item lg={6.1} xs={6}>
                                <Typography key={item.name} variant="subtitle1" color="GrayText" fontSize={13} mr={2}>
                                  {item.headerLabel}
                                </Typography>
                              </Grid>
                              <Grid item lg={5} xs={6} mb={0} mt="auto">
                                <Typography
                                  variant="h6"
                                  fontSize={13}
                                  color={item.dataKey === 'meetingLink' ? theme.palette.info.main : ''}
                                >
                                  : {(student as { [key: string]: string })[item.dataKey ?? '']}
                                </Typography>
                              </Grid>
                            </Grid>
                          ))}
                        </Grid> */}
                      </Grid>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Box>
          </div>
        </Card>
      </VideoDetailRoot>
      <Popup
        size="lg"
        title="Create Video"
        state={createPopup}
        onClose={() => setCreatePopup(false)}
        popupContent={<CreateVideo onClose={() => setCreatePopup(false)} />}
      />
      <Popup
        size="lg"
        title="Original File Map"
        state={mapOGFilePopup}
        onClose={() => setMapOGFilePopup(false)}
        popupContent={<MapOGFile onClose={() => setMapOGFilePopup(false)} />}
      />
      <Popup
        size="xs"
        state={Delete}
        onClose={handleClickCloseDelete}
        popupContent={<DeleteMessage message="Are you sure want to delete?" />}
      />
      <Popup size="lg" state={view} onClose={handleClickCloseView} popupContent={<ViewList />} />
    </Page>
  );
}

export default VideoDetail;
