import React, { useMemo, useState } from 'react';
import { Box, Grid, Stack, Typography, Card, useTheme, Chip } from '@mui/material';
import Popup from '@/components/shared/Popup/Popup';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import useSettings from '@/hooks/useSettings';
import AutorenewOutlinedIcon from '@mui/icons-material/AutorenewOutlined';
import { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import MenuEditDeleteView from '@/components/shared/Selections/MenuEditDeleteView';
import ViewOnlineExam from './ViewDescExam';

export const data = [
  {
    SlNo: 2,
    Class: '10 A',
    Subject: 'CHEMISTRY',
    Exam: 'FIRST TERMINAL EXAMINATION 2020-21',
    Type: 'Descriptive Type',
    StartTime: '29 Aug 2020 10:00 AM',
    EndTime: '29 Aug 2020 11:10 AM',
    AddedQuestions: '15 Questions',
    Staff: 'Passdaily Support',
    Status: 'Published',
  },
  {
    SlNo: 5,
    Class: '3 A',
    Subject: 'BIOLOGY',
    Exam: 'FIRST TERMINAL EXAMINATION 2020-21',
    Type: 'Descriptive Type',
    StartTime: '29 Aug 2020 09:15 AM',
    EndTime: '29 Aug 2020 11:10 AM',
    AddedQuestions: '15 Questions',
    Staff: 'Passdaily Support',
    Status: 'Published',
  },
  {
    SlNo: 7,
    Class: '10 A',
    Subject: 'SOCIAL',
    Exam: 'FIRST TERMINAL EXAMINATION 2020-21',
    Type: 'Descriptive Type',
    StartTime: '29 Aug 2020 02:00 PM',
    EndTime: '29 Aug 2020 03:00 PM',
    AddedQuestions: '15 Questions',
    Staff: 'Passdaily Support',
    Status: 'Published',
  },
  {
    SlNo: 8,
    Class: '4 A',
    Subject: 'HINDI',
    Exam: 'FIRST TERMINAL EXAMINATION 2020-21',
    Type: 'Descriptive Type',
    StartTime: '29 Aug 2020 11:00 AM',
    EndTime: '29 Aug 2020 12:10 PM',
    AddedQuestions: '15 Questions',
    Staff: 'Passdaily Support',
    Status: 'Published',
  },
  {
    SlNo: 10,
    Class: '1 A',
    Subject: 'PHYSICS',
    Exam: 'FIRST TERMINAL EXAMINATION 2020-21',
    Type: 'Descriptive Type',
    StartTime: '29 Aug 2020 01:00 PM',
    EndTime: '29 Aug 2020 02:00 PM',
    AddedQuestions: '15 Questions',
    Staff: 'Passdaily Support',
    Status: 'Published',
  },
  {
    SlNo: 12,
    Class: '2 B',
    Subject: 'GK',
    Exam: 'GK MOCK TEST 12',
    Type: 'Descriptive Type',
    StartTime: '10 Aug 2020 06:00 PM',
    EndTime: '10 Aug 2020 09:00 PM',
    AddedQuestions: '10 Questions',
    Staff: 'Passdaily Support',
    Status: 'Published',
  },
  {
    SlNo: 13,
    Class: '8 B',
    Subject: 'GK',
    Exam: 'GK MOCK TEST 11',
    Type: 'Descriptive Type',
    StartTime: '07 Aug 2020 06:00 PM',
    EndTime: '07 Aug 2020 09:00 PM',
    AddedQuestions: '10 Questions',
    Staff: 'Passdaily Support',
    Status: 'Published',
  },
  {
    SlNo: 15,
    Class: '10 B',
    Subject: 'GK',
    Exam: 'GK MOCK TEST B',
    Type: 'Descriptive Type',
    StartTime: '26 Jul 2020 07:00 AM',
    EndTime: '26 Jul 2020 08:30 AM',
    AddedQuestions: '10 Questions',
    Staff: 'Passdaily Support',
    Status: 'Published',
  },
];

function DescExamList() {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const [view, setView] = useState(false);
  const [Delete, setDelete] = useState(false);
  const [updatedData, setData] = useState(data);

  const handleStatusChange = (index: number) => {
    const copyData = [...updatedData];
    copyData[index].Status = copyData[index].Status === 'Published' ? 'Unpublished' : 'Published';
    setData(copyData);
  };

  const handleClickView = () => setView(true);
  const handleClickCloseView = () => setView(false);
  const handleClickDelete = () => setDelete(true);
  const handleClickCloseDelete = () => setDelete(false);

  const DescExamListColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'Exam',
        headerLabel: 'Exam',
        renderCell: (row) => {
          return (
            <Chip
              size="small"
              label={row.Exam}
              variant="outlined"
              color={row.Status === 'Published' ? 'info' : 'error'}
              sx={{
                border: '0px',
                backgroundColor: isLight ? theme.palette.info.lighter : theme.palette.grey[700],
                color: isLight ? theme.palette.info.darker : theme.palette.grey[100],
              }}
            />
          );
        },
      },
      {
        name: 'Class',
        headerLabel: 'Class & Subject',
        renderCell: (row) => {
          return (
            <Typography variant="h6" fontSize={15}>
              : {row.Class} - {row.Subject}
            </Typography>
          );
        },
      },
      {
        name: 'StartTime',
        dataKey: 'StartTime',
        headerLabel: 'Start Time',
      },

      {
        name: 'EndTime',
        dataKey: 'EndTime',
        headerLabel: 'End Time',
      },
      {
        name: 'Exam',
        headerLabel: 'Exam Type',
        renderCell: (row) => {
          return (
            <Chip
              size="small"
              label={row.Type}
              variant="outlined"
              color="success"
              sx={{
                border: '0px',
                backgroundColor: isLight ? theme.palette.info.lighter : theme.palette.grey[700],
                color: isLight ? theme.palette.info.darker : theme.palette.grey[100],
              }}
            />
          );
        },
      },
      {
        name: 'AddedQuestions',
        dataKey: 'AddedQuestions',
        headerLabel: 'Added Questions',
      },
      {
        name: 'Staff',
        dataKey: 'Staff',
        headerLabel: 'Staff',
      },
    ],
    [isLight, theme]
  );

  return (
    <Box className="card-container" my={2}>
      <Grid container spacing={2}>
        {updatedData.map((student, rowIndex) => (
          <Grid item xl={4} lg={6} md={12} sm={6} xs={12}>
            <Card className="student_card" sx={{ p: 2, boxShadow: 0, border: `1px solid ${theme.palette.grey[300]}` }}>
              <Box display="flex" flexDirection="column">
                <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                  <Chip
                    icon={<AutorenewOutlinedIcon sx={{ transform: 'rotate(180deg)' }} />}
                    size="small"
                    label={student.Status === 'Published' ? 'Click to Unpublish' : 'Click to Publish'}
                    variant="outlined"
                    color="primary"
                    clickable
                    onClick={() => handleStatusChange(rowIndex)}
                  />

                  <Stack direction="row" alignItems="center" gap={1} ml={1}>
                    <Chip
                      size="small"
                      label={student.Status === 'Published' ? ' Published' : 'Unpublished'}
                      variant="outlined"
                      color={student.Status === 'Published' ? 'success' : 'error'}
                      sx={{ border: '0px' }}
                    />
                    <MenuEditDeleteView
                      Edit={() => {
                        return 0;
                      }}
                      Delete={handleClickDelete}
                      View={handleClickView}
                    />
                  </Stack>
                </Box>

                {DescExamListColumns.map((item) => (
                  <Stack direction="row" ml={1}>
                    <Grid container>
                      <Grid item lg={5} xs={6} mt={1}>
                        <Typography key={item.name} variant="subtitle1" fontSize={13} mr={2}>
                          {item.headerLabel}
                        </Typography>
                      </Grid>
                      <Grid item lg={7} xs={6} mb={0} mt="auto">
                        <Typography
                          variant="h6"
                          fontSize={13}
                          color={item.dataKey === 'meetingLink' ? theme.palette.info.main : ''}
                        >
                          {item.dataKey
                            ? `:${' '}${(student as { [key: string]: any })[item.dataKey ?? '']}`
                            : item && item.renderCell && item.renderCell(student, rowIndex)}
                        </Typography>
                      </Grid>
                    </Grid>
                  </Stack>
                ))}
              </Box>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Popup
        size="xs"
        state={Delete}
        onClose={handleClickCloseDelete}
        popupContent={<DeleteMessage message="Are you sure want to delete?" />}
      />
      <Popup size="lg" state={view} popupContent={<ViewOnlineExam onClose={() => handleClickCloseView()} />} />
    </Box>
  );
}

export default DescExamList;
