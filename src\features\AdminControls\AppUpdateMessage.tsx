/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Box,
  Checkbox,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  Collapse,
  FormControl,
} from '@mui/material';
import styled from 'styled-components';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import SearchIcon from '@mui/icons-material/Search';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { MdAdd } from 'react-icons/md';
import { CLASS_SELECT } from '@/config/Selection';

const AppUpdateMessageRoot = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      height: calc(100% - 40px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

export const data = [
  {
    SlNo: 1,
    StudentName: 'AADHIDEV P',
    Class: 'LKG-B',
    RollNumber: 16,
    GuardianName: 'PRATHEESH',
    GuardianNumber: '+91 123-456-7890',
  },
  {
    SlNo: 2,
    StudentName: 'AADITH S',
    Class: 'LKG-C',
    RollNumber: 23,
    GuardianName: 'SIVADASAN',
    GuardianNumber: '+91 234-567-8901',
  },
  {
    SlNo: 3,
    StudentName: 'AADIV P',
    Class: 'LKG-A',
    RollNumber: 19,
    GuardianName: 'PRASADA',
    GuardianNumber: '+91 345-678-9012',
  },
  {
    SlNo: 4,
    StudentName: 'AATHIMIKA VR',
    Class: 'LKG-D',
    RollNumber: 51,
    GuardianName: 'VIJESHI',
    GuardianNumber: '+91 456-789-0123',
  },
  {
    SlNo: 5,
    StudentName: 'ABIS',
    Class: 'UKG-A',
    RollNumber: 33,
    GuardianName: 'SABARI PRASAD N',
    GuardianNumber: '+91 567-890-1234',
  },
  {
    SlNo: 6,
    StudentName: 'AFFANSHAWS',
    Class: 'UKG-C',
    RollNumber: 46,
    GuardianName: 'RSHAJAHAN',
    GuardianNumber: '+91 678-901-2345',
  },
  {
    SlNo: 7,
    StudentName: 'AADHISWAROOP P',
    Class: 'VI-A',
    RollNumber: 27,
    GuardianName: 'PRADEEPKUMAR',
    GuardianNumber: '+91 789-012-3456',
  },
  {
    SlNo: 8,
    StudentName: 'AAHIL A',
    Class: 'III-B',
    RollNumber: 7,
    GuardianName: 'ASHIK',
    GuardianNumber: '+91 890-123-4567',
  },
  {
    SlNo: 9,
    StudentName: 'AADHITH S',
    Class: 'I-C',
    RollNumber: 10,
    GuardianName: 'SHIBU',
    GuardianNumber: '+91 901-234-5678',
  },
  {
    SlNo: 10,
    StudentName: 'AARSHA R',
    Class: 'II-D',
    RollNumber: 11,
    GuardianName: 'AARADHYA K',
    GuardianNumber: '+91 987-654-3210',
  },
  {
    SlNo: 12,
    StudentName: 'AARON V',
    Class: 'IV-A',
    RollNumber: 13,
    GuardianName: 'AADIDEV P',
    GuardianNumber: '+91 876-543-2109',
  },
  {
    SlNo: 14,
    StudentName: 'AISWARYA',
    Class: 'III-B',
    RollNumber: 9,
    GuardianName: 'AJESH',
    GuardianNumber: '+91 765-432-1098',
  },
  {
    SlNo: 15,
    StudentName: 'AADHYA M',
    Class: 'III-A',
    RollNumber: 17,
    GuardianName: 'IV-A',
    GuardianNumber: '+91 890-123-4567',
  },
  {
    SlNo: 16,
    StudentName: 'A ADHISREYA A',
    Class: 'IV-A',
    RollNumber: 19,
    GuardianName: 'ANURAJ',
    GuardianNumber: '+91 654-321-0987',
  },
  {
    SlNo: 17,
    StudentName: 'AAGHNAYA CS',
    Class: 'V-B',
    RollNumber: 14,
    GuardianName: 'SAJIKUMAR',
    GuardianNumber: '+91 543-210-9876',
  },
  {
    SlNo: 18,
    StudentName: 'AAGNEYHARESWARANMR',
    Class: 1,
    RollNumber: 11,
    GuardianName: 'RAHUL KESAVAN',
    GuardianNumber: '+91 234-567-8901',
  },
];

function AppUpdateMessage() {
  const [drawerOpen, setDrawerOpen] = React.useState<boolean>(false);
  const [showFilter, setShowFilter] = useState(false);

  const handleOpen = () => {
    setDrawerOpen(true);
  };

  const toggleDrawerClose = useCallback(() => setDrawerOpen(false), []);

  const getRowKey = useCallback((row: any) => row.examId, []);

  const appUpdateMessageColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'select',
        renderHeader: () => {
          return <Checkbox />;
        },
        renderCell: () => {
          return <Checkbox />;
        },
      },
      {
        name: 'SlNo',
        dataKey: 'SlNo',
        headerLabel: 'Sl.No',
        sortable: true,
      },
      {
        name: 'StudentName',
        dataKey: 'StudentName',
        headerLabel: 'Student Name',
      },
      {
        name: 'Class',
        dataKey: 'Class',
        headerLabel: 'Class',
      },
      {
        name: 'RollNumber',
        dataKey: 'RollNumber',
        headerLabel: 'RollNumber',
      },
      {
        name: 'GuardianName',
        dataKey: 'GuardianName',
        headerLabel: 'GuardianName',
      },
      {
        name: 'GuardianNumber',
        dataKey: 'GuardianNumber',
        headerLabel: 'GuardianNumber',
      },
    ],
    []
  );

  return (
    <Page title="List">
      <AppUpdateMessageRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17}>
              Send AppUpdate Message
            </Typography>
            <Box pb={1} sx={{ flexShrink: 0 }}>
              <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                <SearchIcon />
              </IconButton>
              <Button onClick={handleOpen} sx={{ borderRadius: '20px' }} variant="outlined" size="small">
                <MdAdd size="20px" /> Create
              </Button>
            </Box>
          </Stack>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={3} alignItems="end">
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Autocomplete
                        options={CLASS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Autocomplete
                        options={CLASS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Student Name
                      </Typography>
                      <TextField placeholder="Enter Name" />
                    </FormControl>
                  </Grid>

                  <Grid item lg="auto" xs={12} sm={6}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Paper className="card-table-container" sx={{ marginTop: '12px' }}>
              <DataTable columns={appUpdateMessageColumns} data={data} getRowKey={getRowKey} fetchStatus="success" />
            </Paper>
          </div>
          {/* <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 2 }}>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="secondary">
                Cancel
              </Button>
              <Button onClick={handleOpen} variant="contained" color="primary">
                Save
              </Button>
            </Stack>
          </Box> */}
        </Card>
      </AppUpdateMessageRoot>

      <TemporaryDrawer onClose={toggleDrawerClose} Title="Upload Details" state={drawerOpen} DrawerContent="" />
    </Page>
  );
}

export default AppUpdateMessage;
