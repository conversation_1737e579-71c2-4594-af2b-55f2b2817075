/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  Collapse,
  FormControl,
} from '@mui/material';
import styled from 'styled-components';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import SearchIcon from '@mui/icons-material/Search';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import DeleteIcon from '@mui/icons-material/Delete';
import { MdAdd } from 'react-icons/md';
import DateSelect from '@/components/shared/Selections/DateSelect';
import { YEAR_SELECT } from '@/config/Selection';
import { CategorydummyData } from '@/features/PaymentDetails/CategoryList';
import { SubCategorydummyData } from '@/features/PaymentDetails/SubCategoryList';

const PaymentListRoot = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      height: calc(100% - 40px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;
const data = [
  {
    SlNo: 1,
    Category: 'Expense',
    SubCategory: 'Computer',
    Staff: 'Bianca Ellis',
    Narration: 'Hardware Spare',
    TotalPayment: 15000,
    PaymentDate: '17 Aug, 2023',
    PaymentBy: 'Passdaily',
  },
  {
    SlNo: 2,
    Category: 'Staff Salary',
    SubCategory: 'Salary',
    Staff: 'Tyler Jones',
    Narration: 'Salary',
    TotalPayment: 20000,
    PaymentDate: '16 Aug, 2023',
    PaymentBy: 'Passdaily',
  },
  {
    SlNo: 3,
    Category: 'Repair',
    SubCategory: 'Repair',
    Staff: 'Vincent Lucas',
    Narration: 'Repair',
    TotalPayment: 6000,
    PaymentDate: '10 Aug, 2023',
    PaymentBy: 'Passdaily',
  },
  {
    SlNo: 4,
    Category: 'Income',
    SubCategory: 'Income',
    Staff: 'Kathryn',
    Narration: 'Income',
    TotalPayment: 50000,
    PaymentDate: '09 Aug, 2023',
    PaymentBy: 'Passdaily',
  },
  {
    SlNo: 5,
    Category: 'Staff Salary',
    SubCategory: 'Salary',
    Staff: 'Alan Lawson',
    Narration: 'Staff Salary',
    TotalPayment: 20000,
    PaymentDate: '03 Aug, 2023',
    PaymentBy: 'Passdaily',
  },
  {
    SlNo: 6,
    Category: 'Expense',
    SubCategory: 'Expense',
    Staff: 'Tyler Marshall',
    Narration: 'Expense',
    TotalPayment: 12000,
    PaymentDate: '25 Jul, 2023',
    PaymentBy: 'Passdaily',
  },
  {
    SlNo: 7,
    Category: 'Repair',
    SubCategory: 'Repair',
    Staff: 'Matthews',
    Narration: 'Spares',
    TotalPayment: 4000,
    PaymentDate: '20 Jul, 2023',
    PaymentBy: 'Passdaily',
  },
  {
    SlNo: 8,
    Category: 'Salary',
    SubCategory: 'Salary',
    Staff: 'Ruthi',
    Narration: 'Salary',
    TotalPayment: 20000,
    PaymentDate: '16 Jul, 2023',
    PaymentBy: 'Passdaily',
  },
  {
    SlNo: 9,
    Category: 'Income',
    SubCategory: 'Income',
    Staff: 'Reynolds',
    Narration: 'Income',
    TotalPayment: 40000,
    PaymentDate: '07 Jul, 2023',
    PaymentBy: 'Passdaily',
  },
  {
    SlNo: 10,
    Category: 'Expense',
    SubCategory: 'Computer',
    Staff: 'Marilyn Rivera',
    Narration: 'Others',
    TotalPayment: 15000,
    PaymentDate: '06 Jul, 2023',
    PaymentBy: 'Passdaily',
  },
  {
    SlNo: 11,
    Category: 'Expense',
    SubCategory: 'Others',
    Staff: 'Bruce Stewart',
    Narration: 'Others',
    TotalPayment: 18000,
    PaymentDate: '03 Jul, 2023',
    PaymentBy: 'Passdaily',
  },
];

function PaymentList() {
  const [drawerOpen, setDrawerOpen] = React.useState<boolean>(false);
  const [showFilter, setShowFilter] = useState(false);

  const handleOpen = () => {
    setDrawerOpen(true);
  };

  const toggleDrawerClose = useCallback(() => setDrawerOpen(false), []);

  const getRowKey = useCallback((row: any) => row.examId, []);
  const paymentListColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'slNo',
        dataKey: 'SlNo',
        headerLabel: 'Sl.No',
        sortable: true,
      },
      {
        name: 'name',
        dataKey: 'Category',
        headerLabel: 'Category',
      },
      {
        name: 'title',
        dataKey: 'SubCategory',
        headerLabel: 'Sub Category',
      },
      {
        name: 'file',
        dataKey: 'Staff',
        headerLabel: 'Staff',
      },
      {
        name: 'totalPayment',
        dataKey: 'TotalPayment',
        headerLabel: 'Total Payment',
      },
      {
        name: 'paymentDate',
        dataKey: 'PaymentDate',
        headerLabel: 'Payment Date',
      },
      {
        name: 'paymentBy',
        dataKey: 'PaymentBy',
        headerLabel: 'Payment By',
      },
      {
        name: 'actions',
        headerLabel: 'Actions',
        renderCell: () => {
          return (
            <Stack direction="row" gap={1}>
              <IconButton size="small" color="error" onClick={handleOpen} sx={{ padding: 0.5 }}>
                <DeleteIcon />
              </IconButton>
            </Stack>
          );
        },
      },
    ],
    []
  );
  return (
    <Page title="List">
      <PaymentListRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17}>
              Payments List
            </Typography>
            <Box pb={1} sx={{ flexShrink: 0 }}>
              <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                <SearchIcon />
              </IconButton>
              <Button onClick={handleOpen} sx={{ borderRadius: '20px' }} variant="outlined" size="small">
                <MdAdd size="20px" /> Add
              </Button>
            </Box>
          </Stack>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={3} alignItems="end">
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="h6" fontSize={14}>
                        Academic Year
                      </Typography>
                      <Autocomplete
                        options={YEAR_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select Year" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="h6" fontSize={14}>
                        Category
                      </Typography>
                      <Autocomplete
                        options={CategorydummyData.map((item) => item.name)}
                        renderInput={(params) => <TextField {...params} placeholder="Select Category" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="h6" fontSize={14}>
                        Sub Category
                      </Typography>
                      <Autocomplete
                        options={SubCategorydummyData.map((item) => item.name)}
                        renderInput={(params) => <TextField {...params} placeholder="Select Sub Category" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="h6" fontSize={14}>
                        Staff
                      </Typography>
                      <Autocomplete
                        options={['Staff1', 'Staff2', 'Staff3', 'Admin']}
                        renderInput={(params) => <TextField {...params} placeholder="Select Staff" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg={2} xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="h6" fontSize={14}>
                        From
                      </Typography>
                      <DateSelect />
                    </FormControl>
                  </Grid>
                  <Grid item lg={2} xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="h6" fontSize={14}>
                        To
                      </Typography>
                      <DateSelect />
                    </FormControl>
                  </Grid>

                  <Grid item lg="auto" xs={12} sm={6}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Paper className="card-table-container" sx={{ marginTop: '12px' }}>
              <DataTable columns={paymentListColumns} data={data} getRowKey={getRowKey} fetchStatus="success" />
            </Paper>
          </div>
          {/* <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 2 }}>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="secondary">
                Cancel
              </Button>
              <Button onClick={handleOpen} variant="contained" color="primary">
                Save
              </Button>
            </Stack>
          </Box> */}
        </Card>
      </PaymentListRoot>

      <TemporaryDrawer onClose={toggleDrawerClose} Title="Upload Details" state={drawerOpen} DrawerContent="" />
    </Page>
  );
}

export default PaymentList;
