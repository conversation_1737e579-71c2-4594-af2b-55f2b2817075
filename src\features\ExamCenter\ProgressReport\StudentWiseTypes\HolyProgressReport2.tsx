import React, { useRef, useState } from 'react';
import {
  Button,
  Paper,
  TableRow,
  Typography,
  Grid,
  Avatar,
  Stack,
  Box,
  TableCell,
  Table,
  TableBody,
} from '@mui/material';
import Page from '@/components/shared/Page';
import logo from '@/assets/SchoolLogos/HolyLogo.jpeg';
import user from '@/assets/user.jpg';
import img from '@/assets/holyProgressCardImg/img.png';
import img1 from '@/assets/holyProgressCardImg/img1.png';
import img2 from '@/assets/holyProgressCardImg/img2.png';
import img3 from '@/assets/holyProgressCardImg/img3.png';
import img4 from '@/assets/holyProgressCardImg/img4.png';
import img5 from '@/assets/holyProgressCardImg/img5.png';
import img6 from '@/assets/holyProgressCardImg/img6.png';
import img7 from '@/assets/holyProgressCardImg/img7.png';
import img8 from '@/assets/holyProgressCardImg/img8.png';
import img9 from '@/assets/holyProgressCardImg/img9.png';
import img10 from '@/assets/holyProgressCardImg/img10.png';
import img11 from '@/assets/holyProgressCardImg/img11.png';
import img12 from '@/assets/holyProgressCardImg/img12.png';
import img13 from '@/assets/holyProgressCardImg/img13.png';
import img14 from '@/assets/holyProgressCardImg/img14.png';
import img15 from '@/assets/holyProgressCardImg/img15.png';
import img16 from '@/assets/holyProgressCardImg/img16.png';
import img17 from '@/assets/holyProgressCardImg/img17.png';
import img18 from '@/assets/holyProgressCardImg/img18.png';
import img19 from '@/assets/holyProgressCardImg/img19.png';
import img20 from '@/assets/holyProgressCardImg/img20.png';
import img21 from '@/assets/holyProgressCardImg/img21.png';
import img22 from '@/assets/holyProgressCardImg/img22.png';
import img23 from '@/assets/holyProgressCardImg/img23.png';
import img24 from '@/assets/holyProgressCardImg/img24.png';
import img25 from '@/assets/holyProgressCardImg/img25.png';
import img26 from '@/assets/holyProgressCardImg/img26.png';
import img27 from '@/assets/holyProgressCardImg/img27.png';
import img28 from '@/assets/holyProgressCardImg/img28.png';
import img29 from '@/assets/holyProgressCardImg/img29.png';
import { useReactToPrint } from 'react-to-print';
import styled from 'styled-components';

const Holy2Root = styled.div`
  .MuiTableCell-root {
    border: 1px solid black !important;
    font-weight: 600 !important;
    padding: 0px !important;
    height: 24px !important;
    font-size: 10px !important;
    display: table-cell !important; /* Ensures correct display */
    color: black;
  }

  table {
    border-collapse: collapse !important;
    width: 100% !important;
  }
`;

const Holy2 = () => {
  const [reportCount, setReportCount] = useState(1);
  const reportRef = useRef<(HTMLDivElement | null)[]>([]);

  const handlePrint = useReactToPrint({
    content: () => {
      const printableContent = document.createElement('div');
      reportRef.current.forEach((ref) => {
        if (ref) {
          printableContent.appendChild(ref.cloneNode(true));
        }
      });
      return printableContent;
    },
  });

  return (
    <Page title="Student Wise">
      <Holy2Root style={{ textAlign: 'center', printColorAdjust: 'exact', overflow: 'scroll' }}>
        {/* <Button variant="contained" color="primary" onClick={handlePrint}>
        Print Report Card
      </Button> */}
        <div>
          {/* Buttons to increase/decrease Holy2s */}
          <Stack direction="row" spacing={2} mt={20} mb={2}>
            <Button variant="contained" onClick={() => setReportCount((prev) => Math.max(1, prev - 1))} color="error">
              -
            </Button>
            <Typography variant="h6">Pages: {reportCount}</Typography>
            <Button variant="contained" onClick={() => setReportCount((prev) => prev + 1)} color="success">
              +
            </Button>
            <Button variant="contained" color="primary" onClick={handlePrint}>
              Print Report Card
            </Button>
          </Stack>

          {/* Holy2 List */}
          {Array.from({ length: reportCount }).map((_, index) => {
            return (
              <Box
                key={index}
                ref={(el) => (reportRef.current[index] = el)}
                // ref={reportRef}
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  '@media print': {
                    padding: 0,
                  },
                }}
              >
                {/* =============== Page No 1 =============== */}
                <Paper
                  style={{
                    pageBreakAfter: 'always',
                    width: '210mm', // Adjusted width for margin
                    height: '302.5mm', // Adjusted height for margin
                    padding: '10px',
                    backgroundColor: '#ffff99',
                    border: '2px dashed black',
                  }}
                >
                  <Stack mt={5} direction="row" alignItems="center" gap={2} justifyContent="center">
                    <img src={logo} width={100} alt="" />
                    <Typography variant="h4" color="error" fontWeight="bold">
                      <i> ANGELS&apos; PARADISE</i>
                    </Typography>
                  </Stack>
                  <Stack direction="column" alignItems="center" gap={2} justifyContent="center">
                    <Typography color="black" variant="h6" fontStyle="italic">
                      REPORT CARD
                    </Typography>
                    <Typography color="black" variant="h6" fontStyle="italic">
                      (ACADEMIC YEAR 2024-2025)
                    </Typography>
                  </Stack>
                  <Grid container justifyContent="center" style={{ margin: '10px 0 30px 0 ' }}>
                    <Avatar
                      src={user}
                      sx={{ borderRadius: 0, width: '3.5cm', height: '4.5cm', border: '2px solid black' }}
                    />
                  </Grid>
                  {[
                    ['NAME', 'AADIRAJ SAGAR MORE'],
                    ['ROLL NO.', '1'],
                    ["FATHER'S NAME", 'SAGAR'],
                    ["MOTHER'S NAME", 'ASHWINI'],
                    ['CLASS', 'JRKG'],
                    ['SECTION', 'A'],
                    ['DATE OF BIRTH', '07/Apr/2020'],
                    ['NAME OF THE CLASS TEACHER', 'JANHAVI SHRIPURN JOSHI'],
                  ].map(([label, value], index) => (
                    <Stack key={index} direction="row" alignItems="center" gap={0} justifyContent="space-around" pl={5}>
                      <Stack width="40%" textAlign="start" mb={4}>
                        <Typography color="black" variant="subtitle2" fontSize={13}>
                          {label}
                        </Typography>
                      </Stack>
                      <Stack width="60%" textAlign="start" mb={4}>
                        <Typography color="black" variant="subtitle2" fontSize={13}>
                          :&nbsp;{value}
                        </Typography>
                      </Stack>
                    </Stack>
                  ))}
                  <Stack mt={0} direction="row" justifyContent="space-between" alignItems="center">
                    <div>
                      <img width={150} src={img} alt="img1" />
                    </div>
                    <div>
                      <img width={190} src={img1} alt="img1" />
                    </div>
                  </Stack>
                </Paper>

                {/* Page Break - Forces Next Content to Print on a New A4 Page */}
                {/* =============== Page No 2 =============== */}
                <Paper
                  style={{
                    pageBreakAfter: 'always',
                    width: '210mm',
                    height: '302.5mm',
                    padding: '10px',
                    backgroundColor: '#ffff99',
                    border: '2px dashed black',
                    margin: 'auto',
                  }}
                >
                  {' '}
                </Paper>
                {/* =============== Page No 3 =============== */}
                <Paper
                  style={{
                    pageBreakAfter: 'always',
                    width: '210mm',
                    height: '302.5mm',
                    padding: '10px',
                    backgroundColor: '#ffff99',
                    border: '2px dashed black',
                    margin: 'auto',
                  }}
                >
                  <Box border={1} borderColor="black">
                    <Stack direction="row" py={1} px={1} gap={1}>
                      <Typography color="black" variant="subtitle2" fontSize={12}>
                        NAME:
                      </Typography>
                      <Typography color="black" variant="subtitle2" fontSize={12} borderBottom={1} width={250}>
                        {' '}
                      </Typography>
                      <Typography color="black" variant="subtitle2" fontSize={12}>
                        CLASS:
                      </Typography>
                      <Typography color="black" variant="subtitle2" fontSize={12} borderBottom={1} width={110}>
                        {' '}
                      </Typography>
                      <Typography color="black" variant="subtitle2" fontSize={12}>
                        ROL NO:
                      </Typography>
                      <Typography color="black" variant="subtitle2" fontSize={12} borderBottom={1} width={110}>
                        {' '}
                      </Typography>
                    </Stack>
                    <Table>
                      <TableBody>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            colSpan={5}
                            align="center"
                          >
                            HEALTH STATUS
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                            rowSpan={3}
                          >
                            <Stack alignItems="center">
                              <img width={60} src={img2} alt="" />
                            </Stack>
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            {' '}
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            TERM 1
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            TERM 2
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                            rowSpan={3}
                          >
                            <Stack alignItems="center">
                              <img width={60} src={img3} alt="" />
                            </Stack>
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            HEIGHT
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            105 cm
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            108 cm
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            WEIGHT
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            16 kg
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            16 kg
                          </TableCell>
                        </TableRow>
                        {/* LARGE MOTOR SKILLS */}
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            colSpan={5}
                            align="center"
                          >
                            LARGE MOTOR SKILLS
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                            rowSpan={11}
                          >
                            <Stack gap={2} alignItems="center">
                              <img width={100} src={img4} alt="" />
                              <img width={100} src={img5} alt="" />
                            </Stack>
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            {' '}
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            TERM 1
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            TERM 2
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                            rowSpan={11}
                          >
                            <Stack gap={2} alignItems="center">
                              <img width={100} src={img6} alt="" />
                              <img width={100} src={img7} alt="" />
                            </Stack>
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            WALKING IN STRAIGHT LINE
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            WALKING ON ZIG-ZAG LINE
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            RUNNING
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            JUMPING
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            KICKING
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            THROWING
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            CATCHING
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            BALANCING
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            HOPPING
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            DANCING
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                        </TableRow>
                        {/* FINE MOTOR SKILLS */}
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            colSpan={5}
                            align="center"
                          >
                            FINE MOTOR SKILLS
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                            rowSpan={15}
                          >
                            <Stack gap={2} alignItems="center">
                              <img width={100} src={img8} alt="" />
                              <img width={100} src={img9} alt="" />
                            </Stack>
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            {' '}
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            TERM 1
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            TERM 2
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                            rowSpan={15}
                          >
                            <Stack gap={2} alignItems="center">
                              <img width={130} src={img10} alt="" />
                              <img width={130} src={img11} alt="" />
                            </Stack>
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            PASSING THE PARCEL
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            PICKING OBJECT
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            ZIPPING
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            BUTTONING
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            BUCKLING
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            MOULDING CLAY
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            PAPER FOLDING
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            LACING
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            BEADING
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            COMPLETES PUZZLES
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Need Practice
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Need Practice
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            TEARING & PASTING
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            HOLDING
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            TURNING THE PAGES
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            PINCER GRIP
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </Box>
                </Paper>

                {/* =============== Page No 4 =============== */}

                <Paper
                  style={{
                    pageBreakAfter: 'always',
                    width: '210mm',
                    height: '302.5mm',
                    padding: '10px',
                    backgroundColor: '#ffff99',
                    border: '2px dashed black',
                    margin: 'auto',
                  }}
                >
                  <Box border={1} borderColor="black">
                    <Stack direction="row" py={1} px={1} gap={1}>
                      <Typography color="black" variant="subtitle2" fontSize={12}>
                        NAME:
                      </Typography>
                      <Typography color="black" variant="subtitle2" fontSize={12} borderBottom={1} width={250}>
                        {' '}
                      </Typography>
                      <Typography color="black" variant="subtitle2" fontSize={12}>
                        CLASS:
                      </Typography>
                      <Typography color="black" variant="subtitle2" fontSize={12} borderBottom={1} width={110}>
                        {' '}
                      </Typography>
                      <Typography color="black" variant="subtitle2" fontSize={12}>
                        ROL NO:
                      </Typography>
                      <Typography color="black" variant="subtitle2" fontSize={12} borderBottom={1} width={110}>
                        {' '}
                      </Typography>
                    </Stack>
                    <Table>
                      <TableBody>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            colSpan={5}
                            align="center"
                          >
                            PERSONALITY DEVELOPMENT
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                            rowSpan={12}
                          >
                            <Stack gap={2} alignItems="center">
                              <img width={100} src={img12} alt="" />
                              <img width={120} src={img13} alt="" />
                            </Stack>
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            {' '}
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            TERM 1
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            TERM 2
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                            rowSpan={12}
                          >
                            <Stack gap={2} alignItems="center">
                              <img width={120} src={img14} alt="" />
                              <img width={120} src={img15} alt="" />
                            </Stack>
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            SHARES THINGS
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            COMES NEATLY DRESSED
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            FOLLOWS PERSONAL HYGIENE
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            REGULAR TO SCHOOL
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            COMES ON TIME
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Need Practice
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            RESPECTS TEACHER
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            RESPECTS PEER GROUP
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            COMPLETES TASK ON TIME
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Need Practice
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            WAITS FOR TURN
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            PARTICIPATES IN ACTIVITY
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            WORKS INDEPENDENTLY
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                        </TableRow>
                        {/*  LITERACY SKILLS */}
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            colSpan={5}
                            align="center"
                          >
                            LITERACY SKILLS
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                            rowSpan={14}
                          >
                            <Stack gap={2} alignItems="center">
                              <img width={130} src={img16} alt="" />
                              <img width={130} src={img17} alt="" />
                            </Stack>
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            {' '}
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            TERM 1
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            TERM 2
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                            rowSpan={14}
                          >
                            <Stack gap={2} alignItems="center">
                              <img width={130} src={img18} alt="" />
                              <img width={130} src={img19} alt="" />
                            </Stack>
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            LISTENING TO RHYME
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            LISTENING TO STORIES
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            RECOGNISES LETTERS
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Need Practice
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            IDENTIFIES PHONIC SOUND
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Need Practice
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            RECITES RHYME
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            RECITES PHONIC SONG WITH ACTION
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            READS WORDS WITH PHONIC SOUND
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            RECOGNISES SIGHT WORDS
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Need Practice
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            CONVERSE IN ENGLISH
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Need Practice
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            NARRATES STORY
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            TRACE/WRITE LETTERS OR SENTENCES
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            NEATNESS IN WRITING
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Need Practice
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            WRITES WITH CORRECT FORMATION
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Need Practice
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Need Practice
                          </TableCell>
                        </TableRow>
                        {/* FINE MOTOR SKILLS */}
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            colSpan={5}
                            align="center"
                          >
                            NUMERACY SKILLS
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                            rowSpan={15}
                          >
                            <Stack gap={2} alignItems="center">
                              <img width={120} src={img20} alt="" />
                              <img width={120} src={img21} alt="" />
                            </Stack>
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            {' '}
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            TERM 1
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            TERM 2
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                            rowSpan={15}
                          >
                            <Stack gap={2} alignItems="center">
                              <img width={120} src={img22} alt="" />
                              <img width={120} src={img23} alt="" />
                            </Stack>
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            RECOGNISES NUMBERS
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            COUNTS THE OBJECTS
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            SEQUENCING THE NUMBERS
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            AWARE OF MATHEMATICAL CONCEPT
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            TRACE/WRITE NUMBERS
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            WRITES WITH CORRECT FORMATION
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            WRITES WITH CORRECT FORMATION
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            NEATNESS IN WRITING
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Need Practice
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            EXPLORE WAYS TO SOLVE PROBLEM
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </Box>
                </Paper>
                {/* Page No 5 */}
                <Paper
                  style={{
                    pageBreakAfter: 'always',
                    width: '210mm',
                    height: '302.5mm',
                    padding: '10px',
                    backgroundColor: '#ffff99',
                    border: '2px dashed black',
                    margin: 'auto',
                  }}
                >
                  <Box border={1} borderColor="black">
                    <Stack direction="row" py={1} px={1} gap={1}>
                      <Typography color="black" variant="subtitle2" fontSize={12}>
                        NAME:
                      </Typography>
                      <Typography color="black" variant="subtitle2" fontSize={12} borderBottom={1} width={250}>
                        {' '}
                      </Typography>
                      <Typography color="black" variant="subtitle2" fontSize={12}>
                        CLASS:
                      </Typography>
                      <Typography color="black" variant="subtitle2" fontSize={12} borderBottom={1} width={110}>
                        {' '}
                      </Typography>
                      <Typography color="black" variant="subtitle2" fontSize={12}>
                        ROL NO:
                      </Typography>
                      <Typography color="black" variant="subtitle2" fontSize={12} borderBottom={1} width={110}>
                        {' '}
                      </Typography>
                    </Stack>
                    <Table>
                      <TableBody>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            colSpan={5}
                            align="center"
                          >
                            GENERAL AWARENESS SKILLS
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                            rowSpan={6}
                          >
                            <Stack gap={2} alignItems="center">
                              <img width={130} src={img24} alt="" />
                            </Stack>
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            {' '}
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            TERM 1
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            TERM 2
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                            rowSpan={6}
                          >
                            <Stack gap={2} alignItems="center">
                              <img width={130} src={img25} alt="" />
                            </Stack>
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            IDENTIFIES THINGS IN ENVIRONMENT
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            SORTS AND CLASSIFIES OBJECT
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            OBSERVES WITH INTEREST DURING EXPERIMENTATION
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            INVOLVES IN EXPERIMENTAL LEARNING
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            UNDERSTANDING OF LIFE SKILLS
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                        </TableRow>
                        {/* MUSIC & MOVEMENTS */}
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            colSpan={5}
                            align="center"
                          >
                            MUSIC & MOVEMENTS
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                            rowSpan={5}
                          >
                            <Stack gap={2} alignItems="center">
                              <img width={130} src={img26} alt="" />
                            </Stack>
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            {' '}
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            TERM 1
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            TERM 2
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                            rowSpan={5}
                          >
                            <Stack gap={2} alignItems="center">
                              <img width={130} src={img27} alt="" />
                            </Stack>
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            SHOWS INTEREST IN LISTENING TO SONGS
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            REMEMBERS LYRICS OF THE SONG
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Need Practice
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            SING SONGS WITH RHYTHM
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            SHOWS INTEREST IN DANCE
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                        </TableRow>
                        {/* ART & CRAFT */}
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            colSpan={5}
                            align="center"
                          >
                            ART & CRAFT
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                            rowSpan={6}
                          >
                            <Stack gap={2} alignItems="center">
                              <img width={130} src={img28} alt="" />
                            </Stack>
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            {' '}
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            TERM 1
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            TERM 2
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                            rowSpan={6}
                          >
                            <Stack gap={2} alignItems="center">
                              <img width={130} src={img29} alt="" />
                            </Stack>
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            SHOWS INTEREST IN ART
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            ENJOYS COLOURING
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Always
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            SHOWS IMAGINATION
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            SHOWS CREATIVITY
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            NEATNESS IN COLOURING
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Need Practice
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Sometime
                          </TableCell>
                        </TableRow>
                        {/*  ACADEMIC ASSESSMENT */}
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            colSpan={5}
                            align="center"
                          >
                            ACADEMIC ASSESSMENT
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Subject
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Skill
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Marks
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            TERM 1
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            TERM 2
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                            rowSpan={3}
                          >
                            English
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Oral
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            25
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            10
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            11
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Written
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            50
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            23
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            25
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            TOTAL
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            75
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            33
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            35.5
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                            rowSpan={3}
                          >
                            Maths
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Oral
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            25
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            10
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            11
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Written
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            50
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            23
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            25
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            TOTAL
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            75
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            33
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            35.5
                          </TableCell>
                        </TableRow>

                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                            rowSpan={3}
                          >
                            E V S
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Oral
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            25
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            10
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            11
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Written
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            50
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            23
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            25
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            TOTAL
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            75
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            33
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            35.5
                          </TableCell>
                        </TableRow>

                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                            rowSpan={3}
                          >
                            Hindi
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Oral
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            25
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            10
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            11
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Written
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            50
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            23
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            25
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            TOTAL
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            75
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            33
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            35.5
                          </TableCell>
                        </TableRow>

                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            {' '}
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            GRAND TOTAL
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            250
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            250
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            250
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Coloring
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Grade
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            {' '}
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            C+
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            C+
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Craft
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            Grade
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            {' '}
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            C+
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            C+
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            colSpan={5}
                            align="center"
                          >
                            ATTENDANCE
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            {' '}
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                            colSpan={2}
                          >
                            {' '}
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            TERM 1
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            TERM 2
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            {' '}
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                            colSpan={2}
                          >
                            ATTENDANCE
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            82 / 94
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            82 / 92
                          </TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </Box>
                </Paper>

                {/* Page No 6 */}
                <Paper
                  style={{
                    pageBreakAfter: 'always',
                    width: '210mm',
                    height: '302.5mm',
                    padding: '10px',
                    backgroundColor: '#ffff99',
                    border: '2px dashed black',
                    margin: 'auto',
                  }}
                >
                  <Box border={1} borderColor="black">
                    <Stack direction="row" py={1} px={1} gap={1}>
                      <Typography color="black" variant="subtitle2" fontSize={12}>
                        NAME:
                      </Typography>
                      <Typography color="black" variant="subtitle2" fontSize={12} borderBottom={1} width={250}>
                        {' '}
                      </Typography>
                      <Typography color="black" variant="subtitle2" fontSize={12}>
                        CLASS:
                      </Typography>
                      <Typography color="black" variant="subtitle2" fontSize={12} borderBottom={1} width={110}>
                        {' '}
                      </Typography>
                      <Typography color="black" variant="subtitle2" fontSize={12}>
                        ROL NO:
                      </Typography>
                      <Typography color="black" variant="subtitle2" fontSize={12} borderBottom={1} width={110}>
                        {' '}
                      </Typography>
                    </Stack>
                    <Table>
                      <TableBody>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            colSpan={3}
                            align="center"
                          >
                            GRADATION
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            RANGE
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            GRADE
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            PROFICIENCY LEVEL
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            91% & ABOVE
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            A+
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            EXCELLENT
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            75-90%
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            A
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            VERY GOOD
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            60-74%
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            B+
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            GOOD
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            46-59%
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            B
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            AVERAGE
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            BELOW 45%
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            C
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            NEEDS IMPROVEMENT
                          </TableCell>
                        </TableRow>

                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            colSpan={3}
                            align="center"
                          >
                            TERM RESULT
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                            colSpan={2}
                          >
                            PERCENTAGE
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            60.3%
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                            colSpan={2}
                          >
                            GRADE
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            60.3%
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                            colSpan={2}
                          >
                            RANK
                          </TableCell>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                          >
                            2
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                            colSpan={3}
                          >
                            REMARKS
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            className="table-cell"
                            style={{ border: '1px solid black', fontSize: 11, fontWeight: 600, height: 100 }}
                            align="center"
                            colSpan={3}
                          >
                            Good
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                            className="table-cell"
                            align="center"
                            colSpan={3}
                          >
                            RESULT
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            className="table-cell"
                            style={{ border: '1px solid black', fontSize: 11, fontWeight: 600, height: 100 }}
                            align="center"
                            colSpan={3}
                          >
                            PASSED TO / PROMOTED TO CLASS : SRKG - C
                          </TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                    <Typography color="black" p={1} variant="subtitle2" textAlign="start" fontSize={12}>
                      ISSUE DATE:
                    </Typography>
                    <Stack px={1} mt={20} direction="row" justifyContent="space-between">
                      <Typography color="black" variant="subtitle2" fontSize={12}>
                        CLASS TEACHER
                      </Typography>
                      <Typography color="black" variant="subtitle2" fontSize={12}>
                        PRINCIPAL
                      </Typography>
                      <Typography color="black" variant="subtitle2" fontSize={12}>
                        HEADMISTRESS
                      </Typography>
                      <Typography color="black" variant="subtitle2" fontSize={12}>
                        PARENT
                      </Typography>
                    </Stack>
                  </Box>
                </Paper>
              </Box>
            );
          })}
        </div>
      </Holy2Root>
    </Page>
  );
};

export default Holy2;
