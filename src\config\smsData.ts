import {
  messageSendToAllConveyors,
  messageSendToAllParents,
  messageSendToAllPta,
  messageSendToAllStaff,
} from '@/store/MessageBox/messageBox.thunks';

export const smsrows = [
  {
    messageId: '01',
    messageTitle: 'Annual Day Celebration',
    messageContent: 'School is going to Celebrate annual day celebration on 10/02/2023 at school auditorium.',
    messageDate: '05 Feb 2023,10:00 am',
    messageCreatedBy: 'Passdaily Support',
    messageStatus: 0,
    messageTemplateId: '',
    messageType: 0,
  },
  {
    messageId: '02',
    messageTitle: 'Christmas Celebration',
    messageContent: 'School is going to Celebrate Christmas celebration on 25/12/2022 at school auditorium.',
    messageDate: '15 Dec 2022,12:00 am',
    messageCreatedBy: 'Passdaily Support',
    messageStatus: 'Through Link',
    messageTemplateId: '',
    messageType: '',
  },
  {
    messageId: '03',
    messageTitle: 'Christmas Celebration',
    messageContent: 'School is going to Celebrate Christmas celebration on 25/12/2022 at school auditorium.',
    messageDate: '15 Dec 2022,12:00 am',
    messageCreatedBy: 'Passdaily Support',
    messageStatus: 'Through Link',
    messageTemplateId: '',
    messageType: '',
  },
  {
    messageId: '04',
    messageTitle: 'Christmas Celebration',
    messageContent: 'School is going to Celebrate Christmas celebration on 25/12/2022 at school auditorium.',
    messageDate: '15 Dec 2022,12:00 am',
    messageCreatedBy: 'Passdaily Support',
    messageStatus: 'Through Link',
    messageTemplateId: '',
    messageType: '',
  },
];

export type QuickSendlinksTypes = {
  id: string;
  name: string;
  component: string;
};
export const quicksendlinks1: QuickSendlinksTypes[] = [
  { id: '1', name: 'Student Individual', component: 'Parents' },
  { id: '2', name: 'Staff', component: 'Staffs' },
  { id: '3', name: 'PTA', component: 'PTA' },
  { id: '4', name: 'Conveyors', component: 'Conveyors' },
  { id: '5', name: 'Class Wise', component: 'ClassWise' },
  { id: '6', name: 'Class Division Wise', component: 'ClassDivision' },
  { id: '7', name: 'Public Groups', component: 'PublicGroups' },
];

export const quicksendlinks2: QuickSendlinksTypes[] = [
  { id: '1', name: 'Public Group Wise', component: 'PublicGroupWise' },
  { id: '2', name: 'Group Individual', component: 'Groups' },
  { id: '3', name: 'Group Wise', component: 'GroupWise' },
  // { id: '4', name: 'All Parents' },
  // { id: '5', name: 'All Staff' },
  // { id: '6', name: 'All PTA' },
  // { id: '7', name: 'All Conveyors' },
];

export type SendToAllTypes = {
  id: string;
  name: string;
  action: any;
};
export const sendToAll: SendToAllTypes[] = [
  { id: '1', name: 'All Parents', action: messageSendToAllParents },
  { id: '2', name: 'All Staffs', action: messageSendToAllStaff },
  { id: '3', name: 'All PTA', action: messageSendToAllPta },
  { id: '4', name: 'All Conveyors', action: messageSendToAllConveyors },
];

export const templetelinks = [
  { id: '1', name: 'PTA', link: '' },
  { id: '2', name: 'Conveyors', link: '' },
  { id: '3', name: 'Parents', link: '' },
  { id: '4', name: 'Staff', link: '' },
  { id: '5', name: 'Group', link: '' },
  { id: '6', name: 'Public Group ', link: '' },
  { id: '7', name: 'Class Wise', link: '' },
  { id: '8', name: 'Class Division', link: '' },
  { id: '9', name: 'Group Wise', link: '' },
  { id: '10', name: 'Public Group Wise', link: '' },
  { id: '11', name: 'All Parents', link: '' },
  { id: '12', name: 'All Staffs', link: '' },
  { id: '13', name: 'All Conveyors', link: '' },
  { id: '14', name: 'All PTA', link: '' },
  { id: '15', name: 'Staff App', link: '' },
];
