import {
  Table,
  TableCell,
  TableHead,
  TableRow,
  TableContainer,
  Box,
  TableBody,
  DialogActions,
  Paper,
} from '@mui/material';

export const EditedTable = () => {
  return (
    <Box>
      <Box sx={{ width: { xs: '320px', md: '650px' }, overflow: 'auto' }}>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>SL.No</TableCell>
                <TableCell>Amount (INR)</TableCell>
                <TableCell>Fee Title</TableCell>
                <TableCell>Status</TableCell>
              </TableRow>
            </TableHead>

            <TableBody>
              {[1, 2, 3, 4]?.map(() => (
                <TableRow key={1}>
                  <TableCell>01</TableCell>
                  <TableCell>Exam Fee</TableCell>
                  <TableCell>400</TableCell>
                  <TableCell>
                    {' '}
                    <Paper
                      sx={{
                        display: 'flex',
                        justifyContent: 'center',
                        p: 0.5,
                        border: '1px solid green',
                        maxWidth: 85,
                        backgroundColor: '#f0fdf4',
                        color: 'green',
                      }}
                    >
                      {' '}
                      Passed
                    </Paper>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>
      <DialogActions> </DialogActions>
    </Box>
  );
};
