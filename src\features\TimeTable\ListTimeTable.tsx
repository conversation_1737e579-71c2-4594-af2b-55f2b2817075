/* eslint-disable jsx-a11y/alt-text */
import React, { FormEvent, useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  Button,
  Typography,
  Card,
  IconButton,
  Tooltip,
  Collapse,
  FormControl,
  MenuItem,
  Select,
  SelectChangeEvent,
  useTheme,
} from '@mui/material';
import styled from 'styled-components';
import DeleteIcon from '@mui/icons-material/Delete';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import Popup from '@/components/shared/Popup/Popup';
import { DAY_SELECT, SUBJECT_SELECT } from '@/config/Selection';
import { TIME_TABLE_DATA, TimeTableDataProps } from '@/config/TableData';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import { useAppSelector } from '@/hooks/useAppSelector';
import { getClassData, getYearData } from '@/config/storeSelectors';
import SearchIcon from '@mui/icons-material/Search';
import { IoIosArrowUp } from 'react-icons/io';
import { fetchClassList, fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import useSettings from '@/hooks/useSettings';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import useAuth from '@/hooks/useAuth';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import SaveIcon from '@mui/icons-material/Save';
import CloseIcon from '@mui/icons-material/Close';

const ListTimeTableRoot = styled.div`
  padding: 1rem;
  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 576px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 50px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid ${(props) => props.theme.palette.grey[200]};
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

function ListTimeTable() {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const dispatch = useAppDispatch();
  const { user } = useAuth();
  const adminId: number = user ? user.accountId : 0;
  const [Delete, setDelete] = React.useState(false);
  const [TimeTableData, setTimeTableData] = useState<TimeTableDataProps[] | undefined>([]);
  const [showFilter, setShowFilter] = useState(true);
  const [academicYearFilter, setAcademicYearFilter] = useState(0);
  const [classFilter, setClassFilter] = useState(0);
  const [dayFilter, setDayFilter] = useState<string>(DAY_SELECT[0]);
  const YearData = useAppSelector(getYearData);
  const classListData = useAppSelector(getClassData);
  const [editRowIds, setEditRowIds] = useState<number[]>([]);
  const [editedRows, setEditedRows] = useState<Record<number, Partial<TimeTableDataProps>>>({});
  const [originalRows, setOriginalRows] = useState<Record<number, TimeTableDataProps>>({});

  const handleClickDelete = () => setDelete(true);
  const handleClickCloseDelete = () => setDelete(false);

  React.useEffect(() => {
    dispatch(fetchYearList(adminId));
    dispatch(fetchClassList(adminId));
    const findData: TimeTableDataProps[] | undefined =
      TIME_TABLE_DATA?.length > 0
        ? TIME_TABLE_DATA.filter(
            (list) => list.academicYear === '2022-2023' && list.class === 'VIII-B' && list.day === dayFilter
          )
        : undefined;
    setTimeTableData(findData);
    console.log('filterdata', findData);
    // }
  }, [dispatch, adminId, academicYearFilter, classFilter, dayFilter]);

  const handleYearChange = (e: SelectChangeEvent) => {
    const selected = YearData.find((item: any) => item.accademicId === e.target.value);
    if (selected) {
      setAcademicYearFilter(selected.accademicId); // it's already a string
    }
  };
  const handleClassChange = (e: SelectChangeEvent) => {
    const selected = classListData.find((item: any) => item.classId === e.target.value);
    if (selected) {
      setClassFilter(selected.classId); // it's already a string
    }
  };

  const handleDayChange = (e: SelectChangeEvent) => {
    const selectedDay = e.target.value;
    if (DAY_SELECT.includes(selectedDay)) {
      setDayFilter(selectedDay);
    }
  };

  const handleReset = useCallback((e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    // loadOptionalFeeList(initialOptionalFeeRequest);
    setAcademicYearFilter(0);
    setClassFilter(0);
    setClassFilter(0);
    setDayFilter(DAY_SELECT[0]);
    // dispatch(fetchFeeDateSettings({ adminId, academicId: 10, sectionId: 0 }));
  }, []);

  const handleEditClick = (row: TimeTableDataProps) => {
    setEditRowIds((prev) => [...new Set([...prev, row.id])]);
    setEditedRows((prev) => ({ ...prev, [row.id]: { ...row } }));
    setOriginalRows((prev) => ({ ...prev, [row.id]: { ...row } }));
  };

  const handleFieldChange = (id: number, field: keyof TimeTableDataProps, value: any) => {
    setEditedRows((prev) => ({
      ...prev,
      [id]: { ...prev[id], [field]: value },
    }));
  };

  const handleUpdateRows = useCallback(() => {
    setTimeTableData((prev) =>
      prev?.map((row) => (editRowIds.includes(row.id) ? { ...row, ...(editedRows[row.id] || {}) } : row))
    );
    setEditRowIds([]);
    setEditedRows({});
    setOriginalRows({});
  }, [editRowIds, editedRows]);

  const handleCancelEdit = (id: number) => {
    setEditRowIds((prev) => prev.filter((rowId) => rowId !== id));
    setEditedRows((prev) => {
      const updated = { ...prev };
      delete updated[id];
      return updated;
    });
    setOriginalRows((prev) => {
      const updated = { ...prev };
      delete updated[id];
      return updated;
    });
  };
  const hasChanges = useMemo(() => {
    return Object.entries(editedRows).some(([id, edited]) => {
      const original = originalRows[Number(id)];
      if (!original) return false;
      return (
        edited.academicYear !== original.academicYear ||
        edited.class !== original.class ||
        edited.subject !== original.subject ||
        edited.day !== original.day ||
        edited.period !== original.period
      );
    });
  }, [originalRows, editedRows]);

  const timeTableListColumns: DataTableColumn<TimeTableDataProps>[] = useMemo(
    () => [
      {
        name: 'id',
        headerLabel: 'Sl No',
        renderCell: (row) => {
          return (
            <Typography width={30} fontSize={13} variant="subtitle1">
              {row.id}
            </Typography>
          );
        },
        sortable: true,
      },
      {
        name: 'academicYear',
        headerLabel: 'Academic Year',
        renderCell: (row) =>
          editRowIds.includes(row.id) ? (
            <Select
              sx={{ width: 130, height: 34 }}
              value={editedRows[row.id]?.academicYear || ''}
              onChange={(e) => handleFieldChange(row.id, 'academicYear', e.target.value)}
            >
              {YearData.map((opt) => (
                <MenuItem key={opt.accademicId} value={opt.accademicTime}>
                  {opt.accademicTime}
                </MenuItem>
              ))}
            </Select>
          ) : (
            <Typography width={130} fontSize={13} variant="subtitle1">
              {row.academicYear}
            </Typography>
          ),
      },
      {
        name: 'class',
        headerLabel: 'Class',
        renderCell: (row) =>
          editRowIds.includes(row.id) ? (
            <Select
              sx={{ width: 130, height: 34 }}
              value={editedRows[row.id]?.class || ''}
              onChange={(e) => handleFieldChange(row.id, 'class', e.target.value)}
            >
              {classListData.map((opt) => (
                <MenuItem key={opt.classId} value={opt.className}>
                  {opt.className}
                </MenuItem>
              ))}
            </Select>
          ) : (
            <Typography width={130} fontSize={13} variant="subtitle1">
              {row.class}
            </Typography>
          ),
      },
      {
        name: 'subject',
        headerLabel: 'Subject',
        renderCell: (row) =>
          editRowIds.includes(row.id) ? (
            <Select
              sx={{ width: 130, height: 34 }}
              value={editedRows[row.id]?.subject || ''}
              onChange={(e) => handleFieldChange(row.id, 'subject', e.target.value)}
            >
              {SUBJECT_SELECT.map((opt) => (
                <MenuItem key={opt} value={opt}>
                  {opt}
                </MenuItem>
              ))}
            </Select>
          ) : (
            <Typography width={130} fontSize={13} variant="subtitle2">
              {row.subject}
            </Typography>
          ),
      },
      {
        name: 'day',
        headerLabel: 'Day',
        renderCell: (row) =>
          editRowIds.includes(row.id) ? (
            <Select
              sx={{ width: 130, height: 34 }}
              value={editedRows[row.id]?.day || ''}
              onChange={(e) => handleFieldChange(row.id, 'day', e.target.value)}
            >
              {DAY_SELECT.map((opt) => (
                <MenuItem key={opt} value={opt}>
                  {opt}
                </MenuItem>
              ))}
            </Select>
          ) : (
            <Typography width={130} fontSize={13} variant="subtitle1">
              {row.day}
            </Typography>
          ),
      },
      {
        name: 'period',
        headerLabel: 'Period',
        renderCell: (row) =>
          editRowIds.includes(row.id) ? (
            <Select
              sx={{ width: 130, height: 34 }}
              value={editedRows[row.id]?.period || ''}
              onChange={(e) => handleFieldChange(row.id, 'period', e.target.value)}
            >
              {['1', '2', '3', '4', '5', '6', '7', '8'].map((opt) => (
                <MenuItem key={opt} value={opt}>
                  {opt}
                </MenuItem>
              ))}
            </Select>
          ) : (
            <Typography width={130} fontSize={13} variant="subtitle1">
              {row.period}
            </Typography>
          ),
      },
      {
        name: 'actions',
        headerLabel: 'Actions',
        renderCell: (row) => (
          <Stack direction="row" gap={1} width={80}>
            {editRowIds.includes(row.id) ? (
              <IconButton size="small" onClick={() => handleCancelEdit(row.id)}>
                <Tooltip title="Cancel Edit">
                  <CloseIcon />
                </Tooltip>
              </IconButton>
            ) : (
              <IconButton size="small" onClick={() => handleEditClick(row)}>
                <Tooltip title="Edit Row">
                  <ModeEditIcon />
                </Tooltip>
              </IconButton>
            )}
            <IconButton size="small" color="error">
              <Tooltip title="Delete Row">
                <DeleteIcon />
              </Tooltip>
            </IconButton>
            {editRowIds.includes(row.id) && (
              <IconButton disabled={!hasChanges} color="warning" size="small" onClick={handleUpdateRows}>
                <Tooltip title="Update Row">
                  <SaveIcon />
                </Tooltip>
              </IconButton>
            )}
          </Stack>
        ),
      },
    ],
    [YearData, editRowIds, editedRows, classListData, handleUpdateRows, hasChanges] // Add dependencies for useMemo
  );

  const getRowKey = useCallback((row: any) => row.id, []);

  return (
    <Page title="TimeTable List">
      <ListTimeTableRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17}>
              Timetable List
            </Typography>
            <Box pb={0.5} sx={{ flexShrink: 0 }}>
              {showFilter === false ? (
                <Tooltip title="Search">
                  <IconButton
                    aria-label="delete"
                    color="primary"
                    sx={{ mr: { xs: 0, sm: 1 } }}
                    onClick={() => setShowFilter((x) => !x)}
                  >
                    <SearchIcon />
                  </IconButton>
                </Tooltip>
              ) : (
                <IconButton
                  aria-label="delete"
                  color="primary"
                  sx={{ mr: { xs: 0, sm: 1 } }}
                  onClick={() => setShowFilter((x) => !x)}
                >
                  <IoIosArrowUp />
                </IconButton>
              )}
            </Box>
          </Stack>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate onReset={handleReset}>
                <Grid pb={2} pt={1} container spacing={2} alignItems="end">
                  <Grid item xxl={2} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Select
                        labelId="academicYearFilter"
                        id="academicYearFilterSelect"
                        value={academicYearFilter?.toString()}
                        onChange={handleYearChange}
                        placeholder="Select Year"
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Year
                        </MenuItem>
                        {YearData.map((opt) => (
                          <MenuItem key={opt.accademicId} value={opt.accademicId}>
                            {opt.accademicTime}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Select
                        labelId="classFilter"
                        id="classFilter"
                        value={classFilter?.toString()}
                        onChange={handleClassChange}
                        placeholder="Select Class"
                        sx={{
                          '& .MuiInputBase-input.Mui-disabled': {
                            backgroundColor: theme.palette.grey[200],
                          },
                        }}
                        MenuProps={{
                          PaperProps: {
                            style: {
                              maxHeight: '270px', // Adjust the value to your desired height
                            },
                          },
                        }}
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Class
                        </MenuItem>
                        {classListData.map((opt) => (
                          <MenuItem key={opt.classId} value={opt.classId}>
                            {opt.className}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xxl={2} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Select
                        labelId="dayFilter"
                        id="dayFilter"
                        value={dayFilter?.toString()}
                        onChange={handleDayChange}
                        placeholder="Select Class"
                        sx={{
                          '& .MuiInputBase-input.Mui-disabled': {
                            backgroundColor: theme.palette.grey[200],
                          },
                        }}
                        MenuProps={{
                          PaperProps: {
                            style: {
                              maxHeight: '270px', // Adjust the value to your desired height
                            },
                          },
                        }}
                      >
                        <MenuItem value={DAY_SELECT[0]} sx={{ display: 'none' }}>
                          Select Day
                        </MenuItem>
                        {DAY_SELECT.map((opt) => (
                          <MenuItem key={opt} value={opt}>
                            {opt}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto">
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button type="reset" variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Paper
              className="card-table-container"
              sx={{
                border: `1px solid #e8e8e9`,
                width: '100%',
                overflow: 'auto',
                marginTop: '12px',
              }}
            >
              <DataTable
                // disabledCheckBox={studentsData.map((m) => m.feeMapped?.length === 0)}
                hoverDisable={false}
                tableStyles={{ minWidth: { xs: '1100px' } }}
                showHorizontalScroll
                columns={timeTableListColumns}
                data={TimeTableData}
                getRowKey={getRowKey}
                // fetchStatus={optionalFeeSettingListStatus}
                fetchStatus="success"
              />
            </Paper>
          </div>
        </Card>
        <Popup
          size="xs"
          state={Delete}
          onClose={handleClickCloseDelete}
          popupContent={<DeleteMessage message="Do you want delete from the list?" />}
        />
      </ListTimeTableRoot>
    </Page>
  );
}

export default ListTimeTable;
