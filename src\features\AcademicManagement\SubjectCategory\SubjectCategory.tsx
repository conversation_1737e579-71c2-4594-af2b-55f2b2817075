/* eslint-disable react/self-closing-comp */
/* eslint-disable prettier/prettier */
/* eslint-disable jsx-a11y/alt-text */
import React, { useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Typography,
  Card,
  IconButton,
  Collapse,
  FormControl,
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import styled from 'styled-components';
import { smsrows } from '@/config/smsData';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import DeleteIcon from '@mui/icons-material/Delete';
import Popup from '@/components/shared/Popup/Popup';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import { MdAdd } from 'react-icons/md';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import { Create } from './Create&Edit';

const SubjectCategoryRoot = styled.div`
  padding: 1rem;
  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
    }
  }
`;

function SubjectCategory() {
  const [showFilter, setShowFilter] = useState(false);

  const [Delete, setDelete] = React.useState(false);
  const [drawerOpen, setDrawerOpen] = React.useState<boolean>(false);
  const [popup, setPopup] = React.useState(false);

  const handleClickDelete = () => setDelete(true);
  const handleClickCloseDelete = () => setDelete(false);

  const toggleDrawerOpen = () => {
    setDrawerOpen(true);
  };

  const toggleDrawerClose = () => setDrawerOpen(false);

  const handleClickOpen = () => {
    setDrawerOpen(false);
    setPopup(true);
  };

  const handleClickClose = () => setPopup(false);
  return (
    <Page title="Manage Year">
      <SubjectCategoryRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17} width="80%">
              Subject Category List
            </Typography>
            <Box pb={1} sx={{ flexShrink: 0 }}>
              <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                <SearchIcon />
              </IconButton>
              <Button sx={{ borderRadius: '20px' }} variant="outlined" size="small" onClick={toggleDrawerOpen}>
                <MdAdd size="20px" /> Create
              </Button>
            </Box>
          </Stack>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={2} alignItems="end">
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 300 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Subject Category
                      </Typography>
                      <TextField
                        sx={{ minWidth: { xs: '100%', sm: 300 } }}
                        fullWidth
                        placeholder="Enter Subject Category"
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      {/* <Button type="submit" variant="contained" color="primary" fullWidth>
                        Search
                      </Button> */}
                      <Button type="reset" variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>
          </div>
          <Box>
            <Paper
              sx={{
                marginTop: '12px',
                border: `1px solid #e8e8e9`,
                width: '100%',
                overflow: 'auto',
                '&::-webkit-scrollbar': {
                  width: 0,
                },
              }}
            >
              <TableContainer
                sx={{
                  width: { xs: '700px', md: '100%' },
                }}
              >
                <Table stickyHeader aria-label="simple table">
                  <TableHead>
                    <TableRow>
                      <TableCell>Sl.No</TableCell>
                      <TableCell>Subject Category</TableCell>
                      <TableCell>Action</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {smsrows.map((lnrow) => (
                      <TableRow hover key={lnrow.rollNo}>
                        <TableCell>{lnrow.rollNo}</TableCell>
                        <TableCell>Category 1</TableCell>
                        <TableCell>
                          <Stack direction="row" gap={1}>
                            <IconButton size="small" onClick={toggleDrawerOpen}>
                              <ModeEditIcon />
                            </IconButton>
                            <IconButton onClick={handleClickDelete} color="error" size="small">
                              <DeleteIcon />
                            </IconButton>
                          </Stack>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Paper>
          </Box>
        </Card>
      </SubjectCategoryRoot>
      <Popup
        size="xs"
        state={Delete}
        onClose={handleClickCloseDelete}
        popupContent={<DeleteMessage message="Do you want delete from the list?" />}
      />
      <Popup
        size="xs"
        state={popup}
        onClose={handleClickClose}
        popupContent={<SuccessMessage message=" Saved Successfully" />}
      />
      <TemporaryDrawer
        onClose={toggleDrawerClose}
        Title="Create"
        state={drawerOpen}
        DrawerContent={<Create open={handleClickOpen} onClose={toggleDrawerClose} />}
      />
    </Page>
  );
}

export default SubjectCategory;
