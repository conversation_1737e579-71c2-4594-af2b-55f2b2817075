import React, { useState } from 'react';
import { <PERSON>rid, <PERSON><PERSON><PERSON>, TextField, Button, Stack } from '@mui/material';

const CreateTC = ({ student, onCancel }: any) => {
  const [editedStudent, setEditedStudent] = useState(student);

  const handleChange = (e: any) => {
    const { name, value } = e.target;
    setEditedStudent((prevStudent: any) => ({
      ...prevStudent,
      [name]: value,
    }));
  };

  // const handleUpdate = () => {
  //   onUpdate(editedStudent.id, editedStudent);
  // };

  return (
    <Grid container spacing={2} m={4} maxWidth="90%">
      <Grid item xs={12}>
        <Typography variant="h6" mb={2}>
          Create Transfer Certificate
        </Typography>
      </Grid>
      <Grid item md={3} xs={6}>
        <TextField
          name="admissionNo"
          label="Adm No"
          variant="outlined"
          value={editedStudent.admissionNumber}
          onChange={handleChange}
          fullWidth
        />
      </Grid>
      <Grid item md={3} xs={6}>
        <TextField
          name="rollNo"
          label="Roll No"
          variant="outlined"
          value={editedStudent.slNo}
          onChange={handleChange}
          fullWidth
        />
      </Grid>
      <Grid item md={6} xs={12}>
        <TextField
          name="name"
          label="Student Name"
          variant="outlined"
          value={editedStudent.studentName}
          onChange={handleChange}
          fullWidth
        />
      </Grid>
      <Grid item md={3} xs={6}>
        <TextField
          name="class"
          label="Class"
          variant="outlined"
          value={editedStudent.class}
          onChange={handleChange}
          fullWidth
        />
      </Grid>
      <Grid item md={3} xs={6}>
        <TextField
          name="academic"
          label="Academic"
          variant="outlined"
          value={editedStudent.academicYear}
          onChange={handleChange}
          fullWidth
        />
      </Grid>
      <Grid item md={3} xs={6}>
        <TextField
          name="gender"
          label="Gender"
          variant="outlined"
          value={editedStudent.gender}
          onChange={handleChange}
          fullWidth
        />
      </Grid>
      <Grid item md={3} xs={6}>
        <TextField
          name="bloodGroup"
          label="Blood Group"
          variant="outlined"
          value={editedStudent.bloodGroup}
          onChange={handleChange}
          fullWidth
        />
      </Grid>
      <Grid item md={3} xs={6}>
        <TextField
          name="dateOfBirth"
          label="Date of Birth"
          variant="outlined"
          value={editedStudent.DOB}
          onChange={handleChange}
          fullWidth
        />
      </Grid>
      <Grid item md={3} xs={6}>
        <TextField
          name="houseGroup"
          label="House Group"
          variant="outlined"
          value={editedStudent.houseGroup}
          onChange={handleChange}
          fullWidth
        />
      </Grid>
      <Grid item md={6} xs={12}>
        <TextField
          name="aadharNo"
          label="Aadhar No."
          variant="outlined"
          value={editedStudent.aadharNo}
          onChange={handleChange}
          fullWidth
        />
      </Grid>
      <Grid item md={6} xs={12}>
        <TextField
          name="guardianName"
          label="Guardian Name"
          variant="outlined"
          value={editedStudent.parentName}
          onChange={handleChange}
          fullWidth
        />
      </Grid>
      <Grid item md={6} xs={12}>
        <TextField
          name="guardianNumber"
          label="Guardian Number"
          variant="outlined"
          value={editedStudent.guardianNo}
          onChange={handleChange}
          fullWidth
        />
      </Grid>
      <Grid item md={6} xs={12}>
        <TextField
          name="caste"
          label="Caste"
          variant="outlined"
          value={editedStudent.caste}
          onChange={handleChange}
          fullWidth
        />
      </Grid>
      <Grid item md={6} xs={12}>
        <TextField
          name="religion"
          label="Religion"
          variant="outlined"
          value={editedStudent.religion}
          onChange={handleChange}
          fullWidth
        />
      </Grid>
      <Grid item md={6} xs={12}>
        <TextField
          name="busNo"
          label="Bus Route"
          variant="outlined"
          value={editedStudent.busNo}
          onChange={handleChange}
          fullWidth
        />
      </Grid>
      <Grid item md={6} xs={12}>
        <TextField
          name="district"
          label="District"
          variant="outlined"
          value={editedStudent.district}
          onChange={handleChange}
          fullWidth
        />
      </Grid>
      <Grid item md={6} xs={12}>
        <TextField
          name="taluka"
          label="Taluka"
          variant="outlined"
          value={editedStudent.taluka}
          onChange={handleChange}
          fullWidth
        />
      </Grid>
      <Grid item md={6} xs={12}>
        <TextField
          name="state"
          label="State"
          variant="outlined"
          value={editedStudent.state}
          onChange={handleChange}
          fullWidth
        />
      </Grid>
      <Grid item xs={12}>
        <Stack justifyContent="end" direction="row" spacing={2}>
          <Button variant="contained" color="secondary" onClick={onCancel}>
            Cancel
          </Button>
          <Button variant="contained" color="primary" onClick={onCancel}>
            Save
          </Button>
        </Stack>
      </Grid>
    </Grid>
  );
};

export default CreateTC;
