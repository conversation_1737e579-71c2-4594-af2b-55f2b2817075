/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import { Autocomplete, Divider, Grid, Stack, TextField, Button, Typography, Box } from '@mui/material';
import styled from 'styled-components';
import { CLASS_SELECT, YEAR_SELECT } from '@/config/Selection';

const NewMaterialRoot = styled.div`
  width: 100%;
  padding: 0.5rem 1.5rem;
`;
type Props = {
  onClick: () => void;
};
function NewMaterial({ onClick }: Props) {
  return (
    <NewMaterialRoot>
      <Box>
        <Typography variant="h6" fontSize={17}>
          New Study Material
        </Typography>
        <Divider />
        <Grid pb={4} pt={2} container spacing={3}>
          <Grid item lg={6} xs={12}>
            <Grid item xs={12}>
              <Typography variant="h6" fontSize={14}>
                Academic Year
              </Typography>
              <Autocomplete
                options={YEAR_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select" />}
              />
            </Grid>
            <Grid item xs={12}>
              <Typography variant="h6" fontSize={14}>
                Class
              </Typography>
              <Autocomplete
                options={CLASS_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select" />}
              />
            </Grid>
            <Grid item xs={12}>
              <Typography variant="h6" fontSize={14}>
                Subject
              </Typography>
              <Autocomplete
                options={YEAR_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select" />}
              />
            </Grid>
            <Grid item xs={12}>
              <Typography variant="h6" fontSize={14}>
                Material Title
              </Typography>
              <TextField fullWidth placeholder="Enter Title" />
            </Grid>
            <Grid item xs={12}>
              <Typography variant="h6" fontSize={14}>
                Material Description
              </Typography>
              <TextField fullWidth multiline minRows={2} placeholder="Enter Description" />
            </Grid>
          </Grid>
          <Grid item lg={6} xs={12}>
            <Grid item xs={12}>
              <Typography variant="h6" fontSize={14}>
                File Name
              </Typography>
              <TextField fullWidth placeholder="Enter Name" sx={{ mb: 1 }} />
              <input accept="*" type="file" />
            </Grid>
            <Grid item xs={12}>
              <Typography variant="h6" fontSize={14}>
                File Name
              </Typography>
              <TextField fullWidth placeholder="Enter Name" sx={{ mb: 1 }} />
              <input accept="*" type="file" />
            </Grid>
            <Grid item xs={12}>
              <Typography variant="h6" fontSize={14}>
                File Name
              </Typography>
              <TextField fullWidth placeholder="Enter Name" sx={{ mb: 1 }} />
              <input accept="*" type="file" />
            </Grid>
            <Stack
              spacing={2}
              direction="row"
              sx={{ pt: { xs: 3.5, md: 3.5 }, ml: 'auto', width: { xs: '95%', md: '100%' } }}
            >
              <Button variant="contained" color="secondary" fullWidth>
                Cancel
              </Button>
              <Button onClick={onClick} variant="contained" color="primary" fullWidth>
                Save
              </Button>
            </Stack>
          </Grid>
        </Grid>
      </Box>
    </NewMaterialRoot>
  );
}

export default NewMaterial;
