/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  Collapse,
  FormControl,
  Chip,
  useTheme,
} from '@mui/material';
import styled from 'styled-components';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import SearchIcon from '@mui/icons-material/Search';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { YEAR_SELECT } from '@/config/Selection';
import DeleteIcon from '@mui/icons-material/Delete';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import { TbPhoto } from 'react-icons/tb';
import { BsPlayCircle } from 'react-icons/bs';
import { MdAdd } from 'react-icons/md';
import { bluePreset, redPreset, rosePreset } from '@/utils/Colors';
import { AlbumListInfo } from '@/types/Album';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import CreateAlbumForm from './CreateAlbum';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';

const AlbumListRoot = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      height: calc(100% - 40px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

const DefaultAlbumListInfo: AlbumListInfo = {
  albumId: 0,
  albumName: '',
  accademicYear: '2023-2024',
  albumDescription: '',
  albumType: 1,
};
function AlbumList() {
  const theme = useTheme();
  const { confirm } = useConfirm();
  const [alubumDetail, setAlubumDetail] = useState<AlbumListInfo>(DefaultAlbumListInfo);
  const [drawerOpen, setDrawerOpen] = React.useState<boolean>(false);
  const [showFilter, setShowFilter] = useState(false);
  const [albumDatas, setAlbumDatas] = useState<AlbumListInfo[]>([]);

  const handleOpen = () => {
    setAlubumDetail(DefaultAlbumListInfo);
    setDrawerOpen(true);
  };

  const toggleDrawerClose = useCallback(() => setDrawerOpen(false), []);

  useEffect(() => {
    try {
      const existingArray = JSON.parse(localStorage.getItem('myDummyAlbumNameListArray')) || [];
      setAlbumDatas(existingArray);
    } catch (error) {
      // Handle error if JSON parsing fails or localStorage.getItem returns null
      console.error('Error retrieving data from localStorage:', error);
      setAlbumDatas([]);
    }
  }, [albumDatas]);

  const handleSaveorEdit = useCallback(
    async (values: AlbumListInfo, mode: 'create' | 'edit') => {
      if (mode === 'create') {
        const { albumId, ...rest } = values;

        toggleDrawerClose();
        const successMessage = <SuccessMessage message="Album created successfully" />;
        await confirm(successMessage, 'Album created', { okLabel: 'Ok', showOnlyOk: true });

        const existingArray = JSON.parse(localStorage.getItem('myDummyAlbumNameListArray')) || [];
        const newEntry = {
          albumId: Math.floor(Math.random() * 100000),
          accademicYear: values.accademicYear,
          albumName: values.albumName,
          albumDescription: values.albumDescription,
          albumType: values.albumType,
        };
        existingArray.push(newEntry);
        localStorage.setItem('myDummyAlbumNameListArray', JSON.stringify(existingArray));
      } else if (mode === 'edit') {
        const { albumId, ...rest } = values;

        toggleDrawerClose();
        const successMessage = <SuccessMessage message="Album updated successfully" />;
        await confirm(successMessage, 'Album updated', { okLabel: 'Ok', showOnlyOk: true });
        const existingArray = JSON.parse(localStorage.getItem('myDummyAlbumNameListArray')) || [];
        const updatedArray = existingArray.map((entry: AlbumListInfo) => {
          if (entry.albumId === albumId) {
            return {
              ...entry,
              accademicYear: values.accademicYear,
              albumName: values.albumName,
              albumDescription: values.albumDescription,
              albumType: values.albumType,
            };
          }
          return entry;
        });
        localStorage.setItem('myDummyAlbumNameListArray', JSON.stringify(updatedArray));
      }
    },
    [confirm]
  );

  const handleEditAlbum = useCallback((classObj: AlbumListInfo) => {
    setAlubumDetail(classObj);
    setDrawerOpen(true);
  }, []);

  const handleDeleteAlbum = useCallback(
    async (classObj: AlbumListInfo) => {
      const deleteConfirmMessage = (
        <DeleteMessage
          message={
            <div>
              Are you sure you want to delete the class <br />
              &quot;{classObj.className}&quot; ?
            </div>
          }
        />
      );
      if (await confirm(deleteConfirmMessage, 'Delete Class?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
        const deleteResponse = await dispatch(deleteClass(classObj.classId)).unwrap();
        if (deleteResponse.deleted) {
          const deleteDoneMessage = <DeleteMessage message="Class deleted successfully." />;
          await confirm(deleteDoneMessage, 'Deleted', { okLabel: 'Ok', showOnlyOk: true });
          let pageNumberToMove = pagenumber;
          if (paginationInfo.remainingpages === 0 && pagenumber > 1 && currentItemCount === 1) {
            pageNumberToMove = pagenumber - 1;
          }
          loadClassList({ ...currentClassListRequest, pageNumber: pageNumberToMove });
        }
      }
    },
    [confirm]
  );

  const getRowKey = useCallback((row: any) => row.examId, []);
  const albumListColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'albumId',
        dataKey: 'albumId',
        headerLabel: 'Sl.No',
        sortable: true,
      },
      {
        name: 'albumName',
        dataKey: 'albumName',
        headerLabel: 'Album Name',
      },
      {
        name: 'albumDescription',
        dataKey: 'albumDescription',
        headerLabel: 'Description',
      },
      {
        name: 'albumType',
        dataKey: 'albumType',
        headerLabel: 'Album Type',
        renderCell: (row) => {
          return (
            <Chip
              icon={
                row.albumType === 1 ? (
                  <TbPhoto color={rosePreset.main} fontSize={20} />
                ) : (
                  <BsPlayCircle color={bluePreset.main} fontSize={20} />
                )
              }
              label={row.albumType === 1 ? 'Image' : 'Video'}
              sx={{
                px: 1,
                color: row.albumType === 1 ? rosePreset.main : bluePreset.main,
                fontWeight: 'bold',
                backgroundColor: row.title === 1 ? rosePreset.lighter : bluePreset.lighter,
              }}
            />
          );
        },
      },
      {
        name: 'actions',
        headerLabel: 'Actions',
        renderCell: (row) => {
          return (
            <Stack direction="row" gap={2}>
              <IconButton size="small" onClick={() => handleEditAlbum(row)} sx={{ padding: 0.5 }}>
                <ModeEditIcon />
              </IconButton>
              <IconButton size="small" color="error" onClick={() => handleDeleteAlbum(row)} sx={{ padding: 0.5 }}>
                <DeleteIcon />
              </IconButton>
              <Button
                onClick={handleOpen}
                sx={{ borderRadius: '20px', backgroundColor: theme.palette.primary.lighter }}
                variant="outlined"
                size="small"
              >
                <MdAdd size="20px" /> Add
              </Button>
            </Stack>
          );
        },
      },
    ],
    []
  );
  return (
    <Page title="List">
      <AlbumListRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17}>
              Album Name List
            </Typography>
            <Box pb={1} sx={{ flexShrink: 0 }}>
              <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                <SearchIcon />
              </IconButton>
              <Button onClick={handleOpen} sx={{ borderRadius: '20px' }} variant="outlined" size="small">
                <MdAdd size="20px" /> Create
              </Button>
            </Box>
          </Stack>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={4} pt={1} container spacing={3} alignItems="end">
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 250 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Autocomplete
                        options={YEAR_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select Year" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 250 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Album Name
                      </Typography>
                      <TextField placeholder="Enter Name" />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 250 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Album Type
                      </Typography>
                      <Autocomplete
                        options={['Image', 'Video']}
                        renderInput={(params) => <TextField {...params} placeholder="Select Type" />}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item lg="auto" xs={12} sm={6}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Paper className="card-table-container" sx={{ marginTop: '12px' }}>
              <DataTable columns={albumListColumns} data={albumDatas} getRowKey={getRowKey} fetchStatus="success" />
            </Paper>
          </div>
          {/* <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 2 }}>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="secondary">
                Cancel
              </Button>
              <Button onClick={handleOpen} variant="contained" color="primary">
                Save
              </Button>
            </Stack>
          </Box> */}
        </Card>
      </AlbumListRoot>

      <TemporaryDrawer
        onClose={toggleDrawerClose}
        Title="Upload Details"
        state={drawerOpen}
        DrawerContent={
          <CreateAlbumForm
            onSave={handleSaveorEdit}
            albumDetail={alubumDetail}
            onCancel={toggleDrawerClose}
            onClose={toggleDrawerClose}
            // isSubmitting={isSubmitting}
          />
        }
      />
    </Page>
  );
}

export default AlbumList;
