/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  Collapse,
  FormControl,
} from '@mui/material';
import styled from 'styled-components';
import SearchIcon from '@mui/icons-material/Search';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { STATUS_SELECT } from '@/config/Selection';
import DeleteIcon from '@mui/icons-material/Delete';
import ModeEditIcon from '@mui/icons-material/ModeEdit';

const MembersListRoot = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      height: calc(100% - 45px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;
const dummyData = [
  {
    SlNo: 1,
    Name: 'Manohar',
    Role: 'President',
    Mobile: '+91-9546845701',
    Email: '<EMAIL>',
    Status: 'Publish',
  },
  {
    SlNo: 2,
    Name: 'Hemal',
    Role: 'Vice President',
    Mobile: '+91-8675670235',
    Email: '<EMAIL>',
    Status: 'Publish',
  },
  {
    SlNo: 3,
    Name: 'Rishabh',
    Role: 'Secretary',
    Mobile: '+91-9072564723',
    Email: '<EMAIL>',
    Status: 'Publish',
  },
  {
    SlNo: 4,
    Name: 'Shresthi',
    Role: 'Joint Secretary',
    Mobile: '+91-**********',
    Email: '<EMAIL>',
    Status: 'Publish',
  },
  {
    SlNo: 5,
    Name: 'Binod',
    Role: 'Cashier',
    Mobile: '+91-**********',
    Email: '<EMAIL>',
    Status: 'Publish',
  },
  {
    SlNo: 6,
    Name: 'Chanakya',
    Role: 'Member',
    Mobile: '+91-**********',
    Email: '<EMAIL>',
    Status: 'Publish',
  },
  {
    SlNo: 7,
    Name: 'Aarin',
    Role: 'Accountant',
    Mobile: '+91-**********',
    Email: '<EMAIL>',
    Status: 'Publish',
  },
  {
    SlNo: 8,
    Name: 'Jaivardhan',
    Role: 'Staff',
    Mobile: '+91-**********',
    Email: '<EMAIL>',
    Status: 'Publish',
  },
];
function MembersList() {
  const [showFilter, setShowFilter] = useState(false);

  const getRowKey = useCallback((row: any) => row.examId, []);
  const MembersListColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'slNo',
        dataKey: 'SlNo',
        headerLabel: 'Sl.No',
        sortable: true,
      },
      {
        name: 'name',
        dataKey: 'Name',
        headerLabel: 'Name',
      },
      {
        name: 'role',
        dataKey: 'Role',
        headerLabel: 'Role',
      },
      {
        name: 'mobile',
        dataKey: 'Mobile',
        headerLabel: 'Mobile',
      },
      {
        name: 'mail',
        dataKey: 'Email',
        headerLabel: 'Email',
      },
      {
        name: 'status',
        dataKey: 'Status',
        headerLabel: 'Status',
      },
      {
        name: 'actions',
        headerLabel: 'Actions',
        renderCell: () => {
          return (
            <Stack direction="row" gap={1}>
              <IconButton size="small" sx={{ padding: 0.5 }}>
                <ModeEditIcon />
              </IconButton>
              <IconButton size="small" color="error" sx={{ padding: 0.5 }}>
                <DeleteIcon />
              </IconButton>
            </Stack>
          );
        },
      },
    ],
    []
  );

  return (
    <Page title="Fee Alert Pending">
      <MembersListRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17}>
              Members List
            </Typography>
            <Box pb={1} sx={{ flexShrink: 0 }}>
              <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                <SearchIcon />
              </IconButton>
            </Box>
          </Stack>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={3} alignItems="end">
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 180 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Name
                      </Typography>
                      <TextField placeholder="Enter Name" />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 180 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Mobile
                      </Typography>
                      <TextField placeholder="Enter No." />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 180 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Role
                      </Typography>
                      <Autocomplete
                        options={[]}
                        renderInput={(params) => <TextField {...params} placeholder="Select Fee" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 180 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Status
                      </Typography>
                      <Autocomplete
                        options={STATUS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select Fee" />}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item lg="auto" xs={12} sm={6}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Paper className="card-table-container" sx={{ marginTop: '12px' }}>
              <DataTable columns={MembersListColumns} data={dummyData} getRowKey={getRowKey} fetchStatus="success" />
            </Paper>
          </div>
        </Card>
      </MembersListRoot>
    </Page>
  );
}

export default MembersList;
