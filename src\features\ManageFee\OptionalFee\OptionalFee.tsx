/* eslint-disable jsx-a11y/alt-text */
import React, { useState } from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Typography,
  Card,
  Avatar,
} from '@mui/material';
import styled from 'styled-components';
import { CLASS_SELECT, YEAR_SELECT } from '@/config/Selection';
import { StudentInfoData } from '@/config/StudentDetails';
import AddIcon from '@mui/icons-material/Add';
import DoneIcon from '@mui/icons-material/Done';

const OptionalFeeRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 16px;
`;

export default function OptionalFee() {
  const [done, setDone] = useState(false);
  const [done2, setDone2] = useState(false);

  const handleChange = () => {
    setDone(!done);
  };
  const handleChange2 = () => {
    setDone2(!done);
  };
  return (
    <Page title="Fees Collection">
      <OptionalFeeRoot>
        <Card elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Typography variant="h6" fontSize={17}>
            Fee Collection
          </Typography>
          <Divider />
          <Grid pb={4} pt={2} container spacing={3}>
            <Grid item lg={4} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Select Class
              </Typography>
              <Autocomplete
                options={CLASS_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select Class" />}
              />
            </Grid>
            <Grid item lg={4} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Accademic Year
              </Typography>
              <Autocomplete
                options={YEAR_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select Year" />}
              />
            </Grid>
            <Grid item lg={4} xs={12}>
              <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                <Button variant="contained" color="secondary" fullWidth>
                  Reset
                </Button>
                <Button variant="contained" color="primary" fullWidth>
                  Search
                </Button>
              </Stack>
            </Grid>
          </Grid>

          <Box>
            <Paper
              sx={{
                border: `.0625rem solid #e8e8e9`,
                width: '100%',
                overflow: 'auto',
                '&::-webkit-scrollbar': {
                  width: 0,
                },
              }}
            >
              <TableContainer
                sx={{
                  maxHeight: 410,
                  width: { xs: '43.75rem', md: '100%' },
                  '&::-webkit-scrollbar': {
                    width: 0,
                  },
                }}
              >
                <Table stickyHeader aria-label="simple table">
                  <TableHead>
                    <TableRow>
                      <TableCell>Adm.No</TableCell>
                      <TableCell>Student</TableCell>
                      <TableCell align="center">New Admission Fee</TableCell>
                      <TableCell align="center">Admission Form Fee</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {StudentInfoData.map((row) => (
                      <TableRow hover key={row.rollNo}>
                        <TableCell>{row.admissionNo}</TableCell>
                        <TableCell>
                          <Stack direction="row">
                            <Avatar src={row.image} sx={{ mr: 2 }} />
                            <Typography pt={0.7} fontSize={15}>
                              {row.name}
                            </Typography>
                          </Stack>
                        </TableCell>
                        <TableCell align="center">
                          <Button variant="outlined" size="small" onClick={handleChange}>
                            {done ? <DoneIcon /> : <AddIcon />}
                          </Button>
                        </TableCell>
                        <TableCell align="center">
                          <Button variant="outlined" size="small" onClick={handleChange2}>
                            {done2 ? <DoneIcon /> : <AddIcon />}
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Paper>
          </Box>
        </Card>
      </OptionalFeeRoot>
      {/* <TemporaryDrawer
        onClose={toggleDrawerClose}
        state={drawerOpen}
        DrawerContent={<EditList onClose={toggleDrawerClose} open={handleClickOpen} />}
      /> */}
      {/* <Popup size="md" state={popup} onClose={handleClickClose} popupContent={<EditedTable />} />
      <Popup
        size="xs"
        state={Delete}
        onClose={handleClickCloseDelete}
        popupContent={<DeleteMessage message="Are you sure want to delete?" />}
      /> */}
    </Page>
  );
}
