import { MessagesTypeProps } from '@/types/Common';
import { Box, Stack, Typography } from '@mui/material';
import Lottie from 'lottie-react';

export const DeleteMessage = ({ message, icon, jsonIcon, loop }: MessagesTypeProps) => {
  return (
    <Stack sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
      <Box
        sx={{
          // backgroundColor: '#F4F6F8',
          // p: 2,
          borderRadius: '50%',
          height: 80,
          width: 80,
          display: 'flex',
          justifyContent: 'center',
        }}
      >
        {icon ? (
          <img style={{ borderRadius: '10%' }} src={icon} alt={icon} />
        ) : (
          <Lottie animationData={jsonIcon} loop={loop} style={{ width: '200px' }} />
        )}
      </Box>
      <Typography variant="subtitle1" textAlign="center" color="secondary">
        {message}
      </Typography>
    </Stack>
  );
};
