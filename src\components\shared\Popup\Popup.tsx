import * as React from 'react';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons-material/Close';
import { Box, Dialog, Typography } from '@mui/material';
import styled from 'styled-components';

export interface DialogTitleProps {
  id: string;
  children?: React.ReactNode;
  onClose: (() => void) | undefined;
  disabled?: boolean;
}
const BootstrapDialog = styled(Dialog)(({ theme }) => ({
  '& .MuiDialogContent-root': {
    padding: theme.spacing(0, 0, 3, 0),
  },
  '& .MuiDialogActions-root': {
    padding: theme.spacing(1),
  },
  '& .close-button:hover': {
    // transition: '100ms',
    // transform: 'scale(1.1)',
  },
  // '& .MuiDialogTitle-root': {
  //   padding: theme.spacing(1, 3, 1, 3),
  // },
}));
export function BootstrapDialogTitle(props: DialogTitleProps) {
  const { children, onClose, disabled, ...other } = props;
  return (
    <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }} {...other}>
      <Typography variant="h6" fontSize={16}>
        {children}
      </Typography>
      {onClose ? (
        <IconButton
          disabled={disabled}
          className="close-button"
          aria-label="close"
          onClick={onClose}
          sx={{
            position: 'absolute',
            right: 10,
            top: 10,
            zIndex: 11,
            color: (theme) => theme.palette.grey[500],
          }}
        >
          <CloseIcon />
        </IconButton>
      ) : null}
    </DialogTitle>
  );
}

export type PopupProps = {
  popupContent?: React.ReactNode;
  state: boolean;
  onClose?: () => void;
  size?: any;
  title?: string;
  disabled?: boolean;
};

export default function Popup({ state, onClose, popupContent, size, title, disabled }: PopupProps) {
  return (
    <div>
      <BootstrapDialog
        // onClose={onClose}
        fullWidth
        maxWidth={size}
        aria-labelledby="customized-dialog-title"
        open={state}
      >
        <Box>
          <BootstrapDialogTitle id="customized-dialog-title" onClose={onClose} disabled={disabled}>
            {title ?? title}
            {/* <Divider sx={{ mt: 1, width: '100%' }} /> */}
          </BootstrapDialogTitle>
        </Box>

        <DialogContent dividers>{popupContent}</DialogContent>
      </BootstrapDialog>
    </div>
  );
}
