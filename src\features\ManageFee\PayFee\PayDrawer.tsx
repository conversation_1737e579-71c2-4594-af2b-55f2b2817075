import {
  Box,
  Table,
  TableBody,
  Table<PERSON>ell,
  Table<PERSON>ontainer,
  <PERSON>po<PERSON>,
  TableHead,
  TableRow,
  Button,
  Stack,
} from '@mui/material';
import React from 'react';
import SuccessPaid from '@/assets/ManageFee/successPaid.svg';

type PayPrps = {
  handleClickReceipt1: () => void;
  handleClickReceipt2: () => void;
};
export const Pay = ({ handleClickReceipt1, handleClickReceipt2 }: PayPrps) => {
  return (
    <Box>
      <Box flexDirection="column" display="flex" alignItems="center" mb={3}>
        <img src={SuccessPaid} alt="" width={100} />
        <Typography variant="body1" color="secondary" mt={2}>
          Fees Paid Successfully
        </Typography>
      </Box>
      <TableContainer>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Fee Details</TableCell>
              <TableCell align="right">Amount (INR)</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {[1, 2, 3, 4, 5, 6, 7]?.map((item) => (
              <TableRow key={item}>
                <TableCell color="secondary"> PTA</TableCell>
                <TableCell align="right"> 800</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      <Box sx={{ borderTop: '2px dashed gray' }} p={3} display="flex" justifyContent="space-between">
        <Typography variant="body2" color="secondary">
          Total Amount Paid
        </Typography>
        <Typography variant="body2" fontWeight={700}>
          3,500
        </Typography>
      </Box>

      <Box sx={{ justifyContent: 'center', pt: 3, position: 'relative' }}>
        <Stack spacing={2} direction="row" sx={{}}>
          <Button
            fullWidth
            variant="contained"
            color="secondary"
            sx={{ backgroundColor: '#f1f2f3', color: 'black', fontWeight: 600 }}
            size="medium"
            onClick={handleClickReceipt1}
          >
            Get Receipt
          </Button>
          <Button
            onClick={handleClickReceipt2}
            //   onKeyDown={toggleDrawer(false)}
            fullWidth
            variant="contained"
            color="secondary"
            size="medium"
            sx={{ backgroundColor: '#f1f2f3', color: 'black', fontWeight: 600 }}
          >
            Detailed Receipt
          </Button>
        </Stack>
      </Box>
    </Box>
  );
};
