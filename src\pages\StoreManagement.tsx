import React from 'react';
import { Link, useLocation, useOutletContext } from 'react-router-dom';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';
import styled from 'styled-components';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import Page from '@/components/shared/Page';
import ManageProducts from '@/features/StoreManagement/ManageProducts';
import MapProducts from '@/features/StoreManagement/MapProducts';
import ProductSale from '@/features/StoreManagement/ProductSale';
import SaleList from '@/features/StoreManagement/SaleList';
import MappedList from '@/features/StoreManagement/MappedList';
import DailyReport from '@/features/StoreManagement/DailyReport';
import { useDispatch } from 'react-redux';
import { flushStore } from '@/store/flush.slice';
import { OutletContextType } from '@/types/Common';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const StoreManagementRoot = styled.div`
  min-height: calc(100vh - ${TOP_BAR_HEIGHT});

  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};

  .tab {
    background-color: ${(props) => (props.theme.themeMode === 'light' ? '#fff' : props.theme.palette.grey[800])};
    /* @media screen and (min-width: 1414px) {
      width: 100%;
    }
    @media screen and (max-width: 720px) {
    } */
    width: 100%;
  }
  .Mui-selected {
    color: ${(props) => props.theme.palette.primary.main};
  }
`;

function TabPanel({ children, value, index }: TabPanelProps) {
  return (
    <Box
      sx={{ paddingTop: '3rem' }}
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
    >
      {value === index && <Box>{children}</Box>}
    </Box>
  );
}
function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`,
  };
}

function StoreManagement() {
  const { topBarHeight } = useOutletContext<OutletContextType>();
  const location = useLocation();
  const [value, setValue] = React.useState(0);

  const dispatch = useDispatch();

  React.useEffect(() => {
    dispatch(flushStore());
    console.log('Flush Store');
  }, [dispatch, value]);

  React.useEffect(() => {
    const currentPath = location.pathname;
    const routePaths = [
      '/store-management/product-list',
      '/store-management/map-product',
      '/store-management/mapped-list',
      '/store-management/new-sale',
      '/store-management/sale-list',
      '/store-management/daily-report',
    ];
    const index = routePaths.indexOf(currentPath);
    if (index !== -1) {
      setValue(index);
    }
  }, [location]);

  return (
    <Page title="">
      <StoreManagementRoot>
        <Tabs
          className="tab"
          variant="scrollable"
          scrollButtons
          allowScrollButtonsMobile
          value={value}
          aria-label="basic tabs"
          sx={{
            ml: '0px',
            borderBottom: 1,
            borderColor: 'divider',
            position: 'fixed',
             top: `${topBarHeight}px`,
            zIndex: 1,
          }}
        >
          {/* <Tab {...a11yProps(0)} label="Add New" component={Link} to="/store-management/new-product" /> */}
          <Tab {...a11yProps(1)} label="Product List" component={Link} to="/store-management/product-list" />
          <Tab {...a11yProps(2)} label="Product Mapping" component={Link} to="/store-management/map-product" />
          <Tab {...a11yProps(3)} label="Mapped List" component={Link} to="/store-management/mapped-list" />
          <Tab {...a11yProps(4)} label="New Sale" component={Link} to="/store-management/new-sale" />
          <Tab {...a11yProps(5)} label="Sale List" component={Link} to="/store-management/sale-list" />
          <Tab {...a11yProps(5)} label="Daily Report" component={Link} to="/store-management/daily-report" />
        </Tabs>

        <TabPanel value={value} index={0}>
          <ManageProducts />
        </TabPanel>
        <TabPanel value={value} index={1}>
          <MapProducts />
        </TabPanel>
        <TabPanel value={value} index={2}>
          <MappedList />
        </TabPanel>
        <TabPanel value={value} index={3}>
          <ProductSale />
        </TabPanel>
        <TabPanel value={value} index={4}>
          <SaleList />
        </TabPanel>
        <TabPanel value={value} index={5}>
          <DailyReport />
        </TabPanel>
      </StoreManagementRoot>
    </Page>
  );
}

export default StoreManagement;
