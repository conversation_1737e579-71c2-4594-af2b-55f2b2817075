import React, { useState } from 'react';

const FileUploader = () => {
  const [files, setFiles] = useState([]);
  const [progress, setProgress] = useState([]);

  const handleUpload = () => {
    const formData = new FormData();
    files.forEach((file) => {
      formData.append('file', file);
    });

    // Create a new XMLHttpRequest object
    const xhr = new XMLHttpRequest();

    // Set up the progress event listener
    xhr.upload.addEventListener('progress', (event) => {
      const percentage = Math.round((event.loaded / event.total) * 100);
      setProgress((prevProgress) => {
        const newProgress = prevProgress.map((item, index) => {
          if (index === event.target.id) {
            return { ...item, percentage };
          }
          return item;
        });
        return newProgress;
      });
    });

    // Set up the error event listener
    xhr.addEventListener('error', (event) => {
      console.error(event);
    });

    // Set up the load event listener
    xhr.addEventListener('load', (event) => {
      if (event.target.status === 200) {
        // Success!
      } else {
        // Error!
      }
    });

    // Open the request
    xhr.open('POST', 'http://localhost:3000/upload');

    // Send the request
    xhr.send(formData);
  };

  const handleFileChange = (event) => {
    setFiles(event.target.files);
  };

  return (
    <div>
      <input type="file" multiple onChange={handleFileChange} />
      <button onClick={handleUpload}>Upload</button>
      {progress.map((item, index) => (
        <div key={index}>
          <p>{item.fileName}</p>
          <progress value={item.percentage} max={100} />
        </div>
      ))}
    </div>
  );
};

export default FileUploader;
