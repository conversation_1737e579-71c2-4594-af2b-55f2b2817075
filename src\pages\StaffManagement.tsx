/* eslint-disable prettier/prettier */
import * as React from 'react';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';
import Page from '@/components/shared/Page';
import styled from 'styled-components';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import CtsAllocationClassWise from '@/features/StaffManagement/CTSAllocationCW';
import StaffReAllocation from '@/features/StaffManagement/StaffReAllocation';
import HoursEngagedList from '@/features/StaffManagement/HoursEngagedList';
import StaffCategoryMap from '@/features/StaffManagement/StaffCategoryMap';
import ManageConveyers from '@/features/StaffManagement/ManageConveyors/ManageConveyers';
import ManageStaffs from '@/features/StaffManagement/ManageStaffs/ManageStaffs ';
import { Link, useLocation, useOutletContext } from 'react-router-dom';
import StaffActivityReport from '@/features/StaffManagement/StaffActivityReport/StaffActivityReport';
import { useDispatch } from 'react-redux';
import { flushStore } from '@/store/flush.slice';
import { OutletContextType } from '@/types/Common';
import CtsAllocation from '@/features/StaffManagement/CTSAllocation/CTSAllocation';
import CtsAllocationTeacherWise from '@/features/StaffManagement/CTSAllocationTW';
import StaffCategory from '@/features/StaffManagement/StaffCategory';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const StaffManagementRoot = styled.div`
  min-height: calc(100vh - ${TOP_BAR_HEIGHT});

  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  .tab {
    background-color: ${(props) => (props.theme.themeMode === 'light' ? '#fff' : props.theme.palette.grey[800])};
    @media screen and (min-width: 1267px) {
      width: 100%;
    }
    @media screen and (max-width: 720px) {
      width: 100%;
    }
  }
  .Mui-selected {
    color: ${(props) => props.theme.palette.primary.main};
  }
  /* .MuiTabs-scrollButtons.Mui-disabled {
    opacity: 0.3;
  } */
`;

function TabPanel({ children, value, index }: TabPanelProps) {
  return (
    <Box
      sx={{ paddingTop: '3rem' }}
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
    >
      {value === index && <Box>{children}</Box>}
    </Box>
  );
}

function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`,
  };
}

export default function StaffManagement() {
  const { topBarHeight } = useOutletContext<OutletContextType>();
  const location = useLocation();
  const [value, setValue] = React.useState(0);

  const dispatch = useDispatch();

  React.useEffect(() => {
    dispatch(flushStore());
    console.log('Flush Store');
  }, [dispatch, value]);

  React.useEffect(() => {
    const currentPath = location.pathname;
    const routePaths = [
      '/staff-management/list',
      '/staff-management/allocation-list',
      // '/staff-management/class-wise-allocation',
      // '/staff-management/teacher-wise-allocation',
      // '/staff-management/allocation',
      '/staff-management/re-allocate',
      '/staff-management/hours-list',
      '/staff-management/staff-activity-report',
      '/staff-management/category-list',
      '/staff-management/category-map',
      '/staff-management/conveyors',
      '/staff-management/conveyors',
    ];
    const index = routePaths.indexOf(currentPath);
    if (index !== -1) {
      setValue(index);
    }
  }, [location]);

  return (
    <Page title="Staff Management">
      <StaffManagementRoot>
        <Tabs
          className="tab"
          variant="scrollable"
          scrollButtons
          allowScrollButtonsMobile
          value={value}
          aria-label="basic tabs"
          sx={{
            ml: '0px',
            borderBottom: 1,
            borderColor: 'divider',
            position: 'fixed',
            top: `${topBarHeight}px`,
            zIndex: 1,
            // width: '100%',
          }}
        >
          <Tab {...a11yProps(0)} label="Manage Staffs" component={Link} to="/staff-management/list" />
          <Tab {...a11yProps(1)} label="CTS Allocation" component={Link} to="/staff-management/allocation-list" />
          {/* <Tab {...a11yProps(2)} label="CTS Allocation Class Wise" component={Link} to="/staff-management/class-wise-allocation" /> */}
          {/* <Tab {...a11yProps(3)} label="CTS List" component={Link} to="/staff-management/allocation" /> */}
          <Tab {...a11yProps(2)} label="Staff Re-Allocation" component={Link} to="/staff-management/re-allocate" />
          <Tab {...a11yProps(3)} label="Hours Engaged List" component={Link} to="/staff-management/hours-list" />
          <Tab
            {...a11yProps(4)}
            label="Staff Activity Report"
            component={Link}
            to="/staff-management/staff-activity-report"
          />
          <Tab label="Staff Category" {...a11yProps(5)} component={Link} to="/staff-management/category-list" />
          <Tab {...a11yProps(6)} label="Staff Category Map" component={Link} to="/staff-management/category-map" />
          <Tab {...a11yProps(7)} label="Manage Conveyors" component={Link} to="/staff-management/conveyors" />
          {/* <Tab label="CTS List" {...a11yProps(3)} /> */}
          {/* <Tab label="Staff Activity Report" {...a11yProps(5)} /> */}
        </Tabs>
        <TabPanel value={value} index={0}>
          <ManageStaffs />
        </TabPanel>
        <TabPanel value={value} index={1}>
          <CtsAllocation />
        </TabPanel>
        {/* <TabPanel value={value} index={1}>
          <CtsAllocationClassWise />
        </TabPanel>
        <TabPanel value={value} index={1}>
          <CtsAllocationTeacherWise />
        </TabPanel> */}
        <TabPanel value={value} index={2}>
          <StaffReAllocation />
        </TabPanel>
        <TabPanel value={value} index={3}>
          <HoursEngagedList />
        </TabPanel>
        <TabPanel value={value} index={4}>
          <StaffActivityReport />
        </TabPanel>
        <TabPanel value={value} index={5}>
          <StaffCategory />
        </TabPanel>
        <TabPanel value={value} index={6}>
          <StaffCategoryMap />
        </TabPanel>
        <TabPanel value={value} index={7}>
          <ManageConveyers />
        </TabPanel>
        {/* <TabPanel value={value} index={5}>
          <ManageClass />
        </TabPanel> */}
      </StaffManagementRoot>
    </Page>
  );
}
