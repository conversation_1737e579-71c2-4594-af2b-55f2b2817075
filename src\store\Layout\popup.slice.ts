import { createSlice } from '@reduxjs/toolkit';
import type { PopupState } from '@/types/Layout';
import type { RootState } from '@/store';

const initialState: PopupState = {
  popupOpen: false,
};

export const popupSlice = createSlice({
  name: 'popup',
  initialState,
  reducers: {
    togglePopup: (state) => {
      state.popupOpen = !state.popupOpen;
    },
  },
});

export const { togglePopup } = popupSlice.actions;

export const getPopupState = (state: RootState) => state.popup.popupOpen;

export default popupSlice.reducer;
