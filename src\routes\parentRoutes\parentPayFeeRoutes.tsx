import Loadable from '@/components/shared/Loadable';
import OnlinePayment from '@/Parent-Side/features/Fees/OnlinePayment';
import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';

const ManageFee = Loadable(lazy(() => import('@/Parent-Side/pages/ManageFee')));
// const PayFee = Loadable(lazy(() => import('@/Parent-Side/features/Fees/PayFee')));
// const PaidList = Loadable(lazy(() => import('@/Parent-Side/features/Fees/PaidList')));
const PayFee = Loadable(lazy(() => import('@/features/ManageFee/PayFee/PayFee')));
const PaidList = Loadable(lazy(() => import('@/features/ManageFee/PaidList/TotalFeePaidList')));
// const OnlinePayment = Loadable(lazy(() => import('@/Parent-Side/features/Fees/OnlinePayment')));

export const  parentPayFeeRoutes: RouteObject[] = [
  {
    path: '/parent-fees',
    element: <ManageFee />,
    children: [
      {
        path: 'pay-fee',
        element: <PayFee />,
      },
      {
        path: 'payment-history',
        element: <PaidList />,
      },
      {
        path: 'online-payment',
        element: <OnlinePayment />,
      },
    ],
  },
];
