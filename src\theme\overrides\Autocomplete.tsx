import { Components, Theme } from '@mui/material';

export default function Autocomplete(theme: Theme): Components<Theme> {
  return {
    MuiAutocomplete: {
      defaultProps: {},
      styleOverrides: {
        paper: {
          boxShadow: theme.customShadows.dropdown,
        },
        listbox: {
          padding: theme.spacing(0, 1),
          '& .MuiAutocomplete-option': {
            padding: theme.spacing(1),
            margin: theme.spacing(1, 0),
            borderRadius: theme.shape.borderRadius,
          },
        },
        inputRoot: {
          '& .MuiInputBase-input': {
            padding: '0.57rem 0.675rem',
            height: 4,
          },
          '& .MuiAutocomplete-endAdornment': {
            paddingRight: theme.spacing(1.5), // Adjust the value as needed
          },
        },
      },
    },
  };
}
