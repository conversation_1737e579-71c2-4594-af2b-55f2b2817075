import { forwardRef, useImperativeHandle, useRef } from 'react';
import { PaymentOrderResponse } from '@/types/Payment';

export type PaymentLaunchFormHandle = {
  submitForm: () => void;
};

export type PaymentLaunchFormProps = {
  orderResponse: PaymentOrderResponse;
};

const PaymentLaunchForm = forwardRef<PaymentLaunchFormHandle, PaymentLaunchFormProps>(({ orderResponse }, ref) => {
  const formRef = useRef<HTMLFormElement | null>(null);

  useImperativeHandle(
    ref,
    () => ({
      submitForm: () => {
        HTMLFormElement.prototype.submit.call(formRef.current);
      },
    }),
    []
  );

  return (
    <form
      name="sdklaunch"
      id="sdklaunch"
      action={orderResponse.launchUrl}
      method="POST"
      ref={formRef}
      style={{ display: 'none!important' }}
    >
      <input type="hidden" id="bdorderid" name="bdorderid" value={orderResponse.bdOrderId} />
      <input type="hidden" id="merchantid" name="merchantid" value={orderResponse.merchantId} />
      <input type="hidden" id="rdata" name="rdata" value={orderResponse.rData} />
      <input name="submit" type="submit" value="Complete your Payment" style={{ display: 'none!important' }} />
    </form>
  );
});

export default PaymentLaunchForm;
