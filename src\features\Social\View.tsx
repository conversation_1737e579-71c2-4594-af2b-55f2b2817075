/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import {
  Autocomplete,
  Grid,
  Stack,
  TextField,
  Button,
  Typography,
  Box,
  FormControl,
  Select,
  MenuItem,
  RadioGroup,
  FormControlLabel,
  Radio,
  Avatar,
  IconButton,
  InputAdornment,
} from '@mui/material';
import styled from 'styled-components';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import { CLASS_SELECT, STATUS_OPTIONS, SUBJECT_SELECT, YEAR_SELECT } from '@/config/Selection';
import SelectDateTimePicker from '@/components/shared/Selections/TimePicker';
import InputFileUpload from '@/components/shared/Selections/FilesUpload';
import DateSelect from '@/components/shared/Selections/DateSelect';
import TextareaField from '@/components/shared/Selections/TextareaField';
import { ErrorIcon } from '@/theme/overrides/CustomIcons';
import CollectionsIcon from '@mui/icons-material/Collections';
import MenuEditDelete from '@/components/shared/Selections/MenuEditDelete';

const ViewRoot = styled.div`
  width: 100%;
  padding: 1.5rem;
  height: 400px;
  overflow: auto;
`;

function View({ onClose }: any) {
  return (
    <ViewRoot>
      {[1, 2, 3, 4, 5, 6, 7, 8].map(() => (
        <Box display="flex" justifyContent="space-between" mb={2} mx={1}>
          <Stack direction="row" alignItems="start" gap={1}>
            <Avatar
              src="https://images.unsplash.com/photo-1633332755192-727a05c4013d?q=80&w=1780&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
              sx={{ width: '35px', height: '35px' }}
            />
            <Stack>
              <Typography variant="subtitle2" fontSize={12}>
                Alex Peter
              </Typography>
              <Typography variant="subtitle1" fontSize={10} color="secondary">
                VII-A
              </Typography>
            </Stack>
          </Stack>
          {/* <Stack>
            <MenuEditDelete />
          </Stack> */}
        </Box>
      ))}
    </ViewRoot>
  );
}

export default View;
