/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Avatar,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Typography,
  Card,
  IconButton,
} from '@mui/material';
import Excel from '@/assets/attendance/Excel.svg';
import Pdf from '@/assets/attendance/PDF.svg';
import styled from 'styled-components';
import { CLASS_SELECT, YEAR_SELECT } from '@/config/Selection';
import { MdAdd } from 'react-icons/md';

import { Students } from '@/config/TableData';
import DeleteIcon from '@mui/icons-material/Delete';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import Popup from '@/components/shared/Popup/Popup';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import AddStructure from '@/features/AcademicManagement/LanguageStudent/AddStructure';
import { EditLanguage } from './Edit';
import { MappedList } from './MappedList';

const LanguageStudentRoot = styled.div`
  padding: 1rem;
  .Card {
    height: calc(100vh - 100px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }
  }
`;

function LanguageStudent() {
  const [Delete, setDelete] = React.useState(false);
  const [popupStructure, setPopupStructure] = React.useState(false);
  const [drawerOpen, setDrawerOpen] = React.useState<boolean>(false);
  const [popup, setPopup] = React.useState(false);

  const handleClickDelete = () => setDelete(true);
  const handleClickCloseDelete = () => setDelete(false);

  const ClickOpenAddStructure = () => setPopupStructure(true);
  const ClickCloseAddStructure = () => setPopupStructure(false);

  const toggleDrawerOpen = () => {
    setDrawerOpen(true);
  };

  const toggleDrawerClose = () => setDrawerOpen(false);

  const handleClickOpen = () => {
    setDrawerOpen(false);
    setPopup(true);
  };

  const handleClickClose = () => setPopup(false);
  const [drawerOpen2, setDrawerOpen2] = React.useState<boolean>(false);

  const DrawerOpenMappedList = () => {
    setDrawerOpen2(true);
    setPopupStructure(false);
  };

  const DrawerCloseMappedList = () => setDrawerOpen2(false);
  return (
    <Page title="Language Student">
      <LanguageStudentRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17} width="100%">
              Language Student Congifuration
            </Typography>
            <Stack pb={1}>
              <Button
                sx={{ borderRadius: '20px', width: '130px' }}
                variant="outlined"
                size="small"
                onClick={ClickOpenAddStructure}
              >
                <MdAdd size="20px" /> Add Structure
              </Button>
            </Stack>
          </Stack>
          <Divider />
          <Grid pb={4} pt={2} container spacing={3}>
            <Grid item lg={2.5} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Academic Year
              </Typography>
              <Autocomplete
                options={YEAR_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select" />}
              />
            </Grid>
            <Grid item lg={2.5} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Class
              </Typography>
              <Autocomplete
                options={CLASS_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select" />}
              />
            </Grid>
            <Grid item lg={2.5} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Language
              </Typography>
              <Autocomplete
                options={CLASS_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select" />}
              />
            </Grid>
            <Grid item lg={2.5} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Student
              </Typography>
              <Autocomplete
                options={CLASS_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select" />}
              />
            </Grid>
            <Grid item lg={2} xs={12}>
              <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                <Button variant="contained" color="secondary" fullWidth>
                  Reset
                </Button>
                <Button variant="contained" color="primary" fullWidth>
                  Search
                </Button>
              </Stack>
            </Grid>
          </Grid>

          <Paper
            sx={{
              border: `1px solid #e8e8e9`,
              width: '100%',
              overflow: 'auto',
              '&::-webkit-scrollbar': {
                width: 0,
              },
            }}
          >
            <TableContainer
              sx={{
                width: { xs: '700px', md: '100%' },
              }}
            >
              <Table stickyHeader aria-label="simple table">
                <TableHead>
                  <TableRow>
                    <TableCell>Sl.No</TableCell>
                    <TableCell>Admission No</TableCell>
                    <TableCell>Student Name</TableCell>
                    <TableCell>Language</TableCell>
                    <TableCell>Academic Year</TableCell>
                    <TableCell>Class</TableCell>
                    <TableCell>Action</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {Students.map((lnrow) => (
                    <TableRow hover key={lnrow.rollNo}>
                      <TableCell>{lnrow.rollNo}</TableCell>
                      <TableCell>45678</TableCell>
                      <TableCell>
                        <Stack direction="row">
                          <Avatar src={lnrow.image} sx={{ mr: 2 }} />
                          <Typography pt={0.7} fontSize={15}>
                            {lnrow.name}
                          </Typography>
                        </Stack>
                      </TableCell>
                      <TableCell>Hindi</TableCell>
                      <TableCell>2022-2023</TableCell>
                      <TableCell>VII-A</TableCell>
                      <TableCell>
                        <Stack direction="row" gap={1}>
                          <IconButton size="small" onClick={toggleDrawerOpen}>
                            <ModeEditIcon />
                          </IconButton>
                          <IconButton onClick={handleClickDelete} size="small" color="error">
                            <DeleteIcon />
                          </IconButton>
                        </Stack>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>

          <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 2 }}>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="error" sx={{ gap: 1 }} size="medium">
                <img className="icon" src={Pdf} />{' '}
                <Typography color="white" fontSize={12}>
                  Download
                </Typography>
              </Button>
              <Button variant="contained" color="success" sx={{ gap: 1 }} size="medium">
                <img className="icon" src={Excel} />{' '}
                <Typography color="white" fontSize={12}>
                  Download
                </Typography>
              </Button>
            </Stack>
          </Box>
        </Card>
        {/* ------Delete Popup--------------- */}
        <Popup
          size="xs"
          state={Delete}
          onClose={handleClickCloseDelete}
          popupContent={<DeleteMessage message="Do you want delete from the list?" />}
        />
        {/* ------Add Structure Popup--------------- */}
        <Popup
          size="lg"
          state={popupStructure}
          onClose={ClickCloseAddStructure}
          popupContent={<AddStructure onClick={DrawerOpenMappedList} />}
        />
        {/* ------Edit Drawer--------------- */}
        <TemporaryDrawer
          onClose={toggleDrawerClose}
          Title="Edit"
          state={drawerOpen}
          DrawerContent={<EditLanguage open={handleClickOpen} onClose={toggleDrawerClose} />}
        />
        {/* ------Success Popup--------------- */}
        <Popup
          size="xs"
          state={popup}
          onClose={handleClickClose}
          popupContent={<SuccessMessage message="Saved Successfully" />}
        />
        <TemporaryDrawer
          onClose={DrawerCloseMappedList}
          Title="Edit"
          state={drawerOpen2}
          DrawerContent={<MappedList />}
        />
      </LanguageStudentRoot>
    </Page>
  );
}

export default LanguageStudent;
