import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import Loadable from '@/components/shared/Loadable';

const Report = Loadable(lazy(() => import('@/pages/Reports')));
const StudentDetails = Loadable(lazy(() => import('@/features/ExtraReports/StudentDetails/StudentDetails')));
const StudentBday = Loadable(lazy(() => import('@/features/ExtraReports/StudentBday')));
const StudentsAttendance = Loadable(lazy(() => import('@/features/Attendance-Marking/StudentsAttendance')));
const AbsenteesList = Loadable(lazy(() => import('@/features/Attendance-Marking/AbsenteesList')));
const Subject = Loadable(lazy(() => import('@/features/AcademicManagement/ManageSubject/ManageSubject')));
const CTSlist = Loadable(lazy(() => import('@/features/StaffManagement/CTSAllocation/CTSAllocation')));
const PTA = Loadable(lazy(() => import('@/features/ExtraReports/PtaMembersList')));
const ManageStaffs = Loadable(lazy(() => import('@/features/StaffManagement/ManageStaffs/ManageStaffs ')));
const ManageConveyers = Loadable(lazy(() => import('@/features/StaffManagement/ManageConveyors/ManageConveyers')));

export const reportRoutes: RouteObject[] = [
  {
    path: '/report',
    element: <Report />,
    children: [
      {
        path: 'student-details',
        element: <StudentDetails />,
      },
      {
        path: 'student-birthday-list',
        element: <StudentBday />,
      },
      {
        path: 'attendance-report',
        element: <StudentsAttendance />,
      },
      {
        path: 'absentees-list',
        element: <AbsenteesList />,
      },
      {
        path: 'teacher-subject-list',
        element: <Subject />,
      },
      {
        path: 'class-teacher-info',
        element: <CTSlist />,
      },
      {
        path: 'pta-members-list',
        element: <PTA />,
      },
      {
        path: 'staff-list',
        element: <ManageStaffs />,
      },
      {
        path: 'conveyors-list',
        element: <ManageConveyers />,
      },
    ],
  },
];
