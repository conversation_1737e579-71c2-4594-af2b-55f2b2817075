import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import Loadable from '@/components/shared/Loadable';

const Assignment = Loadable(lazy(() => import('@/pages/Assignment')));
const AssignmentList = Loadable(lazy(() => import('@/features/Assignment/AssignmentList/AssignmentList')));
const MapToProgressCard = Loadable(lazy(() => import('@/features/Assignment/MapToProgressCard')));
const ZoomKeyList = Loadable(lazy(() => import('@/features/LiveClass/ZoomKeyList/ZoomKeyList')));

export const assignmentRoutes: RouteObject[] = [
  {
    path: '/assignment',
    element: <Assignment />,
    children: [
      {
        path: 'assignment-list',
        element: <AssignmentList />,
      },
      {
        path: 'map-progress-card',
        element: <MapToProgressCard />,
      },
      {
        path: 'zoom-key-list',
        element: <ZoomKeyList />,
      },
    ],
  },
];
