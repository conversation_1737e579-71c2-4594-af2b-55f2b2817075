import React, { useRef, useState } from "react";
import TextField from "@mui/material/TextField";
import Box from "@mui/material/Box";

const MultiTextField = () => {
  const [fields, setFields] = useState([
    { id: 1, value: "" },
    { id: 2, value: "" },
    { id: 3, value: "" },
  ]);

  // Create an array of refs for each TextField
  const refs = useRef<(HTMLInputElement | null)[]>([]);

  const handleChange = (id: number, value: string) => {
    setFields((prevFields) =>
      prevFields.map((field) =>
        field.id === id ? { ...field, value } : field
      )
    );

    // Move focus to the next field if available
    const currentIndex = fields.findIndex((field) => field.id === id);
    if (value.trim() && refs.current[currentIndex + 1]) {
      refs.current[currentIndex + 1]?.focus();
    }
  };

  return (
    <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
      {fields.map((field, index) => (
        <TextField
          key={field.id}
          inputRef={(el) => (refs.current[index] = el)} // Assign refs dynamically
          label={`Field ${field.id}`}
          value={field.value}
          onChange={(e) => handleChange(field.id, e.target.value)}
          variant="outlined"
        />
      ))}
    </Box>
  );
};

export default MultiTextField;
