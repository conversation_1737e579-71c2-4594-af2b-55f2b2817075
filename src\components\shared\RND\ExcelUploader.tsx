import React, { useState } from 'react';
import {
  Box,
  Button,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  TextField,
  IconButton,
  useTheme,
} from '@mui/material';
import { CloudUpload, Edit, Save } from '@mui/icons-material';
import * as XLSX from 'xlsx';
import axios from 'axios';
import { useConfirm } from '../Popup/Confirmation';
import { SuccessMessage } from '../Popup/SuccessMessage';
import { FaFolderOpen } from 'react-icons/fa';
import { borderTopLeftRadius } from 'html2canvas/dist/types/css/property-descriptors/border-radius';

const ExcelUploader: React.FC = () => {
  const { confirm } = useConfirm();
  const theme = useTheme();
  const [data, setData] = useState<any[]>([]);
  const [file, setFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);
  const [editRow, setEditRow] = useState<number | null>(null);
  const [editedData, setEditedData] = useState<any[]>([]);

  const formatHeader = (header: string) => {
    return header
      .replace(/([a-z])([A-Z])/g, '$1 $2')
      .replace(/_/g, ' ')
      .replace(/\b\w/g, (char) => char.toUpperCase());
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (!selectedFile) return;

    setFile(selectedFile);
    const reader = new FileReader();
    reader.readAsArrayBuffer(selectedFile);
    reader.onload = (e) => {
      const buffer = e.target?.result;
      const workbook = XLSX.read(buffer, { type: 'array' });
      const sheetName = workbook.SheetNames[0];
      const sheet = workbook.Sheets[sheetName];
      const parsedData = XLSX.utils.sheet_to_json(sheet, { header: 1 });

      if (parsedData.length > 0) {
        parsedData[0] = parsedData[0].map((header: string) => formatHeader(header));
      }
      setData(parsedData);
      setEditedData(parsedData.slice(1));
    };
  };
  const formatHeaderUpload = (header: string) => {
    // If header is already camelCase (single word or matches camelCase pattern), return as is
    // if (/^[a-z]+([A-Z][a-z]*)*$/.test(header)) {
    //   return header;
    // }
    return header
      .trim()
      .split(/\s+/) // Split by spaces
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1)) // Capitalize first letter, keep rest unchanged
      .join('');
  };

  // Example usage:
  console.log(formatHeaderUpload('Roll No')); // Output: "RollNo"
  console.log(formatHeaderUpload('Student Name')); // Output: "StudentName"
  console.log(formatHeaderUpload('class section')); // Output: "ClassSection"
  console.log(formatHeaderUpload('ID')); // Output: "ID"
  console.log(formatHeaderUpload('RollNo')); // Output: "RollNo"

  const handleUploadToAPI = async () => {
    if (!file) {
      const errorMessage = <SuccessMessage loop={false} message="Please select a file first." />;
      await confirm(errorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
      return;
    }

    setUploading(true);

    const reader = new FileReader();
    reader.readAsArrayBuffer(file);
    reader.onload = async (e) => {
      const buffer = e.target?.result;
      const workbook = XLSX.read(buffer, { type: 'array' });
      const sheetName = workbook.SheetNames[0];
      const sheet = workbook.Sheets[sheetName];
      const parsedData = XLSX.utils.sheet_to_json(sheet, { header: 1 });

      if (parsedData.length < 2) {
        const errorMessage = <SuccessMessage loop={false} message="The file must have at least one row of data." />;
        await confirm(errorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
        setUploading(false);
        return;
      }

      // Extract headers from the first row and format them
      const headers = parsedData[0].map(formatHeaderUpload);

      // Convert remaining rows into objects with formatted key-value pairs
      const formattedData = editedData.map((row: any[]) => {
        const obj: Record<string, any> = {};
        headers.forEach((header: string, index: number) => {
          obj[header] = row[index] || ''; // Handle empty cells
        });
        return obj;
      });

      console.log('Formatted Data:', formattedData);
      try {
        const response = await axios.post(
          'https://your-api-endpoint.com/upload',
          { data: formattedData },
          { headers: { 'Content-Type': 'application/json' } }
        );

        const successMessage = <SuccessMessage loop={false} message={`File "${file.name}" uploaded successfully!`} />;
        await confirm(successMessage, 'Success', { okLabel: 'Ok', showOnlyOk: true });

        console.log('Response:', response.data);
      } catch (error) {
        console.error('Upload failed', error);
        const errorMessage = (
          <SuccessMessage loop={false} message={`Upload failed for "${file.name}". Please try again.`} />
        );
        await confirm(errorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
      } finally {
        setUploading(false);
      }
    };
  };

  const handleEditRow = (index: number) => {
    setEditRow(index);
  };

  const handleSaveRow = () => {
    setEditRow(null);
  };

  const handleInputChange = (rowIndex: number, cellIndex: number, value: string) => {
    const updatedData = [...editedData];
    updatedData[rowIndex][cellIndex] = value;
    setEditedData(updatedData);
  };

  return (
    <Box sx={{ p: 3, mb: 10, textAlign: 'center' }}>
      <Typography variant="h5" gutterBottom>
        Upload and Preview Excel File
      </Typography>
      <Button color="info" variant="contained" component="label" startIcon={<FaFolderOpen />}>
        Choose Excel
        <input type="file" hidden accept=".xlsx, .xls" onChange={handleFileUpload} />
      </Button>

      {file && <Typography sx={{ mt: 2 }}>Selected File: {file.name}</Typography>}

      {data.length > 0 && (
        <TableContainer component={Paper} sx={{ mt: 3, borderRadius: 0, height: 500 }}>
          <Table stickyHeader sx={{ minWidth: '1000px', borderCollapse: 'collapse' }}>
            <TableHead sx={{}}>
              <TableRow>
                {data[0]?.map((header: string, index: number) => (
                  <TableCell
                    key={index}
                    sx={{
                      '&:first-of-type': {
                        borderTopLeftRadius: '0px', // Adjust as needed
                      },
                      '&:last-of-type': {
                        borderTopRightRadius: '0px', // Adjust as needed
                      },

                      backgroundColor: theme.palette.success.dark,
                      color: theme.palette.common.white,
                    }}
                  >
                    {header}
                  </TableCell>
                ))}
                <TableCell
                  sx={{
                    '&:last-of-type': {
                      borderTopRightRadius: '0px', // Adjust as needed
                    },

                    backgroundColor: theme.palette.success.dark,
                    color: theme.palette.common.white,
                  }}
                >
                  Actions
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {editedData.map((row, rowIndex) => (
                <TableRow key={rowIndex}>
                  {data[0].map((_, cellIndex) => (
                    <TableCell key={cellIndex} sx={{ border: 1, borderColor: theme.palette.grey[300], height: 10 }}>
                      {editRow === rowIndex ? (
                        <TextField
                          sx={{
                            minWidth: 10,
                          }}
                          value={row[cellIndex] || ''}
                          onChange={(e) => handleInputChange(rowIndex, cellIndex, e.target.value)}
                          size="small"
                        />
                      ) : (
                        row[cellIndex]
                      )}
                    </TableCell>
                  ))}
                  <TableCell sx={{ border: 1, borderColor: theme.palette.grey[300] }}>
                    {editRow === rowIndex ? (
                      <IconButton size='small' color="success" onClick={handleSaveRow}>
                        <Save color="success" />
                      </IconButton>
                    ) : (
                      <IconButton size='small' color="secondary" onClick={() => handleEditRow(rowIndex)}>
                        <Edit color="secondary" />
                      </IconButton>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {file && (
        <Button
          startIcon={<CloudUpload />}
          variant="contained"
          color="primary"
          sx={{ mt: 2 }}
          onClick={handleUploadToAPI}
          disabled={uploading}
        >
          {uploading ? 'Uploading...' : 'Upload'}
        </Button>
      )}
    </Box>
  );
};

export default ExcelUploader;
