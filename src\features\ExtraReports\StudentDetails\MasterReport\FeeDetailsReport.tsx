/* eslint-disable jsx-a11y/alt-text */
import { useCallback, useMemo } from 'react';
import { Box, Grid, Card, useTheme, Chip, Paper } from '@mui/material';
import styled from 'styled-components';
import useSettings from '@/hooks/useSettings';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { StudentFeeDetailsReportType, TermFeeDetailsReportType } from '@/types/Reports';

const StudentFeeDetailsReportData = [
  {
    id: 1,
    fee: 'Annual fee',
    amount: 700,
    paid: 600,
  },
  {
    id: 2,
    fee: 'Annual fee',
    amount: 700,
    paid: 600,
  },
  {
    id: 3,
    fee: 'Annual fee',
    amount: 700,
    paid: 600,
  },

  {
    id: 4,
    fee: 'Annual fee',
    amount: 700,
    paid: 600,
  },
  {
    id: 5,
    fee: 'Annual fee',
    amount: 700,
    paid: 600,
  },
  {
    id: 6,
    fee: 'Annual fee',
    amount: 700,
    paid: 600,
  },
  {
    id: 7,
    fee: 'Annual fee',
    amount: 700,
    paid: 600,
  },
];
const TermFeeDetailsReportData = [
  {
    id: 1,
    term: 'Term 1',
    amount: 7000,
    busFee: 900,
    paid: 600,
  },
  {
    id: 2,
    term: 'Term 1',
    amount: 7000,
    busFee: 900,
    paid: 600,
  },
  {
    id: 3,
    term: 'Term 1',
    amount: 7000,
    busFee: 900,
    paid: 600,
  },

  {
    id: 4,
    term: 'Term 1',
    amount: 7000,
    busFee: 900,
    paid: 600,
  },
  {
    id: 5,
    term: 'Term 1',
    amount: 7000,
    busFee: 900,
    paid: 600,
  },
  {
    id: 6,
    term: 'Term 1',
    amount: 7000,
    busFee: 900,
    paid: 600,
  },
  {
    id: 7,
    term: 'Term 1',
    amount: 7000,
    busFee: 900,
    paid: 600,
  },
];

const FeeDetailsReportRoot = styled.div`
  .ReportCard {
    display: flex;
    flex-direction: column;
    height: 350px;
    /* background-color: ${(props) =>
      props.theme.themeMode === 'light' ? '#fff9f5' : props.theme.palette.grey[900]}; */
    /* border: 1px solid ${(props) => props.theme.palette.grey[300]}; */

    .card-main-body {
      display: flex;
      flex-direction: column;
      min-height: calc(100% - 5px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }
    }
  }
`;

type FeeDetailsProps = {
  idStudentFee: string;
  idTermFee: string;
};

function FeeDetailsReport({ idStudentFee, idTermFee }: FeeDetailsProps) {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const tableStyles1 = {
    '& th:nth-of-type(n+2):nth-of-type(-n+6), & td:nth-of-type(n+2):nth-of-type(-n+6)': {
      textAlign: 'center',
    },
    backgroundColor: isLight ? '#fff9f5' : theme.palette.grey[900],
  };
  const tableStyles2 = {
    '& th:nth-of-type(n+2):nth-of-type(-n+6), & td:nth-of-type(n+2):nth-of-type(-n+6)': {
      textAlign: 'center',
    },
    backgroundColor: isLight ? '#fffef2' : theme.palette.grey[900],
  };

  const getRowKeyStudentFeeDetailsReport = useCallback((row: StudentFeeDetailsReportType) => row.id, []);
  const getRowKeyTermFeeDetailsReport = useCallback((row: TermFeeDetailsReportType) => row.id, []);

  const StudentFeeDetailsReportColumns: DataTableColumn<StudentFeeDetailsReportType>[] = useMemo(
    () => [
      {
        name: 'fee',
        dataKey: 'fee',
        headerLabel: 'Fee',
      },
      {
        name: 'amount',
        dataKey: 'amount',
        headerLabel: 'Amount',
      },
      {
        name: 'paid',
        dataKey: 'paid',
        headerLabel: 'Paid',
      },
    ],
    []
  );
  const TermFeeDetailsReportColumns: DataTableColumn<TermFeeDetailsReportType>[] = useMemo(
    () => [
      {
        name: 'term',
        dataKey: 'term',
        headerLabel: 'Term',
      },
      {
        name: 'amount',
        dataKey: 'amount',
        headerLabel: 'Amount',
      },
      {
        name: 'busFee',
        dataKey: 'busFee',
        headerLabel: 'Bus Fee',
      },
      {
        name: 'paid',
        dataKey: 'paid',
        headerLabel: 'Paid',
      },
    ],
    []
  );
  return (
    <FeeDetailsReportRoot>
      <Grid container spacing={{ xs: 0, xl: 5 }}>
        <Grid id={idStudentFee} item xl={6} lg={12} xs={12}>
          <Card
            className="ReportCard"
            sx={{
              mt: 3,
              backgroundColor: isLight ? '#fff9f5' : theme.palette.grey[900],
              boxShadow: 0,
              px: { xs: 3, md: 2 },
              py: { xs: 2, md: 2 },
            }}
          >
            <div className="card-main-body">
              <Box>
                <Chip
                  label="Student Fee Details"
                  variant="filled"
                  sx={{
                    height: 25,
                    background: isLight ? theme.palette.common.black : theme.palette.common.white,
                    color: isLight ? theme.palette.common.white : theme.palette.common.black,
                  }}
                />
              </Box>
              <Paper className="card-table-container">
                <DataTable
                  columns={StudentFeeDetailsReportColumns}
                  data={StudentFeeDetailsReportData}
                  getRowKey={getRowKeyStudentFeeDetailsReport}
                  fetchStatus="success"
                  tableStyles={tableStyles1}
                />
              </Paper>
            </div>
          </Card>
        </Grid>
        <Grid id={idTermFee} item xl={6} lg={12} xs={12}>
          <Card
            className="ReportCard"
            sx={{
              mt: 3,
              backgroundColor: isLight ? '#fffef2' : theme.palette.grey[900],
              boxShadow: 0,
              px: { xs: 3, md: 2 },
              py: { xs: 2, md: 2 },
            }}
          >
            <div className="card-main-body">
              <Box>
                <Chip
                  label="Term Fee Details"
                  variant="filled"
                  sx={{
                    height: 25,
                    background: isLight ? theme.palette.common.black : theme.palette.common.white,
                    color: isLight ? theme.palette.common.white : theme.palette.common.black,
                  }}
                />
              </Box>
              <Paper className="card-table-container">
                <DataTable
                  columns={TermFeeDetailsReportColumns}
                  data={TermFeeDetailsReportData}
                  getRowKey={getRowKeyTermFeeDetailsReport}
                  fetchStatus="success"
                  tableStyles={tableStyles2}
                />
              </Paper>
            </div>
          </Card>
        </Grid>
      </Grid>
    </FeeDetailsReportRoot>
  );
}

export default FeeDetailsReport;
