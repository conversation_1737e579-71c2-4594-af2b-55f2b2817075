import React, { useState } from 'react';
import { Grid, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Stack } from '@mui/material';
import CustomTextField from '@/components/shared/Selections/CustomTextField';

const EditDrivers = ({ staff, onCancel, onUpdate }: any) => {
  const [editedStaff, setEditedStaff] = useState(staff);

  const handleChange = (e: any) => {
    const { name, value } = e.target;
    setEditedStaff((prevStaff: any) => ({
      ...prevStaff,
      [name]: value,
    }));
  };

  const handleUpdate = () => {
    onUpdate(editedStaff.id, editedStaff);
  };

  return (
    <Grid container spacing={2} m={4} maxWidth="90%">
      <Grid item xs={12}>
        <Typography variant="h6" mb={2}>
          Edit Driver Details
        </Typography>
      </Grid>
      <Grid item md={6} xs={12}>
        <CustomTextField
          name="ID"
          label="ID No."
          variant="outlined"
          value={editedStaff.idNo}
          onChange={handleChange}
          fullWidth
        />
      </Grid>
      <Grid item md={6} xs={12}>
        <CustomTextField
          name="name"
          label="Driver Name"
          variant="outlined"
          value={editedStaff.driverName}
          onChange={handleChange}
          fullWidth
        />
      </Grid>
      <Grid item xs={12}>
        <CustomTextField
          name="number"
          label="Mobile Number"
          variant="outlined"
          value={editedStaff.driverNumber}
          onChange={handleChange}
          fullWidth
        />
      </Grid>
      <Grid item xs={12}>
        <CustomTextField
          name="date"
          label="Last Updated"
          variant="outlined"
          value={editedStaff.lastUpdated}
          onChange={handleChange}
          fullWidth
        />
      </Grid>
      <Grid item xs={12}>
        <Stack justifyContent="end" direction="row" spacing={2}>
          <Button variant="contained" color="secondary" onClick={onCancel}>
            Cancel
          </Button>
          <Button variant="contained" color="primary" onClick={handleUpdate}>
            Save
          </Button>
        </Stack>
      </Grid>
    </Grid>
  );
};

export default EditDrivers;
