import BarChart from '@/components/Dashboard/BarChart';
import Typography from '@mui/material/Typography';
import { Box, Card, Stack } from '@mui/material';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useAppSelector } from '@/hooks/useAppSelector';
import { getClassData, getFeeOverviewChartListData } from '@/config/storeSelectors';
import { fetchClassList } from '@/store/Dashboard/dashboard.thunks';
import { useEffect, useState } from 'react';
import useAuth from '@/hooks/useAuth';
import MenuItem from '@mui/material/MenuItem';
import Select, { SelectChangeEvent } from '@mui/material/Select';
import styled, { useTheme } from 'styled-components';
import { ClassListInfo } from '@/types/AcademicManagement';
import NodataGraphIcon from '@/assets/Graph.png';
import { fetchFeeOverviewChart } from '@/store/ManageFee/manageFee.thunks';
import useSettings from '@/hooks/useSettings';
import { OverViewProps } from '@/types/Common';

const StatisticRoot = styled.div`
  width: 100%;
  .apexcharts-menu-icon {
    display: none;
  }
`;

function Statistic({ academicId, feeTypeId }: OverViewProps) {
  const { user } = useAuth();
  const theme = useTheme();
  const isLight = useSettings().themeMode === 'light';
  const adminId: number | undefined = user?.accountId;
  const dispatch = useAppDispatch();
  // const dashboardFeeChartStatus = useAppSelector(getdashboardFeeChartStatus);
  const feeOverviewChartListData = useAppSelector(getFeeOverviewChartListData);
  // const ClassStatus = useAppSelector(getClassStatus);
  const ClassData = useAppSelector(getClassData);
  const AllClassOption = {
    classId: -1,
    className: 'All Class',
    classDescription: 'string',
    classStatus: 1,
  };

  const classDataWithAllClass = [AllClassOption, ...ClassData];
  const [classOptions, setClassOptions] = useState(classDataWithAllClass[0]);
  const { classId, className } = classOptions || {};

  const handleChange = (event: SelectChangeEvent) => {
    const selectedClass = classDataWithAllClass.find((item) => item.className === event.target.value);
    if (selectedClass) {
      setClassOptions(selectedClass);
    }
  };
  useEffect(() => {
    dispatch(fetchClassList(adminId));
    dispatch(fetchFeeOverviewChart({ adminId, academicId, classId, feeTypeId }));
  }, [dispatch, adminId, classId, academicId, feeTypeId]);

  const totalFeeSum = feeOverviewChartListData.reduce((acc, currentMonth) => {
    return acc + parseFloat(currentMonth.totalFeeCollected);
  }, 0);
  console.log(totalFeeSum);
  const barChartDataFeeStatistic = [
    { name: 'Fee', data: feeOverviewChartListData.map((item) => item.totalFeeCollected) },
  ];

  const barChartOptionFeeStatistic = {
    chart: {
      foreColor: '#732ebe',
    },
    colors: ['#732ebe'],
    // stroke: ['100px'],
    noData: { style: {} },
    xaxis: {
      categories: feeOverviewChartListData.map((item) => item.monthName.substring(0, 3)),
      labels: {
        style: {
          fontWeight: 600,
          fontSize: '10',
        },
      },
      axisBorder: {
        show: true,
      },
      axisTicks: {
        show: false,
      },
    },
    yaxis: {
      labels: {
        style: {
          fontWeight: 600,
        },
      },
    },
    grid: {
      borderColor: 'rgba(163, 174, 208, 0.3)',
      show: true,
      yaxis: {
        lines: {
          show: false,
        },
      },
      row: {
        opacity: 0.5,
      },
      xaxis: {
        lines: {
          show: false,
        },
      },
    },
    fill: {
      type: 'gradient',
    },
    plotOptions: {
      bar: {
        borderRadius: 10,
        borderRadiusApplication: 0,
        columnWidth: '50%',
      },
    },
    tooltip: {
      style: {},
      theme: 'dark',
    },

    dataLabels: {
      enabled: false,
      style: {
        colors: ['#fff'],
        // fontSize: '8',
      },
    },
  };

  return (
    <StatisticRoot>
      <Card
        sx={{ height: '235px', backgroundColor: isLight ? theme.palette.chart.violet[4] : theme.palette.grey[900] }}
      >
        <Box display="flex" p={2} alignItems="center" justifyContent="space-between">
          <Typography variant="h6" fontSize={14}>
            Fee Statistic
          </Typography>
          <Stack direction={{ xs: 'column', sm: 'row' }}>
            {/* <SelectBox Selection_Options={YEAR_SELECT} placeholder="Year" /> */}
            <Select
              sx={{ backgroundColor: theme.palette.common.white, color: theme.palette.primary.main, height: 30 }}
              value={className}
              onChange={handleChange}
              displayEmpty
              labelId="demo-dialog-select-label"
              id="demo-dialog-select"
              inputProps={{ 'aria-label': 'Without label' }}
              MenuProps={{
                PaperProps: {
                  style: {
                    maxHeight: '250px', // Adjust the value to your desired height
                  },
                },
              }}
            >
              {/* <MenuItem value={className} className="d-none">
              {className}
            </MenuItem> */}
              {classDataWithAllClass?.map((item: ClassListInfo) => (
                <MenuItem sx={{ fontSize: '13px' }} key={item.classId} value={item.className}>
                  {item.className}
                </MenuItem>
              ))}
            </Select>
          </Stack>
        </Box>
        <Box sx={{ overflowX: 'scroll', height: '100%' }}>
          {totalFeeSum === 0 ? (
            <Stack
              // sx={{ height: { sm: '140px' } }}
              direction="column"
              justifyContent="center"
              alignItems="center"
              spacing={1}
              my={2}
            >
              <img width={140} src={NodataGraphIcon} alt="" style={{ opacity: 0.8 }} />
              <Typography variant="subtitle2" color="secondary">
                No Fees Data in this Class.
              </Typography>
            </Stack>
          ) : (
            <Box sx={{ width: { sm: '100%', xs: '550px' }, height: '170px' }}>
              <BarChart chartDatas={barChartDataFeeStatistic} chartOptions={barChartOptionFeeStatistic} height="100%" />
            </Box>
          )}
        </Box>
      </Card>
    </StatisticRoot>
  );
}

export default Statistic;
