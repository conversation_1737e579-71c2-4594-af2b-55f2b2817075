import React, { use<PERSON><PERSON>back, useMemo, useRef, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Box,
  Button,
  Divider,
  FormControl,
  Card,
  Stack,
  Collapse,
  Grid,
  IconButton,
  TextField,
  Typography,
  Select,
  MenuItem,
  Paper,
  useTheme,
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import { breakPointsMaxwidth } from '@/config/breakpoints';
import { useReactToPrint } from 'react-to-print';
import styled from 'styled-components';
import { EXAM_SELECT_OPTIONS } from '@/config/Selection';
import { GradeWiseProgrssReport } from '@/types/ExamCenter';
import Excel from '@/assets/attendance/Excel.svg';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import TabButton from '@/components/shared/TabButton';

const dummyData: GradeWiseProgrssReport[] = [
  {
    slNo: 1,
    subjectName: 'Mathematics',
    teacherName: '<PERSON>',
    aplus: 5,
    a: 10,
    bplus: 15,
    b: 20,
    cplus: 25,
    c: 30,
    dplus: 35,
    d: 40,
    eplus: 45,
    e: 50,
  },
  {
    slNo: 2,
    subjectName: 'Science',
    teacherName: 'Jane Smith',
    aplus: 6,
    a: 11,
    bplus: 16,
    b: 21,
    cplus: 26,
    c: 31,
    dplus: 36,
    d: 41,
    eplus: 46,
    e: 51,
  },
  {
    slNo: 3,
    subjectName: 'English',
    teacherName: 'Emily Johnson',
    aplus: 7,
    a: 12,
    bplus: 17,
    b: 22,
    cplus: 27,
    c: 32,
    dplus: 37,
    d: 42,
    eplus: 47,
    e: 52,
  },
];
// const dummyGrade = [{ name: 'a+', dataKey: 'A+', headerLabel: 'A+' }];
const GradeWiseRoot = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    min-height: calc(100vh - 100px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      height: calc(100% - 100px);
      flex-grow: 1;
      .certificate {
        /* border: 2px solid ${(props) => props.theme.palette.primary.main}; */
        /* padding: 0px 20px; */
        /* border-radius: 10px; */
      }
      .main-card-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid ${(props) => props.theme.palette.secondary.lighter};
        border-radius: 6px;
        overflow: hidden;
        .card-top {
          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.primary.lighter : props.theme.palette.grey[900]};
        }

        .card-table-container {
          flex-grow: 1;
          width: 100%;
          height: 100%;
          display: flex;
          flex-direction: column;
          border: 1px solid ${(props) => props.theme.palette.secondary.lighter};
          overflow: hidden;
          border-radius: 0px;

          .MuiTableContainer-root {
            height: 100%;
          }
          .MuiTable-root {
          }

          .MuiTablePagination-root {
            flex-grow: 1;
            flex-shrink: 0;
          }
          .MuiTableCell-root {
            /* border: 0.5px solid ${(props) => props.theme.palette.secondary.lighter}; */
            border-radius: 0px;
          }
        }
      }
    }

    .table_top {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-evenly;
      border: 1px solid ${(props) => props.theme.palette.secondary.lighter};
    }

    @media ${breakPointsMaxwidth.xl} {
      .MuiTableCell-root {
        font-size: 11px;
      }
      .MuiTableCell-root .MuiTypography-root {
        font-size: 11px;
      }
    }
    @media screen and (max-width: 1217px) {
      .MuiFormControl-root {
        width: 200px;
      }
      .select_box {
        width: 200px;
      }
    }
    @media screen and (max-width: 1160px) {
      .MuiTableContainer-root {
        /* width: 900px; */
      }
      .card-table-container {
        overflow: auto;
      }
    }
    .card_top {
      display: flex;
      justify-content: space-between;
      flex-direction: row;
    }
    .title_searchbar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex: 1;
    }
    .progress_report_tab {
      display: flex;
      align-items: center;
      flex-direction: row;
    }
    @media screen and (max-width: 1160px) {
      .card_top {
        display: flex;
        flex-direction: column;
      }
      .progress_report_tab {
        display: flex;
        justify-content: end;
        padding-bottom: 10px;
        overflow-x: scroll;
        ::-webkit-scrollbar {
          height: 10px;
        }
        ::-webkit-scrollbar-thumb {
          background-color: ${(props) => props.theme.palette.grey[400]};
          border-radius: 20px;
        }
      }
    }
    @media screen and (max-width: 998px) {
      .progress_report_tab {
        justify-content: start;
      }
    }
    @media screen and (max-width: 768px) {
      .progress_report_tab {
        ::-webkit-scrollbar {
          height: 0px;
        }
      }
    }
  }
`;

export type GradeWiseProps = {
  onClickPromotionList: () => void;
  onClickStudentWise: () => void;
  onClickClassWise: () => void;
  onClickTopper: () => void;
};
function GradeWise({ onClickPromotionList, onClickStudentWise, onClickClassWise, onClickTopper }: GradeWiseProps) {
  const [showFilter, setShowFilter] = useState(false);
  const theme = useTheme();
  // const { themeMode } = useSettings();

  const componentRef = useRef<HTMLDivElement>(null);
  const handlePrint = useReactToPrint({
    content: () => componentRef.current,
    pageStyle: `
      @page {
        size: landscape;
        margin: 20mm;
      }
      @media print {
        body {
          -webkit-print-color-adjust: exact;
        }
      }
    `,
  });

  const getRowKey = useCallback((row: GradeWiseProgrssReport) => row.slNo, []);

  const gradeListColumns: DataTableColumn<GradeWiseProgrssReport>[] = useMemo(
    () => [
      {
        name: 'slNo',
        dataKey: 'slNo',
        headerLabel: 'Sl No',
      },
      {
        name: 'subjectName',
        dataKey: 'subjectName',
        headerLabel: 'Subject Name',
      },
      {
        name: 'teacherName',
        dataKey: 'teacherName',
        headerLabel: 'Teacher Name',
      },
      {
        name: 'aplus',
        dataKey: 'aplus',
        headerLabel: 'A+',
      },
      {
        name: 'a',
        dataKey: 'a',
        headerLabel: 'A',
      },
      {
        name: 'b+',
        dataKey: 'b+',
        headerLabel: 'B+',
      },
      {
        name: 'b',
        dataKey: 'b',
        headerLabel: 'B',
      },
      {
        name: 'c+',
        dataKey: 'c+',
        headerLabel: 'C+',
      },
      {
        name: 'c',
        dataKey: 'c',
        headerLabel: 'C',
      },
      {
        name: 'd+',
        dataKey: 'd+',
        headerLabel: 'D+',
      },
      {
        name: 'd',
        dataKey: 'd',
        headerLabel: 'D',
      },
      {
        name: 'e+',
        dataKey: 'e+',
        headerLabel: 'E+',
      },
      {
        name: 'e',
        dataKey: 'e',
        headerLabel: 'E',
      },
    ],
    []
  );

  return (
    <Page title="Grade Wise">
      <GradeWiseRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <div className="card_top ">
            <div className="title_searchbar">
              <Typography variant="h6" fontSize={17}>
                Grade Wise
              </Typography>

              <Box sx={{ flexShrink: 0 }}>
                <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                  <SearchIcon />
                </IconButton>
              </Box>
            </div>
            <Stack className="progress_report_tab">
              <div style={{ flexShrink: 0 }}>
                <TabButton title="Student Individual" variant="outlined" onClick={onClickStudentWise} />
                <TabButton title="Student Promotion List" variant="outlined" onClick={onClickPromotionList} />
                <TabButton title="Class Wise" variant="outlined" onClick={onClickClassWise} />
                <TabButton title="Topper" variant="outlined" onClick={onClickTopper} />
                <TabButton title="Grade Wise" variant="contained" />
              </div>
            </Stack>
          </div>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={3} alignItems="end">
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', xl: 240 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Year
                      </Typography>
                      <TextField name="year" />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', xl: 240 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <TextField name="className" />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', xl: 240 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Exam
                      </Typography>
                      <Select className="select_box" labelId="classStatusFilter" id="classStatusFilterSelect">
                        <MenuItem value={-1}>All</MenuItem>
                        {EXAM_SELECT_OPTIONS.map((opt) => (
                          <MenuItem key={opt.id} value={opt.id}>
                            {opt.exam}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12} sm={6}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button type="reset" variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>
            <Box ref={componentRef} className="main-card-container" sx={{ mt: 2 }}>
              <Box className="card-top text-center" sx={{ py: 2 }}>
                <Stack direction="column" gap={1}>
                  <Typography variant="h6" fontSize={17} sx={{ color: theme.palette.primary.main }}>
                    PULIYAPARAMB HIGH SCHOOL
                  </Typography>
                  <Typography variant="h6" fontSize={15}>
                    VII&nbsp;-&nbsp;ANNUAL EXAMINATION
                  </Typography>
                </Stack>
              </Box>
              <Paper className="card-table-container">
                <Box className="table_top">
                  <Stack direction="row" flexWrap="wrap">
                    <Typography variant="subtitle1">CLASS NAME&nbsp;:&nbsp;</Typography>
                    <Typography variant="subtitle1" className="fw-bold">
                      VII-A
                    </Typography>
                  </Stack>
                  <Stack direction="row" flexWrap="wrap">
                    <Typography variant="subtitle1"> CLASS TEACHER&nbsp;:&nbsp;</Typography>
                    <Typography variant="subtitle1" className="fw-bold">
                      ASMA H
                    </Typography>
                  </Stack>
                </Box>
                <DataTable
                  tableWidth={600}
                  textAlign="text-center"
                  columns={gradeListColumns}
                  data={dummyData}
                  getRowKey={getRowKey}
                  fetchStatus="success"
                />
              </Paper>
            </Box>
          </div>
          <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, mt: 3 }}>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="success" sx={{ gap: 1 }} size="medium">
                <img className="icon" alt="Excel" src={Excel} />
                <Typography color="white" fontSize={12}>
                  Download
                </Typography>
              </Button>
              <Button onClick={handlePrint} variant="contained" color="primary">
                Print
              </Button>
            </Stack>
          </Box>
        </Card>
      </GradeWiseRoot>
    </Page>
  );
}

export default GradeWise;
