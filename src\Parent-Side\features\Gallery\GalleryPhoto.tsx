/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  useTheme,
  IconButton,
  Collapse,
  Tooltip,
  FormControl,
  Chip,
  Snackbar,
  Alert,
  Tabs,
  Paper,
  ImageListItem,
} from '@mui/material';
import styled from 'styled-components';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import SearchIcon from '@mui/icons-material/Search';
import Popup from '@/components/shared/Popup/Popup';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import Physics from '@/Parent-Side/assets/liveclass/Icons/Physics.svg';
import { MdAdd, MdContentCopy } from 'react-icons/md';
import { CloseIcon } from '@/theme/overrides/CustomIcons';
import { Student, StudentInfoData } from '@/config/StudentDetails';
import useSettings from '@/hooks/useSettings';
import AutorenewOutlinedIcon from '@mui/icons-material/AutorenewOutlined';
import { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import DateSelect from '@/components/shared/Selections/DateSelect';
import MenuEditDelete from '@/components/shared/Selections/MenuEditDelete';
import CreateScheduleList from '@/features/LiveClass/ScheduleList/CreateScheduleList';
import InsertDriveFileIcon from '@mui/icons-material/InsertDriveFile';
import { ImageList } from '@mui/material';

export const data = [
  {
    title: 'John Michel',
    img: 'https://images.unsplash.com/photo-1484249157003-9ef495e190c0?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    author: 'Passdaily',
    issuedOn: '12/02/2024',
    returnDate: '30/02/2024',
    bookId: '021',
    price: '200',
    fine: '50',
    status: 'Not Returned',
  },
  {
    title: 'John Michel',
    img: 'https://images.unsplash.com/photo-1484249157003-9ef495e190c0?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    author: 'Passdaily',
    issuedOn: '12/02/2024',
    returnDate: '30/02/2024',
    bookId: '021',
    price: '200',
    fine: '50',
    status: 'Returned',
  },
  {
    title: 'John Michel',
    img: 'https://images.unsplash.com/photo-1484249157003-9ef495e190c0?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    author: 'Passdaily',
    issuedOn: '12/02/2024',
    returnDate: '30/02/2024',
    bookId: '021',
    price: '200',
    fine: '50',
    status: 'Not Returned',
  },
  {
    title: 'John Michel',
    img: 'https://images.unsplash.com/photo-1484249157003-9ef495e190c0?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    author: 'Passdaily',
    issuedOn: '12/02/2024',
    returnDate: '30/02/2024',
    bookId: '021',
    price: '200',
    fine: '50',
    status: 'Returned',
  },
  {
    title: 'John Michel',
    img: 'https://images.unsplash.com/photo-1484249157003-9ef495e190c0?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    author: 'Passdaily',
    issuedOn: '12/02/2024',
    returnDate: '30/02/2024',
    bookId: '021',
    price: '200',
    fine: '50',
    status: 'Not Returned',
  },
  {
    title: 'John Michel',
    img: 'https://images.unsplash.com/photo-1484249157003-9ef495e190c0?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    author: 'Passdaily',
    issuedOn: '12/02/2024',
    returnDate: '30/02/2024',
    bookId: '021',
    price: '200',
    fine: '50',
    status: 'Returned',
  },
  {
    title: 'John Michel',
    img: 'https://images.unsplash.com/photo-1484249157003-9ef495e190c0?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    author: 'Passdaily',
    issuedOn: '12/02/2024',
    returnDate: '30/02/2024',
    bookId: '021',
    price: '200',
    fine: '50',
    status: 'Not Returned',
  },
  {
    title: 'John Michel',
    img: 'https://images.unsplash.com/photo-1484249157003-9ef495e190c0?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    author: 'Passdaily',
    issuedOn: '12/02/2024',
    returnDate: '30/02/2024',
    bookId: '021',
    price: '200',
    fine: '50',
    status: 'Not Returned',
  },
  {
    title: 'John Michel',
    img: 'https://images.unsplash.com/photo-1484249157003-9ef495e190c0?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    author: 'Passdaily',
    issuedOn: '12/02/2024',
    returnDate: '30/02/2024',
    bookId: '021',
    price: '200',
    fine: '50',
    status: 'Not Returned',
  },
  {
    title: 'John Michel',
    img: 'https://images.unsplash.com/photo-1484249157003-9ef495e190c0?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    author: 'Passdaily',
    issuedOn: '12/02/2024',
    returnDate: '30/02/2024',
    bookId: '021',
    price: '200',
    fine: '50',
    status: 'Not Returned',
  },
];

const GalleryPhotoRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  .student_card {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  }

  .Card {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    height: calc(100vh - 110px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 40px);
      flex-grow: 1;

      .card-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: auto;
        ::-webkit-scrollbar {
          width: 0px;
        }

        .MuiTableContainer-root {
          max-height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;
const years = ['2023-2024', '2024-2025'];

function GalleryPhoto() {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const [changeView, setChangeView] = useState(false);
  const [showFilter, setShowFilter] = useState(false);
  const [Delete, setDelete] = useState(false);
  const [createPopup, setCreatePopup] = useState(false);

  const [photoPopup, setPhotoPopup] = React.useState(false);

  const handleOpenPhoto = (e) => {
    setPhotoPopup(true);
    // console.log('videoFile::::----', videoFile);
  };
  const [updatedData, setData] = useState(data);

  const [selectedYear, setSelectedYear] = useState(years[0]);

  const filteredData = data.filter((item) => years.includes(item.label));

  const handleTabChange = (event, newValue) => {
    setSelectedYear(newValue);
  };

  const GalleryPhotoColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'author',
        dataKey: 'author',
        headerLabel: 'Author',
      },
      {
        name: 'issuedOn',
        dataKey: 'issuedOn',
        headerLabel: 'Issued On',
      },
      {
        name: 'returnDate',
        dataKey: 'returnDate',
        headerLabel: 'Return Date',

        // renderCell: (row) => {
        //   return (
        //     <Stack direction="row" alignItems="center" maxWidth="90%">
        //       <Tooltip title={`${row.meetingLink}`} arrow>
        //         <Chip
        //           size="small"
        //           label="40"
        //           variant="outlined"
        //           sx={{
        //             border: '0px',
        //             mr: '1px',
        //             mb: 2,
        //             backgroundColor: isLight ? theme.palette.info.lighter : theme.palette.grey[700],
        //             color: isLight ? theme.palette.info.dark : theme.palette.grey[100],
        //             width: 'auto',
        //           }}
        //         />
        //       </Tooltip>
        //     </Stack>
        //   );
        // },
      },
      {
        name: 'bookId',
        dataKey: 'bookId',
        headerLabel: 'Book Id',
      },
    ],
    [theme, isLight]
  );

  return (
    <Page title="Schedule List">
      <GalleryPhotoRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 1, md: 2 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={20} width="100%">
              Gallery
            </Typography>
          </Stack>
          <Stack>
            <Divider />
          </Stack>
          <Tabs sx={{ mt: 2 }} value={selectedYear} onChange={handleTabChange}>
            {years.map((year) => (
              <Button
                key={year}
                variant={selectedYear === year ? 'contained' : 'outlined'}
                color={selectedYear === year ? 'success' : 'secondary'}
                sx={{
                  mr: 2,
                  borderRadius: 10,
                  backgroundColor: selectedYear === year ? theme.palette.success.main : undefined,
                  color: selectedYear === year ? theme.palette.common.white : undefined,
                  '&:hover': {
                    backgroundColor: selectedYear === year ? theme.palette.success.main : undefined,
                    boxShadow: 'none',
                  },
                }}
                onClick={() => setSelectedYear(year)}
              >
                {year}
              </Button>
            ))}
          </Tabs>
          <div className="card-main-body">
            <Box className="card-container" my={2}>
              <Grid container spacing={3}>
                {updatedData.map((data, rowIndex) => (
                  <Grid item xxl={3} xl={4} lg={6} md={12} sm={6} xs={12}>
                    <Stack sx={{ cursor: 'pointer' }} onClick={() => handleOpenPhoto('')}>
                      <img
                        src="https://images.unsplash.com/photo-1492562080023-ab3db95bfbce?q=80&w=2048&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                        alt=""
                      />
                    </Stack>
                    <Stack mt={1} direction="row" justifyContent="space-between" alignItems="center">
                      <Stack>
                        <Typography variant="subtitle2" fontSize={13}>
                          {data.title}
                        </Typography>
                        <Box display="flex" alignItems="center">
                          <CalendarTodayIcon color="secondary" sx={{ fontSize: '10px', marginRight: '5px' }} />
                          <Typography
                            color="GrayText"
                            sx={{ fontFamily: 'Poppins medium', fontWeight: '500', fontSize: '10px' }}
                          >
                            12/02/2024
                          </Typography>
                        </Box>
                      </Stack>
                      <Typography variant="subtitle2" fontSize={18}>
                        05
                      </Typography>
                    </Stack>
                  </Grid>
                ))}
              </Grid>
            </Box>
          </div>
        </Card>
      </GalleryPhotoRoot>
      <Popup
        title="Alex Peter"
        size="sm"
        state={photoPopup}
        onClose={() => setPhotoPopup(false)}
        popupContent={
          <Box p={3} display="flex" gap={3}>
            <Stack>
              <img
                width={4000}
                src="https://images.unsplash.com/photo-1514471269849-fda3a4441307?q=80&w=1887&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                alt=""
              />
            </Stack>
            <Stack>
              <ImageList variant="masonry" cols={3} gap={8}>
                {updatedData.map((item) => (
                  <ImageListItem key={item.img}>
                    <img
                      srcSet={`${item.img}?w=248&fit=crop&auto=format&dpr=2 2x`}
                      src={`${item.img}?w=248&fit=crop&auto=format`}
                      alt={item.title}
                      loading="lazy"
                    />
                  </ImageListItem>
                ))}
              </ImageList>
            </Stack>
          </Box>
        }
      />
      {/* <Popup
        size="xs"
        state={Delete}
        onClose={handleClickCloseDelete}
        popupContent={<DeleteMessage message="Are you sure want to delete?" />}
      /> */}
      {/* <Popup
        size="sm"
        title="Assignment Details"
        state={createPopup}
        onClose={() => setCreatePopup(false)}
        popupContent={<View />}
      /> */}
    </Page>
  );
}

export default GalleryPhoto;
