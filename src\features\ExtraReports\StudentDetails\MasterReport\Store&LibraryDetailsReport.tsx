/* eslint-disable jsx-a11y/alt-text */
import { useCallback, useMemo } from 'react';
import { Box, Grid, Card, useTheme, Chip, Paper } from '@mui/material';
import styled from 'styled-components';
import useSettings from '@/hooks/useSettings';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { LibraryDetailsReportType, StoreDetailsReportType } from '@/types/Reports';

const StoreDetailsReportData = [
  {
    id: 1,
    product: 'Annual product',
    productPrice: 700,
    quantity: 600,
    paid: 456,
  },
  {
    id: 2,
    product: 'Annual product',
    productPrice: 700,
    quantity: 600,
    paid: 456,
  },
  {
    id: 3,
    product: 'Annual product',
    productPrice: 700,
    quantity: 600,
    paid: 456,
  },

  {
    id: 4,
    product: 'Annual product',
    productPrice: 700,
    quantity: 600,
    paid: 456,
  },
  {
    id: 5,
    product: 'Annual product',
    productPrice: 700,
    quantity: 600,
    paid: 456,
  },
  {
    id: 6,
    product: 'Annual product',
    productPrice: 700,
    quantity: 600,
    paid: 456,
  },
  {
    id: 7,
    product: 'Annual product',
    productPrice: 700,
    quantity: 600,
    paid: 456,
  },
];
const LibraryDetailsReportData = [
  {
    id: 1,
    book: 'Term 1',
    issueDate: 'Dec 25, 2023',
    expectedDate: 'Jan 02, 2024',
    return: 'Jan 25, 2024',
  },
  {
    id: 2,
    book: 'Term 1',
    issueDate: 'Dec 25, 2023',
    expectedDate: 'Jan 02, 2024',
    return: 'Jan 25, 2024',
  },
  {
    id: 3,
    book: 'Term 1',
    issueDate: 'Dec 25, 2023',
    expectedDate: 'Jan 02, 2024',
    return: 'Jan 25, 2024',
  },

  {
    id: 4,
    book: 'Term 1',
    issueDate: 'Dec 25, 2023',
    expectedDate: 'Jan 02, 2024',
    return: 'Jan 25, 2024',
  },
  {
    id: 5,
    book: 'Term 1',
    issueDate: 'Dec 25, 2023',
    expectedDate: 'Jan 02, 2024',
    return: 'Jan 25, 2024',
  },
  {
    id: 6,
    book: 'Term 1',
    issueDate: 'Dec 25, 2023',
    expectedDate: 'Jan 02, 2024',
    return: 'Jan 25, 2024',
  },
  {
    id: 7,
    book: 'Term 1',
    issueDate: 'Dec 25, 2023',
    expectedDate: 'Jan 02, 2024',
    return: 'Jan 25, 2024',
  },
];

const StoreLibraryDetailsReportRoot = styled.div`
  .ReportCard {
    display: flex;
    flex-direction: column;
    height: 350px;

    .card-main-body {
      display: flex;
      flex-direction: column;
      min-height: calc(100% - 5px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }
    }
  }
`;

type StoreLibraryProps = {
  idStoreDetails: string;
  idLibraryDetails: string;
};

function StoreLibraryDetailsReport({ idStoreDetails, idLibraryDetails }: StoreLibraryProps) {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const tableStyles1 = {
    '& th:nth-of-type(n+2):nth-of-type(-n+6), & td:nth-of-type(n+2):nth-of-type(-n+6)': {
      textAlign: 'center',
    },
    backgroundColor: isLight ? '#f7f7fe' : theme.palette.grey[900],
  };
  const tableStyles2 = {
    backgroundColor: isLight ? '#fff3fa' : theme.palette.grey[900],
  };

  const getRowKeyStoreDetailsReport = useCallback((row: StoreDetailsReportType) => row.id, []);
  const getRowKeyLibraryDetailsReport = useCallback((row: LibraryDetailsReportType) => row.id, []);

  const StoreDetailsReportColumns: DataTableColumn<StoreDetailsReportType>[] = useMemo(
    () => [
      {
        name: 'product',
        dataKey: 'product',
        headerLabel: 'Product',
      },
      {
        name: 'productPrice',
        dataKey: 'productPrice',
        headerLabel: 'Product Price',
      },
      {
        name: 'quantity',
        dataKey: 'quantity',
        headerLabel: 'Quantity',
      },
      {
        name: 'paid',
        dataKey: 'paid',
        headerLabel: 'Paid',
      },
    ],
    []
  );
  const LibraryDetailsReportColumns: DataTableColumn<LibraryDetailsReportType>[] = useMemo(
    () => [
      {
        name: 'book',
        dataKey: 'book',
        headerLabel: 'Book',
      },
      {
        name: 'issueDate',
        dataKey: 'issueDate',
        headerLabel: 'Issue Date',
      },
      {
        name: 'expectedDate',
        dataKey: 'expectedDate',
        headerLabel: 'Expected Date',
      },
      {
        name: 'return',
        dataKey: 'return',
        headerLabel: 'Return',
      },
    ],
    []
  );
  return (
    <StoreLibraryDetailsReportRoot>
      <Grid container spacing={{ xs: 0, xl: 5 }}>
        <Grid id={idStoreDetails} item xl={6} lg={12} xs={12}>
          <Card
            className="ReportCard"
            sx={{
              mt: 3,
              backgroundColor: isLight ? '#f7f7fe' : theme.palette.grey[900],
              boxShadow: 0,
              px: { xs: 3, md: 2 },
              py: { xs: 2, md: 2 },
            }}
          >
            <div className="card-main-body">
              <Box>
                <Chip
                  label="Store Details"
                  variant="filled"
                  sx={{
                    height: 25,
                    background: isLight ? theme.palette.common.black : theme.palette.common.white,
                    color: isLight ? theme.palette.common.white : theme.palette.common.black,
                  }}
                />
              </Box>
              <Paper className="card-table-container">
                <DataTable
                  columns={StoreDetailsReportColumns}
                  data={StoreDetailsReportData}
                  getRowKey={getRowKeyStoreDetailsReport}
                  fetchStatus="success"
                  tableStyles={tableStyles1}
                />
              </Paper>
            </div>
          </Card>
        </Grid>
        <Grid id={idLibraryDetails} item xl={6} lg={12} xs={12}>
          <Card
            className="ReportCard"
            sx={{
              mt: 3,
              backgroundColor: isLight ? '#fff3fa' : theme.palette.grey[900],
              boxShadow: 0,
              px: { xs: 3, md: 2 },
              py: { xs: 2, md: 2 },
            }}
          >
            <div className="card-main-body">
              <Box>
                <Chip
                  label="Library Details"
                  variant="filled"
                  sx={{
                    height: 25,
                    background: isLight ? theme.palette.common.black : theme.palette.common.white,
                    color: isLight ? theme.palette.common.white : theme.palette.common.black,
                  }}
                />
              </Box>
              <Paper className="card-table-container">
                <DataTable
                  columns={LibraryDetailsReportColumns}
                  data={LibraryDetailsReportData}
                  getRowKey={getRowKeyLibraryDetailsReport}
                  fetchStatus="success"
                  tableStyles={tableStyles2}
                />
              </Paper>
            </div>
          </Card>
        </Grid>
      </Grid>
    </StoreLibraryDetailsReportRoot>
  );
}

export default StoreLibraryDetailsReport;
