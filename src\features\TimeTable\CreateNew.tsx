/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import { Autocomplete, Box, Divider, Grid, Stack, TextField, Button, Typography, Card } from '@mui/material';
import styled from 'styled-components';
import { CLASS_SELECT, DAY_SELECT, STATUS_SELECT, SUBJECT_SELECT, YEAR_SELECT } from '@/config/Selection';
import typography from '@/theme/typography';
import Popup from '@/components/shared/Popup/Popup';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';

const CreateNewRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  h6 {
    font-weight: 600;
  }
  .MuiTextField-root:hover .MuiInputBase-input {
    cursor: text;
  }
`;

function CreateNew() {
  const [popup, setPopup] = React.useState(false);

  const handleClickOpen = () => setPopup(true);
  const handleClickClose = () => setPopup(false);

  return (
    <Page title="Create New">
      <CreateNewRoot>
        <Card elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Typography variant="h6" fontSize={17}>
            Add New to TimeTable
          </Typography>
          <Divider />
          <Grid pb={4} pt={2} container spacing={3}>
            <Grid item lg={6} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Academic Year
              </Typography>
              <Autocomplete
                options={YEAR_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select Year" />}
              />
            </Grid>
            <Grid item lg={6} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Day
              </Typography>
              <Autocomplete
                options={DAY_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select Day" />}
              />
            </Grid>
            <Grid item lg={6} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Class
              </Typography>
              <Autocomplete
                options={CLASS_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select Class" />}
              />
            </Grid>
            <Grid item lg={6} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Period
              </Typography>
              <Autocomplete
                options={Array.from({ length: 8 }, (_, index) => index + 1)}
                renderInput={(params) => <TextField {...params} placeholder="Select Period" />}
              />
            </Grid>
            <Grid item lg={6} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Subject
              </Typography>
              <Autocomplete
                options={SUBJECT_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select Type" />}
              />
            </Grid>
            <Grid item lg={6} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Status
              </Typography>
              <Autocomplete
                options={STATUS_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select Status" />}
              />
            </Grid>
          </Grid>
          <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' } }}>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="secondary" sx={{ fontFamily: typography.fontFamily }}>
                Cancel
              </Button>
              <Button onClick={handleClickOpen} variant="contained" color="primary">
                Save
              </Button>
            </Stack>
          </Box>
        </Card>
        <Popup
          size="xs"
          state={popup}
          onClose={handleClickClose}
          popupContent={<SuccessMessage message="TimeTable Updated Successfully" />}
        />
      </CreateNewRoot>
    </Page>
  );
}

export default CreateNew;
