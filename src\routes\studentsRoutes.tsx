/* eslint-disable prettier/prettier */
import Loadable from '@/components/shared/Loadable';
import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';

const ManageStudents = Loadable(lazy(() => import('@/pages/ManageStudents')));
const NewStudent = Loadable(lazy(() => import('@/features/ManageStudents/NewStudent')));
const StudentParentInfo = Loadable(lazy(() => import('@/features/ManageStudents/StudentInfo/StudentParentInfo')));
const QuickUpdateStudent = Loadable(lazy(() => import('@/features/ManageStudents/QuickUpdateStudent')));
const ClassReAllocation = Loadable(lazy(() => import('@/features/ManageStudents/StudentReAllocation/StudentClassReAllocation')));
const PromoteStudents = Loadable(lazy(() => import('@/features/ManageStudents/PromoteStudents')));
const StudentsRemark = Loadable(lazy(() => import('@/features/ManageStudents/StudentsRemark')));
const StudentsExtracurricular = Loadable(lazy(() => import('@/features/ManageStudents/StudentsExtracurricular')));

export const studentsRoutes: RouteObject[] = [
  {
    path: '/manage-students',
    element: <ManageStudents />,
    children: [
      {
        path: 'new',
        element: <NewStudent />,
      },
      {
        path: 'info',
        element: <StudentParentInfo />,
      },
      {
        path: 'download-student-info',
        element: <StudentParentInfo />,
      },
      {
        path: 'quick-update',
        element: <QuickUpdateStudent />,
      },
      {
        path: 'reallocate',
        element: <ClassReAllocation />,
      },
      {
        path: 'promote',
        element: <PromoteStudents />,
      },
      {
        path: 'remarks',
        element: <StudentsRemark />,
      },
      {
        path: 'extracurricular',
        element: <StudentsExtracurricular />,
      },
    ],
  },
];
