/* eslint-disable no-nested-ternary */
import { breakPointsMinwidth } from '@/config/breakpoints';
import styled from 'styled-components';
import Page from '@/components/shared/Page';
import React, { useEffect, useRef, useState } from 'react';
import { Grid, Stack, useTheme, TextField, Button, Typography, Box, Divider, Snackbar, Alert } from '@mui/material';
import StThereseLogo from '@/assets/SchoolLogos/StThereseLogo.png';
import axios from 'axios';
import dayjs, { Dayjs } from 'dayjs';
import CheckIcon from '@mui/icons-material/Check';
import { fetchClassList } from '@/store/Dashboard/dashboard.thunks';
import useAuth from '@/hooks/useAuth';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useReactToPrint } from 'react-to-print';
import { useLocation } from 'react-router-dom';

const AdmissionFormPrintRoot = styled.div`
  width: 100%;
  @media print {
    @page {
      size: A4;
      padding: 1mm;
      margin: 6.5mm; /* Ensure each page has margins */
      border: 1px solid black;
    }
    body {
      -webkit-print-color-adjust: exact;
    }
  }
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.grey[900]};
  /* min-height: calc(100vh - 160px); */
  /* .MuiOutlinedInput-root {
    border-radius: '10px'; // No border radius
  } */
  /* padding: 1rem; */

  .Card {
    min-height: calc(100vh - 35px);
  }
  /* @media ${breakPointsMinwidth.lg} {
    padding: 2rem;
  } */
`;

type FileDetail = {
  FileId: number;
  FileName: string;
  FileTitle: string;
};

type FileDetailsArray = FileDetail[];

export type AdmissionFormTypes = {
  Syllabus: number;
  ClassName: string;
  Surname: string;
  StudentName: string;
  FathersName: string;
  SGender: number;
  SDob: string | Dayjs;
  SdobInWords: string;
  SPlaceOfBorth: string;
  SMotherTongue: string;
  SReligion: string;
  SCaste: string;
  SNationality: string;
  SchoolLastAttended: string;
  SAddress1: string;
  SAddress2: string;
  SAddress3: string;
  StudentBloodGroup: string;
  SAadharNo: string;
  FatherName: string;
  FMotherTongue: string;
  FDob: string | Dayjs;
  FEmail: string;
  FReligion: string;
  FCaste: string;
  FQualification: string;
  FOccupation: string;
  FNameOfCompany: string;
  FCompanyYear: string;
  FCompanyMonth: string;
  FOfficeAddress: string;
  FOfficeMobile: string;
  FOfficeTelephone: string;
  FAadharNo: string;
  MotherName: string;
  MMotherTongue: string;
  MDob: string | Dayjs;
  MReligion: string;
  MCaste: string;
  MQualification: string;
  MOccupation: string;
  MNameOfCompany: string;
  MCompanyYear: string;
  MCompanyMonth: string;
  MOfficeAddress: string;
  MOfficeTelephone: string;
  MOfficeMobile: string;
  MAadharNo: string;
  MEmail: string;
  GEmail: string;
  GCaste: string;
  GAadharNo: string;
  GuardianName: string;
  GMotherTongue: string;
  GDob: string | Dayjs;
  GReligion: string;
  GQualification: string;
  GOccupation: string;
  GNameOfCompany: string;
  GCompanyYear: string;
  GCompanyMonth: string;
  GOfficeAddress: string;
  GOfficeTelephone: string;
  GOfficeMobile: string;
  Sibling1: string;
  Sibling1Std: string;
  Sibiling2: string;
  Sibling2Std: string;
  Sibling3: string;
  Sibling3Std: string;
  SchoolTransport: number;
  StopName: string;
  Vaccination1: string;
  Vaccination1Date: string | Dayjs;
  Vaccination2: string;
  Vaccination2Date: string | Dayjs;
  Vaccination3: string;
  Vaccination3Date: string | Dayjs;
  passportPhoto: string;
  birthCertificate: string; // Store birth certificate image
  studentAadhar: string; // Store student's Aadhaar
  parentAadhar: string; // Store parent's Aadhaar
  proof: string;
  cast: string;
  firstSemesterMarkSheet: string;
  RegistrationNo?: number;
};

function AdmissionFormPrint() {
  const { user } = useAuth();
  const theme = useTheme();
  const adminId: number | undefined = user?.accountId;
  const dispatch = useAppDispatch();

  const [regNoError, setRegNoError] = useState(false);
  const [regNoErrorMsg, setRegNoregNoErrorMsg] = useState('');

  useEffect(() => {
    dispatch(fetchClassList(adminId));
  }, [dispatch, adminId]);

  const location = useLocation();
  const details = location.state?.details || ''; // Access the passed details
  const [searchNumber, setSearchNumber] = useState(details);
  const [studentData, setStudentData] = useState<AdmissionFormTypes | undefined>();
  const [fileDetails, setFileDetails] = useState<FileDetailsArray>([]);
  const [loading, setLoading] = useState(false);

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchNumber(event.target.value);
  };

  const onSearch = async () => {
    if (!searchNumber.trim()) {
      setRegNoregNoErrorMsg('Please enter a valid registration number.');
      return;
    }

    try {
      setLoading(true);
      const response = await axios.get(
        `https://thereseregapi.pasdaily.in/ElixirApi/StudentSet/SearchAdmission?RegistrationNo=${searchNumber}`
      );

      console.log('API Response:', response.data);

      if (response.data?.RegistrationDetails) {
        setStudentData(response.data.RegistrationDetails);
        setFileDetails(response.data.FileDetails);
      } else {
        setRegNoError(true);
        setRegNoregNoErrorMsg('No student found with this registration number.');
        setStudentData(null);
      }
    } catch (error) {
      setRegNoError(true);
      console.error('Error fetching student data:', error);
      setRegNoregNoErrorMsg('Failed to fetch data. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Detect active section on scroll
  useEffect(() => {
    onSearch();
  }, []);

  const formRef = useRef(null);

  const handlePrint = useReactToPrint({
    content: () => formRef.current,
  });
  return (
    <Page title="AdmissionFormPrint">
      <AdmissionFormPrintRoot>
        <Stack direction="row" justifyContent="end" p={2}>
          <TextField
            sx={{ width: 100, mr: 2 }}
            placeholder="Enter No"
            type="number"
            size="small"
            variant="outlined"
            value={searchNumber}
            onChange={handleSearchChange}
          />
          <Button variant="contained" color="primary" onClick={onSearch} disabled={searchNumber === '' || loading}>
            {loading ? 'Loading...' : 'Search'}
          </Button>
        </Stack>
        <Box
          ref={formRef}
          sx={{
            '@media print': {
              //   border: '1px solid black',
              padding: 0,
              //   margin: 5,
            },
          }}
        >
          <Stack alignItems="center" className="container-fluid">
            <img width={100} src={StThereseLogo} alt="StThereseLogo" />
            <Typography
              textAlign="center"
              fontSize={10}
              bgcolor={theme.palette.error.main}
              color={theme.palette.common.white}
              px={1}
              variant="h5"
              fontWeight={600}
            >
              ST. THERESE CONVENT SCHOOL
            </Typography>
            <Typography
              textAlign="center"
              fontSize={10}
              color={theme.palette.error.main}
              fontWeight={600}
              mt={1}
              variant="h5"
            >
              DOMBIVLI
            </Typography>
            <Typography
              textAlign="center"
              fontWeight={600}
              mt={1}
              variant="h4"
              fontSize={{ xs: '20px', sm: '28px', md: '2.125rem' }}
            >
              ST. THERESE CONVENT SCHOOL
            </Typography>
            <Typography
              fontSize={{ xs: '13px', sm: '16px', md: '18px' }}
              textAlign="center"
              fontWeight={600}
              mt={1}
              variant="h6"
            >
              Near Premier Colony, Kolegaon, 1 Dombivli East, Thane (DIst) <br /> Maharashtra-421 204
            </Typography>
          </Stack>
          <Typography
            className="container-fluid"
            bgcolor={theme.palette.grey[300]}
            textAlign="center"
            fontWeight={600}
            mt={2}
            variant="h5"
            py={3}
            fontSize={{ xs: '16px', sm: '18px', md: '1.5rem' }}
          >
            <u>APPLICATION FOR ADMISSION 2025-2026</u>
          </Typography>
          <Stack px={{ xs: 0, md: 10 }}>
            <Stack direction="row" gap={1} className="container-fluid">
              <Typography fontWeight={600} mt={3} variant="subtitle2" fontSize={12}>
                Reg No.&nbsp;:
              </Typography>
              <Typography fontWeight={600} mt={3} color="error" variant="subtitle2" fontSize={12}>
                {studentData?.RegistrationNo}
              </Typography>
            </Stack>
            <Stack className="container-fluid">
              <Typography fontWeight={600} mt={2} variant="subtitle2" fontSize={12}>
                Please admit my son/daughter/ward, details about whom are given below:
              </Typography>
              <Typography fontWeight={600} mt={1} variant="subtitle2" fontSize={12}>
                I declare that the following data are correct
              </Typography>
            </Stack>
            <Stack className="container-fluid" sx={{ mt: 1 }}>
              <form noValidate>
                <Typography color={theme.palette.error.darker} pt={2} variant="h6" fontSize={12}>
                  1. Details of Candidate
                </Typography>
                <Divider sx={{ border: 1 }} />
                <Grid container direction="row" justifyContent="start">
                  <Grid item lg={12} xs={12}>
                    <Box
                      component="form"
                      noValidate
                      autoComplete="off"
                      display="flex"
                      flexDirection="column"
                      justifyContent="start"
                      alignItems="start"
                      gap={2}
                      sx={{ mt: 2 }}
                    >
                      <Grid container spacing={2}>
                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Board&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.Syllabus === 1 ? 'State' : ''}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Standard in which admission is sought&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp; {studentData?.ClassName}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Surname&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.Surname}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Name&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.StudentName}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Father&apos;s Name&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.FathersName}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Gender&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.SGender === 0 ? 'Male' : studentData?.SGender === 1 ? 'Female' : ''}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Date of Birth (In figure)&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.SDob ? dayjs(studentData.SDob).format('DD/MM/YYYY') : ''}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Date of Birth (In words)&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.SdobInWords}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Place of Birth&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.SPlaceOfBorth}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Mother Tongue&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.SMotherTongue}
                          </Typography>
                        </Grid>
                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Religion&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.SReligion}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Caste&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.SCaste}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Nationality&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.SNationality}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            School Last Attended&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.SchoolLastAttended}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Full Residential Address&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.SAddress1}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Locality&nbsp;:
                          </Typography>
                        </Grid>

                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.SAddress2}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Pincode&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.SAddress3}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Blood Group of the Child&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.StudentBloodGroup}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Candidate Aadhaar Number&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.SAadharNo}
                          </Typography>
                        </Grid>
                      </Grid>
                    </Box>
                  </Grid>
                </Grid>

                <Typography color={theme.palette.error.darker} textAlign="start" pt={5} variant="h6" fontSize={12}>
                  2. About father of the student whose admission is sought
                </Typography>
                <Divider sx={{ border: 1 }} />
                <Grid container direction="row" justifyContent="start" mb={5}>
                  <Grid item lg={12} xs={12}>
                    <Box
                      component="form"
                      noValidate
                      autoComplete="off"
                      display="flex"
                      flexDirection="column"
                      justifyContent="start"
                      alignItems="start"
                      gap={2}
                      sx={{ mt: 2 }}
                    >
                      <Grid container spacing={2}>
                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Name&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.FatherName}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Mother Tongue&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.FMotherTongue}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Date of Birth (In figure)&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.FDob ? dayjs(studentData.FDob).format('DD/MM/YYYY') : ''}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Father&apos;s Email &nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.FEmail}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Religion&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.FReligion}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Caste&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.FCaste}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Academic Qualification&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.FQualification}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Occupation & Designation&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.FOccupation}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Name of Present Company/Concern Working&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.FNameOfCompany}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            In Years&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.FCompanyYear}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Annual Income&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.FCompanyMonth}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Full Office Address&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.FOfficeAddress}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Mobile Number&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.FOfficeMobile}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Telephone Number&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.FOfficeTelephone}
                          </Typography>
                        </Grid>
                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Father&apos;s Aadhaar Number &nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.FAadharNo}
                          </Typography>
                        </Grid>
                      </Grid>
                    </Box>
                  </Grid>
                </Grid>

                <Typography color={theme.palette.error.darker} textAlign="start" pt={5} variant="h6" fontSize={12}>
                  3. About mother of the student whose admission is sought
                </Typography>
                <Divider sx={{ border: 1 }} />
                <Grid container direction="row" justifyContent="start" mb={5}>
                  <Grid item lg={12} xs={12}>
                    <Box
                      component="form"
                      noValidate
                      autoComplete="off"
                      display="flex"
                      flexDirection="column"
                      justifyContent="start"
                      alignItems="start"
                      gap={2}
                      sx={{ mt: 2 }}
                    >
                      <Grid container spacing={2}>
                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Name&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.MotherName}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Mother Tongue&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.MMotherTongue}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Date of Birth (In figure)&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.MDob ? dayjs(studentData.MDob).format('DD/MM/YYYY') : ''}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Mother&apos;s Email &nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.MEmail}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Religion&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.MReligion}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Caste&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.MCaste}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Academic Qualification&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.MQualification}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Occupation & Designation&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.MOccupation}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Name of Present Company/Concern Working&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.MNameOfCompany}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            In Years&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.MCompanyYear}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Annual Income&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.MCompanyMonth}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Full Office Address&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.MOfficeAddress}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Mobile Number&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.MOfficeMobile}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Telephone Number&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.MOfficeTelephone}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Mother&apos;s Aadhar Number&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.MAadharNo}
                          </Typography>
                        </Grid>
                      </Grid>
                    </Box>
                  </Grid>
                </Grid>

                <Typography color={theme.palette.error.darker} textAlign="start" pt={5} variant="h6" fontSize={12}>
                  4. About Guardian of the student whose admission is sought
                </Typography>
                <Divider sx={{ border: 1 }} />
                <Grid container direction="row" justifyContent="center">
                  <Grid item lg={12} xs={12}>
                    <Box
                      component="form"
                      noValidate
                      autoComplete="off"
                      display="flex"
                      flexDirection="column"
                      justifyContent="center"
                      alignItems="center"
                      gap={2}
                      sx={{ mt: 2 }}
                    >
                      <Grid container spacing={2}>
                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Name&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.GuardianName}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Mother Tongue&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.GMotherTongue}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Date of Birth (In figure)&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.GDob ? dayjs(studentData.GDob).format('DD/MM/YYYY') : ''}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Gaurdian&apos;s Email&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.GEmail}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Religion&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.GReligion}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Caste&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.GCaste}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Academic Qualification&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.GQualification}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Occupation & Designation&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.GOccupation}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Name of Present Company/Concern Working&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.GNameOfCompany}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            In Years&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.GCompanyYear}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Annual Income&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.GCompanyMonth}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Full Office Address&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.GOfficeAddress}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Mobile Number&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.GOfficeMobile}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Telephone Number&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.GOfficeTelephone}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Guardian&apos;s Aadhar Number&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.GAadharNo}
                          </Typography>
                        </Grid>
                      </Grid>
                    </Box>
                  </Grid>
                </Grid>

                <Typography color={theme.palette.error.darker} textAlign="start" pt={5} variant="h6" fontSize={12}>
                  5. Brothers/Sisters studying in this school[attach photocopies of ID cards]
                </Typography>
                <Divider sx={{ border: 1 }} />
                <Grid container direction="row" justifyContent="center">
                  <Grid item lg={12} xs={12}>
                    <Box
                      component="form"
                      noValidate
                      autoComplete="off"
                      display="flex"
                      flexDirection="column"
                      justifyContent="center"
                      alignItems="center"
                      gap={2}
                      sx={{ mt: 2 }}
                    >
                      <Grid container spacing={2}>
                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Name&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.Sibling1}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Standard&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.Sibling1Std}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Name&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.Sibiling2}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Standard&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.Sibling2Std}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Name&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.Sibling3}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Standard&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.Sibling3Std}
                          </Typography>
                        </Grid>
                      </Grid>
                    </Box>
                  </Grid>
                </Grid>
                <Typography color={theme.palette.error.darker} textAlign="start" pt={5} variant="h6" fontSize={12}>
                  6. Whether your child avail school&apos;s transport
                </Typography>
                <Divider sx={{ border: 1 }} />
                <Grid container direction="row" justifyContent="start">
                  <Grid item lg={12} xs={12}>
                    <Box
                      component="form"
                      noValidate
                      autoComplete="off"
                      display="flex"
                      flexDirection="column"
                      justifyContent="start"
                      alignItems="start"
                      gap={2}
                      sx={{ mt: 2 }}
                    >
                      <Grid container spacing={2}>
                        <Grid item lg={5} textAlign="end">
                          <Box sx={{ display: 'flex', justifyContent: 'end', gap: 2, ml: { xs: 0, lg: 45 }, mt: 3 }}>
                            <Button
                              sx={{ width: 100 }}
                              variant={studentData?.SchoolTransport === 1 ? 'contained' : 'outlined'}
                              color="secondary"
                              startIcon={studentData?.SchoolTransport === 1 ? <CheckIcon /> : null}
                              disabled
                            >
                              Yes
                            </Button>
                            <Button
                              sx={{ width: 100 }}
                              variant={studentData?.SchoolTransport === 0 ? 'contained' : 'outlined'}
                              color="secondary"
                              startIcon={studentData?.SchoolTransport === 0 ? <CheckIcon /> : null} // Conditionally show CheckIcon
                              disabled
                            >
                              No
                            </Button>
                          </Box>
                        </Grid>
                      </Grid>
                      {studentData?.SchoolTransport === 1 && (
                        <Grid container spacing={2}>
                          <Grid item xs={4.5} textAlign="end">
                            <Typography variant="h6" fontSize={13}>
                              Enter Stop Name&nbsp;:
                            </Typography>
                          </Grid>
                          <Grid item xs={6} textAlign="start">
                            <Typography variant="h6" fontSize={13}>
                              {studentData?.StopName}
                            </Typography>
                          </Grid>
                        </Grid>
                      )}
                    </Box>
                  </Grid>
                </Grid>
                <Typography color={theme.palette.error.darker} textAlign="start" pt={5} variant="h6" fontSize={12}>
                  8. Last three vaccination taken
                </Typography>
                <Divider sx={{ border: 1 }} />
                <Grid container direction="row" justifyContent="start">
                  <Grid item lg={12} xs={12}>
                    <Box
                      component="form"
                      noValidate
                      autoComplete="off"
                      display="flex"
                      flexDirection="column"
                      justifyContent="start"
                      alignItems="start"
                      gap={2}
                      sx={{ mt: 2 }}
                    >
                      <Grid container spacing={2}>
                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Name&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.Vaccination1}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Date&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;
                            {studentData?.Vaccination1Date
                              ? dayjs(studentData.Vaccination1Date).format('DD/MM/YYYY')
                              : ''}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Name&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.Vaccination2}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Date&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;
                            {studentData?.Vaccination2Date
                              ? dayjs(studentData.Vaccination2Date).format('DD/MM/YYYY')
                              : ''}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Name&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{studentData?.Vaccination3}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Date&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;
                            {studentData?.Vaccination3Date
                              ? dayjs(studentData.Vaccination3Date).format('DD/MM/YYYY')
                              : ''}
                          </Typography>
                        </Grid>
                      </Grid>
                    </Box>
                  </Grid>
                </Grid>

                <Typography color={theme.palette.error.darker} textAlign="start" pt={5} variant="h6" fontSize={12}>
                  9. Upload Documents
                </Typography>
                <Divider sx={{ border: 1 }} />
                <Grid container direction="row" justifyContent="start">
                  <Grid item lg={12} xs={12}>
                    <Box
                      component="form"
                      noValidate
                      autoComplete="off"
                      display="flex"
                      flexDirection="column"
                      justifyContent="start"
                      alignItems="start"
                      gap={2}
                      sx={{ mt: 2 }}
                    >
                      <Grid container spacing={2}>
                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Passport size photo of child&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{fileDetails[0]?.FileTitle === 'Profile Photo' ? 'Attached' : ''}
                          </Typography>
                        </Grid>

                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Birth certificate&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{fileDetails[1]?.FileTitle === 'Student Birth Certificate' ? 'Attached' : ''}
                          </Typography>
                        </Grid>
                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Aadhar Card of Student&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{fileDetails[2]?.FileTitle === 'Student Adhaar Card' ? 'Attached' : ''}
                          </Typography>
                        </Grid>
                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Aadhar card of Parent&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{fileDetails[3]?.FileTitle === 'Parent Adhaar Card' ? 'Attached' : ''}
                          </Typography>
                        </Grid>
                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Address proof&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{fileDetails[4]?.FileTitle === 'Address Proof' ? 'Attached' : ''}
                          </Typography>
                        </Grid>
                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Caste Certificate&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{fileDetails[5]?.FileTitle === 'Cast Certificate' ? 'Attached' : ''}
                          </Typography>
                        </Grid>
                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            First Semester Mark Sheet&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;{fileDetails[6]?.FileTitle === 'First Semester Mark Sheet' ? 'Attached' : ''}
                          </Typography>
                        </Grid>
                      </Grid>
                    </Box>
                  </Grid>
                </Grid>

                <Divider sx={{ border: 1, mt: 5 }} />
                <Grid container direction="row" justifyContent="start">
                  <Grid item lg={12} xs={12}>
                    <Box
                      component="form"
                      noValidate
                      autoComplete="off"
                      display="flex"
                      flexDirection="column"
                      justifyContent="start"
                      alignItems="start"
                      gap={2}
                      sx={{ mt: 2 }}
                    >
                      <Grid container spacing={2}>
                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Date on which application is submitted&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;
                          </Typography>
                        </Grid>
                        <Typography pl={2} mt={5} variant="h6" fontSize={11}>
                          I have read the rules of the school given in the prospectus and I do hereby undertake to
                          follow them in the event of my application being considered favourable. I also undertake to
                          abide by the other rules, which may come up from time to time as modifications of the existing
                          one, or additions to t same, according to the discretion of the management. I further
                          undertake to pay the school fees and bus fees in time and in the manner prescribed in the
                          relevant rules.
                        </Typography>
                        <Grid mt={5} item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            Signature of the applicant&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid mt={5} item xs={3} textAlign="start">
                          <Typography borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;
                          </Typography>
                        </Grid>
                      </Grid>
                    </Box>
                  </Grid>
                </Grid>

                <Divider sx={{ border: 1, mt: 5 }} />
                <Typography color={theme.palette.error.darker} textAlign="start" pt={5} variant="h6" fontSize={12}>
                  For office use only
                </Typography>
                <Grid container direction="row" justifyContent="start">
                  <Grid item lg={12} xs={12}>
                    <Typography variant="h6" fontSize={11}>
                      Recommendations&nbsp;:
                    </Typography>
                    <Box
                      component="form"
                      noValidate
                      autoComplete="off"
                      display="flex"
                      flexDirection="column"
                      justifyContent="start"
                      alignItems="start"
                      gap={2}
                      sx={{ mt: 2 }}
                    >
                      <Typography variant="h6" fontSize={11} pl={10}>
                        Certificates enclosed&nbsp;:
                      </Typography>
                      <Grid container spacing={2}>
                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            1.&nbsp;
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography variant="h6" fontSize={11}>
                            Passport size photo of the child and child with parents
                          </Typography>
                        </Grid>
                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            2.&nbsp;
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography variant="h6" fontSize={11}>
                            Official certificate of birth: (Original and Xerox attested)&nbsp;:
                          </Typography>
                        </Grid>
                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            3.&nbsp;
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography variant="h6" fontSize={11}>
                            Adhar card Xerox attested
                          </Typography>
                        </Grid>
                        <Grid item xs={4.5} textAlign="end">
                          <Typography variant="h6" fontSize={11}>
                            4.&nbsp;
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="start">
                          <Typography variant="h6" fontSize={11}>
                            PAN Card Xerox of parents attested.
                          </Typography>
                        </Grid>
                      </Grid>
                      <Box width="100%" display="flex" justifyContent="space-between" alignItems="center" mt={5}>
                        <Stack mt={5} direction="row" gap={2}>
                          <Typography variant="h6" fontSize={11}>
                            Form Receipt No&nbsp;:
                          </Typography>
                          <Typography width={150} borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;
                          </Typography>
                        </Stack>

                        <Stack mt={5} direction="row" gap={2}>
                          <Typography variant="h6" fontSize={11}>
                            Date&nbsp;:
                          </Typography>
                          <Typography width={150} borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;
                          </Typography>
                        </Stack>
                        <Stack mt={5} direction="row" gap={2}>
                          <Typography variant="h6" fontSize={11}>
                            Amount&nbsp;:
                          </Typography>
                          <Typography width={150} borderBottom={1} variant="h6" fontSize={11}>
                            &nbsp;
                          </Typography>
                        </Stack>
                      </Box>
                    </Box>
                  </Grid>
                </Grid>
              </form>
            </Stack>
          </Stack>
        </Box>
        <Box my={5} sx={{ display: 'flex', gap: 2, justifyContent: 'center', textAlign: 'center' }}>
          <Button onClick={handlePrint} variant="outlined" color="success" sx={{ my: 3, width: 100 }}>
            Print
          </Button>
        </Box>
      </AdmissionFormPrintRoot>
      <Snackbar
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
        open={regNoError}
        autoHideDuration={3000}
        onClose={() => setRegNoError(false)}
        // message="Receipt number already exist"
      >
        <Alert severity="error" variant="filled">
          <Typography variant="subtitle2" display="flex" alignItems="center">
            {regNoErrorMsg}
          </Typography>
        </Alert>
      </Snackbar>
    </Page>
  );
}

export default AdmissionFormPrint;
