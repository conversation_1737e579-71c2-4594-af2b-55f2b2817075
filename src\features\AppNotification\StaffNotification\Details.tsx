import React from 'react';
import Typography from '@mui/material/Typography';
import { Stack, TextField, Grid, Box } from '@mui/material';
import styled, { useTheme } from 'styled-components';

const DetailsRoot = styled.div`
  width: 100%;
  padding: 0.5rem 1.5rem;
`;

export const Details = () => {
  const theme = useTheme();
  return (
    <DetailsRoot>
      <Box sx={{ pt: 3 }}>
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Typography variant="h6" fontWeight={800}>
            Details
          </Typography>
          <Typography variant="h6" fontWeight={500} fontSize={13} sx={{ color: theme.palette.grey[500] }}>
            Date : 31/06/2023
          </Typography>
        </Stack>
        <Grid container spacing={3} pt={2}>
          <Grid item lg={6}>
            <Typography variant="h6" fontSize={14}>
              Subject
            </Typography>
            <TextField fullWidth id="" label="" value="SMS Inbox" />
          </Grid>
          <Grid item lg={6}>
            <Typography variant="h6" fontSize={14}>
              Mail Type
            </Typography>
            <TextField fullWidth id="" label="" value="Staff" />
          </Grid>
          <Grid item lg={12}>
            <Typography variant="h6" fontSize={14}>
              Content
            </Typography>
            <TextField
              fullWidth
              multiline
              minRows={2}
              value="Lorem Ipsum is simply dummy text of the printing and typesetting industry. "
              InputProps={{ inputProps: { style: { resize: 'both' } } }}
            />
          </Grid>
        </Grid>
      </Box>
    </DetailsRoot>
  );
};
