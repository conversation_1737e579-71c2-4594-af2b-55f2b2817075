/* eslint-disable react/self-closing-comp */
import styled from 'styled-components';
import Typography from '@mui/material/Typography';
import { Swiper, SwiperSlide } from 'swiper/react';
import KeyboardArrowLeftIcon from '@mui/icons-material/KeyboardArrowLeft';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import { Avatar, Stack, Box, Button, Chip, TextField, IconButton } from '@mui/material';
import 'swiper/css';
import 'swiper/css/effect-coverflow';
import 'swiper/css/pagination';
import 'swiper/css/navigation';
import { Pagination, Navigation } from 'swiper';
import React, { useEffect, useRef, useState } from 'react';
import { MarkRegisterCEDataType } from '@/types/ExamCenter';

const IndivitualEnrollCERoot = styled.div<{ theme: any }>`
  padding: 1;
  .swiper_container {
    position: relative;
    padding: 1rem;
  }
  .swiper-slide {
    position: relative;
    /* padding: 1rem 0rem; */
  }
  .swiper-button-next,
  .swiper-button-prev {
    position: absolute;
    top: var(--swiper-navigation-top-offset, 50%);
    margin-top: calc(0px - (var(--swiper-navigation-size) / 2));
    z-index: 10;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .events-card {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[800]};
    border: 1px solid
      ${(props) => (props.theme.themeMode === 'light' ? props.theme.palette.grey[300] : props.theme.palette.grey[900])};
  }
  .swiper-button-prev,
  .swiper-button-next {
    height: 30px;
    width: 30px;
    color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.primary.main : props.theme.palette.primary.main};
  }
`;

interface IndivitualEnrollCEProps {
  data?: MarkRegisterCEDataType[];
  setData: React.Dispatch<React.SetStateAction<MarkRegisterCEDataType[]>>;
  OnClose: () => void;
  sendMarkRegisterData: (data: any, mode: 'single' | 'multiple') => void;
  passMark?: string;
  outOfMark?: string;
  passMarkCE?: string;
  outOfMarkCE?: string;
  currentIndex?: any;
  setCurrentIndex?: any;
}

const IndivitualEnrollCE = ({
  currentIndex,
  setCurrentIndex,
  data,
  setData,
  OnClose,
  sendMarkRegisterData,
  passMark,
  outOfMark,
  passMarkCE,
  outOfMarkCE,
}: IndivitualEnrollCEProps) => {
  // useEffect(() => {
  //   if (dashboardEventsListStatus === 'idle') {
  //     dispatch(fetchDashboardEvents(adminId));
  //   }
  // }, [dispatch, dashboardEventsListStatus, adminId]);
  const swiperRef = useRef<import('swiper').Swiper | null>(null);
  const textFieldRefs = useRef<(HTMLInputElement | null)[][]>([]);

  useEffect(() => {
    textFieldRefs.current[currentIndex][0]?.focus();
  }, [currentIndex]);

  const handleNext = () => {
    if (currentIndex < data.length - 1) {
      setCurrentIndex((prev) => prev + 1);
      swiperRef.current?.slideNext();
      // const updatedData = data.map((item, idx) => {
      //   if (idx === currentIndex + 1) {
      //     return {
      //       ...item,
      //       periodicTest: '',
      //       multipleAssesment: '',
      //       portFolio: '',
      //       subjectEnrichment: '',
      //       scoredMarkTheory: '',
      //     };
      //   }
      //   return item;
      // });
      // setData(updatedData);
    }
  };

  const handlePrevious = () => {
    if (currentIndex > 0) {
      setCurrentIndex((prev) => prev - 1);
      swiperRef.current?.slidePrev();
    }
  };

  const handleKeyPress = (event: React.KeyboardEvent<HTMLDivElement>, index: number) => {
    if (event.key === 'Enter') {
      swiperRef.current?.slideNext();
      // const updatedData = data.map((item, idx) => {
      //   if (idx === currentIndex + 1) {
      //     return {
      //       ...item,
      //       periodicTest: '',
      //       multipleAssesment: '',
      //       portFolio: '',
      //       subjectEnrichment: '',
      //       scoredMarkTheory: '',
      //     };
      //   }
      //   return item;
      // });
      // setData(updatedData);
      handleNext();
      sendMarkRegisterData(data, 'single');
      // setTimeout(() => {
      textFieldRefs.current[index][0]?.focus();
      // }, 700);
    }
  };

  return (
    <IndivitualEnrollCERoot>
      <Swiper
        spaceBetween={16}
        effect="coverflow"
        grabCursor
        breakpoints={{
          299: {
            slidesPerView: 1,
          },
          499: {
            slidesPerView: 1,
          },
          999: {
            slidesPerView: 1,
          },
          1599: {
            slidesPerView: 1,
          },
        }}
        coverflowEffect={{
          rotate: 0,
          depth: 0,
          stretch: 0,
        }}
        pagination={{ el: '.swiper-pagination', clickable: true }}
        navigation={{
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev',
          // clickable: true,
        }}
        modules={[Pagination, Navigation]}
        className="swiper_container "
        onSwiper={(swiper) => {
          swiperRef.current = swiper;
        }}
      >
        {/* // dashboardEventsListStatus === 'loading'
          //   ? [1, 2, 3].map((event) => (
          //       <SwiperSlide key={event} className="swiper-slide ">
          //         <Card sx={{ boxShadow: '0' }} className="events-card">
          //           <Skeleton sx={{ height: 170 }} animation="wave" variant="rectangular" />
          //           <CardContent>
          //             <Skeleton animation="wave" height={10} style={{ mb: 10, mt: 10 }} />
          //             <Skeleton animation="wave" height={10} width="80%" />
          //             <Stack direction="row" display="flex" alignItems="center" justifyContent="space-between">
          //               <Box display="flex" alignItems="center">
          //                 <Skeleton animation="wave" height={10} width="80%" />
          //               </Box>
          //               <Box display="flex" alignItems="center">
          //                 <Skeleton animation="wave" height={10} width="80%" />
          //               </Box>
          //             </Stack>
          //           </CardContent>
          //         </Card>
          //       </SwiperSlide>
          //     ))
          //   : */}
        <SwiperSlide className="swiper-slide ">
          <Box display="flex" flexDirection="column" textAlign="center" gap={1}>
            <Stack direction="row" justifyContent="center" gap={2} alignItems="center">
              <Box
                // border={2}
                borderRadius={10}
                p={1.5}
                sx={{
                  position: 'relative',
                  overflow: 'hidden',
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    borderRadius: 'inherit',
                    padding: '1.3px', // Adjust to control border thickness
                    WebkitMaskComposite: 'xor',
                    maskComposite: 'exclude',
                  },
                }}
              >
                <Avatar
                  sx={{
                    width: 100,
                    height: 100,
                    // boxShadow: '0px 0px 15px #fff '
                  }}
                  alt={data[currentIndex].studentName}
                  src={data[currentIndex].studentImage}
                />
              </Box>
            </Stack>
            <Stack direction="column" alignItems="center" gap={1}>
              <Typography variant="h6" fontSize={20}>
                {data[currentIndex].studentName}
              </Typography>

              <Stack direction="row" alignItems="center" gap={2}>
                <Chip
                  size="small"
                  label={data[currentIndex].className}
                  // variant="contained"
                  color="success"
                />
                <Chip
                  size="small"
                  label="English"
                  // variant="contained"
                  color="info"
                />
              </Stack>
              <Stack direction="row" alignItems="center" gap={2}>
                <Typography variant="subtitle1" fontSize={13}>
                  Pass-Mark&nbsp;(CE&nbsp;):{' '}
                  <span style={{ fontWeight: 'bold' }}>{passMarkCE === '' ? 'N/A' : passMarkCE}</span>
                </Typography>
                <Typography variant="subtitle1" fontSize={13}>
                  Out-of-Mark(CE):{' '}
                  <span style={{ fontWeight: 'bold' }}> {outOfMarkCE === '' ? 'N/A' : outOfMarkCE}</span>
                </Typography>
              </Stack>
              <Stack direction="row" alignItems="center" gap={2}>
                <Typography variant="subtitle1" fontSize={13}>
                  Pass-Mark(TE): <span style={{ fontWeight: 'bold' }}>{passMark === '' ? 'N/A' : passMark}</span>
                </Typography>
                <Typography variant="subtitle1" fontSize={13}>
                  Out-of-Mark(TE): <span style={{ fontWeight: 'bold' }}>{outOfMark === '' ? 'N/A' : outOfMark}</span>
                </Typography>
              </Stack>
              <Stack direction="row" alignItems="center" gap={2} mt={2}>
                <Box display="flex" alignItems="center" gap={2}>
                  <Typography variant="subtitle2" fontSize={12}>
                    PT:
                  </Typography>
                  <TextField
                    sx={{
                      maxWidth: '150px',
                      '& .MuiInputBase-root': {
                        height: '30px',
                      },
                      '& .MuiOutlinedInput-input': {
                        padding: '5px',
                      },
                    }}
                    inputRef={(el) => {
                      if (!textFieldRefs.current[currentIndex]) {
                        textFieldRefs.current[currentIndex] = [];
                      }
                      textFieldRefs.current[currentIndex][0] = el;
                    }}
                    onKeyPress={(event) => {
                      if (event.key === 'Enter') {
                        event.preventDefault();
                        textFieldRefs.current[currentIndex][1]?.focus();
                      }
                    }}
                    value={data[currentIndex].periodicTest !== 'N' ? data[currentIndex].periodicTest : ''}
                    onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                      const updatedData = data.map((item) => {
                        if (item.studentId === data[currentIndex].studentId) {
                          return { ...item, periodicTest: event.target.value };
                        }
                        return item;
                      });
                      setData(updatedData);
                    }}
                  />
                </Box>
                <Box display="flex" alignItems="center" gap={2}>
                  <Typography variant="subtitle2" fontSize={12}>
                    MA:
                  </Typography>
                  <TextField
                    sx={{
                      maxWidth: '150px',
                      '& .MuiInputBase-root': {
                        height: '30px',
                      },
                      '& .MuiOutlinedInput-input': {
                        padding: '5px',
                      },
                    }}
                    inputRef={(el) => {
                      if (!textFieldRefs.current[currentIndex]) {
                        textFieldRefs.current[currentIndex] = [];
                      }
                      textFieldRefs.current[currentIndex][1] = el;
                    }}
                    onKeyPress={(event) => {
                      if (event.key === 'Enter') {
                        event.preventDefault();
                        textFieldRefs.current[currentIndex][2]?.focus();
                      }
                    }}
                    value={data[currentIndex].multipleAssesment !== 'N' ? data[currentIndex].multipleAssesment : ''}
                    onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                      const updatedData = data.map((item) => {
                        if (item.studentId === data[currentIndex].studentId) {
                          return { ...item, multipleAssesment: event.target.value };
                        }
                        return item;
                      });
                      setData(updatedData);
                    }}
                  />
                </Box>
                <Box display="flex" alignItems="center" gap={2}>
                  <Typography variant="subtitle2" fontSize={12}>
                    PO:
                  </Typography>
                  <TextField
                    sx={{
                      maxWidth: '150px',
                      '& .MuiInputBase-root': {
                        height: '30px',
                      },
                      '& .MuiOutlinedInput-input': {
                        padding: '5px',
                      },
                    }}
                    inputRef={(el) => {
                      if (!textFieldRefs.current[currentIndex]) {
                        textFieldRefs.current[currentIndex] = [];
                      }
                      textFieldRefs.current[currentIndex][2] = el;
                    }}
                    onKeyPress={(event) => {
                      if (event.key === 'Enter') {
                        event.preventDefault();
                        textFieldRefs.current[currentIndex][3]?.focus();
                      }
                    }}
                    value={data[currentIndex].portFolio !== 'N' ? data[currentIndex].portFolio : ''}
                    onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                      const updatedData = data.map((item) => {
                        if (item.studentId === data[currentIndex].studentId) {
                          return { ...item, portFolio: event.target.value };
                        }
                        return item;
                      });
                      setData(updatedData);
                    }}
                  />
                </Box>
                <Box display="flex" alignItems="center" gap={2}>
                  <Typography variant="subtitle2" fontSize={12}>
                    SE:
                  </Typography>
                  <TextField
                    sx={{
                      maxWidth: '150px',
                      '& .MuiInputBase-root': {
                        height: '30px',
                      },
                      '& .MuiOutlinedInput-input': {
                        padding: '5px',
                      },
                    }}
                    inputRef={(el) => {
                      if (!textFieldRefs.current[currentIndex]) {
                        textFieldRefs.current[currentIndex] = [];
                      }
                      textFieldRefs.current[currentIndex][3] = el;
                    }}
                    onKeyPress={(event) => {
                      if (event.key === 'Enter') {
                        event.preventDefault();
                        textFieldRefs.current[currentIndex][4]?.focus();
                      }
                    }}
                    value={data[currentIndex]?.subjectEnrichment !== 'N' ? data[currentIndex]?.subjectEnrichment : ''}
                    onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                      const updatedData = data.map((item) => {
                        if (item.studentId === data[currentIndex]?.studentId) {
                          return { ...item, subjectEnrichment: event.target.value };
                        }
                        return item;
                      });
                      setData(updatedData);
                    }}
                  />
                </Box>
              </Stack>
              <Box display="flex" alignItems="center" gap={2} mt={1} mb={4}>
                <Typography variant="subtitle2" fontSize={12}>
                  Enter Mark :
                </Typography>
                <TextField
                  sx={{
                    maxWidth: '150px',
                    '& .MuiInputBase-root': {
                      height: '30px',
                    },
                    '& .MuiOutlinedInput-input': {
                      padding: '5px',
                    },
                  }}
                  inputRef={(el) => {
                    if (!textFieldRefs.current[currentIndex]) {
                      textFieldRefs.current[currentIndex] = [];
                    }
                    textFieldRefs.current[currentIndex][4] = el;
                  }}
                  onKeyPress={(event) => handleKeyPress(event, currentIndex)}
                  value={data[currentIndex].scoredMarkTheory !== 'N' ? data[currentIndex].scoredMarkTheory : ''}
                  onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                    const updatedData = data.map((item) => {
                      if (item.studentId === data[currentIndex].studentId) {
                        return { ...item, scoredMarkTheory: event.target.value };
                      }
                      return item;
                    });
                    setData(updatedData);
                  }}
                />
              </Box>
              <Stack direction="row" alignItems="center" gap={2}>
                <Button
                  variant="outlined"
                  color="primary"
                  onClick={() => {
                    OnClose();
                    sendMarkRegisterData(data, 'multiple');
                  }}
                >
                  Update
                </Button>
                {currentIndex !== data.length - 1 && (
                  <Button
                    variant="contained"
                    color="primary"
                    className="slider-controler"
                    onClick={() => {
                      swiperRef.current?.slideNext();
                      sendMarkRegisterData(data, 'single');
                      handleNext();
                      // setTimeout(() => {
                      textFieldRefs.current[currentIndex + 1][0]?.focus();
                      // }, 500);
                    }}
                  >
                    Update & Next
                  </Button>
                )}
              </Stack>
            </Stack>
            <Stack position="absolute" top={150} left={0}>
              <Button
                disabled={currentIndex === 0}
                variant="contained"
                sx={{ borderRadius: 10, minWidth: 0, p: 0.5 }}
                onClick={() => {
                  handlePrevious();
                }}
                color="primary"
              >
                <KeyboardArrowLeftIcon />
              </Button>
            </Stack>
            <Stack position="absolute" top={150} right={0}>
              <Button
                disabled={currentIndex === data.length - 1}
                variant="contained"
                sx={{ borderRadius: 10, minWidth: 0, p: 0.5 }}
                onClick={() => {
                  handleNext();
                }}
              >
                <KeyboardArrowRightIcon />
              </Button>
            </Stack>
          </Box>
        </SwiperSlide>
      </Swiper>
    </IndivitualEnrollCERoot>
  );
};

export default IndivitualEnrollCE;
