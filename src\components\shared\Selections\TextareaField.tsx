/* eslint-disable no-nested-ternary */

import { TextField, Typography } from '@mui/material';
import { useEffect, useState } from 'react';

type TextareaFieldProps = {
  InputProps?: {};
  placeholder?: string;
  helperText?: string | false | undefined;
  error?: boolean;
  value: string;
  name: string;
  onChange?: (event: React.ChangeEvent<HTMLTextAreaElement>) => void;
  limit?: number | undefined;
  ShowCharectersCount?: number;
};

export default function TextareaField({
  InputProps,
  placeholder,
  helperText,
  error,
  value,
  name,
  onChange,
  limit,
  ShowCharectersCount,
}: TextareaFieldProps) {
  const [content, setContent] = useState(value);
  const [charCount, setCharCount] = useState(limit - value.length);

  useEffect(() => {
    setContent(value);
    setCharCount(limit - value.length);
  }, [value, limit]);

  const handleChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = event.target.value;
    if (newValue.length <= limit) {
      setContent(newValue);
      setCharCount(limit - newValue.length);
      onChange(event); // Call the onChange prop with the event object
    }
  };

  return (
    <>
      <TextField
        multiline
        fullWidth
        name={name}
        onChange={handleChange} // Pass handleChange as the onChange handler
        value={content}
        error={error}
        helperText={helperText}
        InputProps={InputProps}
        placeholder={placeholder}
      />
      {ShowCharectersCount === 1 && (
        <Typography variant="subtitle1" fontSize={10}>
          {charCount === 1
            ? `${charCount} character remaining.`
            : charCount === 0
            ? `${charCount} characters remaining.`
            : `${charCount} out of ${limit} characters remaining.`}
        </Typography>
      )}
    </>
  );
}
