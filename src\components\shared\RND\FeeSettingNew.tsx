/* eslint-disable no-nested-ternary */
/* eslint-disable no-else-return */
/* eslint-disable jsx-a11y/alt-text */
import React, { ChangeEvent, FormEvent, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  Tooltip,
  Collapse,
  FormControl,
  MenuItem,
  useTheme,
  SelectChangeEvent,
  Select,
  IconButton,
  Checkbox,
  Box,
  InputAdornment,
} from '@mui/material';
import styled from 'styled-components';
import NoData from '@/assets/no-datas.png';
import SaveIcon from '@mui/icons-material/Save';
import DeleteIcon from '@mui/icons-material/Delete';
import deleteBin from '@/assets/ManageFee/deleteBin.json';
import errorIcon from '@/assets/ManageFee/Error.json';
import deleteSuccess from '@/assets/ManageFee/deleteSuccess.json';
import { StudentMappedList } from '@/config/TableData';
import { MdAdd } from 'react-icons/md';
import SearchIcon from '@mui/icons-material/Search';
import { IoIosArrowUp } from 'react-icons/io';
import ErrorIcon from '@/assets/ManageFee/ErrorIcon.json';
import Success from '@/assets/ManageFee/Success.json';
import Updated from '@/assets/ManageFee/Updated.json';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import Popup from '@/components/shared/Popup/Popup';
import { getManageFeeSubmitting, getTermFeeData, getTermFeeStatus, getYearData } from '@/config/storeSelectors';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { useAppSelector } from '@/hooks/useAppSelector';
import useAuth from '@/hooks/useAuth';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import Lottie from 'lottie-react';
import {
  DeleteAllBasicFee,
  DeleteBasicFee,
  createBasicFeeSetting,
  createBasicFeeSettingTitle,
  fetchGetTermFee,
} from '@/store/ManageFee/manageFee.thunks';
import { fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import {
  BasicFeeDetailsType,
  BasicFeeMappedDeleteAllDataType,
  ClassSectionType,
  CreateBasicFeeSettingTitleDataType,
  TermFeeDetailsDataType,
} from '@/types/ManageFee';
import useSettings from '@/hooks/useSettings';
import LoadingButton from '@mui/lab/LoadingButton';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import { ErrorMessage } from '@/components/shared/Popup/ErrorMessage';
import { FEE_TYPE_ID_OPTIONS, FEE_TYPE_OPTIONS } from '@/config/Selection';
import { getTextColor } from '@/utils/Colors';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import { Skeleton } from '@mui/material';
import { Table } from '@mui/material';
import { TableRow } from '@mui/material';
import { TableCell } from '@mui/material';
import { TableBody } from '@mui/material';
import DTVirtuoso from '@/components/shared/RND/DataTableVirtuoso';
import { CreateEditBasicFeeTitleForm } from '@/features/ManageFee/BasicFeeSetting/CreateEditBasicFeeTitleForm';

// import { EditList } from './EditList';
// import { EditedTable } from './EditedTable';

const FeeSettingRoot = styled.div`
  padding: 1rem;
  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 1300px) {
      height: calc(100vh - 20px);
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 50px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;
        position: relative;

        .MuiTable-root ::-webkit-scrollbar {
          height: 16px;
        }
        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
        .MuiTableCell-head {
          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? '#ebd6fd' : props.theme.palette.grey[900]};
          /* background-color: ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.info.light : props.theme.palette.grey[900]}; */
        }
        .MuiTableCell-head:nth-child(1) {
          padding: 4px;
          z-index: 11;
          position: sticky;
          left: 0;
          width: 44px;
        }
        .MuiTableCell-head:nth-child(2) {
          z-index: 11;
          position: sticky;
          left: 44px;
        }
        .MuiTableCell-head:nth-child(3) {
          z-index: 11;
          position: sticky;
          left: 204px;
          width: 100px;
        }
        .MuiTableCell-head:nth-child(4) {
          z-index: 11;
          position: sticky;
          left: 334px;
          width: 120px;
        }
        .MuiTableCell-head:last-child {
          z-index: 11;
          position: sticky;
          right: 0px;
          /* width: 50px; */
        }

        .MuiTableCell-root.MuiTableCell-body:nth-child(1) {
          padding: 4px;
          position: sticky;
          left: 0;
          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? '#f3e8ff' : props.theme.palette.grey[900]};
          /* background-color: ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.info.lighter : props.theme.palette.grey[900]}; */
          width: 44px;
          z-index: 1;
        }
        .MuiTableCell-root.MuiTableCell-body:nth-child(2) {
          position: sticky;
          left: 44px;
          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? '#f3e8ff' : props.theme.palette.grey[900]};
          /* background-color: ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.info.lighter : props.theme.palette.grey[900]}; */
          z-index: 1;
        }
        .MuiTableCell-root.MuiTableCell-body:nth-child(3) {
          position: sticky;
          left: 204px;
          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? '#f3e8ff' : props.theme.palette.grey[900]};
          /* background-color: ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.info.lighter : props.theme.palette.grey[900]}; */
          width: 100px;
          z-index: 1;
        }
        .MuiTableCell-root.MuiTableCell-body:nth-child(4) {
          position: sticky;
          left: 334px;
          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? '#f3e8ff' : props.theme.palette.grey[900]};
          /* background-color: ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.info.lighter : props.theme.palette.grey[900]}; */
          width: 120px;
          z-index: 1;
        }
        .MuiTableCell-root.MuiTableCell-body:last-child {
          position: sticky;
          right: 0px;
          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? '#f3e8ff' : props.theme.palette.grey[900]};
          /* background-color: ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.info.lighter : props.theme.palette.grey[800]}; */
          width: 100px;
          z-index: 1;
        }
        .header-color {
          color: ${(props) => getTextColor(props.theme)};
        }
      }
    }
  }
`;
type TermFeeDetailsRequestType = {
  adminId: number;
  accademicId: number;
  feeId: number;
  feeType: number;
};
type TermFeeDetailsType = {
  feeId: number;
  feeTitle: string;
  feeType: number;
  feeMapped: FeeMappedType;
};
type FeeAmountType = {
  sectionId: number;
  amount: number;
  dbResult: string;
  academicFeeId: number;
};
type FeeMappedType = { feeId: number; sectionId: number; amount: number; academicFeeId: number };

interface TermAmount {
  sectionId: number;
  amount: number;
  dbResult: string;
  academicFeeId: number;
  feeId: number;
}
interface TermFeeDetail {
  feeId: number;
  feeTitle: string;
  feeType: number;
  feeMapped: TermAmount[];
}
interface SectionAmount {
  [key: string]: number;
}
export default function FeeSetting() {
  const { user } = useAuth();
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const dispatch = useAppDispatch();
  const { confirm } = useConfirm();
  const [showFilter, setShowFilter] = useState(true);
  const [popup, setPopup] = React.useState(false);
  const adminId: number = user ? user.accountId : 0;
  const YearData = useAppSelector(getYearData);
  const defualtYear = YearData[0]?.accademicId || 0;
  const TermFeeData: any = useAppSelector(getTermFeeData);
  const TermFeeStatus = useAppSelector(getTermFeeStatus);
  const isSubmitting = useAppSelector(getManageFeeSubmitting);
  const defaultYearId = 10;
  const [academicYearFilter, setAcademicYearFilter] = useState(defualtYear);
  const [feeTypeFilter, setFeeTypeFilter] = useState(0);
  const [classSections, setClassSections] = useState([]);
  const [termFeeDetails, setBasicFeeDetails] = useState<TermFeeDetail[]>([]);
  const [termFee, setTermFeeData] = useState<TermFeeDetailsDataType[]>([]);
  const handleClickOpen = () => setPopup(true);
  const handleClickClose = () => setPopup(false);
  const [individualSaveLoading, setIndividualSaveLoading] = useState<Record<string, boolean>>({});
  const [feeTypeByRow, setFeeTypeByRow] = useState<{ [key: string]: number }>({});
  const [feeType, setFeeType] = useState(1);
  const [individualSaveButtonEnabled, setIndividualSaveButtonEnabled] = useState<{ [key: string]: boolean }>({});
  const [sectionAmounts, setSectionAmounts] = useState<any>({});
  const [saving, setSaving] = useState(false);
  const [showSuccessIcon, setShowSuccessIcon] = useState<{ [key: string]: boolean }>({});
  const [checkedRows, setCheckedRows] = useState({});
  const [succesResponse, setSuccesResponse] = useState('');
  const [selectedRows, setSelectedRows] = useState<BasicFeeDetailsType[]>([]);
  const [savingAll, setSavingAll] = useState(false);
  const [switchToUpdateButton, setSwitchToUpdateButton] = useState(false);
  const [amountEnter, setAmountEnter] = useState<number | string>(0 || '');
  const [textBoxValue, setTextBoxValue] = useState<boolean>(true);
  const textFieldRefs = useRef<{ [key: string]: HTMLInputElement | null }>({});
  const [currentPage, setCurrentPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const [drawerOpen, setDrawerOpen] = React.useState<boolean>(false);

  const toggleDrawerOpen = () => {
    setDrawerOpen(true);
  };
  const toggleDrawerClose = () => setDrawerOpen(false);

  const initialTermFeeListRequest = React.useMemo(
    () => ({
      adminId,
      academicId: academicYearFilter,
      feeTypeId: feeTypeFilter,
    }),
    [adminId, academicYearFilter, feeTypeFilter]
  );
  const currentTermFeeListRequest = React.useMemo(
    () => ({
      adminId,
      academicId: academicYearFilter,
      feeTypeId: feeTypeFilter,
    }),
    [adminId, academicYearFilter, feeTypeFilter]
  );

  const loadTermFeeList = useCallback(
    async (request: { adminId: number; academicId: number; feeTypeId: number }) => {
      try {
        const data: any = await dispatch(fetchGetTermFee(request)).unwrap();
        console.log('data::::', data);
        // setTermFeeData(data);
        // const ClassSectionArray = data.sections ? data.sections.map((item) => ({ ...item })) : [];
        // setClassSections(ClassSectionArray);
        // console.log('ClassSectionArray::::', ClassSectionArray);

        setTermFeeData(data);
        const TermFeeDetails = data.termFeeDetails
          ? data.termFeeDetails.map((item: BasicFeeDetailsType) => ({ ...item }))
          : [];
        // setBasicFeeDetails(TermFeeDetails);
        console.log('TermFeeDetails::::', TermFeeDetails);
        // console.log('TermFeeDetails::::', TermFeeDetails);
      } catch (error) {
        console.error('Error loading term fee list:', error);
      }
    },
    [dispatch]
  );

  React.useEffect(() => {
    dispatch(fetchYearList(adminId));
    if (TermFeeStatus === 'idle') {
      loadTermFeeList(initialTermFeeListRequest);
      // dispatch(fetchGetTermFee(initialTermFeeListRequest));
    }
    // setTermFeeData(TermFeeData);
    if (TermFeeData && Array.isArray(TermFeeData.termFeeDetails)) {
      const TermFeeDetails = TermFeeData.termFeeDetails.map((item: any) => item);
      setBasicFeeDetails(TermFeeDetails);
    } else {
      setBasicFeeDetails([]); // Reset or set to an empty array if the data is not available
    }
    // console.log('TermFeeData----::::', TermFeeData);
    console.log('TermFeeData----::::', TermFeeData);
  }, [dispatch, adminId, initialTermFeeListRequest, TermFeeData, loadTermFeeList, TermFeeStatus, academicYearFilter]);

  const handleYearChange = (e: SelectChangeEvent) => {
    setAcademicYearFilter(parseInt(YearData.filter((item) => item.accademicId === e.target.value)[0].accademicId, 10));
    loadTermFeeList({ ...currentTermFeeListRequest, academicId: parseInt(e.target.value, 10) });
    setSelectedRows([]);
  };

  const handleFeeTypeChange = (e: SelectChangeEvent) => {
    setCurrentPage(0);
    const feeTypeId = parseInt(e.target.value, 10);
    setFeeTypeFilter(feeTypeId);
    loadTermFeeList({ ...currentTermFeeListRequest, feeTypeId: parseInt(e.target.value, 10) });
    setSelectedRows([]);
  };

  const handleChange = (e: SelectChangeEvent, row: any) => {
    const newFeeType = parseInt(e.target.value, 10);
    setFeeType(newFeeType);
    setFeeTypeByRow((prev) => ({
      ...prev,
      [row.feeId]: newFeeType,
    }));
  };
  // =========================

  // =========================

  const [classSectionsData, setClassSectionsData] = useState(classSections);

  // const handleAmountChange = (newValue, row, sectionId) => {
  //   const updatedFeeMapped = section.feeMapped.map((fee) => {
  //     if (fee.feeId === row.feeId && fee.sectionId === sectionId) {
  //       return { ...fee, amount: newValue };
  //     }
  //     return fee;
  //   });
  //   return { ...section, feeMapped: updatedFeeMapped };
  //   setClassSectionsData(updatedData);
  //   console.log('updatedData::::----', newValue);
  // };
  // Step 2: Update the state whenever text is entered into the text field

  const handleAmountChange = useCallback(
    (
      e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
      row: TermFeeDetailsType,
      sectionId: number,
      id: number
    ) => {
      const newValue = parseInt(e.target.value, 10);
      const newValueString = e.target.value;
      const feeMapped = Array.isArray(row.feeMapped) ? row.feeMapped : [];
      const amountObj = feeMapped.find((f: any) => f.sectionId === sectionId);
      const amount = amountObj ? amountObj.amount : '';

      setAmountEnter(newValue || newValueString);
      const { feeId } = row;
      // Update the enabled state for the corresponding row
      if (newValue === 0 || newValueString === '') {
        setIndividualSaveButtonEnabled((prevEnabled) => ({
          ...prevEnabled,
          [`${feeId}`]: false,
        }));
      } else {
        setIndividualSaveButtonEnabled((prevEnabled) => ({
          ...prevEnabled,
          [`${feeId}`]: true,
        }));
      }
      console.log('individualSaveButtonEnabled::::----', individualSaveButtonEnabled);
      if (!Number.isNaN(newValue) || id !== 1) {
        setSectionAmounts((prevAmounts: any) => ({
          ...prevAmounts,
          [`${row.feeId}_${sectionId}`]: newValue,
        }));
        console.log('sectionAmounts::::----', sectionAmounts);
      } else {
        setSectionAmounts((prevAmounts: any) => ({
          ...prevAmounts,
          [`${row.feeId}_${sectionId}`]: amount,
        }));
      }
    },

    [individualSaveButtonEnabled, sectionAmounts]
  );

  const handleReset = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      setSelectedRows([]);
      setSectionAmounts({});
      setAcademicYearFilter(defualtYear);
      setFeeTypeFilter(0);
      loadTermFeeList({ ...currentTermFeeListRequest, feeTypeId: 0 });
    },
    [currentTermFeeListRequest, loadTermFeeList, defualtYear]
  );
  const getRowKey = useCallback((row: any) => row.feeId, []);

  // const handleCheckboxClick = (row) => {
  //   const updatedCheckedRows = { ...checkedRows };
  //   updatedCheckedRows[row.feeId] = !isSelectedRow;
  //   setCheckedRows(updatedCheckedRows);
  // };

  const handleSave = useCallback(
    async (row: TermFeeDetailsType) => {
      try {
        setSaving(true);
        const { feeId } = row;
        const allTermAmntWithFeeIdUpdates: { [key: number]: TermAmount[] } = {};

        // Construct feeAmount array based on section amounts
        const feeAmounts = Object.keys(sectionAmounts)
          .filter((key) => parseInt(key.split('_')[0], 10) === feeId)
          .map((key) => {
            const amount = parseInt(sectionAmounts[key], 10);
            if (!Number.isNaN(amount)) {
              return {
                sectionId: parseInt(key.split('_')[1], 10),
                amount,
                dbResult: 'string',
                academicFeeId: 0,
              };
            }
            return null;
          })
          .filter((feeAmount) => feeAmount !== null); // Remove any null values from the array

        const sectionAmountsKeys = Object.keys(feeAmounts);

        // Check if any of the amounts is zero
        const zeroAmountExists = sectionAmountsKeys.some((key) => parseInt(sectionAmounts[key], 10) === 0);
        console.log('sectionAmounts----::::', sectionAmounts);

        if (zeroAmountExists) {
          console.error('Error: Amount cannot be zero');
        } else {
          setIndividualSaveLoading((prevMap) => ({ ...prevMap, [row.feeId]: true }));

          const sendReq = [
            {
              adminId,
              accademicId: academicYearFilter,
              feeId,
              feeType: feeTypeByRow[feeId],
              feeTypeId: feeTypeFilter,
              feeAmount: feeAmounts,
            },
          ];
          console.log('sendReq::::----', sendReq);

          // const actionResult = await dispatch(createBasicFeeSetting(sendReq));

          const actionResults = await Promise.all(sendReq.map((req) => dispatch(createBasicFeeSetting([req]))));

          // Process all results
          actionResults.forEach((actionResult) => {
            if (actionResult && Array.isArray(actionResult.payload)) {
              // loadTermFeeList(currentTermFeeListRequest);
              // setSectionAmounts({});
              setSelectedRows((prev) => prev.filter((f) => f.feeId !== row?.feeId));
              setIndividualSaveLoading((prevMap) => ({ ...prevMap, [row.feeId]: false }));
              const results = actionResult.payload;
              console.log('results::::----', results);
              // Filter out items without feeAmount property
              const filteredResults = results.filter((f) => f.feeAmount);
              console.log('filteredResults::::----', filteredResults);

              // Extract feeAmount arrays
              const feeAmountsArray = filteredResults.map((f) => f.feeAmount);

              // Flatten the array of feeAmount arrays
              const termAmnt = feeAmountsArray.flat();
              console.log('termAmnt::::----', termAmnt);

              //   const termAmntWithFeeId = termAmnt.map((item) => ({
              //     ...item,
              //     feeId,
              //   }));

              const termAmntWithFeeId = termAmnt.map((item) => ({
                // ...item,
                academicFeeId: item.academicFeeId,
                amount: item.amount,
                feeId,
                sectionId: item.sectionId,
              }));
              // const termAmntWithFeeId = termAmnt.map((item) => ({
              //   feeId,
              //   academicFeeId: item.academicFeeId,
              //   amount: item.amount,
              //   sectionId: item.sectionId,
              // }));
              console.log('termAmntWithFeeId::::----', termAmntWithFeeId);

              setSectionAmounts((prevAmounts) => {
                const updatedAmounts = { ...prevAmounts };

                // Remove the values corresponding to the current row's feeId
                Object.keys(updatedAmounts).forEach((key: any) => {
                  const [currentFeeId, currentSectionId] = key.split('_').map(Number);
                  if (currentFeeId === feeId) {
                    delete updatedAmounts[key];
                  }
                });

                return updatedAmounts;
              });
              // sectionAmounts({});

              // Collect all updates for each feeId
              if (!allTermAmntWithFeeIdUpdates[feeId]) {
                allTermAmntWithFeeIdUpdates[feeId] = [];
              }
              allTermAmntWithFeeIdUpdates[feeId] = [...allTermAmntWithFeeIdUpdates[feeId], ...termAmntWithFeeId];

              // Update feeMapped for the current row
              //   const updatedTermFeeDetails = termFeeDetails.map((item) => {
              //     if (item.feeId === feeId && Array.isArray(item.feeMapped)) {
              //       const updatedTermFeeMapped = [...item.feeMapped, ...termAmntWithFeeId];
              //       return { ...item, feeMapped: updatedTermFeeMapped };
              //     }
              //     return item;
              //   });

              //   setBasicFeeDetails(updatedTermFeeDetails);

              // Find the item with dbResult === 'Success'
              const SuccessResult = termAmnt.find((f) => f.dbResult === 'Success');
              console.log('SuccessResult::::----', SuccessResult);
              if (SuccessResult) {
                setSuccesResponse(SuccessResult.dbResult);
              }

              // Find the item with dbResult === 'Updated'
              const UpdateResult = termAmnt.find((f) => f.dbResult === 'Updated');
              console.log('UpdateResult::::----', UpdateResult);
              if (UpdateResult) {
                setSuccesResponse(UpdateResult.dbResult);
              }
              setSaving(false);
              setIndividualSaveButtonEnabled((prevEnabled) => ({
                ...prevEnabled,
                [feeId]: false, // Enable the button again after the operation completes
              }));

              setShowSuccessIcon((prevMap) => ({ ...prevMap, [row.feeId]: true }));
              setTimeout(() => {
                setShowSuccessIcon((prevMap) => ({ ...prevMap, [row.feeId]: false }));
              }, 5000);

              if (SuccessResult) {
                // Update feeMapped for the current row
                const updatedTermFeeDetails = termFeeDetails.map((item) => {
                  if (item.feeId === feeId) {
                    const feeMappedArr = Array.isArray(item.feeMapped) ? item.feeMapped : [];
                    const updatedTermFeeMapped = [...feeMappedArr, ...termAmntWithFeeId];

                    console.log('termAmntWithFeeId1st::::----', termAmntWithFeeId); // Correct placement of console.log
                    return { ...item, feeMapped: updatedTermFeeMapped };
                  }
                  return item;
                });

                setBasicFeeDetails(updatedTermFeeDetails);
              } else if (UpdateResult) {
                // const updatedTermFeeDetails = termFeeDetails.map((item) => {
                //   if (allTermAmntWithFeeIdUpdates[item.feeId] && Array.isArray(item.feeMapped)) {
                //     const updatedTermFeeMapped = [...item.feeMapped, ...allTermAmntWithFeeIdUpdates[item.feeId]];
                //     return { ...item, feeMapped: updatedTermFeeMapped };
                //   }
                //   return item;
                // });

                const updatedTermFeeDetails = termFeeDetails.map((item) => {
                  if (item.feeId === feeId && Array.isArray(item.feeMapped)) {
                    const existingTermMap = item.feeMapped.find((i) =>
                      termAmntWithFeeId.some((fee) => fee.academicFeeId === i.academicFeeId)
                    );

                    if (existingTermMap) {
                      // If an existing academicFeeId is found, update that instead of adding new duplicates
                      const updatedTermFeeMapped = item.feeMapped.map((i) =>
                        termAmntWithFeeId.some((fee) => fee.academicFeeId === i.academicFeeId)
                          ? { ...i, ...termAmntWithFeeId.find((fee) => fee.academicFeeId === i.academicFeeId) }
                          : i
                      );
                      return { ...item, feeMapped: updatedTermFeeMapped };
                    } else {
                      // If no existing academicFeeId matches, push all termAmntWithFeeId
                      const updatedTermFeeMapped = [...item.feeMapped, ...termAmntWithFeeId];
                      return { ...item, feeMapped: updatedTermFeeMapped };
                    }
                  }
                  return item;
                });

                setBasicFeeDetails(updatedTermFeeDetails);

                console.log('termAmntWithFeeId2nd::::----', termAmntWithFeeId);
              }
            }
          });
          // loadTermFeeList(currentTermFeeListRequest);
        }
        // Update termFeeDetails with all collected updates
        const updatedTermFeeDetails = termFeeDetails.map((item) => {
          if (allTermAmntWithFeeIdUpdates[item.feeId] && Array.isArray(item.feeMapped)) {
            const updatedTermFeeMapped = [...item.feeMapped, ...allTermAmntWithFeeIdUpdates[item.feeId]];
            return { ...item, feeMapped: updatedTermFeeMapped };
          }
          return item;
        });

        setBasicFeeDetails(updatedTermFeeDetails);
      } catch (error) {
        // Handle errors here
        setIndividualSaveButtonEnabled((prevEnabled) => ({
          ...prevEnabled,
          [row.feeId]: true,
        }));
        setIndividualSaveLoading((prevMap) => ({ ...prevMap, [row.feeId]: false }));
        const errorMessage = (
          <ErrorMessage loop={false} jsonIcon={ErrorIcon} message="Something went wrong please try again." />
        );
        await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
      }
    },
    [
      dispatch,
      adminId,
      // feeType,
      academicYearFilter,
      sectionAmounts,
      termFeeDetails,
      // loadTermFeeList,
      // currentTermFeeListRequest,
      feeTypeByRow,
      feeTypeFilter,
      confirm,
    ]
  );

  const handleAllSave = useCallback(async () => {
    try {
      setSavingAll(true);
      const feeIdArray = Object.keys(sectionAmounts).map((key) => parseInt(key.split('_')[0], 10));

      // Ensure feeIdArray contains unique values
      const uniqueFeeIds = [...new Set(feeIdArray)];

      // Object to hold all updates for each feeId
      const allTermAmntWithFeeIdUpdates: { [key: number]: TermAmount[] } = {};

      // Use Promise.all to handle asynchronous dispatch calls for each feeId
      await Promise.all(
        uniqueFeeIds.map(async (feeId) => {
          // Filter feeAmounts for the current feeId
          const filteredFeeAmounts = Object.entries(sectionAmounts)
            .filter(([key]) => parseInt(key.split('_')[0], 10) === feeId)
            .reduce((obj, [key, value]) => {
              obj[key] = value;
              return obj;
            }, {} as SectionAmount);

          const feeAmountsArray = Object.entries(filteredFeeAmounts)
            .map(([key, value]) => {
              const amount = parseInt(value, 10);
              if (!Number.isNaN(amount)) {
                return {
                  sectionId: parseInt(key.split('_')[1], 10),
                  amount,
                  dbResult: 'string',
                  academicFeeId: 0,
                };
              }
              return null;
            })
            .filter((feeAmount) => feeAmount !== null); // Filter out null values

          const zeroAmountExists = Object.values(filteredFeeAmounts).some((amount) => amount === 0);

          if (!zeroAmountExists) {
            const sendReq = [
              {
                adminId,
                accademicId: academicYearFilter,
                feeId,
                feeType,
                feeTypeId: feeTypeFilter,
                feeAmount: feeAmountsArray,
              },
            ];

            const actionResult = await dispatch(createBasicFeeSetting(sendReq));

            if (actionResult && Array.isArray(actionResult.payload)) {
              setSwitchToUpdateButton(false);
              setSelectedRows([]);
              setSectionAmounts({});
              const results = actionResult.payload;
              const filteredResults = results.filter((f) => f.feeAmount);
              const termAmountsArray = filteredResults.map((f) => f.feeAmount);
              const termAmnt = termAmountsArray.flat();

              const termAmntWithFeeId = termAmnt.map((item) => ({
                ...item,
                feeId,
              }));

              // Collect all updates for each feeId
              if (!allTermAmntWithFeeIdUpdates[feeId]) {
                allTermAmntWithFeeIdUpdates[feeId] = [];
              }
              allTermAmntWithFeeIdUpdates[feeId] = [...allTermAmntWithFeeIdUpdates[feeId], ...termAmntWithFeeId];

              const SuccessResult = termAmnt.find((f) => f.dbResult === 'Success');
              if (SuccessResult) {
                setSuccesResponse(SuccessResult.dbResult);
                // loadTermFeeList(currentTermFeeListRequest);
              }

              const UpdateResult = termAmnt.find((f) => f.dbResult === 'Updated');
              if (UpdateResult) {
                setSuccesResponse(UpdateResult.dbResult);
                // loadTermFeeList(currentTermFeeListRequest);
              }

              setIndividualSaveButtonEnabled((prevEnabled) => ({
                ...prevEnabled,
                [feeId]: false, // Enable the button again after the operation completes
              }));

              setCheckedRows([]);
            }
          }
        })
      );
      // Update termFeeDetails with all collected updates
      const updatedTermFeeDetails = termFeeDetails.map((item) => {
        if (allTermAmntWithFeeIdUpdates[item.feeId]) {
          const updatedTermFeeMapped = Array.isArray(item.feeMapped)
            ? [...item.feeMapped, ...allTermAmntWithFeeIdUpdates[item.feeId]]
            : [...allTermAmntWithFeeIdUpdates[item.feeId]]; // Ensure feeMapped is an array before spreading
          return { ...item, feeMapped: updatedTermFeeMapped };
        }
        return item;
      });

      setBasicFeeDetails(updatedTermFeeDetails);
      setSavingAll(false); // Set saving to false after all asynchronous operations are completed
    } catch (error) {
      // Handle errors here
      setSavingAll(false);
      const errorMessage = (
        <ErrorMessage loop={false} jsonIcon={ErrorIcon} message="Something went wrong please try again." />
      );
      await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
    }
  }, [dispatch, confirm, adminId, academicYearFilter, feeTypeFilter, sectionAmounts, feeType, termFeeDetails]);

  const handleFeeTitleSave = useCallback(
    async (values: CreateBasicFeeSettingTitleDataType) => {
      try {
        console.log('values::::----', values);
        const response = await dispatch(createBasicFeeSettingTitle(values)).unwrap();
        console.log('response::::----', response);
        if (response.id > 0) {
          toggleDrawerClose();
          loadTermFeeList(currentTermFeeListRequest);
          // loadTermFeeList({ ...currentTermFeeListRequest, academicId: 10 });
          const successMessage = (
            <SuccessMessage icon="" loop={false} jsonIcon={Success} message="Fee title create successfully" />
          );
          await confirm(successMessage, '', { okLabel: 'Ok', showOnlyOk: true });

          // Update feeMapped for the current row
          // const newRow = { ...values, feeTitle: values.feeTitle, feeType: values.feeType }; // Assuming values.feeType exists
          // const updatedTermFeeDetails = termFeeDetails.map((item) => {
          //   const updatedTermFeeMapped = [...item.feeMapped];
          //   return { ...item, feeMapped: [...updatedTermFeeMapped, newRow] };
          // });
          // setBasicFeeDetails(updatedTermFeeDetails);
          // console.log('updatedTermFeeDetails::::----', updatedTermFeeDetails);
        } else if (response.id === 0) {
          toggleDrawerClose();
          const errorMessage = <ErrorMessage icon="" jsonIcon={ErrorIcon} message="Fee title already created" />;
          await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
          toggleDrawerOpen();
          // Update feeMapped for the current row

          // console.log('updatedTermFeeDetails::::----', updatedTermFeeDetails);
        } else {
          const errorMessage = <ErrorMessage icon="" jsonIcon={ErrorIcon} message="Fee title create failed" />;
          await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        }
      } catch (error) {
        console.error(error);
        const errorMessage = (
          <ErrorMessage
            icon=""
            jsonIcon={ErrorIcon}
            message="Fee title creating something went wrong please try again."
          />
        );
        await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
      }
    },
    [dispatch, confirm, loadTermFeeList, currentTermFeeListRequest]
  );

  const handleDeleteRow = useCallback(
    async (row: BasicFeeDetailsType) => {
      const { feeId } = row;
      console.log('row::::----', row);
      // const fineMappedIds = row.filter((f) => f.termId === term.termId).map((m) => m.fineMappedId);

      const sendConfirmMessage = (
        <DeleteMessage
          jsonIcon={deleteBin}
          message={
            <div style={{ color: theme.palette.error.main }}>
              Are you sure you want to delete the row &quot;{}&quot; ?
            </div>
          }
        />
      );

      if (await confirm(sendConfirmMessage, 'Delete Row?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
        const sendReq = [
          {
            adminId,
            accademicId: academicYearFilter,
            feeId,
            dbResult: 'string',
            deletedId: 0,
          },
        ];

        console.log('sendReq', sendReq);

        const deleteResponse = await dispatch(DeleteAllBasicFee(sendReq));
        console.log('deleteResponse::::----', deleteResponse);
        if (deleteResponse && Array.isArray(deleteResponse.payload)) {
          const results = deleteResponse.payload;
          const errorMessages = results.find((result) => result.dbResult === 'Failed');
          const successMessages = results.find((result) => result.dbResult === 'Success');

          if (!errorMessages) {
            setSectionAmounts({});
            // loadTermFeeList({ ...currentTermFeeListRequest, feeTypeId: feeTypeFilter });

            const deleteSuccessMessage = (
              <SuccessMessage loop={false} jsonIcon={deleteSuccess} message="Delete successfully" />
            );
            await confirm(deleteSuccessMessage, 'Success', { okLabel: 'Ok', showOnlyOk: true });
            const updatedTermFeeDetails = termFeeDetails.map((item) => {
              return {
                ...item,
                feeMapped: Array.isArray(item.feeMapped)
                  ? item.feeMapped.filter((f) => !row.feeMapped.some((e: any) => e.academicFeeId === f.academicFeeId))
                  : [], // Ensure feeMapped is an array or default to an empty array
              };
            });

            setBasicFeeDetails(updatedTermFeeDetails);
          } else if (!successMessages) {
            const deleteErrorMessage = <DeleteMessage loop={false} jsonIcon={errorIcon} message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          } else {
            const deleteErrorMessage = <DeleteMessage loop={false} jsonIcon={errorIcon} message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          }
          // loadTermFeeList(currentTermFeeListRequest);
        }
      }
    },
    [confirm, dispatch, adminId, academicYearFilter, theme, termFeeDetails]
  );

  const handleDeleteCell = useCallback(
    async (academicFeeId: number, sectionId: number) => {
      const sendConfirmMessage = (
        <DeleteMessage
          jsonIcon={deleteBin}
          message={
            <div style={{ color: theme.palette.error.main }}>
              Are you sure you want to delete the cell &quot;{}&quot; ?
            </div>
          }
        />
      );

      if (await confirm(sendConfirmMessage, 'Delete Row?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
        const sendReq = [
          {
            adminId,
            accademicId: academicYearFilter,
            academicFeeId,
            dbResult: '',
            deletedId: 0,
          },
        ];

        console.log('sendReq', sendReq);

        const deleteResponse = await dispatch(DeleteBasicFee(sendReq));
        console.log('deleteResponse::::----', deleteResponse);
        console.log('sectionAmounts::::----', sectionAmounts);
        if (deleteResponse && Array.isArray(deleteResponse.payload)) {
          const results = deleteResponse.payload;
          const errorMessages = results.find((result) => result.dbResult === 'Failed');
          const successMessages = results.find((result) => result.dbResult === 'Deleted');

          if (!errorMessages) {
            setSectionAmounts({});
            // loadTermFeeList(currentTermFeeListRequest);
            const deleteSuccessMessage = (
              <SuccessMessage loop={false} jsonIcon={deleteSuccess} message="Delete successfully" />
            );
            await confirm(deleteSuccessMessage, 'Success', { okLabel: 'Ok', showOnlyOk: true });
            const updatedTermFeeDetails = termFeeDetails.map((item) => {
              return {
                ...item,
                feeMapped: Array.isArray(item.feeMapped)
                  ? item.feeMapped.filter((f) => f.academicFeeId !== academicFeeId)
                  : [], // Ensure feeMapped is an array or default to an empty array
              };
            });

            setBasicFeeDetails(updatedTermFeeDetails);

            // Filter out the sectionAmounts related to the deleted sectionId
            const filteredSectionAmounts = Object.keys(sectionAmounts).filter(
              (key) => parseInt(key.split('_')[1], 10) !== sectionId
            );

            setSectionAmounts(filteredSectionAmounts);

            // setSectionAmounts({});
          } else if (!successMessages) {
            const deleteErrorMessage = <DeleteMessage loop={false} jsonIcon={errorIcon} message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          } else {
            const deleteErrorMessage = <DeleteMessage loop={false} jsonIcon={errorIcon} message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          }
          // loadTermFeeList(currentTermFeeListRequest);
        }
      }
    },
    [confirm, dispatch, adminId, academicYearFilter, theme, termFeeDetails, sectionAmounts]
  );

  const handleDeleteMultiple = useCallback(async () => {
    const sendConfirmMessage = (
      <DeleteMessage jsonIcon={deleteBin} message={<div>Are you sure you want to delete all ?</div>} />
    );
    if (await confirm(sendConfirmMessage, 'Delete Basic Fee?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
      // const deleteResponse = await dispatch(deleteBasicFeeList(sendReq));
      const sendRequests: BasicFeeMappedDeleteAllDataType[] = await Promise.all(
        selectedRows?.map(async (row) => {
          const sendReq = {
            adminId,
            accademicId: academicYearFilter,
            feeId: row.feeId,
            dbResult: '',
            deletedId: 0,
          };
          return sendReq;
        }) || []
      );
      const deleteResponse = await dispatch(DeleteAllBasicFee(sendRequests));

      console.log('deleteResponse', deleteResponse);
      if (deleteResponse && Array.isArray(deleteResponse.payload)) {
        const results: BasicFeeMappedDeleteAllDataType[] = deleteResponse.payload;
        const errorMessages = results.find((result) => result.dbResult === 'Failed');
        const successMessages = results.find((result) => result.dbResult === 'Success');

        if (!errorMessages) {
          // setSectionAmounts({});
          // loadTermFeeList(currentTermFeeListRequest);

          const deleteSuccessMessage = (
            <SuccessMessage loop={false} jsonIcon={deleteSuccess} message="Delete All Successfully" />
          );
          await confirm(deleteSuccessMessage, 'Success', { okLabel: 'Ok', showOnlyOk: true });
          const updatedTermFeeDetails = termFeeDetails.map((item) => {
            return {
              ...item,
              feeMapped: Array.isArray(item.feeMapped)
                ? item.feeMapped.filter(
                    (f) => !selectedRows.feeMapped.some((e: FeeMappedType) => e.academicFeeId === f.academicFeeId)
                  )
                : [], // Ensure feeMapped is an array or default to an empty array
            };
          });

          setBasicFeeDetails(updatedTermFeeDetails);
        } else if (!successMessages) {
          const deleteErrorMessage = <DeleteMessage loop={false} jsonIcon={errorIcon} message="Delete Failed" />;
          await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
        } else {
          const deleteErrorMessage = (
            <DeleteMessage loop={false} jsonIcon={errorIcon} message="Something went wrong please try again" />
          );
          await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
        }
        // loadTermFeeList(currentTermFeeListRequest);
        setSelectedRows([]);
        // setBasicFeeDetails((prevDetails) => prevDetails.filter((item) => !selectedRows.includes(item)));
      }
    }
  }, [confirm, dispatch, selectedRows, adminId, academicYearFilter, termFeeDetails]);
  const anyCellNonZeroOrEmpty = Object.values(sectionAmounts).some((value) => value !== 0 && value !== '');

  const isSelected = useCallback((row: BasicFeeDetailsType) => selectedRows?.indexOf(row) !== -1, [selectedRows]);

  const skeletonRows = 10;

  const handleRowClick = useCallback(
    (row: BasicFeeDetailsType) => {
      const selectedIndex = selectedRows?.indexOf(row);
      let newSelected: BasicFeeDetailsType[] = [];

      if (selectedIndex === -1) {
        newSelected = newSelected.concat(selectedRows, row);
      } else if (selectedIndex === 0) {
        newSelected = newSelected.concat(selectedRows.slice(1));
      } else if (selectedIndex === selectedRows.length - 1) {
        newSelected = newSelected.concat(selectedRows.slice(0, -1));
      } else if (selectedIndex > 0) {
        newSelected = newSelected.concat(selectedRows.slice(0, selectedIndex), selectedRows.slice(selectedIndex + 1));
      }

      setSelectedRows(newSelected);
    },
    [selectedRows]
  );

  const handleRowAllClick = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      if (event.target.checked) {
        // Set all non-disabled rows as selected
        setSelectedRows(termFeeDetails.filter((row) => row.feeMapped !== null));
      } else {
        setSelectedRows([]);
      }
    },
    [termFeeDetails, setSelectedRows]
  );

  // Pagination handlers
  const handlePageChange = useCallback(
    (event: any, newPage: any) => {
      setCurrentPage(newPage); // Update current page
    },
    [setCurrentPage]
  );

  const handleChangeRowsPerPage = useCallback(
    (event: any) => {
      setRowsPerPage(parseInt(event.target.value, 10)); // Update rows per page
      // Reset to first page when changing rows per page
    },
    [setRowsPerPage]
  );
  // Calculate the paginated data
  const paginatedData = termFeeDetails.slice(currentPage * rowsPerPage, currentPage * rowsPerPage + rowsPerPage);

  const pageProps = useMemo(
    () => ({
      rowsPerPageOptions: [10, 20, 30, 50, 100, 200, 300, 500],
      pageNumber: currentPage,
      pageSize: rowsPerPage,
      totalRecords: termFeeDetails.length,
      onPageChange: handlePageChange,
      onRowsPerPageChange: handleChangeRowsPerPage,
    }),
    [handleChangeRowsPerPage, handlePageChange, currentPage, rowsPerPage, termFeeDetails]
  );

  const FeeSettingColumns: DataTableColumn<any>[] = useMemo(() => {
    const baseColumns: DataTableColumn<any>[] = [
      {
        name: 'selection',
        renderHeader: () => {
          return (
            <Checkbox
              onChange={handleRowAllClick}
              indeterminate={selectedRows?.length > 0 && selectedRows?.length < termFeeDetails.length}
              checked={selectedRows?.length > 0}
            />
          );
        },
        renderCell: (row) => {
          const isRowSelected = isSelected(row);
          return (
            <Checkbox checked={isRowSelected} disabled={row.feeMapped === null} onClick={() => handleRowClick(row)} />
          );
        },
      },
      {
        name: 'feeTitle',
        renderHeader: () => (
          <Typography width={100} className="header-color" variant="subtitle2" fontSize={13}>
            Fee Title
          </Typography>
        ),
        renderCell: (row) => (
          <Typography width={150} variant="subtitle2" fontSize={13}>
            {row.feeTitle}
          </Typography>
        ),
      },
      {
        name: 'applicableTo',
        renderHeader: () => (
          <Typography width={100} className="header-color" variant="subtitle2" fontSize={13}>
            Applicable To
          </Typography>
        ),
        renderCell: (row) => (
          // <Select labelId="classStatusFilter" id="classStatusFilterSelect" value="">
          //   <MenuItem value={-1}>All</MenuItem>
          //   <MenuItem>Standard</MenuItem>
          //   <MenuItem>Optional</MenuItem>
          // </Select>
          <Select
            // labelId="academicYearFilter"
            // id="academicYearFilterSelect"
            sx={{ height: 31, width: 120 }}
            value={feeTypeByRow[row.feeId] || row.feeType} // Use feeTypeByRow if available, otherwise use default feeType
            onChange={(e) => handleChange(e, row)} // Pass row to handleChange
            placeholder="Select"
          >
            {FEE_TYPE_OPTIONS.map((opt) => (
              <MenuItem key={opt.id} value={opt.id}>
                {opt.name}
              </MenuItem>
            ))}
          </Select>
        ),
      },
      {
        name: 'termFeeAmount',
        renderHeader: () => (
          <Typography width={120} className="header-color" variant="subtitle2" fontSize={13}>
            Term Fee Amount
          </Typography>
        ),
        renderCell: (row) => {
          const isSelectedRow = selectedRows.includes(row);
          const sectionIds = TermFeeData.sections.map((m: ClassSectionType) => m.sectionId);
          const feeMapped = Array.isArray(row.feeMapped) ? row.feeMapped : [];
          const id = 1;
          // Check if all sectionIds in feeMapped are included in sectionIds
          const allAmountCheck = sectionIds.every((sectionId: any) =>
            feeMapped.some((f: FeeMappedType) => f.sectionId === sectionId)
          );
          return (
            <TextField
              disabled={!isSelectedRow && allAmountCheck}
              // key={`cell_${row.feeId}_${item.sectionId}`}
              name="amount"
              type="number"
              variant="outlined"
              size="small"
              placeholder="Enter amount"
              sx={{
                // backgroundColor: isLight ? theme.palette.common.white : theme.palette.grey[900],
                borderRadius: 1,
                '& .MuiInputBase-input.Mui-disabled': {
                  backgroundColor: theme.palette.grey[300],
                  borderRadius: 1,
                  padding: 1,
                },
                '& .MuiInputBase-input': {
                  borderRadius: 1,
                  padding: 1,
                },
              }}
              onChange={(e) => {
                setTextBoxValue(true);
                // Pass each sectionId with the amount change
                sectionIds
                  .filter(
                    (sectionId: number) =>
                      !feeMapped.some((f: FeeMappedType) => f.sectionId === sectionId && !isSelectedRow)
                  )
                  .forEach((sectionId: number) => {
                    handleAmountChange(e, row, sectionId, id);
                  });
              }}
              // value={}
              defaultValue={textBoxValue === true && sectionAmounts[`${row.feeId}_${sectionIds[0]}`]}
              InputProps={{
                style: {
                  // padding: '0px 5px 0px 0px',
                  minWidth: 85,
                  color: isSelectedRow ? theme.palette.warning.main : '',
                  // backgroundColor: '#fbf7e6',
                },
              }}
            />
          );
        },
      },
    ];

    const headerAndBodyCells = TermFeeData.sections
      ? TermFeeData.sections.map((item: ClassSectionType) => ({
          name: `${item.sectionId}`,
          renderHeader: () => (
            <Stack className="month-name" key={item.sectionId}>
              <Typography className="header-color" variant="subtitle2" fontSize={12}>
                {item.sectionName}
              </Typography>
            </Stack>
          ),
          renderCell: (row: any) => {
            const monthData =
              row.feeMapped?.find(
                (data: FeeMappedType) => data.feeId === row.feeId && data.sectionId === item.sectionId
              ) ?? [];
            const isSelectedRow = selectedRows.includes(row);
            const id = 0;

            const { amount, academicFeeId } = monthData;
            return (
              <TextField
                inputRef={(el) => {
                  textFieldRefs.current[`${row.feeId}_${item.sectionId}`] = el;
                }}
                disabled={!isSelectedRow && amount}
                key={`cell_${row.feeId}_${item.sectionId}`}
                name="amount"
                type="number"
                variant="outlined"
                size="small"
                sx={{
                  '& .MuiInputBase-input.Mui-disabled': {
                    // backgroundColor: '#f5fcf9',
                    borderRadius: 1,
                    padding: 1,
                  },
                  '& .MuiInputBase-input': {
                    borderRadius: 1,
                    padding: 1,
                  },
                }}
                onChange={(e) => {
                  if (item.sectionId && !item[0]?.sectionId) {
                    setTextBoxValue(false);
                  }
                  handleAmountChange(e, row, item.sectionId, id);
                }}
                defaultValue={amount}
                value={sectionAmounts[`${row.feeId}_${item.sectionId}`]}
                // defaultValue={amount || ''}
                InputProps={{
                  style: {
                    padding: '0px 5px 0px 0px',
                    minWidth: 85,
                    color: isSelectedRow && amount ? theme.palette.warning.main : '',
                    // backgroundColor: '#fbf7e6',
                  },
                  endAdornment: !isSelectedRow && amount && (
                    <InputAdornment position="end" sx={{ m: 0 }}>
                      <IconButton
                        onClick={() => amount && handleDeleteCell(academicFeeId, item.sectionId)}
                        size="small"
                        edge="end"
                      >
                        <DeleteIcon sx={{ fontSize: '16px' }} />
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
            );
          },
        }))
      : [TermFeeData.sections, selectedRows, sectionAmounts, handleAmountChange];

    const baseColumns2: DataTableColumn<any>[] = [
      {
        name: 'action',
        renderHeader: () => (
          <Typography
            className="header-color"
            width={100}
            // className="header-color"
            variant="subtitle2"
            fontSize={13}
          >
            Action
          </Typography>
        ),
        renderCell: (row) => {
          const isSelectedRow = selectedRows.includes(row);
          return (
            <Stack direction="row" gap={1}>
              {showSuccessIcon[row.feeId] === true ? (
                <Stack
                  width="64px"
                  height="24px"
                  direction="row"
                  justifyContent="center"
                  flexDirection="column"
                  alignItems="center"
                >
                  {succesResponse === 'Success' ? (
                    <>
                      <Lottie animationData={Success} loop={false} style={{ width: '30px' }} />
                      <Typography color={theme.palette.success.main} fontSize={7} variant="subtitle2">
                        Saved
                      </Typography>
                    </>
                  ) : (
                    <>
                      <Lottie animationData={Updated} loop={false} style={{ width: '30px' }} />
                      <Typography color={theme.palette.warning.main} fontSize={7} variant="subtitle2">
                        Updated
                      </Typography>
                    </>
                  )}
                </Stack>
              ) : (
                <LoadingButton
                  loading={individualSaveLoading[row.feeId]}
                  onClick={() => handleSave(row)}
                  variant="contained"
                  size="small"
                  color={isSelectedRow ? 'warning' : 'success'}
                  disabled={!individualSaveButtonEnabled[row.feeId] || saving}
                  sx={{ py: 0.5, fontSize: '10px' }}
                >
                  {!individualSaveLoading[row.feeId] ? (isSelectedRow ? 'Update' : 'Save') : ''}
                </LoadingButton>
              )}
              <Button
                disabled={row.feeMapped === null}
                onClick={() => handleDeleteRow(row)}
                variant="contained"
                size="small"
                color="error"
                sx={{ py: 0.5, fontSize: '10px' }}
              >
                Delete
              </Button>
            </Stack>
          );
        },
      },
    ];

    return [...baseColumns, ...headerAndBodyCells, ...baseColumns2];
  }, [
    handleAmountChange,
    feeTypeByRow,
    handleDeleteCell,
    handleDeleteRow,
    handleSave,
    individualSaveButtonEnabled,
    individualSaveLoading,
    saving,
    sectionAmounts,
    selectedRows,
    showSuccessIcon,
    succesResponse,
    theme,
    textBoxValue,
    handleRowClick,
    isSelected,
    handleRowAllClick,
    termFeeDetails,
    TermFeeData,
  ]);

  return (
    <Page title="Fees Setting">
      <FeeSettingRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between" mb={2} alignItems="center">
            <Typography variant="h6" fontSize={17}>
              Fees Setting
            </Typography>
            <Stack direction="row" alignItems="center" gap={1}>
              {selectedRows.length > 0 && (
                <Tooltip title="Delete">
                  <IconButton
                    sx={{ visibility: { xs: 'hidden', sm: 'visible' } }}
                    aria-label="delete"
                    color="error"
                    onClick={handleDeleteMultiple}
                  >
                    <DeleteIcon />
                  </IconButton>
                </Tooltip>
              )}
              {showFilter === false ? (
                <Tooltip title="Search">
                  <IconButton aria-label="delete" color="primary" onClick={() => setShowFilter((x) => !x)}>
                    <SearchIcon />
                  </IconButton>
                </Tooltip>
              ) : (
                <IconButton aria-label="delete" color="primary" onClick={() => setShowFilter((x) => !x)}>
                  <IoIosArrowUp />
                </IconButton>
              )}
              <Button sx={{ borderRadius: '20px' }} variant="outlined" size="small" onClick={toggleDrawerOpen}>
                <MdAdd size="20px" /> Create
              </Button>
            </Stack>
          </Stack>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate onReset={handleReset}>
                <Grid pb={4} container spacing={2} alignItems="end">
                  <Grid item xxl={2} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Select
                        labelId="academicYearFilter"
                        id="academicYearFilterSelect"
                        value={academicYearFilter.toString()}
                        onChange={handleYearChange}
                        placeholder="Select Year"
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Year
                        </MenuItem>
                        {YearData.map((opt) => (
                          <MenuItem key={opt.accademicId} value={opt.accademicId}>
                            {opt.accademicTime}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Select Type
                      </Typography>
                      <Select
                        labelId="feeTypeFilter"
                        id="feeTypeFilterSelect"
                        value={feeTypeFilter.toString()}
                        onChange={handleFeeTypeChange}
                        placeholder="Select Type"
                      >
                        <MenuItem sx={{ display: 'none' }} value={0}>
                          Select Type
                        </MenuItem>
                        {FEE_TYPE_ID_OPTIONS.map((opt) => (
                          <MenuItem key={opt.id} value={opt.id}>
                            {opt.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto">
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button type="reset" variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>
            {feeTypeFilter !== 0 && (
              <Box pr={1} mt={!showFilter ? 2 : 0} display="flex" justifyContent="end">
                <LoadingButton
                  loading={savingAll}
                  onClick={handleAllSave}
                  disabled={amountEnter === 0 || amountEnter === '' || !anyCellNonZeroOrEmpty}
                  color={selectedRows.length > 0 ? 'warning' : 'success'}
                  startIcon={<SaveIcon fontSize="small" />}
                  size="small"
                  variant="contained"
                  sx={{ py: 0.2, px: 1, fontSize: 12 }}
                >
                  {selectedRows.length > 0 ? 'Update All ' : 'Save All'}
                </LoadingButton>
              </Box>
            )}
            <Paper className="card-table-container" sx={{ marginTop: '12px' }}>
              {TermFeeStatus === 'loading' && paginatedData.length === 0 ? (
                <Table>
                  <TableBody>
                    {[...Array(rowsPerPage)].map((_, rowIndex: number) => (
                      <TableRow key={rowIndex}>
                        {FeeSettingColumns.map((column, colIndex) => (
                          <TableCell key={colIndex}>
                            <Skeleton variant="text" height={20} />
                          </TableCell>
                        ))}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <DTVirtuoso
                  tableStyles={{ minWidth: { xs: '1100px' } }}
                  // ShowCheckBox
                  setSelectedRows={setSelectedRows}
                  selectedRows={selectedRows}
                  columns={FeeSettingColumns}
                  data={feeTypeFilter === 0 ? [] : paginatedData}
                  getRowKey={getRowKey}
                  fetchStatus={TermFeeStatus}
                  // fetchStatus="success"
                  showHorizontalScroll
                  PaginationProps={pageProps}
                  allowPagination
                />
              )}
            </Paper>
          </div>
          {/* <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 3 }}>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="secondary">
                Cancel
              </Button>
              <Button onClick={handleClickOpen} variant="contained" color="primary">
                Update
              </Button>
            </Stack>
          </Box> */}
        </Card>
      </FeeSettingRoot>
      <TemporaryDrawer
        onClose={toggleDrawerClose}
        state={drawerOpen}
        Title="Create Fee Title"
        DrawerContent={<CreateEditBasicFeeTitleForm onSave={handleFeeTitleSave} onCancel={toggleDrawerClose} />}
      />
      <Popup
        size="xs"
        state={popup}
        onClose={handleClickClose}
        popupContent={<SuccessMessage message="Fees Updated Successfully" />}
      />
      {/* <TestScrolling /> */}
    </Page>
  );
}
