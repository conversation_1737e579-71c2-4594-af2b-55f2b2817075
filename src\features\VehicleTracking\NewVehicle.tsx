/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import { Box, Divider, Grid, Stack, TextField, Button, Typography, Card } from '@mui/material';
import styled from 'styled-components';
import typography from '@/theme/typography';
import Popup from '@/components/shared/Popup/Popup';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';

const NewVehicleRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  h6 {
    font-weight: 600;
  }
  .MuiTextField-root:hover .MuiInputBase-input {
    cursor: text;
  }
`;

function NewVehicle() {
  const [popup, setPopup] = React.useState(false);

  const handleClickOpen = () => setPopup(true);
  const handleClickClose = () => setPopup(false);

  return (
    <Page title="Add New Vehicle">
      <NewVehicleRoot>
        <Card elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Typography variant="h6" fontSize={17}>
            Add New Vehicle
          </Typography>
          <Divider />
          <Grid pb={4} pt={2} container spacing={3}>
            <Grid item lg={6} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Vehicle Reg No.
              </Typography>
              <TextField placeholder="Enter Vehicle No." fullWidth />
            </Grid>
            <Grid item lg={6} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Vehicle Name
              </Typography>
              <TextField placeholder="Enter Name" fullWidth />
            </Grid>
            <Grid item lg={6} xs={12}>
              <Typography variant="h6" fontSize={14}>
                SIM No.
              </Typography>
              <TextField placeholder="Enter SIM No." fullWidth />
            </Grid>
            <Grid item lg={6} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Exe Name
              </Typography>
              <TextField placeholder="Enter Exe Name" fullWidth />
            </Grid>
            <Grid item lg={6} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Driver Name
              </Typography>
              <TextField placeholder="Enter Driver Name" fullWidth />
            </Grid>
            <Grid item lg={6} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Vehicle Root
              </Typography>
              <TextField placeholder="Select Type" fullWidth />
            </Grid>
          </Grid>
          <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' } }}>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="secondary" sx={{ fontFamily: typography.fontFamily }}>
                Cancel
              </Button>
              <Button onClick={handleClickOpen} variant="contained" color="primary">
                Add
              </Button>
            </Stack>
          </Box>
        </Card>
        <Popup
          size="xs"
          state={popup}
          onClose={handleClickClose}
          popupContent={<SuccessMessage message="Vehicle Enrolled Successfully" />}
        />
      </NewVehicleRoot>
    </Page>
  );
}

export default NewVehicle;
