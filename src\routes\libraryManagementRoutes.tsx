import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import Loadable from '@/components/shared/Loadable';

const LibraryManagement = Loadable(lazy(() => import('@/pages/LibraryManagement')));
const EnrollBooks = Loadable(lazy(() => import('@/features/LibraryManagement/EnrollBooks')));
const BookList = Loadable(lazy(() => import('@/features/LibraryManagement/BookList/BookList')));
const IssueList = Loadable(lazy(() => import('@/features/LibraryManagement/IssueList/IssueList')));
const CategoryManager = Loadable(lazy(() => import('@/features/LibraryManagement/CategoryManager')));
const LocationManager = Loadable(lazy(() => import('@/features/LibraryManagement/LocationManager')));
const BookFineSetting = Loadable(lazy(() => import('@/features/LibraryManagement/BookFineSetting')));

export const libraryManagementRoutes: RouteObject[] = [
  {
    path: '/library-management',
    element: <LibraryManagement />,
    children: [
      {
        path: 'new-book',
        element: <EnrollBooks />,
      },
      {
        path: 'book-list',
        element: <BookList />,
      },
      {
        path: 'issue-list',
        element: <IssueList />,
      },
      {
        path: 'category',
        element: <CategoryManager />,
      },
      {
        path: 'location',
        element: <LocationManager />,
      },
      {
        path: 'fine-setting',
        element: <BookFineSetting />,
      },
    ],
  },
];
