import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, TextField, Button, Stack, FormControl } from '@mui/material';
import { useFormik } from 'formik';
import { StudentListInfo } from '@/types/StudentManagement';
import * as Yup from 'yup';
import LoadingButton from '@mui/lab/LoadingButton';

export type CreateEditStudentFormProps = {
  onSave: (values: StudentListInfo, mode: 'create' | 'edit') => void;
  onCancel: (mode: 'create' | 'edit') => void;
  onClose: (mode: 'create' | 'edit') => void;
  studentDetail: StudentListInfo;
  isSubmitting: boolean;
};

const CreateEditStudentValidationSchema = Yup.object({
  // studentName: Yup.string().required('Please enter Student Name'),
  // studentDescription: Yup.string().required('Please enter Student description'),
  // studentId: Yup.string().required('Please enter Student Id'),
  // admissionNumber: Yup.string().required('Please enter '),
  // admissionDate: Yup.string().required('Please enter admissionDate '),
  studentFname: Yup.string().required('Please enter Student Fname '),
  // studentMname: Yup.string().required('Please enter Student Mname '),
  // studentLname: Yup.string().required('Please enter Student Lname'),
  // studentGender: Yup.string().required('Please enter Student Gender'),
  // studentDob: Yup.string().required('Please enter Student Dob'),
  // studentBloodGroup: Yup.string().required('Please enter Student BloodGroup '),
  // studentBirthplace: Yup.string().required('Please enter Student Birthplace '),
  // studentNationality: Yup.string().required('Please enter Student Nationality '),
  // studentMotherTongue: Yup.string().required('Please enter Student Mother Tongue '),
  // studentReligion: Yup.string().required('Please enter Student Religion '),
  // studentCaste: Yup.string().required('Please enter Student Caste '),
  // studentCastType: Yup.string().required('Please enter Student CastType '),
  // studentFatherName: Yup.string().required('Please enter Student FatherName '),
  // studentMotherName: Yup.string().required('Please enter Student MotherName '),
  // studentFatherQualification: Yup.string().required('Please enter Student Father Qualification '),
  // studentMotherQualification: Yup.string().required('Please enter Student Mother Qualification '),
  // studentPhoneNumber: Yup.string().required('Please enter Student PhoneNumber '),
  // studentGuardianName: Yup.string().required('Please enter Student GuardianName '),
  // studentGuardianNumber: Yup.string().required('Please enter Student GuardianNumber '),
  // studentPAddress: Yup.string().required('Please enter Student PAddress'),
  // classId: Yup.string().required('Please enter classId'),
  // studAcademicId: Yup.string().required('Please enter '),
  // academicId: Yup.string().required('Please enter '),
  // classSection: Yup.string().required('Please enter   ClassSection '),
  // classDescription: Yup.string().required('Please enter ClassDescription '),
  // studentEmailId: Yup.string().required('Please enter Student EmailId '),
  // studentImage: Yup.string().required('Please enter Student Image '),
  // studentLastStudied: Yup.string().required('Please enter Student LastStudied '),
  // studentRollNumber: Yup.string().required('Please enter Student RollNumber'),
  // sessionId: Yup.string().required('Please enter sessionId'),
  // studentStatus: Yup.string().required('Please enter Student Status '),
  // studentCAddress: Yup.string().required('Please enter Student CAddress '),
  // studentFatherOccupation: Yup.string().required('Please enter Student FatherOccupation '),
  // studentMotherOccupation: Yup.string().required('Please enter Student MotherOccupation '),
  // className: Yup.string().required('Please enter className '),
  // academicTime: Yup.string().required('Please enter academicTime '),
});

const CreateEditStudentForm = ({ studentDetail, onSave, onCancel, isSubmitting }: CreateEditStudentFormProps) => {
  const mode = studentDetail.studentId === 0 ? 'create' : 'edit';
  const {
    values: {
      admissionNumber,
      admissionDate,
      studentFname,
      studentMname,
      studentLname,
      studentGender,
      studentDob,
      studentBloodGroup,
      studentBirthplace,
      studentNationality,
      studentMotherTongue,
      studentReligion,
      studentCaste,
      studentCastType,
      studentFatherName,
      studentMotherName,
      studentFatherQualification,
      studentMotherQualification,
      studentPhoneNumber,
      studentGuardianName,
      studentGuardianNumber,
      studentPAddress,
      classId,
      studAcademicId,
      academicId,
      classSection,
      classDescription,
      studentEmailId,
      studentImage,
      studentLastStudied,
      studentRollNumber,
      sessionId,
      studentStatus,
      studentCAddress,
      studentFatherOccupation,
      studentMotherOccupation,
      className,
      academicTime,
    },
    handleChange,
    handleSubmit,
    touched,
    errors,
  } = useFormik<StudentListInfo>({
    initialValues: {
      studentId: studentDetail.studentId,
      admissionNumber: studentDetail.admissionNumber,
      admissionDate: studentDetail.admissionDate,
      studentFname: studentDetail.studentFname,
      studentMname: studentDetail.studentMname,
      studentLname: studentDetail.studentLname,
      studentGender: studentDetail.studentGender,
      studentDob: studentDetail.studentDob,
      studentBloodGroup: studentDetail.studentBloodGroup,
      studentBirthplace: studentDetail.studentBirthplace,
      studentNationality: studentDetail.studentNationality,
      studentMotherTongue: studentDetail.studentMotherTongue,
      studentReligion: studentDetail.studentReligion,
      studentCaste: studentDetail.studentCaste,
      studentCastType: studentDetail.studentCastType,
      studentFatherName: studentDetail.studentFatherName,
      studentMotherName: studentDetail.studentMotherName,
      studentFatherQualification: studentDetail.studentFatherQualification,
      studentMotherQualification: studentDetail.studentMotherQualification,
      studentPhoneNumber: studentDetail.studentPhoneNumber,
      studentGuardianName: studentDetail.studentGuardianName,
      studentGuardianNumber: studentDetail.studentGuardianNumber,
      studentPAddress: studentDetail.studentPAddress,
      classId: studentDetail.classId,
      studAcademicId: studentDetail.studAcademicId,
      academicId: studentDetail.academicId,
      classSection: studentDetail.classSection,
      classDescription: studentDetail.classDescription,
      studentEmailId: studentDetail.studentEmailId,
      studentImage: studentDetail.studentImage,
      studentLastStudied: studentDetail.studentLastStudied,
      studentRollNumber: studentDetail.studentRollNumber,
      sessionId: studentDetail.sessionId,
      studentStatus: studentDetail.studentStatus,
      studentCAddress: studentDetail.studentCAddress,
      studentFatherOccupation: studentDetail.studentFatherOccupation,
      studentMotherOccupation: studentDetail.studentMotherOccupation,
      className: studentDetail.className,
      academicTime: studentDetail.academicTime,
    },
    validationSchema: CreateEditStudentValidationSchema,
    onSubmit: (values) => {
      onSave(values, mode);
    },
  });

  // const [editedStudent, setEditedStudent] = useState(studentDetail);

  // const handleChange = (e: any) => {
  //   const { name, value } = e.target;
  //   setEditedStudent((prevStudent: any) => ({
  //     ...prevStudent,
  //     [name]: value,
  //   }));
  // };

  // const handleUpdate = () => {
  //   onUpdate(editedStudent.id, editedStudent);
  // };

  return (
    <form noValidate onSubmit={handleSubmit}>
      <Grid container spacing={2} m={4} maxWidth="90%">
        <Grid item xs={12}>
          <Typography variant="h6" mb={2}>
            Edit Student Details
          </Typography>
        </Grid>
        <Grid item md={3} xs={6}>
          <FormControl>
            <TextField
              placeholder="Admission No."
              name="admissionNumber"
              value={admissionNumber}
              onChange={handleChange}
              error={touched.admissionNumber && !!errors.admissionNumber}
              helperText={errors.admissionNumber}
              disabled={isSubmitting}
              fullWidth
            />
          </FormControl>
        </Grid>
        <Grid item md={3} xs={6}>
          <FormControl>
            <TextField
              placeholder="Admission Date"
              name="admissionDate"
              value={admissionDate}
              onChange={handleChange}
              error={touched.admissionDate && !!errors.admissionDate}
              helperText={errors.admissionDate}
              disabled={isSubmitting}
              fullWidth
            />
          </FormControl>
        </Grid>
        <Grid item md={3} xs={6}>
          <FormControl>
            <TextField
              placeholder="First Name"
              name="studentFname"
              value={studentFname}
              onChange={handleChange}
              error={touched.studentFname && !!errors.studentFname}
              helperText={errors.studentFname}
              disabled={isSubmitting}
              fullWidth
            />
          </FormControl>
        </Grid>
        <Grid item md={3} xs={6}>
          <FormControl>
            <TextField
              placeholder="Middle Name"
              name="studentMname"
              value={studentMname}
              onChange={handleChange}
              error={touched.studentMname && !!errors.studentMname}
              helperText={errors.studentMname}
              disabled={isSubmitting}
              fullWidth
            />
          </FormControl>
        </Grid>
        <Grid item md={3} xs={6}>
          <FormControl>
            <TextField
              placeholder="Last Name"
              name="studentLname"
              value={studentLname}
              onChange={handleChange}
              error={touched.studentLname && !!errors.studentLname}
              helperText={errors.studentLname}
              disabled={isSubmitting}
              fullWidth
            />
          </FormControl>
        </Grid>
        <Grid item md={3} xs={6}>
          <FormControl>
            <TextField
              placeholder="Gender"
              name="studentGender"
              value={studentGender}
              onChange={handleChange}
              error={touched.studentGender && !!errors.studentGender}
              helperText={errors.studentGender}
              disabled={isSubmitting}
              fullWidth
            />
          </FormControl>
        </Grid>
        <Grid item md={3} xs={6}>
          <FormControl>
            <TextField
              placeholder="Dob"
              name="studentDob"
              value={studentDob}
              onChange={handleChange}
              error={touched.studentDob && !!errors.studentDob}
              helperText={errors.studentDob}
              disabled={isSubmitting}
              fullWidth
            />
          </FormControl>
        </Grid>
        <Grid item md={3} xs={6}>
          <FormControl>
            <TextField
              placeholder="BloodGroup"
              name="studentBloodGroup"
              value={studentBloodGroup}
              onChange={handleChange}
              error={touched.studentBloodGroup && !!errors.studentBloodGroup}
              helperText={errors.studentBloodGroup}
              disabled={isSubmitting}
              fullWidth
            />
          </FormControl>
        </Grid>
        <Grid item md={3} xs={6}>
          <FormControl>
            <TextField
              placeholder="Birthplace"
              name="studentBirthplace"
              value={studentBirthplace}
              onChange={handleChange}
              error={touched.studentBirthplace && !!errors.studentBirthplace}
              helperText={errors.studentBirthplace}
              disabled={isSubmitting}
              fullWidth
            />
          </FormControl>
        </Grid>
        <Grid item md={3} xs={6}>
          <FormControl>
            <TextField
              placeholder="Nationality"
              name="studentNationality"
              value={studentNationality}
              onChange={handleChange}
              error={touched.studentNationality && !!errors.studentNationality}
              helperText={errors.studentNationality}
              disabled={isSubmitting}
              fullWidth
            />
          </FormControl>
        </Grid>
        <Grid item md={3} xs={6}>
          <FormControl>
            <TextField
              placeholder="MotherTongue"
              name="studentMotherTongue"
              value={studentMotherTongue}
              onChange={handleChange}
              error={touched.studentMotherTongue && !!errors.studentMotherTongue}
              helperText={errors.studentMotherTongue}
              disabled={isSubmitting}
              fullWidth
            />
          </FormControl>
        </Grid>
        <Grid item md={3} xs={6}>
          <FormControl>
            <TextField
              placeholder="Religion"
              name="studentReligion"
              value={studentReligion}
              onChange={handleChange}
              error={touched.studentReligion && !!errors.studentReligion}
              helperText={errors.studentReligion}
              disabled={isSubmitting}
              fullWidth
            />
          </FormControl>
        </Grid>
        <Grid item md={3} xs={6}>
          <FormControl>
            <TextField
              placeholder="Caste"
              name="studentCaste"
              value={studentCaste}
              onChange={handleChange}
              error={touched.studentCaste && !!errors.studentCaste}
              helperText={errors.studentCaste}
              disabled={isSubmitting}
              fullWidth
            />
          </FormControl>
        </Grid>
        <Grid item md={3} xs={6}>
          <FormControl>
            <TextField
              placeholder="CastType"
              name="studentCastType"
              value={studentCastType}
              onChange={handleChange}
              error={touched.studentCastType && !!errors.studentCastType}
              helperText={errors.studentCastType}
              disabled={isSubmitting}
              fullWidth
            />
          </FormControl>
        </Grid>
        <Grid item md={3} xs={6}>
          <FormControl>
            <TextField
              placeholder="FatherName"
              name="studentFatherName"
              value={studentFatherName}
              onChange={handleChange}
              error={touched.studentFatherName && !!errors.studentFatherName}
              helperText={errors.studentFatherName}
              disabled={isSubmitting}
              fullWidth
            />
          </FormControl>
        </Grid>
        <Grid item md={3} xs={6}>
          <FormControl>
            <TextField
              placeholder="MotherName"
              name="studentMotherName"
              value={studentMotherName}
              onChange={handleChange}
              error={touched.studentMotherName && !!errors.studentMotherName}
              helperText={errors.studentMotherName}
              disabled={isSubmitting}
              fullWidth
            />
          </FormControl>
        </Grid>
        <Grid item md={3} xs={6}>
          <FormControl>
            <TextField
              placeholder="FatherQualification"
              name="studentFatherQualification"
              value={studentFatherQualification}
              onChange={handleChange}
              error={touched.studentFatherQualification && !!errors.studentFatherQualification}
              helperText={errors.studentFatherQualification}
              disabled={isSubmitting}
              fullWidth
            />
          </FormControl>
        </Grid>
        <Grid item md={3} xs={6}>
          <FormControl>
            <TextField
              placeholder="MotherQualification"
              name="studentMotherQualification"
              value={studentMotherQualification}
              onChange={handleChange}
              error={touched.studentMotherQualification && !!errors.studentMotherQualification}
              helperText={errors.studentMotherQualification}
              disabled={isSubmitting}
              fullWidth
            />
          </FormControl>
        </Grid>
        <Grid item md={3} xs={6}>
          <FormControl>
            <TextField
              placeholder="PhoneNumber"
              name="studentPhoneNumber"
              value={studentPhoneNumber}
              onChange={handleChange}
              error={touched.studentPhoneNumber && !!errors.studentPhoneNumber}
              helperText={errors.studentPhoneNumber}
              disabled={isSubmitting}
              fullWidth
            />
          </FormControl>
        </Grid>
        <Grid item md={3} xs={6}>
          <FormControl>
            <TextField
              placeholder="GuardianName"
              name="studentGuardianName"
              value={studentGuardianName}
              onChange={handleChange}
              error={touched.studentGuardianName && !!errors.studentGuardianName}
              helperText={errors.studentGuardianName}
              disabled={isSubmitting}
              fullWidth
            />
          </FormControl>
        </Grid>
        <Grid item md={3} xs={6}>
          <FormControl>
            <TextField
              placeholder="GuardianNumber"
              name="studentGuardianNumber"
              value={studentGuardianNumber}
              onChange={handleChange}
              error={touched.studentGuardianNumber && !!errors.studentGuardianNumber}
              helperText={errors.studentGuardianNumber}
              disabled={isSubmitting}
              fullWidth
            />
          </FormControl>
        </Grid>
        <Grid item md={3} xs={6}>
          <FormControl>
            <TextField
              placeholder="PAddress"
              name="studentPAddress"
              value={studentPAddress}
              onChange={handleChange}
              error={touched.studentPAddress && !!errors.studentPAddress}
              helperText={errors.studentPAddress}
              disabled={isSubmitting}
              fullWidth
            />
          </FormControl>
        </Grid>
        <Grid item md={3} xs={6}>
          <FormControl>
            <TextField
              placeholder="classId"
              name="classId"
              value={classId}
              onChange={handleChange}
              error={touched.classId && !!errors.classId}
              helperText={errors.classId}
              disabled={isSubmitting}
              fullWidth
            />
          </FormControl>
        </Grid>
        <Grid item md={3} xs={6}>
          <FormControl>
            <TextField
              placeholder="studAcademicId"
              name="studAcademicId"
              value={studAcademicId}
              onChange={handleChange}
              error={touched.studAcademicId && !!errors.studAcademicId}
              helperText={errors.studAcademicId}
              disabled={isSubmitting}
              fullWidth
            />
          </FormControl>
        </Grid>
        <Grid item md={3} xs={6}>
          <FormControl>
            <TextField
              placeholder="academicId"
              name="academicId"
              value={academicId}
              onChange={handleChange}
              error={touched.academicId && !!errors.academicId}
              helperText={errors.academicId}
              disabled={isSubmitting}
              fullWidth
            />
          </FormControl>
        </Grid>
        <Grid item md={3} xs={6}>
          <FormControl>
            <TextField
              placeholder="classSection"
              name="classSection"
              value={classSection}
              onChange={handleChange}
              error={touched.classSection && !!errors.classSection}
              helperText={errors.classSection}
              disabled={isSubmitting}
              fullWidth
            />
          </FormControl>
        </Grid>
        <Grid item md={3} xs={6}>
          <FormControl>
            <TextField
              placeholder="classDescription"
              name="classDescription"
              value={classDescription}
              onChange={handleChange}
              error={touched.classDescription && !!errors.classDescription}
              helperText={errors.classDescription}
              disabled={isSubmitting}
              fullWidth
            />
          </FormControl>
        </Grid>
        <Grid item md={3} xs={6}>
          <FormControl>
            <TextField
              placeholder="EmailId"
              name="studentEmailId"
              value={studentEmailId}
              onChange={handleChange}
              error={touched.studentEmailId && !!errors.studentEmailId}
              helperText={errors.studentEmailId}
              disabled={isSubmitting}
              fullWidth
            />
          </FormControl>
        </Grid>
        <Grid item md={3} xs={6}>
          <FormControl>
            <TextField
              placeholder="Image"
              name="studentImage"
              value={studentImage}
              onChange={handleChange}
              error={touched.studentImage && !!errors.studentImage}
              helperText={errors.studentImage}
              disabled={isSubmitting}
              fullWidth
            />
          </FormControl>
        </Grid>
        <Grid item md={3} xs={6}>
          <FormControl>
            <TextField
              placeholder="LastStudied"
              name="studentLastStudied"
              value={studentLastStudied}
              onChange={handleChange}
              error={touched.studentLastStudied && !!errors.studentLastStudied}
              helperText={errors.studentLastStudied}
              disabled={isSubmitting}
              fullWidth
            />
          </FormControl>
        </Grid>
        <Grid item md={3} xs={6}>
          <FormControl>
            <TextField
              placeholder="RollNumber"
              name="studentRollNumber"
              value={studentRollNumber}
              onChange={handleChange}
              error={touched.studentRollNumber && !!errors.studentRollNumber}
              helperText={errors.studentRollNumber}
              disabled={isSubmitting}
              fullWidth
            />
          </FormControl>
        </Grid>
        <Grid item md={3} xs={6}>
          <FormControl>
            <TextField
              placeholder="sessionId"
              name="sessionId"
              value={sessionId}
              onChange={handleChange}
              error={touched.sessionId && !!errors.sessionId}
              helperText={errors.sessionId}
              disabled={isSubmitting}
              fullWidth
            />
          </FormControl>
        </Grid>
        <Grid item md={3} xs={6}>
          <FormControl>
            <TextField
              placeholder="Status"
              name="studentStatus"
              value={studentStatus}
              onChange={handleChange}
              error={touched.studentStatus && !!errors.studentStatus}
              helperText={errors.studentStatus}
              disabled={isSubmitting}
              fullWidth
            />
          </FormControl>
        </Grid>
        <Grid item md={3} xs={6}>
          <FormControl>
            <TextField
              placeholder="CAddress"
              name="studentCAddress"
              value={studentCAddress}
              onChange={handleChange}
              error={touched.studentCAddress && !!errors.studentCAddress}
              helperText={errors.studentCAddress}
              disabled={isSubmitting}
              fullWidth
            />
          </FormControl>
        </Grid>
        <Grid item md={3} xs={6}>
          <FormControl>
            <TextField
              placeholder="FatherOccupation"
              name="studentFatherOccupation"
              value={studentFatherOccupation}
              onChange={handleChange}
              error={touched.studentFatherOccupation && !!errors.studentFatherOccupation}
              helperText={errors.studentFatherOccupation}
              disabled={isSubmitting}
              fullWidth
            />
          </FormControl>
        </Grid>
        <Grid item md={3} xs={6}>
          <FormControl>
            <TextField
              placeholder="MotherOccupation"
              name="studentMotherOccupation"
              value={studentMotherOccupation}
              onChange={handleChange}
              error={touched.studentMotherOccupation && !!errors.studentMotherOccupation}
              helperText={errors.studentMotherOccupation}
              disabled={isSubmitting}
              fullWidth
            />
          </FormControl>
        </Grid>
        <Grid item md={3} xs={6}>
          <FormControl>
            <TextField
              placeholder="className"
              name="className"
              value={className}
              onChange={handleChange}
              error={touched.className && !!errors.className}
              helperText={errors.className}
              disabled={isSubmitting}
              fullWidth
            />
          </FormControl>
        </Grid>
        <Grid item md={3} xs={6}>
          <FormControl>
            <TextField
              placeholder="academicTime"
              name="academicTime"
              value={academicTime}
              onChange={handleChange}
              error={touched.academicTime && !!errors.academicTime}
              helperText={errors.academicTime}
              disabled={isSubmitting}
              fullWidth
            />
          </FormControl>
        </Grid>
        <Grid item xs={12}>
          <Stack justifyContent="end" direction="row" spacing={2}>
            <Button onClick={() => onCancel(mode)} fullWidth variant="contained" color="secondary" type="button">
              Cancel
            </Button>
            <LoadingButton
              loading={isSubmitting}
              fullWidth
              variant="contained"
              color="primary"
              type="submit"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Saving...' : 'Save'}
            </LoadingButton>
          </Stack>
        </Grid>
      </Grid>
    </form>
  );
};

export default CreateEditStudentForm;
