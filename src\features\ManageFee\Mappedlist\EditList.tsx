import {
  <PERSON>,
  <PERSON>ack,
  Typography,
  Button,
  Autocomplete,
  TextField,
  TableContainer,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Checkbox,
} from '@mui/material';
import { CLASS_SELECT, YEAR_SELECT } from '@/config/Selection';

type EditListProps = {
  handleClickOpen: () => void;
  toggleDrawer: () => void;
};
export const EditList = ({ handleClickOpen, toggleDrawer }: EditListProps) => (
  <Box>
    <Box mt={3}>
      <Typography variant="h6" fontSize={14}>
        Academic Year
      </Typography>
      <Autocomplete
        sx={{ mb: 2 }}
        options={YEAR_SELECT}
        renderInput={(params) => <TextField {...params} placeholder="Select" />}
      />

      <Typography variant="h6" fontSize={14}>
        Select Class
      </Typography>
      <Autocomplete options={CLASS_SELECT} renderInput={(params) => <TextField {...params} placeholder="Select" />} />
    </Box>
    <TableContainer sx={{ mt: 3 }}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell align="left">
              <Checkbox />
              Fee Title
            </TableCell>
            <TableCell align="right">Amount (INR)</TableCell>
          </TableRow>
        </TableHead>

        <TableBody>
          {[1, 2, 3, 4]?.map(() => (
            <TableRow key={1}>
              <TableCell>
                <Checkbox /> PTA
              </TableCell>
              <TableCell align="right">
                <TextField placeholder="Enter amount" sx={{ width: '60%' }} />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>

    <Box sx={{ justifyContent: 'center', pr: { lg: '5' }, pt: 3, position: 'relative' }}>
      <Stack spacing={2} direction="row">
        <Button onClick={toggleDrawer} fullWidth variant="contained" color="secondary">
          Cancel
        </Button>
        <Button onClick={handleClickOpen} fullWidth variant="contained" color="primary">
          Set Structure
        </Button>
      </Stack>
    </Box>
  </Box>
);
