import Anchor from '@/components/Dashboard/Anchor';
import { Card, Stack, Typography } from '@mui/material';
import styled from 'styled-components';

const InfoCardRoot = styled(Card)`
  padding: 0.9375rem;
  height: 100%;
  position: relative;

  .content {
    display: flex;
    flex-direction: column;

    a {
      align-self: flex-end;
      text-decoration: none;
    }
  }
`;

export type InfoCardProps = {
  title: string;
  content: string;
  url: string;
};

function InfoCard({ title, content, url }: InfoCardProps) {
  return (
    <InfoCardRoot className="infoCard" elevation={0}>
      <div className="content">
        <Typography variant="subtitle2">{title}</Typography>
        <Typography variant="body2" mb={5}>
          {content}
        </Typography>
        <Stack position="absolute" bottom="0.9375rem" right="0.9375rem">
          <Anchor href={url}>View more</Anchor>
        </Stack>
      </div>
    </InfoCardRoot>
  );
}

export default InfoCard;
