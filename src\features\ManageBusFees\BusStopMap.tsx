/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  Collapse,
  FormControl,
} from '@mui/material';
import styled from 'styled-components';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import SearchIcon from '@mui/icons-material/Search';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { STATUS_SELECT } from '@/config/Selection';
import DeleteIcon from '@mui/icons-material/Delete';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import { MdAdd } from 'react-icons/md';

const BusStopMapRoot = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }
    @media screen and (max-height: 815px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      height: calc(100% - 40px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;
const dummyData = [
  {
    slNo: 1,
    name: 'Bus No. 1',
    stop: 'Palakkad',
  },
  {
    slNo: 2,
    name: 'Bus No. 2',
    stop: 'Thrissur',
  },
  {
    slNo: 3,
    name: 'Bus No. 3',
    stop: 'Kochi',
  },
  {
    slNo: 4,
    name: 'Bus No. 4',
    stop: 'Trivandrum',
  },
  {
    slNo: 5,
    name: 'Bus No. 5',
    stop: 'Kozhikkode',
  },
  {
    slNo: 1,
    name: 'Bus No. 1',
    stop: 'Palakkad',
  },
  {
    slNo: 2,
    name: 'Bus No. 2',
    stop: 'Thrissur',
  },
  {
    slNo: 3,
    name: 'Bus No. 3',
    stop: 'Kochi',
  },
  {
    slNo: 4,
    name: 'Bus No. 4',
    stop: 'Trivandrum',
  },
  {
    slNo: 5,
    name: 'Bus No. 5',
    stop: 'Kozhikkode',
  },
];
function BusStopMap() {
  const [drawerOpen, setDrawerOpen] = React.useState<boolean>(false);
  const [showFilter, setShowFilter] = useState(false);

  const handleOpen = () => {
    setDrawerOpen(true);
  };

  const toggleDrawerClose = useCallback(() => setDrawerOpen(false), []);

  const getRowKey = useCallback((row: any) => row.examId, []);
  const busStopMapColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'slNo',
        dataKey: 'slNo',
        headerLabel: 'Sl.No',
        sortable: true,
      },
      {
        name: 'name',
        dataKey: 'name',
        headerLabel: 'Bus Name',
      },
      {
        name: 'stop',
        dataKey: '',
        headerLabel: 'Bus Stop',
      },
      {
        name: 'actions',
        headerLabel: 'Actions',
        renderCell: () => {
          return (
            <Stack direction="row" gap={1}>
              <IconButton size="small" onClick={handleOpen} sx={{ padding: 0.5 }}>
                <ModeEditIcon />
              </IconButton>
              <IconButton size="small" color="error" onClick={handleOpen} sx={{ padding: 0.5 }}>
                <DeleteIcon />
              </IconButton>
            </Stack>
          );
        },
      },
    ],
    []
  );

  return (
    <Page title="List">
      <BusStopMapRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17}>
              Bus-Stop Mapped List
            </Typography>
            <Box pb={1} sx={{ flexShrink: 0 }}>
              <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                <SearchIcon />
              </IconButton>
              <Button onClick={handleOpen} sx={{ borderRadius: '20px', mr: 2 }} variant="outlined" size="small">
                Manage Stops
              </Button>
              <Button onClick={handleOpen} sx={{ borderRadius: '20px' }} variant="outlined" size="small">
                <MdAdd size="20px" /> Map New
              </Button>
            </Box>
          </Stack>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={3} alignItems="end">
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 250 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Bus Name
                      </Typography>
                      <TextField placeholder="Enter Name" />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 250 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Bus Stop
                      </Typography>
                      <Autocomplete
                        options={[]}
                        renderInput={(params) => <TextField {...params} placeholder="Select Stop" />}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item lg="auto" xs={12} sm={6}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Paper className="card-table-container" sx={{ marginTop: '12px' }}>
              <DataTable columns={busStopMapColumns} data={dummyData} getRowKey={getRowKey} fetchStatus="success" />
            </Paper>
          </div>
        </Card>
      </BusStopMapRoot>

      <TemporaryDrawer
        onClose={toggleDrawerClose}
        Title="Add New Bus Details"
        state={drawerOpen}
        DrawerContent={
          <Box mt={5}>
            <Stack sx={{ height: 'calc(100vh - 150px)' }}>
              <Typography mt={2} variant="h6" fontSize={14}>
                Bus Name/No.
              </Typography>
              <TextField placeholder="Enter Name/No." />
              <Typography mt={2} variant="h6" fontSize={14}>
                Description
              </Typography>
              <TextField
                multiline
                fullWidth
                minRows={3}
                InputProps={{ inputProps: { style: { resize: 'both' } } }}
                placeholder="Enter description..."
              />
              <Typography mt={2} variant="h6" fontSize={14}>
                Status
              </Typography>
              <Autocomplete
                fullWidth
                options={STATUS_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select status" />}
              />
            </Stack>
            <Box sx={{ justifyContent: 'center', pt: 3, position: 'relative' }}>
              <Stack spacing={2} direction="row">
                <Button onClick={toggleDrawerClose} fullWidth variant="contained" color="secondary">
                  Cancel
                </Button>
                <Button fullWidth variant="contained" color="primary">
                  Save
                </Button>
              </Stack>
            </Box>
          </Box>
        }
      />
    </Page>
  );
}

export default BusStopMap;
