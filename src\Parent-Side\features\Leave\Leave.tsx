/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  useTheme,
  IconButton,
  Collapse,
  Tooltip,
  FormControl,
  Snackbar,
  Chip,
} from '@mui/material';
import styled from 'styled-components';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import SearchIcon from '@mui/icons-material/Search';
import Popup from '@/components/shared/Popup/Popup';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import Physics from '@/Parent-Side/assets/liveclass/Icons/Physics.svg';
import { MdAdd, MdContentCopy } from 'react-icons/md';
import { CloseIcon } from '@/theme/overrides/CustomIcons';
import { Student, StudentInfoData } from '@/config/StudentDetails';
import useSettings from '@/hooks/useSettings';
import AutorenewOutlinedIcon from '@mui/icons-material/AutorenewOutlined';
import { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import DateSelect from '@/components/shared/Selections/DateSelect';
import MenuEditDelete from '@/components/shared/Selections/MenuEditDelete';
import CreateScheduleList from '@/features/LiveClass/ScheduleList/CreateScheduleList';
import InsertDriveFileIcon from '@mui/icons-material/InsertDriveFile';
import View from '../StudyMaterials/View';
import DatePickers from '@/components/shared/Selections/DatePicker';

export const data = [
  {
    createdBy: 'Passdaily',
    class: 'VII-B',
    meetingTitle: 'Daily Class',
    meetingID: 'IDGHDGB',
    academicYear: '2023-2024',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
  {
    createdBy: 'Passdaily',
    class: 'VI-C',
    academicYear: '2023-2024',
    meetingID: 'IDGHDGB',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
  {
    createdBy: 'Passdaily',
    class: 'XII-B',
    meetingTitle: 'Peter',
    academicYear: '2023-2024',
    meetingID: 'IDGHDGB',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
  {
    createdBy: 'Passdaily',
    class: 'V-A',
    meetingTitle: 'Daily Class Mic',
    academicYear: '2023-2024',
    meetingID: 'IDGHDGB',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
  {
    createdBy: 'Passdaily',
    class: 'II-B',
    meetingTitle: 'john',
    academicYear: '2023-2024',
    meetingID: 'IDGHDGB',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
  {
    createdBy: 'Passdaily',
    class: 'XII-C',
    meetingTitle: 'Micheal',
    academicYear: '2023-2024',
    meetingID: 'IDGHDGB',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
  {
    createdBy: 'Passdaily',
    class: 'I-B',
    meetingTitle: 'jack',
    academicYear: '2023-2024',
    meetingID: 'IDGHDGB',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
  {
    createdBy: 'Passdaily',
    class: 'VII-A',
    meetingTitle: 'Daily Class',
    academicYear: '2023-2024',
    meetingID: 'IDGHDGB',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
  {
    createdBy: 'Passdaily',
    class: 'VI-C',
    meetingTitle: 'Daily Class',
    academicYear: '2023-2024',
    meetingID: 'IDGHDGB',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
  {
    createdBy: 'Passdaily',
    class: 'X-B',
    meetingTitle: 'Daily Class',
    academicYear: '2023-2024',
    meetingID: 'IDGHDGB',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
];

const LeaveRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  .student_card {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  }

  .Card {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    height: calc(100vh - 110px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 40px);
      flex-grow: 1;

      .card-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: auto;
        ::-webkit-scrollbar {
          width: 0px;
        }

        .MuiTableContainer-root {
          max-height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

function Leave() {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const [showFilter, setShowFilter] = useState(false);
  const [Delete, setDelete] = useState(false);
  const [createPopup, setCreatePopup] = useState(false);

  const [students, setStudents] = useState(StudentInfoData);
  const [open, setOpen] = useState(false);
  const [openNew, setOpenNew] = useState(false);
  const [showNewMultiple, setShowNewMultiple] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [updatedData, setData] = useState(data);

  const handleStatusChange = (index) => {
    const copyData = [...updatedData];
    copyData[index].status = copyData[index].status === 'Published' ? 'Unpublished' : 'Published';
    setData(copyData);
  };

  const handleClickDelete = () => setDelete(true);
  const handleClickCloseDelete = () => setDelete(false);
  const handlePageChange = useCallback((event: unknown, newPage: number) => {
    // loadClassList({ ...currentClassListRequest, pageNumber: newPage + 1 });
  }, []);

  const handleChangeRowsPerPage = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const newPageSize = +event.target.value;
    // loadClassList({ ...currentClassListRequest, pageNumber: 1, pageSize: newPageSize });
  }, []);

  const pageProps = useMemo(
    () => ({
      rowsPerPageOptions: [10, 20, 30],
      pageNumber: 1,
      pageSize: 10,
      totalRecords: 100,
      onPageChange: handlePageChange,
      onRowsPerPageChange: handleChangeRowsPerPage,
    }),
    [handleChangeRowsPerPage, handlePageChange]
  );

  const handleClose = () => {
    setOpen(false);
  };
  const handleOpenNew = () => {
    setOpenNew(true);
  };

  const handleCloseNew = () => {
    setOpenNew(false);
  };
  const handleToggleNewMultiple = () => {
    setShowNewMultiple((prevState) => !prevState);
  };

  const handleUpdate = (id: number, updatedData: Student) => {
    const updatedStudents = students.map((student) => (student.id === id ? { ...student, ...updatedData } : student));
    setStudents(updatedStudents);
    setOpen(false);
  };

  const getRowKey = useCallback((row: any) => row.examId, []);

  const [isSnackbarOpen, setIsSnackbarOpen] = useState(false);

  const handleCopyClick = (text: string) => {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        setIsSnackbarOpen(true);
      })
      .catch((err) => {
        console.error('Unable to copy link to clipboard', err);
      });
  };

  const handleCloseSnackbar = () => {
    setIsSnackbarOpen(false);
  };

  const LeaveColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'class',
        dataKey: 'class',
        headerLabel: 'Class',
      },
      {
        name: 'startTime',
        dataKey: 'startTime',
        headerLabel: 'Start Time',
      },
      {
        name: 'teacher',
        dataKey: 'teacher',
        headerLabel: 'Teacher',
      },
      {
        name: 'student',
        headerLabel: 'Student',
        renderCell: (row) => {
          return (
            <Stack direction="row" alignItems="center" maxWidth="90%">
              <Tooltip title={`${row.meetingLink}`} arrow>
                <Chip
                  size="small"
                  label="40"
                  variant="outlined"
                  sx={{
                    border: '0px',
                    mr: '1px',
                    mb: 2,
                    backgroundColor: isLight ? theme.palette.info.lighter : theme.palette.grey[700],
                    color: isLight ? theme.palette.info.dark : theme.palette.grey[100],
                    width: 'auto',
                  }}
                />
              </Tooltip>
            </Stack>
          );
        },
      },
    ],
    [theme, isLight]
  );

  //   const renderContent = () => {
  //     return students.map((student, rowIndex) => (
  //       <Card key={student.id}>
  //         {LeaveColumns.map((column) => (
  //           <Typography key={column.name}>{student[column.dataKey]}</Typography>
  //         ))}
  //       </Card>
  //     ));
  //   };

  // const content: ReactNode = data.map((item, rowIndex: number) => {
  //   let cellData: ReactNode = null;

  //   return (
  //     <Typography key={column.name} variant="subtitle1" mb={1} fontSize={13}>
  //       <b>{cellData}</b>
  //     </Typography>
  //   );
  // });

  // Content = content;

  return (
    <Page title="Schedule List">
      <LeaveRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 1, md: 2 } }}>
          <Stack direction="row" justifyContent="space-between" mb={1}>
            <Typography variant="h6" fontSize={20}>
              Leave Applications
            </Typography>
            <Button
              onClick={() => setCreatePopup(true)}
              sx={{ borderRadius: '20px' }}
              variant="contained"
              size="small"
              color="success"
            >
              <MdAdd size="20px" />
              Create
            </Button>
          </Stack>
          <Stack>
            <Divider />
          </Stack>
          <div className="card-main-body">
            <Box className="card-container" my={2}>
              <Grid container spacing={2}>
                {updatedData.map((student, rowIndex) => (
                  <Grid item xl={3} lg={6} md={12} sm={6} xs={12}>
                    <Card
                      className="student_card"
                      sx={{ p: 2, boxShadow: 0, border: `1px solid ${theme.palette.grey[300]}` }}
                    >
                      <Box display="flex" flexDirection="column">
                        <Box display="flex" alignItems="center" gap={2} mb={2} justifyContent="space-between">
                          <Chip size="small" color="success" label="Approved" />

                          <MenuEditDelete
                            Edit={() => {
                              return 0;
                            }}
                            Delete={handleClickDelete}
                          />
                        </Box>

                        <Box>
                          <Typography variant="subtitle2">Sick Leave</Typography>
                          <Typography variant="subtitle1" fontSize={12} color="grey">
                            Lorem ipsum dolor sit amet consectetur adipisicing elit. Porro commodi ea optio hic ipsam
                            expedita.
                          </Typography>
                          <Box display="flex" alignItems="center" justifyContent="right" my={2}>
                            <CalendarTodayIcon color="secondary" sx={{ fontSize: '10px', marginRight: '5px' }} />
                            <Typography
                              sx={{
                                fontFamily: 'Poppins medium',
                                fontWeight: '500',
                                fontSize: '10px',
                                color: 'gray',
                              }}
                            >
                              02 Feb 2024
                            </Typography>
                          </Box>
                        </Box>
                      </Box>

                      {/* <Button
                        onClick={() => setChangeView('details')}
                        fullWidth
                        variant="contained"
                        color="success"
                        size="small"
                      >
                        View Details
                      </Button> */}
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Box>
          </div>
        </Card>
      </LeaveRoot>
      <Popup
        size="xs"
        state={Delete}
        onClose={handleClickCloseDelete}
        popupContent={<DeleteMessage message="Are you sure want to delete?" />}
      />
      <Popup
        size="sm"
        title="Create Leave Request"
        state={createPopup}
        onClose={() => setCreatePopup(false)}
        popupContent={
          <Stack p={3} sx={{ width: '100%' }} direction="column" gap={1}>
            <Stack direction="row" gap={5}>
              <FormControl fullWidth>
                <Typography variant="subtitle1">From</Typography>
                <DatePickers name="messageDateFilter" />
              </FormControl>

              <FormControl fullWidth>
                <Typography variant="subtitle1">To</Typography>
                <DatePickers name="messageDateFilter" />
              </FormControl>
            </Stack>
            <FormControl fullWidth>
              <Typography variant="subtitle1">Title</Typography>
              <TextField fullWidth placeholder="Enter title" />
            </FormControl>
            <FormControl fullWidth>
              <Typography variant="subtitle1">Description</Typography>
              <TextField
                multiline
                fullWidth
                placeholder="Type here..."
                InputProps={{
                  inputProps: {
                    style: { resize: 'vertical', minHeight: '100px', maxHeight: '200px' },
                  },
                }}
                size="small"
              />
            </FormControl>
          </Stack>
        }
      />
    </Page>
  );
}

export default Leave;
