import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import Loadable from '@/components/shared/Loadable';

const FeeAlert = Loadable(lazy(() => import('@/pages/FeeAlert')));
const UpdateFees = Loadable(lazy(() => import('@/features/FeeAlert/UpdateFees')));
const FeeAlertList = Loadable(lazy(() => import('@/features/FeeAlert/PendingList')));
const PendingList = Loadable(lazy(() => import('@/features/FeeAlert/PendingList')));

export const feeAlertRoutes: RouteObject[] = [
  {
    path: '/fee-alert',
    element: <FeeAlert />,
    children: [
      {
        path: 'update-fees',
        element: <UpdateFees />,
      },
      {
        path: 'list',
        element: <FeeAlertList />,
      },
      {
        path: 'pending',
        element: <PendingList />,
      },
    ],
  },
];
