/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import {
  Autocomplete,
  Grid,
  Stack,
  TextField,
  Button,
  Typography,
  Box,
  FormControl,
  Select,
  MenuItem,
  RadioGroup,
  FormControlLabel,
  Radio,
} from '@mui/material';
import styled from 'styled-components';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import { CLASS_SELECT, STATUS_OPTIONS, SUBJECT_SELECT, YEAR_SELECT } from '@/config/Selection';
import SelectDateTimePicker from '@/components/shared/Selections/TimePicker';
import InputFileUpload from '@/components/shared/Selections/FilesUpload';
import DateSelect from '@/components/shared/Selections/DateSelect';

const MapAssignmentRoot = styled.div`
  width: 100%;
  padding: 0.5rem 1.5rem 0.5rem 1.5rem;
`;

function MapAssignment({ onClose }: any) {
  return (
    <MapAssignmentRoot>
      <form noValidate>
        <Grid container spacing={4}>
          {/* -------From-------- */}
          <Grid item lg={6} md={6} sm={6} xs={12}>
            <Stack spacing={1}>
              <Typography variant="h6" color="GrayText">
                From :
              </Typography>
              <FormControl fullWidth>
                <Typography variant="h6" fontSize={14}>
                  Academic Year
                </Typography>
                <Autocomplete
                  options={YEAR_SELECT}
                  renderInput={(params) => <TextField {...params} placeholder="Select Year" />}
                />
              </FormControl>

              <FormControl fullWidth>
                <Typography variant="h6" fontSize={14}>
                  Class
                </Typography>
                <Autocomplete
                  options={CLASS_SELECT}
                  renderInput={(params) => <TextField {...params} placeholder="Select class" />}
                />
              </FormControl>

              <FormControl fullWidth>
                <Typography variant="h6" fontSize={14}>
                  Subject
                </Typography>
                <Autocomplete
                  options={SUBJECT_SELECT}
                  renderInput={(params) => <TextField {...params} placeholder="Select class" />}
                />
              </FormControl>

              <FormControl fullWidth>
                <Typography variant="h6" fontSize={14}>
                  Assignment H/W
                </Typography>
                <Autocomplete
                  options={SUBJECT_SELECT}
                  renderInput={(params) => <TextField {...params} placeholder="Select class" />}
                />
              </FormControl>
            </Stack>
          </Grid>
          {/* -------To-------- */}
          <Grid item lg={6} md={6} sm={6} xs={12}>
            <Stack spacing={1}>
              <Typography variant="h6" color="GrayText">
                To :
              </Typography>
              <FormControl fullWidth>
                <Typography variant="h6" fontSize={14}>
                  Academic Year To
                </Typography>
                <Autocomplete
                  options={YEAR_SELECT}
                  renderInput={(params) => <TextField {...params} placeholder="Select Year" />}
                />
              </FormControl>

              <FormControl fullWidth>
                <Typography variant="h6" fontSize={14}>
                  Class To
                </Typography>
                <Autocomplete
                  options={CLASS_SELECT}
                  renderInput={(params) => <TextField {...params} placeholder="Select class" />}
                />
              </FormControl>

              <FormControl fullWidth>
                <Typography variant="h6" fontSize={14}>
                  Subject To
                </Typography>
                <Autocomplete
                  options={SUBJECT_SELECT}
                  renderInput={(params) => <TextField {...params} placeholder="Select class" />}
                />
              </FormControl>
            </Stack>
          </Grid>
        </Grid>
        <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 3 }}>
          <Stack spacing={2} direction="row">
            <Button variant="contained" onClick={onClose} color="secondary">
              Cancel
            </Button>
            <Button variant="contained" color="primary">
              Save
            </Button>
          </Stack>
        </Box>
      </form>
    </MapAssignmentRoot>
  );
}

export default MapAssignment;
