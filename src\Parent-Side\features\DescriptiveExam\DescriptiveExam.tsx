/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  useTheme,
  IconButton,
  Collapse,
  Tooltip,
  FormControl,
  Chip,
  Snackbar,
  Alert,
  Tabs,
  Tab,
} from '@mui/material';
import styled from 'styled-components';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import SearchIcon from '@mui/icons-material/Search';
import Popup from '@/components/shared/Popup/Popup';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import Physics from '@/Parent-Side/assets/liveclass/Icons/Physics.svg';
import { MdAdd, MdContentCopy } from 'react-icons/md';
import { CloseIcon } from '@/theme/overrides/CustomIcons';
import { Student, StudentInfoData } from '@/config/StudentDetails';
import useSettings from '@/hooks/useSettings';
import AutorenewOutlinedIcon from '@mui/icons-material/AutorenewOutlined';
import { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import DateSelect from '@/components/shared/Selections/DateSelect';
import MenuEditDelete from '@/components/shared/Selections/MenuEditDelete';
import CreateScheduleList from '@/features/LiveClass/ScheduleList/CreateScheduleList';
import InsertDriveFileIcon from '@mui/icons-material/InsertDriveFile';
import View from '../StudyMaterials/View';
import DetailsExam from './Details';

export const data = [
  {
    createdBy: 'Passdaily',
    label: 'Physics',
    class: 'VII-B',
    meetingTitle: 'Daily Class',
    meetingID: 'IDGHDGB',
    academicYear: '2023-2024',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
  {
    createdBy: 'Passdaily',
    label: 'Chemistry',
    class: 'VI-C',
    academicYear: '2023-2024',
    meetingID: 'IDGHDGB',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
  {
    createdBy: 'Passdaily',
    class: 'XII-B',
    meetingTitle: 'Peter',
    academicYear: '2023-2024',
    meetingID: 'IDGHDGB',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
  {
    createdBy: 'Passdaily',
    class: 'V-A',
    meetingTitle: 'Daily Class Mic',
    academicYear: '2023-2024',
    meetingID: 'IDGHDGB',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
  {
    createdBy: 'Passdaily',
    class: 'II-B',
    meetingTitle: 'john',
    academicYear: '2023-2024',
    meetingID: 'IDGHDGB',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
  {
    createdBy: 'Passdaily',
    class: 'XII-C',
    meetingTitle: 'Micheal',
    academicYear: '2023-2024',
    meetingID: 'IDGHDGB',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
  {
    createdBy: 'Passdaily',
    class: 'I-B',
    meetingTitle: 'jack',
    academicYear: '2023-2024',
    meetingID: 'IDGHDGB',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
  {
    createdBy: 'Passdaily',
    class: 'VII-A',
    meetingTitle: 'Daily Class',
    academicYear: '2023-2024',
    meetingID: 'IDGHDGB',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
  {
    createdBy: 'Passdaily',
    class: 'VI-C',
    meetingTitle: 'Daily Class',
    academicYear: '2023-2024',
    meetingID: 'IDGHDGB',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
  {
    createdBy: 'Passdaily',
    class: 'X-B',
    meetingTitle: 'Daily Class',
    academicYear: '2023-2024',
    meetingID: 'IDGHDGB',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Published',
  },
];

const DescriptiveExamRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  .student_card {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  }

  .Card {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    height: calc(100vh - 110px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 40px);
      flex-grow: 1;

      .card-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: auto;
        ::-webkit-scrollbar {
          width: 0px;
        }
      }
    }
    .Mui-selected {
      color: ${(props) => props.theme.palette.common.white};
      background-color: ${(props) => props.theme.palette.success.main};
      padding: 10px;
      border-radius: 50px;
    }
    .Mui-Tab {
      color: ${(props) => props.theme.palette.common.white};
      background-color: ${(props) => props.theme.palette.success.main};
      padding: 0px;
      border-radius: 50px;
    }
    .MuiTabs-indicator {
      height: 0px;
    }
  }
`;
const subjects = ['Physics', 'English', 'Chemistry', 'Mathematics', 'Biology']; // Example subjects array
function DescriptiveExam() {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const [changeView, setChangeView] = useState<'descriptive' | 'details'>('descriptive');

  const [updatedData, setData] = useState(data);

  const [selectedSubject, setSelectedSubject] = useState(subjects[0]);

  const filteredData = data.filter((item) => subjects.includes(item.label));

  const handleTabChange = (event, newValue) => {
    setSelectedSubject(newValue);
  };

  return changeView !== 'details' ? (
    <Page title="Schedule List">
      <DescriptiveExamRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 1, md: 2 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={20}>
              Descriptive Exam
            </Typography>
          </Stack>
            <Divider />
          {/* <Tabs value={selectedSubject} onChange={handleTabChange}>
            {subjects.map((subject) => (
              <Tab label={subject} value={subject} key={subject} />
            ))}
          </Tabs> */}
          <Tabs
            variant="scrollable"
            allowScrollButtonsMobile  
            sx={{ mt: 2, width: 500, overflowX: 'scroll' }}
            value={selectedSubject}
            onChange={handleTabChange}
          >
            {subjects.map((subject) => (
              <Button
                key={subject}
                variant={selectedSubject === subject ? 'contained' : 'outlined'}
                color={selectedSubject === subject ? 'success' : 'secondary'}
                sx={{
                  mr: 2,
                  borderRadius: 10,
                  backgroundColor: selectedSubject === subject ? theme.palette.success.main : undefined,
                  color: selectedSubject === subject ? theme.palette.common.white : undefined,
                  '&:hover': {
                    backgroundColor: selectedSubject === subject ? theme.palette.success.main : undefined,
                    boxShadow: 'none',
                  },
                }}
                onClick={() => setSelectedSubject(subject)}
              >
                {subject}
              </Button>
            ))}
          </Tabs>
          <div className="card-main-body">
            <Box className="card-container" my={2}>
              <Grid container spacing={2}>
                {updatedData.map((student, rowIndex) => (
                  <Grid item xl={3} lg={6} md={12} sm={6} xs={12}>
                    <Card
                      className="student_card"
                      sx={{ p: 2, boxShadow: 0, border: `1px solid ${theme.palette.grey[300]}` }}
                    >
                      <Box display="flex" flexDirection="column">
                        <Box display="flex" alignItems="center" gap={2} mb={2} justifyContent="space-between">
                          <Stack direction="row" alignItems="center" gap={2}>
                            <div>
                              <img width={40} src={Physics} alt="" />
                            </div>
                            <Typography variant="subtitle2">Physics</Typography>
                          </Stack>
                          <Chip variant="outlined" size="small" label="Class : VII-A" />
                        </Box>

                        <Box>
                          <Typography variant="subtitle2">Half Year Examination</Typography>
                          <Typography variant="subtitle1" fontSize={12} color="grey">
                            Lorem ipsum dolor sit amet consectetur adipisicing elit. Porro commodi ea optio hic ipsam
                            expedita.
                          </Typography>
                          <Stack direction="row" alignItems="center" justifyContent="space-between" gap={3} my={2}>
                            <Box display="flex" alignItems="center">
                              <CalendarTodayIcon color="secondary" sx={{ fontSize: '10px', marginRight: '5px' }} />
                              <Typography
                                sx={{
                                  fontFamily: 'Poppins medium',
                                  fontWeight: '500',
                                  fontSize: '10px',
                                  color: 'gray',
                                }}
                              >
                                <span>Start:</span> 02 Feb 24
                              </Typography>
                            </Box>
                            <Box display="flex" alignItems="center">
                              <CalendarTodayIcon color="secondary" sx={{ fontSize: '10px', marginRight: '5px' }} />
                              <Typography
                                sx={{
                                  fontFamily: 'Poppins medium',
                                  fontWeight: '500',
                                  fontSize: '10px',
                                  color: 'gray',
                                }}
                              >
                                <span>End:</span> 28 Feb 24
                              </Typography>
                            </Box>
                          </Stack>
                        </Box>
                      </Box>

                      <Button
                        onClick={() => setChangeView('details')}
                        fullWidth
                        variant="contained"
                        color="success"
                        size="small"
                      >
                        View Details
                      </Button>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Box>
          </div>
        </Card>
      </DescriptiveExamRoot>
      {/* <Popup
        size="xs"
        state={Delete}
        onClose={handleClickCloseDelete}
        popupContent={<DeleteMessage message="Are you sure want to delete?" />}
      /> */}
      {/* <Popup
        size="sm"
        title="Assignment Details"
        state={createPopup}
        onClose={() => setCreatePopup(false)}
        popupContent={<View />}
      /> */}
    </Page>
  ) : (
    <DetailsExam onBackClick={() => setChangeView('descriptive')} />
  );
}

export default DescriptiveExam;
