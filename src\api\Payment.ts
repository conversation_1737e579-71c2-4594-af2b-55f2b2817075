import {
  GetcheckLoginType,
  GetParentPayFeeDataType,
  PaymentOrderAPIResponse,
  PaymentOrderRequest,
  TransactionAPIResponse,
  TransactionResponseRequest,
} from '@/types/Payment';
import ApiUrls from '@/config/ApiUrls';
import { privateApi } from './base/api';
import { APIResponse } from './base/types';
import { GetReceiptForPrintType } from '@/types/ManageFee';

async function createOrder(orderRequest: PaymentOrderRequest): Promise<PaymentOrderAPIResponse> {
  const response = await privateApi.post<PaymentOrderAPIResponse>(ApiUrls.PaymentCreateOrder, orderRequest);
  return response.data;
}

async function transactionResponse(transactionRequest: TransactionResponseRequest): Promise<TransactionAPIResponse> {
  const response = await privateApi.post<TransactionAPIResponse>(ApiUrls.TransactionResponse, transactionRequest);
  console.log(response);
  return response.data;
}

// Get Parent Pay Fee
async function getParentPayFee(
  studentId: number | undefined,
  feeTypeId: number | undefined
): Promise<APIResponse<GetParentPayFeeDataType[]>> {
  if (studentId === undefined || feeTypeId === undefined) {
    throw new Error('studentId and feeTypeId are required');
  }

  const url = ApiUrls.GetParentPayFee.replace('{studentId}', studentId.toString()).replace(
    '{feeTypeId}',
    feeTypeId.toString()
  );

  const response = await privateApi.get<any>(url);
  return response;
}

async function GetReceiptOnline(receiptId: number | undefined): Promise<APIResponse<GetReceiptForPrintType[]>> {
  if (receiptId === undefined) {
    throw new Error('receiptId required');
  }

  const url = ApiUrls.GetReceiptOnline.replace('{receiptId}', receiptId.toString());

  const response = await privateApi.get<any>(url);
  return response;
}

async function checkLogin(adminId: string | number | null): Promise<APIResponse<GetcheckLoginType>> {
  if (adminId === undefined) {
    throw new Error('adminId required');
  }

  const url = ApiUrls.checkLogin.replace('{adminId}', adminId.toString());

  const response = await privateApi.get<any>(url);
  return response;
}
export default {
  createOrder,
  transactionResponse,
  getParentPayFee,
  GetReceiptOnline,
  checkLogin,
};
