/* eslint-disable jsx-a11y/alt-text */
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Divider,
  Grid,
  Stack,
  TextField,
  Button,
  Table,
  TableContainer,
  TableCell,
  TableHead,
  TableRow,
  TableBody,
  Typography,
  Card,
  IconButton,
  Paper,
  MenuItem,
  Select,
  FormControl,
  Box,
  Collapse,
  Tooltip,
} from '@mui/material';
import styled from 'styled-components';
import DeleteIcon from '@mui/icons-material/Delete';
import { smsrows } from '@/config/smsData';
import React, { useState } from 'react';
import DatePickers from '@/components/shared/Selections/DatePicker';
import Popup from '@/components/shared/Popup/Popup';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import SearchIcon from '@mui/icons-material/Search';
import { IoIosArrowUp } from 'react-icons/io';
import Report from './Report';

const NotificationReportRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  padding: 1rem;
  .Card {
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }
  }
`;

function NotificationReport() {
  const [Delete, setDelete] = React.useState(false);
  const [report, setReport] = React.useState(false);
  const [showFilter, setShowFilter] = useState(false);

  const handleClickDelete = () => setDelete(true);
  const handleClickCloseDelete = () => setDelete(false);

  // const handleClickOpenReport = () => setDelete(true);
  const handleClickCloseReport = () => setReport(false);

  return (
    <Page title="Notification Report">
      <NotificationReportRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
            flexWrap="wrap"
            className="top-head"
          >
            <Typography variant="h6" fontSize={17}>
              SMS Template List
            </Typography>

            {showFilter === false ? (
              <Tooltip title="Search">
                <IconButton
                  aria-label="delete"
                  color="primary"
                  sx={{ mr: { xs: 0, sm: 1 } }}
                  onClick={() => setShowFilter((x) => !x)}
                >
                  <SearchIcon />
                </IconButton>
              </Tooltip>
            ) : (
              <IconButton
                aria-label="delete"
                color="primary"
                sx={{ mr: { xs: 0, sm: 1 } }}
                onClick={() => setShowFilter((x) => !x)}
              >
                <IoIosArrowUp />
              </IconButton>
            )}
          </Stack>
          <Divider sx={{ marginBottom: 1 }} />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid container spacing={3}>
                  <Grid item xs={12} sm={2}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12}>
                        Subject
                      </Typography>
                      <TextField
                        placeholder="Enter subject"
                        name="messageTitle"
                        // value={messageTitle}
                        // onBlur={handleBlur}
                        // onChange={(e) => {
                        //   handleChange(e);
                        // }}
                        // error={touched.messageTitle && !!errors.messageTitle}
                        // helperText={touched.messageTitle && errors.messageTitle}
                        // // disabled={isSubmitting}
                        // InputProps={{
                        //   endAdornment: (
                        //     <>
                        //       {msgId > 0 && <SuccessIcon color="success" />}
                        //       {touched.messageTitle && !!errors.messageTitle && <ErrorIcon color="error" />}
                        //     </>
                        //   ),
                        // }}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={2}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Mail Type
                      </Typography>
                      <Select
                        labelId="classStatusFilter"
                        id="classStatusFilterSelect"
                        // value={classStatusFilter?.toString() || '-1'}
                        // onChange={handleStatusChange}
                      >
                        <MenuItem value={-1}>All</MenuItem>
                        {/* {STATUS_OPTIONS.map((opt) => (
                        <MenuItem key={opt.id} value={opt.id}>
                          {opt.name}
                        </MenuItem>
                      ))} */}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item sm={2} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        From
                      </Typography>
                      <DatePickers
                      // value={messageDateFilter}
                      // onChange={(e) => {
                      //   const formattedDate = e ? e.format('YYYY-MM-DD') : '';
                      //   setMessageDateFilter(formattedDate);
                      //   loadMessageTempList({ ...currentMessageTempRequest, messageDate: formattedDate });
                      // }}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item sm={2} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        To
                      </Typography>
                      <DatePickers
                      // value={messageDateFilter}
                      // onChange={(e) => {
                      //   const formattedDate = e ? e.format('YYYY-MM-DD') : '';
                      //   setMessageDateFilter(formattedDate);
                      //   loadMessageTempList({ ...currentMessageTempRequest, messageDate: formattedDate });
                      // }}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto">
                    <Stack direction="row" sx={{ marginBottom: '2px' }}>
                      <Button type="reset" variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Paper sx={{ border: '1px solid #e8e8e9', overflow: 'auto' }}>
              <TableContainer sx={{ width: { xs: '850px', md: '100%' } }}>
                <Table stickyHeader>
                  <TableHead>
                    <TableRow>
                      <TableCell>Sl.No</TableCell>
                      <TableCell>Student</TableCell>
                      <TableCell>Title</TableCell>
                      <TableCell>Class</TableCell>
                      <TableCell>Date</TableCell>
                      <TableCell>Mail Type</TableCell>
                      <TableCell>Class Section</TableCell>
                      <TableCell>Sent By</TableCell>
                      <TableCell>Options</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {smsrows.map((smsrow) => (
                      <TableRow key={smsrow.messageId} sx={{ borderBottom: '1px solid  rgb(200, 200, 200)' }}>
                        <TableCell>{smsrow.messageId}</TableCell>
                        <TableCell>Fionna Grand</TableCell>
                        <TableCell>Attendance Inbox</TableCell>
                        <TableCell>VII-A</TableCell>
                        <TableCell>01/02/2023</TableCell>
                        <TableCell>Parent</TableCell>
                        <TableCell>Section</TableCell>
                        <TableCell>Passdaily</TableCell>
                        <TableCell>
                          <Stack direction="row" spacing={2}>
                            <Button size="small" variant="outlined" color="secondary" onClick={() => setReport(true)}>
                              Report
                            </Button>
                            <IconButton onClick={handleClickDelete} size="small">
                              <DeleteIcon />
                            </IconButton>
                          </Stack>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Paper>
          </div>
        </Card>
      </NotificationReportRoot>
      <Popup
        size="xs"
        state={Delete}
        onClose={handleClickCloseDelete}
        popupContent={<DeleteMessage message="Are you sure want to delete?" />}
      />
      <Popup size="lg" title="Report" state={report} onClose={handleClickCloseReport} popupContent={<Report />} />
    </Page>
  );
}

export default NotificationReport;
