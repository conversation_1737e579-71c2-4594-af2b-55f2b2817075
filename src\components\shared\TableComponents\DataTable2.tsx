/* eslint-disable no-else-return */
/* eslint-disable react/no-unstable-nested-components */
// /* eslint-disable react/no-unstable-nested-components */
// /* eslint-disable no-nested-ternary */
// import { ReactNode, useCallback, useEffect, useRef } from 'react';
// import {
//   useTheme,
//   Paper,
//   TableContainer,
//   Table,
//   TableHead,
//   TableRow,
//   TableCell,
//   TablePagination,
//   TableBody,
//   Skeleton,
//   Checkbox,
//   TextField,
//   Typography,
//   Box,
//   Stack,
//   TableFooter,
// } from '@mui/material';
// import { FetchStatus } from '@/types/Common';
// import NoData from '@/assets/no-datas.png';
// import { Virtuoso } from 'react-virtuoso';
// import SortableTableCell from './SortableTableCell';

// import { useMemo } from 'react';
// import { TableVirtuoso } from 'react-virtuoso';

// export type DataTableColumn<D> = {
//   name: string;
//   headerLabel?: string;
//   renderHeader?: () => ReactNode;
//   sortable?: boolean;
//   dataKey?: string;
//   renderCell?: (row: D, rowIndex?: number) => ReactNode;
//   width?: string;
//   align?: string;
//   renderFooter?: () => ReactNode;
//   footerLabel?: string;
// };

// export type DataTablePaginationProps = {
//   rowsPerPageOptions?: number[];
//   totalRecords: number;
//   pageNumber: number;
//   pageSize: number;
//   onPageChange: (event: React.MouseEvent<HTMLButtonElement> | null, page: number) => void;
//   onRowsPerPageChange?: React.ChangeEventHandler<HTMLTextAreaElement | HTMLInputElement>;
//   showPagination?: boolean;
//   showFirstButton?: boolean;
//   showLastButton?: boolean;
// };

// type ArrayStateProps = {
//   rowIndex: number;
//   columnIndex: number;
//   value: string;
// };

// export type DataTableProps<D> = {
//   columns: DataTableColumn<D>[];
//   data: D[];
//   allowPagination?: boolean;
//   allowSorting?: boolean;
//   sortColumn?: string;
//   sortDirection?: 'asc' | 'desc';
//   onSort?: (columnName: string) => void;
//   headerClassName?: string;
//   PaginationProps?: DataTablePaginationProps;
//   fetchStatus?: FetchStatus;
//   getRowKey: (item: D, index?: number) => React.Key | null | undefined;
//   deletingRecords?: (string | number)[];
//   onCheckboxClick?: any;
//   ShowCheckBox?: boolean | undefined;
//   setSelectedRows?: any;
//   selectedRows?: any;
//   RowSelected?: boolean;
//   isCancell?: any;
//   textAlign?: string;
//   tableWidth?: number;
//   tableStyles?: {} | undefined;
//   tableCellStyles?: {} | undefined;
//   respnseResults?: {} | undefined;
//   tableRowSelect?: boolean;
//   disabledCheckBox?: any[];
//   isSubmitting?: boolean;
//   footerColumn?: any;
//   showHorizontalScroll?: boolean;
//   tableCellSkeltonStyle?: {} | undefined;
//   arrayState?: any;
// };

// function DataTable2<D>({
//   columns,
//   data,
//   getRowKey,
//   allowPagination = false,
//   allowSorting = false,
//   ShowCheckBox = false,
//   sortColumn,
//   sortDirection = 'asc',
//   onSort,
//   headerClassName,
//   fetchStatus,
//   PaginationProps,
//   deletingRecords,
//   setSelectedRows,
//   selectedRows,
//   RowSelected,
//   textAlign,
//   tableWidth,
//   tableStyles,
//   tableCellStyles,
//   tableRowSelect,
//   disabledCheckBox,
//   isSubmitting,
//   footerColumn,
//   showHorizontalScroll,
//   onCheckboxClick,
//   tableCellSkeltonStyle,
//   arrayState,
// }: DataTableProps<D>) {
//   const tableRef = useRef<any>(null);
//   const theme = useTheme();
//   // const [selected, setSelected] = useState<D[]>(selectedRows);
//   const isSelected = (row: D) => selectedRows?.indexOf(row) !== -1;

//   const handleRowClick = (row: D) => {
//     // Check if the clicked row is disabled
//     const rowIndex = data.findIndex((item) => item === row);
//     const isRowDisabled = disabledCheckBox && disabledCheckBox[rowIndex];

//     if (!isRowDisabled) {
//       const selectedIndex = selectedRows?.indexOf(row);
//       let newSelected: D[] = [];

//       if (selectedIndex === -1) {
//         newSelected = newSelected.concat(selectedRows, row);
//         if (typeof arrayState === 'function') {
//           arrayState((prev: ArrayStateProps[]) => prev.filter((cell: ArrayStateProps) => cell.rowIndex !== rowIndex));
//         }
//       } else if (selectedIndex === 0) {
//         newSelected = newSelected.concat(selectedRows.slice(1));
//       } else if (selectedIndex === selectedRows.length - 1) {
//         newSelected = newSelected.concat(selectedRows.slice(0, -1));
//       } else if (selectedIndex > 0) {
//         newSelected = newSelected.concat(selectedRows.slice(0, selectedIndex), selectedRows.slice(selectedIndex + 1));
//       }

//       setSelectedRows(newSelected);
//       if (onCheckboxClick) {
//         onCheckboxClick(newSelected);
//       }
//     }
//   };
//   const handleRowAllClick = (event: React.ChangeEvent<HTMLInputElement>) => {
//     if (event.target.checked) {
//       if (typeof arrayState === 'function') {
//         arrayState([]);
//       }
//       // Filter out disabled rows before setting selected rows
//       const enabledRows = data.filter((row, index) => !disabledCheckBox || !disabledCheckBox[index]);
//       setSelectedRows(enabledRows);
//       if (onCheckboxClick) {
//         onCheckboxClick(enabledRows);
//       }
//     } else {
//       setSelectedRows([]);
//       if (onCheckboxClick) {
//         onCheckboxClick([]);
//       }
//     }
//   };
//   localStorage.setItem('absenteesData', JSON.stringify(selectedRows));

//   useEffect(() => {
//     if (tableRef.current) {
//       tableRef.current.scrollTo({
//         top: 0,
//         behavior: 'smooth',
//       });
//     }
//   }, [PaginationProps?.pageNumber, PaginationProps?.pageSize]);

//   const handleSort = useCallback(
//     (col: string) => {
//       onSort?.(col);
//     },
//     [onSort]
//   );

//   if (allowPagination && !PaginationProps) {
//     throw new Error("PaginationProps must be passed when 'allowPagination' is set to true");
//   }

//   let tableBodyContent: ReactNode = null;

//   if (fetchStatus === 'loading') {
//     const rowCount = allowPagination ? PaginationProps?.pageSize : data.length;

//     tableBodyContent = (
//       <Virtuoso
//         style={{ height: '400px' }}
//         totalCount={rowCount}
//         itemContent={(rowIndex) => (
//           <TableRow key={`Row_${rowIndex}`}>
//             {ShowCheckBox && (
//               <TableCell padding="checkbox">
//                 <Skeleton variant="text" />
//               </TableCell>
//             )}
//             {columns.map((column, colIndex) => (
//               <TableCell key={`Row_${rowIndex}_Col_${colIndex}`}>
//                 <Skeleton variant="text" />
//               </TableCell>
//             ))}
//           </TableRow>
//         )}
//       />
//     );
//   }

//   if (fetchStatus === 'success') {
//     const rowCount = allowPagination ? PaginationProps?.pageSize : data.length;

//     tableBodyContent = (
//       <Virtuoso
//         style={{ height: '400px', width: '1000px' }}
//         totalCount={rowCount}
//         itemContent={(rowIndex) => {
//           const item = data[rowIndex];
//           const rowKey = getRowKey(item, rowIndex);
//           const isDeleting = rowKey && deletingRecords?.includes(rowKey);
//           const isRowSelected = isSelected(item);

//           return (
//             <TableRow
//               hover
//               role="checkbox"
//               aria-checked={RowSelected && isRowSelected}
//               tabIndex={-1}
//               selected={RowSelected && isRowSelected}
//               key={rowKey}
//               sx={{
//                 opacity: isDeleting ? 0.5 : 1,
//                 cursor: 'pointer',
//                 '&:last-child td': {
//                   borderBottom: '0px',
//                 },
//               }}
//             >
//               {ShowCheckBox && (
//                 <TableCell>
//                   <Checkbox
//                     disabled={isSubmitting || (disabledCheckBox?.[rowIndex] ?? false)}
//                     color="primary"
//                     checked={isRowSelected}
//                     onClick={() => handleRowClick(item)}
//                     inputProps={{ 'aria-labelledby': `checkbox-${rowKey}` }}
//                   />
//                 </TableCell>
//               )}
//               {columns.map((column) => {
//                 let cellData = null;
//                 if (column.renderCell) {
//                   cellData = column.renderCell(item, rowIndex);
//                 } else if (column.dataKey === 'TextField') {
//                   cellData = <TextField disabled={!isRowSelected} placeholder={column.name} />;
//                 } else if (column.dataKey) {
//                   cellData = (item as Record<string, any>)[column.dataKey];
//                 }

//                 return (
//                   <TableCell
//                     key={`Row_${rowKey}_${column.name}`}
//                     className={textAlign}
//                     sx={{ textAlign: column.align || 'start' }}
//                     width={column.width || 'auto'}
//                     onClick={
//                       column.dataKey === 'TextField' || column.dataKey === 'Button'
//                         ? undefined
//                         : tableRowSelect
//                         ? () => handleRowClick(item)
//                         : undefined
//                     }
//                   >
//                     {cellData}
//                   </TableCell>
//                 );
//               })}
//             </TableRow>
//           );
//         }}
//       />
//     );
//   }

//   return (
//     <Paper className="card-table-container" sx={{ position: 'relative', display: 'flex', justifyContent: 'center' }}>
//       {/* <button onClick={handleResponseClick}>handleResponseClick</button> */}
//       {data.length !== 0 ? (
//         <TableContainer
//           sx={{
//             '&::-webkit-scrollbar': {
//               height: showHorizontalScroll ? '15px' : '',
//               // width: '15px',
//             },
//           }}
//         >
//           <Stack
//             ref={tableRef}
//             // sx={{
//             //   overflowY: 'auto',
//             //   maxHeight: '100%',
//             //   minWidth: tableWidth,
//             //   '&::-webkit-scrollbar': {
//             //     height: '15px',
//             //   },
//             // }}
//           >
//             <Table stickyHeader size="small" sx={tableStyles}>
//               <TableHead className={headerClassName}>
//                 <TableRow>
//                   {ShowCheckBox && (
//                     <TableCell>
//                       <Checkbox
//                         color="primary"
//                         onChange={handleRowAllClick}
//                         indeterminate={selectedRows?.length > 0 && selectedRows.length < data.length}
//                         checked={selectedRows?.length > 0 && selectedRows.length === data.length}
//                       />
//                     </TableCell>
//                   )}
//                   {columns.map((column) => {
//                     if (allowSorting && column.sortable) {
//                       return (
//                         <SortableTableCell
//                           columnName={column.name}
//                           active={sortColumn === column.name}
//                           direction={sortDirection}
//                           onClick={handleSort}
//                           key={column.name}
//                         >
//                           {column.renderHeader ? column.renderHeader() : column.headerLabel}
//                         </SortableTableCell>
//                       );
//                     }
//                     return (
//                       <TableCell
//                         className={textAlign}
//                         key={column.name}
//                         // sx={{ backgroundColor: tableBgColor, tableStyles }}
//                         sx={tableCellStyles}
//                       >
//                         {column.renderHeader ? column.renderHeader() : column.headerLabel}
//                       </TableCell>
//                     );
//                   })}
//                 </TableRow>
//               </TableHead>

//               <TableBody>{tableBodyContent}</TableBody>
//               <TableFooter sx={{ height: '100%' }}>
//                 {/* <TableRow style={{ position: 'sticky', bottom: 0, backgroundColor: theme.palette.grey[300] }}>
//                   <TableCell className={textAlign}> </TableCell>
//                   <TableCell
//                     // sx={{ backgroundColor: tableBgColor, tableStyles }}
//                     sx={{ color: theme.palette.common.black }}
//                   >
//                     Monthly Total
//                   </TableCell>
//                   {footerColumn?.map((column) => {
//                     return (
//                       <TableCell
//                         key={column.name}
//                         // sx={{ backgroundColor: tableBgColor, tableStyles }}
//                         sx={{ color: theme.palette.common.black }}

//                       >
//                         11
//                       </TableCell>
//                     );
//                   })}
//                   <TableCell
//                     className={textAlign}
//                     // sx={{ backgroundColor: tableBgColor, tableStyles }}
//                     sx={tableCellStyles}
//                   >
//                     {' '}
//                   </TableCell>
//                 </TableRow> */}
//                 {footerColumn}
//               </TableFooter>
//             </Table>
//           </Stack>
//         </TableContainer>
//       ) : (
//         <Box
//           display="flex"
//           alignItems="center"
//           justifyContent="center"
//           width="100%"
//           height={{ xs: 'calc(100vh - 400px)', sm: 'calc(100vh - 400px)', lg: 'calc(100vh - 100px)' }}
//         >
//           <Stack direction="column" alignItems="center">
//             <img src={NoData} width="150px" alt="" />
//             <Typography variant="subtitle2" mt={2} color="GrayText">
//               No data found !
//             </Typography>
//           </Stack>
//         </Box>
//       )}
//       {!!PaginationProps && allowPagination && (
//         <TablePagination
//           rowsPerPageOptions={PaginationProps.rowsPerPageOptions}
//           component="div"
//           count={PaginationProps.totalRecords}
//           rowsPerPage={PaginationProps.pageSize}
//           page={PaginationProps.pageNumber}
//           onPageChange={PaginationProps.onPageChange}
//           onRowsPerPageChange={PaginationProps.onRowsPerPageChange}
//           showFirstButton
//           showLastButton
//         />
//       )}
//     </Paper>
//   );
// }

// export default DataTable2;

/* eslint-disable no-nested-ternary */
import React, { useMemo, useState, useEffect, useCallback, ChangeEvent } from 'react';
import { TableVirtuoso } from 'react-virtuoso';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Checkbox,
  TextField,
  Button,
  Typography,
  IconButton,
  Stack,
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import { useConfirm } from '../Popup/Confirmation';
import DeleteIcon from '@mui/icons-material/Delete';
import CurrencyRupeeIcon from '@mui/icons-material/CurrencyRupee';
import { fetchFeeDateSettings } from '@/store/ManageFee/manageFee.thunks';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { getfeeDateSettingsData, getfeeDateSettingsStatus } from '@/config/storeSelectors';
import { useAppSelector } from '@/hooks/useAppSelector';
import useSettings from '@/hooks/useSettings';
import { useTheme } from 'styled-components';
import useAuth from '@/hooks/useAuth';
import Lottie from 'lottie-react';
import { DataTableColumn } from './DataTableVirtuoso';

export function TestScrollTable() {
  const isLight = useSettings().themeMode === 'light';
  const theme = useTheme();
  const { user } = useAuth();
  const dispatch = useAppDispatch();
  const { confirm } = useConfirm();
  const adminId: number = user ? user.accountId : 0;
  const [selectedRows, setSelectedRows] = useState<Set<number>>(new Set());
  const [data, setData] = useState<any[]>([]);
  const [editMode, setEditMode] = useState<{ [key: string]: boolean }>({});
  const [termFeeDetails, setTermFeeDetails] = useState<any[]>([]);
  const feeDateSettingsData = useAppSelector(getfeeDateSettingsData);
  const feeDateSettingsStatus = useAppSelector(getfeeDateSettingsStatus);
  const [termFee, setTermFeeData] = useState<any>([]);
  const [clickedCells, setClickedCells] = useState<{ rowIndex: number; columnIndex: number; value: string }[]>([]);
  const [individualSaveButtonEnabled, setIndividualSaveButtonEnabled] = useState<{ [key: string]: boolean }>({});
  const [checkedRows, setCheckedRows] = useState({});
  const [feeAmounts, setFeeAmounts] = useState<{ [key: string]: number }>({});
  const [amountEnter, setAmountEnter] = useState<any>(0 || '');
  const [drawerOpen, setDrawerOpen] = React.useState<boolean>(false);
  const [textBoxValue, setTextBoxValue] = useState<boolean>(true);
  const [errMessages, setErrMessages] = useState<{ [key: string]: boolean }>({});
  const [currentPage, setCurrentPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [enteredValues, setEnteredValues] = useState<number[]>([]);
  const [enteredCells, setEnteredCells] = useState({});
  const [rowSums, setRowSums] = useState({});
  const [showErrorIcon, setShowErrorIcon] = useState<{ [key: string]: boolean }>({});

  const currentTermFeeListRequest = React.useMemo(
    () => ({
      adminId,
      academicId: 11,
      feeTypeId: 1,
      sectionId: 1,
    }),
    [adminId]
  );

  const loadFeeDateSettingsList = useCallback(
    async (request: { adminId: number; academicId: number; sectionId: number; feeTypeId: number }) => {
      try {
        // setClickedCells([]);
        const datas: any = await dispatch(fetchFeeDateSettings(request)).unwrap();
        console.log('data::::', datas);
        // setTermFeeData(data);
        // const ClassSectionArray = data.sections ? data.sections.map((item) => ({ ...item })) : [];
        // setClassSections(ClassSectionArray);
        // console.log('ClassSectionArray::::', ClassSectionArray);
        const TermFeeDetails = datas.termFeeDetails ? datas.termFeeDetails.map((item: any) => ({ ...item })) : [];
        setTermFeeDetails(TermFeeDetails);
        console.log('TermFeeDetails::::', TermFeeDetails);
        // console.log('TermFeeDetails::::', TermFeeDetails);
      } catch (error) {
        console.error('Error loading term fee list:', error);
      }
    },
    [dispatch]
  );

  React.useEffect(() => {
    if (feeDateSettingsStatus === 'idle') {
      // dispatch(fetchYearList(adminId));
      // dispatch(fetchClassSections({ adminId, academicId: academicYearFilter }));
      loadFeeDateSettingsList(currentTermFeeListRequest);
      // dispatch(fetchGetTermFee(initialTermFeeListRequest));
    }
    setTermFeeData(feeDateSettingsData);
    // console.log('TermFeeData----::::', TermFeeData);
    <AddIcon />;
  }, [currentTermFeeListRequest, feeDateSettingsStatus, loadFeeDateSettingsList, feeDateSettingsData]);

  useEffect(() => {
    const savedData = localStorage.getItem('tableData');
    if (savedData) {
      setData(JSON.parse(savedData));
    } else {
      const initialData = Array.from({ length: 1000 }, (_, index) => ({
        id: index,
        name: `Fee ${index}`,
        description: `Description for Fee ${index}`,
        months: Array(12).fill(''), // Initialize months with empty strings
      }));
      setData(initialData);
    }
  }, []);

  const saveToLocalStorage = useCallback((updatedData: any[]) => {
    localStorage.setItem('tableData', JSON.stringify(updatedData));
  }, []);

  const handleRowSelect = useCallback(
    (id: number) => {
      const newSelectedRows = new Set(selectedRows);
      if (newSelectedRows.has(id)) {
        newSelectedRows.delete(id);
      } else {
        newSelectedRows.add(id);
      }
      setSelectedRows(newSelectedRows);
    },
    [selectedRows]
  );

  // const handleAmountChange = useCallback(
  //   (rowId: number, monthIndex: number, value: string) => {
  //     const updatedData = [...data];
  //     updatedData[rowId].months[monthIndex] = value;
  //     setData(updatedData);
  //     saveToLocalStorage(updatedData);
  //   },
  //   [data, saveToLocalStorage]
  // );

  const handleSave = useCallback(
    (rowId: number) => {
      console.log('Row saved:', data[rowId]);
    },
    [data]
  );

  const toggleEditMode = useCallback((rowId: number, monthIndex: number) => {
    const key = `${rowId}-${monthIndex}`;
    setEditMode((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  }, []);

  const handleAmountChange = useCallback(
    (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>, row: any, termId: number, id: number) => {
      const { feeId } = row;
      const cellKey: any = `${feeId}_${termId}`;

      const newValue = parseInt(e.target.value, 10);
      const newValueString = e.target.value;

      // setAmountEnter(newValue || newValueString);
      // setEnteredValues((prevValues) => [...prevValues, newValue]);
      // setEnteredCells((prevCells) => ({ ...prevCells, [cellKey]: newValue }));

      // console.log('enteredValues::::----', enteredValues);

      const termFeeMapped = Array.isArray(row.termFeeMapped) ? row.termFeeMapped : [];
      const amountObj = termFeeMapped.find((f) => f.termId === termId);
      const amount = amountObj ? amountObj.amount : 0;

      // Update the enabled state for the corresponding row
      const isZeroOrEmpty = newValue === 0 || newValueString === '';
      setIndividualSaveButtonEnabled((prevEnabled) => ({
        ...prevEnabled,
        [`${feeId}`]: !isZeroOrEmpty,
      }));

      // Update the feeAmounts array based on the new value
      setFeeAmounts((prevAmounts) => {
        const updatedAmounts = { ...prevAmounts };
        if (newValue === 0 || Number.isNaN(newValue)) {
          delete updatedAmounts[cellKey];
        } else if (newValue !== 0 || !Number.isNaN(newValue) || id !== 1) {
          updatedAmounts[cellKey] = newValue;
        } else {
          updatedAmounts[cellKey] = amount;
        }

        return updatedAmounts;
      });

      // // Update the feeAmounts array based on the new value
      // setFeeAmounts((prevAmounts) => {
      //   const updatedAmounts = { ...prevAmounts };

      //   // Check if the feeId exists in the feeAmounts array
      //   if (updatedAmounts[`${feeId}_${termId}`]) {
      //     // If the newValue is different, update the feeAmounts array
      //     if (updatedAmounts[`${feeId}_${termId}`] !== newValue) {
      //       updatedAmounts[`${feeId}_${termId}`] = newValue;
      //     }
      //   } else {
      //     // If the `${feeId}_${termId}` doesn't exist, add it to the feeAmounts array
      //     updatedAmounts[`${feeId}_${termId}`] = newValue;
      //   }

      //   return updatedAmounts;
      // });
      const { value } = e.target;
      const newAmount = parseFloat(value) || 0;
      const newTermAmounts = { ...feeAmounts, [`${row.feeId}_${termId}`]: newAmount };
      // setFeeAmounts(newTermAmounts);
      // Calculate the total sum for the row
      const rowSum = Object.entries(newTermAmounts)
        .filter(([key]) => key.startsWith(`${row.feeId}_`)) // Get only entries for the current row
        .reduce((acc, [, amnt]) => acc + amnt, 0);

      // Update the row sum state
      setRowSums((prevSums) => ({ ...prevSums, [`${row.feeId}_${termId}`]: rowSum }));
      console.log('rowSum::::----', rowSum);
      // console.log('rowSums::::----', rowSums);
      // console.log('newTermAmounts::::----', newTermAmounts);
      // console.log('feeAmounts::::----', feeAmounts);
      // console.log('newValue::::----', newValue);
    },
    [setAmountEnter, setEnteredValues, feeAmounts, setIndividualSaveButtonEnabled, enteredValues, setFeeAmounts]
  );

  const FeeDateSettingsListColumns: DataTableColumn<any>[] = useMemo(() => {
    const baseColumns: DataTableColumn<any>[] = [
      {
        name: 'feeTitle',
        renderHeader: () => (
          <Typography minWidth={130} textAlign="start" className="header-color" variant="subtitle2" fontSize={14}>
            Title
          </Typography>
        ),
        renderCell: (row) => (
          <Typography
            className="header-color"
            // color="primary"
            px={1}
            textAlign="start"
            variant="subtitle2"
            fontSize={14}
          >
            {row.feeTitle}
          </Typography>
        ),
      },
      {
        name: 'amount',
        renderHeader: () => (
          <Typography minWidth={100} textAlign="start" className="header-color" variant="subtitle2" fontSize={14}>
            Amount
          </Typography>
        ),
        renderCell: (row) => (
          <Typography px={1} textAlign="start" variant="subtitle1" fontSize={14}>
            {row.amount}
          </Typography>
        ),
      },
      {
        name: 'termfeeAmount',
        renderHeader: () => (
          <Typography minWidth={120} className="header-color" variant="subtitle2" fontSize={13}>
            Term Fee Amount
          </Typography>
        ),
        renderCell: (row) => {
          const isSelectedRow = selectedRows.has(row.feeId);
          const termIds = termFee.terms.map((m: any) => m.termId);
          const termFeeMapped = Array.isArray(row.termFeeMapped) ? row.termFeeMapped : [];
          const id = 1;
          // Check if all termIds in termFeeMapped are included in termIds
          const allAmountCheck = termIds.every((termId: number) => termFeeMapped.some((f: any) => f.termId === termId));
          return (
            <Stack>
              <TextField
                disabled={!isSelectedRow && allAmountCheck}
                // key={`cell_${row.feeId}_${item.sectionId}`}
                name="amount"
                type="number"
                variant="outlined"
                size="small"
                placeholder="Enter amount"
                error={!!errMessages[row.feeId]}
                // helperText={errMessages[row.feeId]}
                sx={{
                  // backgroundColor: theme.palette.common.white,
                  // backgroundColor: isLight ? theme.palette.common.white : theme.palette.grey[900],
                  borderRadius: 1,
                  '& .MuiInputBase-input.Mui-disabled': {
                    backgroundColor: theme.palette.grey[300],
                    borderRadius: 1,
                    padding: 1,
                    '&:hover': {
                      borderColor: theme.palette.grey[900],
                      // border:2,
                    },
                  },
                  mx: 1,
                  '& .MuiInputBase-input': {
                    borderRadius: 1,
                    padding: 1,
                  },
                }}
                onChange={(e) => {
                  const { value } = e.target;
                  if (value > row.amount) {
                    setErrMessages((prev) => ({
                      ...prev,
                      [row.feeId]: 'Enter a valid amount',
                    }));
                  } else {
                    setErrMessages((prev) => ({
                      ...prev,
                      [row.feeId]: '',
                    }));
                  }

                  setTextBoxValue(true);
                  termIds
                    .filter((termId: number) => !termFeeMapped.some((f: any) => f.termId === termId && !isSelectedRow))
                    .forEach((termId: number) => {
                      handleAmountChange(e, row, termId, id);
                    });
                }}
                // onChange={(e) => {
                //   setTextBoxValue(true);
                //   // Pass each termId with the amount change
                //   termIds
                //     .filter(
                //       (termId: number) => !termFeeMapped.some((f: TermAmount) => f.termId === termId && !isSelectedRow)
                //     )
                //     .forEach((termId: number) => {
                //       handleAmountChange(e, row, termId, id);
                //     });
                // }}
                value={textBoxValue === true && feeAmounts[`${row.feeId}_${termIds[0]}`]}
                // defaultValue={0}
                InputProps={{
                  inputProps: {
                    maxLength: 2,
                  },
                  style: {
                    // padding: '0px 5px 0px 0px',
                    margin: '25px 1px',
                    minWidth: 85,
                    color: isSelectedRow ? theme.palette.warning.main : '',
                    // backgroundColor: '#fbf7e6',
                  },
                }}
                FormHelperTextProps={{
                  style: {
                    fontSize: '10px',
                    margin: '0px',
                  },
                }}
              />
              {!!errMessages[row.feeId] && (
                <Typography
                  position="absolute"
                  bottom={5}
                  minWidth={100}
                  variant="subtitle1"
                  fontSize={10}
                  pl={1.5}
                  color={theme.palette.error.main}
                >
                  {errMessages[row.feeId]}
                </Typography>
              )}
            </Stack>
          );
        },
      },
    ];

    const headerAndBodyCells = termFee.terms
      ? termFee.terms.map((item: any, columnIndex: number) => ({
          name: `${item.termId}`,
          renderHeader: () => (
            <Stack minWidth={80} key={item.termId} className="header-color" direction="row" justifyContent="center">
              <Typography variant="subtitle2" fontSize={12}>
                {item.termTitle}
              </Typography>
            </Stack>
          ),
          renderCell: (row: any, rowIndex: number) => {
            const isCellClicked = clickedCells.some(
              (cell) => cell.rowIndex === rowIndex && cell.columnIndex === columnIndex
            );
            const isSelectedRow = selectedRows.has(row.feeId);
            const monthData = row.termFeeMapped && row.termFeeMapped.find((data) => data.termId === item.termId);

            // const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
            //   const newValue = e.target.value;
            //   const updatedCells = [...clickedCells];
            //   const cellIndex = clickedCells.findIndex(
            //     (cell) => cell.rowIndex === rowIndex && cell.columnIndex === columnIndex
            //   );

            //   if (cellIndex !== -1) {
            //     // Update existing cell value
            //     updatedCells[cellIndex] = { rowIndex, columnIndex, value: newValue };
            //   } else {
            //     // Add new cell value
            //     updatedCells.push({ rowIndex, columnIndex, value: newValue });
            //   }
            //   setClickedCells(updatedCells);
            // };
            const id = 0;
            if (monthData) {
              const { amount, academicTermId } = monthData;
              return (
                <Stack
                  position="relative"
                  direction="row"
                  alignItems="center"
                  key={`cell_${row.feeId}_${item.termId}`}
                  onClick={() => {
                    if (!isCellClicked) {
                      setClickedCells([...clickedCells, { rowIndex, columnIndex, value: '' }]);
                    }
                  }}
                  className={isSelectedRow ? 'cellActive' : 'cellInActive'}
                  sx={{
                    borderLeft: 3,
                  }}
                  color="#000"
                  gap={2}
                  pl={1}
                  height="80px"
                >
                  {!isSelectedRow && (
                    <>
                      <Stack direction="row" alignItems="center">
                        <CurrencyRupeeIcon sx={{ fontSize: '14px' }} color="success" />
                        <Typography variant="subtitle2" fontSize={12}>
                          {amount}
                        </Typography>
                      </Stack>
                      <Stack position="absolute" top={2} right={2}>
                        <IconButton size="small">
                          <DeleteIcon sx={{ fontSize: 16 }} />
                        </IconButton>
                      </Stack>
                    </>
                  )}
                  {isSelectedRow && (
                    <>
                      {/* <Typography position="absolute" top={0} right={2} variant="subtitle2" fontSize={10}>
                        Edit
                      </Typography> */}
                      <Stack direction="row" alignItems="center" justifyContent="center">
                        <CurrencyRupeeIcon sx={{ fontSize: '14px' }} color="warning" />
                        <TextField
                          name=""
                          type="number"
                          variant="outlined"
                          size="small"
                          sx={{
                            '& fieldset': { border: '1px' },
                            '& .MuiOutlinedInput-root': {
                              '&:has(> input[data-com-onepassword-filled="light"])': {
                                backgroundColor: 'transparent',
                              },
                            },
                          }}
                          inputProps={{
                            maxLength: 6,
                            style: {
                              padding: '30px 1px',
                              minWidth: 50,
                              cursor: 'text',
                              // fontFamily: ,
                              color: theme.palette.warning.main,
                            },
                            // '& input::placeholder': {
                            //   fontSize: '1px',
                            // },
                          }}
                          // placeholder="₹"
                          onChange={(e) => handleAmountChange(e, row, item.termId, id)}
                          value={feeAmounts[`${row.feeId}_${item.termId}`]}
                          defaultValue={amount}
                        />
                      </Stack>
                    </>
                  )}
                </Stack>
              );
            } else {
              // const monthData2 = row.termFeeMapped && row.termFeeMapped.find((data) => data.termId === item.termId);
              // if (monthData2) {
              //   const { amount } = monthData2;
              return (
                <Stack
                  alignItems="center"
                  justifyContent="center"
                  key={`cell_${row.feeId}_${item.termId}`}
                  // color={isSelectedRow ? theme.palette.grey[400] : theme.palette.grey[500]}
                  // bgcolor={isSelectedRow ? theme.palette.grey[50] : ''}
                  className={isSelectedRow ? 'inputCellActive' : 'inputCellInActive'}
                  gap={2}
                  minWidth="100%"
                  height="100%"
                >
                  {isCellClicked ? (
                    <Stack direction="row" alignItems="center" justifyContent="center">
                      <CurrencyRupeeIcon sx={{ fontSize: '14px' }} />
                      <TextField
                        name=""
                        type="number"
                        variant="outlined"
                        size="small"
                        sx={{
                          '& fieldset': { border: '1px' },
                          '& .MuiOutlinedInput-root': {
                            '&:has(> input[data-com-onepassword-filled="light"])': {
                              backgroundColor: 'red',
                            },
                          },
                        }}
                        inputProps={{
                          maxLength: 6,
                          style: {
                            padding: '30px 1px',
                            minWidth: 50,
                            // fontFamily: 'sans-serif',
                            color: `${showErrorIcon[row.feeId] === true && 'red'}`,
                          },
                          // '& input::placeholder': {
                          //   fontSize: '1px',
                          // },
                        }}
                        // placeholder="₹"
                        onChange={(e) => handleAmountChange(e, row, item.termId, id)}
                        value={feeAmounts[`${row.feeId}_${item.termId}`]}
                        disabled={rowSums[`${row.feeId}_${item.termId}`] > row.amount || isSelectedRow}
                      />
                    </Stack>
                  ) : (
                    !isCellClicked && (
                      <Stack
                        onClick={() =>
                          !isSelectedRow && setClickedCells([...clickedCells, { rowIndex, columnIndex, value: '' }])
                        }
                        sx={{
                          width: '100%',
                          height: '100%',
                          '&:hover': {
                            color: !isSelectedRow ? theme.palette.success.main : '',
                            transform: !isSelectedRow ? 'scale(1.2)' : '',
                            transition: !isSelectedRow ? '.1s' : '',
                          },
                        }}
                        direction="row"
                        justifyContent="center"
                        alignItems="center"
                      >
                        <AddIcon />
                      </Stack>
                    )
                  )}
                </Stack>
              );
            }
          },
        }))
      : [];

    const baseColumns2: DataTableColumn<any>[] = [
      // {
      //   name: 'total',
      //   headerLabel: 'Total',
      //   renderCell: (row, index) => (
      //     <Typography px={1} textAlign="start" variant="subtitle1" fontSize={13}>
      //       {calculateTotals(row, index)}
      //     </Typography>
      //   ),
      // },
      {
        name: 'Actions',
        renderHeader: () => (
          <Typography textAlign="start" className="header-color" variant="subtitle2" fontSize={14}>
            Actions
          </Typography>
        ),
        // renderCell: (row) => {
        //   const isSelectedRow = selectedRows.includes(row);
        //   return (
        //     <Stack minWidth={80} direction="row" alignItems="center" justifyContent="center" gap={1}>
        //       {
        //         showSuccessIcon[row.feeId] === true ? (
        //           <Stack direction="row" justifyContent="center" flexDirection="column" alignItems="center">
        //             {succesResponse === 'Success' ? (
        //               <>
        //                 <Lottie animationData={Success} loop={false} style={{ width: '30px' }} />
        //                 <Typography color={theme.palette.success.main} fontSize={7} variant="subtitle2">
        //                   Saved
        //                 </Typography>
        //               </>
        //             ) : (
        //               <>
        //                 <Lottie animationData={Updated} loop={false} style={{ width: '30px' }} />
        //                 <Typography color={theme.palette.warning.main} fontSize={7} variant="subtitle2">
        //                   Updated
        //                 </Typography>
        //               </>
        //             )}
        //           </Stack>
        //         ) : showErrorIcon[row.feeId] === true ? (
        //           <Stack direction="row" justifyContent="center">
        //             <Lottie animationData={Error} loop={false} style={{ width: '30px' }} />
        //           </Stack>
        //         ) : !individualSaveLoading[row.feeId] === true ? (
        //           <IconButton
        //             disabled={!individualSaveButtonEnabled[row.feeId] || saving}
        //             size="small"
        //             color={isSelectedRow ? 'warning' : 'success'}
        //             aria-label=""
        //             // onClick={() => handleSave(row)}
        //           >
        //             Save
        //             {/* <SaveIcon fontSize="small" /> */}
        //           </IconButton>
        //         ) : (
        //           <Stack direction="row" justifyContent="center" flexDirection="column" alignItems="center">
        //             <>
        //               <Lottie
        //                 animationData={isSelectedRow ? UpdateLoading : SaveLoading}
        //                 loop
        //                 style={{ width: '20px' }}
        //               />
        //               <Typography
        //                 color={isSelectedRow ? theme.palette.warning.main : theme.palette.success.main}
        //                 fontSize={7}
        //                 variant="subtitle2"
        //               >
        //                 {isSelectedRow ? 'Updating...' : 'Saving...'}
        //               </Typography>
        //             </>
        //           </Stack>
        //         )
        //         // <LoadingButton
        //         //   loading={individualSaveLoading[row.feeId]}
        //         //   onClick={() => handleSave(row)}
        //         //   variant="contained"
        //         //   size="small"
        //         //   color="success"
        //         //   disabled={!individualSaveButtonEnabled[row.feeId] || saving}
        //         //   sx={{ py: 0.5, fontSize: '10px' }}
        //         // >
        //         //   {!individualSaveLoading[row.feeId] ? 'Save' : ''}
        //         // </LoadingButton>
        //       }

        //       <IconButton
        //         disabled={row.termFeeMapped === null}
        //         // onClick={() => handleDeleteRow(row)}
        //         size="small"
        //         color="error"
        //         aria-label=""
        //       >
        //         <DeleteIcon fontSize="small" />
        //       </IconButton>
        //     </Stack>
        //   );
        // },
      },
    ];

    return [...baseColumns, ...headerAndBodyCells, ...baseColumns2];
  }, [
    theme,
    handleSave,
    clickedCells,
    feeAmounts,
    termFee,
    individualSaveButtonEnabled,
    showErrorIcon,
    handleAmountChange,
    selectedRows,
    textBoxValue,
    errMessages,
    rowSums,
  ]);

  const isAllSelected = selectedRows.size === data.length;

  const handleSelectAll = useCallback(() => {
    if (isAllSelected) {
      setSelectedRows(new Set());
    } else {
      setSelectedRows(new Set(data.map((row) => row.id)));
    }
  }, [isAllSelected, data]);

  return (
    <TableContainer component={Paper} style={{ height: 500 }}>
      <TableVirtuoso
        data={termFeeDetails}
        components={{
          Table: (props) => <Table {...props} />,
          TableHead,
          TableRow,
          TableBody,
        }}
        fixedHeaderContent={() => (
          <TableHead>
            <TableRow sx={{ backgroundColor: isLight ? '#f5f5f5' : '#333' }}>
              <TableCell>
                <Checkbox checked={isAllSelected} onChange={handleSelectAll} />
              </TableCell>
              {FeeDateSettingsListColumns.map((column) => (
                <TableCell key={column.name}>
                  {column.renderHeader ? column.renderHeader() : column.headerLabel}
                </TableCell>
                // <TableCell key={column.name}>{column.renderHeader()}</TableCell>
              ))}
            </TableRow>
          </TableHead>
        )}
        itemContent={(index, row) => (
          <TableRow key={row.id}>
            <TableCell>
              <Checkbox checked={selectedRows.has(row.feeId)} onChange={() => handleRowSelect(row.feeId)} />
            </TableCell>
            {FeeDateSettingsListColumns.map((column) => {
              let cellData = null;
              if (column.renderCell) {
                cellData = column.renderCell(row, index);
              } else if (column.dataKey === 'TextField') {
                // cellData = <TextField disabled={!isRowSelected} placeholder={column.name} />;
              } else if (column.dataKey) {
                cellData = (row as Record<string, any>)[column.dataKey];
              }
              return (
                <TableCell
                  // onClick={column.dataKey === 'TextField' ? undefined : column.dataKey === 'Button' ? undefined : ''}
                  sx={{ textAlign: column.align || 'start' }}
                  key={`Row_${index}_${column.name}`}
                  width={column.width ? column.width : 'auto'}
                >
                  {cellData}
                </TableCell>
              );
            })}
            {/* {termFeeDetails.map((column) => (
              <TableCell key={column.name}>{column.renderCell ? column.renderCell(row) : null}</TableCell>
            ))} */}
            <TableCell>
              <Button
                variant="contained"
                size="small"
                disabled={!selectedRows.has(row.id)}
                onClick={() => handleSave(row.id)}
              >
                Save
              </Button>
            </TableCell>
          </TableRow>
        )}
      />
    </TableContainer>
  );
}
