{"homepage": "https://passdaily-technologies.github.io/passdaily-web", "proxy": "http://localhost:8080", "name": "passdaily", "private": true, "version": "0.0.0", "type": "module", "scripts": {"predeploy": "pnpm vite build", "deploy": "gh-pages -d dist", "dev": "vite", "build": "node --max-old-space-size=4096 node_modules/vite/bin/vite.js build", "preview": "vite preview", "lint": "eslint .", "lint:fix": "eslint --fix .", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "start": "react-scripts --max_old_space_size=4096 start", "test": "react-scripts test", "eject": "react-scripts eject"}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@fontsource/roboto": "^4.5.8", "@material-ui/core": "^4.12.4", "@mdxeditor/editor": "^3.35.1", "@mui/base": "5.0.0-beta.0", "@mui/icons-material": "^5.14.0", "@mui/lab": "5.0.0-alpha.120", "@mui/material": "^5.14.0", "@mui/styled-engine-sc": "^5.12.0", "@mui/x-data-grid": "^5.17.26", "@mui/x-date-pickers": "^6.19.6", "@mui/x-date-pickers-pro": "^6.18.0", "@react-google-maps/api": "^2.18.1", "@react-icons/all-files": "^4.1.0", "@reduxjs/toolkit": "^1.9.5", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/lodash": "^4.14.202", "@types/node": "^16.18.38", "@types/react": "^18.2.14", "@types/react-dom": "^18.2.6", "@zoom/meetingsdk": "^3.13.0", "@zoomus/websdk": "^2.18.3", "apexcharts": "^3.41.0", "axios": "^1.4.0", "bootstrap": "^5.3.0", "chart.js": "^3.9.1", "dayjs": "^1.11.10", "formik": "^2.4.2", "framer-motion": "^10.12.18", "html2canvas": "^1.4.1", "http-proxy-middleware": "^3.0.0", "jspdf": "^2.5.1", "jspdf-autotable": "^3.8.2", "jwt-decode": "^3.1.2", "lodash": "^4.17.21", "lodash.debounce": "^4.0.8", "lottie-react": "^2.4.0", "material-ui-popup-state": "^5.0.10", "nanoid": "^5.0.7", "react": "^18.2.0", "react-advanced-cropper": "^0.20.0", "react-apexcharts": "^1.4.0", "react-avatar-editor": "^13.0.2", "react-beautiful-dnd": "^13.1.1", "react-bootstrap": "^2.8.0", "react-chartjs-2": "^4.3.1", "react-confirm": "0.3.0-2", "react-custom-scrollbars": "^4.2.1", "react-datepicker": "^4.16.0", "react-dom": "^18.2.0", "react-easy-crop": "^5.2.0", "react-h5-audio-player": "^3.9.1", "react-helmet-async": "^1.3.0", "react-icons": "^4.10.1", "react-player": "^2.12.0", "react-redux": "^8.1.1", "react-router-dom": "^6.14.1", "react-router-tabs": "^1.3.2", "react-scripts": "5.0.1", "react-slick": "^0.29.0", "react-swipeable-views": "^0.14.0", "react-time-picker": "^6.5.2", "react-to-pdf": "^1.0.1", "react-to-print": "^2.14.13", "react-transition-group": "^4.4.5", "react-virtuoso": "^4.12.3", "reactjs-tiptap-editor": "^0.3.4", "redux-logger": "^3.0.6", "styled-components": "^5.3.11", "swiper": "^9.4.1", "typescript": "^4.9.5", "uuid": "^9.0.0", "vite-plugin-pwa": "^0.16.7", "web-vitals": "^2.1.4", "xlsx": "^0.18.5", "yup": "^0.32.11"}, "devDependencies": {"@rollup/plugin-alias": "^4.0.4", "@storybook/addon-docs": "^6.5.16", "@storybook/addon-essentials": "7.0.0-beta.54", "@storybook/addon-interactions": "7.0.0-beta.54", "@storybook/addon-links": "7.0.0-beta.54", "@storybook/addons": "^6.5.16", "@storybook/blocks": "7.0.0-alpha.8", "@storybook/preset-create-react-app": "7.0.0-beta.54", "@storybook/react": "7.0.0-beta.54", "@storybook/react-webpack5": "7.0.0-beta.54", "@storybook/testing-library": "0.0.14-next.1", "@storybook/theming": "^6.5.16", "@types/lodash.debounce": "^4.0.7", "@types/react": "^18.0.27", "@types/react-confirm": "^0.2.0", "@types/react-datepicker": "^4.11.2", "@types/react-dom": "^18.0.10", "@types/react-redux": "^7.1.25", "@types/react-slick": "^0.23.10", "@types/react-swipeable-views": "^0.13.2", "@types/redux-logger": "^3.0.9", "@types/styled-components": "^5.1.26", "@types/uuid": "^9.0.2", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "@vitejs/plugin-basic-ssl": "^1.0.1", "@vitejs/plugin-react": "^3.1.0", "babel-plugin-named-exports-order": "0.0.2", "eslint": "^8.44.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-prettier": "^8.8.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-storybook": "^0.6.12", "gh-pages": "^5.0.0", "miragejs": "^0.1.47", "prettier": "2.8.3", "prop-types": "15.8.1", "storybook": "7.0.0-beta.54", "tsconfig-paths-webpack-plugin": "^4.1.0", "typescript": "^4.9.3", "vite": "^4.4.3", "vite-plugin-checker": "^0.6.1", "vite-plugin-mkcert": "^1.16.0", "webpack": "5.75.0"}, "pnpm": {"overrides": {"nth-check@<2.0.1": ">=2.0.1"}}, "eslintConfig": {"overrides": [{"files": ["**/*.stories.*"], "rules": {"import/no-anonymous-default-export": "off"}}, {"files": ["**/*.stories.*"], "rules": {"import/no-anonymous-default-export": "off"}}]}}