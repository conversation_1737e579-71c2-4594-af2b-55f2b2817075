import ApiUrls from '@/config/ApiUrls';
import {
  ConveyorDataType,
  GroupsDataType,
  MessageTempCreateRequest,
  MessageTempDataType,
  MessageTempRequest,
  ParentsDataType,
  PtaDataListRequest,
  PtaDataType,
  SendToParentsRequest,
  SendToParentsResponse,
  SendToPtaType,
  SendToStaffType,
  SendToConveyorType,
  SendToGroupMembersType,
  StaffDataListRequest,
  StaffDataType,
  GroupMembersDataType,
  ClassDivisionDataType,
  SendToClassDivisionType,
  ClassSectionDataType,
  SendToClassSectionType,
  GroupWiseDataType,
  SendToGroupWiseType,
  PublicGroupsDataType,
  PublicGroupMembersDataType,
  SendToPublicGroupMembersType,
  SendToPublicGroupWiseType,
  PublicGroupWiseDataType,
  DeleteMultipleMessageTemplateType,
  UplaodFilesType,
} from '@/types/MessageBox';
import { CreateResponse, DeleteR<PERSON>ponse, SendResponse, UpdateResponse } from '@/types/Common';

import { privateApi } from './base/api';
import { APIResponse } from './base/types';

async function GetMessageTempList(request: MessageTempRequest): Promise<APIResponse<MessageTempDataType[]>> {
  const response = await privateApi.post<MessageTempDataType[]>(ApiUrls.GetMessageTempList, request);
  console.log('response', response);
  return response;
}

async function CreateNewMessage(request: MessageTempCreateRequest): Promise<APIResponse<CreateResponse>> {
  const response = await privateApi.post<CreateResponse>(ApiUrls.CreateMessageTemplate, request);
  return response;
}

async function UpdateMessageTemplate(request: MessageTempCreateRequest): Promise<APIResponse<UpdateResponse>> {
  const response = await privateApi.post<UpdateResponse>(ApiUrls.EditMessageTemplate, request);
  return response;
}

async function DeleteMessageTemplate(messageId: number): Promise<APIResponse<DeleteResponse>> {
  const url = ApiUrls.DeleteMessageTemplate.replace('{messageId}', messageId.toString());
  const response = await privateApi.get<DeleteResponse>(url);
  return response;
}

async function DeleteMultipleMessageTemplate(
  request: DeleteMultipleMessageTemplateType[]
): Promise<APIResponse<DeleteResponse>> {
  const response = await privateApi.post<DeleteResponse>(ApiUrls.DeleteMultipleMessageTemplate, request);
  return response;
}

async function GetParentsList(
  adminId: number | undefined,
  academicId: number | undefined,
  classId: number | undefined
): Promise<APIResponse<ParentsDataType[]>> {
  if (adminId === undefined || academicId === undefined || classId === undefined) {
    throw new Error('adminId, academicId, and classId are required');
  }

  const url = ApiUrls.GetParentsList.replace('{adminId}', adminId.toString())
    .replace('{academicId}', academicId.toString())
    .replace('{classId}', classId.toString());

  const response = await privateApi.get<any>(url);
  return response;
}
async function SendToParents(request: SendToParentsRequest): Promise<APIResponse<SendResponse>> {
  const response = await privateApi.post<SendResponse>(ApiUrls.SendToParents, request);
  return response;
}
async function SendToParentsList(request: SendToParentsRequest[]): Promise<APIResponse<SendToParentsResponse>> {
  const response = await privateApi.post<SendToParentsResponse>(ApiUrls.SendToParentsList, request);
  return response;
}

async function GetStaffList(request: StaffDataListRequest): Promise<APIResponse<StaffDataType[]>> {
  const response = await privateApi.post<StaffDataType[]>(ApiUrls.GetStaffList, request);
  return response;
}
async function SendToStaffList(request: SendToStaffType[]): Promise<APIResponse<SendToStaffType>> {
  const response = await privateApi.post<SendToStaffType>(ApiUrls.SendToStaffList, request);
  return response;
}
async function GetPtaList(request: PtaDataListRequest): Promise<APIResponse<PtaDataType[]>> {
  const response = await privateApi.post<PtaDataType[]>(ApiUrls.GetPtaList, request);
  return response;
}
async function SendToPtaList(request: SendToPtaType[]): Promise<APIResponse<SendToPtaType>> {
  const response = await privateApi.post<SendToPtaType>(ApiUrls.SendToPtaList, request);
  return response;
}
async function GetConveyorList(classId: number | undefined): Promise<APIResponse<ConveyorDataType[]>> {
  if (classId === undefined) {
    throw new Error('adminId, academicId, and classId are required');
  }

  const url = ApiUrls.GetConveyorList.replace('{classId}', classId.toString());

  const response = await privateApi.get<any>(url);
  return response;
}
async function SendToConveyorList(request: SendToConveyorType[]): Promise<APIResponse<SendToConveyorType>> {
  const response = await privateApi.post<SendToConveyorType>(ApiUrls.SendToConveyorList, request);
  return response;
}

async function GetGroupsList(
  adminId: number | undefined,
  academicId: number | undefined
): Promise<APIResponse<GroupsDataType[]>> {
  if (adminId === undefined || academicId === undefined) {
    throw new Error('adminId and academicId are required');
  }

  const url = ApiUrls.GetGroupsList.replace('{adminId}', adminId.toString()).replace(
    '{academicId}',
    academicId.toString()
  );

  const response = await privateApi.get<any>(url);
  return response;
}
async function GetGroupMembersList(
  adminId: number | undefined,
  academicId: number | undefined,
  groupId: number | undefined
): Promise<APIResponse<GroupMembersDataType[]>> {
  if (adminId === undefined || academicId === undefined || groupId === undefined) {
    throw new Error('adminId, academicId, and groupId are required');
  }

  const url = ApiUrls.GetGroupMembersList.replace('{adminId}', adminId.toString())
    .replace('{academicId}', academicId.toString())
    .replace('{groupId}', groupId.toString());

  const response = await privateApi.get<any>(url);
  return response;
}
async function SendToGroupMembersList(request: SendToGroupMembersType[]): Promise<APIResponse<SendToGroupMembersType>> {
  const response = await privateApi.post<SendToGroupMembersType>(ApiUrls.SendToGroupMembersList, request);
  return response;
}

async function GetClassDivision(
  adminId: number | undefined,
  academicId: number | undefined
): Promise<APIResponse<ClassDivisionDataType[]>> {
  if (adminId === undefined || academicId === undefined) {
    throw new Error('adminId and academicId are required');
  }

  const url = ApiUrls.GetClassDivision.replace('{adminId}', adminId.toString()).replace(
    '{academicId}',
    academicId.toString()
  );

  const response = await privateApi.get<any>(url);
  return response;
}

async function SendToClassDivision(request: SendToClassDivisionType[]): Promise<APIResponse<SendToClassDivisionType>> {
  const response = await privateApi.post<SendToClassDivisionType>(ApiUrls.SendToClassDivision, request);
  return response;
}

async function GetClassSection(
  adminId: number | undefined,
  academicId: number | undefined
): Promise<APIResponse<ClassSectionDataType[]>> {
  if (adminId === undefined || academicId === undefined) {
    throw new Error('adminId and academicId are required');
  }

  const url = ApiUrls.GetClassSection.replace('{adminId}', adminId.toString()).replace(
    '{academicId}',
    academicId.toString()
  );

  const response = await privateApi.get<any>(url);
  return response;
}
async function SendToClassSection(request: SendToClassSectionType[]): Promise<APIResponse<SendToClassSectionType>> {
  const response = await privateApi.post<SendToClassSectionType>(ApiUrls.SendToClassSection, request);
  return response;
}

async function GetGroupWise(
  adminId: number | undefined,
  academicId: number | undefined
): Promise<APIResponse<GroupWiseDataType[]>> {
  if (adminId === undefined || academicId === undefined) {
    throw new Error('adminId and academicId are required');
  }

  const url = ApiUrls.GetGroupWise.replace('{adminId}', adminId.toString()).replace(
    '{academicId}',
    academicId.toString()
  );

  const response = await privateApi.get<any>(url);
  return response;
}

async function SendToGroupWise(request: SendToGroupWiseType[]): Promise<APIResponse<SendToGroupWiseType>> {
  const response = await privateApi.post<SendToGroupWiseType>(ApiUrls.SendToGroupWise, request);
  return response;
}

// ====================

async function GetPublicGroupList(
  adminId: number | undefined,
  academicId: number | undefined
): Promise<APIResponse<PublicGroupsDataType[]>> {
  if (adminId === undefined || academicId === undefined) {
    throw new Error('adminId and academicId are required');
  }

  const url = ApiUrls.GetPublicGroupList.replace('{adminId}', adminId.toString()).replace(
    '{academicId}',
    academicId.toString()
  );

  const response = await privateApi.get<any>(url);
  return response;
}
async function GetPublicGroupMembersList(
  adminId: number | undefined,
  academicId: number | undefined,
  groupId: number | undefined
): Promise<APIResponse<PublicGroupMembersDataType[]>> {
  if (adminId === undefined || academicId === undefined || groupId === undefined) {
    throw new Error('adminId, academicId, and groupId are required');
  }

  const url = ApiUrls.GetPublicGroupMembersList.replace('{adminId}', adminId.toString())
    .replace('{academicId}', academicId.toString())
    .replace('{groupId}', groupId.toString());

  const response = await privateApi.get<any>(url);
  return response;
}
async function SendToPublicGroupMembersList(
  request: SendToPublicGroupMembersType[]
): Promise<APIResponse<SendToPublicGroupMembersType>> {
  const response = await privateApi.post<SendToPublicGroupMembersType>(ApiUrls.SendToPublicGroupMembersList, request);
  return response;
}

// Public group wise //

async function GetPublicGroupWiseList(
  adminId: number | undefined,
  academicId: number | undefined,
  groupId: number | undefined
): Promise<APIResponse<PublicGroupWiseDataType[]>> {
  if (adminId === undefined || academicId === undefined || groupId === undefined) {
    throw new Error('adminId, academicId, and groupId are required');
  }

  const url = ApiUrls.GetPublicGroupWiseList.replace('{adminId}', adminId.toString())
    .replace('{academicId}', academicId.toString())
    .replace('{groupId}', groupId.toString());

  const response = await privateApi.get<any>(url);
  return response;
}
async function SendToPublicGroupWiseList(
  request: SendToPublicGroupWiseType[]
): Promise<APIResponse<SendToPublicGroupWiseType>> {
  const response = await privateApi.post<SendToPublicGroupWiseType>(ApiUrls.SendToPublicGroupWiseList, request);
  return response;
}
//
// ==============================Send To All===================================
//

async function SendToAllParents(
  adminId: number | undefined,
  academicId: number | undefined,
  messageId: number | undefined
): Promise<APIResponse<SendResponse>> {
  if (adminId === undefined || academicId === undefined || messageId === undefined) {
    throw new Error('adminId, academicId, and messageId are required');
  }

  const url = ApiUrls.SendToAllParents.replace('{adminId}', adminId.toString())
    .replace('{academicId}', academicId.toString())
    .replace('{messageId}', messageId.toString());

  const response = await privateApi.get<any>(url);
  return response;
}

async function SendToAllStaff(
  adminId: number | undefined,
  academicId: number | undefined,
  messageId: number | undefined
): Promise<APIResponse<SendResponse>> {
  if (adminId === undefined || academicId === undefined || messageId === undefined) {
    throw new Error('adminId, academicId, and messageId are required');
  }

  const url = ApiUrls.SendToAllStaff.replace('{adminId}', adminId.toString())
    .replace('{academicId}', academicId.toString())
    .replace('{messageId}', messageId.toString());

  const response = await privateApi.get<any>(url);
  return response;
}

async function SendToAllPta(
  adminId: number | undefined,
  academicId: number | undefined,
  messageId: number | undefined
): Promise<APIResponse<SendResponse>> {
  if (adminId === undefined || academicId === undefined || messageId === undefined) {
    throw new Error('adminId, academicId, and messageId are required');
  }

  const url = ApiUrls.SendToAllPta.replace('{adminId}', adminId.toString())
    .replace('{academicId}', academicId.toString())
    .replace('{messageId}', messageId.toString());

  const response = await privateApi.get<any>(url);
  return response;
}

async function SendToAllConveyors(
  adminId: number | undefined,
  academicId: number | undefined,
  messageId: number | undefined
): Promise<APIResponse<SendResponse>> {
  if (adminId === undefined || academicId === undefined || messageId === undefined) {
    throw new Error('adminId, academicId, and messageId are required');
  }

  const url = ApiUrls.SendToAllConveyors.replace('{adminId}', adminId.toString())
    .replace('{academicId}', academicId.toString())
    .replace('{messageId}', messageId.toString());

  const response = await privateApi.get<any>(url);
  return response;
}

async function FetchMessageTemplate(messageId: number | undefined): Promise<APIResponse<MessageTempDataType>> {
  if (messageId === undefined) {
    throw new Error('messageId required');
  }

  const url = ApiUrls.FetchMessageTemplate.replace('{messageId}', messageId.toString());

  const response = await privateApi.get<any>(url);
  return response;
}

type UploadProgressEvent = ProgressEvent & {
  loaded: number;
  total: number;
};

// File Upload
async function FileUpload(files: File[]): Promise<APIResponse<UplaodFilesType>> {
  try {
    console.log('files', files);
    const response = await privateApi.post<any>(ApiUrls.FileUpload, files, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    console.log(' response.data', response.data);
    return response.data;
  } catch (error) {
    throw new Error('Failed to upload files');
  }
}
// async function FileUpload(files: File[]): Promise<APIResponse<UplaodFilesType>> {
//   try {
//     const formData = new FormData();
//     files.forEach((file) => {
//       formData.append('files', file);
//     });
//     const response = await privateApi.post<any>(ApiUrls.FileUpload, formData, {
//       headers: {
//         'Content-Type': 'multipart/form-data',
//       },
//     });
//     return response.data;
//   } catch (error) {
//     throw new Error('Failed to upload files');
//   }
// }
const methods = {
  GetMessageTempList,
  CreateNewMessage,
  UpdateMessageTemplate,
  DeleteMessageTemplate,
  FetchMessageTemplate,
  GetParentsList,
  SendToParents,
  SendToParentsList,
  GetStaffList,
  SendToStaffList,
  GetPtaList,
  SendToPtaList,
  GetConveyorList,
  SendToConveyorList,
  GetGroupsList,
  GetGroupMembersList,
  SendToGroupMembersList,
  GetClassDivision,
  SendToClassDivision,
  GetClassSection,
  SendToClassSection,
  GetGroupWise,
  SendToGroupWise,
  GetPublicGroupList,
  GetPublicGroupMembersList,
  SendToPublicGroupMembersList,
  GetPublicGroupWiseList,
  SendToPublicGroupWiseList,
  SendToAllParents,
  SendToAllStaff,
  SendToAllPta,
  SendToAllConveyors,
  DeleteMultipleMessageTemplate,
  FileUpload,
};

export default methods;
