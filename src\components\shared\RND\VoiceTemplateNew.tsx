/* eslint-disable object-shorthand */
/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/alt-text */
import React, { FormEvent, ReactElement, useCallback, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Divider,
  Grid,
  Stack,
  TextField,
  Button,
  Box,
  Typography,
  Card,
  IconButton,
  Collapse,
  Select,
  MenuItem,
  FormControl,
  SelectChangeEvent,
  Skeleton,
  Tooltip,
  Checkbox,
} from '@mui/material';
import LoadingButton from '@mui/lab/LoadingButton';
import styled, { useTheme } from 'styled-components';
import Popup from '@/components/shared/Popup/Popup';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import MenuEditDelete from '@/components/shared/Selections/MenuEditDelete';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import SearchIcon from '@mui/icons-material/Search';
import { MdAdd, MdDelete } from 'react-icons/md';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { getVoiceData, getVoiceStatus, getVoiceSubmitting } from '@/config/storeSelectors';
import {
  messageSendToAllConveyors,
  messageSendToAllParents,
  messageSendToAllPta,
  messageSendToAllStaff,
} from '@/store/MessageBox/messageBox.thunks';
import { useAppSelector } from '@/hooks/useAppSelector';
import CachedIcon from '@mui/icons-material/Cached';
import dayjs, { Dayjs } from 'dayjs';
import AudioPlayer from 'react-h5-audio-player';
import 'react-h5-audio-player/lib/styles.css';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import useAuth from '@/hooks/useAuth';
import RemoveFile from '@/assets/MessageIcons/remove-file.gif';
import SaveFile from '@/assets/MessageIcons/save-file.gif';
import DeleteFile from '@/assets/MessageIcons/bin-file.gif';
import LoadingMsg from '@/assets/MessageIcons/loading-message.gif';
import ErrorMsgTemplate from '@/assets/MessageIcons/error-msg-template.gif';
import ErrorMsg from '@/assets/MessageIcons/error-message.gif';
import ErrorMsg1 from '@/assets/MessageIcons/error-message1.gif';
import SuccessMsg from '@/assets/MessageIcons/message-success.gif';
import messageIcon from '@/assets/MessageIcons/messages.gif';
import NoData from '@/assets/no-datas.png';
import emptyMsg from '@/assets/emptyMsg.gif';
import { ErrorMessage } from '@/components/shared/Popup/ErrorMessage';
import { LoadingMessage } from '@/components/shared/Popup/LoadingMessage';
import Lottie from 'lottie-react';
import useSettings from '@/hooks/useSettings';
import successIcon from '@/assets/MessageIcons/success.json';
import errorIcon from '@/assets/MessageIcons/error-message.json';
import { IoIosArrowUp } from 'react-icons/io';
// import ReactAudioPlayer from 'react-audio-player';
import {
  DeleteMultipleVoiceMessageList,
  DeleteVoiceMessageList,
  createVoice,
  fetchVoiceMessageList,
  voiceMessageSendToAllConveyors,
  voiceMessageSendToAllParents,
  voiceMessageSendToAllPta,
  voiceMessageSendToAllStaff,
} from '@/store/VoiceMessage/voiceMessage.thunk';
import SendMessage from '@/features/MessageBox/SmsTemplate/SendMessage';
import {
  DeleteMultipleVoiceMessageType,
  VoiceCreateRequest,
  VoiceDataType,
  VoiceMessageFilter,
} from '@/types/VoiceMessage';
import DatePickers from '@/components/shared/Selections/DatePicker';
import { STATUS_OPTIONS } from '@/config/Selection';
import { CreateEditNotificationForm } from '@/features/VoiceMessage/VoiceTemplate/CreateEditVoiceMessageForm';
import { InputLabel } from '@mui/material';
import FilesUpload from '../Selections/FilesUpload';
// import { TransitionGroup, CSSTransition } from 'react-transition-group';

const TemplateVoiceMessageRoot = styled.div`
  padding: 1rem;

  .icon {
    margin-right: 10px;
  }
  .Card {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 30px);
      flex-grow: 1;

      .card-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: auto;
        @media screen and (max-width: 996px) {
          height: 100%;
        }
      }
    }

    /* .Card-Top {
    display: flex;
    align-items: center;
    justify-content: space-between;
  } */
    .ListCard {
      /* border: 1px solid
        ${(props) =>
        props.theme.themeMode === 'light' ? props.theme.palette.grey[300] : props.theme.palette.grey[900]}; */

      background-color: ${(props) =>
        props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.grey[900]};
    }
    .Skelton-ListCard {
      border: 1px solid
        ${(props) =>
          props.theme.themeMode === 'light' ? props.theme.palette.grey[300] : props.theme.palette.grey[900]};

      background-color: ${(props) =>
        props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.grey[900]};
    }
  }
`;

export type SendButtonType = {
  id: string;
  name: string;
  component: string;
};
export type AllSendButtonsType = {
  id: number;
  name: string;
  action: any;
};
export const sendbuttons: SendButtonType[] = [
  { id: '1', name: 'Parent', component: 'Parents' },
  { id: '2', name: 'Staff', component: 'Staffs' },
  { id: '3', name: 'PTA', component: 'PTA' },
  { id: '4', name: 'Conveyor', component: 'Conveyors' },
  { id: '5', name: 'Group', component: 'Groups' },
  { id: '6', name: 'Public Group', component: 'PublicGroups' },
  { id: '7', name: 'Group Wise', component: 'GroupWise' },
  { id: '8', name: 'Public Group Wise', component: 'PublicGroupWise' },
  { id: '9', name: 'Class Wise', component: 'ClassWise' },
  { id: '10', name: 'Class Division', component: 'ClassDivision' },
];

export const MESSAGE_TYPE_OPTIONS = [
  { id: '-1', name: 'All' },
  { id: '1', name: '30 Character' },
  { id: '2', name: 'Approved Tepmlate' },
  { id: '3', name: 'Through Link' },
];

// interface VoiceMessageInfoCreateRequest extends VoiceCreateRequest {
//   voiceId?: number;
// }

const DefaultVoiceMessageInfo: VoiceCreateRequest = {
  voiceId: 0,
  voiceTitle: '',
  voiceFile: '',
  // createdDate: '',
  templateId: '',
  voiceStatus: 1,
  adminId: 0,
};
export type MessageTempDeleteType = {
  voiceId: number;
  adminId: number;
};
interface DeleteMultipleVoiceMessageWithStatus extends DeleteMultipleVoiceMessageType {
  deleteStatus: 'Success' | 'Failed' | '' | undefined;
  details?: string;
}

function TemplateVoiceMessage() {
  const theme = useTheme();
  const isLight = useSettings().themeMode === 'light';
  const dispatch = useAppDispatch();
  const { confirm } = useConfirm();
  const [content, setContent] = React.useState('');
  const [option, setOption] = React.useState('');
  const [popupView, setPopupView] = React.useState(false);
  const [popupSuccess, setPopupSuccess] = React.useState(false);
  const [popupSuccessSend, setPopupSuccessSend] = React.useState(false);
  const [drawerOpen, setDrawerOpen] = React.useState<boolean>(false);
  const [showFilter, setShowFilter] = useState(false);
  const [voiceMessage, setVoiceMessage] = useState<VoiceCreateRequest>(DefaultVoiceMessageInfo);
  const [voiceTitleFilter, setVoiceTitleFilter] = useState('');
  const [voiceDateFilter, setVoiceDateFilter] = useState<Dayjs | string>('');
  const [voiceStatusFilter, setVoiceStatusFilter] = useState<string | number>('-1');
  const isSubmitting = useAppSelector(getVoiceSubmitting);
  const VoiceMessageListData = useAppSelector(getVoiceData);
  const [voiceMessageListDatas, setVoiceMessageListDatas] = useState<VoiceDataType[]>([]);
  const VoiceStatus = useAppSelector(getVoiceStatus);
  const [individualSendLoadingMap, setIndividualSendLoadingMap] = useState<Record<number, string>>({});
  const [successResults, setSuccessResults] = useState<Record<string, string>>({});
  const [errorTooltip, setErrorTooltip] = useState<{ [key: string]: React.ReactNode }>({});
  const { user } = useAuth();
  const adminId: number | undefined = user?.accountId;
  const [selectedCards, setSelectedCards] = useState<any[]>([]);
  // const [loadRefresh, setLoadRefresh] = useState(true);

  const setButtonLoadingStatus = (cardId: number, buttonName: string, status: string | null): void => {
    setIndividualSendLoadingMap((prevState: any) => ({
      ...prevState,
      [cardId]: {
        ...prevState[cardId],
        [buttonName]: status,
      },
    }));
  };

  // const getButtonLoadingStatus = (cardId: number, buttonId: number) => {
  //   return individualSendLoadingMap[cardId]?.[buttonId] || null;
  //   console.log(individualSendLoadingMap[cardId]?.[buttonId] || 'not found');
  // };
  // const isSelected = (row: D) => selectedCards?.indexOf(row) !== -1;

  const handleCheckboxChange = (voiceId: any) => {
    const selectedIndex: number = selectedCards?.indexOf(voiceId);
    let newSelected: DeleteMultipleVoiceMessageType[] = [];

    if (selectedIndex === -1) {
      newSelected = newSelected.concat(selectedCards, voiceId);
    } else if (selectedIndex === 0) {
      newSelected = newSelected.concat(selectedCards.slice(1));
    } else if (selectedIndex === selectedCards.length - 1) {
      newSelected = newSelected.concat(selectedCards.slice(0, -1));
    } else if (selectedIndex > 0) {
      newSelected = newSelected.concat(selectedCards.slice(0, selectedIndex), selectedCards.slice(selectedIndex + 1));
    }
    console.log('newSelected', newSelected);

    setSelectedCards(newSelected);
  };

  const sendbuttons2: AllSendButtonsType[] = [
    { id: 1, name: 'All Parents', action: voiceMessageSendToAllParents },
    { id: 2, name: 'All Staffs', action: voiceMessageSendToAllStaff },
    { id: 3, name: 'All PTA', action: voiceMessageSendToAllPta },
    { id: 4, name: 'All Conveyors', action: voiceMessageSendToAllConveyors },
  ];

  const initialVoiceMessageRequest = React.useMemo(
    () => ({
      voiceTitle: '',
      createdDate: '',
      voiceStatus: '-1',
      adminId: adminId,
      accademicId: 10,
    }),
    [adminId]
  );
  const currentVoiceMessageRequest = React.useMemo(
    () => ({
      voiceTitle: voiceTitleFilter,
      createdDate: voiceDateFilter,
      voiceStatus: voiceStatusFilter,
      adminId: adminId,
      accademicId: 10,
    }),
    [voiceTitleFilter, voiceStatusFilter, voiceDateFilter, adminId]
  );

  const toggleDrawerClose = useCallback(() => setDrawerOpen(false), []);

  const handleClick = useCallback((item: SendButtonType, row: VoiceCreateRequest) => {
    setVoiceMessage(row);
    setPopupView(true);
    setOption(item.name);
    setContent(item.component);
  }, []);

  const handleClickClose = () => setPopupView(false);
  // const handleOpenPopup = () => setPopupSuccess(true);
  const handleClosePopup = () => setPopupSuccess(false);

  // const handleOpenPopupSend = () => setPopupSuccessSend(true);
  const handleClosePopupSend = () => setPopupSuccessSend(false);

  const handleCreateMessage = () => {
    setVoiceMessage(DefaultVoiceMessageInfo);
    setDrawerOpen(true);
  };

  const loadVoiceMessageList = useCallback(
    (request: VoiceMessageFilter) => {
      dispatch(fetchVoiceMessageList(request));
      setSelectedCards([]);
    },
    [dispatch]
  );

  React.useEffect(() => {
    if (VoiceStatus === 'idle') {
      dispatch(fetchVoiceMessageList(currentVoiceMessageRequest));
      // setVoiceMessageData(dispatch(fetchMessageTempList(currentVoiceMessageRequest)));
      // loadVoiceMessageList(currentVoiceMessageRequest);
    }
    setVoiceMessageListDatas(VoiceMessageListData);
    console.log('messageTemplate', VoiceMessageListData);
  }, [dispatch, VoiceStatus, VoiceMessageListData, currentVoiceMessageRequest]);

  const handleStatusChange = (e: SelectChangeEvent) => {
    // const typeVal = parseInt(e.target.value, 10);
    setVoiceStatusFilter(STATUS_OPTIONS.filter((item) => item.id === e.target.value)[0].id);
    loadVoiceMessageList({ ...currentVoiceMessageRequest, voiceStatus: parseInt(e.target.value, 10) });
  };

  const handleReset = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      setVoiceTitleFilter('');
      setVoiceStatusFilter('-1');
      setVoiceDateFilter('');
      loadVoiceMessageList(initialVoiceMessageRequest);
    },
    [initialVoiceMessageRequest, loadVoiceMessageList]
  );

  const handleEditMessage = useCallback((row: VoiceCreateRequest) => {
    setVoiceMessage(row);
    setDrawerOpen(true);
  }, []);

  const showConfirmation = useCallback(
    async (successMessage: ReactElement, title: string) => {
      const confirmation = confirm(successMessage, title, {
        okLabel: 'Ok',
        showOnlyOk: true,
      });

      return confirmation;
    },
    [confirm]
  );
  // const CreateEditMode = messageTemplate.voiceId === 0 ? 'create' : 'edit';

  const handleSaveorEdit = useCallback(
    async (value: VoiceCreateRequest, mode: 'create' | 'edit') => {
      try {
        if (mode === 'create') {
          const { ...rest } = value;
          const response = await dispatch(createVoice(rest)).unwrap();

          loadVoiceMessageList({ ...currentVoiceMessageRequest });

          if (response) {
            setDrawerOpen(false);
            await showConfirmation(<SuccessMessage icon={SaveFile} message="Message created successfully" />, '');
          }
          // const successMessage = <SuccessMessage icon={AddFile} message="Message created successfully" />;
          // await confirm(successMessage, 'Message Created', { okLabel: 'Ok', showOnlyOk: true });
        } else {
          // await showConfirmation(
          //   <SuccessMessage icon={SaveFile} message="Message created successfully" />,
          //   'Message Created'
          // );
          // const response = await dispatch(EditMessageTemplate(value)).unwrap();
          // setDrawerOpen(false);
          // loadVoiceMessageList(currentVoiceMessageRequest);
          // if (response.rowsAffected === 0) {
          //   await showConfirmation(<SuccessMessage icon={SaveFile} message="Message updated successfully" />, '');
          //   // const successMessage = <SuccessMessage icon={AddFile} message="Message updated successfully" />;
          //   // await confirm(successMessage, 'Message Updated', { okLabel: 'Ok', showOnlyOk: true });
          //   // loadVoiceMessageList(currentVoiceMessageRequest);
          // }
        }
      } catch (error) {
        // Handle errors here
        setDrawerOpen(false);
        await showConfirmation(<ErrorMessage icon={ErrorMsgTemplate} message="Message content already created" />, '');
        handleEditMessage(value);
        setDrawerOpen(true);
        console.error(error);
      }
    },
    [currentVoiceMessageRequest, showConfirmation, dispatch, loadVoiceMessageList, handleEditMessage]
  );
  const handleDeleteMessage = useCallback(
    async (messageobj: VoiceDataType) => {
      const sendConfirmMessage = (
        <DeleteMessage
          icon={RemoveFile}
          message={
            <div>
              Are you sure you want to delete the template <br />
              &quot;{messageobj.voiceTitle}&quot; ?
            </div>
          }
        />
      );
      if (await confirm(sendConfirmMessage, 'Delete Template?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
        const id = messageobj.voiceId;
        const deleteResponse = await dispatch(DeleteVoiceMessageList({ adminId, voiceId: id })).unwrap();
        console.log('deleteResponse', deleteResponse);
        if (deleteResponse.deleted) {
          const deleteDoneMessage = <DeleteMessage icon={DeleteFile} message="Template deleted successfully." />;
          await confirm(deleteDoneMessage, 'Deleted', { okLabel: 'Ok', showOnlyOk: true });

          setVoiceMessageListDatas((prevVoiceMessageListDatas) =>
            prevVoiceMessageListDatas.filter((item) => item.voiceId !== messageobj.voiceId)
          );
        }
        //  else {
        //   const deleteErrorMessage = <DeleteMessage message="Template not deleted." />;
        //   await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
        // }
      }
    },
    [confirm, dispatch, adminId]
  );

  const handleDeleteMultiple = useCallback(async () => {
    const sendConfirmMessage = (
      <DeleteMessage icon={RemoveFile} message={<div>Are you sure you want to delete the template ?</div>} />
    );
    if (await confirm(sendConfirmMessage, 'Delete Template?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
      const sendRequests: DeleteMultipleVoiceMessageType[] = await Promise.all(
        selectedCards?.map(async (voiceId) => {
          const sendReq = { voiceId, adminId };
          return sendReq;
        }) || []
      );
      console.log('sendRequests', sendRequests);

      const deleteResponse = await dispatch(DeleteMultipleVoiceMessageList(sendRequests));
      if (deleteResponse && Array.isArray(deleteResponse.payload)) {
        const results: DeleteMultipleVoiceMessageWithStatus[] = deleteResponse.payload;
        const errorMessages = results.find((result) => result.deleteStatus === 'Failed');
        const successMessages = results.find((result) => result.deleteStatus === 'Success');
        const details = results.find((result) => result.details);
        console.log('details', details);
        // Reload only the specific message template with the deleted voiceId

        if (!errorMessages) {
          const deleteSuccessMessage = <SuccessMessage icon={DeleteFile} message={`${details?.details}`} />;
          await confirm(deleteSuccessMessage, 'Success', { okLabel: 'Ok', showOnlyOk: true });
        } else if (!successMessages) {
          const deleteErrorMessage = <DeleteMessage icon={DeleteFile} message={`${details?.details}`} />;
          await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
        } else {
          const deleteErrorMessage = <DeleteMessage icon={DeleteFile} message={`${details?.details}`} />;
          await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
        }
        // setSelectedCards((prevSelectedCards) =>
        //   prevSelectedCards.filter((card) => card.voiceId !== errorMessages?.voiceId)
        // );
        // Filter out the deleted cards from VoiceMessageListDatas
        setVoiceMessageListDatas((prevVoiceMessageListDatas) =>
          prevVoiceMessageListDatas.filter((item: any) => !selectedCards.includes(item.voiceId))
        );
        // setLoadRefresh(false);
        // loadVoiceMessageList(currentVoiceMessageRequest);

        setSelectedCards([]);
      }
    }
  }, [confirm, dispatch, selectedCards, adminId]);

  const handleSendVoiceMessageAll = useCallback(
    async (row: AllSendButtonsType, card: VoiceDataType) => {
      const sendConfirmMessage = (
        <LoadingMessage
          icon={isSubmitting ? LoadingMsg : messageIcon}
          message={
            <div>
              Are you sure you want to send to <br />
              <span style={{ color: theme.palette.primary.main }}>&quot;{row.name}&quot; ?</span>
            </div>
          }
        />
      );
      if (await confirm(sendConfirmMessage, 'Send Message?', { okLabel: 'Send', cancelLabel: 'Cancel' })) {
        const sendResponse = await dispatch(
          row.action({ adminId: adminId, academicId: 10, voiceId: card.voiceId })
        ).unwrap();
        setButtonLoadingStatus(card.voiceId, row.name, sendResponse.result);
        // setSuccessResult(sendResponse.result);
        console.log(sendResponse.result);

        if (sendResponse.result === 'Success') {
          const sendDoneMessage = <LoadingMessage icon={SuccessMsg} message="Message Sent successfully." />;
          await confirm(sendDoneMessage, '', { okLabel: 'Ok', showOnlyOk: true });

          const buttonKey = `${card.voiceId}_${row.id}`;
          setSuccessResults((prevResults) => ({
            ...prevResults,
            [buttonKey]: 'Success',
          }));
          setErrorTooltip((prevTooltip) => ({
            ...prevTooltip,
            [`${card.voiceId}_${row.id}`]: (
              <div>
                &quot;{row.name}&quot;&nbsp;
                <span style={{ color: theme.palette.success.main }}>Messages sent successfully.</span>
              </div>
            ),
          }));
        } else if (sendResponse.result === 'Failed') {
          const sendDoneMessage = (
            <LoadingMessage
              icon={ErrorMsg}
              message="No messages sent to any Members, Please check the numbers and Try again."
            />
          );
          await confirm(sendDoneMessage, '', { okLabel: 'Ok', showOnlyOk: true });
          const buttonKey = `${card.voiceId}_${row.id}`;
          setSuccessResults((prevResults) => ({
            ...prevResults,
            [buttonKey]: 'Failed',
          }));
          setErrorTooltip((prevTooltip) => ({
            ...prevTooltip,
            [`${card.voiceId}_${row.id}`]: (
              <div>
                &quot;{row.name}&quot;&nbsp;
                <span style={{ color: theme.palette.error.main }}>
                  No messages sent to any Members, Please check the numbers and Try again.
                </span>
              </div>
            ),
          }));
        } else {
          const sendDoneMessage = <LoadingMessage icon={ErrorMsg1} message="Something Went Wrong Please try later." />;
          await confirm(sendDoneMessage, '', { okLabel: 'Ok', showOnlyOk: true });
          const buttonKey = `${card.voiceId}_${row.id}`;
          setSuccessResults((prevResults) => ({
            ...prevResults,
            [buttonKey]: 'Error',
          }));
          setErrorTooltip((prevTooltip) => ({
            ...prevTooltip,
            [`${card.voiceId}_${row.id}`]: (
              <div>
                &quot;{row.name}&quot;&nbsp;
                <span style={{ color: theme.palette.error.main }}>Something Went Wrong Please try later.</span>
              </div>
            ),
          }));
        }
        console.log('individualSendLoadingMap', individualSendLoadingMap);
      }
    },
    [dispatch, confirm, adminId, individualSendLoadingMap, isSubmitting, theme]
  );
  const [openForms, setOpenForms] = useState<{ [key: string]: boolean }>({});

  const handleToggle = (voiceId: number) => {
    setOpenForms((prev) => ({
      ...prev,
      [voiceId]: !prev[voiceId], // Toggle only the clicked card
    }));
  };
  const [voiceTitle, setVoiceTitle] = useState('');
  const [status, setStatus] = useState('');
  const handleSave = (voiceId) => {
    console.log('Saving Voice Template:', { voiceId, voiceTitle, status });
    // Add API call or state update logic here
  };

  return (
    <Page title="SMS Template">
      <TemplateVoiceMessageRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
            flexWrap="wrap"
            className="top-head"
          >
            <Typography variant="h6" fontSize={17}>
              Voice Template List
            </Typography>
            <Box
              pb={1}
              sx={{ flexShrink: 0 }}
              display="flex"
              alignItems="center"
              justifyContent="end"
              flex="1"
              // flexDirection={{ xs: 'column', sm: 'row' }}
            >
              {selectedCards.length > 0 && (
                <Tooltip title="Delete">
                  <IconButton
                    sx={{ visibility: { xs: 'hidden', sm: 'visible' } }}
                    aria-label="delete"
                    color="error"
                    onClick={handleDeleteMultiple}
                  >
                    <MdDelete />
                    {/* <Lottie animationData={allDelete} loop={false} style={{ width: '40px', height: '40px' }} /> */}
                  </IconButton>
                </Tooltip>
              )}
              <Tooltip title="Refresh">
                <IconButton
                  aria-label="delete"
                  color="primary"
                  sx={{ mr: { xs: 0, sm: 0 } }}
                  onClick={() => loadVoiceMessageList(currentVoiceMessageRequest)}
                >
                  <CachedIcon />
                </IconButton>
              </Tooltip>

              {showFilter === false ? (
                <Tooltip title="Search">
                  <IconButton
                    aria-label="delete"
                    color="primary"
                    sx={{ mr: { xs: 0, sm: 1 } }}
                    onClick={() => setShowFilter((x) => !x)}
                  >
                    <SearchIcon />
                  </IconButton>
                </Tooltip>
              ) : (
                <IconButton
                  aria-label="delete"
                  color="primary"
                  sx={{ mr: { xs: 0, sm: 1 } }}
                  onClick={() => setShowFilter((x) => !x)}
                >
                  <IoIosArrowUp />
                </IconButton>
              )}

              <Button sx={{ borderRadius: '20px' }} variant="contained" size="small" onClick={handleCreateMessage}>
                <MdAdd size="20px" />
                Create
              </Button>
            </Box>
          </Stack>
          <Divider sx={{ marginBottom: 1 }} />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form
                noValidate
                onReset={handleReset}
                style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}
              >
                <Grid pb={4} container spacing={2} alignItems="end">
                  <Grid item lg="auto" md={6} sm={6} xs={12}>
                    <FormControl fullWidth sx={{ minWidth: { xs: '100%', sm: 190 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Title
                      </Typography>
                      <TextField
                        placeholder="Enter Name"
                        value={voiceTitleFilter}
                        onChange={(e) => {
                          setVoiceTitleFilter(e.target.value);
                          loadVoiceMessageList({ ...currentVoiceMessageRequest, voiceTitle: e.target.value });
                        }}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" md={6} sm={6} xs={12}>
                    <FormControl fullWidth sx={{ width: { xs: '100%', lg: 180 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Date
                      </Typography>
                      <DatePickers
                        name="notificationDateFilter"
                        value={dayjs(voiceDateFilter, 'DD/MM/YYYY')}
                        onChange={(e) => {
                          const formattedDate = e ? e.format('DD/MM/YYYY') : '';
                          setVoiceDateFilter(formattedDate);
                          loadVoiceMessageList({ ...currentVoiceMessageRequest, createdDate: formattedDate });
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item lg="auto" md={6} sm={6} xs={12}>
                    <FormControl fullWidth sx={{ minWidth: { xs: '100%', sm: 180 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Status
                      </Typography>
                      <Select
                        labelId="voiceStatusFilter"
                        id="voiceStatusFilter"
                        value={voiceStatusFilter?.toString() || '-1'}
                        onChange={handleStatusChange}
                      >
                        {STATUS_OPTIONS.map((opt) => (
                          <MenuItem key={opt.id} value={opt.id}>
                            {opt.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto">
                    <Stack direction="row" sx={{ marginBottom: '2px' }}>
                      <Button type="reset" variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>
            <Box className="card-container" pb={3.5}>
              {/* Show Create Template Form When Button is Clicked */}

              <Grid container spacing={5}>
                {VoiceStatus === 'loading' ? (
                  [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12].map((index) => (
                    <Grid item xl={6} lg={12} sm={12} xs={12} key={index}>
                      <Card className="Skelton-ListCard" sx={{ boxShadow: 0, py: 3, px: 2, border: '1px solid light' }}>
                        <Box display="flex" alignItems={{ xs: 'start', sm: 'center' }}>
                          <Stack sx={{ flexDirection: { xs: 'column', sm: 'row' }, flex: 1 }}>
                            <Stack direction="row" gap={2.5} alignItems="center" flex={1} sx={{ ml: { xs: 2 }, mr: 1 }}>
                              <Skeleton variant="rounded" width={20} height={20} />
                              <Skeleton variant="rounded" width={180} height={15} />
                            </Stack>
                            <Stack
                              direction="row"
                              alignItems="center"
                              sx={{ ml: { xs: 1.5 }, mr: 1.5, my: { xs: 1, sm: 0 } }}
                            >
                              <Skeleton variant="rounded" sx={{ borderRadius: 50 }} width={150} height={25} />
                            </Stack>
                          </Stack>
                          <Stack mr={0.5}>
                            <Skeleton variant="circular" width={25} height={25} />
                          </Stack>
                        </Box>
                        <Box mt={1} px={2}>
                          <Skeleton variant="rounded" width="100%" height={45} />
                          <Stack direction="row" spacing={2} mt={2} mb={2.6}>
                            <Skeleton variant="rounded" width={200} height={15} />

                            <Skeleton variant="rounded" width={200} height={15} />
                          </Stack>
                          <Stack mb={2}>
                            <Skeleton variant="rounded" width={80} height={15} />
                          </Stack>
                          <Stack direction="row" spacing={2} mb={2}>
                            {[1, 2, 3, 4, 5].map(() => (
                              <Skeleton variant="rounded" width={100} height={25} />
                            ))}
                          </Stack>
                          <Stack direction="row" spacing={2} mb={2}>
                            {[1, 2, 3, 4, 5].map(() => (
                              <Skeleton variant="rounded" width={100} height={25} />
                            ))}
                          </Stack>
                          <Stack direction="row" spacing={2} mb={1}>
                            {[1, 2, 3, 4, 5].map(() => (
                              <Skeleton variant="rounded" width={100} height={25} />
                            ))}
                          </Stack>
                        </Box>
                      </Card>
                    </Grid>
                  ))
                ) : voiceMessageListDatas.length !== 0 ? (
                  voiceMessageListDatas.map((card: VoiceDataType, index) => (
                    <Grid key={card.voiceId} item xl={4} lg={12} sm={12} xs={12} overflow="hidden">
                      <Card sx={{ height: 500, backgroundColor: theme.palette.grey[100], p: 2 }}>
                        {index < 3 && (
                          <Box display="flex" justifyContent="center" mb={2}>
                            <Button
                              fullWidth
                              variant="outlined"
                              color="secondary"
                              onClick={() => handleToggle(card.voiceId)}
                              sx={{
                                backgroundColor: theme.palette.common.white, // Optional hover effect
                                border: `2px dashed ${theme.palette.grey[300]}`, // Dashed bottom border
                                '&:hover': {
                                  border: `2px dashed ${theme.palette.grey[400]}`, // Darker border on hover
                                  backgroundColor: theme.palette.grey[300], // Optional hover effect
                                  color: theme.palette.grey[700], // Optional hover effect
                                },
                              }}
                              //   startIcon={openForms[card.voiceId] === 'Create Template' ? <MdAdd /> : ''}
                            >
                              {openForms[card.voiceId] ? 'Cancel' : '+ Create Voice Template'}
                            </Button>
                          </Box>
                        )}

                        {/* Expandable Form for Each Card */}
                        <Collapse in={openForms[card.voiceId]} timeout="auto" unmountOnExit>
                          <Card sx={{ mb: 2, p: 2 }}>
                            <CreateEditNotificationForm
                              form="card"
                              adminId={adminId}
                              isSubmitting={isSubmitting}
                              voiceMessageDetails={voiceMessage}
                              onSave={handleSaveorEdit}
                              onCancel={toggleDrawerClose}
                            />
                          </Card>
                        </Collapse>

                        <Card sx={{ p: 2, boxShadow: 0 }} key={card.voiceId} className="ListCard">
                          <Box display="flex" alignItems={{ xs: 'start', sm: 'center' }}>
                            <Stack sx={{ flexDirection: { xs: 'column', sm: 'row' }, flex: 1 }}>
                              <Stack direction="row" alignItems="center" flex={1} sx={{ ml: { xs: 0.5 }, mr: 1 }}>
                                <Tooltip
                                  placement="top"
                                  title={
                                    <span style={{ color: theme.palette.common.black }}>
                                      Message Id : {card.voiceId}
                                    </span>
                                  }
                                >
                                  <Checkbox
                                    sx={{ mr: 1 }}
                                    onChange={() => handleCheckboxChange(card.voiceId)}
                                    checked={selectedCards.includes(card.voiceId)}
                                  />
                                </Tooltip>
                                <Tooltip placement="top" title={card.voiceTitle}>
                                  <Typography
                                    variant="h6"
                                    fontSize={16}
                                    sx={{
                                      maxWidth: { xs: 130, sm: 175 },
                                      textOverflow: 'ellipsis',
                                      whiteSpace: 'nowrap',
                                      overflow: 'hidden',
                                    }}
                                  >
                                    {card.voiceTitle}
                                  </Typography>
                                </Tooltip>
                              </Stack>
                              <Stack
                                direction="row"
                                alignItems="center"
                                sx={{ ml: { xs: 1.5 }, mr: 1, my: { xs: 1, sm: 0 } }}
                              >
                                {/* <Chip
                                size="small"
                                label={`Type : ${
                                  card.messageType === 1
                                    ? '30 Character'
                                    : card.messageType === 2
                                    ? 'Approved Template'
                                    : card.messageType === 3
                                    ? 'Through Link'
                                    : 'Unknown'
                                }`}
                                variant="filled"
                                sx={{
                                  backgroundColor: theme.palette.chart.violet[4],
                                  color: theme.palette.common.black,
                                  fontWeight: 'bold',
                                }}
                                // color="info"
                              /> */}
                              </Stack>
                            </Stack>
                            <Stack>
                              <MenuEditDelete
                                Delete={() => handleDeleteMessage(card)}
                                Edit={() => handleEditMessage(card)}
                              />
                            </Stack>
                          </Box>
                          <Box mt={1} px={2}>
                            <Box flexGrow={1} sx={{ minHeight: '35px', overflow: 'hidden' }}>
                              {/* <Tooltip placement="bottom" title={card.messageContent}>
                              <Typography
                                variant="subtitle2"
                                fontSize={13}
                                sx={{
                                  color: theme.palette.grey[600],
                                  display: '-webkit-box',
                                  WebkitBoxOrient: 'vertical',
                                  WebkitLineClamp: 2, // Set the number of lines to display
                                  maxWidth: '90%',
                                  textOverflow: 'ellipsis',
                                }}
                              >
                                {card.messageContent}
                              </Typography>
                            </Tooltip> */}
                              <AudioPlayer
                                muted
                                style={{
                                  borderRadius: 10,
                                  backgroundColor: isLight ? theme.palette.grey[100] : theme.palette.grey[800],
                                }}
                                src={`http://demo.passdaily.in/Voice Mail/${card.voiceFile}`}
                                onPlay={(e) => console.log('onPlay')}
                                // other props here
                              />
                              {/* <ReactAudioPlayer src="my_audio_file.ogg" autoPlay controls /> */}
                              {/* <MusicPlayerSlider /> */}
                            </Box>
                            <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} my={2}>
                              <Stack direction="row" spacing={1} flexWrap="wrap">
                                <Typography variant="body2" fontSize={13} sx={{ color: theme.palette.grey[600] }}>
                                  Composed Date :
                                </Typography>
                                <Typography variant="subtitle2" fontSize={13}>
                                  {dayjs(card.createdDate).format('DD/MM/YYYY')}
                                  {/* {card.createdDate.split('T')[0]} */}
                                </Typography>
                              </Stack>
                              <Stack direction="row" spacing={1} flexWrap="wrap">
                                {/* <Typography variant="body2" fontSize={13} sx={{ color: theme.palette.grey[600] }}>
                                Created By :
                              </Typography> */}
                                {/* <Typography variant="subtitle2" fontSize={13}>
                                {card.createdBy}
                              </Typography> */}
                              </Stack>
                            </Stack>
                            <Typography variant="h6" fontSize={14} pb={1}>
                              Send To:
                            </Typography>
                            {sendbuttons.map((item) => (
                              <Button
                                key={item.id}
                                sx={{ mr: 1, mb: 1.5, p: 0.5, fontWeight: 600, fontSize: 12 }}
                                size="small"
                                variant="outlined"
                                color="primary"
                                onClick={() => handleClick(item, card)}
                              >
                                {item.name}
                              </Button>
                            ))}
                            {sendbuttons2.map((item) => (
                              <Tooltip key={item.id} title={errorTooltip[`${card.voiceId}_${item.id}`]}>
                                <LoadingButton
                                  sx={{ mr: 1, mb: 1.5, p: 0.5, fontWeight: 600, fontSize: 12 }}
                                  size="small"
                                  variant={
                                    successResults[`${card.voiceId}_${item.id}`] === 'Error' ? 'contained' : 'outlined'
                                  }
                                  color={
                                    successResults[`${card.voiceId}_${item.id}`] === 'Error' ||
                                    successResults[`${card.voiceId}_${item.id}`] === 'Failed'
                                      ? 'error'
                                      : successResults[`${card.voiceId}_${item.id}`] === 'Success'
                                      ? 'success'
                                      : 'primary'
                                  }
                                  onClick={() =>
                                    successResults[`${card.voiceId}_${item.id}`] !== 'Success'
                                      ? handleSendVoiceMessageAll(item, card)
                                      : null
                                  }
                                  // disabled={
                                  //   successResults[`${card.voiceId}_${item.id}`] === 'Success'
                                  //   // successResults[`${card.voiceId}_${item.id}`] === 'Failed'
                                  // }
                                >
                                  {successResults[`${card.voiceId}_${item.id}`] === 'Success' ? (
                                    <Lottie animationData={successIcon} loop={false} style={{ width: '22px' }} />
                                  ) : successResults[`${card.voiceId}_${item.id}`] === 'Failed' ? (
                                    <Lottie animationData={errorIcon} loop={false} style={{ width: '25px' }} />
                                  ) : successResults[`${card.voiceId}_${item.id}`] === 'Error' ? (
                                    'Retry'
                                  ) : (
                                    item.name
                                  )}
                                </LoadingButton>
                              </Tooltip>
                            ))}
                          </Box>
                        </Card>
                      </Card>
                    </Grid>
                  ))
                ) : (
                  <Box
                    display="flex"
                    alignItems="center"
                    justifyContent="center"
                    width="100%"
                    sx={{ pl: { xs: 5 }, pt: { xs: 2, md: 3 } }}
                    height={{ xs: 'calc(100vh - 240px)', sm: 'calc(100vh - 240px)' }}
                    flexDirection="column"
                  >
                    <img width={150} height={150} src={emptyMsg} alt="" />
                    <Typography variant="subtitle2" mt={2} color="GrayText">
                      No data found !
                    </Typography>
                  </Box>
                )}
              </Grid>
            </Box>
          </div>
        </Card>
      </TemplateVoiceMessageRoot>
      {selectedCards.length > 0 && (
        <Tooltip title="Delete">
          <IconButton
            sx={{
              visibility: { xs: 'visible', sm: 'hidden' },
              position: 'fixed',
              bottom: 15,
              right: 15,
              // border: '1px solid #000',
              boxShadow: 10,
              backgroundColor: isLight ? theme.palette.common.white : theme.palette.common.white,
            }}
            aria-label="delete"
            color="error"
            // sx={{ mr: { xs: 0, sm: 1 } }}
            onClick={handleDeleteMultiple}
          >
            <MdDelete />
            {/* <Lottie animationData={allDelete} loop={false} style={{ width: '40px', height: '40px' }} /> */}
          </IconButton>
        </Tooltip>
      )}
      {/* {isSubmitting ? (
        CreateEditMode === 'create' ? (
          <LoadingPopup
            // title="Message Creating"
            popupContent={<LoadingMessage icon={AddFile} message="Message template creating Please Wait..." />}
          />
        ) : CreateEditMode === 'edit' ? (
          ''
        ) : // <LoadingPopup
        //   // title="Message Creating"
        //   popupContent={<LoadingMessage icon={EditFile} message="Message template updating Please Wait..." />}
        // />
        null
      ) : null} */}

      <TemporaryDrawer
        onClose={toggleDrawerClose}
        Title={voiceMessage === DefaultVoiceMessageInfo ? 'Create Voice Template' : 'Edit Voice Template'}
        state={drawerOpen}
        DrawerContent={
          <CreateEditNotificationForm
            adminId={adminId}
            isSubmitting={isSubmitting}
            voiceMessageDetails={voiceMessage}
            onSave={handleSaveorEdit}
            onCancel={toggleDrawerClose}
          />
        }
      />
      <Popup
        size="xs"
        state={popupSuccessSend}
        onClose={handleClosePopupSend}
        popupContent={<SuccessMessage message="File Send Successfully" />}
      />
      <Popup
        size="xl"
        title={`Send to ${option}:`}
        state={popupView}
        onClose={handleClickClose}
        popupContent={
          <SendMessage
            isSubmitting={isSubmitting}
            content={content}
            templateId={voiceMessage.templateId}
            voiceId={voiceMessage.voiceId}
          />
        }
      />
      {/* -------------Edit------------- */}

      <Popup
        size="xs"
        state={popupSuccess}
        onClose={handleClosePopup}
        popupContent={<SuccessMessage message="File Save Successfully" />}
      />
    </Page>
  );
}

export default TemplateVoiceMessage;
