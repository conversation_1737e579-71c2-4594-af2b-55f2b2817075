/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import { Autocomplete, Grid, Stack, TextField, Button, Typography, Box, FormControl } from '@mui/material';
import styled from 'styled-components';
import { CLASS_SELECT, STATUS_SELECT, SUBJECT_SELECT, YEAR_SELECT } from '@/config/Selection';
import SelectDateTimePicker from '@/components/shared/Selections/TimePicker';
import dayjs from 'dayjs';

const CreateVehicleRoot = styled.div`
  width: 100%;
  padding: 1.5rem;
`;

function CreateVehicle({ onClose }: any) {
  return (
    <CreateVehicleRoot>
      <form noValidate>
        <Grid container spacing={3}>
          <Grid item md={6} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14}>
                Vehicle Reg.No
              </Typography>
              <TextField placeholder="Enter number " />
            </FormControl>
          </Grid>
          <Grid item md={6} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14}>
                Vehicle Name
              </Typography>
              <TextField placeholder="Enter name " />
            </FormControl>
          </Grid>
          <Grid item md={6} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14}>
                Vehicle Root
              </Typography>
              <TextField placeholder="Enter root " />
            </FormControl>
          </Grid>
          <Grid item md={6} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14}>
                Driver Name
              </Typography>
              <TextField placeholder="Enter name " />
            </FormControl>
          </Grid>
          <Grid item md={6} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14}>
                Driver Number
              </Typography>
              <TextField placeholder="Enter number " />
            </FormControl>
          </Grid>
          <Grid item md={6} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14}>
                Terminal ID
              </Typography>
              <TextField placeholder="Enter id" />
            </FormControl>
          </Grid>
          <Grid item md={6} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14}>
                SIM Number
              </Typography>
              <TextField placeholder="Enter number" />
            </FormControl>
          </Grid>
          <Grid item md={6} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14}>
                EXE Name
              </Typography>
              <TextField placeholder="Enter name" />
            </FormControl>
          </Grid>
          <Grid item md={6} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14}>
                IP Address
              </Typography>
              <TextField placeholder="Enter exe" />
            </FormControl>
          </Grid>
          <Grid item md={6} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14}>
                Port Number
              </Typography>
              <TextField placeholder="Enter port number" />
            </FormControl>
          </Grid>

          <Grid item md={12} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14}>
                Vehicle Status
              </Typography>
              <Autocomplete
                options={STATUS_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select status" />}
              />
            </FormControl>
          </Grid>
        </Grid>
        <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 5 }}>
          <Stack spacing={2} direction="row">
            <Button variant="contained" color="secondary">
              Cancel
            </Button>
            <Button variant="contained" color="primary">
              Submit
            </Button>
          </Stack>
        </Box>
      </form>
    </CreateVehicleRoot>
  );
}

export default CreateVehicle;
