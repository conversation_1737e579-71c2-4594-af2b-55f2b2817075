import { Dayjs } from 'dayjs';
import { FetchStatus } from './Common';

// SMS Template //
export type MessageTempRequest = {
  messageTitle: string;
  messageDate: Dayjs | string | null;
  messageType: string | number;
};

export type MessageTempDataType = {
  messageId: number;
  messageTitle: string;
  messageContent: string;
  messageDate: string;
  messageCreatedBy: string | number | undefined;
  messageStatus: number;
  messageTemplateId: string;
  messageType: number;
};

export type MessageTempCreateRequest = Omit<MessageTempDataType, 'messageCreatedBy'>;

export type MessageTypeOptions = {
  id: number;
  name: string;
};

// Parents //
export type ParentsDataType = {
  studentId: number;
  admissionNo: string;
  studentRollNo: number;
  studentName: string;
  guardianName: string;
  guardianNumber: string;
  className: string;
  classId: number;
  accademicTime: string;
  sendStatus?: 'Success' | 'Failed' | '' | undefined;
};
export type SendToParentsRequest = {
  messageId: number;
  studentId: number;
  studentRollNo: number;
  studentName: string;
  guardianName: string;
  guardianNumber: string;
  className: string;
  classId: number;
  accademicTime: string;
  adminId: number;
  sendStatus?: 'Success' | 'Failed' | '' | undefined;
};
export type SendToParentsResponse = SendToParentsRequest & { sendStatus: 'Success' | 'Failed' };
// Staff //
export type StaffDataListRequest = {
  staffCode: string;
  staffName: string;
  staffPhoneNumber: string;
  accademicId: number;
  jobRole: string;
  gender: string;
  category: number;
};
export type StaffDataType = {
  staffId: number;
  staffCode: string;
  staffName: string;
  staffGender: string;
  staffJobRole: string;
  staffPhoneNumber: string;
  staffImage: string;
  stafftatus: number;
};
export type SendToStaffType = {
  messageId: number;
  staffId: number;
  staffName: string;
  staffPhoneNumber: string;
  adminId: number;
};

// PTA //
export type PtaDataListRequest = {
  ptaMemberName: string;
  ptaMemberRole: string;
  ptaMemberMobile: string;
  ptaMemberEmail: string;
  ptaMemberStatus: string;
};
export type PtaDataType = {
  ptaMemberId: number;
  ptaMemberName: string;
  ptaMemberRole: number;
  ptaMemberMobile: string;
  ptaMemberAddress: string;
  ptaMemberEmail: string;
  ptaMemberImage: string;
  ptaMemberStatus: number;
};
export type SendToPtaType = {
  messageId: number;
  ptaMemberName: string;
  ptaMemberMobile: string;
  adminId: number;
  sendStatus: string;
};

// Conveyor //
export type ConveyorDataType = {
  conveyorsId: number;
  conveyorsName: string;
  conveyorsMobile: string;
  vehicleNo: string;
  conveyorAddress: string;
  studentId: number;
  classId: number;
  accademicId: number;
  adminId: number;
};
export type ConveyorListRequest = {
  classId: any;
};
export type SendToConveyorType = {
  messageId: number;
  conveyorsName: string;
  conveyorsMobile: string;
  adminId: number;
  sendStatus: string;
};

// Group //
export type GroupsDataType = {
  groupId: number;
  groupName: string;
  groupType: number;
};
export type GroupMembersDataType = {
  studentId: number;
  studentName: string;
  guardianName: string;
  guardianNumber: string;
  groupName: string;
  classId: number;
  className: string;
  accademicTime: string;
};
export type SendToGroupMembersType = {
  messageId: number;
  groupName: string;
  studentId: number;
  studentName: string;
  guardianName: string;
  guardianNumber: string;
  className: string;
  adminId: number;
  classId: number;
  sendStatus: string;
};

// ClassDivision //
export type ClassDivisionDataType = {
  classId: number;
  className: string;
  classSectionName: string;
  sortOrder: string;
  accademicId: number;
  accademicTime: string;
};
export type SendToClassDivisionType = {
  adminId: number;
  accademicId: number;
  classId: number;
  messageId: number;
  sendStatus: 'Success' | 'Failed' | '' | undefined;
};
// ClassSection //
export type ClassSectionDataType = {
  classSectionName: string;
  accademicId: number;
  accademicTime: string;
};
export type SendToClassSectionType = {
  adminId: number;
  accademicId: number;
  classSectionName: string;
  messageId: number;
  sendStatus: 'Success' | 'Failed' | '' | undefined;
};

// GroupWise //
export type GroupWiseDataType = {
  groupId: number;
  groupName: string;
  groupType: number;
};
export type SendToGroupWiseType = {
  adminId: number;
  groupId: number;
  accademicId: number;
  messageId: number | undefined;
};

// Public Group //
export type PublicGroupsDataType = {
  groupId: number;
  groupName: string;
  groupType: number;
  accademicId: number;
  accademicTime: string;
};
export type PublicGroupMembersDataType = {
  gmemberId: number;
  gmemberName: string;
  gmemberNumber: string;
  groupName: string;
  accademicId: number;
  accademicTime: string;
};
export type SendToPublicGroupMembersType = {
  messageId: number;
  gmemberName: string;
  gmemberNumber: string;
  adminId: number;
  groupName: string;
  sendStatus: 'Success' | 'Failed' | '' | undefined;
};

// Public group wise //
export type PublicGroupWiseDataType = {
  groupId: number;
  groupName: string;
  groupType: string;
  gmemberNumber: string;
  accademicId: number;
  accademicTime: string;
};
export type SendToPublicGroupWiseType = {
  adminId: number;
  accademicId: number;
  groupId: number;
  messageId: number;
  sendStatus: 'Success' | 'Failed' | '' | undefined;
};

export type DeleteMultipleMessageTemplateType = {
  messageId: number;
  adminId?: number;
};

export type UplaodFilesType = {
  result: string;
  details: string;
};

// Message Box State //
export type MessageBoxState = {
  messageTempList: {
    status: FetchStatus;
    data: MessageTempDataType[];
    error: string | null;
  };
  messageTemplate: {
    status: FetchStatus;
    data: MessageTempDataType | {};
    error: string | null;
  };

  parentsList: {
    status: FetchStatus;
    data: ParentsDataType[];
    error: string | null;
  };
  staffList: {
    status: FetchStatus;
    data: StaffDataType[];
    error: string | null;
  };
  ptaList: {
    status: FetchStatus;
    data: PtaDataType[];
    error: string | null;
  };
  conveyorList: {
    status: FetchStatus;
    data: ConveyorDataType[];
    error: string | null;
  };
  groupList: {
    status: FetchStatus;
    data: GroupsDataType[];
    error: string | null;
  };
  groupMembersList: {
    status: FetchStatus;
    data: GroupMembersDataType[];
    error: string | null;
  };
  classDivisionList: {
    status: FetchStatus;
    data: ClassDivisionDataType[];
    error: string | null;
  };
  classSectionList: {
    status: FetchStatus;
    data: ClassSectionDataType[];
    error: string | null;
  };
  groupWiseList: {
    status: FetchStatus;
    data: GroupWiseDataType[];
    error: string | null;
  };
  publicGroupList: {
    status: FetchStatus;
    data: PublicGroupsDataType[];
    error: string | null;
  };
  publicGroupMembersList: {
    status: FetchStatus;
    data: PublicGroupMembersDataType[];
    error: string | null;
  };
  publicGroupWiseList: {
    status: FetchStatus;
    data: PublicGroupWiseDataType[];
    error: string | null;
  };
  filesUploadList: {
    status: FetchStatus;
    data: UplaodFilesType[];
    error: string | null;
    progress: number;
  };
  submitting: boolean;
  deletingRecords: Record<number, boolean>;
  error: string | null | undefined;
};
