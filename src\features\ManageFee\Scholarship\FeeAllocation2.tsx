/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable no-nested-ternary */
/* eslint-disable react/no-array-index-key */
/* eslint-disable jsx-a11y/alt-text */
import React, { Dispatch, FormEvent, SetStateAction, useCallback, useEffect, useState } from 'react';
import {
  Divider,
  Checkbox,
  Grid,
  Paper,
  Stack,
  Button,
  Typography,
  Box,
  FormControl,
  Select,
  MenuItem,
  SelectChangeEvent,
  useTheme,
  TextField,
  InputAdornment,
} from '@mui/material';
import styled from 'styled-components';
import PercentIcon from '@mui/icons-material/Percent';
import CurrencyRupeeIcon from '@mui/icons-material/CurrencyRupee';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useAppSelector } from '@/hooks/useAppSelector';
import { getScholarshipFeeListStatus, getYearData } from '@/config/storeSelectors';
import useAuth from '@/hooks/useAuth';
import { fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import LoadingButton from '@mui/lab/LoadingButton';
import SaveIcon from '@mui/icons-material/Save';
import { fetchScholarshipFeeList } from '@/store/ManageFee/manageFee.thunks';

const FeeAllocationRoot = styled.div`
  width: 100%;
  padding: 8px 24px;
  .MuiTableCell-root {
    border: 1px solid ${(props) => props.theme.palette.grey[100]};
    border-radius: 0px;
  }
  .MuiTableCell-head {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.info.light : props.theme.palette.grey[900]};
  }
  .MuiTableCell-head:nth-child(1) {
    z-index: 11;
    position: sticky;
    left: 0;
    width: 50px;
  }
  .MuiTableCell-head:nth-child(2) {
    z-index: 11;
    position: sticky;
    left: 50px;
    width: 130px;
  }
  .MuiTableCell-root.MuiTableCell-body:nth-child(1) {
    position: sticky;
    left: 0;
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.info.lighter : props.theme.palette.grey[900]};
    width: 50px;
    z-index: 1;
  }
  .MuiTableCell-root.MuiTableCell-body:nth-child(2) {
    position: sticky;
    left: 50px;
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.info.lighter : props.theme.palette.grey[900]};
    width: 130px;
    z-index: 1;
  }
  .MuiTableCell-root:first-child {
    padding: 7px;
  }
`;
export type ScholarshipAllocationType = {
  id?: number;
  feeId: number;
  termId: number;
  type: number;
  value: string;
  dbResult: string;
  scholarshipId: number;
  scholarshipMapId: number;
};
export type FeeAllocationProps = {
  scholarshipId?: number;
  academicYearId: number;
  isSubmitting: boolean;
  scholarshipValues: ScholarshipAllocationType[];
  onClose: () => void;
  setScholarshipValues: Dispatch<SetStateAction<ScholarshipAllocationType[]>>;
  scholarshipValues2: any;
  setScholarshipValuesArray: any;
  scholarshipValuesArray: ScholarshipAllocationType[];
  handleSave?: any;
  id?: number | undefined;
  showFeeAllocationView: 'TableView' | 'EditView';
  feeTypeId: number;
};

function FeeAllocation2({
  onClose,
  scholarshipId,
  academicYearId,
  isSubmitting,
  scholarshipValues,
  scholarshipValues2,
  setScholarshipValues,
  setScholarshipValuesArray,
  scholarshipValuesArray,
  id,
  handleSave,
  showFeeAllocationView,
  feeTypeId,
}: FeeAllocationProps) {
  const { user } = useAuth();
  const theme = useTheme();
  const dispatch = useAppDispatch();
  const adminId: number = user ? user.accountId : 0;
  const [selectedRows, setSelectedRows] = useState<number[]>([]);
  const YearData = useAppSelector(getYearData);
  const [academicYearFilter, setAcademicYearFilter] = useState(academicYearId);
  const ScholarshipFeeStatus = useAppSelector(getScholarshipFeeListStatus);
  const [selectedData, setSelectedData] = useState([{ feeId: 0, feeTitle: '' }]);
  const [selectedTerms, setSelectedTerms] = useState([{ termId: 0, termTitle: '' }]);
  const [isLoading, setIsloading] = useState<boolean>(false);
  const [disabledCheckBoxes, setDisabledCheckBoxes] = useState<boolean[]>([]);
  const [checkedItems, setCheckedItems] = useState<{ [key: string]: boolean }>({});
  const [isAllSelected, setIsAllSelected] = useState<boolean>(false);
  const [disabledFields, setDisabledFields] = useState<{ [key: string]: boolean }>({});

  const initialClassSectionListRequest = React.useMemo(
    () => ({
      adminId,
      academicId: academicYearId,
      feeTypeId,
    }),
    [adminId, academicYearId]
  );
  const currentClassSectionListRequest = React.useMemo(
    () => ({
      adminId,
      academicId: academicYearFilter,
      feeTypeId,
    }),
    [adminId, academicYearFilter]
  );

  const loadClassSectionList = useCallback(
    async (request: { adminId: number; academicId: number; feeTypeId: number }) => {
      try {
        const data: any = await dispatch(fetchScholarshipFeeList(request)).unwrap();
        setSelectedData(data.basicFeeDetails);
        setSelectedTerms(data.termFeeDetails);
        setSelectedRows([]);
      } catch (error) {
        console.error('Error loading Scholarship list:', error);
      }
    },
    [dispatch]
  );

  // useEffect(() => {
  //   // Save scholarshipValuesArray to local storage whenever it changes
  //   localStorage.setItem('scholarshipValuesArray', JSON.stringify(scholarshipValuesArray));
  // }, [scholarshipValuesArray]);

  React.useEffect(() => {
    dispatch(fetchYearList(adminId));
    loadClassSectionList(initialClassSectionListRequest);
    console.log('scholarshipId::::----', scholarshipId);
    console.log('id::::----', id);
    console.log('scholarshipValues::::----', scholarshipValues);
    console.log('scholarshipValuesArray::::----', scholarshipValuesArray);
  }, [dispatch, adminId, initialClassSectionListRequest, loadClassSectionList, academicYearFilter]);

  const handleYearChange = (e: SelectChangeEvent) => {
    setAcademicYearFilter(parseInt(YearData.filter((item) => item.accademicId === e.target.value)[0].accademicId, 10));
    loadClassSectionList({ ...currentClassSectionListRequest, academicId: parseInt(e.target.value, 10) });
    setSelectedRows([]);
  };

  const handleReset = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      setAcademicYearFilter(parseInt(YearData[0].accademicId, 10));
      setDisabledCheckBoxes([]);
      loadClassSectionList(currentClassSectionListRequest);
    },
    [YearData]
  );
  const handleCancel = () => {
    loadClassSectionList(currentClassSectionListRequest);
    setDisabledCheckBoxes([]);
  };
  const handleSubmit = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      loadClassSectionList(currentClassSectionListRequest);
      setSelectedRows([]);
    },
    [loadClassSectionList, currentClassSectionListRequest]
  );

  const getRowKey = useCallback((row: { feeId: number; feeTitle: string }) => row.feeId, []);

  const handleRowCheckboxChange = (feeId: number) => {
    const isRowSelected = selectedRows.includes(feeId);
    const updatedSelectedRows = isRowSelected ? selectedRows.filter((i) => i !== feeId) : [...selectedRows, feeId];
    setSelectedRows(updatedSelectedRows);

    const updatedCheckedItems = { ...checkedItems };
    const updatedDisabledFields = { ...disabledFields };
    selectedTerms.forEach((term) => {
      const key = `${feeId}-${term.termId}`;
      updatedCheckedItems[key] = !isRowSelected;
      updatedDisabledFields[key] = !updatedCheckedItems[key];
    });
    setCheckedItems(updatedCheckedItems);
    setDisabledFields(updatedDisabledFields);
  };

  // Initialize disabled fields
  useEffect(() => {
    const initialDisabledFields: { [key: string]: boolean } = {};
    selectedData.forEach((row) => {
      selectedTerms.forEach((term) => {
        const key = `${row.feeId}-${term.termId}`;
        initialDisabledFields[key] = true;
      });
    });
    setDisabledFields(initialDisabledFields);
  }, [selectedData, selectedTerms]);

  // Function to update disabled state based on checkbox state
  const updateDisabledState = useCallback(() => {
    const updatedDisabledFields: { [key: string]: boolean } = {};
    selectedData.forEach((row) => {
      selectedTerms.forEach((term) => {
        const key = `${row.feeId}-${term.termId}`;
        updatedDisabledFields[key] = !checkedItems[key];
      });
    });
    setDisabledFields(updatedDisabledFields);
  }, [selectedData, selectedTerms, checkedItems]);

  const handleIndividualCheckboxChange = (feeId: number, termId: number) => {
    const key = `${feeId}-${termId}`;
    const updatedCheckedItems = { ...checkedItems, [key]: !checkedItems[key] };
    const updatedDisabledFields = { ...disabledFields, [key]: !updatedCheckedItems[key] };
    setCheckedItems(updatedCheckedItems);
    setDisabledFields(updatedDisabledFields);
  };

  const handleAllCheckboxChange = () => {
    const newIsAllSelected = !isAllSelected;
    setIsAllSelected(newIsAllSelected);

    const updatedSelectedRows = newIsAllSelected ? selectedData.map((row) => row.feeId) : [];
    setSelectedRows(updatedSelectedRows);

    const updatedCheckedItems: Record<string, boolean> = {};
    const updatedDisabledFields: Record<string, boolean> = {};
    if (newIsAllSelected) {
      selectedData.forEach((row) => {
        selectedTerms.forEach((term) => {
          const key = `${row.feeId}-${term.termId}`;
          updatedCheckedItems[key] = true;
          updatedDisabledFields[key] = false;
        });
      });
    } else {
      selectedData.forEach((row) => {
        selectedTerms.forEach((term) => {
          const key = `${row.feeId}-${term.termId}`;
          updatedCheckedItems[key] = false;
          updatedDisabledFields[key] = true;
        });
      });
    }
    setCheckedItems(updatedCheckedItems);
    setDisabledFields(updatedDisabledFields);
  };

  const handleTextChange = (feeId: number, termId: number, newValue: string, newType: number | null) => {
    if (!Array.isArray(scholarshipValuesArray)) {
      console.error('scholarshipValues2 is not an array');
    }
    // Check if the combination of feeId and termId already exists in scholarshipValues2
    const existingIndex = Array.isArray(scholarshipValuesArray)
      ? scholarshipValuesArray.findIndex(
          (row) => row.feeId === feeId && row.termId === termId && row.scholarshipId === scholarshipId
        )
      : -1;

    // If the combination doesn't exist, push the new object into scholarshipValues2
    if (existingIndex === -1) {
      const newScholarshipValue = {
        feeId,
        termId,
        type: newType, // Set the type for the new value
        value: newValue,
        scholarshipId,
      };
      setScholarshipValuesArray([...scholarshipValuesArray, newScholarshipValue]);
    } else {
      // If the combination already exists, update the value and type of that object
      const updatedScholarshipValues = scholarshipValuesArray?.map((item, index) => {
        if (index === existingIndex) {
          return { ...item, type: newType, value: newValue };
        }
        return item;
      });
      setScholarshipValuesArray(updatedScholarshipValues);
    }

    // You can log the new values for debugging if needed
    console.log(scholarshipValuesArray);
    console.log(newType);
    console.log(newValue);
    console.log(feeId);
    console.log(termId);
    console.log('scholarshipValuesArray::::----', scholarshipValuesArray);
  };

  const handleTypeChange = (feeId: number, termId: number, newValue: string | null, newType: number) => {
    // Get the current value for the feeId and termId combination
    const existingIndex = scholarshipValuesArray.findIndex(
      (item) => item.feeId === feeId && item.termId === termId && item.scholarshipId === scholarshipId
    );

    // If the combination exists, update the type property
    if (existingIndex !== -1) {
      const updatedScholarshipValues = scholarshipValuesArray.map((item, index) => {
        if (index === existingIndex) {
          return { ...item, type: newType, value: newValue };
        }
        return item;
      });
      setScholarshipValuesArray(updatedScholarshipValues);
    } else {
      const newScholarshipValue = {
        feeId,
        termId,
        type: newType, // Assuming type is always 1 for text field changes
        value: newValue,
        scholarshipId,
      };
      setScholarshipValuesArray([...scholarshipValuesArray, newScholarshipValue]);
    }

    // You can log the updated values for debugging if needed
    console.log(scholarshipValues2);
    console.log('newType::::----', newType);
    console.log(feeId);
    console.log(termId);
  };

  const onSave = () => {
    // Save the scholarship values using the callback
    handleSave(scholarshipValuesArray);
    console.log('Scholarship values saved:', scholarshipValuesArray);
  };

  const ClassSectionListColumns: DataTableColumn<{ feeId: number; feeTitle: string }>[] = [
    {
      name: '',
      renderHeader: () => {
        return (
          <Checkbox
            disabled={showFeeAllocationView === 'TableView'}
            aria-label="this is All checkbox selected "
            checked={isAllSelected}
            indeterminate={selectedRows.length > 0 && selectedRows.length < selectedData.length}
            onChange={handleAllCheckboxChange}
          />
        );
      },
      renderCell: (row) => {
        return (
          <Checkbox
            disabled={showFeeAllocationView === 'TableView'}
            aria-label="this is selected row wise checkbox selected "
            checked={selectedRows.includes(row.feeId)}
            onChange={() => handleRowCheckboxChange(row.feeId)}
          />
        );
      },
    },
    {
      name: 'fee',
      renderCell: (row) => {
        return (
          <Typography width={130} variant="subtitle2">
            {row.feeTitle}
          </Typography>
        );
      },
      headerLabel: 'Fee Title',
    },
    ...selectedTerms.map((term) => ({
      name: `${term.termId}`,
      headerLabel: `${term.termTitle}`,
      renderCell: (row: any) => {
        const key = `${row.feeId}-${term.termId}`;
        const index = Array.isArray(scholarshipValues2)
          ? scholarshipValues2.findIndex(
              (scholarship) => scholarship.feeId === row.feeId && scholarship.termId === term.termId
            )
          : -1;
        const index2 = scholarshipValuesArray.findIndex(
          (f) => f.feeId === row.feeId && f.termId === term.termId && f.scholarshipId === scholarshipId
        );

        const value = index !== -1 ? scholarshipValues2[index].value : null; // Get scholarship value for current feeId and termId
        const value2 = index2 !== -1 ? scholarshipValuesArray[index2].value : null; // Get scholarship value for current feeId and termId
        const type = index !== -1 ? scholarshipValues2[index].type : null; // Get scholarship value for current feeId and termId
        const type2 = index2 !== -1 ? scholarshipValuesArray[index2].type : type; // Get scholarship value for current feeId and termId
        return (
          <Stack width={180}>
            <Box display="flex">
              {showFeeAllocationView === 'EditView' && (
                <Stack>
                  <Checkbox
                    aria-label="this is individual corresponding checkbox selected "
                    checked={!!checkedItems[key]}
                    onChange={() => handleIndividualCheckboxChange(row.feeId, term.termId)}
                  />
                </Stack>
              )}
              {showFeeAllocationView === 'EditView' ? (
                <TextField
                  name=""
                  disabled={disabledFields[key]}
                  // value={}
                  defaultValue={value || value2}
                  onChange={(e) => handleTextChange(row.feeId, term.termId, e.target.value, type2)}
                  InputProps={{
                    style: {
                      padding: '0px',
                      backgroundColor: value ? theme.palette.success.lighter : '',
                    },

                    // startAdornment: (
                    //   <InputAdornment position="start">
                    //     {type === 1 || type2 === 1 ? '%' : type === 2 || type2 === 2 ? '₹' : ''}
                    //   </InputAdornment>
                    // ),
                    endAdornment: (
                      <InputAdornment position="end">
                        <Select
                          defaultValue={type || type2}
                          onChange={(e) => handleTypeChange(row.feeId, term.termId, value2, e.target.value)}
                          disabled={disabledFields[key]}
                          displayEmpty
                          sx={{
                            '& .MuiSelect-select': {
                              padding: '8px 32px 10px 8px',
                              backgroundColor: disabledFields[key] ? theme.palette.grey[100] : theme.palette.grey[400],
                              borderBottomLeftRadius: 0,
                              borderTopLeftRadius: 0,
                            },

                            borderTopLeftRadius: 0,
                            borderBottomLeftRadius: 0,

                            width: 55,
                          }}
                        >
                          <MenuItem value={1}>
                            <PercentIcon sx={{ fontSize: '14px' }} />
                          </MenuItem>
                          <MenuItem value={2}>
                            <CurrencyRupeeIcon sx={{ fontSize: '14px' }} />
                          </MenuItem>
                        </Select>
                      </InputAdornment>
                    ),
                    inputProps: {
                      maxLength: type === 1 || type2 === 1 ? 3 : undefined,
                    },
                  }}
                  variant="outlined"
                  fullWidth
                />
              ) : (
                <Box>
                  {type === 1 ? (
                    <Stack direction="row" alignItems="center">
                      <Typography variant="subtitle1" fontSize={14}>
                        {value || ''}
                      </Typography>
                      <PercentIcon sx={{ fontSize: 14 }} />
                    </Stack>
                  ) : type === 2 ? (
                    <Stack direction="row" alignItems="center">
                      <CurrencyRupeeIcon sx={{ fontSize: 14 }} />
                      <Typography variant="subtitle1" fontSize={14}>
                        {value || ''}
                      </Typography>
                    </Stack>
                  ) : (
                    ''
                  )}
                </Box>
              )}
            </Box>
          </Stack>
        );
      },
    })),
  ];

  return (
    <FeeAllocationRoot>
      <Box>
        <Divider />
        <form noValidate onReset={handleReset} onSubmit={handleSubmit}>
          <Grid pb={2} pt={1} container spacing={3} alignItems="end">
            <Grid item xl={2} lg={3} md={3} sm={6} xs={12}>
              <FormControl fullWidth>
                <Typography variant="h6" fontSize={12} color="GrayText">
                  Academic Year
                </Typography>
                <Select
                  labelId="academicYearFilter"
                  id="academicYearFilterSelect"
                  value={academicYearFilter.toString()}
                  onChange={handleYearChange}
                  placeholder="Select Year"
                  disabled
                >
                  {YearData.map((opt) => (
                    <MenuItem key={opt.accademicId} value={opt.accademicId}>
                      {opt.accademicTime}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xl={2} lg={4} md={3} sm={6} xs={12}>
              <FormControl fullWidth>
                <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                  <Button disabled variant="contained" color="secondary" type="reset" fullWidth>
                    Reset
                  </Button>
                </Stack>
              </FormControl>
            </Grid>
          </Grid>
        </form>
        <Paper
          sx={{
            border: selectedData?.length === 0 ? `0px` : `1px solid ${theme.palette.grey[300]}`,
            width: '100%',
            overflow: 'auto',
            // minHeight: 'calc(100vh - 23.7rem)',
          }}
        >
          <Box
            sx={{
              height: '100%',
              width: { xs: selectedData?.length !== 0 ? '43.75rem' : '100%', md: '100%' },
            }}
          >
            <DataTable
              isSubmitting={isSubmitting}
              disabledCheckBox={disabledCheckBoxes}
              columns={ClassSectionListColumns}
              data={selectedData}
              getRowKey={getRowKey}
              fetchStatus={ScholarshipFeeStatus}
              showHorizontalScroll
            />
          </Box>
        </Paper>
        <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 3 }}>
          {selectedData.length !== 0 && (
            <Stack spacing={2} direction="row">
              <Button disabled={isSubmitting} onClick={onClose} variant="contained" color="secondary">
                Cancel
              </Button>
              <LoadingButton
                startIcon={<SaveIcon />}
                fullWidth
                loadingPosition="start"
                onClick={() => {
                  onSave();
                  onClose();
                }}
                loading={isSubmitting}
                variant="contained"
                color="primary"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Saving...' : 'Save'}
              </LoadingButton>
            </Stack>
          )}
        </Box>
      </Box>
    </FeeAllocationRoot>
  );
}

export default FeeAllocation2;
