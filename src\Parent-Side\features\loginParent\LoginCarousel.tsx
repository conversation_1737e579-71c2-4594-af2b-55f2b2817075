import SlideImage2 from '@/assets/loginslider/SlideImage2.svg';
import Slider, { Settings } from 'react-slick';
import styled from 'styled-components';
import CarouselArrow from '@/components/Dashboard/CarouselArrow';

const settings: Settings = {
  dots: false,
  infinite: true,
  speed: 500,
  slidesToShow: 1,
  slidesToScroll: 1,
  nextArrow: <CarouselArrow direction="next" />,
  prevArrow: <CarouselArrow direction="previous" />,
  autoplay: true,
  autoplaySpeed: 10000,
};

const LoginCarouselRoot = styled.div`
  max-width: 100%;
  padding: 40px;
`;

function LoginCarousel() {
  return (
    <LoginCarouselRoot>
      <Slider {...settings}>
        <img src={SlideImage2} alt="One" />
        <img src={SlideImage2} alt="Two" />
      </Slider>
    </LoginCarouselRoot>
  );
}

export default LoginCarousel;
