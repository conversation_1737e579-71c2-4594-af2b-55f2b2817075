import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import Loadable from '@/components/shared/Loadable';

const Gallery = Loadable(lazy(() => import('@/Parent-Side/pages/Gallery')));
const GalleryPhoto = Loadable(lazy(() => import('@/Parent-Side/features/Gallery/GalleryPhoto')));
const GalleryVideo = Loadable(lazy(() => import('@/Parent-Side/features/Gallery/GalleryVideo')));

export const parentGalleryRoutes: RouteObject[] = [
  {
    path: '/gallery',
    element: <Gallery />,
    children: [
      {
        path: 'gallery-photo',
        element: <GalleryPhoto />,
      },
      {
        path: 'gallery-video',
        element: <GalleryVideo />,
      },
    ],
  },
];
