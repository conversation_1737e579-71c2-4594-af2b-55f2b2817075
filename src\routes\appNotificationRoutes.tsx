import Loadable from '@/components/shared/Loadable';
import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';

const AppNotification = Loadable(lazy(() => import('@/pages/AppNotification')));
const NotificationCreate = Loadable(lazy(() => import('@/features/AppNotification/NotificationCreate')));
const NotificationList = Loadable(lazy(() => import('@/features/AppNotification/NotificationList/NotificationList')));
const NotificationReport = Loadable(
  lazy(() => import('@/features/AppNotification/NotificationReport/NotificationReport'))
);
const StaffNotificationReport = Loadable(
  lazy(() => import('@/features/AppNotification/StaffNotification/StaffNotificationReport'))
);

export const notificationRoutes: RouteObject[] = [
  {
    path: '/app-notification',
    element: <AppNotification />,
    children: [
      {
        path: 'create',
        element: <NotificationCreate />,
      },
      {
        path: 'list',
        element: <NotificationList />,
      },
      {
        path: 'report',
        element: <NotificationReport />,
      },
      {
        path: 'staff-report',
        element: <StaffNotificationReport />,
      },
    ],
  },
];
