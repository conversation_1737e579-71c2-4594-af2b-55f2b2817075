import React from 'react';
import {
  Box,
  Button,
  FormControl,
  MenuItem,
  Select,
  Stack,
  TextField,
  InputAdornment,
  Typography,
} from '@mui/material';
import { MESSAGE_TYPE_OPTIONS } from '@/config/Selection';
import { MessageTempDataType } from '@/types/MessageBox';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { ErrorIcon } from '@/theme/overrides/CustomIcons';
import LoadingButton from '@mui/lab/LoadingButton';
import TextareaField from '@/components/shared/Selections/TextareaField';

export type CreateEditMessageFormProps = {
  onSave: (values: MessageTempDataType, mode: 'create' | 'edit') => void;
  onCancel: (mode: 'create' | 'edit') => void;
  messageDetails: MessageTempDataType;
  isSubmitting: boolean;
  adminId: number | undefined;
};

const CreateEditMessageTempValidationSchema = Yup.object({
  messageTitle: Yup.string().required('Please enter Message Title'),
  messageType: Yup.number().oneOf([1, 2, 3], 'Please select Message Type'),
  messageTemplateId: Yup.string().when('messageType', {
    is: 2,
    then: Yup.string().required('Please enter Template Id'),
    otherwise: Yup.string().notRequired(),
  }),
  messageContent: Yup.string().required('Please enter Message Content'),
  // .test('exists', 'Class name already used', async (val) => {
  //   try {
  //     if (val) {
  //       const existsResponse = await api.MessageBox.CreateNewMessage(val);
  //       return !existsResponse.data;
  //     }

  //     return true;
  //   } catch {
  //     return true;
  //   }
  // }),
});

export const CreateEditMessageForm = ({
  messageDetails,
  onCancel,
  isSubmitting,
  onSave,
  adminId,
}: CreateEditMessageFormProps) => {
  const mode = messageDetails.messageId === 0 ? 'create' : 'edit';

  const {
    values: { messageTitle, messageContent, messageType, messageTemplateId },
    handleChange,
    handleBlur,
    handleSubmit,
    touched,
    errors,
  } = useFormik<MessageTempDataType>({
    initialValues: {
      messageId: messageDetails.messageId,
      messageTitle: messageDetails.messageTitle,
      messageContent: messageDetails.messageContent,
      messageDate: messageDetails.messageDate,
      messageCreatedBy: adminId,
      messageStatus: messageDetails.messageStatus,
      messageTemplateId: messageDetails.messageTemplateId,
      messageType: messageDetails.messageType,
    },
    validationSchema: CreateEditMessageTempValidationSchema,
    onSubmit: (values) => {
      onSave(values, mode);
    },
    validateOnBlur: false,
    // validate: (messageVals) => {
    //   const errorObj: any = {};
    //   messageVals.messageContent.forEach(async (classRow, rowIndex, arr) => {
    //     if (arr.some((x, i) => classRow.className !== '' && x.className === classRow.className && i !== rowIndex)) {
    //       if (!errorObj.classes) {
    //         errorObj.classes = [];
    //       }
    //       errorObj.classes[rowIndex] = {};
    //       errorObj.classes[rowIndex].className = 'Duplicate class name';
    //     }
    //   });
    //   return errorObj;
    // },
  });
  const limit = messageType === 1 ? 30 : 3000;
  const showTemplateIdField = messageType === 2;

  return (
    <Box mt={3}>
      <form noValidate onSubmit={handleSubmit}>
        <Stack
          sx={{
            height: 'calc(100vh - 150px)',
            overflow: 'auto',
            '&::-webkit-scrollbar': {
              width: 0,
            },
          }}
          direction="column"
        >
          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Compose Message
            </Typography>
            <TextField
              placeholder="Enter message title"
              name="messageTitle"
              value={messageTitle}
              onBlur={handleBlur}
              onChange={handleChange}
              error={touched.messageTitle && !!errors.messageTitle}
              helperText={errors.messageTitle}
              disabled={isSubmitting}
              InputProps={{
                endAdornment: touched.messageTitle && !!errors.messageTitle && <ErrorIcon color="error" />,
              }}
            />
          </FormControl>

          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Message content
            </Typography>

            <TextareaField
              limit={limit}
              ShowCharectersCount={messageType}
              placeholder="Enter content..."
              name="messageContent"
              value={messageContent}
              onChange={handleChange}
              error={touched.messageContent && !!errors.messageContent}
              helperText={touched.messageContent && errors.messageContent}
              InputProps={{
                inputProps: {
                  style: { resize: 'vertical', minHeight: '60px', maxHeight: '100px' },
                  maxLength: limit,
                },
                endAdornment: touched.messageContent && !!errors.messageContent && (
                  <InputAdornment position="end">
                    <ErrorIcon color="error" sx={{ mr: 2 }} />
                  </InputAdornment>
                ),
              }}
            />

            {/* <TextField
                multiline
                fullWidth
                minRows={2}
                InputProps={{
                  // inputProps: { style: { resize: 'vertical' } },
                  endAdornment: touched.messageContent && !!errors.messageContent && (
                    <InputAdornment position="end">
                      <ErrorIcon color="error" sx={{ mr: 2 }} />
                    </InputAdornment>
                  ),
                }}
                placeholder="Enter content..."
                name="messageContent"
                value={messageContent}
                onChange={handleChange}
                error={touched.messageContent && !!errors.messageContent}
                helperText={touched.messageContent && errors.messageContent}
                disabled={isSubmitting}
              /> */}
          </FormControl>
          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Type
            </Typography>
            <Select
              name="messageType"
              value={messageType}
              onChange={handleChange}
              error={touched.messageType && !!errors.messageType}
              disabled={isSubmitting}
            >
              {MESSAGE_TYPE_OPTIONS.map((opt) => (
                <MenuItem key={opt.id} value={opt.id}>
                  {opt.name}
                </MenuItem>
              ))}
            </Select>
            {touched.messageType && !!errors.messageType && (
              <Typography color="red" fontSize="12px" ml={2.5} mt={1} variant="subtitle1">
                {errors.messageType}
              </Typography>
            )}
          </FormControl>
          {showTemplateIdField && (
            <FormControl>
              <Typography mt={2} variant="subtitle1" fontSize={12}>
                Template Id
              </Typography>
              <TextField
                placeholder="Enter template id"
                name="messageTemplateId"
                value={messageTemplateId}
                onChange={handleChange}
                error={touched.messageTemplateId && !!errors.messageTemplateId}
                helperText={errors.messageTemplateId}
                disabled={isSubmitting}
              />
            </FormControl>
          )}
        </Stack>
        <Box sx={{ justifyContent: 'center', pt: 3, position: 'relative' }}>
          <Stack spacing={2} direction="row" sx={{}}>
            <Button onClick={() => onCancel(mode)} fullWidth variant="contained" color="secondary" type="button">
              Cancel
            </Button>
            <LoadingButton
              loading={isSubmitting}
              fullWidth
              variant="contained"
              color="primary"
              type="submit"
              loadingPosition="start"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Saving...' : 'Save'}
            </LoadingButton>
          </Stack>
        </Box>
      </form>
    </Box>
  );
};
