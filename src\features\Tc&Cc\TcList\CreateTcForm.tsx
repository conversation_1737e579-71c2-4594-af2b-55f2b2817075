/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import {
  Autocomplete,
  Grid,
  Stack,
  TextField,
  Button,
  Typography,
  Box,
  FormControl,
  Select,
  MenuItem,
  RadioGroup,
  FormControlLabel,
  Radio,
} from '@mui/material';
import styled from 'styled-components';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import { CLASS_SELECT, STATUS_OPTIONS, SUBJECT_SELECT, YEAR_SELECT } from '@/config/Selection';
import SelectDateTimePicker from '@/components/shared/Selections/TimePicker';
import InputFileUpload from '@/components/shared/Selections/FilesUpload';
import DateSelect from '@/components/shared/Selections/DateSelect';

const CreateTcFormRoot = styled.div`
  width: 100%;
  padding: 1.5rem;
`;

function CreateTcForm({ onClose }: any) {
  return (
    <CreateTcFormRoot>
      <form noValidate>
        <Grid container spacing={2} direction="row" justifyContent="space-between">
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                SL.No
              </Typography>
              <TextField placeholder="Enter sl.number " name="" />
            </FormControl>
          </Grid>
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Admission No
              </Typography>
              <TextField placeholder="Enter admissinon no " name="" />
            </FormControl>
          </Grid>
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Name of the pupil
              </Typography>
              <TextField placeholder="Enter student name " name="" />
            </FormControl>
          </Grid>
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Father's / Guardian Name
              </Typography>
              <TextField placeholder="Enter name " name="" />
            </FormControl>
          </Grid>
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Nationality
              </Typography>
              <TextField placeholder="Enter nationality " name="" />
            </FormControl>
          </Grid>
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Schedule Caste or Tribe (SC,ST,OBC)
              </Typography>
              <TextField placeholder="sc,st,obc " name="" />
            </FormControl>
          </Grid>
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Religion and Community
              </Typography>
              <TextField placeholder="Enter religion and community " name="" />
            </FormControl>
          </Grid>
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Date of Birth
              </Typography>
              <TextField placeholder="Enter date of birth " name="" />
            </FormControl>
          </Grid>
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Identification Mark one
              </Typography>
              <TextField placeholder="Enter identification mark " name="" />
            </FormControl>
          </Grid>
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Identification Mark Two
              </Typography>
              <TextField placeholder="Enter identification mark " name="" />
            </FormControl>
          </Grid>
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="h6" fontSize={14} mb={1}>
                Date of first admission ic school & class
              </Typography>
              <TextField placeholder="Enter admission ic school & class" name="" />
            </FormControl>
          </Grid>
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="h6" fontSize={14} mb={1}>
                Which class Last Studies in (W&F)
              </Typography>
              <TextField placeholder="Enter last studies words & figure" name="" />
            </FormControl>
          </Grid>
          <Grid item lg={12} xs={12}>
            <FormControl fullWidth>
              <Typography variant="h6" fontSize={14} mb={1}>
                School/Board annual examination last taken with result
              </Typography>
              <TextField placeholder="Enter last taken with result" name="" />
            </FormControl>
          </Grid>
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="h6" fontSize={14} mb={1}>
                Qualified for promotion to next standard
              </Typography>
              <RadioGroup
                defaultValue="yes"
                row
                aria-labelledby="demo-row-radio-buttons-group-label"
                name="row-radio-buttons-group"
              >
                <FormControlLabel value="yes" control={<Radio checkedIcon={<CheckCircleIcon />} />} label="Yes" />
                <FormControlLabel value="no" control={<Radio checkedIcon={<CheckCircleIcon />} />} label="No" />
              </RadioGroup>
            </FormControl>
          </Grid>
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="h6" fontSize={14} mb={1}>
                If failed once/twice in the class same class
              </Typography>
              <RadioGroup
                defaultValue="yes"
                row
                aria-labelledby="demo-row-radio-buttons-group-label"
                name="row-radio-buttons-group"
              >
                <FormControlLabel value="yes" control={<Radio checkedIcon={<CheckCircleIcon />} />} label="Yes" />
                <FormControlLabel value="no" control={<Radio checkedIcon={<CheckCircleIcon />} />} label="No" />
              </RadioGroup>
            </FormControl>
          </Grid>
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="h6" fontSize={14} mb={1}>
                Subject Studied
              </Typography>
              <TextField placeholder="Subject one" name="" />
            </FormControl>
          </Grid>
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="h6" fontSize={14} mb={1}>
                Subject Studied
              </Typography>
              <TextField placeholder="Subject two" name="" />
            </FormControl>
          </Grid>
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="h6" fontSize={14} mb={1}>
                Subject Studied
              </Typography>
              <TextField placeholder="Subject three" name="" />
            </FormControl>
          </Grid>
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="h6" fontSize={14} mb={1}>
                Subject Studied
              </Typography>
              <TextField placeholder="Subject four" name="" />
            </FormControl>
          </Grid>
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="h6" fontSize={14} mb={1}>
                Subject Studied
              </Typography>
              <TextField placeholder="Subject five" name="" />
            </FormControl>
          </Grid>
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="h6" fontSize={14} mb={1}>
                Subject Studied
              </Typography>
              <TextField placeholder="Subject six" name="" />
            </FormControl>
          </Grid>
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="h6" fontSize={14} mb={1}>
                Subject Studied
              </Typography>
              <TextField placeholder="Subject seven" name="" />
            </FormControl>
          </Grid>
        </Grid>
        <Stack>
          <Typography variant="h6"> More Information</Typography>
        </Stack>
        <Grid container spacing={3} direction="row" justifyContent="space-between">
          <Grid item lg={12} xs={12}>
            <FormControl fullWidth>
              <Typography variant="h6" fontSize={14} mb={1}>
                If failed once/twice in the class same class
              </Typography>
              <RadioGroup
                defaultValue="yes"
                row
                aria-labelledby="demo-row-radio-buttons-group-label"
                name="row-radio-buttons-group"
              >
                <FormControlLabel value="yes" control={<Radio checkedIcon={<CheckCircleIcon />} />} label="Yes" />
                <FormControlLabel value="no" control={<Radio checkedIcon={<CheckCircleIcon />} />} label="No" />
              </RadioGroup>
            </FormControl>
          </Grid>
          <Grid item lg={12} xs={12}>
            <FormControl fullWidth>
              <Typography variant="h6" fontSize={14} mb={1}>
                Any fee concession availed of, if so the nature of such concession
              </Typography>
              <TextField placeholder="Enter nature of such concession" name="" />
            </FormControl>
          </Grid>
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="h6" fontSize={14} mb={1}>
                Total No of working days
              </Typography>
              <TextField placeholder="Enter working days" name="" />
            </FormControl>
          </Grid>
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="h6" fontSize={14} mb={1}>
                Total No of Present
              </Typography>
              <TextField placeholder="Enter present" name="" />
            </FormControl>
          </Grid>
          <Grid item lg={12} xs={12}>
            <FormControl fullWidth>
              <Typography variant="h6" fontSize={14} mb={1}>
                Date of pupil's last attendance school
              </Typography>
              <TextField placeholder="Enter last attendance school" name="" />
            </FormControl>
          </Grid>
          <Grid item lg={12} xs={12}>
            <FormControl fullWidth>
              <Typography variant="h6" fontSize={14} mb={1}>
                If failed once/twice in the class same class
              </Typography>
              <RadioGroup
                defaultValue="yes"
                row
                aria-labelledby="demo-row-radio-buttons-group-label"
                name="row-radio-buttons-group"
              >
                <FormControlLabel value="Excellent" control={<Radio checkedIcon={<CheckCircleIcon />} />} label="Yes" />
                <FormControlLabel value="Good" control={<Radio checkedIcon={<CheckCircleIcon />} />} label="No" />
                <FormControlLabel
                  value="Satisfactory"
                  control={<Radio checkedIcon={<CheckCircleIcon />} />}
                  label="No"
                />
              </RadioGroup>
            </FormControl>
          </Grid>
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="h6" fontSize={14} mb={1}>
                Date of application for certificate
              </Typography>
              <TextField placeholder="Enter application certificate" name="" />
            </FormControl>
          </Grid>
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="h6" fontSize={14} mb={1}>
                Date of issue of certificate
              </Typography>
              <TextField placeholder="Enter Date of issue of certificate" name="" />
            </FormControl>
          </Grid>
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="h6" fontSize={14} mb={1}>
                Reason for leaving the school
              </Typography>
              <TextField placeholder="Enter reason " name="" />
            </FormControl>
          </Grid>
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="h6" fontSize={14} mb={1}>
                Any other remarks
              </Typography>
              <TextField placeholder="Enter other remarks" name="" />
            </FormControl>
          </Grid>
          <Grid item lg={12} xs={12}>
            <FormControl fullWidth>
              <Typography variant="h6" fontSize={14} mb={1}>
                Name of the school to which admission is sought
              </Typography>
              <TextField placeholder="Enter school to which admission is sought" name="" />
            </FormControl>
          </Grid>
        </Grid>
        <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 3 }}>
          <Stack spacing={2} direction="row">
            <Button variant="contained" color="secondary">
              Cancel
            </Button>
            <Button variant="contained" color="primary">
              Save
            </Button>
          </Stack>
        </Box>
      </form>
    </CreateTcFormRoot>
  );
}

export default CreateTcForm;
