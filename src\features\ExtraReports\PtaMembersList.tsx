/* eslint-disable prettier/prettier */
/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  Collapse,
  FormControl,
} from '@mui/material';
import styled from 'styled-components';
import SearchIcon from '@mui/icons-material/Search';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import { MdAdd } from 'react-icons/md';
import { STATUS_SELECT, YEAR_SELECT } from '@/config/Selection';

const PtaMembersListRoot = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      height: calc(100% - 100px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;
const data = [
  {
    slNo: 1,
    name: 'Passdaily Support',
    Role: 'President',
    Mobile: '546446',
    EmailId: '<EMAIL>',
    Status: 'Publish',
  },
  {
    slNo: 2,
    name: 'Passdaily Support',
    Role: 'Vice President',
    Mobile: '54+85',
    EmailId: '<EMAIL>',
    Status: 'Publish',
  },
  {
    slNo: 3,
    name: 'Passdaily Support',
    Role: 'Secretary',
    Mobile: '54674',
    EmailId: '<EMAIL>',
    Status: 'Publish',
  },
  {
    slNo: 4,
    name: 'Passdaily Support',
    Role: 'Joint Secretary',
    Mobile: '5+596',
    EmailId: '<EMAIL>',
    Status: 'Publish',
  },
  {
    slNo: 5,
    name: 'Passdaily Support',
    Role: 'Cashier',
    Mobile: '45',
    EmailId: '<EMAIL>',
    Status: 'Publish',
  },
  {
    slNo: 6,
    name: 'Passdaily Support',
    Role: 'Member',
    Mobile: '6468+',
    EmailId: '<EMAIL>',
    Status: 'Publish',
  },
  {
    slNo: 7,
    name: 'Passdaily Support',
    Role: 'Treasurer',
    Mobile: '784563',
    EmailId: '<EMAIL>',
    Status: 'Publish',
  },
  {
    slNo: 8,
    name: 'Passdaily Support',
    Role: 'Event Coordinator',
    Mobile: '987654',
    EmailId: '<EMAIL>',
    Status: 'Publish',
  },
  {
    slNo: 9,
    name: 'Passdaily Support',
    Role: 'Public Relations Officer',
    Mobile: '123456',
    EmailId: '<EMAIL>',
    Status: 'Publish',
  },
  {
    slNo: 10,
    name: 'Passdaily Support',
    Role: 'Webmaster',
    Mobile: '543210',
    EmailId: '<EMAIL>',
    Status: 'Publish',
  },
  {
    slNo: 11,
    name: 'Passdaily Support',
    Role: 'Graphic Designer',
    Mobile: '789012',
    EmailId: '<EMAIL>',
    Status: 'Publish',
  },
];

function PtaMembersList() {
  // const [drawerOpen, setDrawerOpen] = React.useState<boolean>(false);
  const [showFilter, setShowFilter] = useState(false);

  // const handleOpen = () => {
  //   setDrawerOpen(true);
  // };

  // const toggleDrawerClose = useCallback(() => setDrawerOpen(false), []);

  const getRowKey = useCallback((row: any) => row.examId, []);
  const PtaMembersListColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'slNo',
        dataKey: 'slNo',
        headerLabel: 'Sl.No',
        sortable: true,
      },
      { name: 'name', dataKey: 'name', headerLabel: 'Name' },
      {
        name: 'Role',
        dataKey: 'Role',
        headerLabel: 'Role',
      },
      {
        name: 'Mobile',
        dataKey: 'Mobile',
        headerLabel: 'Mobile',
      },
      {
        name: 'EmailId',
        dataKey: 'EmailId',
        headerLabel: 'EmailId',
      },
      {
        name: 'Status',
        dataKey: 'Status',
        headerLabel: 'Status',
      },
      {
        name: 'actions',
        headerLabel: 'Actions',
        renderCell: () => {
          return (
            <Stack direction="row" gap={1}>
              <IconButton size="small" sx={{ padding: 0.5 }}>
                <EditIcon />
              </IconButton>
              <IconButton size="small" color="error" sx={{ padding: 0.5 }}>
                <DeleteIcon />
              </IconButton>
            </Stack>
          );
        },
      },
    ],
    []
  );
  return (
    <Page title="List">
      <PtaMembersListRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17}>
              PTA Members List
            </Typography>
            <Box pb={1} sx={{ flexShrink: 0 }}>
              <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                <SearchIcon />
              </IconButton>
              <Button sx={{ borderRadius: '20px' }} variant="outlined" size="small">
                <MdAdd size="20px" /> Add
              </Button>
            </Box>
          </Stack>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={3} alignItems="end">
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="h6" fontSize={14}>
                        Academic Year
                      </Typography>
                      <Autocomplete
                        options={YEAR_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select Year" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="h6" fontSize={14}>
                        Role
                      </Typography>
                      <Autocomplete
                        options={['President', 'Vice President', 'Secretary', 'Joint Secretary', 'Cashier']}
                        renderInput={(params) => <TextField {...params} placeholder="Select Role" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="h6" fontSize={14}>
                        Email
                      </Typography>
                      <TextField placeholder="Enter Email" />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="h6" fontSize={14}>
                        Mobile
                      </Typography>
                      <TextField placeholder="Enter Mobile" />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="h6" fontSize={14}>
                        Status
                      </Typography>
                      <Autocomplete
                        options={STATUS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select Status" />}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item lg="auto" xs={12} sm={6}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Paper className="card-table-container" sx={{ marginTop: '5px' }}>
              <DataTable columns={PtaMembersListColumns} data={data} getRowKey={getRowKey} fetchStatus="success" />
            </Paper>
          </div>
          <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 2 }}>
            <Stack spacing={2} direction="row">
              {/* <Button variant="contained" color="secondary">
                Cancel
              </Button> */}
              <Button variant="contained" color="primary">
                Download
              </Button>
            </Stack>
          </Box>
        </Card>
      </PtaMembersListRoot>

      {/* <TemporaryDrawer onClose={toggleDrawerClose} Title="Upload Details" state={drawerOpen} DrawerContent="" /> */}
    </Page>
  );
}

export default PtaMembersList;
