/* eslint-disable new-cap */
/* eslint-disable no-else-return */
/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useRef, useState } from 'react';
import {
  Box,
  Skeleton,
  Stack,
  Table,
  TableContainer,
  TableRow,
  TableBody,
  Typography,
  TableCell,
  useTheme,
  TableHead,
} from '@mui/material';
import styled from 'styled-components';
import useSettings from '@/hooks/useSettings';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import LoadingButton from '@mui/lab/LoadingButton';
import { useAppSelector } from '@/hooks/useAppSelector';
import { fetchReceiptOnline } from '@/store/Payment/payment.thunk';
import { getReceiptForPrintListStatus } from '@/config/storeSelectors';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import generatePDF, { Resolution, Margin, Options } from 'react-to-pdf';
import passdailLogo from '@/assets/SchoolLogos/logo-small.svg';
import holyLogo from '@/assets/SchoolLogos/HolyLogo.jpeg';
import carmelLogo from '@/assets/SchoolLogos/CarmelLogo.png';
import thereseLogo from '@/assets/SchoolLogos/StthereseLogo.png';
import thomasLogo from '@/assets/SchoolLogos/StThomasLogo.png';
import MIMLogo from '@/assets/SchoolLogos/MIMLogo.png';
import nirmalaLogo from '@/assets/SchoolLogos/NirmalaLogo.png';
import { useReactToPrint } from 'react-to-print';
import dayjs from 'dayjs';
import { GetReceiptDetailsType, GetReceiptPaymentModeType, GetReceiptTermfeepaidType } from '@/types/ManageFee';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';

const OnlineRecieptRoot = styled.div`
  /* padding: 1rem; */
  @media screen and (max-width: 768px) {
    width: 1000px;
  }
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.grey[200] : props.theme.palette.grey[900]};
  /* position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1111; */
  /* @media screen and (max-width: 576px) { */
  /* padding: 0.5rem; */
  /* } */

  /* .Card { */
  /* min-height: calc(100vh - 160px); */
  /* @media screen and (max-width: 996px) { */
  /* height: calc(100% - 52px); */
  /* height: 100%; */
  /* } */
  /* } */
  .TableCard {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.grey[200] : props.theme.palette.grey[900]};
  }

  .fee-card {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.grey[100] : props.theme.palette.grey[900]};
  }

  .Main-div {
    margin-top: 0px;
  }
  .amount_words {
    max-width: 95%;
  }
  .print-preview-container {
    top: 0px;
    bottom: 0px;
    margin: 20px;
    padding: 20px;
    border: 1px solid;
  }
  .scrollbar::-webkit-scrollbar {
    width: 0px;
  }
`;

const printStyles = `
@media print {

}
`;
const styleSheet = document.createElement('style');
styleSheet.type = 'text/css';
styleSheet.innerText = printStyles;
document.head.appendChild(styleSheet);

function OnlineReciept() {
  const theme = useTheme();
  const isLight = useSettings().themeMode === 'light';
  const dispatch = useAppDispatch();
  // const [parentPayFeeList, setPrentPayFeeList] = useState<GetParentPayFeeDataType[]>([])

  const queryParams = new URLSearchParams(window.location.search);
  const receiptIdNo = parseInt(queryParams.get('ReceiptId') || '0', 10);
  const [loading, setLoading] = useState(false);

  // const componentRef = useRef<HTMLInputElement | null>(null);

  const options: Options = useMemo(
    () => ({
      resolution: Resolution.MEDIUM,
      filename: `Receipt-${receiptIdNo}.pdf`,
      page: {
        margin: Margin.SMALL,
        format: 'letter',
      },
      canvas: {
        mimeType: 'image/png',
        qualityRatio: 1,
      },
      overrides: {
        pdf: {
          compress: true,
        },
        canvas: {
          useCORS: true,
          // removeContainer: true,
        },
      },
    }),
    [receiptIdNo]
  );
  const getTargetElement = () => document.getElementById('content-id');

  const handleDownloadPDF = useCallback(async () => {
    setLoading(true);
    try {
      await generatePDF(getTargetElement, options);
    } finally {
      setLoading(false);
    }
  }, [options]);
  // const exportPDF = () => {
  //   const input = componentRef.current;
  //   html2canvas(input).then((canvas) => {
  //     const imgData = canvas.toDataURL('image/png');
  //     const pdf = new jsPDF();

  //     // Calculate the dimensions of the PDF page
  //     const pdfWidth = pdf.internal.pageSize.getWidth();
  //     const pdfHeight = pdf.internal.pageSize.getHeight();

  //     // Calculate the aspect ratio of the canvas image
  //     // const aspectRatio = canvas.width / canvas.height;
  //     // let imgWidth = pdfWidth;
  //     // let imgHeight = pdfWidth / aspectRatio;

  //     // if (imgHeight > pdfHeight) {
  //     //   imgHeight = pdfHeight;
  //     //   imgWidth = pdfWidth;
  //     // }

  //     pdf.addImage(imgData, 'PNG', 0, 0, pdfWidth, pdfHeight);
  //     pdf.save('table.pdf');
  //   });
  // };
  const receiptForPrintListStatus = useAppSelector(getReceiptForPrintListStatus);
  const [paymodeData, setPaymodeData] = React.useState<GetReceiptPaymentModeType[]>([]);
  const [termfeepaid, setTermfeepaid] = React.useState<GetReceiptTermfeepaidType[]>([]);
  const [receiptDetails, setReceiptDetails] = React.useState<GetReceiptDetailsType | undefined>(undefined);
  const {
    schoolName,
    schoolAddress,
    schoolEmail,
    schoolPhone,
    studentName,
    createdDate,
    receiptType,
    className,
    contactNo,
    admissionNo,
    grandTotalWords,
    receiptNo,
  } = receiptDetails || {};

  // const handlePrint = useReactToPrint({
  //   content: () => componentRef.current,
  // });

  const loadReceiptDataList = useCallback(
    async (request: number) => {
      try {
        const data = await dispatch(fetchReceiptOnline(request)).unwrap();
        console.log('data::::', data);
        setReceiptDetails(data.receiptDetail);
        setPaymodeData(data.paymentmode);
        setTermfeepaid(data.termfeepaid);
      } catch (error) {
        console.error('Error loading reciept:', error);
      }
    },
    [dispatch]
  );

  React.useEffect(() => {
    loadReceiptDataList(receiptIdNo);
  }, [loadReceiptDataList, receiptIdNo]);

  const formattedDate = dayjs(createdDate, 'YYYY/MM/DD').format('DD/MM/YYYY');
  const totalAmountPaid = termfeepaid?.reduce((total, item) => total + item.totalAmount, 0);

  return (
    <OnlineRecieptRoot>
      <Box sx={{ justifyContent: 'center' }}>
        <Stack pt={2} pl={3} spacing={2} direction="row" justifyContent="start">
          <LoadingButton
            loadingPosition="start"
            loading={loading}
            startIcon={<PictureAsPdfIcon />}
            // loading={isSubmitting}
            variant="contained"
            color="success"
            // sx={{ backgroundColor: '#f1f2f3', color: 'black', fontWeight: 600 }}
            size="medium"
            // onClick={exportPDF}
            onClick={handleDownloadPDF}
            // onClick={!printButtonShow ? handlePay : handlePrint}
          >
            Download
          </LoadingButton>
        </Stack>
      </Box>
      <Box py="1%" id="content-id">
        <Box className="print-preview-container">
          {receiptForPrintListStatus === 'loading' ? (
            <Box flexDirection="column" display="flex" gap={1} alignItems="center" mb={receiptType ? 2 : 2}>
              <Skeleton variant="circular" width={70} height={70} />
              <Skeleton variant="rounded" width={180} height={15} />
              <Skeleton variant="rounded" width={230} height={15} />
              <Skeleton variant="rounded" width={300} height={15} />
            </Box>
          ) : (
            <Box flexDirection="column" display="flex" alignItems="center" mb={receiptType ? 2 : 0}>
              <img src={passdailLogo} width={70} alt="passdailLogo" />
              {/* {receiptType === 'ReceiptDefault' && <img src={holyLogo} width={70} alt="holyLogo" />} */}
              {/* <img src={carmelLogo} width={70} alt="carmelLogo" /> */}
              {/* <img src={thereseLogo} width={70} alt="thereseLogo" /> */}
              {/* <img src={thomasLogo} width={70} alt="thomasLogo" /> */}
              {/* <img src={nirmalaLogo} width={70} alt="nirmalaLogo" /> */}
              {/* <img src={MIMLogo} width={70} alt="MIMLogo" /> */}
              {receiptType === 'ReceiptDefault' && (
                <>
                  <Typography variant="subtitle2" fontSize={16} color="primary">
                    {schoolName}
                  </Typography>
                  <Typography variant="body1" fontSize={13} color="secondary">
                    {schoolAddress}
                  </Typography>
                  <Typography variant="body1" fontSize={13} color="secondary">
                    {schoolEmail}, {schoolPhone}
                  </Typography>
                </>
              )}
            </Box>
          )}
           {/* ============ MIM HIGH SCHOOL =========== */}
          {/* <Typography variant="subtitle2" fontSize={16} color="primary">
            Mueenul Islam Manoor High School
          </Typography>
          <Typography variant="body1" fontSize={13} color="secondary">
            KANDANAKAM,KALADI PO MALAPPURAM 679582
          </Typography>
          <Typography variant="body1" fontSize={13} color="secondary">
            04942103095,9645942121
          </Typography> */}
          {/* ========== Holy Angels Gardens ========== */}
          {receiptType === 'ReceiptHolyNursery' && (
            <Box
              className="Main-div"
              flexDirection="row"
              display="flex"
              mt={3}
              gap={4}
              justifyContent="center"
              alignItems="center"
              p={2}
              mb={2}
              bgcolor="#e2b4bb"
            >
              <img src={holyLogo} width={100} alt="holyLogo" />
              <Stack textAlign="center" flex={1}>
                <Typography variant="h1" fontWeight="bold" fontSize={30} color="primary">
                  <b>ANGELS&apos; GARDENS</b>
                </Typography>
                <Typography variant="body1" fontSize={13}>
                  The Kindergarten Experience
                </Typography>
                <Typography variant="body1" fontSize={15}>
                  Gandhinagar, Dombivli (E)
                </Typography>
              </Stack>
              <div style={{ visibility: 'hidden', width: 100 }} />
            </Box>
          )}
          {/* ========== Holy Angels Paradise Kindergarten ========== */}
          {receiptType === 'ReceiptHolyKg' && (
            <Box
              className="Main-div"
              flexDirection="row"
              display="flex"
              mt={3}
              gap={4}
              justifyContent="center"
              alignItems="center"
              p={2}
              mb={2}
              bgcolor="#e2b4bb"
            >
              <img src={holyLogo} width={100} alt="holyLogo" />
              <Stack textAlign="center" flex={1}>
                <Typography variant="h1" fontWeight="bold" fontSize={30} color="primary">
                  <b>ANGELS&apos; PARADISE</b>
                </Typography>
                <Typography variant="body1" fontSize={13}>
                  The Kindergarten Experience
                </Typography>
                <Typography variant="body1" fontSize={15}>
                  Gandhinagar, Dombivli (E)
                </Typography>
              </Stack>
              <div style={{ visibility: 'hidden', width: 100 }} />
            </Box>
          )}
          {/* ========== Holy Angels Paradise College ========== */}
          {receiptType === 'ReceiptHolyCollege' && (
            <Box
              className="Main-div"
              flexDirection="column"
              mt={3}
              bgcolor="yellow"
              border={1}
              p={1}
              display="flex"
              alignItems="center"
              mb={2}
            >
              <img src={holyLogo} width={70} alt="holyLogo" />
              <Stack color={theme.palette.common.black} direction="row" justifyContent="space-between" gap={5}>
                <Typography variant="subtitle2" fontSize={14}>
                  Affiliated to CBSE
                </Typography>
                <Typography variant="subtitle2" fontSize={13}>
                  TRINITY EDUCATIONAL TRUST&apos;S
                </Typography>
                <Typography variant="subtitle2" fontSize={14}>
                  ISO 9001: 2008 Certified
                </Typography>
              </Stack>
              <Typography variant="h1" fontWeight="bold" fontSize={30} color="primary">
                <b>HOLY ANGELS&apos; SCHOOL & Jr. COLLEGE</b>
              </Typography>
              <Typography color={theme.palette.common.black} variant="body1" fontSize={13}>
                (A Private Unaided Minority Institution)
              </Typography>
              <Typography color={theme.palette.common.black} variant="body1" fontSize={9}>
                Behind P & T Colony, Nandivli - Gandhinagar, Dombivli (E) Dist. Thane, MAH
                <EMAIL> Tel: (0251) 2821975, 2821234
              </Typography>
            </Box>
          )}
          {/* ============================ */}
          <Box border={1} p={1} display="flex" justifyContent="space-between" mb={1}>
            <Stack>
              <Typography variant="subtitle1" fontSize={13}>
                Name : <span className="fw-bold">{studentName}</span>
              </Typography>
              <Typography variant="subtitle1" fontSize={13}>
                Admission No : <span className="fw-bold">{admissionNo}</span>
              </Typography>
              <Typography variant="subtitle1" fontSize={13}>
                Class Name : <span className="fw-bold">{className}</span>
              </Typography>
            </Stack>
            <Stack>
              <Typography variant="subtitle1" fontSize={13}>
                Date : <span className="fw-bold">{formattedDate}</span>
              </Typography>
              <Typography variant="subtitle1" fontSize={13}>
                Receipt No : <span className="fw-bold">{receiptNo}</span>
              </Typography>
              <Typography variant="subtitle1" fontSize={13}>
                Contact No : <span className="fw-bold">{contactNo}</span>
              </Typography>
            </Stack>
          </Box>
          {/* <Divider sx={{ my: 1 }} /> */}
          <Box
            className="scrollbar"
            sx={{
              // height: 'calc(100vh - 320px)',
              // overflow: 'auto',
              mb: 2,
              // '&::-webkit-scrollbar': {
              //   width: '0px',
              // },
            }}
          >
            {/* <TableContainer sx={{ height: 'calc(100vh - 470px)' }}> */}
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ '&:first-of-type': { paddingLeft: 0 } }}>Fee Title</TableCell>
                    <TableCell>Term Title</TableCell>
                    <TableCell align="right">Amount (INR)</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {termfeepaid?.map((item) => (
                    <TableRow key={item.receiptDetailsId}>
                      <TableCell sx={{ '&:first-of-type': { paddingLeft: 0 } }} color="secondary">
                        {item.feeTitle}
                      </TableCell>
                      <TableCell color="secondary"> {item.termTitle}</TableCell>
                      <TableCell align="right"> {item.totalAmount}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
            <Box sx={{ borderTop: '2px dashed gray' }} py={3} display="flex" justifyContent="space-between">
              <Typography variant="body2" fontWeight={700}>
                Total Amount Paid
              </Typography>
              <Typography variant="body2" fontWeight={700}>
                {/* {grandTotal} Display total amount */}
                {totalAmountPaid.toLocaleString()}
              </Typography>
            </Box>
            <TableContainer sx={{ mb: 2 }}>
              <Table stickyHeader>
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ '&:first-of-type': { paddingLeft: 0 } }}>Payment Mode</TableCell>
                    <TableCell>Reference No</TableCell>
                    <TableCell align="right">Amount</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {paymodeData?.map((item) => (
                    <TableRow key={item.paymentModeId}>
                      <TableCell sx={{ '&:first-of-type': { paddingLeft: 0 } }} color="secondary">
                        {item.paymentType}
                      </TableCell>
                      <TableCell> {item.chequeOrDDNo}</TableCell>
                      <TableCell align="right"> {item.amount}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
            <Stack className="amount_words">
              <Typography variant="body2" pb={1} color="secondary">
                Total amount in words :&nbsp;
                <span
                  className="fw-bold"
                  style={{ color: isLight ? theme.palette.common.black : theme.palette.common.white }}
                >
                  {grandTotalWords}
                </span>
              </Typography>
            </Stack>
          </Box>
        </Box>
      </Box>
    </OnlineRecieptRoot>
  );
}

export default OnlineReciept;
