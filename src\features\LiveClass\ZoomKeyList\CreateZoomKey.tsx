/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import { Stack, TextField, Button, Typography, Box, Select, MenuItem } from '@mui/material';
import { STATUS_OPTIONS } from '@/config/Selection';

function CreateZoomKey({ setCreatePopup }) {
  return (
    <Box mt={3}>
      <Stack sx={{ height: 'calc(100vh - 150px)' }}>
        <Typography mt={3} variant="subtitle2" fontSize={14} color="GrayText">
          Zoom Key Title
        </Typography>
        <TextField placeholder="Enter Key Title" fullWidth />
        <Typography mt={3} variant="subtitle2" fontSize={14} color="GrayText">
          Zoom User Name
        </Typography>
        <TextField placeholder="Enter User Name " name="" fullWidth />
        <Typography mt={3} variant="subtitle2" fontSize={14} color="GrayText">
          Zoom Password
        </Typography>
        <TextField placeholder="Enter Password " name="" fullWidth />
        <Typography mt={3} variant="subtitle2" fontSize={14} color="GrayText">
          Status
        </Typography>
        <Select
          name=""
          value=""
          fullWidth
          //   error={touched.classStatus && !!errors.classStatus}
        >
          {STATUS_OPTIONS.map((opt) => (
            <MenuItem key={opt.id} value={opt.id}>
              {opt.name}
            </MenuItem>
          ))}
        </Select>
      </Stack>
      <Box sx={{ justifyContent: 'center', pt: 3, position: 'relative' }}>
        <Stack spacing={2} direction="row" sx={{}}>
          <Button onClick={() => setCreatePopup(false)} fullWidth mt={3} variant="contained" color="secondary">
            Cancel
          </Button>
          <Button onClick={() => setCreatePopup(false)} fullWidth variant="contained" color="primary">
            Save
          </Button>
        </Stack>
      </Box>
    </Box>
  );
}

export default CreateZoomKey;
