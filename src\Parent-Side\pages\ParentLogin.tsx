import { breakPointsMinwidth } from '@/config/breakpoints';
import styled from 'styled-components';
import Page from '@/components/shared/Page';
import InfoCard from '@/features/login/InfoCard';
import LoginCarousel from '@/Parent-Side/features/loginParent/LoginCarousel';
import LoginForm from '@/Parent-Side/features/loginParent/LoginForm';
import { Button, Stack } from '@mui/material';
import { useState } from 'react';
import { Link } from 'react-router-dom';

const LoginRoot = styled.div`
  min-height: calc(100vh - 38px);
  padding: 0;
  padding-top: 1.25rem;
  display: flex;
  flex-direction: column;
  gap: 20px;
  justify-content: center;

  @media ${breakPointsMinwidth.lg} {
    padding: 2rem;
  }
`;

const DummyContent =
  ' Lorem Ipsum is simply dummy text of the printing and typesetting industry has been the industry&apos;s standard dummy text ever a type specimen book.';

function ParentLogin() {
  return (
    <Page title="ParentLogin">
      {/* <Stack position="absolute" top={10} right={10} direction="row" gap={2}>
        <Link to="/parent/pay-fee">
          <Button size="small" variant="contained" color="success" sx={{ py: 0.1, px: 1 }}>
            Online Pay
          </Button>
        </Link>
        <Link to="/parent/pay-fee-new">
          <Button size="small" variant="contained" color="info" sx={{ py: 0.1, px: 1 }}>
            Online Pay
          </Button>
        </Link>
        <Link to="/parent/reciept">
          <Button size="small" variant="contained" sx={{ py: 0.1, px: 1 }}>
            Online Receipt
          </Button>
        </Link>
        <Link to="/parent/pay-response">
          <Button size="small" variant="contained" color="success" sx={{ py: 0.1, px: 1 }}>
            Response Page
          </Button>
        </Link>
      </Stack> */}
      <LoginRoot className="container-fluid">
        <div className="row container-lg mx-auto">
          <div className="col-lg-7 d-none d-lg-flex flex-column justify-content-center">
            <LoginCarousel />
          </div>
          <div className="col-lg-5">
            <LoginForm />
          </div>
        </div>
        {/* <div className="row container-lg mx-auto">
          <div className="col-md-4 mb-2">
            <InfoCard content={DummyContent} url="/" />
          </div>
          <div className="col-md-4 mb-2">
            <InfoCard content={DummyContent} url="/" />
          </div>
          <div className="col-md-4 mb-2">
            <InfoCard content={DummyContent} url="/" />
          </div>
        </div> */}
      </LoginRoot>
    </Page>
  );
}

export default ParentLogin;
