import React, { FormEvent, useCallback, useEffect, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  Chip,
  FormControl,
  MenuItem,
  Select,
  SelectChangeEvent,
  Collapse,
  Tooltip,
} from '@mui/material';
import styled from 'styled-components';
import { MdAdd } from 'react-icons/md';
import DeleteIcon from '@mui/icons-material/Delete';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import { useAppSelector } from '@/hooks/useAppSelector';
import {
  getSubjectListData,
  getSubjectListPageInfo,
  getSubjectListStatus,
  getSubjectDeletingRecords,
  getSubjectSortColumn,
  getSubjectSortDirection,
  getSubjectSubmitting,
} from '@/config/storeSelectors';
import { setSortColumn, setSortDirection } from '@/store/Academics/SubjectManagement/subjectManagement.slice';
import {
  addNewSubject,
  deleteSubject,
  fetchSubjectList,
  updateSubject,
} from '@/store/Academics/SubjectManagement/subjectManagement.thunks';
import { SubjectListInfo, SubjectListRequest } from '@/types/AcademicManagement';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { STATUS_OPTIONS } from '@/config/Selection';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import SearchIcon from '@mui/icons-material/Search';
import CreateEditSubjectForm from './CreateEditSubjectForm';
import CreateSubjectMultiple from './CreateSubjectMultiple';

const ManageSubjectRoot = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 50px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

const DefaultSubjectInfo: SubjectListInfo = {
  subjectId: 0,
  subjectName: '',
  subjectDescription: '',
  subjectStatus: 1,
};

function ManageSubject() {
  const { confirm } = useConfirm();

  // const [forceDispatch, setForceDispatch] = useState(false);
  const [selectedSubjectDetail, setSelectedSubjectDetail] = useState<SubjectListInfo>(DefaultSubjectInfo);

  const dispatch = useAppDispatch();
  const [subjectNameFilter, setsubjectNameFilter] = useState('');
  const [subjectStatusFilter, setsubjectStatusFilter] = useState(-1);

  const SubjectListStatus = useAppSelector(getSubjectListStatus);
  const SubjectListData = useAppSelector(getSubjectListData);
  // const SubjectListError = useAppSelector(getSubjectListError);
  const paginationInfo = useAppSelector(getSubjectListPageInfo);
  const sortColumn = useAppSelector(getSubjectSortColumn);
  const sortDirection = useAppSelector(getSubjectSortDirection);
  const isSubmitting = useAppSelector(getSubjectSubmitting);
  const deletingRecords = useAppSelector(getSubjectDeletingRecords);

  const { pagenumber, pagesize, totalrecords } = paginationInfo;

  const currentSubjectListRequest = useMemo(
    () => ({
      pageNumber: pagenumber,
      pageSize: pagesize,
      sortColumn,
      sortDirection,
      filters: {
        subjectName: subjectNameFilter,
        subjectStatus: subjectStatusFilter,
      },
    }),
    [subjectNameFilter, subjectStatusFilter, pagenumber, pagesize, sortColumn, sortDirection]
  );

  const loadSubjectList = useCallback(
    (request: SubjectListRequest) => {
      dispatch(fetchSubjectList(request));
    },
    [dispatch]
  );

  const [drawerOpen, setDrawerOpen] = useState(false);
  const [showFilter, setShowFilter] = useState(false);

  const [view, setView] = useState<'SubjectList' | 'CreateClassMultiple'>('SubjectList');

  const handleAddNewSubject = () => {
    setSelectedSubjectDetail(DefaultSubjectInfo);
    setDrawerOpen(true);
  };

  const toggleDrawerClose = useCallback(() => setDrawerOpen(false), []);

  const handleSaveorEdit = useCallback(
    async (value: SubjectListInfo, mode: 'create' | 'edit') => {
      if (mode === 'create') {
        const { subjectId, ...rest } = value;
        const response = await dispatch(addNewSubject(rest)).unwrap();

        if (response.id > 0) {
          setDrawerOpen(false);
          const successMessage = <SuccessMessage message="Subject created successfully" />;
          await confirm(successMessage, 'Subject Created', { okLabel: 'Ok', showOnlyOk: true });

          loadSubjectList({
            ...currentSubjectListRequest,
            pageNumber: 1,
            sortColumn: 'subjectId',
            sortDirection: 'desc',
          });
        }
      } else {
        const response = await dispatch(updateSubject(value)).unwrap();
        if (response.rowsAffected > 0) {
          setDrawerOpen(false);
          const successMessage = <SuccessMessage message="Subject updated successfully" />;
          await confirm(successMessage, 'Subject Updated', { okLabel: 'Ok', showOnlyOk: true });

          loadSubjectList(currentSubjectListRequest);
        }
      }
    },
    [confirm, currentSubjectListRequest, dispatch, loadSubjectList]
  );

  const handleAddMultipleSubjectes = () => {
    setView('CreateClassMultiple');
  };

  const handlebackFromSubjectMultiple = () => {
    setView('SubjectList');
    loadSubjectList({ ...currentSubjectListRequest, pageNumber: 1, sortColumn: 'subjectId', sortDirection: 'asc' });
  };

  useEffect(() => {
    if (SubjectListStatus === 'idle') {
      loadSubjectList(currentSubjectListRequest);
    }
    // console.log('datass::', SubjectListData);
  }, [loadSubjectList, SubjectListStatus, currentSubjectListRequest]);

  const handleStatusChange = (e: SelectChangeEvent) => {
    const statusVal = parseInt(e.target.value, 10);
    setsubjectStatusFilter(statusVal);
    loadSubjectList({
      ...currentSubjectListRequest,
      filters: { ...currentSubjectListRequest.filters, subjectStatus: statusVal },
    });
  };

  const getRowKey = useCallback((row: SubjectListInfo) => row.subjectId, []);

  const handlePageChange = useCallback(
    (event: unknown, newPage: number) => {
      loadSubjectList({ ...currentSubjectListRequest, pageNumber: newPage + 1 });
    },
    [currentSubjectListRequest, loadSubjectList]
  );

  const handleChangeRowsPerPage = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const newPageSize = +event.target.value;
      loadSubjectList({ ...currentSubjectListRequest, pageNumber: 1, pageSize: newPageSize });
    },
    [currentSubjectListRequest, loadSubjectList]
  );

  const handleEditSubject = useCallback((SubjectObj: SubjectListInfo) => {
    setSelectedSubjectDetail(SubjectObj);
    setDrawerOpen(true);
  }, []);

  const currentItemCount = SubjectListData.length;

  const handleDeleteSubject = useCallback(
    async (SubjectObj: SubjectListInfo) => {
      const deleteConfirmMessage = (
        <DeleteMessage
          message={
            <div>
              Are you sure you want to delete the Subject <br />
              &quot;{SubjectObj.subjectName}&quot; ?
            </div>
          }
        />
      );
      if (await confirm(deleteConfirmMessage, 'Delete Subject?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
        const deleteResponse = await dispatch(deleteSubject(SubjectObj.subjectId)).unwrap();
        if (deleteResponse.deleted) {
          const deleteDoneMessage = <DeleteMessage message="Subject deleted successfully." />;
          await confirm(deleteDoneMessage, 'Deleted', { okLabel: 'Ok', showOnlyOk: true });
          let pageNumberToMove = pagenumber;
          if (paginationInfo.remainingpages === 0 && pagenumber > 1 && currentItemCount === 1) {
            pageNumberToMove = pagenumber - 1;
            loadSubjectList({ ...currentSubjectListRequest, pageNumber: pageNumberToMove });
          } else {
            loadSubjectList(currentSubjectListRequest);
          }
        }
      }
    },
    [
      confirm,
      currentSubjectListRequest,
      currentItemCount,
      dispatch,
      loadSubjectList,
      pagenumber,
      paginationInfo.remainingpages,
    ]
  );

  const handleSort = useCallback(
    (column: string) => {
      const newReq = { ...currentSubjectListRequest };
      if (column === sortColumn) {
        console.log('Toggling direction');
        const sortDirectionNew = sortDirection === 'asc' ? 'desc' : 'asc';
        newReq.pageNumber = 1;
        newReq.sortDirection = sortDirectionNew;
        dispatch(setSortDirection(sortDirectionNew));
      } else {
        newReq.pageNumber = 1;
        newReq.sortColumn = column;
        dispatch(setSortColumn(column));
      }

      loadSubjectList(newReq);
    },
    [currentSubjectListRequest, dispatch, loadSubjectList, sortColumn, sortDirection]
  );

  const SubjectListColumns: DataTableColumn<SubjectListInfo>[] = useMemo(
    () => [
      {
        name: 'subjectId',
        headerLabel: 'Subject Id',
        dataKey: 'subjectId',
        sortable: true,
      },
      {
        name: 'subjectName',
        dataKey: 'subjectName',
        headerLabel: 'Subject Name',
        sortable: true,
      },
      {
        name: 'subjectDescription',
        dataKey: 'subjectDescription',
        headerLabel: 'Subject Description',
      },
      {
        name: 'subjectStatus',
        headerLabel: 'Subject Status',
        sortable: true,
        renderCell: (row) => {
          return (
            <Chip
              size="small"
              label={row.subjectStatus === 0 ? 'Unpublished' : 'Published'}
              variant="outlined"
              color={row.subjectStatus === 1 ? 'success' : 'error'}
            />
          );
        },
      },
      {
        name: 'actions',
        headerLabel: 'Actions',
        renderCell: (row) => {
          return (
            <Stack direction="row" gap={1}>
              <IconButton size="small" onClick={() => handleEditSubject(row)} sx={{ padding: 0.5 }}>
                <ModeEditIcon />
              </IconButton>
              <IconButton size="small" color="error" sx={{ padding: 0.5 }} onClick={() => handleDeleteSubject(row)}>
                <DeleteIcon />
              </IconButton>
            </Stack>
          );
        },
      },
    ],
    [handleDeleteSubject, handleEditSubject]
  );

  const pageProps = useMemo(
    () => ({
      rowsPerPageOptions: [10, 20, 30],
      pageNumber: pagenumber - 1,
      pageSize: pagesize,
      totalRecords: totalrecords,
      onPageChange: handlePageChange,
      onRowsPerPageChange: handleChangeRowsPerPage,
    }),
    [handleChangeRowsPerPage, handlePageChange, pagenumber, pagesize, totalrecords]
  );

  const handleReset = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      setsubjectNameFilter('');
      setsubjectStatusFilter(-1);
      loadSubjectList({
        ...currentSubjectListRequest,
        pageNumber: 1,
        pageSize: 20,
        sortColumn: 'subjectId',
        sortDirection: 'desc',
        filters: {
          subjectName: '',
          subjectStatus: -1,
        },
      });
    },
    [currentSubjectListRequest, loadSubjectList]
  );

  return view === 'SubjectList' ? (
    <Page title="Manage Subject">
      <ManageSubjectRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between" pb={1} alignItems="center" flexWrap="wrap">
            <Stack direction="row" alignItems="center" justifyContent="space-between" flex={3} whiteSpace="nowrap">
              <Typography variant="h6" fontSize={17}>
                Manage Subject
              </Typography>
              <Tooltip title="Search">
                <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                  <SearchIcon />
                </IconButton>
              </Tooltip>
            </Stack>
            <Box
              display="flex"
              justifyContent="end"
              alignItems="center"
              columnGap={2}
              sx={{
                flexShrink: 0,
                flex: 1,
                '@media (max-width: 390px)': {
                  flexWrap: 'wrap',
                  rowGap: 1,
                },
              }}
            >
              <Button
                sx={{
                  borderRadius: '20px',
                  whiteSpace: 'nowrap',
                  '@media (max-width: 390px)': {
                    width: '100%',
                  },
                }}
                variant="outlined"
                size="small"
                onClick={handleAddNewSubject}
              >
                <MdAdd size="20px" /> Add Subject
              </Button>
              <Button
                sx={{
                  borderRadius: '20px',
                  whiteSpace: 'nowrap',
                  '@media (max-width: 390px)': {
                    width: '100%',
                  },
                }}
                size="small"
                variant="outlined"
                onClick={handleAddMultipleSubjectes}
              >
                <MdAdd size="20px" /> Add Multiple Subjectes
              </Button>
            </Box>
          </Stack>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate onReset={handleReset}>
                <Grid pb={2} pt={1} container spacing={3} alignItems="end">
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 300 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Subject Name
                      </Typography>
                      <TextField
                        name="subjectName"
                        value={subjectNameFilter}
                        onChange={(e) => {
                          setsubjectNameFilter(e.target.value);
                          loadSubjectList({
                            ...currentSubjectListRequest,
                            filters: { ...currentSubjectListRequest.filters, subjectName: e.target.value },
                          });
                        }}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 300 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Subject Status
                      </Typography>
                      <Select
                        labelId="subjectStatusFilter"
                        id="subjectStatusFilterSelect"
                        value={subjectStatusFilter?.toString() || '-1'}
                        onChange={handleStatusChange}
                      >
                        <MenuItem value={-1}>All</MenuItem>
                        {STATUS_OPTIONS.map((opt) => (
                          <MenuItem key={opt.id} value={opt.id}>
                            {opt.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12} sm={6}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      {/* <Button type="submit" variant="contained" color="primary" fullWidth>
                        Search
                      </Button> */}
                      <Button type="reset" variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>
            <Paper className="card-table-container" sx={{ marginTop: '12px' }}>
              <DataTable
                tableStyles={{ minWidth: { xs: '800px', lg: '1200px', xl: '100% ' } }}
                columns={SubjectListColumns}
                data={SubjectListData}
                getRowKey={getRowKey}
                fetchStatus="success"
                allowPagination
                allowSorting
                PaginationProps={pageProps}
                deletingRecords={deletingRecords}
                sortColumn={sortColumn}
                sortDirection={sortDirection}
                onSort={handleSort}
              />
            </Paper>
          </div>
        </Card>
      </ManageSubjectRoot>

      <TemporaryDrawer
        onClose={toggleDrawerClose}
        Title={selectedSubjectDetail === DefaultSubjectInfo ? 'Add New Subject' : 'Edit Subject Details'}
        state={drawerOpen}
        DrawerContent={
          <CreateEditSubjectForm
            subjectDetail={selectedSubjectDetail}
            onSave={handleSaveorEdit}
            onCancel={toggleDrawerClose}
            onClose={toggleDrawerClose}
            isSubmitting={isSubmitting}
          />
        }
      />
    </Page>
  ) : (
    <CreateSubjectMultiple onBackClick={handlebackFromSubjectMultiple} />
  );
}

export default ManageSubject;
