import React, { FormEvent, useCallback, useEffect, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  Chip,
  FormControl,
  MenuItem,
  Select,
  SelectChangeEvent,
  Collapse,
  Tooltip,
} from '@mui/material';
import styled from 'styled-components';
import { MdAdd } from 'react-icons/md';
import DeleteIcon from '@mui/icons-material/Delete';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import { useAppSelector } from '@/hooks/useAppSelector';
import {
  getSectionListData,
  getSectionListPageInfo,
  getSectionListStatus,
  getSectionDeletingRecords,
  getSectionSortColumn,
  getSectionSortDirection,
  getSectionSubmitting,
} from '@/config/storeSelectors';
import { setSortColumn, setSortDirection } from '@/store/Academics/SectionManagement/sectionManagement.slice';
import {
  addNewSection,
  deleteSection,
  fetchSectionList,
  updateSection,
} from '@/store/Academics/SectionManagement/sectionManagement.thunks';
import { SectionListInfo, SectionListRequest } from '@/types/AcademicManagement';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { STATUS_OPTIONS } from '@/config/Selection';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import SearchIcon from '@mui/icons-material/Search';
import CreateEditSectionForm from './CreateEditSectionForm';

const ManageSectionRoot = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 50px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

const DefaultSectionInfo: SectionListInfo = {
  sectionId: 0,
  sectionName: '',
  sectionStatus: 1,
};

function ManageSection() {
  const { confirm } = useConfirm();

  // const [forceDispatch, setForceDispatch] = useState(false);
  const [selectedSectionDetail, setSelectedSectionDetail] = useState<SectionListInfo>(DefaultSectionInfo);

  const dispatch = useAppDispatch();
  const [sectionNameFilter, setsectionNameFilter] = useState('');
  const [sectionStatusFilter, setsectionStatusFilter] = useState(-1);

  const SectionListStatus = useAppSelector(getSectionListStatus);
  const SectionListData = useAppSelector(getSectionListData);
  // const SectionListError = useAppSelector(getSectionListError);
  const paginationInfo = useAppSelector(getSectionListPageInfo);
  const sortColumn = useAppSelector(getSectionSortColumn);
  const sortDirection = useAppSelector(getSectionSortDirection);
  const isSubmitting = useAppSelector(getSectionSubmitting);
  const deletingRecords = useAppSelector(getSectionDeletingRecords);

  const { pagenumber, pagesize, totalrecords } = paginationInfo;

  const currentSectionListRequest = useMemo(
    () => ({
      pageNumber: pagenumber,
      pageSize: pagesize,
      sortColumn,
      sortDirection,
      filters: {
        sectionName: sectionNameFilter,
        sectionStatus: sectionStatusFilter,
      },
    }),
    [sectionNameFilter, sectionStatusFilter, pagenumber, pagesize, sortColumn, sortDirection]
  );

  const loadSectionList = useCallback(
    (request: SectionListRequest) => {
      dispatch(fetchSectionList(request));
    },
    [dispatch]
  );

  const [drawerOpen, setDrawerOpen] = useState(false);
  const [showFilter, setShowFilter] = useState(false);

  const handleAddNewSection = () => {
    setSelectedSectionDetail(DefaultSectionInfo);
    setDrawerOpen(true);
  };

  const toggleDrawerClose = useCallback(() => setDrawerOpen(false), []);

  const handleSaveorEdit = useCallback(
    async (value: SectionListInfo, mode: 'create' | 'edit') => {
      if (mode === 'create') {
        const { sectionId, ...rest } = value;
        const response = await dispatch(addNewSection(rest)).unwrap();

        if (response.id > 0) {
          setDrawerOpen(false);
          const successMessage = <SuccessMessage message="Section created successfully" />;
          await confirm(successMessage, 'Section Created', { okLabel: 'Ok', showOnlyOk: true });

          loadSectionList({
            ...currentSectionListRequest,
            pageNumber: 1,
            sortColumn: 'sectionId',
            sortDirection: 'desc',
          });
        }
      } else {
        const response = await dispatch(updateSection(value)).unwrap();
        if (response.rowsAffected > 0) {
          setDrawerOpen(false);
          const successMessage = <SuccessMessage message="Section updated successfully" />;
          await confirm(successMessage, 'Section Updated', { okLabel: 'Ok', showOnlyOk: true });

          loadSectionList(currentSectionListRequest);
        }
      }
    },
    [confirm, currentSectionListRequest, dispatch, loadSectionList]
  );

  useEffect(() => {
    if (SectionListStatus === 'idle') {
      loadSectionList(currentSectionListRequest);
    }
    // console.log('datass::', SectionListData);
  }, [loadSectionList, SectionListStatus, currentSectionListRequest]);

  const handleStatusChange = (e: SelectChangeEvent) => {
    const statusVal = parseInt(e.target.value, 10);
    setsectionStatusFilter(statusVal);
    loadSectionList({
      ...currentSectionListRequest,
      filters: { ...currentSectionListRequest.filters, sectionStatus: statusVal },
    });
  };

  const getRowKey = useCallback((row: SectionListInfo) => row.sectionId, []);

  const handlePageChange = useCallback(
    (event: unknown, newPage: number) => {
      loadSectionList({ ...currentSectionListRequest, pageNumber: newPage + 1 });
    },
    [currentSectionListRequest, loadSectionList]
  );

  const handleChangeRowsPerPage = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const newPageSize = +event.target.value;
      loadSectionList({ ...currentSectionListRequest, pageNumber: 1, pageSize: newPageSize });
    },
    [currentSectionListRequest, loadSectionList]
  );

  const handleEditSection = useCallback((SectionObj: SectionListInfo) => {
    setSelectedSectionDetail(SectionObj);
    setDrawerOpen(true);
  }, []);

  const currentItemCount = SectionListData.length;

  const handleDeleteSection = useCallback(
    async (SectionObj: SectionListInfo) => {
      const deleteConfirmMessage = (
        <DeleteMessage
          message={
            <div>
              Are you sure you want to delete the Section <br />
              &quot;{SectionObj.sectionName}&quot; ?
            </div>
          }
        />
      );
      if (await confirm(deleteConfirmMessage, 'Delete Section?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
        const deleteResponse = await dispatch(deleteSection(SectionObj.sectionId)).unwrap();
        if (deleteResponse.deleted) {
          const deleteDoneMessage = <DeleteMessage message="Section deleted successfully." />;
          await confirm(deleteDoneMessage, 'Deleted', { okLabel: 'Ok', showOnlyOk: true });
          let pageNumberToMove = pagenumber;
          if (paginationInfo.remainingpages === 0 && pagenumber > 1 && currentItemCount === 1) {
            pageNumberToMove = pagenumber - 1;
            loadSectionList({ ...currentSectionListRequest, pageNumber: pageNumberToMove });
          } else {
            loadSectionList(currentSectionListRequest);
          }
        }
      }
    },
    [
      confirm,
      currentSectionListRequest,
      currentItemCount,
      dispatch,
      loadSectionList,
      pagenumber,
      paginationInfo.remainingpages,
    ]
  );

  const handleSort = useCallback(
    (column: string) => {
      const newReq = { ...currentSectionListRequest };
      if (column === sortColumn) {
        console.log('Toggling direction');
        const sortDirectionNew = sortDirection === 'asc' ? 'desc' : 'asc';
        newReq.pageNumber = 1;
        newReq.sortDirection = sortDirectionNew;
        dispatch(setSortDirection(sortDirectionNew));
      } else {
        newReq.pageNumber = 1;
        newReq.sortColumn = column;
        dispatch(setSortColumn(column));
      }

      loadSectionList(newReq);
    },
    [currentSectionListRequest, dispatch, loadSectionList, sortColumn, sortDirection]
  );

  const SectionListColumns: DataTableColumn<SectionListInfo>[] = useMemo(
    () => [
      {
        name: 'sectionId',
        headerLabel: 'Section Id',
        dataKey: 'sectionId',
        sortable: true,
      },
      {
        name: 'sectionName',
        dataKey: 'sectionName',
        headerLabel: 'Section Name',
        sortable: true,
      },
      {
        name: 'sectionStatus',
        headerLabel: 'Section Status',
        sortable: true,
        renderCell: (row) => {
          return (
            <Chip
              size="small"
              label={row.sectionStatus === 0 ? 'Unpublished' : 'Published'}
              variant="outlined"
              color={row.sectionStatus === 1 ? 'success' : 'error'}
            />
          );
        },
      },
      {
        name: 'actions',
        headerLabel: 'Actions',
        renderCell: (row) => {
          return (
            <Stack direction="row" gap={1}>
              <IconButton size="small" onClick={() => handleEditSection(row)} sx={{ padding: 0.5 }}>
                <ModeEditIcon />
              </IconButton>
              <IconButton size="small" color="error" sx={{ padding: 0.5 }} onClick={() => handleDeleteSection(row)}>
                <DeleteIcon />
              </IconButton>
            </Stack>
          );
        },
      },
    ],
    [handleDeleteSection, handleEditSection]
  );

  const pageProps = useMemo(
    () => ({
      rowsPerPageOptions: [10, 20, 30, 50, 100, 200, 300, 500],
      pageNumber: pagenumber - 1,
      pageSize: pagesize,
      totalRecords: totalrecords,
      onPageChange: handlePageChange,
      onRowsPerPageChange: handleChangeRowsPerPage,
    }),
    [handleChangeRowsPerPage, handlePageChange, pagenumber, pagesize, totalrecords]
  );

  const handleReset = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      setsectionNameFilter('');
      setsectionStatusFilter(-1);
      loadSectionList({
        ...currentSectionListRequest,
        pageNumber: 1,
        pageSize: 20,
        sortColumn: 'sectionId',
        sortDirection: 'desc',
        filters: {
          sectionName: '',
          sectionStatus: -1,
        },
      });
    },
    [currentSectionListRequest, loadSectionList]
  );

  return (
    <Page title="Manage Section">
      <ManageSectionRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between" pb={1} alignItems="center" flexWrap="wrap">
            <Stack direction="row" alignItems="center" justifyContent="space-between" flex={5} whiteSpace="nowrap">
              <Typography variant="h6" fontSize={17}>
                Manage Section
              </Typography>
              <Tooltip title="Search">
                <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                  <SearchIcon />
                </IconButton>
              </Tooltip>
            </Stack>
            <Box
              display="flex"
              justifyContent="end"
              alignItems="center"
              columnGap={2}
              sx={{
                flexShrink: 0,
                flex: 1,
                '@media (max-width: 340px)': {
                  flexWrap: 'wrap',
                  rowGap: 1,
                },
              }}
            >
              <Button
                sx={{
                  borderRadius: '20px',
                  whiteSpace: 'nowrap',
                  '@media (max-width: 340px)': {
                    width: '100%',
                  },
                }}
                variant="outlined"
                size="small"
                onClick={handleAddNewSection}
              >
                <MdAdd size="20px" /> Add Section
              </Button>
            </Box>
          </Stack>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate onReset={handleReset}>
                <Grid pb={2} pt={1} container spacing={3} alignItems="end">
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 300 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Section Name
                      </Typography>
                      <TextField
                        name="sectionName"
                        value={sectionNameFilter}
                        onChange={(e) => {
                          setsectionNameFilter(e.target.value);
                          loadSectionList({
                            ...currentSectionListRequest,
                            filters: { ...currentSectionListRequest.filters, sectionName: e.target.value },
                          });
                        }}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 300 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Section Status
                      </Typography>
                      <Select
                        labelId="sectionStatusFilter"
                        id="sectionStatusFilterSelect"
                        value={sectionStatusFilter?.toString() || '-1'}
                        onChange={handleStatusChange}
                      >
                        <MenuItem value={-1}>All</MenuItem>
                        {STATUS_OPTIONS.map((opt) => (
                          <MenuItem key={opt.id} value={opt.id}>
                            {opt.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12} sm={6}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      {/* <Button type="submit" variant="contained" color="primary" fullWidth>
                        Search
                      </Button> */}
                      <Button type="reset" variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>
            <Paper className="card-table-container" sx={{ marginTop: '12px' }}>
              <DataTable
                tableStyles={{ minWidth: { xs: '700px', lg: '1200px', xl: '100% ' } }}
                columns={SectionListColumns}
                data={SectionListData}
                getRowKey={getRowKey}
                fetchStatus="success"
                allowPagination
                allowSorting
                PaginationProps={pageProps}
                deletingRecords={deletingRecords}
                sortColumn={sortColumn}
                sortDirection={sortDirection}
                onSort={handleSort}
              />
            </Paper>
          </div>
        </Card>
      </ManageSectionRoot>

      <TemporaryDrawer
        onClose={toggleDrawerClose}
        Title={selectedSectionDetail === DefaultSectionInfo ? 'Add New Section' : 'Edit Section Details'}
        state={drawerOpen}
        DrawerContent={
          <CreateEditSectionForm
            sectionDetail={selectedSectionDetail}
            onSave={handleSaveorEdit}
            onCancel={toggleDrawerClose}
            onClose={toggleDrawerClose}
            isSubmitting={isSubmitting}
          />
        }
      />
    </Page>
  );
}

export default ManageSection;
