import React, { useEffect, useState } from 'react';
import { LinearProgress, Box } from '@mui/material';
import { motion, AnimatePresence } from 'framer-motion';
import { useTheme } from '@mui/material';

interface ProgressBarProps {
  targetPercentage: number;
  delay: number; // Delay for each progress bar
}

const AnimatedProgressBar: React.FC<ProgressBarProps> = ({ targetPercentage, delay }) => {
  const theme = useTheme();
  const [progress, setProgress] = useState(0);
  const [visiblePercentage, setVisiblePercentage] = useState(0);

  useEffect(() => {
    let timeout: NodeJS.Timeout;

    const updateProgress = (current: number) => {
      if (current < targetPercentage) {
        const speed = current < 20 ? 5 : 3; // Slow for first 20%, then faster
        timeout = setTimeout(() => {
          setProgress(current + 1);
          if ((current + 1) % 10 === 0) {
            setVisiblePercentage(current + 1);
          }
          updateProgress(current + 1);
        }, speed);
      }
    };

    // Delay the start of each progress bar
    const startTimeout = setTimeout(() => {
      updateProgress(0);
    }, delay);

    return () => {
      clearTimeout(timeout);
      clearTimeout(startTimeout);
    };
  }, [targetPercentage, delay]);
  // Dynamic color change based on progress
  const getProgressColor = () => {
    if (progress <= 10) return theme.palette.grey[400];
    if (progress <= 40) return theme.palette.common.black;
    if (progress <= 60) return theme.palette.common.black;
    if (progress <= 80) return theme.palette.common.black;
    return theme.palette.common.black;
  };

  return (
    <Box display="flex" alignItems="center" width="100%" mb={2}>
      {/* Progress Bar */}
      <LinearProgress
        variant="determinate"
        value={progress}
        sx={{
          flexGrow: 1,
          height: 10,
          borderRadius: 5,
          backgroundColor: theme.palette.grey[300],
          '& .MuiLinearProgress-bar': {
            backgroundColor: getProgressColor(),
            transition: 'width 0.2s ease-in-out',
          },
        }}
      />

      {/* Animated Percentage Number */}
      <Box sx={{ ml: 2, position: 'relative', height: '24px', width: '40px', overflow: 'hidden' }}>
        <AnimatePresence>
          <motion.div
            key={visiblePercentage}
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: -20, opacity: 0 }}
            transition={{ duration: 0.2 }}
            style={{
              position: 'absolute',
              width: '100%',
              textAlign: 'center',
              fontWeight: 'bold',
              fontSize: '13px',
              marginTop: 2.5,
            }}
          >
            {progress}%
          </motion.div>
        </AnimatePresence>
      </Box>
    </Box>
  );
};
export default AnimatedProgressBar;
// // Component to display multiple progress bars with staggered delays
// const MultipleProgressBars: React.FC = () => {
//   const progressBars = [
//     { targetPercentage: 90, delay: 0 },
//     { targetPercentage: 75, delay: 500 },
//     { targetPercentage: 60, delay: 1000 },
//     { targetPercentage: 45, delay: 1500 },
//     { targetPercentage: 30, delay: 2000 },
//   ];

//   return (
//     <Box width="50%" mx="auto" mt={5}>
//       {progressBars.map((bar, index) => (
//         <AnimatedProgressBar key={index} targetPercentage={bar.targetPercentage} delay={bar.delay} />
//       ))}
//     </Box>
//   );
// };

// export default MultipleProgressBars;
