import { YEAR_SELECT } from '@/config/Selection';
import { <PERSON><PERSON><PERSON>, Avatar, TextField, Stack, useTheme, Box, Divider, Button, Autocomplete } from '@mui/material';
import styled from 'styled-components';

const ViewStaffFormRoot = styled.div`
  width: 100%;
`;

const ViewStaffForm = () => {
  const theme = useTheme();

  return (
    <ViewStaffFormRoot>
      <Divider sx={{ mt: 2 }} />
      <Box sx={{ py: ' 0.5rem', px: ' 1.5rem' }}>
        <Stack direction="row" gap={2} alignItems="center">
          <Avatar alt="" sx={{ width: 40, height: 40 }} src="" />
          <Typography variant="subtitle1"> Staff Name</Typography>
        </Stack>
        <Stack mt={2}>
          <Typography variant="h6" fontSize={14}>
            First Name
          </Typography>
          <Autocomplete
            options={YEAR_SELECT}
            renderInput={(params) => <TextField {...params} placeholder="Select Exams" />}
          />
          <Stack direction="row" justifyContent="space-around" sx={{ pt: { xs: 3.5, md: 3.79 } }}>
            <Button fullWidth variant="contained" sx={{ mx: 2 }} color="secondary">
              Cancel
            </Button>
            <Button fullWidth variant="contained" sx={{ mx: 2 }} color="primary">
              Assign
            </Button>
          </Stack>
        </Stack>
      </Box>
    </ViewStaffFormRoot>
  );
};

export default ViewStaffForm;
