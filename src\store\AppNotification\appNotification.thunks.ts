import api from '@/api';
import { CreateResponse, DeleteResponse, SendResponse } from '@/types/Common';
import {
  DeleteMultipleNotificationListType,
  NotificationCreateRequest,
  NotificationDataType,
  NotificationRequest,
  SendToClassDivisionType,
  SendToClassSectionType,
  SendToParentsRequest,
  SendToParentsResponse,
  SendToStaffType,
} from '@/types/Notification';
import { createAsyncThunk } from '@reduxjs/toolkit';

export const createNotification = createAsyncThunk<CreateResponse, NotificationCreateRequest, { rejectValue: string }>(
  'notification/create',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.Notification.CreateNewNotification(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in creating Notification List');
    }
  }
);

export const fileUpload = createAsyncThunk<any, { files: File[] }, { rejectValue: string }>(
  'notification/FileUpload',
  async ({ files }, { rejectWithValue }) => {
    try {
      // Assuming your API method expects FormData for file upload
      const response = await api.Notification.FileUpload(files);

      return response;
    } catch {
      return rejectWithValue('Something went wrong in Upload files');
    }
  }
);

export const fetchNotificationList = createAsyncThunk<
  NotificationDataType[],
  NotificationRequest,
  { rejectValue: string }
>('notification/notificationList', async (request, { rejectWithValue }) => {
  try {
    const response = await api.Notification.GetNotificationList(request);
    console.log('request', request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching Notification List');
  }
});

export const DeleteNotificationList = createAsyncThunk<
  DeleteResponse,
  { adminId: number | undefined; notificationId: number },
  { rejectValue: string }
>('notification/delete', async ({ adminId, notificationId }, { rejectWithValue }) => {
  try {
    const response = await api.Notification.DeleteNotificationList(adminId, notificationId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in deleting Notification List');
  }
});

export const DeleteMultipleNotificationList = createAsyncThunk<
  DeleteResponse,
  DeleteMultipleNotificationListType[],
  { rejectValue: string }
>('notification/deleteMultiple', async (request, { rejectWithValue }) => {
  try {
    const response = await api.Notification.DeleteMultipleNotificationList(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in deleting Notification List');
  }
});
// -----
export const notificationSendToParentsList = createAsyncThunk<
  SendToParentsResponse,
  SendToParentsRequest[],
  { rejectValue: string }
>('notification/SendToParentsList', async (request, { rejectWithValue }) => {
  try {
    const response = await api.Notification.SendToParentsList(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in sending notification To Parents List');
  }
});

export const notificationSendToStaffList = createAsyncThunk<
  SendToStaffType,
  SendToStaffType[],
  { rejectValue: string }
>('notification/SendToStaffList', async (request, { rejectWithValue }) => {
  try {
    const response = await api.Notification.SendToStaffList(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in sending notification To Staff List');
  }
});
export const notificationSendToClassDivision = createAsyncThunk<
  SendToClassDivisionType,
  SendToClassDivisionType[],
  { rejectValue: string }
>('notification/SendToClassDivision', async (request, { rejectWithValue }) => {
  try {
    const response = await api.Notification.SendToClassDivision(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in sending notification To ClassDivision List');
  }
});

export const notificationSendToClassSection = createAsyncThunk<
  SendToClassSectionType,
  SendToClassSectionType[],
  { rejectValue: string }
>('notification/SendToClassSection', async (request, { rejectWithValue }) => {
  try {
    const response = await api.Notification.SendToClassSection(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in sending notification To ClassSection List');
  }
});

export const notificationSendToAllParents = createAsyncThunk<
  SendResponse,
  { adminId: number | undefined; academicId: number | undefined; notificationId: number | undefined },
  { rejectValue: string }
>('notification/SendToAllParents', async ({ adminId, academicId, notificationId }, { rejectWithValue }) => {
  try {
    const response = await api.Notification.SendToAllParents(adminId, academicId, notificationId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in sending notifications To All Parents');
  }
});
export const notificationSendToAllStaff = createAsyncThunk<
  SendResponse,
  { adminId: number | undefined; academicId: number | undefined; notificationId: number | undefined },
  { rejectValue: string }
>('notification/SendToAllStaff', async ({ adminId, academicId, notificationId }, { rejectWithValue }) => {
  try {
    const response = await api.Notification.SendToAllStaff(adminId, academicId, notificationId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in sending notifications To All Staff');
  }
});
