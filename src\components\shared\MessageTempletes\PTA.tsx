/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable no-nested-ternary */
/* eslint-disable react/no-array-index-key */
/* eslint-disable jsx-a11y/alt-text */
import React, { FormEvent, useCallback, useState } from 'react';
import {
  Divider,
  Grid,
  Paper,
  Stack,
  Button,
  Typography,
  TextField,
  Box,
  FormControl,
  Select,
  MenuItem,
  SelectChangeEvent,
  useTheme,
  IconButton,
  Tooltip,
} from '@mui/material';
import styled from 'styled-components';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useAppSelector } from '@/hooks/useAppSelector';
import { getPtaListStatus, getMessageTempSubmitting } from '@/config/storeSelectors';
import useAuth from '@/hooks/useAuth';
import { fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import { fetchPtaList, messageSendToPtaList } from '@/store/MessageBox/messageBox.thunks';
import LoadingButton from '@mui/lab/LoadingButton';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import SendIcon from '@mui/icons-material/Send';
import LoadingMsg from '@/assets/MessageIcons/loading-message.gif';
import { SendToPtaType, PtaDataListRequest, PtaDataType } from '@/types/MessageBox';
import { PTA_ROLE_SELECT } from '@/config/Selection';
import ErrorMsg from '@/assets/MessageIcons/error-message.gif';
import ErrorMsg1 from '@/assets/MessageIcons/error-message1.gif';
import ErrorMsg2 from '@/assets/MessageIcons/error-message2.gif';
import SuccessMsg from '@/assets/MessageIcons/message-success.gif';
import successIcon from '@/assets/MessageIcons/success.json';
import Lottie from 'lottie-react';
import errorIcon from '@/assets/MessageIcons/error-message.json';
import { breakPointsMaxwidth } from '@/config/breakpoints';
import { SuccessMessage } from '../Popup/SuccessMessage';
import { ErrorMessage } from '../Popup/ErrorMessage';
import { useConfirm } from '../Popup/Confirmation';
import LoadingPopup from '../Popup/LoadingPopup';
import { LoadingMessage } from '../Popup/LoadingMessage';
import { SendPopupProps } from '@/types/Common';
import { voiceMessageSendToPtaList } from '@/store/VoiceMessage/voiceMessage.thunk';
import { SendVoiceToPtaRequest } from '@/types/VoiceMessage';

const PtaRoot = styled.div`
  width: 100%;
  padding: 8px 24px;
  .delivery-report-btn {
    @media ${breakPointsMaxwidth.xs} {
      width: 100%;
    }
  }
  .send-btn {
    @media ${breakPointsMaxwidth.sm} {
      width: 50%;
    }
    @media ${breakPointsMaxwidth.xs} {
      width: 100%;
    }
  }
  .cancel-btn {
    @media ${breakPointsMaxwidth.xs} {
      width: 25%;
    }
    @media ${breakPointsMaxwidth.sm} {
      width: 50%;
    }
  }
`;

interface PtaDataWithStatus extends PtaDataType {
  sendStatus: 'Success' | 'Failed' | '' | undefined;
}

function Pta({ messageId, voiceId, isSubmitting, templateId }: SendPopupProps) {
  const { user } = useAuth();
  const theme = useTheme();
  const dispatch = useAppDispatch();
  const { confirm } = useConfirm();
  const adminId: number = user ? user.accountId : 0;
  const [view, setView] = useState<'main' | 'report'>('main');
  const [ptaMemberNameFilter, setPtaMemberNameFilter] = useState('');
  const [ptaMemberRoleFilter, setPtaMemberRoleFilter] = useState('-1');
  const [ptaMemberMobileFilter, setPtaMemberMobileFilter] = useState('');
  const [ptaMemberEmailFilter, setPtaMemberEmailFilter] = useState('');
  const [ptaMemberStatusFilter, setPtaMemberStatusFilter] = useState('-1');
  const [sendResults, setSendResults] = useState<PtaDataWithStatus[]>([]);
  const [selectedRows, setSelectedRows] = useState<PtaDataWithStatus[]>([]);
  // const PtaListData = useAppSelector(getPtaListData);
  const PtaListStatus = useAppSelector(getPtaListStatus);
  const [selectedData, setSelectedData] = useState<PtaDataWithStatus[]>([]);
  const [disabledCheckBoxes, setDisabledCheckBoxes] = useState<boolean[]>([]);
  const [errMsgTooltip, setErrMsgTooltip] = useState<React.ReactNode>(undefined);

  const initialPtaListRequest: PtaDataListRequest = React.useMemo(
    () => ({
      ptaMemberName: '',
      ptaMemberRole: '-1',
      ptaMemberMobile: '',
      ptaMemberEmail: '',
      ptaMemberStatus: '-1',
    }),
    []
  );
  const currentPtaListRequest: PtaDataListRequest = React.useMemo(
    () => ({
      ptaMemberName: ptaMemberNameFilter,
      ptaMemberRole: ptaMemberRoleFilter,
      ptaMemberMobile: ptaMemberMobileFilter,
      ptaMemberEmail: ptaMemberEmailFilter,
      ptaMemberStatus: ptaMemberStatusFilter,
    }),
    [ptaMemberNameFilter, ptaMemberRoleFilter, ptaMemberMobileFilter, ptaMemberEmailFilter, ptaMemberStatusFilter]
  );

  const loadPtaList = useCallback(
    async (request: PtaDataListRequest) => {
      try {
        const data = await dispatch(fetchPtaList(request)).unwrap();
        const dataWithStatus = data.map((item) => ({
          ...item,
          sendStatus: sendResults.find((i) => i.ptaMemberMobile === item.ptaMemberMobile)?.sendStatus,
        }));
        console.log(dataWithStatus);
        setSelectedData(dataWithStatus);
        setSelectedRows([]);
      } catch (error) {
        console.error('Error loading list:', error);
      }
    },
    [dispatch, setSelectedData, sendResults]
  );

  React.useEffect(() => {
    dispatch(fetchYearList(adminId));
    loadPtaList(initialPtaListRequest);
  }, [dispatch, adminId]);

  // const handleStatusChange = (e: SelectChangeEvent) => {
  //   setPtaMemberStatusFilter(STATUS_OPTIONS.filter((item) => item.id === e.target.value)[0].id);
  //   loadPtaList({ ...currentPtaListRequest, ptaMemberStatus: e.target.value });
  //   setSelectedRows([]);
  // };
  const handleRoleChange = (e: SelectChangeEvent) => {
    setPtaMemberRoleFilter(PTA_ROLE_SELECT.filter((item) => item.id === e.target.value)[0].id);
    loadPtaList({ ...currentPtaListRequest, ptaMemberRole: e.target.value });
    setSelectedRows([]);
  };

  const handleReset = useCallback((e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setPtaMemberNameFilter('');
    setPtaMemberRoleFilter('-1');
    setPtaMemberMobileFilter('');
    setPtaMemberEmailFilter('');
    setPtaMemberStatusFilter('-1');
    setSelectedRows([]);
  }, []);

  const handleSubmit = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      loadPtaList(currentPtaListRequest);
      setSelectedRows([]);
    },
    [loadPtaList, currentPtaListRequest]
  );

  const handleBack = useCallback(() => {
    setView('main');
  }, []);

  const handleCancel = useCallback(() => {
    setSelectedRows([]);
    loadPtaList(currentPtaListRequest);
    setDisabledCheckBoxes([]);
  }, [currentPtaListRequest, loadPtaList]);

  const handleSendMessage = useCallback(async () => {
    try {
      console.log(selectedRows);
      if (!selectedRows || selectedRows.length === 0) {
        const errorMessage = <ErrorMessage icon={ErrorMsg1} message="Please Select Atleast one PTA Member to sent" />;
        await confirm(errorMessage, 'PTA Member not selected', { okLabel: 'Ok', showOnlyOk: true });
        return;
      }
      const sendRequests: SendToPtaType[] = await Promise.all(
        selectedRows?.map(async (item) => {
          const { ptaMemberName, ptaMemberMobile } = item;
          const sendReq = { messageId, adminId, ptaMemberName, ptaMemberMobile };
          return sendReq;
        }) || []
      );
      const messageSendCount = sendRequests.length;

      const actionResult = await dispatch(messageSendToPtaList(sendRequests));
      if (actionResult && Array.isArray(actionResult.payload)) {
        const results: PtaDataWithStatus[] = actionResult.payload;

        const errorMessages = results.find((result) => result.sendStatus === 'Failed');
        const successMessages = results.find((result) => result.sendStatus === 'Success');
        if (successMessages) {
          const successMessage = (
            <SuccessMessage icon={SuccessMsg} message={`Message sent to ${messageSendCount} Recipient.`} />
          );
          await confirm(successMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        } else if (errorMessages) {
          setErrMsgTooltip(
            <Typography variant="subtitle2" fontSize={12} style={{ color: theme.palette.error.main }}>
              No messages sent to PTA Member, Please check the numbers and Try again
            </Typography>
          );
          const errorMessage = (
            <ErrorMessage
              icon={ErrorMsg2}
              message="No messages sent to PTA Member, Please check the numbers and Try again"
            />
          );
          await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        } else {
          setErrMsgTooltip(
            <Typography variant="subtitle2" fontSize={12} style={{ color: theme.palette.error.main }}>
              Error sending messages to PTA Member, Please recheck and Try again{' '}
            </Typography>
          );
          const errorMessage = (
            <ErrorMessage
              icon={ErrorMsg}
              message="Error sending messages to PTA Member, Please recheck and Try again"
            />
          );
          await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        }
        setSendResults([...sendResults, ...results]);
        const dataResult = selectedData.map((item) => ({
          ...item,
          sendStatus:
            item.sendStatus !== 'Success' && item.sendStatus !== 'Failed'
              ? results.find((result) => result.ptaMemberMobile === item.ptaMemberMobile)?.sendStatus
              : item.sendStatus,
        }));

        // setSelectedRows([]);
        setSelectedData(dataResult);
      } else {
        const errorMessage = <ErrorMessage icon={ErrorMsg1} message="Error while sending messages, Please Try again" />;
        await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
      }
      setSelectedRows([]);
      // Update disabled state for each selected row
      const updatedDisabledCheckBoxes = selectedData.map((selectedItem) =>
        selectedRows.includes(selectedItem)
          ? !disabledCheckBoxes[selectedData.indexOf(selectedItem)]
          : disabledCheckBoxes[selectedData.indexOf(selectedItem)]
      );

      setDisabledCheckBoxes(updatedDisabledCheckBoxes);
    } catch (error) {
      console.error('Error sending messages:', error);
    }
  }, [dispatch, confirm, messageId, selectedRows, setSelectedRows, adminId, selectedData, sendResults]);

  const handleSendVoiceMessage = useCallback(async () => {
    try {
      console.log(selectedRows);
      if (!selectedRows || selectedRows.length === 0) {
        const errorMessage = <ErrorMessage icon={ErrorMsg1} message="Please Select Atleast one PTA Member to sent" />;
        await confirm(errorMessage, 'PTA Member not selected', { okLabel: 'Ok', showOnlyOk: true });
        return;
      }
      const sendRequests: SendVoiceToPtaRequest[] = await Promise.all(
        selectedRows?.map(async (item) => {
          const { ...rest } = item;
          const sendReq = { voiceId, adminId, templateId, ...rest };
          return sendReq;
        }) || []
      );
      const messageSendCount = sendRequests.length;

      const actionResult = await dispatch(voiceMessageSendToPtaList(sendRequests));
      if (actionResult && Array.isArray(actionResult.payload)) {
        const results: PtaDataWithStatus[] = actionResult.payload;

        const errorMessages = results.find((result) => result.sendStatus === 'Failed');
        const successMessages = results.find((result) => result.sendStatus === 'Success');
        if (successMessages) {
          const successMessage = (
            <SuccessMessage icon={SuccessMsg} message={`Message sent to ${messageSendCount} Recipient.`} />
          );
          await confirm(successMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        } else if (errorMessages) {
          setErrMsgTooltip(
            <Typography variant="subtitle2" fontSize={12} style={{ color: theme.palette.error.main }}>
              No messages sent to PTA Member, Please check the numbers and Try again
            </Typography>
          );
          const errorMessage = (
            <ErrorMessage
              icon={ErrorMsg2}
              message="No messages sent to PTA Member, Please check the numbers and Try again"
            />
          );
          await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        } else {
          setErrMsgTooltip(
            <Typography variant="subtitle2" fontSize={12} style={{ color: theme.palette.error.main }}>
              Error sending messages to PTA Member, Please recheck and Try again{' '}
            </Typography>
          );
          const errorMessage = (
            <ErrorMessage
              icon={ErrorMsg}
              message="Error sending messages to PTA Member, Please recheck and Try again"
            />
          );
          await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        }
        setSendResults([...sendResults, ...results]);
        const dataResult = selectedData.map((item) => ({
          ...item,
          sendStatus:
            item.sendStatus !== 'Success' && item.sendStatus !== 'Failed'
              ? results.find((result) => result.ptaMemberMobile === item.ptaMemberMobile)?.sendStatus
              : item.sendStatus,
        }));

        // setSelectedRows([]);
        setSelectedData(dataResult);
      } else {
        const errorMessage = <ErrorMessage icon={ErrorMsg1} message="Error while sending messages, Please Try again" />;
        await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
      }
      setSelectedRows([]);
      // Update disabled state for each selected row
      const updatedDisabledCheckBoxes = selectedData.map((selectedItem) =>
        selectedRows.includes(selectedItem)
          ? !disabledCheckBoxes[selectedData.indexOf(selectedItem)]
          : disabledCheckBoxes[selectedData.indexOf(selectedItem)]
      );

      setDisabledCheckBoxes(updatedDisabledCheckBoxes);
    } catch (error) {
      console.error('Error sending messages:', error);
    }
  }, [dispatch, confirm, messageId, selectedRows, setSelectedRows, adminId, selectedData, sendResults]);

  const getRowKey = useCallback((row: PtaDataWithStatus) => row.ptaMemberId, []);

  const getStatusIcon = (row: PtaDataWithStatus) => {
    let iconComponent;

    if (row.sendStatus === 'Failed') {
      iconComponent = (
        <Tooltip title={errMsgTooltip} placement="left">
          <Stack>
            <Lottie animationData={errorIcon} loop={false} style={{ width: '30px' }} />
          </Stack>
        </Tooltip>
      );
    } else if (row.sendStatus === 'Success') {
      iconComponent = (
        <Stack>
          <Lottie animationData={successIcon} loop={false} style={{ width: '30px' }} />
        </Stack>
      );
    } else if (row.sendStatus === null) {
      iconComponent = 'empty';
    } else {
      iconComponent = null;
    }

    return <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>{iconComponent}</Box>;
  };
  const PtaListColumns: DataTableColumn<PtaDataWithStatus>[] = [
    {
      name: 'status',
      renderCell: getStatusIcon,
    },
    {
      name: 'slNo',
      headerLabel: 'Sl.No',
      renderCell: (row, index) => {
        return <Typography variant="subtitle2">{index + 1}</Typography>;
      },
      sortable: true,
    },
    {
      name: 'studentName',
      headerLabel: 'Name',
      renderCell: (row) => {
        return (
          <Stack direction="row" gap={1} alignItems="center">
            <Typography variant="subtitle2">{row.ptaMemberName}</Typography>
          </Stack>
        );
      },
    },
    {
      name: 'ptaMemberRole',
      headerLabel: 'Role',
      renderCell: (row) => {
        return (
          <Typography variant="subtitle1">
            {row && row.ptaMemberRole && PTA_ROLE_SELECT.find((item) => item.id === row.ptaMemberRole.toString())?.role}
          </Typography>
        );
      },
    },
    {
      name: 'mobileNumber',
      dataKey: 'ptaMemberMobile',
      headerLabel: 'Mobile Number',
    },
  ];
  return (
    <PtaRoot>
      <Box>
        <Divider />
        {view === 'main' ? (
          <form noValidate onReset={handleReset} onSubmit={handleSubmit}>
            <Grid pb={2} pt={1} container columnGap={2} rowGap={1} alignItems="end">
              <Grid item lg={3} md={3} sm={5.5} xs={12}>
                <FormControl fullWidth>
                  <Typography variant="subtitle2" fontSize={12} color="GrayText">
                    Name
                  </Typography>
                  <TextField
                    name="ptaMemberName"
                    placeholder="Enter name"
                    value={ptaMemberNameFilter}
                    onChange={(e) => {
                      setPtaMemberNameFilter(e.target.value);
                      loadPtaList({
                        ...currentPtaListRequest,
                        ptaMemberName: e.target.value,
                      });
                    }}
                  />
                </FormControl>
              </Grid>
              <Grid item lg={3} md={3} sm={5.5} xs={12}>
                <FormControl fullWidth>
                  <Typography variant="subtitle2" fontSize={12} color="GrayText">
                    Role
                  </Typography>
                  <Select
                    labelId="ptaMemberRoleFilter"
                    id="ptaMemberRoleFilterSelect"
                    value={ptaMemberRoleFilter.toString()}
                    onChange={handleRoleChange}
                    placeholder="Select Role"
                  >
                    {PTA_ROLE_SELECT.map((opt) => (
                      <MenuItem key={opt.id} value={opt.id}>
                        {opt.role}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              {/* <Grid item md="auto" xs={12}>
                <FormControl sx={{ minWidth: { xs: '100%', sm: 200 } }}>
                  <Typography variant="subtitle2" fontSize={12} color="GrayText">
                    Email
                  </Typography>
                  <TextField
                    name="ptaMemberEmail"
                    value={ptaMemberEmailFilter}
                    onChange={(e) => {
                      setPtaMemberEmailFilter(e.target.value);
                      loadPtaList({
                        ...currentPtaListRequest,
                        ptaMemberEmail: e.target.value,
                      });
                    }}
                  />
                </FormControl>
              </Grid> */}
              <Grid item lg={3} md={3} sm={5.5} xs={12}>
                <FormControl fullWidth>
                  <Typography variant="subtitle2" fontSize={12} color="GrayText">
                    Phone Number
                  </Typography>
                  <TextField
                    name="ptaMemberMobile"
                    placeholder="Enter number"
                    type="number"
                    value={ptaMemberMobileFilter}
                    onChange={(e) => {
                      setPtaMemberMobileFilter(e.target.value);
                      loadPtaList({
                        ...currentPtaListRequest,
                        ptaMemberMobile: e.target.value,
                      });
                    }}
                  />
                </FormControl>
              </Grid>
              {/* <Grid item md="auto" xs={12}>
                <FormControl sx={{ minWidth: { xs: '100%', sm: 200 } }}>
                  <Typography variant="h6" fontSize={12} color="GrayText">
                    Status
                  </Typography>
                  <Select
                    labelId="ptaMemberStatusFilter"
                    id="ptaMemberStatusFilterSelect"
                    value={ptaMemberStatusFilter.toString()}
                    onChange={handleStatusChange}
                    placeholder="Select Status"
                  >
                    {STATUS_OPTIONS.map((opt) => (
                      <MenuItem key={opt.id} value={opt.id}>
                        {opt.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid> */}
              <Grid item lg={2} md={2} sm={5.5} xs={12}>
                <FormControl fullWidth>
                  <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                    <Button variant="contained" color="secondary" type="reset" fullWidth>
                      Reset
                    </Button>
                    <Button variant="contained" color="primary" type="submit" fullWidth>
                      Search
                    </Button>
                  </Stack>
                </FormControl>
              </Grid>
            </Grid>
          </form>
        ) : (
          <Stack direction="row" alignItems="center" gap={1}>
            <IconButton onClick={handleBack} color="primary" size="small">
              <ArrowBackIcon fontSize="small" />
            </IconButton>
            <Typography variant="subtitle2" fontSize={17} py={2}>
              DELIVERY REPORT
            </Typography>
          </Stack>
        )}
        <Paper
          sx={{
            border:
              selectedData?.length === 0 && sendResults?.length === 0 ? `0px` : `1px solid ${theme.palette.grey[300]}`,
            width: '100%',
            overflow: 'auto',
          }}
        >
          <Box
            sx={{
              height: 'calc(100vh - 23.7rem)',
              width: { xs: selectedData?.length !== 0 ? '43.75rem' : '100%', md: '100%' },
            }}
          >
            {view === 'main' ? (
              <DataTable
                ShowCheckBox
                disabledCheckBox={disabledCheckBoxes}
                RowSelected
                setSelectedRows={setSelectedRows}
                selectedRows={selectedRows}
                columns={PtaListColumns}
                data={selectedData}
                getRowKey={getRowKey}
                fetchStatus={PtaListStatus}
              />
            ) : (
              <DataTable
                setSelectedRows={setSelectedRows}
                selectedRows={selectedRows}
                columns={PtaListColumns}
                data={sendResults}
                getRowKey={getRowKey}
                fetchStatus={PtaListStatus}
              />
            )}
          </Box>
        </Paper>
        <Box
          display="flex"
          sx={{
            justifyContent: {
              xs: 'right',
            },
            pr: {
              lg: '5',
            },
            pt: 3,
          }}
        >
          {view === 'main' && selectedData.length !== 0 && (
            <Stack
              spacing={2}
              direction={{ xs: 'column', sm: 'row' }}
              justifyContent={{ xs: 'ceneter', sm: 'space-between' }}
              width={sendResults.length > 0 ? '100%' : 'auto'}
            >
              {sendResults && sendResults.length > 0 && (
                <Button
                  className="delivery-report-btn"
                  sx={{ width: { xs: '48.5%', sm: 'auto' } }}
                  color="warning"
                  onClick={() => setView('report')}
                  variant="contained"
                >
                  Delivery Report
                </Button>
              )}
              <Stack spacing={2} direction="row">
                <Button
                  className="cancel-btn"
                  // sx={{ width: { xs: '25%', sm: 'auto' } }}
                  onClick={handleCancel}
                  variant="contained"
                  color="secondary"
                >
                  Cancel
                </Button>
                <LoadingButton
                  fullWidth
                  className="send-btn"
                  loadingPosition="start"
                  endIcon={<SendIcon />}
                  // sx={{ width: { sm: 'auto' } }}
                  onClick={voiceId && voiceId !== 0 ? handleSendVoiceMessage : handleSendMessage}
                  loading={isSubmitting}
                  variant="contained"
                  color="primary"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Sending...' : 'Send'}
                </LoadingButton>
              </Stack>
            </Stack>
          )}
        </Box>
      </Box>
      {isSubmitting ? (
        <LoadingPopup popupContent={<LoadingMessage icon={LoadingMsg} message="Sending messages please wait." />} />
      ) : null}
    </PtaRoot>
  );
}

export default Pta;
