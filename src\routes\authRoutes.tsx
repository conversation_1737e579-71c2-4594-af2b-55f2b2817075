import Loadable from '@/components/shared/Loadable';
import { lazy } from 'react';

const Login = Loadable(lazy(() => import('@/pages/Login')));
const Registration = Loadable(lazy(() => import('@/pages/Registration')));
const AdmissionFormNew = Loadable(lazy(() => import('@/pages/AdmissionFormNew')));
const AdmissionForm = Loadable(lazy(() => import('@/pages/AdmissionForm')));
const AdmissionFormPrint = Loadable(lazy(() => import('@/features/AdmissinFormPrint')));

export const authRoutes = [
  {
    path: '/login',
    element: <Login />,
  },
  {
    path: '/registration',
    element: <Registration />,
  },
  {
    path: '/admission-form',
    element: <AdmissionForm />,
  },
  {
    path: '/admission-form-new',
    element: <AdmissionFormNew />,
  },
  {
    path: '/print-admission-form',
    element: <AdmissionFormPrint />,
  },
];
