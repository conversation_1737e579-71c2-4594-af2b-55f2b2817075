import { <PERSON>ton, TextField, Typography, Box, Stack, FormControl, Autocomplete, Select, MenuItem } from '@mui/material';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import LoadingButton from '@mui/lab/LoadingButton';
import { ALBUM_TYPE_OPTIONS, YEAR_SELECT, YEAR_SELECT_OPTIONS } from '@/config/Selection';
import { AlbumListInfo } from '@/types/Album';

export type CreateEditAlbumFormProps = {
  onSave: (values: AlbumListInfo, mode: 'create' | 'edit') => void;
  onCancel: (mode: 'create' | 'edit') => void;
  onClose: (mode: 'create' | 'edit') => void;
  albumDetail: AlbumListInfo;
  isSubmitting: boolean;
};

const CreateAlbumValidationSchema = Yup.object({
  // accademicYear: Yup.string().required('Please select year'),
  albumName: Yup.string().required('Please enter Album Name'),
  albumDescription: Yup.string().required('Please enter description'),
  albumType: Yup.number().oneOf([0, 1], 'Please select type'),
});

function CreateAlbumForm({ onSave, onCancel, isSubmitting, albumDetail }: CreateEditAlbumFormProps) {
  const mode = albumDetail.albumId === 0 ? 'create' : 'edit';

  const {
    values: { accademicYear, albumName, albumDescription, albumType },
    handleChange,
    handleSubmit,
    touched,
    errors,
  } = useFormik<AlbumListInfo>({
    initialValues: {
      albumId: albumDetail.albumId,
      accademicYear: albumDetail.accademicYear,
      albumName: albumDetail.albumName,
      albumDescription: albumDetail.albumDescription,
      albumType: albumDetail.albumType,
    },
    validationSchema: CreateAlbumValidationSchema,
    onSubmit: (values) => {
      onSave(values, mode);
    },
  });

  return (
    <Box mt={3}>
      <form noValidate onSubmit={handleSubmit}>
        <Stack
          sx={{
            height: 'calc(100vh - 150px)',
            overflow: 'auto',
            '&::-webkit-scrollbar': {
              width: 0,
            },
          }}
          direction="column"
        >
          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Accademic Year
            </Typography>
            <Select
              value={accademicYear}
              onChange={handleChange}
              error={touched.accademicYear && !!errors.accademicYear}
              disabled={isSubmitting}
              name="accademicYear"
            >
              <MenuItem value="" className="d-none">
                Select year
              </MenuItem>
              {YEAR_SELECT_OPTIONS.map((opt) => (
                <MenuItem key={opt.id} value={opt.year}>
                  {opt.year}
                </MenuItem>
              ))}
            </Select>
            {touched.accademicYear && !!errors.accademicYear && (
              <Typography color="red" fontSize="0.625rem" variant="subtitle1">
                {errors.accademicYear}
              </Typography>
            )}
          </FormControl>
          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Album Name
            </Typography>
            <TextField
              placeholder="Album Name"
              name="albumName"
              value={albumName}
              onChange={handleChange}
              error={touched.albumName && !!errors.albumName}
              helperText={errors.albumName}
              disabled={isSubmitting}
            />
          </FormControl>
          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Album Description
            </Typography>
            <TextField
              placeholder="Album Description"
              name="albumDescription"
              value={albumDescription}
              onChange={handleChange}
              error={touched.albumDescription && !!errors.albumDescription}
              helperText={errors.albumDescription}
              disabled={isSubmitting}
            />
          </FormControl>
          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Album Type
            </Typography>
            <Select
              name="albumType"
              value={albumType}
              onChange={handleChange}
              error={touched.albumType && !!errors.albumType}
              disabled={isSubmitting}
            >
              <MenuItem value="" className="d-none">
                Select type
              </MenuItem>
              {ALBUM_TYPE_OPTIONS.map((opt) => (
                <MenuItem key={opt.id} value={opt.id}>
                  {opt.name}
                </MenuItem>
              ))}
            </Select>
            {touched.albumType && !!errors.albumType && (
              <Typography color="red" fontSize="0.625rem" variant="subtitle1">
                {errors.albumType}
              </Typography>
            )}
          </FormControl>
        </Stack>
        <Box sx={{ justifyContent: 'center', pt: 3, position: 'relative' }}>
          <Stack spacing={2} direction="row" sx={{}}>
            <Button onClick={() => onCancel(mode)} fullWidth variant="contained" color="secondary" type="button">
              Cancel
            </Button>
            <LoadingButton
              loading={isSubmitting}
              fullWidth
              variant="contained"
              color="primary"
              type="submit"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Saving...' : 'Save'}
            </LoadingButton>
          </Stack>
        </Box>
      </form>
    </Box>
  );
}

export default CreateAlbumForm;
