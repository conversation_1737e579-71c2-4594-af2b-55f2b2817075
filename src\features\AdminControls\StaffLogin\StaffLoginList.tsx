/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  Collapse,
  FormControl,
  useTheme,
} from '@mui/material';
import styled from 'styled-components';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import SearchIcon from '@mui/icons-material/Search';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import { MdAdd } from 'react-icons/md';
import DateSelect from '@/components/shared/Selections/DateSelect';
import { YEAR_SELECT } from '@/config/Selection';
import { CategorydummyData } from '@/features/PaymentDetails/CategoryList';
import { SubCategorydummyData } from '@/features/PaymentDetails/SubCategoryList';
import useSettings from '@/hooks/useSettings';
import Popup from '@/components/shared/Popup/Popup';
import CreateEditStaffAccountForm from './CreateEditStaffAccountForm';
import ZoomCredential from './ZoomCredential';

const StaffLoginListRoot = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      height: calc(100% - 40px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;
const data = [
  {
    SlNo: '1',
    Name: 'Passdaily Support',
    LoginName: 'psupport',
    Password: 'demo@2023',
    Role: 'ADMIN',
    ZoomUserID: '<EMAIL>',
    status: 'Active',
  },
  {
    SlNo: '2',
    Name: 'RUBY',
    LoginName: 'ruby',
    Password: 'ruby123',
    Role: 'ADMIN',
    ZoomUserID: '<EMAIL>',
    status: 'Active',
  },
  {
    SlNo: '3',
    Name: 'ANSAR',
    LoginName: 'ansar',
    Password: 'ansar123',
    Role: 'ADMIN',
    ZoomUserID: '<EMAIL>',
    status: 'Active',
  },
  {
    SlNo: '12',
    Name: 'REJIN',
    LoginName: 'rejin',
    Password: 'rejin123',
    ZoomUserID: '<EMAIL>',
    status: 'Active',
    Role: 'ADMIN',
  },
  {
    SlNo: '5',
    Name: 'FAVAS',
    LoginName: 'favas',
    Password: 'favas123',
    Role: 'ADMIN',
    ZoomUserID: '<EMAIL>',
    status: 'Active',
  },
  {
    SlNo: '6',
    Name: 'AJEESH',
    LoginName: 'ajesh',
    Password: 'ajesh123',
    Role: 'ADMIN',
    ZoomUserID: '<EMAIL>',
    status: 'Active',
  },
  {
    SlNo: '7',
    Name: 'SANTHOSH',
    LoginName: 'santhosh',
    Password: 'santhosh154',
    Role: 'TEACHER',
    ZoomUserID: '<EMAIL>',
    status: 'Active',
  },
  {
    SlNo: '8',
    Name: 'ANVER',
    LoginName: 'anveradmin',
    Password: 'anver$2014',
    Role: 'ADMIN',
    ZoomUserID: '<EMAIL>',
    status: 'Active',
  },
  {
    SlNo: '9',
    Name: 'SUPPORT',
    LoginName: 'psupport',
    Password: 'demo@2023',
    Role: 'ADMIN',
    ZoomUserID: '<EMAIL>',
    status: 'Active',
  },
  {
    SlNo: '10',
    Name: 'RUBY',
    LoginName: 'ruby',
    Password: 'ruby123',
    Role: 'ADMIN',
    ZoomUserID: '<EMAIL>',
    status: 'Active',
  },
  {
    SlNo: '11',
    Name: 'ANSAR',
    LoginName: 'ansar',
    Password: 'ansar123',
    Role: 'ADMIN',
    ZoomUserID: '<EMAIL>',
    status: 'Active',
  },
  {
    SlNo: '12',
    Name: 'REJIN',
    LoginName: 'rejin',
    Password: 'rejin123',
    ZoomUserID: '<EMAIL>',
    status: 'Active',
    Role: 'ADMIN',
  },
];

function StaffLoginList() {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const [drawerOpen, setDrawerOpen] = React.useState<boolean>(false);
  const [showFilter, setShowFilter] = useState(false);
  const [changeView, setChangeView] = useState<boolean>(false);
  const [createAccount, setCreateAccount] = useState<boolean>(false);
  const [zoomCredential, setZoomCredential] = useState<boolean>(false);

  const handleOpen = () => {
    setDrawerOpen(true);
  };

  const toggleDrawerClose = useCallback(() => setDrawerOpen(false), []);

  const getRowKey = useCallback((row: any) => row.examId, []);
  const adminStaffLoginListColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'slNo',
        dataKey: 'SlNo',
        headerLabel: 'Sl.No',
        sortable: true,
      },
      {
        name: 'name',
        dataKey: 'Name',
        headerLabel: 'Name',
      },
      {
        name: 'loginName',
        dataKey: 'LoginName',
        headerLabel: 'User ID',
      },
      {
        name: 'password',
        dataKey: 'Password',
        headerLabel: 'Password',
      },
      {
        name: 'role',
        dataKey: 'Role',
        headerLabel: 'Role',
      },
      {
        name: 'zoomUserID',
        dataKey: 'ZoomUserID',
        headerLabel: 'Zoom UserID',
      },
      {
        name: 'status',
        dataKey: 'status',
        headerLabel: 'Status',
      },
      {
        name: 'actions',
        headerLabel: 'Actions',
        renderCell: () => {
          return (
            <Stack direction="row" gap={1}>
              <IconButton size="small" onClick={() => setCreateAccount(true)} sx={{ padding: 0.5 }}>
                <EditIcon />
              </IconButton>
              <IconButton size="small" color="error" sx={{ padding: 0.5 }}>
                <DeleteIcon />
              </IconButton>
              <Button size="small" variant="contained" color="secondary">
                Password
              </Button>
              <Button
                size="small"
                variant="contained"
                color="primary"
                sx={{ width: '130px' }}
                onClick={() => setZoomCredential(true)}
              >
                Zoom Credential
              </Button>
            </Stack>
          );
        },
      },
    ],
    []
  );

  return (
    <Page title="List">
      <StaffLoginListRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17}>
              Staff Login List
            </Typography>
            <Box pb={1} sx={{ flexShrink: 0 }}>
              <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                <SearchIcon />
              </IconButton>
              <Button
                onClick={() => setCreateAccount(true)}
                sx={{ borderRadius: '20px' }}
                variant="outlined"
                size="small"
              >
                <MdAdd size="20px" /> Add
              </Button>
              {/* <Tooltip title={changeView === true ? 'Table View' : 'Card View'}>
                <IconButton className="change-view-button" color="primary" onClick={() => setChangeView((c) => !c)}>
                  {changeView === true ? <BiTable /> : <TiBusinessCard />}
                </IconButton>
              </Tooltip> */}
            </Box>
          </Stack>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={3} alignItems="end">
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="h6" fontSize={14}>
                        Academic Year
                      </Typography>
                      <Autocomplete
                        options={YEAR_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select Year" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="h6" fontSize={14}>
                        Category
                      </Typography>
                      <Autocomplete
                        options={CategorydummyData.map((item) => item.name)}
                        renderInput={(params) => <TextField {...params} placeholder="Select Category" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="h6" fontSize={14}>
                        Sub Category
                      </Typography>
                      <Autocomplete
                        options={SubCategorydummyData.map((item) => item.name)}
                        renderInput={(params) => <TextField {...params} placeholder="Select Sub Category" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="h6" fontSize={14}>
                        Staff
                      </Typography>
                      <Autocomplete
                        options={['Staff1', 'Staff2', 'Staff3', 'Admin']}
                        renderInput={(params) => <TextField {...params} placeholder="Select Staff" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg={2} xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="h6" fontSize={14}>
                        From
                      </Typography>
                      <DateSelect />
                    </FormControl>
                  </Grid>
                  <Grid item lg={2} xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="h6" fontSize={14}>
                        To
                      </Typography>
                      <DateSelect />
                    </FormControl>
                  </Grid>

                  <Grid item lg="auto" xs={12} sm={6}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>
            {changeView === true ? (
              // <Box className="card-container" my={2}>
              //   <Grid container spacing={2}>
              //     {data.map((student, rowIndex) => (
              //       <Grid item xl={4} lg={6} md={12} sm={6} xs={12}>
              //         <Card
              //           className="student_card"
              //           sx={{ p: 2, boxShadow: 0, border: `1px solid ${theme.palette.grey[300]}` }}
              //         >
              //           <Box display="flex" flexDirection="column">
              //             <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
              //               <Chip
              //                 size="small"
              //                 label={student.status === 'Published' ? 'Click to Unpublish' : 'Click to Publish'}
              //                 variant="outlined"
              //                 color="primary"
              //                 clickable
              //               />

              //               <Stack direction="row" alignItems="center" gap={1} ml={1}>
              //                 <Chip
              //                   size="small"
              //                   label={student.status === 'Published' ? ' Published' : 'Unpublished'}
              //                   variant="outlined"
              //                   color={student.status === 'Published' ? 'success' : 'error'}
              //                   sx={{ border: '0px' }}
              //                 />
              //                 <MenuEditDeleteView
              //                 // Edit={() => setCreatePopup(true)}
              //                 // Delete={handleClickDelete}
              //                 // View={handleClickView}
              //                 />
              //               </Stack>
              //             </Box>

              //             {adminStaffLoginListColumns.map((item) => (
              //               <Stack direction="row" ml={1}>
              //                 <Grid container>
              //                   <Grid item lg={5} xs={6}>
              //                     <Typography mt={0.5} key={item.name} variant="subtitle1" fontSize={13} mr={2}>
              //                       {item.headerLabel}
              //                     </Typography>
              //                   </Grid>
              //                   <Grid item lg={7} xs={6} mb={0} mt="auto">
              //                     <Typography
              //                       variant="h6"
              //                       mt={0.5}
              //                       fontSize={13}
              //                       color={item.dataKey === 'meetingLink' ? theme.palette.info.main : ''}
              //                     >
              //                       {item.dataKey
              //                         ? `:${' '}${(student as { [key: string]: any })[item.dataKey ?? '']}`
              //                         : item && item.renderCell && item.renderCell(student, rowIndex)}
              //                     </Typography>
              //                   </Grid>
              //                 </Grid>
              //               </Stack>
              //             ))}
              //           </Box>
              //         </Card>
              //       </Grid>
              //     ))}
              //   </Grid>
              // </Box>
              ''
            ) : (
              <Paper className="card-table-container" sx={{ marginTop: '12px' }}>
                <DataTable
                  columns={adminStaffLoginListColumns}
                  data={data}
                  getRowKey={getRowKey}
                  fetchStatus="success"
                />
              </Paper>
            )}
          </div>
          {/* <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 2 }}>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="secondary">
                Cancel
              </Button>
              <Button onClick={handleOpen} variant="contained" color="primary">
                Save
              </Button>
            </Stack>
          </Box> */}
        </Card>
      </StaffLoginListRoot>
      <Popup
        size="sm"
        title="Create Account"
        state={createAccount}
        onClose={() => setCreateAccount(false)}
        popupContent={<CreateEditStaffAccountForm onClose={() => setCreateAccount(false)} />}
      />
      <Popup
        size="sm"
        title="Credential Details"
        state={zoomCredential}
        onClose={() => setZoomCredential(false)}
        popupContent={<ZoomCredential onClose={() => setZoomCredential(false)} />}
      />
    </Page>
  );
}

export default StaffLoginList;
