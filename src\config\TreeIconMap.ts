// import { RxDashboard } from 'react-icons/rx';
// import { RiBusWifiLine, RiQuestionAnswerLine, RiUserFollowLine, RiUserSettingsLine } from 'react-icons/ri';
// import { FiMail } from 'react-icons/fi';
// import { BiBus, BiStore } from 'react-icons/bi';
// import { HiOutlineMicrophone, HiOutlineCurrencyRupee, HiOutlineBell, HiOutlineUserGroup } from 'react-icons/hi';
// import { SlGraduation } from 'react-icons/sl';
// import { FaChalkboardTeacher } from 'react-icons/fa';
// import { TiThListOutline } from 'react-icons/ti';
// import { BsCashCoin, BsJournalCheck } from 'react-icons/bs';
// import { IoLibraryOutline, IoNewspaperOutline } from 'react-icons/Io5';
// import { MdOutlinePhotoAlbum } from 'react-icons/md';
// import { AiOutlineAlert } from 'react-icons/ai';
// import { TbUsersGroup } from 'react-icons/tb';
// import { GrUserSettings } from 'react-icons/gr';
// import { CiSettings } from 'react-icons/ci';
// import { LuCalendarDays } from 'react-icons/lu';
import {
  FcApprove,
  FcCalendar,
  FcCamcorderPro,
  FcConferenceCall,
  FcCollaboration,
  FcCurrencyExchange,
  FcDebt,
  FcExpired,
  FcGraduationCap,
  FcHome,
  FcInspection,
  FcIntegratedWebcam,
  FcKindle,
  FcLibrary,
  FcManager,
  FcMoneyTransfer,
  FcOvertime,
  FcPhoneAndroid,
  FcPortraitMode,
  FcReadingEbook,
  FcRules,
  FcServices,
  FcSms,
  FcStackOfPhotos,
  FcSurvey,
  FcTemplate,
  FcVoicemail,
  FcVoicePresentation,
  FcWebcam,
  FcDiploma2,
  FcViewDetails,
  FcShop,
  FcSelfie,
  FcDecision,
} from 'react-icons/fc';
// export const TreeIconMap: any = {
//   dashboard: RxDashboard,
//   attendanceMarking: RiUserFollowLine,
//   messageBoxSMS: FiMail,
//   voiceMessage: HiOutlineMicrophone,
//   appNotification: HiOutlineBell,
//   manageFee: HiOutlineCurrencyRupee,
//   academicManagement: SlGraduation,
//   manageStudents: HiOutlineUserGroup,
//   staffManagement: FaChalkboardTeacher,
//   parentEnquiry: RiQuestionAnswerLine,
//   vehicleTracking: RiBusWifiLine,
//   timeTable: TiThListOutline,
//   publishResult: BsJournalCheck,
//   examCenter: IoNewspaperOutline,
//   album: MdOutlinePhotoAlbum,
//   busFee: BiBus,
//   feeAlert: AiOutlineAlert,
//   adminControls: RiUserSettingsLine,
//   paymentDetails: BsCashCoin,
//   library: IoLibraryOutline,
//   store: BiStore,
//   group: TbUsersGroup,
//   settings: CiSettings,
//   calendar: LuCalendarDays,
// };

//                           Coloured Icons
// ----------------------------------------------------------------
export const TreeIconMap: any = {
  dashboard: FcHome,
  attendanceMarking: FcApprove,
  messageBoxSMS: FcSms,
  voiceMessage: FcVoicemail,
  appNotification: FcPhoneAndroid,
  academicManagement: FcGraduationCap,
  manageStudents: FcManager,
  staffManagement: FcReadingEbook,
  manageFee: FcDebt,
  busFee: FcCurrencyExchange,
  parentEnquiry: FcVoicePresentation,
  vehicleTracking: FcIntegratedWebcam,
  timeTable: FcOvertime,
  publishResult: FcInspection,
  examCenter: FcTemplate,
  album: FcStackOfPhotos,
  paymentDetails: FcMoneyTransfer,
  feeAlert: FcExpired,
  adminControls: FcPortraitMode,
  library: FcLibrary,
  report: FcSurvey,
  store: FcShop,
  calendar: FcCalendar,
  settings: FcServices,
  group: FcConferenceCall,
  liveClass: FcWebcam,
  onlineVideoClass: FcCamcorderPro,
  assignment: FcRules,
  onlineExam: FcKindle,
  descriptiveExam: FcKindle,
  objectiveExam: FcKindle,
  pta: FcCollaboration,
  certificate: FcDiploma2,
  tcandcc: FcViewDetails,
  social: FcSelfie,
  schoolplan: FcShop,
  rnd: FcDecision,
};
