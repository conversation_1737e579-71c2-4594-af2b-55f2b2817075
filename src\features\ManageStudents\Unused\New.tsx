/* eslint-disable prettier/prettier */
import React, { useState } from 'react';
import { Grid, Typography, Button, Stack } from '@mui/material';
import CustomTextField from '@/components/shared/Selections/CustomTextField';

const NewStudent = ({ student, onCancel, onUpdate }: any) => {
  const [editedStudent, setEditedStudent] = useState(student);

  const handleChange = (e: any) => {
    const { name, value } = e.target;
    setEditedStudent((prevStudent: any) => ({
      ...prevStudent,
      [name]: value,
    }));
  };
    // onUpdate(editedStudent);

  return (
    <Grid container spacing={2} m={4} maxWidth="90%">
      <Grid item xs={12}>
        <Typography variant="h6" mb={2}>
          Add Indivitual Student
        </Typography>
      </Grid>
      <Grid item md={3} xs={6}>
        <CustomTextField
          name="admissionNo"
          label="Adm No"
          variant="outlined"
          onChange={handleChange}
          value={undefined}
        />
      </Grid>
      <Grid item md={3} xs={6}>
        <CustomTextField name="rollNo" label="Roll No" variant="outlined" onChange={handleChange} value={undefined} />
      </Grid>
      <Grid item md={6} xs={12}>
        <CustomTextField
          name="name"
          label="Student Name"
          variant="outlined"
          onChange={handleChange}
          value={undefined}
        />
      </Grid>
      <Grid item md={3} xs={6}>
        <CustomTextField
          name="class"
          label="Class"
          variant="outlined"
          onChange={handleChange}
          value={undefined}
        />
      </Grid>
      <Grid item md={3} xs={6}>
        <CustomTextField
          name="S. ID"
          label="S. ID"
          variant="outlined"
          onChange={handleChange}
          value={undefined}
        />
      </Grid>
      <Grid item md={3} xs={6}>
        <CustomTextField
          name="gender"
          label="Gender"
          variant="outlined"
          onChange={handleChange}
          value={undefined}
        />
      </Grid>
      <Grid item md={3} xs={6}>
        <CustomTextField
          name="bloodGroup"
          label="Blood Group"
          variant="outlined"
          onChange={handleChange}
          value={undefined}
        />
      </Grid>
      <Grid item md={3} xs={6}>
        <CustomTextField
          name="dateOfBirth"
          label="Date of Birth"
          variant="outlined"
          onChange={handleChange}
          value={undefined}
        />
      </Grid>
      <Grid item md={3} xs={6}>
        <CustomTextField
          name="houseGroup"
          label="House Group"
          variant="outlined"
          onChange={handleChange}
          value={undefined}
        />
      </Grid>
      <Grid item md={6} xs={12}>
        <CustomTextField
          name="aadharNo"
          label="Aadhar No."
          variant="outlined"
          onChange={handleChange}
          value={undefined}
        />
      </Grid>
      <Grid item md={6} xs={12}>
        <CustomTextField
          value={undefined}
          name="Father'sName"
          label="Father's Name"
          variant="outlined"
          onChange={handleChange}
        />
      </Grid>
      <Grid item md={6} xs={12}>
        <CustomTextField
          value={undefined}
          name="Father'sNumber"
          label="Father's Number"
          variant="outlined"
          onChange={handleChange}
        />
      </Grid>
      <Grid item md={6} xs={12}>
        <CustomTextField
          value={undefined}
          name="Mother'sName"
          label="Mother's Name"
          variant="outlined"
          onChange={handleChange}
        />
      </Grid>
      <Grid item md={6} xs={12}>
        <CustomTextField
          value={undefined}
          name="Mother'sNumber"
          label="Mother's Number"
          variant="outlined"
          onChange={handleChange}
        />
      </Grid>
      <Grid item md={6} xs={12}>
        <CustomTextField
          name="caste"
          label="Caste"
          variant="outlined"
          onChange={handleChange}
          value={undefined}
        />
      </Grid>
      <Grid item md={6} xs={12}>
        <CustomTextField
          name="religion"
          label="Religion"
          variant="outlined"
          onChange={handleChange}
          value={undefined}
        />
      </Grid>
      <Grid item md={6} xs={12}>
        <CustomTextField
          name="busNo"
          label="Bus Route"
          variant="outlined"
          onChange={handleChange}
          value={undefined}
        />
      </Grid>
      <Grid item md={6} xs={12}>
        <CustomTextField
          name="district"
          label="District"
          variant="outlined"
          onChange={handleChange}
          value={undefined}
        />
      </Grid>
      <Grid item md={6} xs={12}>
        <CustomTextField
          name="taluka"
          label="Taluka"
          variant="outlined"
          onChange={handleChange}
          value={undefined}
        />
      </Grid>
      <Grid item md={6} xs={12}>
        <CustomTextField
          name="state"
          label="State"
          variant="outlined"
          onChange={handleChange}
          value={undefined}
        />
      </Grid>
      <Grid item xs={12}>
        <CustomTextField
          name="Address"
          label="Address"
          variant="outlined"
          onChange={handleChange}
          value={undefined}
        />
      </Grid>
      <Grid item xs={12}>
        <Stack justifyContent="end" direction="row" spacing={2}>
          <Button variant="contained" color="secondary" onClick={onCancel}>
            Cancel
          </Button>
          <Button variant="contained" color="primary" onClick={onCancel}>
            Save
          </Button>
        </Stack>
      </Grid>
    </Grid>
  );
};

export default NewStudent;
