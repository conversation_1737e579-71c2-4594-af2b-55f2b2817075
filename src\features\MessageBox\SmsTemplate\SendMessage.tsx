import React from 'react';
import Pta from '@/components/shared/MessageTempletes/PTA';
import Conveyors from '@/components/shared/MessageTempletes/Conveyors';
import Parents from '@/components/shared/MessageTempletes/Parents';
import Staffs from '@/components/shared/MessageTempletes/Staffs';
import GroupWise from '@/components/shared/MessageTempletes/GroupWise';
import PublicGroups from '@/components/shared/MessageTempletes/PublicGroups';
import ClassWise from '@/components/shared/MessageTempletes/ClassWise';
import ClassDivision from '@/components/shared/MessageTempletes/ClassDivision';
import Groups from '@/components/shared/MessageTempletes/Groups';
import PublicGroupWise from '@/components/shared/MessageTempletes/PublicGroupWise';

const SendMessage = ({
  content,
  messageId,
  notificationId,
  voiceId,
  isSubmitting,
  templateId,
}: {
  content: string;
  messageId?: number;
  notificationId?: number;
  voiceId?: number;
  templateId?: number | string;
  isSubmitting?: boolean;
}) => {
  switch (content) {
    case 'Parents':
      return (
        <Parents
          messageId={messageId}
          notificationId={notificationId}
          voiceId={voiceId}
          isSubmitting={isSubmitting}
          templateId={templateId}
        />
      );
    case 'Staffs':
      return (
        <Staffs
          messageId={messageId}
          notificationId={notificationId}
          voiceId={voiceId}
          isSubmitting={isSubmitting}
          templateId={templateId}
        />
      );
    case 'PTA':
      return <Pta messageId={messageId} voiceId={voiceId} isSubmitting={isSubmitting} templateId={templateId} />;
    case 'Conveyors':
      return <Conveyors messageId={messageId} voiceId={voiceId} isSubmitting={isSubmitting} templateId={templateId} />;
    case 'Groups':
      return <Groups messageId={messageId} voiceId={voiceId} isSubmitting={isSubmitting} templateId={templateId} />;
    case 'GroupWise':
      return <GroupWise messageId={messageId} voiceId={voiceId} isSubmitting={isSubmitting} templateId={templateId} />;
    case 'PublicGroups':
      return (
        <PublicGroups messageId={messageId} voiceId={voiceId} isSubmitting={isSubmitting} templateId={templateId} />
      );
    case 'PublicGroupWise':
      return (
        <PublicGroupWise messageId={messageId} voiceId={voiceId} isSubmitting={isSubmitting} templateId={templateId} />
      );
    case 'ClassWise':
      return (
        <ClassWise
          messageId={messageId}
          notificationId={notificationId}
          voiceId={voiceId}
          isSubmitting={isSubmitting}
          templateId={templateId}
        />
      );
    case 'ClassDivision':
      return (
        <ClassDivision
          messageId={messageId}
          notificationId={notificationId}
          voiceId={voiceId}
          isSubmitting={isSubmitting}
          templateId={templateId}
        />
      );
    default:
      return <div> </div>;
  }
};

export default SendMessage;
