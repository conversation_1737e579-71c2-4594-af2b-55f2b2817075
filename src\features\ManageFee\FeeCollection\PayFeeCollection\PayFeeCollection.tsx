/* eslint-disable no-else-return */
/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/alt-text */
import React, { MutableRefObject, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Box,
  Divider,
  Paper,
  Stack,
  TextField,
  Button,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Typography,
  Card,
  IconButton,
  Collapse,
  Select,
  MenuItem,
  FormControl,
  useTheme,
  Tooltip,
  Checkbox,
  FormControlLabel,
  Radio,
  Snackbar,
  Alert,
  SelectChangeEvent,
} from '@mui/material';
import CurrencyRupeeIcon from '@mui/icons-material/CurrencyRupee';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import { IoIosArrowUp } from 'react-icons/io';
import { GoInfo } from 'react-icons/go';
import SearchIcon from '@mui/icons-material/Search';
import DeleteIcon from '@mui/icons-material/Delete';
import DeleteAnimatedIcon from '@/assets/ManageFee/trash-bin.gif';
import styled from 'styled-components';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import Popup from '@/components/shared/Popup/Popup';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import { SuccessIcon } from '@/theme/overrides/CustomIcons';
import useSettings from '@/hooks/useSettings';
import { getTextColor } from '@/utils/Colors';
import useAuth from '@/hooks/useAuth';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import CachedIcon from '@mui/icons-material/Cached';
import { getstudentTermFeeStatusListData, getstudentTermFeeStatusListStatus } from '@/config/storeSelectors';
import { useAppSelector } from '@/hooks/useAppSelector';
import { StudentFeeStatusDataType, StudentTermFeeStatusDataType } from '@/types/ManageFee';
import AddIcon from '@mui/icons-material/Add';
import { CheckReceiptNo, fetchStudentTermFeeStatus } from '@/store/ManageFee/manageFee.thunks';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import Popover from '@mui/material/Popover';
import PopupState, { bindTrigger, bindPopover } from 'material-ui-popup-state';
import DatePickers from '@/components/shared/Selections/DatePicker';
import dayjs, { Dayjs } from 'dayjs';
import { Pay } from './PayDrawer';
import { getRowIdFromRowModel } from '@mui/x-data-grid/hooks/features/rows/gridRowsUtils';
import DTVirtuoso from '@/components/shared/RND/DataTableVirtuoso';

const PayFeeCollectionRoot = styled.div`
  padding: 1rem;
  @media screen and (max-width: 576px) {
    padding: 0.5rem;
  }
  .Card {
    display: flex;
    flex-direction: column;
    /* max-height: calc(120vh - 10px); */
    height: 100%;
    /* @media screen and (max-width: 996px) {
      height: 100%;
    } */

    .card-main-body {
      display: flex;
      flex-direction: column;
      /* max-height: calc(100% - 50px); */
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        border-radius: 0px;
        flex-direction: column;
        /* border: 1px solid #e8e8e9; */
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
        .MuiTableCell-root.MuiTableCell-body {
          border: 1px solid ${(props) => props.theme.palette.grey[200]};
        }

        .MuiTableCell-root.MuiTableCell-body {
          padding: 0px;
        }
        .MuiTableCell-root:first-child {
          padding: 6px;
        }
        .MuiTableCell-root.MuiTableCell-head {
          border-top-right-radius: 0px;
          border-top-left-radius: 0px;
        }
        .MuiTableRow-root:hover {
          background-color: transparent; /* Change this to your desired hover color */
        }
        .MuiTableCell-head {
          /* background-color: #880ed4;
          color: #fff; */
          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.info.light : props.theme.palette.grey[900]};
        }
        .MuiTableCell-head:nth-child(1) {
          z-index: 11;
          position: sticky;
          left: 0;
        }
        .MuiTableCell-head:nth-child(2) {
          z-index: 11;
          position: sticky;
          left: 49.5px;
        }
        .MuiTableCell-head:nth-child(3) {
          z-index: 11;
          position: sticky;
          left: 171px;
        }
        .MuiTableCell-head:nth-child(4) {
          z-index: 11;
          position: sticky;
          left: 253px;
        }
        .MuiTableCell-head:nth-child(5) {
          z-index: 11;
          position: sticky;
          left: 334px;
        }
        .MuiTableCell-root.MuiTableCell-body:nth-child(1) {
          position: sticky;
          left: 0;
          /*background-color: #8d16d6;
          color: #fff; */

          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.info.lighter : props.theme.palette.grey[900]};
          /* width: 50px; */
          z-index: 1;
        }
        .MuiTableCell-root.MuiTableCell-body:nth-child(2) {
          position: sticky;
          left: 49.5px;
          /* background-color: #8d16d6;
          color: #fff; */

          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.info.lighter : props.theme.palette.grey[900]};
          width: 150px;
          z-index: 1;
        }
        .MuiTableCell-root.MuiTableCell-body:nth-child(3) {
          position: sticky;
          left: 171px;
          /* background-color: #8d16d6;
          color: #fff; */

          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.info.lighter : props.theme.palette.grey[900]};
          z-index: 1;
        }
        .MuiTableCell-root.MuiTableCell-body:nth-child(4) {
          position: sticky;
          left: 253px;
          /* background-color: #8d16d6;
          color: #fff; */

          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.info.lighter : props.theme.palette.grey[900]};

          z-index: 1;
        }
        .MuiTableCell-root.MuiTableCell-body:nth-child(5) {
          position: sticky;
          left: 334px;
          /* background-color: #8d16d6;
          color: #fff; */

          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.info.lighter : props.theme.palette.grey[900]};
          width: 100px;
          z-index: 1;
        }
        .MuiTable-root:first-child {
          border-top-left-radius: 0px;
        }
      }
    }
    .month-name {
      color: ${(props) => getTextColor(props.theme)};
    }
    .Payment_Info .MuiTableCell-root {
      font-size: 12px;
    }
    .Payment_Info .MuiOutlinedInput-input {
      background-color: red;
    }
    .Payment_Info .MuiTableCell-root.MuiTableCell-head {
      background-color: ${(props) =>
        props.theme.themeMode === 'light' ? props.theme.palette.grey[100] : props.theme.palette.grey[900]};
      border-top-right-radius: 0px;
      border-top-left-radius: 0px;
    }
    .Payment_Info .MuiTableCell-root {
      border: 0px;
      border-bottom: 0px;
    }
    .Payment_Info input {
      width: 10px;
    }
  }
`;

type FeeCollectionProps = {
  onBackClick: () => void;
  studentId?: number;
  classId?: number;
  sectionId?: number;
  studentDetails?: StudentFeeStatusDataType;
  academicId?: number;
};

interface TermSum {
  termId: number;
  amount: number;
  feeId: number | null;
  discount: number;
  scholarship: number;
  fine: number;
}
type TermFeeStatsProps = {
  termId: number;
  amount: number;
  scholarship: number;
  discount: number;
  fine: number;
  paid: number;
  balance: number;
};
type TermFeeDetailsType = {
  feeId: number;
  feeTitle: string;
  totalFee: number;
  paid: number;
  balance: number;
  termFeeStats: TermFeeStatsProps[];
};
type CellDataType = {
  feeId: number;
  termId: number;
  feeTitle: string;
  termTitle: string;
  amount: number;
  scholarship: number;
  discount: number;
  fine: number;
  termFeeStats?: TermFeeStatsProps[];
};
type TermFeePushDataType = {
  feeId: number;
  termId: number;
  totalAmount: number;
  scholarship: number;
  discount: number;
  fine: number;
  grandTotal: number;
  payStatus: string;
  receiptDetailsId: number;
};
interface ReceiptType {
  receiptType: string;
}
type TermsType = {
  termId: number;
  termTitle: string;
};
type StudentTermFeeStatusDataType2 = {
  terms: TermsType;
  termFeeDetails: TermFeeDetailsType[];
};

export default function PayFeeCollection({
  onBackClick,
  classId,
  sectionId,
  studentId,
  studentDetails,
  academicId,
}: FeeCollectionProps) {
  const isLight = useSettings().themeMode === 'light';
  const theme = useTheme();
  const { user } = useAuth();
  const dispatch = useAppDispatch();
  const { confirm } = useConfirm();
  const adminId: number = user ? user.accountId : 0;
  // const academicId = 10;
  const currentDate = new Date();

  const [drawerOpen, setDrawerOpen] = React.useState<boolean>(false);

  const toggleDrawerOpen = () => setDrawerOpen(true);

  const toggleDrawerClose = () => setDrawerOpen(false);

  const [showFilter, setShowFilter] = useState(true);
  const [deleteFee, setDeleteFee] = useState(false);
  const [showPaymentIfno, setShowPaymentIfno] = useState(true);

  const StudentTermFeeStatusListData = useAppSelector(getstudentTermFeeStatusListData);
  const StudentTermFeeStatusListStatus = useAppSelector(getstudentTermFeeStatusListStatus);

  const [termFee, setTermFeeData] = useState<StudentTermFeeStatusDataType[]>([]);
  const [termFeeDetails, setTermFeeDetails] = useState([]);
  const [receiptType, setReceiptType] = useState<ReceiptType>({ receiptType: '' });
  const [monthSum, setMonthSum] = useState<number[]>(Array(termFee?.terms?.length || 0).fill(0));
  const [columnMonthSum, setColumnMonthSum] = useState();
  const [openCellInfo, setOpenCellInfo] = useState(false);

  const initialStudentTermFeeListRequest = React.useMemo(
    () => ({
      adminId,
      academicId,
      sectionId: studentDetails.sectionId,
      classId: studentDetails.classId,
      studentId: studentDetails.studentId,
    }),
    [adminId, academicId, studentDetails]
  );
  // const currentTermFeeListRequest = React.useMemo(
  //   () => ({
  //     adminId,
  //     academicId: 10,
  //     sectionId,
  //     classId,
  //     studentId,
  //   }),
  //   [adminId, classId, sectionId, studentId]
  // );

  const loadStudentTermFeeDataList = useCallback(
    async (request: { adminId: number; academicId: number; sectionId: number; classId: number; studentId: number }) => {
      try {
        const data = await dispatch(fetchStudentTermFeeStatus(request)).unwrap();
        console.log('data::::', data);
        setTermFeeDetails(data.termFeeDetails);
        setReceiptType(data.receiptType);
        // const TermFeeDetails = data.termFeeDetails ? data.termFeeDetails.map((item) => ({ ...item })) : [];
      } catch (error) {
        console.error('Error loading term fee list:', error);
      }
    },
    [dispatch]
  );

  React.useEffect(() => {
    // dispatch(fetchStudentTermFeeStatus({ adminId, academicId, sectionId, classId, studentId }));
    if (StudentTermFeeStatusListStatus === 'idle') {
      loadStudentTermFeeDataList(initialStudentTermFeeListRequest);
    }

    setTermFeeData(StudentTermFeeStatusListData);
  }, [
    dispatch,
    initialStudentTermFeeListRequest,
    StudentTermFeeStatusListStatus,
    StudentTermFeeStatusListData,
    loadStudentTermFeeDataList,
    adminId,
    classId,
    sectionId,
    studentId,
  ]);

  const [rows, setRows] = useState([
    {
      id: 1,
      paymentType: '',
      amount: '',
      chequeNo: '',
      dateOfIssue: '',
      deppositDate: '',
      clearingDate: '',
      micr: '',
      bank: '',
      branch: '',
      schoolBank: '',
    },
  ]);

  const [headerValues, setHeaderValues] = useState<string[]>([]);
  const [selectedCells, setSelectedCells] = useState<{ rowIndex: number; columnIndex: number }[]>([]);
  // const [monthSum, setMonthSum] = useState(Array(termsLength).fill(0));
  const [totalAmount, setTotalAmount] = useState<number>(0);
  const [newtotalAmount, setNewtotalAmount] = useState([]);
  const [totalScholarship, setTotalScholarship] = useState<number>(0);
  const [totalDiscount, setTotalDiscount] = useState<number>(0);
  const [totalFine, setTotalFine] = useState<number>(0);
  const [fId, setFId] = useState<number>(0);
  const [tId, setTId] = useState<number>(0);
  const [cellData, setCellData] = useState<CellDataType[]>([]);
  const [cellClickedState, setCellClickedState] = useState({});
  const [selectedRows, setSelectedRows] = useState<TermFeeDetailsType[]>([]);
  const [selectedRowCell, setSelectedRowCell] = useState([]);
  const [isSelectedAllPending, setSelectAllPending] = useState(false);
  const [selectedColumn, setSelectedColumn] = useState<number[]>([]);
  const [selectedAllAmount, setSelectedAllAmount] = useState([]);
  const [payingDate, setPayingDate] = useState('');
  const [deppositDate, setDepositDate] = useState('');
  const [clearingDate, setClearingDate] = useState('');
  const [remarks, setRemarks] = useState('');
  const [receiptNumber, setReceiptNo] = useState<number | undefined>();
  const [error, setError] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);

  const initialPayDetails: any = {
    adminId,
    academicId,
    sectionId,
    classId,
    studentId,
    totalAmount,
    grandTotal: 0,
    remarks: '',
    payingDate,
    payStatus: '',
    receiptNo: 0,
    termfeepaying: [],
    paymentmode: [
      {
        paymentTypeId: 0,
        amount: 0,
        chequeOrDDNo: '',
        dateOfIssue: '',
        deppositDate: '',
        clearingDate: '',
        bankId: 0,
        payStatus: '',
      },
    ],
  };

  const [payDetails, setPayDetails] = useState({
    adminId,
    academicId,
    sectionId,
    classId,
    studentId,
    totalAmount,
    totalScholarship: 0,
    totalDiscount: 0,
    totalFine: 0,
    grandTotal: 0,
    remarks: '',
    payingDate,
    payStatus: '',
    receiptNo: 0,
    termfeepaying: [
      {
        feeId: fId,
        termId: tId,
        totalAmount,
        grandTotal: 0,
        payStatus: '',
        receiptDetailsId: 0,
      },
    ],
    paymentmode: [
      {
        paymentTypeId: 0,
        amount: 0,
        chequeOrDDNo: '',
        dateOfIssue: '',
        deppositDate: '',
        clearingDate: '',
        bankId: 0,
        payStatus: '',
      },
    ],
  });

  const handleAddRow = () => {
    const newRowId = rows.length > 0 ? rows[rows.length - 1].id + 1 : 1;
    const newRow = {
      id: newRowId,
      paymentType: '',
      amount: '',
      chequeNo: '',
      dateOfIssue: '',
      deppositDate: '',
      clearingDate: '',
      micr: '',
      bank: '',
      branch: '',
      schoolBank: '',
    };
    console.log('newRowId::::----', newRowId);

    // Update paymentmode array in payDetails
    setPayDetails((prevState) => ({
      ...prevState,
      paymentmode: [
        ...prevState.paymentmode,
        {
          paymentTypeId: 0,
          amount: 0,
          chequeOrDDNo: '',
          dateOfIssue: '',
          deppositDate: '',
          clearingDate: '',
          bankId: 0,
          payStatus: '',
        },
      ],
    }));

    // Update rows
    setRows([...rows, newRow]);
  };

  const handleDeleteRow = (id: number) => {
    if (id === 1) {
      return; // Prevent deletion of the first row
    }

    // Remove corresponding paymentmode object
    setPayDetails((prevState) => ({
      ...prevState,
      paymentmode: prevState.paymentmode.filter((payment, index) => index !== id - 1), // -2 because of index starting from 0 and skipping the first object
    }));
    console.log('id::::----', id);

    // Update rows
    const updatedRows = rows.filter((row) => row.id !== id);
    setRows(updatedRows);
  };

  const [selectedPaymentTypeId, setSelectedPaymentTypeId] = useState<number>(0);
  const [selectedBankId, setSelectedBankId] = useState(0);

  // Function to update payment type for a specific row
  const handlePaymentTypeChange = (event: SelectChangeEvent<number>, index: number) => {
    const { value } = event.target;
    setSelectedPaymentTypeId(Number(value));
    setPayDetails((prevState) => ({
      ...prevState,
      paymentmode: prevState.paymentmode.map((item, i) => {
        if (i === index) {
          return {
            ...item,
            paymentTypeId: Number(value),
          };
        }
        return item;
      }),
    }));
  };

  const handlePaymentModeChange = (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
    index: number,
    field: string
  ) => {
    const { value } = event.target;
    setPayDetails((prevState) => ({
      ...prevState,
      paymentmode: prevState.paymentmode.map((item, i) => {
        if (i === index) {
          return {
            ...item,
            [field]: value,
          };
        }
        return item;
      }),
    }));
  };
  const handleDateChange = (date: Dayjs | null, index: number, fieldName: string) => {
    const formattedDate = date ? dayjs(date).format('DD/MM/YYYY') : '';
    setPayDetails((prevState) => ({
      ...prevState,
      paymentmode: prevState.paymentmode.map((item, i) => {
        if (i === index) {
          return {
            ...item,
            [fieldName]: formattedDate,
          };
        }
        return item;
      }),
    }));
  };

  // Function to update bank ID for a specific row
  const handleBankChange = (event: SelectChangeEvent<number>, index: number) => {
    const { value } = event.target;
    setSelectedBankId(Number(value));
    setPayDetails((prevState) => ({
      ...prevState,
      paymentmode: prevState.paymentmode.map((item, i) => {
        if (i === index) {
          return {
            ...item,
            bankId: Number(value),
          };
        }
        return item;
      }),
    }));
  };

  const handleRemarksChange = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { value } = event.target;
    setRemarks(value);
    setPayDetails((prevState) => ({
      ...prevState,
      remarks: value,
    }));
  };
  const handleReceiptNoChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = event.target;
    const numberValue = Number(value);
    setReceiptNo(numberValue);
    console.log('numberValue::::----', numberValue);

    setPayDetails((prevState) => ({
      ...prevState,
      payingReceiptNo: numberValue,
    }));
  };

  const checkReceiptNumber = useCallback(
    async (receiptNo: number | undefined) => {
      console.log('receiptNo::::----', receiptNo);
      try {
        const response = await dispatch(CheckReceiptNo(receiptNo)).unwrap();
        console.log('response::::----', response);

        if (response) {
          if (response.result === 'Exist') {
            setError(true);
            setSnackbarOpen(true);
          }
        } else {
          setError(false);
        }
      } catch {
        console.error('Error checking receipt number:');
      }
    },
    [dispatch]
  );
  const handleClickOutside = useCallback(() => {
    if (receiptNumber) {
      checkReceiptNumber(receiptNumber);
    }
  }, [checkReceiptNumber, receiptNumber]);
  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };
  // Handle click outside TextField
  useEffect(() => {
    const handleClick = (e: any) => {
      const receiptNoElement = document.getElementById('receiptNumber');
      console.log('receiptNoElement::::----', receiptNoElement);

      if (receiptNoElement && !receiptNoElement.contains(e.target)) {
        handleClickOutside();
      }
    };

    document.addEventListener('click', handleClick);

    return () => {
      document.removeEventListener('click', handleClick);
    };
  }, [handleClickOutside]);

  const [cellLength, setCellLength] = useState(false);
  const [cellColumnLength, setCellColumnLength] = useState(false);

  // Function to handle cell selection
  const handleCellClick = useCallback(
    (rowIndex: number, columnIndex: number, feeTitle: any, termTitle: any, monthData: TermFeeStatsProps) => {
      const { amount, scholarship, discount, fine, balance } = monthData;
      // Update selected cell indices
      setFId(rowIndex);
      setTId(columnIndex);

      const termsArray = termFee.terms || [];
      const filteredTermsArray = termsArray.filter(
        (t: { termId: number; termTitle: number }) => t.termId === columnIndex
      );
      const termIds = filteredTermsArray.map((t: { termId: number; termTitle: number }) => t.termId);
      const term = filteredTermsArray.find((t: { termId: number; termTitle: number }) => t.termId === columnIndex);
      const termIdTook = term ? term.termId : null;

      const newHeaderValues: any = [...headerValues];
      newHeaderValues[columnIndex] = balance;
      setHeaderValues(newHeaderValues);

      // Check if cell is already selected
      const cellIndex = cellData.findIndex((cell) => cell.feeId === rowIndex && cell.termId === columnIndex);

      if (cellIndex !== -1) {
        // Cell already selected, deselect it
        // setSelectedCells((prevSelectedCells) =>
        //   prevSelectedCells.filter((cell) => !(cell.rowIndex === rowIndex && cell.columnIndex === columnIndex))
        // );
        // Remove the corresponding cell data from the array
        setCellData((prevCellData) =>
          prevCellData.filter((cell) => !(cell.feeId === rowIndex && cell.termId === columnIndex))
        );
        // Reduce the paid value from the sum
        // const newMonthSum = [...monthSum];
        // const balanceAmnt = parseFloat(balance);
        // newMonthSum[columnIndex] -= balanceAmnt;
        // setMonthSum(newMonthSum);
        // const newMonthSum = monthSum.map((sum, index) => (index === columnIndex ? sum - parseFloat(balance) : sum));
        // setMonthSum(newMonthSum);

        // Subtract the amount from the corresponding termId
        // Subtract the amount from the corresponding termId
        // Set the state with the new term sums (assuming you use React state)
        // Reduce the paid value from the total amount
        const cellDataSelectedAmount =
          cellData.find((cell) => cell.feeId === rowIndex && cell.termId === columnIndex)?.amount || 0;
        setTotalAmount((prevTotalAmount) => prevTotalAmount - cellDataSelectedAmount);
        setTotalScholarship((prevTotalAmount) => prevTotalAmount - Number(scholarship));
        setTotalDiscount((prevTotalAmount) => prevTotalAmount - Number(discount));
        setTotalFine((prevTotalAmount) => prevTotalAmount - Number(fine));
        // Remove the object from termfeepaying array
        setPayDetails((prevPayDetails) => ({
          ...prevPayDetails,
          totalAmount: prevPayDetails.totalAmount - Number(balance),
          grandTotal: prevPayDetails.grandTotal - Number(balance),
          remarks,
          payingDate,
          totalScholarship: prevPayDetails.totalScholarship - Number(scholarship),
          totalDiscount: prevPayDetails.totalDiscount - Number(discount),
          totalFine: prevPayDetails.totalFine - Number(fine),
          termfeepaying: prevPayDetails.termfeepaying.filter(
            (item) => item.feeId !== rowIndex || item.termId !== columnIndex
          ),
        }));

        setSelectedRows((prevSelectedRows) => {
          return prevSelectedRows.filter((row) => row.feeId !== rowIndex);
        });

        // setNewtotalAmount((prevTamount) => {
        //   return prevTamount.filter((row) => row.feeId !== rowIndex && row.termId !== columnIndex);
        // });

        setSelectedColumn((prevSelectedColumns) => {
          return prevSelectedColumns.filter((f) => f !== termIdTook);
        });
      } else {
        setCellData((prevCellData: any) => [
          ...prevCellData,
          { feeId: rowIndex, termId: columnIndex, feeTitle, termTitle, amount: balance, scholarship, discount, fine },
        ]);

        const termFeeDet = termFee.termFeeDetails || [];
        const termFeeStats = termFeeDet
          .filter((det: TermFeeDetailsType) => det.feeId === rowIndex)
          .flatMap((det: TermFeeDetailsType) => det.termFeeStats || []);
        const paidEntries = termFeeStats.filter((data: TermFeeStatsProps) => data.balance !== 0);

        const rowSelectedCells = cellData.filter((cell) => cell.feeId === rowIndex);
        const allRowCellsSelected = rowSelectedCells.length + 1 === paidEntries.length;
        setCellLength(allRowCellsSelected);

        const termFeeDetDatas = termFeeDet.find(
          (det: TermFeeDetailsType) =>
            det.feeId === rowIndex && det.termFeeStats && det.termFeeStats.some((f) => f.balance !== 0)
        );

        setSelectedRows((prevSelectedRows) => {
          if (allRowCellsSelected) {
            // Ensure termFeeDetDatas is an array
            const termFeeDetArray = Array.isArray(termFeeDetDatas) ? termFeeDetDatas : [termFeeDetDatas];

            // Create a Set of current IDs to ensure uniqueness (assuming each row has a unique `id`)
            const currentIds = new Set(prevSelectedRows.map((row) => row.feeId));

            // Filter out termFeeDetDatas that are already in the previous rows
            const newEntries = termFeeDetArray.filter((det) => !currentIds.has(det.feeId));

            // Return updated selectedRows state by concatenating unique new entries
            return [...prevSelectedRows, ...newEntries];
          }
          return prevSelectedRows;
        });

        // const TAmounts: { feeId: number; termId: number; balance: number }[] = [...newtotalAmount];
        // termFeeDet.forEach((cell) => {
        //   if (cell.termFeeStats && Array.isArray(cell.termFeeStats)) {
        //     cell.termFeeStats
        //       .filter((s) => cell.feeId === rowIndex && s.termId === columnIndex && s.balance !== 0)
        //       .forEach((stat) => {
        //         TAmounts.push({ feeId: cell.feeId, termId: columnIndex, balance: stat.balance });
        //       });
        //   }
        // });

        // setNewtotalAmount(TAmounts);
        // ============== column Auto check ================== //

        // .flatMap((det) => det.termFeeStats || []);
        const termFeeStats2 = termFeeDet.flatMap((det: TermFeeDetailsType) => det.termFeeStats || []);
        const paidEntries2 = termFeeStats2.filter(
          (data: TermFeeStatsProps) => data.balance !== 0 && termIds.includes(data.termId)
        );

        const columnSelectedCells = cellData.filter((cell) => cell.termId === columnIndex);
        const allColumnCellsSelected = columnSelectedCells.length + 1 === paidEntries2.length;

        setSelectedColumn((prevSelectedColumns) => {
          if (allColumnCellsSelected) {
            if (!prevSelectedColumns.includes(termIdTook)) {
              return [...prevSelectedColumns, termIdTook];
            } else if (prevSelectedColumns.includes(termIdTook)) {
              setSelectedColumn((innerPrevSelectedColumns) => {
                return innerPrevSelectedColumns.filter((f) => f !== termIdTook);
              });
            }
          }
          return prevSelectedColumns;
        });
        setCellColumnLength(allColumnCellsSelected);

        setTotalAmount((prevTotalAmount) => prevTotalAmount + Number(balance));
        setTotalScholarship((prevTotalAmount) => prevTotalAmount + Number(scholarship));
        setTotalDiscount((prevTotalAmount) => prevTotalAmount + Number(discount));
        setTotalFine((prevTotalAmount) => prevTotalAmount + Number(fine));
        // Add the object to termfeepaying array
        setPayDetails((prevPayDetails) => ({
          ...prevPayDetails,
          totalAmount: prevPayDetails.totalAmount + Number(balance),
          grandTotal: prevPayDetails.grandTotal + Number(balance),
          remarks,
          payingDate,
          totalScholarship: prevPayDetails.totalScholarship + Number(scholarship),
          totalDiscount: prevPayDetails.totalDiscount + Number(discount),
          totalFine: prevPayDetails.totalFine + Number(fine),
          termfeepaying: [
            ...prevPayDetails.termfeepaying,
            {
              feeId: rowIndex,
              termId: columnIndex,
              totalAmount: Number(balance),
              scholarship,
              discount,
              fine,
              grandTotal: Number(balance),
              payStatus: '',
              receiptDetailsId: 0,
            },
          ],
        }));

        // setSelectedRowCell(allRowCellsSelected)
      }

      // Clear the value from the text field when cell is unselected
      if (cellIndex !== -1) {
        const updatedHeaderValues = [...headerValues];
        updatedHeaderValues[columnIndex] = ''; // Clear the value
        setHeaderValues(updatedHeaderValues);
      }
    },
    [cellData, setFId, setTId, setCellData, payingDate, setTotalAmount, headerValues, remarks, termFee]
  );
  // const isRowSelected = (row) => selectedRows.some((selectedRow) => selectedRow.feeId === row.feeId);
  // Add this function to handle changes in the column text field
  // Function to handle header text field change

  const handleHeaderTextFieldChange = useCallback(
    (value: string, id: number, balance: number, row: TermFeeDetailsType) => {
      const numValue = Number(value);

      // Ensure the entered value is greater than 0 and less than or equal to the balance
      if (numValue <= balance) {
        setCellData((prevCellData) =>
          prevCellData.map((cell) =>
            cell.termId === id && cell.feeId === row.feeId ? { ...cell, amount: numValue } : cell
          )
        );

        // Update payDetails
        setPayDetails((prev) => ({
          ...prev,
          totalAmount: numValue,
          termfeepaying: prev.termfeepaying.map((fee) => (fee.termId === id ? { ...fee, totalAmount: numValue } : fee)),
        }));
      } else {
        console.warn('Invalid input amount: Must be greater than 0 and less than or equal to balance');
      }
    },
    [setCellData, setPayDetails]
  );

  const handleSelectAllPendingChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      setSelectAllPending(event.target.checked);
      setSelectedColumn([]);
      setSelectedCells([]);
      // If checkbox is checked, select all rows with balance !== 0

      const termFeeDet: TermFeeDetailsType[] = termFee.termFeeDetails || [];

      // const selectedAllAmounts = termFeeDet.filter(
      //   (row) => Array.isArray(row.termFeeStats) && row.termFeeStats.every((data) => data.balance !== 0).amount
      // );

      const TAmount = termFeeDet.reduce((acc, cell) => {
        if (cell.termFeeStats && Array.isArray(cell.termFeeStats)) {
          const totalAmnt = cell.termFeeStats
            .filter((s) => s.balance !== 0)
            .reduce((sum, stat) => sum + stat.balance, 0);
          acc += totalAmnt;
        }
        return acc;
      }, 0);

      setTotalAmount(TAmount);

      const TScholarship = termFeeDet.reduce((acc, cell) => {
        if (cell.termFeeStats && Array.isArray(cell.termFeeStats)) {
          const totalAmnt = cell.termFeeStats
            .filter((s) => s.balance !== 0)
            .reduce((sum, stat) => sum + stat.scholarship, 0);
          acc += totalAmnt;
        }
        return acc;
      }, 0);

      console.log('TScholarship::::----', TScholarship);
      setTotalScholarship(TScholarship);

      const TDiscount = termFeeDet.reduce((acc, cell) => {
        if (cell.termFeeStats && Array.isArray(cell.termFeeStats)) {
          const totalAmnt = cell.termFeeStats
            .filter((s) => s.balance !== 0)
            .reduce((sum, stat) => sum + stat.discount, 0);
          acc += totalAmnt;
        }
        return acc;
      }, 0);

      console.log('TDiscount::::----', TDiscount);
      setTotalScholarship(TDiscount);

      const TFine = termFeeDet.reduce((acc, cell) => {
        if (cell.termFeeStats && Array.isArray(cell.termFeeStats)) {
          const totalAmnt = cell.termFeeStats.filter((s) => s.balance !== 0).reduce((sum, stat) => sum + stat.fine, 0);
          acc += totalAmnt;
        }
        return acc;
      }, 0);

      console.log('TFine::::----', TFine);
      setTotalScholarship(TFine);
      if (event.target.checked) {
        // const selectedIds = payDetails
        //   .filter((row) => row.termFeeStats.every((data) => data.balance !== 0))
        //   .map((row) => row.feeId);
        // setSelectedRows(selectedIds);
        // console.log('selectedRows::::----', selectedRows);

        // Collect and sum amounts by termId
        // const amountsByTermId = termFeeDet.reduce((acc, cell) => {
        //   if (Array.isArray(cell.termFeeStats)) {
        //     cell.termFeeStats
        //       .filter((stat) => stat.balance !== 0)
        //       .forEach((stat) => {
        //         if (!acc[stat.termId]) {
        //           acc[stat.termId] = 0;
        //         }
        //         acc[stat.termId] += stat.balance;
        //       });
        //   }
        //   return acc;
        // }, {});

        // console.log('selectedAllAmounts::::----', selectedAllAmounts);
        // setSelectedAllAmount(amountsByTermId);

        const cellDataEntries: CellDataType[] = [];
        const termTitles = (termFee.terms || []).reduce((acc, term) => {
          acc[term.termId] = term.termTitle;
          return acc;
        }, {});

        termFeeDet.forEach((det) => {
          const termFeeStats = det.termFeeStats || [];

          termFeeStats.forEach((data) => {
            if (data.balance !== 0) {
              // Check if the entry already exists in cellDataEntries
              const existingEntryIndex = cellDataEntries.findIndex(
                (entry) => entry.feeId === det.feeId && entry.termId === data.termId
              );

              if (existingEntryIndex !== -1) {
                // Update the existing entry
                cellDataEntries[existingEntryIndex] = {
                  ...cellDataEntries[existingEntryIndex],
                  feeTitle: det.feeTitle,
                  termTitle: termTitles[data.termId] || '',
                  amount: data.balance,
                  scholarship: data.scholarship,
                  discount: data.discount,
                  fine: data.fine,
                };
              } else {
                // Create a new entry if it doesn't exist
                cellDataEntries.push({
                  termId: data.termId,
                  feeId: det.feeId,
                  feeTitle: det.feeTitle,
                  termTitle: termTitles[data.termId] || '',
                  amount: data.balance,
                  scholarship: data.scholarship,
                  discount: data.discount,
                  fine: data.fine,
                });
              }
            }
          });
        });

        console.log('cellDataEntries----::::', cellDataEntries);

        setCellData((prevCellData) => {
          // Create a copy of prevCellData to modify
          const updatedCellData = [...prevCellData];

          // Merge cellDataEntries into updatedCellData
          cellDataEntries.forEach((newEntry) => {
            const existingIndex = updatedCellData.findIndex(
              (entry) => entry.feeId === newEntry.feeId && entry.termId === newEntry.termId
            );

            if (existingIndex !== -1) {
              // Update the existing entry
              updatedCellData[existingIndex] = {
                ...updatedCellData[existingIndex],
                ...newEntry,
              };
            } else {
              // Add the new entry if it doesn't exist
              updatedCellData.push(newEntry);
            }
          });

          return updatedCellData;
        });

        console.log('cellData----::::', cellData);
        // ============ Selected rows ==============

        const rowSelectedCells = [...new Set(cellDataEntries.flatMap((cell) => cell.feeId))];

        const feeIdArray = termFeeDet.filter((det) => rowSelectedCells.includes(det.feeId));

        // const allRowCellsSelected = rowSelectedCells.length === termFeeId.length;
        // setCellLength(allRowCellsSelected);

        setSelectedRows(feeIdArray);

        // ==============================
        const columnSelectedCells = [...new Set(cellDataEntries.flatMap((cell) => cell.termId))];
        setSelectedColumn(columnSelectedCells);
        console.log('columnSelectedCells----::::', columnSelectedCells);

        const termFeeDet2: any[] = termFee.termFeeDetails || [];

        setPayDetails((prevPayDetails) => ({
          ...prevPayDetails,
          totalAmount: prevPayDetails.totalAmount + TAmount,
          grandTotal: prevPayDetails.grandTotal + TAmount,
          remarks,
          payingDate,
          totalScholarship: prevPayDetails.totalScholarship + TScholarship,
          totalDiscount: prevPayDetails.totalDiscount + TDiscount,
          totalFine: prevPayDetails.totalFine + TFine,
          termfeepaying: [
            ...prevPayDetails.termfeepaying,
            ...termFeeDet2.reduce((acc, det) => {
              const termFeeStats = det.termFeeStats || []; // Ensure termFeeStats is defined

              termFeeStats.forEach((data: any) => {
                if (data.balance !== 0) {
                  acc.push({
                    feeId: det.feeId, // Assuming row.feeId is defined in the context
                    termId: data.termId,
                    totalAmount: data.amount,
                    scholarship: data.scholarship,
                    discount: data.discount,
                    fine: data.fine,
                    grandTotal: data.amount, // Assuming grandTotal is the same as amount for each entry
                    payStatus: '',
                    receiptDetailsId: 0,
                  });
                }
              });

              return acc;
            }, []),
          ],
        }));
      } else {
        setSelectedRows([]); // Clear selected rows if checkbox is unchecked
        setTotalAmount(0);
        setCellData([]);
        // setSelectedAllAmount([]); // Reset the amountsByTermId to an empty object

        setPayDetails((prevPayDetails) => ({
          ...prevPayDetails,
          totalAmount: prevPayDetails.totalAmount + TAmount,
          grandTotal: prevPayDetails.grandTotal + TAmount,
          remarks,
          payingDate,
          totalScholarship: prevPayDetails.totalScholarship + TScholarship,
          totalDiscount: prevPayDetails.totalDiscount + TDiscount,
          totalFine: prevPayDetails.totalFine + TFine,
          termfeepaying: [],
        }));
      }
      console.log('payDetails::::----', payDetails);
      console.log('selectedRows::::----', selectedRows);
    },
    [termFee, payingDate, remarks, payDetails, cellData, selectedRows]
  );

  const handleRowClick = useCallback(
    (row: TermFeeDetailsType) => {
      // const feeId = { row };
      const { feeTitle } = row;
      const monthData = row.termFeeStats && row.termFeeStats.find((data) => ({ ...data }));
      const paid = row.termFeeStats && row.termFeeStats.find((data) => data.balance !== 0); // Check if paid value is 0
      // const feeTitle = row.map((data) => data.feeTitle);
      const termFeeStats = row.termFeeStats || []; // To ensure termFeeStats array is not null or undefined
      const paidEntries = termFeeStats.filter((data) => data.balance !== 0); // Filter paid entries
      const amounts = paidEntries.map((data) => ({
        termId: data.termId,
        balance: data.balance,
        scholarship: data.scholarship,
        discount: data.discount,
        fine: data.fine,
      }));

      const termTitles =
        termFee.terms && termFee.terms.map((data: TermsType) => ({ termId: data.termId, termTitle: data.termTitle }));

      console.log('fId----::::', fId);
      console.log('amount----::::', amounts);
      console.log('feeTitle----::::', row.feeTitle);
      console.log('termTitles----::::', termTitles);
      console.log('paidEntries----::::', paidEntries);

      const termFeeDet: TermFeeDetailsType[] = termFee.termFeeDetails || [];
      // Toatal Amounts in row
      const TAmount = termFeeDet
        .filter((f) => f.feeId === row.feeId)
        .reduce((acc, cell) => {
          if (cell.termFeeStats && Array.isArray(cell.termFeeStats)) {
            const totalAmnt = cell.termFeeStats
              .filter((s) => s.balance !== 0)
              .reduce((sum, stat) => sum + stat.balance, 0);
            acc += totalAmnt;
          }
          return acc;
        }, 0);

      console.log('TAmount----::::', TAmount);

      // ======================

      // Toatal Scholarship Amounts in row
      const TScholarship = termFeeDet
        .filter((f) => f.feeId === row.feeId)
        .reduce((acc, cell) => {
          if (cell.termFeeStats && Array.isArray(cell.termFeeStats)) {
            const totalAmnt = cell.termFeeStats
              .filter((s) => s.balance !== 0)
              .reduce((sum, stat) => sum + stat.scholarship, 0);
            acc += totalAmnt;
          }
          return acc;
        }, 0);

      console.log('TScholarship----::::', TScholarship);

      // Toatal Discount Amounts in row
      const TDiscount = termFeeDet
        .filter((f) => f.feeId === row.feeId)
        .reduce((acc, cell) => {
          if (cell.termFeeStats && Array.isArray(cell.termFeeStats)) {
            const totalAmnt = cell.termFeeStats
              .filter((s) => s.balance !== 0)
              .reduce((sum, stat) => sum + stat.discount, 0);
            acc += totalAmnt;
          }
          return acc;
        }, 0);

      console.log('TDiscount----::::', TDiscount);

      // Toatal Fine Amounts in row
      const TFine = termFeeDet
        .filter((f) => f.feeId === row.feeId)
        .reduce((acc, cell) => {
          if (cell.termFeeStats && Array.isArray(cell.termFeeStats)) {
            const totalAmnt = cell.termFeeStats
              .filter((s) => s.balance !== 0)
              .reduce((sum, stat) => sum + stat.fine, 0);
            acc += totalAmnt;
          }
          return acc;
        }, 0);

      console.log('TFine----::::', TFine);

      if (!paid) {
        // If paid value is not 0, return without updating selectedRows
        // const newCellData = cellData.filter((data) => data.feeTitle !== feeTitle);
        // setCellData(newCellData);
        // setSelectedRows([]);
        return;
      }
      console.log('selectedCells----::::', selectedCells);

      const selectedIndex = selectedRows?.indexOf(row);
      let newSelected: TermFeeDetailsType[] = [];

      if (selectedIndex === -1) {
        // If feeId is not already selected, add it to the array
        newSelected = newSelected.concat(selectedRows, row);
        // setCellData((prevCellData) => [...prevCellData, { feeTitle, termTitle: '', amount }]);
        // Loop through each amount and set cellData accordingly
        // Update cellData with new termTitles
        const cellDataEntries: CellDataType[] = [];

        // Loop through termTitles to match termId with termFeeStats and create cellDataEntries
        termTitles.forEach((term: { termId: number; termTitle: string }) => {
          const matching = amounts.find((amount) => amount.termId === term.termId);
          if (matching) {
            // Check if the entry already exists in cellDataEntries
            const existingEntryIndex = cellDataEntries.findIndex(
              (entry) => entry.feeId === row.feeId && entry.termId === term.termId
            );

            if (existingEntryIndex !== -1) {
              // Update the existing entry
              cellDataEntries[existingEntryIndex] = {
                ...cellDataEntries[existingEntryIndex],
                feeTitle,
                termTitle: term.termTitle,
                amount: matching.balance,
                termFeeStats: row.termFeeStats || [],
                scholarship: matching.scholarship,
                discount: matching.discount,
                fine: matching.fine,
              };
            } else {
              // Create a new entry if it doesn't exist
              cellDataEntries.push({
                feeId: row.feeId,
                termId: term.termId,
                feeTitle,
                termTitle: term.termTitle,
                amount: matching.balance,
                termFeeStats: row.termFeeStats || [],
                scholarship: matching.scholarship,
                discount: matching.discount,
                fine: matching.fine,
              });
            }

            console.log('matching----::::', matching);
          }
        });

        setCellData((prevCellData) => {
          // Create a copy of prevCellData to modify
          const updatedCellData = [...prevCellData];

          // Merge cellDataEntries into updatedCellData
          cellDataEntries.forEach((newEntry) => {
            const existingIndex = updatedCellData.findIndex(
              (entry) => entry.feeId === newEntry.feeId && entry.termId === newEntry.termId
            );

            if (existingIndex !== -1) {
              // Update the existing entry
              updatedCellData[existingIndex] = {
                ...updatedCellData[existingIndex],
                ...newEntry,
              };
            } else {
              // Add the new entry if it doesn't exist
              updatedCellData.push(newEntry);
            }
          });

          return updatedCellData;
        });

        // const selectedCellData = cellData.filter((cell) => !(cell.feeId === row.feeId));
        // setCellData(selectedCellData);
        // console.log('selectedCellData----::::', selectedCellData);

        const termFeeStat = row.termFeeStats || [];

        const termIDs = termFeeStat.map((m) => m.termId);
        // Filter newtotalAmount to exclude entries that match row.feeId and termId in termFeeStat
        // const filteredBalances = cellData.filter((f) => termIDs.filter((i) => i !== f.termId));

        const filteredBalances = cellData.flatMap((f) => {
          return f.feeId === row.feeId && termIDs.includes(f.termId) ? [f] : [];
        });

        const filteredBalanceAmount = filteredBalances.reduce((sum, obj) => sum + obj.amount, 0);

        setTotalAmount((prevAmount) => {
          const newTotalAmount = prevAmount + TAmount - filteredBalanceAmount;
          return newTotalAmount;
        });
        // ============= COLUMN AUTO SELECT ========================= //
        // Flatten termFeeStats arrays to get termId values from selectedRows
        const selectedTermFeeStats = newSelected.flatMap((cell) => cell.termFeeStats.map((stat) => stat.termId));
        console.log('selectedTermFeeStats::::----', selectedTermFeeStats);

        // Flatten termFeeStats arrays to get termId values from termFeeDet
        const termFeeStats2 = termFeeDet.flatMap((det) => det.termFeeStats || []);
        console.log('termFeeStats2::::----', termFeeStats2);

        // Create a map to store termFeeStats2 objects by termId
        const termFeeStats2Map: { [key: number]: TermFeeStatsProps[] } = termFeeStats2.reduce((acc, stat) => {
          if (!acc[stat.termId]) {
            acc[stat.termId] = [];
          }
          acc[stat.termId].push(stat);
          return acc;
        }, {} as { [key: number]: TermFeeStatsProps[] });

        // Filter and store the matching termFeeStats2 based on selectedTermFeeStats termIds
        const matchedTermFeeStats = selectedTermFeeStats.map((termId) => {
          return {
            termId,
            stats: termFeeStats2Map[termId] || [],
          };
        });

        console.log('matchedTermFeeStats::::----', matchedTermFeeStats);

        // Check if the lengths of termFeeStats arrays for each termId are equal
        const lengthCheckResults = matchedTermFeeStats.map(({ termId, stats }) => {
          const selectedStatsLength = selectedTermFeeStats.filter((id) => id === termId).length;
          const termFeeStats2Length = stats.length;
          return {
            termId,
            lengthsAreEqual: selectedStatsLength === termFeeStats2Length,
            selectedStatsLength,
            termFeeStats2Length,
          };
        });

        console.log('lengthCheckResults::::----', lengthCheckResults);
        // Update selectedColumn state with unique values from columnSelectedCells
        setSelectedColumn((prevSelectedColumns) => {
          // Get term IDs from selectedTermFeeStats and filter based on length check
          const newSelectedColumns = selectedTermFeeStats
            .filter((termId) => {
              // Find the length check result for the current termId
              const lengthCheckResult = lengthCheckResults.find((result) => result.termId === termId);
              // Only include the termId if the lengths are equal
              return lengthCheckResult && lengthCheckResult.lengthsAreEqual;
            })
            .filter((termId) => !prevSelectedColumns.includes(termId)); // Exclude already selected columns

          console.log('newSelectedColumns::::----', newSelectedColumns);

          // Combine previous selected columns with new unique termIds
          const updatedSelectedColumns = [...prevSelectedColumns, ...newSelectedColumns];

          // Use a Set to ensure uniqueness of termIds
          const uniqueSelectedColumns = [...new Set(updatedSelectedColumns)];

          // Return updated selectedColumn state with unique termIds
          return uniqueSelectedColumns;
        });

        // console.log('amountsValueArray----::::', amountsValueArray);
        console.log('totalAmount----::::', totalAmount);
        console.log('filteredBalanceAmount----::::', filteredBalanceAmount);
        console.log('filteredBalances----::::', filteredBalances);

        setPayDetails((prevPayDetails) => ({
          ...prevPayDetails,
          totalAmount: prevPayDetails.totalAmount + TAmount,
          grandTotal: prevPayDetails.grandTotal + TAmount,
          remarks,
          payingDate,
          totalScholarship: prevPayDetails.totalScholarship + TScholarship,
          totalDiscount: prevPayDetails.totalDiscount + TDiscount,
          totalFine: prevPayDetails.totalFine + TFine,
          termfeepaying: [
            ...prevPayDetails.termfeepaying,
            ...paidEntries.map((entry) => ({
              feeId: row.feeId, // Assuming row.feeId is defined in the context
              termId: entry.termId,
              totalAmount: entry.balance,
              scholarship: entry.scholarship,
              discount: entry.discount,
              fine: entry.fine,
              grandTotal: entry.balance, // Assuming grandTotal is the same as amount for each entry
              payStatus: '',
              receiptDetailsId: 0,
            })),
          ],
        }));
      } else if (selectedIndex === 0) {
        // // // Assuming newSelected is provided and structured properly
        const selectedTermFeeStats = newSelected.flatMap((cell) => cell.termFeeStats.map((stat) => stat.termId));
        console.log('selectedTermFeeStats121::::----', selectedTermFeeStats);
        setSelectedColumn((prevSelectedColumns) => {
          const deselectedColumns = prevSelectedColumns.filter((termId) => selectedTermFeeStats.includes(termId));
          console.log('deselectedColumns::::----', deselectedColumns);

          return deselectedColumns;
        });
        // setSelectedColumn((prevSelectedColumns) => {
        //   return prevSelectedColumns.filter((f) => f !== 4);
        // });
        //  If paid value is not 0, return without updating selectedRows
        setCellData((prevSelectedRows) => prevSelectedRows.filter((cell) => cell.feeId !== row.feeId));

        // setSelectedRows((prevSelectedRows) => prevSelectedRows.filter((cell) => cell.feeId !== row.feeId));
        // setMonthSum([]);
        setTotalAmount((prevAmount) => prevAmount - TAmount);
        console.log('totalAmount----::::', totalAmount);

        newSelected = newSelected.concat(selectedRows.slice(1));

        setPayDetails((prevPayDetails) => ({
          ...prevPayDetails,
          totalAmount: prevPayDetails.totalAmount - TAmount,
          grandTotal: prevPayDetails.grandTotal - TAmount,
          remarks,
          payingDate,
          totalScholarship: prevPayDetails.totalScholarship - TScholarship,
          totalDiscount: prevPayDetails.totalDiscount - TDiscount,
          totalFine: prevPayDetails.totalFine - TFine,
          termfeepaying: prevPayDetails.termfeepaying.filter((item) => item.feeId !== row.feeId),
        }));
      } else if (selectedIndex === selectedRows.length - 1) {
        const selectedTermFeeStats = newSelected.flatMap((cell) => cell.termFeeStats.map((stat) => stat.termId));
        console.log('selectedTermFeeStats121::::----', selectedTermFeeStats);
        setSelectedColumn((prevSelectedColumns) => {
          // Filter and store the values that are not present in selectedTermFeeStats
          const deselectedColumns = prevSelectedColumns.filter((termId) => selectedTermFeeStats.includes(termId));
          console.log('deselectedColumns::::----', deselectedColumns);

          // Return the deselected columns to update the state
          return deselectedColumns;
        });
        const termFeeStat = row.termFeeStats || [];
        // If feeId is the last element, remove it from the array
        // const updatedCellData = cellData.filter((cell) => cell.feeId !== row.feeId);
        // setCellData(updatedCellData);
        // setSelectedRows((prevSelectedRows) => prevSelectedRows.filter((cell) => cell.feeId !== row.feeId));
        setCellData((prevSelectedRows) => prevSelectedRows.filter((cell) => cell.feeId !== row.feeId));
        setTotalAmount((prevAmount) => prevAmount - TAmount);
        console.log('totalAmount----::::', totalAmount);

        setPayDetails((prevPayDetails) => ({
          ...prevPayDetails,
          totalAmount: prevPayDetails.totalAmount - TAmount,
          grandTotal: prevPayDetails.grandTotal - TAmount,
          remarks,
          payingDate,
          totalScholarship: prevPayDetails.totalScholarship - TScholarship,
          totalDiscount: prevPayDetails.totalDiscount - TDiscount,
          totalFine: prevPayDetails.totalFine - TFine,
          termfeepaying: prevPayDetails.termfeepaying.filter((item) => item.feeId !== row.feeId),
        }));

        newSelected = newSelected.concat(selectedRows.slice(0, -1));
      } else if (selectedIndex > 0) {
        const selectedTermFeeStats = newSelected.flatMap((cell) => cell.termFeeStats.map((stat) => stat.termId));
        console.log('selectedTermFeeStats121::::----', selectedTermFeeStats);
        setSelectedColumn((prevSelectedColumns) => {
          // Filter and store the values that are not present in selectedTermFeeStats
          const deselectedColumns = prevSelectedColumns.filter((termId) => selectedTermFeeStats.includes(termId));
          console.log('deselectedColumns::::----', deselectedColumns);

          // Return the deselected columns to update the state
          return deselectedColumns;
        });
        const termFeeStat = row.termFeeStats || [];
        // If feeId is in the middle, remove it from the array
        // const updatedCellData = cellData.filter((cell) => cell.feeId !== row.feeId);
        // setCellData(updatedCellData);
        // setSelectedRows((prevSelectedRows) => prevSelectedRows.filter((cell) => cell.feeId !== row.feeId));
        setCellData((prevSelectedRows) => prevSelectedRows.filter((cell) => cell.feeId !== row.feeId));
        setTotalAmount((prevAmount) => prevAmount - TAmount);

        console.log('totalAmount----::::', totalAmount);

        setPayDetails((prevPayDetails) => ({
          ...prevPayDetails,
          totalAmount: prevPayDetails.totalAmount - TAmount,
          grandTotal: prevPayDetails.grandTotal - TAmount,
          remarks,
          payingDate,
          totalScholarship: prevPayDetails.totalScholarship - TScholarship,
          totalDiscount: prevPayDetails.totalDiscount - TDiscount,
          totalFine: prevPayDetails.totalFine - TFine,
          termfeepaying: prevPayDetails.termfeepaying.filter((item) => item.feeId !== row.feeId),
        }));

        newSelected = newSelected.concat(selectedRows.slice(0, selectedIndex), selectedRows.slice(selectedIndex + 1));
      }
      // Update the state with the new selected feeIds

      setSelectedRows(newSelected);
      console.log('selectedColumn::::----', selectedColumn);
      console.log('newSelectedRows::::----', newSelected);
      console.log('payDetails::::----', payDetails);
      console.log('cellData::::----', cellData);
    },
    [fId, selectedCells, selectedRows, payDetails, selectedColumn, cellData, termFee, payingDate, remarks, totalAmount]
  );

  // Define your handleColumnCheckboxClick function
  const handleColumnCheckboxClick = useCallback(
    (item: { termId: number; termTitle: string }) => {
      const termFeeDet: TermFeeDetailsType[] = termFee.termFeeDetails || [];
      // const paid = row.termFeeStats && row.termFeeStats.find((data) => data.balance !== 0);
      // Ensure termFeeStats is an array
      const termFeeStats = termFeeDet.flatMap((det) => det.termFeeStats || []);
      const paidEntries = termFeeStats.find((data) => {
        console.log('Checking data:', data);
        return item.termId === data.termId && data.balance !== 0;
      });
      console.log('paidEntries2233::::----', paidEntries);
      //
      if (!paidEntries) {
        return;
      }

      const TAmount = termFeeDet.reduce((acc, cell) => {
        // Check if termFeeStats exists and is an array
        if (cell.termFeeStats && Array.isArray(cell.termFeeStats)) {
          const termFeeStat = cell.termFeeStats.find((s) => s.termId === item.termId && s.balance !== 0);
          if (termFeeStat) {
            acc += termFeeStat.balance;
          }
        }
        return acc;
      }, 0);

      const TScholarship = termFeeDet.reduce((acc, cell) => {
        // Check if termFeeStats exists and is an array
        if (cell.termFeeStats && Array.isArray(cell.termFeeStats)) {
          const termFeeStat = cell.termFeeStats.find((s) => s.termId === item.termId && s.balance !== 0);
          if (termFeeStat) {
            acc += termFeeStat.scholarship;
          }
        }
        return acc;
      }, 0);
      const TDiscount = termFeeDet.reduce((acc, cell) => {
        // Check if termFeeStats exists and is an array
        if (cell.termFeeStats && Array.isArray(cell.termFeeStats)) {
          const termFeeStat = cell.termFeeStats.find((s) => s.termId === item.termId && s.balance !== 0);
          if (termFeeStat) {
            acc += termFeeStat.discount;
          }
        }
        return acc;
      }, 0);
      const TFine = termFeeDet.reduce((acc, cell) => {
        // Check if termFeeStats exists and is an array
        if (cell.termFeeStats && Array.isArray(cell.termFeeStats)) {
          const termFeeStat = cell.termFeeStats.find((s) => s.termId === item.termId && s.balance !== 0);
          if (termFeeStat) {
            acc += termFeeStat.fine;
          }
        }
        return acc;
      }, 0);

      const newSelected = [...selectedColumn];

      if (newSelected.includes(item.termId)) {
        // If the termId is already selected, remove it
        newSelected.splice(newSelected.indexOf(item.termId), 1);

        const matchedObjects2 = cellData.filter((f) => f.termId !== item.termId);
        console.log('matchedObjects2----::::', matchedObjects2);

        setCellData(matchedObjects2);

        setSelectedColumn((prevSelectedColumns) => {
          return prevSelectedColumns.filter((f) => f !== newSelected);
        });
        // const newCellData = matchedObjects.filter((data) =>
        //   selectedRows.some((selectedRow) => data.feeId !== selectedRow.feeId && data.termId !== item.termId)
        // );

        // setCellData(matchedObjects);

        // const matchedObjects = cellData.filter((cell) =>
        //   selectedRows.some((selectedRow) => {
        //     // Check if selectedRow.termFeeStats is an array before using includes
        //     if (Array.isArray(selectedRow.termFeeStats)) {
        //       return cell.feeId === selectedRow.feeId && !selectedRow.termFeeStats.includes(item.termId);
        //     }
        //     // If selectedRow.termFeeStats is not an array, return false
        //     return false;
        //   })
        // );

        // });

        setTotalAmount((prevAmount) => prevAmount - TAmount);

        setPayDetails((prevPayDetails) => ({
          ...prevPayDetails,
          totalAmount: prevPayDetails.totalAmount - TAmount,
          grandTotal: prevPayDetails.grandTotal - TAmount,
          remarks,
          payingDate,
          totalScholarship: prevPayDetails.totalScholarship - TScholarship,
          totalDiscount: prevPayDetails.totalDiscount - TDiscount,
          totalFine: prevPayDetails.totalFine - TFine,
          termfeepaying: prevPayDetails.termfeepaying.filter((i) => i.termId !== item.termId),
        }));

        const cellFeeIds = cellData.map((cell) => cell.feeId); // Extract feeId from cellData

        setSelectedRows((prev) => prev.filter((f) => !cellFeeIds.includes(f.feeId)));
      } else {
        // Otherwise, add it to the selected termIds

        newSelected.push(item.termId);
        const newMonthSum = [...monthSum];
        newMonthSum[item.termId] += TAmount;
        // console.log('newMonthSum----::::', newMonthSum);
        setMonthSum(newMonthSum);

        // Filter newtotalAmount to exclude entries that match row.feeId and termId in termFeeStat
        // const filteredBalances = cellData.filter((f) => termIDs.filter((i) => i !== f.termId));
        let filteredBalances: any[] = [];

        termFeeDet.forEach((det) => {
          const matches = cellData.filter((f) => f.feeId === det.feeId && f.termId === item.termId);
          filteredBalances = filteredBalances.concat(matches);
        });

        const filteredBalanceAmount = filteredBalances.reduce((sum, obj) => sum + obj.amount, 0);

        setTotalAmount((prevAmount) => {
          const newTotalAmount = prevAmount + TAmount - filteredBalanceAmount;
          return newTotalAmount;
        });

        console.log('filteredBalances----::::', filteredBalances);
        console.log('filteredBalanceAmount----::::', filteredBalanceAmount);

        // setTotalAmount((prevAmount) => prevAmount + TAmount);
        //
        const cellDataEntries: CellDataType[] = [];

        termFeeDet.forEach((det) => {
          const termFeeStats2 = det.termFeeStats || []; // Ensure termFeeStats is defined

          termFeeStats2.forEach((data) => {
            if (item.termId === data.termId && data.balance !== 0) {
              // Check if the entry already exists in cellDataEntries
              const existingEntryIndex = cellDataEntries.findIndex(
                (entry) => entry.feeId === det.feeId && entry.termId === data.termId
              );

              if (existingEntryIndex !== -1) {
                // Update the existing entry
                cellDataEntries[existingEntryIndex] = {
                  ...cellDataEntries[existingEntryIndex],
                  feeTitle: det.feeTitle,
                  termTitle: item.termTitle,
                  amount: data.balance,
                  scholarship: data.scholarship,
                  discount: data.discount,
                  fine: data.fine,
                  termFeeStats: det.termFeeStats,
                };
              } else {
                // Create a new entry if it doesn't exist
                cellDataEntries.push({
                  termId: data.termId,
                  feeId: det.feeId,
                  feeTitle: det.feeTitle,
                  termTitle: item.termTitle,
                  amount: data.balance,
                  scholarship: data.scholarship,
                  discount: data.discount,
                  fine: data.fine,
                  termFeeStats: det.termFeeStats,
                });
              }
            }
          });
        });

        console.log('cellDataEntries----::::', cellDataEntries);

        setCellData((prevCellData) => {
          // Create a copy of prevCellData to modify
          const updatedCellData = [...prevCellData];

          // Merge cellDataEntries into updatedCellData
          cellDataEntries.forEach((newEntry) => {
            const existingIndex = updatedCellData.findIndex(
              (entry) => entry.feeId === newEntry.feeId && entry.termId === newEntry.termId
            );

            if (existingIndex !== -1) {
              // Update the existing entry
              updatedCellData[existingIndex] = {
                ...updatedCellData[existingIndex],
                ...newEntry,
              };
            } else {
              // Add the new entry if it doesn't exist
              updatedCellData.push(newEntry);
            }
          });

          return updatedCellData;
        });

        console.log('cellData----::::', cellData);
        const termFeeDet2: any[] = termFee.termFeeDetails || [];
        setPayDetails((prevPayDetails) => ({
          ...prevPayDetails,
          totalAmount: prevPayDetails.totalAmount + TAmount,
          grandTotal: prevPayDetails.grandTotal + TAmount,
          remarks,
          payingDate,
          totalScholarship: prevPayDetails.totalScholarship + TScholarship,
          totalDiscount: prevPayDetails.totalDiscount + TDiscount,
          totalFine: prevPayDetails.totalFine + TFine,
          termfeepaying: [
            ...prevPayDetails.termfeepaying,
            ...termFeeDet2.reduce((acc, det) => {
              const termFeeStats2 = det.termFeeStats || []; // Ensure termFeeStats is defined

              termFeeStats2.forEach((data: any) => {
                if (item.termId === data.termId && data.balance !== 0) {
                  acc.push({
                    feeId: det.feeId, // Assuming row.feeId is defined in the context
                    termId: item.termId,
                    totalAmount: data.amount,
                    scholarship: data.scholarship,
                    discount: data.discount,
                    fine: data.fine,
                    grandTotal: data.amount, // Assuming grandTotal is the same as amount for each entry
                    payStatus: '',
                    receiptDetailsId: 0,
                  });
                }
              });

              return acc;
            }, []),
          ],
        }));
      }

      // ===========================
      const termFeeStats2 = termFeeDet.filter((det) => det.feeId).flatMap((det) => det.termFeeStats || []);
      const paidEntries2 = [...new Set(termFeeStats2.filter((data) => data.balance !== 0).map((m) => m.termId))];
      console.log('paidEntries2----::::', paidEntries2);
      const newColumnSelectedCells = cellData.flatMap((cell) => cell.termId);
      const cellFeeIds = cellData.map((cell) => cell.feeId); // Extract feeId from cellData
      const feeIdArray = termFeeDet.filter((det) => cellFeeIds.includes(det.feeId));

      const allRowCellsSelected = newSelected.length === paidEntries2.length;
      setCellLength(allRowCellsSelected);

      if (allRowCellsSelected) {
        setSelectedRows(feeIdArray);
      } else {
        setSelectedRows((prev) => prev.filter((f) => cellFeeIds.includes(f.feeId)));
      }
      console.log('allRowCellsSelected----::::', allRowCellsSelected);
      console.log('newColumnSelectedCells----::::', newColumnSelectedCells);
      // console.log('cellTermFeeStats----::::', cellTermFeeStats);
      console.log('feeIdArray----::::', feeIdArray);
      // ===========================
      setSelectedColumn(newSelected);

      // setMonthSum(newMonthSum);
      console.log('monthSum----::::', monthSum);
      console.log('item----::::', item);
      console.log('selectedColumn----::::', selectedColumn);
      console.log('newSelected----::::', newSelected);
      console.log('payDetails----::::', payDetails);
    },
    [
      termFee,
      selectedColumn,
      setSelectedColumn,
      monthSum,
      payDetails,
      payingDate,
      remarks,
      setMonthSum,
      setTotalAmount,
      cellData,
    ]
  );

  React.useEffect(() => {
    console.log('payDetails1111----::::', payDetails);
  }, [payDetails]);

  const handleReset = () => {
    loadStudentTermFeeDataList(initialStudentTermFeeListRequest);
    setSelectedRows([]);
    setSelectedCells([]);
    setSelectAllPending(false);
    setSelectedColumn([]);
    setTotalAmount(0);
    setTotalDiscount(0);
    setTotalFine(0);
    setTotalScholarship(0);
    setPayingDate('');
    setPayDetails(initialPayDetails);
    setRows([
      {
        id: 1,
        paymentType: '',
        amount: '',
        chequeNo: '',
        dateOfIssue: '',
        deppositDate: '',
        clearingDate: '',
        micr: '',
        bank: '',
        branch: '',
        schoolBank: '',
      },
    ]);
    setRemarks('');
    setCellData([]);
    setReceiptNo(0);
  };
  const onCancel = () => {
    setSelectedRows([]);
    setSelectedCells([]);
    setSelectAllPending(false);
    setSelectedColumn([]);
    setTotalAmount(0);
    setTotalDiscount(0);
    setTotalFine(0);
    setTotalScholarship(0);
    setPayingDate('');
    setPayDetails(initialPayDetails);
    setRows([
      {
        id: 1,
        paymentType: '',
        amount: '',
        chequeNo: '',
        dateOfIssue: '',
        deppositDate: '',
        clearingDate: '',
        micr: '',
        bank: '',
        branch: '',
        schoolBank: '',
      },
    ]);
    setRemarks('');
    setCellData([]);
    setReceiptNo(0);
  };

  const inputRefs: MutableRefObject<(HTMLInputElement | null)[]> = useRef([]);
  // Function to set ref for input fields
  const setInputRef = (index: number, ref: any) => {
    inputRefs.current[index] = ref;
  };

  const PayFeeCollectionListColumns: DataTableColumn<any>[] = useMemo(() => {
    const baseColumns: DataTableColumn<any>[] = [
      {
        name: 'checkbox',
        renderHeader: () => {
          return (
            <Checkbox
              aria-label="all checkbox"
              color="primary"
              checked={selectedRows?.length > 0}
              indeterminate={selectedRows?.length > 0 && selectedRows.length < termFeeDetails.length}
              onChange={handleSelectAllPendingChange}
              sx={{ mt: 0.5 }}
            />
          );
        },
        renderCell: (row) => {
          const isSelected = (r: TermFeeDetailsType) =>
            selectedRows?.indexOf(r) !== -1 || selectedRows.some((ro) => ro.feeId === row.feeId);

          // const termIds =
          //   row.termFeeStats &&
          //   row.termFeeStats
          //     .filter((data: { termId: any; balance: number }) => data.termId && data.balance !== 0)
          //     .map((data: { termId: any; balance: number }) => ({
          //       termId: data.termId,
          //       balance: data.balance,
          //     }));
          // const monthData = row.termFeeStats && row.termFeeStats.find((data) => ({ ...data }));

          return (
            <Checkbox
              aria-label="row wise checkbox"
              onClick={() => {
                handleRowClick(row);
              }}
              checked={isSelected(row)}
            />
          );
        },
      },
      {
        name: 'fee',
        headerLabel: 'Fee Title',
        renderCell: (row) => (
          <Typography width={120} color="primary" px={1} textAlign="start" variant="subtitle2" fontSize={14}>
            {row.feeTitle}
          </Typography>
        ),
      },
      {
        name: 'total',
        headerLabel: 'Total',
        renderCell: (row) => (
          <Typography px={1} width={80} textAlign="start" variant="subtitle1" fontSize={13}>
            {row.totalFee}
          </Typography>
        ),
      },
      {
        name: 'paid',
        headerLabel: 'Paid',
        renderCell: (row) => (
          <Typography px={1} width={80} textAlign="start" variant="subtitle1" fontSize={13}>
            {row.paid}
          </Typography>
        ),
      },
      {
        name: 'balance',
        headerLabel: 'Balance',
        renderCell: (row) => (
          <Typography px={1} width={80} textAlign="start" variant="subtitle1" fontSize={13}>
            {row.balance}
          </Typography>
        ),
      },

      ...(termFee.terms
        ? termFee.terms.map((item: any) => ({
            name: `${item.termId}`,
            renderHeader: () => {
              const termFeeDet = termFee.termFeeDetails || [];
              const termFeeStats = termFeeDet.flatMap((det: TermFeeDetailsType) => det.termFeeStats || []);
              const termFeeData = termFeeStats.find((data: TermFeeStatsProps) => data.termId === item.termId);
              const balanceAmount = termFeeData ? termFeeData.balance : 0;

              const termSum: { [key: number]: TermSum } = {};
              const seenCombinations: Set<string> = new Set();

              // Iterate over the cellData array and sum amounts by termId, also store feeId
              cellData.forEach(({ termId, amount, feeId, discount, scholarship, fine }) => {
                const combinationKey: string = `${termId}-${feeId}`;

                if (!seenCombinations.has(combinationKey)) {
                  if (!termSum[termId]) {
                    termSum[termId] = { termId, amount: 0, feeId: null, discount: 0, scholarship: 0, fine: 0 };
                  }

                  termSum[termId].amount += amount;
                  termSum[termId].discount += discount;
                  termSum[termId].scholarship += scholarship;
                  termSum[termId].fine += fine;
                  termSum[termId].feeId = feeId; // Assuming the feeId remains the same for each termId
                  seenCombinations.add(combinationKey);
                }
              });

              const newTermSum: TermSum[] = [];

              // Convert the dictionary back to the newTermSum array format
              Object.entries(termSum).forEach(([termId, { amount, feeId, discount, scholarship, fine }]) => {
                newTermSum.push({
                  termId: parseInt(termId, 10),
                  amount,
                  feeId: parseInt(feeId as string, 10),
                  discount,
                  scholarship,
                  fine,
                });
              });

              const sumValue: number = newTermSum.find((f) => f.termId === item.termId)?.amount || 0;

              const totalAmounts: number = newTermSum.reduce((acc, curr) => acc + curr.amount, 0);
              const totalDiscounts: number = newTermSum.reduce((acc, curr) => acc + curr.discount, 0);
              const totalScholarships: number = newTermSum.reduce((acc, curr) => acc + curr.scholarship, 0);
              const totalFines: number = newTermSum.reduce((acc, curr) => acc + curr.fine, 0);

              if (totalAmounts || totalDiscounts || totalScholarships || totalFines) {
                setTotalAmount(totalAmounts);
                setTotalDiscount(totalDiscounts);
                setTotalScholarship(totalScholarships);
                setTotalFine(totalFines);
              }

              const isSelectedColumns = (id: number) =>
                selectedColumn?.indexOf(id) !== -1 || selectedColumn.includes(item.termId);
              return (
                <Stack key={item.termId} className="month-name" direction="column" alignItems="center">
                  <Typography variant="subtitle2" fontSize={12}>
                    {item.termTitle}
                  </Typography>
                  <TextField
                    // disabled={!cellClickedState[`${row.feeId}_${item.termId}`]}
                    inputProps={{
                      style: {
                        padding: '3px 10px',
                        minWidth: 50,
                        // color:theme.palette.success.main,
                      },
                      input: {
                        '&::placeholder': {
                          color: 'red',
                          opacity: 1, // otherwise firefox shows a lighter color
                        },
                      },
                    }}
                    type="number"
                    size="small"
                    inputRef={(ref) => setInputRef(item.termId, ref)}
                    // placeholder={sumValue || ''}
                    value={sumValue || ''}
                    onChange={(event) =>
                      handleHeaderTextFieldChange(event.target.value, item.termId, balanceAmount, '')
                    }
                  />
                  <Checkbox
                    size="small"
                    sx={{ mt: 0.5 }}
                    onChange={() => {
                      handleColumnCheckboxClick(item);
                    }}
                    checked={
                      isSelectedColumns(item.termId)
                      // (isSelectedAllPending && balanceAmount !== 0)
                    }
                  />
                </Stack>
              );
            },
            width: '200px',
            renderCell: (row: TermFeeDetailsType) => {
              const monthData = row.termFeeStats && row.termFeeStats.find((data) => data.termId === item.termId);
              if (monthData) {
                const { paid, balance, amount, scholarship, discount, fine } = monthData;

                const isRowSelected = cellData.find((f) => f.feeId === row.feeId && f.termId === item.termId);
                // selectedRows.includes(row.feeId); // Check if feeId is selected

                const isColumnSelected = cellData.includes(item.termId); // Check if feeId is selected

                const cellSelected = cellData.some((cell) => cell.feeId === row.feeId && cell.termId === item.termId);
                const cellDataSelectedAmount =
                  cellData.find((cell) => cell.feeId === row.feeId && cell.termId === item.termId)?.amount || 0;
                // console.log('cellData----::::', cellData);
                if (isRowSelected) {
                  console.log('selectedRows----::::', selectedRows);
                  // console.log('isRowSelected----::::', isRowSelected);
                }

                return (
                  <Button sx={{ p: 0, width: '100%' }} color="info">
                    <Stack
                      // key={`cell_${row.feeId}_${rowIndex}`}
                      onClick={() => {
                        // if (!isRowSelected && !isColumnSelected && !isSelectedAllPending) {
                        if (balance !== 0) {
                          handleCellClick(row.feeId, item.termId, row.feeTitle, item.termTitle, monthData);
                          // handleClickCellOpenTextFeild(row.feeId, item.termId);
                        }
                        // }
                      }}
                      sx={{
                        minWidth: 110,
                        height: 100,
                        position: 'relative',
                        background:
                          balance !== 0 &&
                          cellData.some((cell) => cell.feeId === row.feeId && cell.termId === item.termId) // Check if this cell was clicked
                            ? '#edecfc'
                            : balance !== 0
                            ? 'linear-gradient(90deg, rgba(255,249,232,1) 0%, rgba(255,255,255,1) 100%)'
                            : 'linear-gradient(90deg, #e3f5d3 0%, rgba(255,255,255,1) 100%)',

                        borderLeft: 3,
                        '&:hover': {
                          background:
                            (isSelectedAllPending && balance !== 0) ||
                            (isColumnSelected && balance !== 0) ||
                            (isRowSelected && balance !== 0)
                              ? !isSelectedAllPending &&
                                !isRowSelected &&
                                balance !== 0 &&
                                theme.palette.warning.lighter
                              : balance !== 0 &&
                                !selectedCells.some(
                                  (cell) => cell.rowIndex === row.feeId && cell.columnIndex === item.termId
                                ) &&
                                theme.palette.warning.lighter,
                          transition: '0.5s',
                        },
                        borderColor:
                          (isColumnSelected && balance !== 0) || (isRowSelected && balance !== 0)
                            ? (balance !== 0 && isColumnSelected) || isSelectedAllPending || isRowSelected
                              ? '#4a4ac4'
                              : balance !== 0
                              ? theme.palette.warning.main
                              : theme.palette.success.main
                            : balance !== 0 &&
                              cellData.some((cell) => cell.feeId === row.feeId && cell.termId === item.termId)
                            ? '#4a4ac4'
                            : balance !== 0
                            ? theme.palette.warning.main
                            : theme.palette.success.main,
                      }}
                      color="#000"
                      width="100%"
                      direction="column"
                      alignItems="start"
                      gap={2}
                      py={2}
                      pl={1}
                    >
                      {(isColumnSelected && balance !== 0) || (isRowSelected && balance !== 0) ? (
                        <Radio
                          checked={(isColumnSelected && balance !== 0) || (isRowSelected && balance !== 0)}
                          // Check if this cell is selected
                          // onChange={() => handleRowClick(row)} // Handle cell click
                          size="small"
                          checkedIcon={<SuccessIcon sx={{ color: '#4a4ac4' }} />}
                          sx={{ position: 'absolute', right: 0, top: 0 }}
                          disableRipple
                        />
                      ) : (
                        cellData.some((cell) => cell.feeId === row.feeId && cell.termId === item.termId) && (
                          <Radio
                            checked={selectedCells.some(
                              (cell) => cell.rowIndex === row.feeId && cell.columnIndex === item.termId
                            )}
                            // Check if this cell is selected
                            // onChange={() => handleCellClick(row.feeId, item.termId)} // Handle cell click
                            size="small"
                            checkedIcon={<SuccessIcon sx={{ color: '#4a4ac4' }} />}
                            sx={{ position: 'absolute', right: 0, top: 0 }}
                            disableRipple
                          />
                        )
                      )}

                      <Typography visibility={cellSelected ? 'hidden' : 'visible'} variant="subtitle2" fontSize={14}>
                        <CurrencyRupeeIcon sx={{ fontSize: '15px' }} />
                        {amount}
                      </Typography>
                      {/* <Typography variant="subtitle2" fontSize={10}>
                      {paid}
                    </Typography> */}
                      <Typography variant="subtitle2" color="error" fontSize={10}>
                        (<CurrencyRupeeIcon sx={{ fontSize: '10px' }} />
                        {balance !== null && `${balance}`})
                      </Typography>
                      <Stack width="100%" direction="row" alignItems="center" justifyContent="space-between">
                        <Typography
                          // width={50}
                          textAlign="start"
                          variant="subtitle2"
                          sx={{
                            color:
                              balance !== 0
                                ? (isColumnSelected && balance !== 0) || (isRowSelected && balance !== 0)
                                  ? isColumnSelected || isSelectedAllPending || isRowSelected
                                    ? '#4a4ac4'
                                    : theme.palette.warning.main
                                  : cellData.some((cell) => cell.feeId === row.feeId && cell.termId === item.termId)
                                  ? '#4a4ac4'
                                  : theme.palette.warning.main
                                : theme.palette.success.main,
                          }}
                          fontSize={10}
                        >
                          {/* {isRowSelected ? 'success' : ''} */}
                          {(isColumnSelected && balance !== 0) ||
                          (isRowSelected && balance !== 0) ||
                          cellData.some((cell) => cell.feeId === row.feeId && cell.termId === item.termId)
                            ? 'Selected'
                            : balance === 0
                            ? 'Paid'
                            : `Pending`}
                        </Typography>
                        {/* {balance !== 0 && (
                        <IconButton size="small" aria-label="" onClick={() => setDeleteFee(true)}>
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      )} */}
                      </Stack>
                    </Stack>
                    <Stack position="absolute" top={12} left={8.5}>
                      {cellSelected && (
                        <Tooltip
                          placement="top"
                          title={
                            <TextField
                              variant="standard"
                              inputProps={{
                                maxLength: balance.toString().length,
                                style: {
                                  padding: '0px',
                                  fontSize: '12px',
                                  width: 50,
                                  // border: '1px solid red',
                                  color: theme.palette.common.black,
                                },
                                input: {
                                  // border: '1px solid red',
                                  '&::placeholder': {
                                    color: 'red',
                                    opacity: 1, // otherwise firefox shows a lighter color
                                  },
                                },
                              }}
                              type="number"
                              size="small"
                              // inputRef={(ref) => setInputRef(item.termId, ref)}
                              placeholder="₹"
                              // defaultValue={amount || ''}
                              onChange={(event) =>
                                handleHeaderTextFieldChange(event.target.value, item.termId, balance, row)
                              }
                            />
                          }
                        >
                          <Typography color={theme.palette.common.black} variant="subtitle2" fontSize={14}>
                            <CurrencyRupeeIcon sx={{ fontSize: '15px' }} />
                            <span>{cellDataSelectedAmount}</span>
                            {cellDataSelectedAmount < amount && <sub style={{ fontSize: '8px' }}>({amount})</sub>}
                          </Typography>
                        </Tooltip>
                      )}
                    </Stack>
                    <PopupState variant="popover" popupId="demo-popup-popover">
                      {(popupState) => (
                        <div>
                          {(isSelectedAllPending && balance !== 0) ||
                            (isColumnSelected && balance !== 0) ||
                            (isRowSelected && balance !== 0) ||
                            (!cellSelected && (
                              <IconButton
                                {...bindTrigger(popupState)}
                                size="small"
                                sx={{ position: 'absolute', top: '1px', right: '1px' }}
                              >
                                <GoInfo size="15px" />
                              </IconButton>
                            ))}
                          <Popover
                            {...bindPopover(popupState)}
                            anchorOrigin={{
                              vertical: 'top',
                              horizontal: 'center',
                            }}
                            transformOrigin={{
                              vertical: 'bottom',
                              horizontal: 'center',
                            }}
                          >
                            {/* <Box
                            sx={{
                              position: 'relative',
                              mt: '10px',
                              '&::before': {
                                backgroundColor: 'red',
                                content: '""',
                                display: 'block',
                                position: 'absolute',
                                width: 12,
                                height: 12,
                                bottom: 0,
                                transform: 'rotate(45deg)',
                                left: 'calc(50% - 6px)',
                              },
                            }}
                          /> */}
                            <Box
                              sx={{
                                color: '#fff',
                              }}
                              bgcolor="#8F00FF"
                              gap={2}
                              p={1}
                            >
                              <Stack direction="row" gap={2}>
                                <Stack direction="row">
                                  <Typography variant="subtitle1" fontSize={12}>
                                    Amount&nbsp;:&nbsp;
                                  </Typography>
                                  <Typography variant="subtitle2" fontSize={12}>
                                    ₹ {amount}
                                  </Typography>
                                </Stack>
                                <Stack direction="row">
                                  <Typography variant="subtitle1" fontSize={12}>
                                    Paid&nbsp;:&nbsp;
                                  </Typography>
                                  <Typography variant="subtitle2" fontSize={12}>
                                    ₹ {paid}
                                  </Typography>
                                </Stack>
                                <Stack direction="row">
                                  <Typography variant="subtitle1" fontSize={12}>
                                    Scholarship&nbsp;:&nbsp;
                                  </Typography>
                                  <Typography variant="subtitle2" fontSize={12}>
                                    ₹ {scholarship}
                                  </Typography>
                                </Stack>
                              </Stack>
                              <Stack direction="row" gap={2}>
                                <Stack direction="row">
                                  <Typography variant="subtitle1" fontSize={12}>
                                    Discount&nbsp;:&nbsp;
                                  </Typography>
                                  <Typography variant="subtitle2" fontSize={12}>
                                    ₹ {discount}
                                  </Typography>
                                </Stack>
                                <Stack direction="row">
                                  <Typography variant="subtitle1" fontSize={12}>
                                    Fine&nbsp;:&nbsp;
                                  </Typography>
                                  <Typography variant="subtitle2" fontSize={12}>
                                    ₹ {fine}
                                  </Typography>
                                </Stack>
                                <Stack direction="row">
                                  <Typography variant="subtitle1" fontSize={12}>
                                    Balance&nbsp;:&nbsp;
                                  </Typography>
                                  <Typography color="error" variant="subtitle2" fontSize={12}>
                                    ₹ {balance}
                                  </Typography>
                                </Stack>
                              </Stack>
                            </Box>
                          </Popover>
                        </div>
                      )}
                    </PopupState>
                  </Button>
                );
              }
              return <Stack height={100}> </Stack>;
            },
          }))
        : []),
    ];

    return [...baseColumns];
  }, [
    selectedCells,
    termFee,
    handleHeaderTextFieldChange,
    theme,
    selectedRows,
    isSelectedAllPending,
    handleColumnCheckboxClick,
    handleCellClick,
    handleRowClick,
    selectedColumn,
    handleSelectAllPendingChange,
    cellData,
    termFeeDetails,
  ]);

  const getRowKey = useCallback((row: any) => row.feeId, []);
  return (
    <Page title="Fees Collection">
      <PayFeeCollectionRoot>
        <Card className="Card" elevation={1} sx={{ py: { xs: 2, md: 0 } }}>
          <Box
            flexWrap="wrap"
            display="flex"
            py={0.5}
            justifyContent="space-between"
            alignItems="center"
            sx={{ px: { xs: 3, md: 5 } }}
          >
            <Stack gap={2} direction="row" alignItems="center">
              <Tooltip title="Back">
                <IconButton
                  onClick={onBackClick}
                  sx={{
                    backgroundColor: theme.palette.secondary.main,
                    color: theme.palette.common.white,
                    '&:hover': { backgroundColor: theme.palette.secondary.dark },
                    width: '25px',
                    height: '25px',
                  }}
                  size="small"
                >
                  <ArrowBackIcon fontSize="small" />
                </IconButton>
              </Tooltip>
              <Typography variant="h6" fontSize={17}>
                Fee Collection
              </Typography>
            </Stack>

            <Box display="flex" alignItems="center" flexWrap="wrap">
              <FormControlLabel
                control={
                  <Checkbox
                    checked={selectedRows?.length > 0}
                    indeterminate={selectedRows?.length > 0 && selectedRows.length < termFeeDetails.length}
                    onChange={handleSelectAllPendingChange}
                  />
                }
                label="All Fees"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={selectedRows?.length > 0}
                    indeterminate={selectedRows?.length > 0 && selectedRows.length < termFeeDetails.length}
                    onChange={handleSelectAllPendingChange}
                  />
                }
                label="All Pending Fees"
              />
              <Stack direction="row" alignContent="center" gap={1} flexWrap="wrap">
                <Button
                  disabled={cellData.length === 0}
                  onClick={toggleDrawerOpen}
                  variant="outlined"
                  // disabled={selectedCells.length < 0 || selectedRows.length < 0}
                  size="small"
                  sx={{ py: 0, borderRadius: 10 }}
                >
                  Pay
                </Button>
                <Button
                  // onClick={() => handleCellClick2(1, 1)}
                  variant="outlined"
                  size="small"
                  sx={{ py: 0, borderRadius: 10 }}
                >
                  Unpay
                </Button>
                <Button variant="outlined" size="small" sx={{ py: 0, borderRadius: 10 }}>
                  Chalan
                </Button>
                <Button variant="outlined" size="small" sx={{ py: 0, borderRadius: 10 }}>
                  Duplicate Receipt
                </Button>
              </Stack>
              <Tooltip title="Reset All">
                <IconButton aria-label="delete" color="primary" sx={{ ml: 1 }} onClick={() => handleReset()}>
                  <CachedIcon />
                </IconButton>
              </Tooltip>
              {showFilter === false ? (
                <Tooltip title="Total Amount">
                  <IconButton
                    aria-label="delete"
                    color="primary"
                    sx={{ ml: 1 }}
                    onClick={() => setShowFilter((x) => !x)}
                  >
                    <SearchIcon />
                  </IconButton>
                </Tooltip>
              ) : (
                <IconButton aria-label="delete" color="primary" sx={{ ml: 1 }} onClick={() => setShowFilter((x) => !x)}>
                  <IoIosArrowUp />
                </IconButton>
              )}
              {showPaymentIfno === false ? (
                <Tooltip title="Payment Mode">
                  <IconButton
                    aria-label="delete"
                    color="primary"
                    sx={{ ml: 1 }}
                    onClick={() => setShowPaymentIfno((x) => !x)}
                  >
                    <AddIcon />
                  </IconButton>
                </Tooltip>
              ) : (
                <IconButton
                  aria-label="delete"
                  color="primary"
                  sx={{ ml: 1 }}
                  onClick={() => setShowPaymentIfno((x) => !x)}
                >
                  <IoIosArrowUp />
                </IconButton>
              )}
            </Box>
          </Box>

          <Divider />
          <div className="card-main-body">
            <Collapse in={showPaymentIfno}>
              <Stack px={3} py={1} direction="row" flexWrap="wrap" alignItems="start" justifyContent="space-between">
                <Typography variant="subtitle2">Payment Information</Typography>
                <Box display="flex" gap={2} alignItems="start" flexWrap="wrap">
                  <Stack direction="column" alignItems="start">
                    <Typography variant="subtitle1" fontSize={12} color="secondary">
                      Receipt No
                    </Typography>
                    <TextField
                      id="receiptNumber"
                      error={error}
                      type="number"
                      placeholder="Enter number"
                      value={receiptNumber || ''}
                      onChange={handleReceiptNoChange}
                      size="small"
                      InputProps={{
                        inputProps: {
                          style: { width: 120 },
                        },
                      }}
                    />
                  </Stack>
                  <Stack direction="column" alignItems="start">
                    <Typography variant="subtitle1" fontSize={12} color="secondary">
                      Date
                    </Typography>
                    <DatePickers
                      width="180px"
                      name="payingDate"
                      value={dayjs(payingDate, 'DD/MM/YYYY')}
                      onChange={(e) => {
                        const formattedDate = e ? e.format('DD/MM/YYYY') : '';
                        setPayingDate(formattedDate);
                        setPayDetails((prev) => ({
                          ...prev,
                          payingDate: formattedDate,
                        }));
                        console.log('date::::', formattedDate);
                      }}
                    />
                  </Stack>
                  <Stack direction="column" alignItems="START">
                    <Typography variant="subtitle1" fontSize={12} color="secondary">
                      Remarks
                    </Typography>
                    <TextField
                      multiline
                      placeholder="Type here..."
                      value={remarks}
                      onChange={(e) => handleRemarksChange(e)}
                      InputProps={{
                        inputProps: {
                          style: { resize: 'vertical', width: 300, minHeight: '22px', maxHeight: '100px' },
                        },
                      }}
                      size="small"
                    />
                  </Stack>
                </Box>{' '}
              </Stack>
              <Stack direction="row" alignItems="center" justifyContent="end" px={3} pb={2}>
                <Button
                  size="small"
                  type="button"
                  color="secondary"
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={handleAddRow}
                  sx={{ py: 0.5, px: 1, whiteSpace: 'nowrap' }}
                >
                  Add Row
                </Button>
              </Stack>

              <form noValidate>
                <TableContainer
                  sx={{
                    mb: 2,
                    '&::-webkit-scrollbar': {
                      height: '15px',
                    },
                    // width: { xl: 100, xxl: '100%' },
                  }}
                >
                  <Table sx={{ minWidth: 1100 }} aria-label="simple table" stickyHeader>
                    <TableHead>
                      <TableRow className="Payment_Info">
                        <TableCell sx={{ whiteSpace: 'nowrap' }}>Sl.No</TableCell>
                        <TableCell sx={{ whiteSpace: 'nowrap' }}>Payment Type</TableCell>
                        <TableCell sx={{ whiteSpace: 'nowrap' }}>Amount</TableCell>
                        <TableCell sx={{ whiteSpace: 'nowrap' }}>Cheque/DD No</TableCell>
                        <TableCell sx={{ whiteSpace: 'nowrap' }}>Date of Issue</TableCell>
                        <TableCell sx={{ whiteSpace: 'nowrap' }}>Cheque/DD Deposit Date</TableCell>
                        <TableCell sx={{ whiteSpace: 'nowrap' }}>Cheque Clearing Date</TableCell>
                        {/* <TableCell  sx={{whiteSpace:'nowrap'}}>MICR</TableCell>
                        <TableCell  sx={{whiteSpace:'nowrap'}}>Bank</TableCell>
                        <TableCell  sx={{whiteSpace:'nowrap'}}>Branch</TableCell> */}
                        <TableCell sx={{ whiteSpace: 'nowrap' }}>School Bank Account</TableCell>
                        <TableCell sx={{ whiteSpace: 'nowrap' }}> </TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {rows.map((row, rowIndex) => (
                        <TableRow key={row.id}>
                          <TableCell width={50}>{row.id}</TableCell>
                          <TableCell width={100}>
                            <Select
                              sx={{
                                // width: 80,
                                color: selectedPaymentTypeId === 0 ? theme.palette.grey[500] : '',
                                // fontSize: '12px',
                                height: 30,
                              }}
                              value={payDetails.paymentmode[rowIndex].paymentTypeId || 0} // Ensure the value reflects the state
                              onChange={(event) => {
                                handlePaymentTypeChange(event, rowIndex);
                              }} // Call the handler on change
                              fullWidth
                            >
                              <MenuItem sx={{ display: 'none', color: theme.palette.grey[200] }} value={0}>
                                Select type
                              </MenuItem>
                              {termFee.paymentTypes &&
                                termFee.paymentTypes.map((type: { paymentTypeId: number; paymentType: string }) => (
                                  <MenuItem key={type.paymentTypeId} value={type.paymentTypeId}>
                                    {type.paymentType}
                                  </MenuItem>
                                ))}
                            </Select>
                          </TableCell>
                          <TableCell width={180}>
                            <TextField
                              type="number"
                              inputProps={{
                                style: {
                                  padding: '6px 10px',
                                  // fontSize: '12px',
                                  // width: 80,
                                  borderRadius: 7,
                                },
                              }}
                              value={payDetails.paymentmode[rowIndex].amount || ''}
                              onChange={(event) => handlePaymentModeChange(event, rowIndex, 'amount')}
                              variant="outlined"
                              placeholder="Enter"
                            />
                          </TableCell>
                          <TableCell width={200}>
                            <TextField
                              inputProps={{
                                style: {
                                  padding: '6px 10px',
                                  // fontSize: '12px',
                                  // width: 150,
                                  borderRadius: 7,
                                },
                              }}
                              value={payDetails.paymentmode[rowIndex].chequeOrDDNo}
                              onChange={(event) => handlePaymentModeChange(event, rowIndex, 'chequeOrDDNo')}
                              variant="outlined"
                              placeholder="Enter"
                            />
                          </TableCell>
                          <TableCell width={180}>
                            <DatePickers
                            DateIconSize='14px'
                              inputFeildPadding="6px 10px"
                              // width="160px"
                              name="dateOfIssue"
                              // value={row.dateOfIssue ? dayjs(row.dateOfIssue, 'DD/MM/YYYY') : ''}
                              onChange={(event) => handleDateChange(event, rowIndex, 'dateOfIssue')}
                            />
                          </TableCell>
                          <TableCell width={180}>
                            <DatePickers
                            DateIconSize='14px'
                              inputFeildPadding="6px 10px"
                              // width="160px"
                              name="deppositDate"
                              onChange={(event) => handleDateChange(event, rowIndex, 'deppositDate')}
                            />
                          </TableCell>
                          <TableCell width={180}>
                            <DatePickers
                            DateIconSize='14px'
                              inputFeildPadding="6px 10px"
                              // width="160px"
                              name="clearingDate"
                              // value={dayjs(clearingDate, 'DD/MM/YYYY')}
                              onChange={(event) => handleDateChange(event, rowIndex, 'clearingDate')}
                            />
                          </TableCell>

                          <TableCell width={150}>
                            <Select
                              sx={{
                                // width: 80,
                                color: selectedBankId === 0 ? theme.palette.grey[500] : '',
                                // fontSize: '12px',
                                height: 30,
                              }}
                              value={payDetails.paymentmode[rowIndex].bankId || 0}
                              onChange={(event) => handleBankChange(event, rowIndex)} // Call the handler on change
                              fullWidth
                            >
                              <MenuItem sx={{ display: 'none' }} value={0}>
                                Select bank
                              </MenuItem>
                              {termFee.bankDetails &&
                                termFee.bankDetails.map((bank: { bankId: number; bankName: string }) => (
                                  <MenuItem key={bank.bankId} value={bank.bankId}>
                                    {bank.bankName}
                                  </MenuItem>
                                ))}
                            </Select>
                          </TableCell>
                          <TableCell width={50}>
                            <IconButton
                              size="small"
                              color="error"
                              aria-label="Delete Row"
                              onClick={() => handleDeleteRow(row.id)}
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </form>
            </Collapse>

            <Collapse
              in={showFilter}
              sx={{
                px: { xs: 3, md: 5 },
                backgroundColor: isLight ? theme.palette.grey[100] : theme.palette.grey[900],
              }}
            >
              <form noValidate>
                <Box pb={1} display="flex" justifyContent="end" gap={5} flexWrap="wrap">
                  <Stack>
                    <FormControl sx={{ width: '130px' }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Total Amount
                      </Typography>
                      <TextField
                        disabled
                        sx={{
                          '& .MuiInputBase-input.Mui-disabled': {
                            WebkitTextFillColor: theme.palette.primary.main,
                          },
                        }}
                        inputProps={{
                          style: {
                            padding: '5px 10px',
                          },
                        }}
                        name=""
                        type="number"
                        value={totalAmount.toFixed(1)}
                      />
                    </FormControl>
                  </Stack>
                  <Stack>
                    <FormControl sx={{ width: '130px' }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Total Discount
                      </Typography>
                      <TextField
                        disabled
                        value={totalDiscount.toFixed(1)}
                        sx={{
                          '& .MuiInputBase-input.Mui-disabled': {
                            WebkitTextFillColor: theme.palette.primary.main,
                          },
                        }}
                        inputProps={{
                          style: {
                            padding: '5px 10px',
                          },
                        }}
                        name=""
                        type="number"
                        placeholder="0"
                      />
                    </FormControl>
                  </Stack>
                  <Stack>
                    <FormControl sx={{ width: '130px' }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Total Scholarship
                      </Typography>
                      <TextField
                        disabled
                        value={totalScholarship.toFixed(1)}
                        sx={{
                          '& .MuiInputBase-input.Mui-disabled': {
                            WebkitTextFillColor: theme.palette.primary.main,
                          },
                        }}
                        inputProps={{
                          style: {
                            padding: '5px 10px',
                          },
                        }}
                        name=""
                        type="number"
                        placeholder="0"
                      />
                    </FormControl>
                  </Stack>
                  <Stack>
                    <FormControl sx={{ width: '130px' }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Total Fine
                      </Typography>
                      <TextField
                        disabled
                        value={totalFine.toFixed(1)}
                        sx={{
                          '& .MuiInputBase-input.Mui-disabled': {
                            WebkitTextFillColor: theme.palette.primary.main,
                          },
                        }}
                        inputProps={{
                          style: {
                            padding: '5px 10px',
                          },
                        }}
                        name=""
                        type="number"
                        placeholder="0"
                      />
                    </FormControl>
                  </Stack>
                  <Stack>
                    <FormControl sx={{ width: '130px' }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Grand Total
                      </Typography>
                      <TextField
                        disabled
                        sx={{
                          '& .MuiInputBase-input.Mui-disabled': {
                            WebkitTextFillColor: theme.palette.primary.main,
                          },
                        }}
                        inputProps={{
                          style: {
                            padding: '5px 10px',
                          },
                        }}
                        name=""
                        value={totalAmount.toFixed(1)}
                        type="number"
                        placeholder="00"
                      />
                    </FormControl>
                  </Stack>
                </Box>
              </form>
            </Collapse>
            <Divider />
            <Paper className="card-table-container" sx={{ borderRadius: 0, marginTop: '12px' }}>
              <DTVirtuoso
                columns={PayFeeCollectionListColumns}
                data={termFeeDetails}
                getRowKey={getRowKey}
                fetchStatus="success"
                showHorizontalScroll
                tableCellSkeltonStyle={{ width: '1010px' }}
              />
            </Paper>
          </div>
        </Card>
      </PayFeeCollectionRoot>
      <Popup
        size="xs"
        state={deleteFee}
        onClose={() => setDeleteFee(false)}
        popupContent={<DeleteMessage icon={DeleteAnimatedIcon} message="Are you sure want to delete?" />}
      />
      <TemporaryDrawer
        closeIconDisable
        onClose={toggleDrawerClose}
        state={drawerOpen}
        DrawerContent={
          <Pay
            setTotalAmount={setTotalAmount}
            setPayDetails={setPayDetails}
            payDate={payingDate}
            feeStatusData={cellData}
            payDetails={payDetails}
            onClose={toggleDrawerClose}
            onCancel={onCancel}
            load={() => loadStudentTermFeeDataList(initialStudentTermFeeListRequest)}
            studentDetails={studentDetails}
            currentDate={currentDate}
            receiptType={receiptType.receiptType}
            setReceiptNo={setReceiptNo}
            paymentTypes={termFee.paymentTypes}
          />
        }
      />

      <Snackbar
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        // message="Receipt number already exist"
      >
        <Alert onClose={handleSnackbarClose} severity="error" variant="filled" sx={{ width: '100%' }}>
          Receipt number already exist
        </Alert>
      </Snackbar>
    </Page>
  );
}
