import { Dayjs } from 'dayjs';
import { FetchStatus } from './Common';
import { ClassDivisionDataType, ClassSectionDataType, ParentsDataType, StaffDataType } from './MessageBox';

export type NotificationRequest = {
  notificationTitle: string;
  createdDate: Dayjs | string | null;
  notificationStatus: string | number;
  accademicId: number;
  adminId: string | number | undefined;
};

export type NotificationDataType = {
  notificationId: number;
  notificationTitle: string;
  notificationContent: string;
  notificationStatus: string | number;
  notificationFile: string;
  fileCount: number;
  createdDate: string;
  createdBy?: string | number | undefined;
  accademicId: number;
  adminId?: string | number | undefined;
};

export type NotificationCreateRequest = Omit<NotificationDataType, 'createdBy'>;

// upload type
export type UplaodFilesType = {
  result: string;
  details: string;
};
// Delete notification
export type DeleteMultipleNotificationListType = {
  notificationId: number;
  adminId?: number;
};

export type SendToParentsRequest = {
  notificationId: number | undefined;
  studentId: number | undefined;
  studentRollNo: number;
  studentName: string;
  guardianName: string;
  guardianNumber: string;
  className: string;
  classId: number;
  accademicTime: string;
  adminId: number;
};
export type SendToParentsResponse = SendToParentsRequest & { sendStatus: 'Success' | 'Failed' };
// Staff //
export type SendToStaffType = {
  notificationId: number | undefined;
  staffId: number;
  staffName: string;
  staffPhoneNumber: string;
  adminId: number;
};
// ClassDivision //
export type SendToClassDivisionType = {
  adminId: number;
  accademicId: number;
  classId: number;
  notificationId: number | undefined;
  sendStatus: 'Success' | 'Failed' | '' | undefined;
};
// ClassSection //
export type SendToClassSectionType = {
  adminId: number;
  accademicId: number;
  classSectionName: string;
  notificationId: number | undefined;
  sendStatus: 'Success' | 'Failed' | '' | undefined;
};

export type DeleteMultipleMessageTemplateType = {
  notificationId: number;
  adminId?: number;
};
// Notification
export type NotificationState = {
  notificationList: {
    data: NotificationDataType[];
    status: FetchStatus;
    error: string | null;
  };
  filesUploadList: {
    status: FetchStatus;
    data: UplaodFilesType[];
    error: string | null;
  };

  submitting: boolean;
  deletingRecords: Record<number, boolean>;
  error: string | null | undefined;
};
