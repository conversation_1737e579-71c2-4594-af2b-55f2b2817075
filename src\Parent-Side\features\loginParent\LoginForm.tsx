import PasswordField from '@/components/shared/Selections/PasswordField';
import { breakPointsMinwidth } from '@/config/breakpoints';
import useAuth from '@/hooks/useAuth';
import { LoginRequest } from '@/types/Auth';
import Button from '@mui/lab/LoadingButton';
import { Alert, Card, TextField, Typography, useTheme } from '@mui/material';
import styled from 'styled-components';
import { useFormik } from 'formik';
import * as yup from 'yup';
import passdailLogo from '@/assets/logo-small.svg';
import Popup from '@/components/shared/Popup/Popup';
import React from 'react';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import StudentSelection from './StudentSelection';

const LoginFormRoot = styled(Card)`
  padding: 0.9375rem;
  @media ${breakPointsMinwidth.lg} {
    padding: 1.875rem;
  }
  .logo {
    display: flex;
    justify-content: center;
  }
  .logo img {
    width: 40px;
  }
  .title {
    font-size: 1.7rem;
    font-family: 'Poppins Medium';
    font-weight: 500;
    text-align: center;
    letter-spacing: 0px;
  }

  .subtitle {
    font-size: 1.6rem;
    font-family: 'Poppins Medium';
    text-align: center;
    letter-spacing: 0px;
  }

  .greet {
    text-align: center;
    p {
      font-size: 0.75rem;
      font-family: 'Poppins Regular';
      font-weight: 400;
      color: ${(props) => props.theme.palette.secondary.main};
    }
  }
  /* .MuiFormHelperText-root.Mui-error {
    color: blue;
  } */
`;

const validationSchema = yup.object({
  username: yup.string().required('Enter username'),
  password: yup.string().required('Password is required'),
});

function LoginForm() {
  const theme = useTheme();
  const [popupStudents, setPopupStudents] = React.useState(false);
  const [loginParent, setLoginParent] = React.useState('');
  const { loading, login, error, setParentMode } = useAuth();
  const {
    values: { username, password },
    handleSubmit,
    handleChange,
    touched,
    errors,
  } = useFormik<LoginRequest>({
    initialValues: {
      username: '',
      password: '',
    },
    validationSchema,
    onSubmit: (values) => {
      setPopupStudents(true);
      setLoginParent(values);
      // if (loginParent === true) {
      //   login(values);
      // }
    },
  });

  return (
    <LoginFormRoot className="loginCard" elevation={0}>
      <div className="logo">
        <img src={passdailLogo} alt="" />
      </div>
      <Typography variant="h3" className="title mb-3">
        Welcome Passdaily
      </Typography>
      <div className="greet mb-3">
        <Typography variant="body2">
          Lorem Ipsum is simply dummy text of the printing and typesetting industry has been the industry&apos; standard
          dummy.
        </Typography>
      </div>
      <Typography variant="h3" className="subtitle mb-3">
        Sign In
      </Typography>
      <form noValidate onSubmit={handleSubmit}>
        {!!error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}
        <div className="w-100 mb-3">
          <TextField
            id="username"
            name="username"
            variant="outlined"
            color="primary"
            placeholder="Enter username"
            fullWidth
            autoComplete="off"
            value={username}
            onChange={handleChange}
            error={touched.username && Boolean(errors.username)}
            helperText={touched.username && errors.username && <span style={{ color: 'red' }}>{errors.username}</span>}
          />
        </div>
        <div className="w-100 mb-4">
          <PasswordField
            id="password"
            name="password"
            color="primary"
            placeholder="Enter password"
            passwordViewable
            fullWidth
            value={password}
            onChange={handleChange}
            error={touched.password && Boolean(errors.password)}
            helperText={touched.password && errors.password}
          />
        </div>
        <div className="w-100 mb-3">
          <Button
            loading={loading}
            loadingPosition="start"
            startIcon={<span />}
            fullWidth
            type="submit"
            variant="contained"
            color="success"
          >
            {loading ? 'Signing in...' : 'Login'}
          </Button>
        </div>
        <div className="w-100">
          <Button fullWidth type="button" variant="outlined" color="success">
            Create Parent Account
          </Button>
        </div>
      </form>
      <Popup
        size="xl"
        title="STUDENTS"
        state={popupStudents}
        onClose={() => setPopupStudents(false)}
        popupContent={
          <StudentSelection loading={loading} login={login} loginParent={loginParent} setParentMode={setParentMode} />
        }
      />
    </LoginFormRoot>
  );
}

export default LoginForm;
