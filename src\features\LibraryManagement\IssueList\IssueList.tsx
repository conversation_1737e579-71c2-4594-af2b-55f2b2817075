/* eslint-disable jsx-a11y/alt-text */
import React, { FormEvent, useCallback, useEffect, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  Collapse,
  FormControl,
  Avatar,
} from '@mui/material';
import styled from 'styled-components';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import SearchIcon from '@mui/icons-material/Search';
import deleteBin from '@/assets/ManageFee/deleteBin.json';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import DeleteIcon from '@mui/icons-material/Delete';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import DateSelect from '@/components/shared/Selections/DateSelect';
import { CLASS_SELECT, STATUS_SELECT } from '@/config/Selection';
import typography from '@/theme/typography';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import { deleteTermFeeList } from '@/store/ManageFee/manageFee.thunks';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { Select } from '@mui/material';
import { MenuItem } from '@mui/material';
import { SelectChangeEvent } from '@mui/material';
import { getClassData } from '@/config/storeSelectors';
import { useAppSelector } from '@/hooks/useAppSelector';
import { fetchClassList } from '@/store/Dashboard/dashboard.thunks';
import useAuth from '@/hooks/useAuth';
import { useTheme } from '@mui/material';
import StudentsPickerField from '@/components/shared/Selections/StudentsPicker';
import StaffPickerField from '@/components/shared/Selections/StaffPicker';
import NoData from '@/assets/no-datas.png';
import { Chip } from '@mui/material';
import { ReturnBook } from './ReturnBook';
import bookCode from '@/assets/Library/bookCode.png';
import bookTitle from '@/assets/Library/bookTitle.png';
import Author from '@/assets/Library/Author.png';
import Price from '@/assets/Library/Price.png';
import bookStatus from '@/assets/Library/bookStatus.png';
import bookRemark from '@/assets/Library/bookRemark.png';
import Class from '@/assets/Library/Class.png';
import issueDate from '@/assets/Library/issueDate.png';
import returnDate from '@/assets/Library/issueDate.png';
import fineStatus from '@/assets/Library/fineStatus.png';
import fineAmount from '@/assets/Library/fineAmount.png';
import Discount from '@/assets/Library/Discount.png';
import Payable from '@/assets/Library/Payable.png';
import BookTable from './BookList';
import DatePickers from '@/components/shared/Selections/DatePicker';
import useSettings from '@/hooks/useSettings';

const IssueListRoot = styled.div`
  padding: 1rem;
  @media screen and (max-width: 576px) {
    padding: 0.5rem;
  }
  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 240px);
    @media screen and (max-width: 1488px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 50px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;
const bookList = [
  {
    name: 'Favas Thoppil',
    code: '012345',
    author: 'William',
    image:
      'https://cdn.kobo.com/book-images/3c2a6883-6d52-4ba6-9f83-9149a7fa2893/1200/1200/False/the-complete-works-of-william-shakespeare-87.jpg',
  },
  {
    name: 'Anna Smith',
    code: '987654',
    author: 'Jane Austen',
    image:
      'https://cdn.kobo.com/book-images/3c2a6883-6d52-4ba6-9f83-9149a7fa2893/1200/1200/False/the-complete-works-of-william-shakespeare-87.jpg',
  },
  {
    name: 'John Doe',
    code: '112233',
    author: 'Mark Twain',
    image:
      'https://cdn.kobo.com/book-images/3c2a6883-6d52-4ba6-9f83-9149a7fa2893/1200/1200/False/the-complete-works-of-william-shakespeare-87.jpg',
  },
  {
    name: 'Sara Lee',
    code: '445566',
    author: 'Agatha Christie',
    image:
      'https://cdn.kobo.com/book-images/3c2a6883-6d52-4ba6-9f83-9149a7fa2893/1200/1200/False/the-complete-works-of-william-shakespeare-87.jpg',
  },
];

export const data = [
  {
    slNo: 1,
    date: '20/07/23',
    description: 'Welcome to PassDaily - eConnect for Schools and College',
    subject: 'Welcome to PassDaily',
  },
  { slNo: 2, date: '14/08/23', description: 'Keep your password safe', subject: 'Password' },
  {
    slNo: 3,
    date: '21/08/23',
    description: 'PassDaily Team Welcomes you to the eConnect tool for Schools... - eConnect for Schools and College',
    subject: 'Message',
  },
  {
    slNo: 4,
    date: '25/08/23',
    description: 'Welcome to PassDaily - eConnect for Schools and College',
    subject: 'PassDaily',
  },
  { slNo: 5, date: '27/08/23', description: 'Keep your password safe', subject: 'Password' },
  {
    slNo: 6,
    date: '30/08/23',
    description: 'PassDaily Team Welcomes you to the eConnect tool for Schools... - eConnect for Schools and College',
    subject: 'Message',
  },
  {
    slNo: 7,
    date: '02/09/23',
    description: 'Welcome to PassDaily - eConnect for Schools and College',
    subject: 'Welcome',
  },
];

const bookDetails = [
  { label: 'Book Code', value: '1002', icon: bookCode },
  { label: 'Book Title', value: 'William Shakespear', icon: bookTitle },
  { label: 'Author', value: 'William Max', icon: Author },
  { label: 'Price', value: '2000', icon: Price },
  { label: 'Book Status', value: 'Good', icon: bookStatus },
  { label: 'Price', value: '2000', icon: Price },
  { label: 'Book Remark', value: 'The Story is good', icon: bookRemark },
  { label: 'Class', value: 'X-A', icon: Class },
  { label: 'Issue Date', value: '31-02-2025', icon: issueDate },
  { label: 'Return Date', value: '31-02-2025', icon: returnDate },
  { label: 'Fine Status', value: 'Late', icon: fineStatus },
];

const fineDetails = [
  { label: 'Fine Amount', value: '200', icon: fineAmount },
  { label: 'Discount', value: '00', icon: Discount },
  { label: 'Payable', value: '200', icon: Payable },
];

const SELECT = ['Student', 'Staff'];
const StaffList = ['Staff A', 'Staff B'];

const LabelTypography = ({ children, width }: { children: React.ReactNode; width?: string | number }) => {
  const theme = useTheme();
  const isLight = useSettings().themeMode === 'light';
  return (
    <Typography
      variant="subtitle2"
      fontSize={13}
      width={width}
      sx={{ color: isLight ? theme.palette.grey[600] : theme.palette.grey[500] }}
      whiteSpace="nowrap"
    >
      {children}
    </Typography>
  );
};

function IssueList() {
  const theme = useTheme();
  const isLight = useSettings().themeMode === 'light';

  const { confirm } = useConfirm();
  const dispatch = useAppDispatch();
  const { user } = useAuth();
  const adminId: number = user ? user.accountId : 0;
  const [drawerOpen, setDrawerOpen] = React.useState<boolean>(false);
  const [returnListOpen, setReturnListOpen] = React.useState<boolean>(false);
  const [showFilter, setShowFilter] = useState(false);
  const [showSend, setShowSend] = useState(false);
  const ClassData = useAppSelector(getClassData);
  const [issuedTo, setIssuedTo] = useState(SELECT[0]);
  const [classFilter, setClassFilter] = useState(-1);
  const [selectedStaff, setSelectedStaff] = useState('');
  const [selectedStudentIds, setSelectedStudentIds] = useState<string>('');
  const [selectedStudentData, setSelectedStudentData] = useState<any[]>([]);
  const { studentName } = selectedStudentData;

  const handleChangeIssuedTo = (event: SelectChangeEvent) => {
    setIssuedTo(event.target.value);
    console.log('Selected:', event.target.value); // handle selection change here
  };
  const handleClassChange = (e: SelectChangeEvent) => {
    const selectedClass = parseInt(e.target.value, 10);
    if (selectedClass) {
      setClassFilter(selectedClass);
    }
    // loadAttendanceSummary({
    //   ...currentAttendanceSummaryRequest,
    //   classId: selectedClass || 0,
    // });
  };

  const currentIssueListRequest = React.useMemo(
    () => ({
      adminId,
      academicId: 11,
      classId: classFilter,
      feeTypeId: 1,
      studentId: selectedStudentIds,
      paidDate: '',
    }),
    [adminId, classFilter, selectedStudentIds]
  );
  const loadIssueList = useCallback(
    async (request: any) => {
      try {
        // const data = await dispatch(fetchFeeOverviewPaidList(request)).unwrap();
        // console.log('data::::', data);
        // console.log('selectedStudentIds::::', selectedStudentIds);
      } catch (error) {
        console.error('Error loading term fee list:', error);
      }
    },
    [dispatch, selectedStudentIds]
  );
  useEffect(() => {
    dispatch(fetchClassList(adminId));
    loadIssueList(currentIssueListRequest);
    console.log('selectedStudentData::::----', selectedStudentData);
  }, [dispatch, adminId, loadIssueList, currentIssueListRequest]);

  const handleOpen = () => {
    setDrawerOpen(true);
  };

  const toggleDrawerClose = useCallback(() => setDrawerOpen(false), []);

  const handleReset = useCallback((e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIssuedTo(SELECT[0]);
    setClassFilter(-1);
    setSelectedStudentData([]);
    setSelectedStudentIds('');
    // loadTermFeeList({
    //   adminId,
    //   accademicId: academicYearFilter,
    //   termTitle: termTitleFilter,
    //   feeTypeId: feeTypeFilter,
    // });
  }, []);

  const getRowKey = useCallback((row: any) => row.examId, []);
  const handleDeleteTermFeeList = useCallback(
    async (termObj: any) => {
      const { termId } = termObj;
      const deleteConfirmMessage = (
        <DeleteMessage
          jsonIcon={deleteBin}
          message={
            <div>
              Are you sure you want to delete the Row <br />
              &quot;{termObj.termTitle}&quot; ?
            </div>
          }
        />
      );
      if (await confirm(deleteConfirmMessage, 'Delete Row?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
        const sendReq = [{ dbResult: 'string', deletedId: 0 }];

        console.log('sendReq', sendReq);

        const deleteResponse = await dispatch(deleteTermFeeList(sendReq));
        console.log('deleteResponse::::----', deleteResponse);
        if (deleteResponse && Array.isArray(deleteResponse.payload)) {
          const results: any[] = deleteResponse.payload;
          const errorMessages = results.find((result) => result.dbResult === 'Failed');
          const successMessages = results.find((result) => result.dbResult === 'Success');
          // Reload only the specific message template with the deleted messageId

          if (!errorMessages) {
            const deleteSuccessMessage = <SuccessMessage loop={false} message="Delete successfully" />;
            await confirm(deleteSuccessMessage, 'Success', { okLabel: 'Ok', showOnlyOk: true });
          } else if (!successMessages) {
            const deleteErrorMessage = <DeleteMessage message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          } else {
            const deleteErrorMessage = <DeleteMessage message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          }
          // loadTermFeeList({ ...currentTermFeeListRequest });

          // setSnackBar(true);
          // setTermFeeData((prevDetails) => prevDetails.filter((item) => item.termId !== termId));
        }
      }
    },
    [confirm]
  );
  const issueListColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'slNo',
        dataKey: 'slNo',
        headerLabel: 'Sl.No',
        sortable: true,
      },
      {
        name: 'date',
        dataKey: 'date',
        headerLabel: 'Date',
      },
      {
        name: 'subject',
        dataKey: 'subject',
        headerLabel: 'Subject',
      },
      {
        name: 'description',
        dataKey: 'description',
        headerLabel: 'Description',
      },
      {
        name: 'status',
        headerLabel: 'Status',
        renderCell: () => {
          return <Typography>Published</Typography>;
        },
      },
      {
        name: 'actions',
        headerLabel: 'Actions',
        renderCell: () => {
          return (
            <Stack direction="row" gap={1}>
              <IconButton size="small" onClick={handleOpen} sx={{ padding: 0.5 }}>
                <ModeEditIcon />
              </IconButton>
              <IconButton size="small" color="error" onClick={handleDeleteTermFeeList} sx={{ padding: 0.5 }}>
                <DeleteIcon />
              </IconButton>
            </Stack>
          );
        },
      },
    ],
    []
  );

  return (
    <Page title="IssueList">
      <IssueListRoot>
        <Stack direction={{ xs: 'column', lg: 'row' }} gap={{ xs: 0, lg: 3 }} sx={{ mb: 0 }}>
          <Paper
            // elevation={2}
            sx={{
              borderRadius: '16px',
              // bgcolor: theme.palette.chart.violet[5],
              width: '100%',
              px: { xs: 3, md: 5 },
              py: 2,
              mb: 3,
            }}
          >
            <Typography variant="h6" fontSize={17}>
              Issue To
            </Typography>
            <Divider />
            <Stack direction={{ xs: 'column', sm: 'row', lg: 'column', xl: 'row' }} gap={2} width="100%">
              <Stack sx={{ width: { xs: '100%', sm: '40%', lg: '100%', xl: '40%' } }}>
                <form noValidate onReset={handleReset}>
                  <Grid pb={2} pt={1} container rowSpacing={1} columnSpacing={2}>
                    <Grid item lg={12} xs={12}>
                      <FormControl fullWidth>
                        <Typography variant="subtitle1" fontSize={12} color="GrayText">
                          Issued To
                        </Typography>
                        <Select variant="outlined" value={issuedTo} onChange={handleChangeIssuedTo} fullWidth>
                          {SELECT.map((option, index) => (
                            <MenuItem key={index} value={option}>
                              {option}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>

                    {issuedTo === 'Student' && (
                      <Grid item lg={12} xs={12}>
                        <FormControl fullWidth>
                          <Typography variant="subtitle1" fontSize={12} color="GrayText">
                            Select Class
                          </Typography>
                          <Select
                            variant="outlined"
                            value={classFilter}
                            onChange={handleClassChange}
                            placeholder="Select Class"
                            MenuProps={{
                              PaperProps: {
                                style: {
                                  maxHeight: '250px',
                                },
                              },
                            }}
                          >
                            <MenuItem value={-1}>Select All</MenuItem>
                            {ClassData.map((opt) => (
                              <MenuItem key={opt.classId} value={opt.classId}>
                                {opt.className}
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      </Grid>
                    )}

                    <Grid item lg={12} xs={12}>
                      <FormControl fullWidth>
                        <Typography variant="subtitle1" fontSize={12} color="GrayText">
                          {issuedTo === 'Student' ? 'Student' : 'Select Staff'}
                        </Typography>
                        {issuedTo === 'Student' ? (
                          <StudentsPickerField
                            variant="outlined"
                            loadRecentPaidList={loadIssueList}
                            currentRecentPaidListRequest={currentIssueListRequest}
                            setSelectedStudentIds={setSelectedStudentIds}
                            setSelectedStudentData={setSelectedStudentData}
                            classId={classFilter}
                            academicId={11}
                            width="100%"
                            componentsPropsWidth={350}
                          />
                        ) : (
                          <StaffPickerField width="100%" />
                        )}
                      </FormControl>
                    </Grid>

                    <Grid item lg={issuedTo === 'Student' ? 12 : 12} xs={12}>
                      <FormControl fullWidth>
                        <Typography variant="subtitle1" fontSize={12} color="GrayText">
                          Return Date
                        </Typography>
                        {/* <DateSelect /> */}
                        <DatePickers
                          // disabled={studentFilterData.length === 0}
                          width={{
                            // xs: '100%',
                            // sm: '100%',
                            // lg: '100%',
                            // xl: '100%',
                            // xxl: '295px',
                            '@media (max-width:1200px)': {
                              width: '100%',
                            },
                            '@media (min-width:1880px)': {
                              width: '125%',
                            },
                            // '@media (min-width:1640px)': {
                            //   width: '255px',
                            // },
                          }}
                          variant="outlined"
                          name="fromDateFilter"
                          // value={dayjs(dateFilter, 'DD/MM/YYYY')}
                          onChange={(e) => {
                            const formattedDate = e ? e.format('DD/MM/YYYY') : '';
                            // setDateFilter(formattedDate);
                            console.log('date::::', formattedDate);
                            loadIssueList({ ...currentIssueListRequest, paidDate: formattedDate });
                          }}
                        />
                      </FormControl>
                    </Grid>
                    {/* <Grid item lg={2}>
                    <Stack spacing={2} pt={{ xs: '6px', lg: '21px' }} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button type="reset" variant="outlined" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid> */}
                  </Grid>
                </form>
              </Stack>
              <Box
                sx={{
                  width: { xs: '100%', sm: '60%', lg: '100%', xl: '60%' },
                  pl: { xs: 0, sm: 2 },
                  borderLeft: {
                    xs: 0,
                    sm: isLight ? `1px solid ${theme.palette.grey[200]}` : `1px solid ${theme.palette.grey[700]}`,
                  },
                }}
              >
                <Typography mt={1} variant="subtitle2" fontSize={15}>
                  {issuedTo === 'Student' ? 'Student Info' : 'Staff Info'}
                </Typography>
                <Stack
                  alignItems={{ xs: 'center', sm: 'center' }}
                  // sx={{
                  //   flexDirection: {
                  //     xs: 'column',
                  //     sm: 'row',
                  //     md: 'column',
                  //   },
                  //   '@media (min-width:1446px)': {
                  //     flexDirection: 'column',
                  //   },
                  // }}
                  gap={1.5}
                >
                  <Avatar sx={{ width: '100px', height: '100px' }} alt="" src={selectedStudentData[0]?.image} />
                  <Typography variant="subtitle2" fontSize={14}>
                    {selectedStudentData[0]?.studentName || '--'}
                  </Typography>
                  <Stack direction="row" justifyContent="start" width="100%">
                    <LabelTypography width={75}>Class</LabelTypography>
                    <Typography variant="subtitle2" fontSize={13}>
                      : {selectedStudentData[0]?.className || '--'}
                    </Typography>
                  </Stack>
                  <Stack direction="row" justifyContent="start" width="100%">
                    <LabelTypography width={75}>AD No.</LabelTypography>
                    <Typography variant="subtitle2" fontSize={13}>
                      : {selectedStudentData[0]?.admissionNo || '--'}
                    </Typography>
                  </Stack>
                  <Stack direction="row" justifyContent="start" width="100%">
                    <LabelTypography width={75}>Father</LabelTypography>
                    <Typography variant="subtitle2" fontSize={13}>
                      : {selectedStudentData[0]?.fatherName || '--'}
                    </Typography>
                  </Stack>
                  <Stack direction="row" justifyContent="start" width="100%">
                    <LabelTypography width={75}>Mobile No.</LabelTypography>
                    <Typography variant="subtitle2" fontSize={13}>
                      : {selectedStudentData[0]?.fatherNumber || '--'}
                    </Typography>
                  </Stack>
                </Stack>
              </Box>
            </Stack>
            <Divider />
          </Paper>
          {/* --- */}
          <Paper
            elevation={0}
            sx={{
              minHeight: '335px',
              borderRadius: '16px',

              // border: 1,
              // borderColor: theme.palette.grey[300],
              width: '100%',
              px: { xs: 3, md: 5 },
              py: 2,
              mb: 3,
            }}
          >
            <Stack direction="row" gap={2} alignItems="center">
              <Typography variant="h6" fontSize={17}>
                Order List
              </Typography>
            </Stack>
            <Divider />

            <Box
              height={270}
              sx={{
                // height: 270,
                '@media (max-width:1200px)': {
                  height: 540,
                },
                '@media (max-width:992px)': {
                  height: 270,
                },
                mt: 2,
              }}
              overflow="auto"
            >
              {selectedStudentData.length === 0 ? (
                <Box display="flex" alignItems="center" justifyContent="center" width="100%" height="100%">
                  <Stack direction="column" alignItems="center">
                    <img src={NoData} width="150px" alt="No Orders" />
                    <Typography variant="subtitle2" mt={2} color="GrayText">
                      NO ORDERS!
                    </Typography>
                  </Stack>
                </Box>
              ) : (
                <Stack spacing={2}>
                  {bookList.map((book, index) => (
                    <>
                      <Stack key={index} direction="row" alignItems="center" gap={2}>
                        <Stack>
                          <Chip
                            sx={{
                              mb: 0.5,
                              width: 100,
                              fontWeight: 600,
                              clipPath: 'polygon(0 0, 100% 0, calc(100% - 10px) 50%, 100% 100%, 0 100%)',
                              fontSize: 10,
                              borderRadius: 0,
                            }}
                            size="small"
                            label="Not Returned"
                            color="error"
                          />
                          <img src={book.image} width="75px" height="90px" alt={book.name} />
                        </Stack>
                        <Stack gap={0.5}>
                          <Stack direction="row" gap={1}>
                            <Typography variant="subtitle2" fontSize={13} whiteSpace="nowrap">
                              Name:
                            </Typography>
                            <Typography variant="subtitle2" fontSize={13} whiteSpace="nowrap">
                              {book.name}
                            </Typography>
                          </Stack>
                          <Stack direction="row" gap={1}>
                            <LabelTypography>Code :</LabelTypography>
                            <LabelTypography> {book.code}</LabelTypography>
                          </Stack>
                          <Stack direction="row" gap={1}>
                            <LabelTypography>Author :</LabelTypography>
                            <LabelTypography> {book.author}</LabelTypography>
                          </Stack>
                        </Stack>
                        <Stack direction="row" sx={{ ml: 'auto' }} mr={1}>
                          <Button
                            sx={{ bgcolor: 'black', '&:hover': { bgcolor: 'black' } }}
                            variant="contained"
                            color="secondary"
                            onClick={() => setReturnListOpen(true)}
                          >
                            Return
                          </Button>
                        </Stack>
                      </Stack>
                      <Divider />
                    </>
                  ))}
                </Stack>
              )}
            </Box>
          </Paper>
        </Stack>

        {/* ===== */}
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: 2 }}>
          <div className="card-main-body">
            <BookTable />
            {/* <DataTable columns={issueListColumns} data={data} getRowKey={getRowKey} fetchStatus="success" /> */}
          </div>
          {/* <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 2 }}>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="secondary">
                Cancel
              </Button>
              <Button onClick={handleOpen} variant="contained" color="primary">
                Save
              </Button>
            </Stack>
          </Box> */}
        </Card>
      </IssueListRoot>

      <TemporaryDrawer onClose={toggleDrawerClose} Title="Upload Details" state={drawerOpen} DrawerContent="" />
      <TemporaryDrawer
        onClose={() => setReturnListOpen(false)}
        Title="Book Details"
        fontWeight={500}
        state={returnListOpen}
        minWidth={300}
        DrawerContent={
          <ReturnBook onCancel={() => setReturnListOpen(false)} bookDetails={bookDetails} fineDetails={fineDetails} />
        }
      />
    </Page>
  );
}

export default IssueList;
