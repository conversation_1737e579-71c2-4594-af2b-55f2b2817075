import React, { FormEvent, useCallback, useEffect, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  Chip,
  FormControl,
  MenuItem,
  Select,
  SelectChangeEvent,
  Collapse,
  Tooltip,
} from '@mui/material';
import styled from 'styled-components';
import { MdAdd } from 'react-icons/md';
import DeleteIcon from '@mui/icons-material/Delete';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import { useAppSelector } from '@/hooks/useAppSelector';
import {
  getClassListData,
  getClassListPageInfo,
  getClassListStatus,
  getDeletingRecords,
  getSortColumn,
  getSortDirection,
  getSubmitting,
} from '@/config/storeSelectors';
import { setSortColumn, setSortDirection } from '@/store/Academics/ClassManagement/classManagement.slice';
import {
  addNewClass,
  deleteClass,
  fetchClassList,
  updateClass,
} from '@/store/Academics/ClassManagement/classManagement.thunks';
import { ClassListInfo, ClassListRequest } from '@/types/AcademicManagement';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { STATUS_OPTIONS } from '@/config/Selection';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import SearchIcon from '@mui/icons-material/Search';
import CreateEditClassForm from './CreateEditClassForm';
import CreateClassMultiple from './CreateClassMultiple';
import deleteBin from '@/assets/ManageFee/deleteBin.json';
import Success from '@/assets/ManageFee/Success.json';
import { useTheme } from '@mui/material';

const ManageClassRoot = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 50px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

const DefaultClassInfo: ClassListInfo = {
  classId: 0,
  className: '',
  classDescription: '',
  classStatus: 1,
};

function ManageClass() {
  const { confirm } = useConfirm();
  const theme = useTheme();
  // const isLight = useSettings().themeMode === 'light';
  // const [forceDispatch, setForceDispatch] = useState(false);
  const [selectedClassDetail, setSelectedClassDetail] = useState<ClassListInfo>(DefaultClassInfo);

  const dispatch = useAppDispatch();
  const [classNameFilter, setClassNameFilter] = useState('');
  const [classStatusFilter, setClassStatusFilter] = useState(-1);

  const classListStatus = useAppSelector(getClassListStatus);
  const classListData = useAppSelector(getClassListData);
  // const classListError = useAppSelector(getClassListError);
  const paginationInfo = useAppSelector(getClassListPageInfo);
  const sortColumn = useAppSelector(getSortColumn);
  const sortDirection = useAppSelector(getSortDirection);
  const isSubmitting = useAppSelector(getSubmitting);
  const deletingRecords = useAppSelector(getDeletingRecords);

  const { pagenumber, pagesize, totalrecords } = paginationInfo;

  const currentClassListRequest = useMemo(
    () => ({
      pageNumber: pagenumber,
      pageSize: pagesize,
      sortColumn,
      sortDirection,
      filters: {
        className: classNameFilter,
        classStatus: classStatusFilter,
      },
    }),
    [classNameFilter, classStatusFilter, pagenumber, pagesize, sortColumn, sortDirection]
  );

  const loadClassList = useCallback(
    (request: ClassListRequest) => {
      dispatch(fetchClassList(request));
    },
    [dispatch]
  );

  const [drawerOpen, setDrawerOpen] = useState(false);
  const [showFilter, setShowFilter] = useState(false);

  const [view, setView] = useState<'ClassList' | 'CreateClassMultiple'>('ClassList');

  const handleAddNewClass = () => {
    setSelectedClassDetail(DefaultClassInfo);
    setDrawerOpen(true);
  };

  const toggleDrawerClose = useCallback(() => setDrawerOpen(false), []);

  const handleSaveorEdit = useCallback(
    async (value: ClassListInfo, mode: 'create' | 'edit') => {
      if (mode === 'create') {
        const { classId, ...rest } = value;
        const response = await dispatch(addNewClass(rest)).unwrap();

        if (response.id > 0) {
          setDrawerOpen(false);
          const successMessage = <SuccessMessage jsonIcon={Success} message="Class created successfully" />;
          await confirm(successMessage, 'Class Created', { okLabel: 'Ok', showOnlyOk: true });

          loadClassList({ ...currentClassListRequest, pageNumber: 1, sortColumn: 'classId', sortDirection: 'desc' });
        }
      } else {
        const response = await dispatch(updateClass(value)).unwrap();
        if (response.rowsAffected > 0) {
          setDrawerOpen(false);
          const successMessage = <SuccessMessage message="Class updated successfully" />;
          await confirm(successMessage, 'Class Updated', { okLabel: 'Ok', showOnlyOk: true });

          loadClassList(currentClassListRequest);
        }
      }
    },
    [confirm, currentClassListRequest, dispatch, loadClassList]
  );

  const handleAddMultipleClasses = () => {
    setView('CreateClassMultiple');
  };

  const handlebackFromClassMultiple = () => {
    setView('ClassList');
    loadClassList({ ...currentClassListRequest, pageNumber: 1, sortColumn: 'classId', sortDirection: 'asc' });
  };

  useEffect(() => {
    if (classListStatus === 'idle') {
      loadClassList(currentClassListRequest);
    }
    // console.log('datass::', classListData);
  }, [loadClassList, classListStatus, currentClassListRequest]);

  const handleStatusChange = (e: SelectChangeEvent) => {
    const statusVal = parseInt(e.target.value, 10);
    setClassStatusFilter(statusVal);
    loadClassList({
      ...currentClassListRequest,
      filters: { ...currentClassListRequest.filters, classStatus: statusVal },
    });
  };

  const getRowKey = useCallback((row: ClassListInfo) => row.classId, []);

  const handlePageChange = useCallback(
    (event: unknown, newPage: number) => {
      loadClassList({ ...currentClassListRequest, pageNumber: newPage + 1 });
    },
    [currentClassListRequest, loadClassList]
  );

  const handleChangeRowsPerPage = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const newPageSize = +event.target.value;
      loadClassList({ ...currentClassListRequest, pageNumber: 1, pageSize: newPageSize });
    },
    [currentClassListRequest, loadClassList]
  );

  const handleEditClass = useCallback((classObj: ClassListInfo) => {
    setSelectedClassDetail(classObj);
    setDrawerOpen(true);
  }, []);

  const currentItemCount = classListData.length;

  const handleDeleteClass = useCallback(
    async (classObj: ClassListInfo) => {
      const deleteConfirmMessage = (
        <DeleteMessage
          jsonIcon={deleteBin}
          message={
            <div>
              Are you sure you want to delete the class <br />
              <span style={{ color: theme.palette.primary.main }}>&quot;{classObj.className}&quot;</span> ?
            </div>
          }
        />
      );
      if (await confirm(deleteConfirmMessage, 'Delete Class?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
        const deleteResponse = await dispatch(deleteClass(classObj.classId)).unwrap();
        if (deleteResponse.deleted) {
          const deleteDoneMessage = <DeleteMessage message="Class deleted successfully." />;
          await confirm(deleteDoneMessage, 'Deleted', { okLabel: 'Ok', showOnlyOk: true });
          let pageNumberToMove = pagenumber;
          if (paginationInfo.remainingpages === 0 && pagenumber > 1 && currentItemCount === 1) {
            pageNumberToMove = pagenumber - 1;
          }
          loadClassList({ ...currentClassListRequest, pageNumber: pageNumberToMove });
        }
      }
    },
    [
      confirm,
      currentClassListRequest,
      currentItemCount,
      dispatch,
      loadClassList,
      pagenumber,
      paginationInfo.remainingpages,
      theme,
    ]
  );

  const handleSort = useCallback(
    (column: string) => {
      const newReq = { ...currentClassListRequest };
      if (column === sortColumn) {
        console.log('Toggling direction');
        const sortDirectionNew = sortDirection === 'asc' ? 'desc' : 'asc';
        newReq.pageNumber = 1;
        newReq.sortDirection = sortDirectionNew;
        dispatch(setSortDirection(sortDirectionNew));
      } else {
        newReq.pageNumber = 1;
        newReq.sortColumn = column;
        dispatch(setSortColumn(column));
      }

      loadClassList(newReq);
    },
    [currentClassListRequest, dispatch, loadClassList, sortColumn, sortDirection]
  );

  const classListColumns: DataTableColumn<ClassListInfo>[] = useMemo(
    () => [
      {
        name: 'classId',
        headerLabel: 'Class Id',
        dataKey: 'classId',
        sortable: true,
      },
      {
        name: 'className',
        dataKey: 'className',
        headerLabel: 'Class Name',
        sortable: true,
      },
      {
        name: 'classDescription',
        dataKey: 'classDescription',
        headerLabel: 'Class Description',
      },
      {
        name: 'classStatus',
        headerLabel: 'Class Status',
        sortable: true,
        renderCell: (row) => {
          return (
            <Chip
              size="small"
              label={row.classStatus === 0 ? 'Unpublished' : 'Published'}
              variant="outlined"
              color={row.classStatus === 1 ? 'success' : 'error'}
            />
          );
        },
      },
      {
        name: 'actions',
        headerLabel: 'Actions',
        renderCell: (row) => {
          return (
            <Stack direction="row" gap={1}>
              <IconButton size="small" onClick={() => handleEditClass(row)} sx={{ padding: 0.5 }}>
                <ModeEditIcon />
              </IconButton>
              <IconButton size="small" color="error" sx={{ padding: 0.5 }} onClick={() => handleDeleteClass(row)}>
                <DeleteIcon />
              </IconButton>
            </Stack>
          );
        },
      },
    ],
    [handleDeleteClass, handleEditClass]
  );

  const pageProps = useMemo(
    () => ({
      rowsPerPageOptions: [10, 20, 30, 50, 100, 200, 300, 500],
      pageNumber: pagenumber - 1,
      pageSize: pagesize,
      totalRecords: totalrecords,
      onPageChange: handlePageChange,
      onRowsPerPageChange: handleChangeRowsPerPage,
    }),
    [handleChangeRowsPerPage, handlePageChange, pagenumber, pagesize, totalrecords]
  );

  const handleReset = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      setClassNameFilter('');
      setClassStatusFilter(-1);
      loadClassList({
        ...currentClassListRequest,
        pageNumber: 1,
        pageSize: 20,
        sortColumn: 'classId',
        sortDirection: 'desc',
        filters: {
          className: '',
          classStatus: -1,
        },
      });
    },
    [currentClassListRequest, loadClassList]
  );

  return view === 'ClassList' ? (
    <Page title="Manage Class">
      <ManageClassRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between" alignItems="center" flexWrap="wrap" pb={1}>
            <Stack direction="row" alignItems="center" justifyContent="space-between" flex={3} whiteSpace="nowrap">
              <Typography variant="h6" fontSize={17}>
                Manage Class
              </Typography>
              <Tooltip title="Search">
                <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                  <SearchIcon />
                </IconButton>
              </Tooltip>
            </Stack>
            <Box
              display="flex"
              justifyContent="end"
              alignItems="center"
              columnGap={2}
              sx={{
                flexShrink: 0,
                flex: 1,
                '@media (max-width: 340px)': {
                  flexWrap: 'wrap',
                  rowGap: 1,
                },
              }}
            >
              <Button
                sx={{
                  borderRadius: '20px',
                  whiteSpace: 'nowrap',
                  '@media (max-width: 340px)': {
                    width: '100%',
                  },
                }}
                variant="outlined"
                size="small"
                onClick={handleAddNewClass}
              >
                <MdAdd size="20px" /> Add Class
              </Button>
              <Button
                sx={{
                  borderRadius: '20px',
                  whiteSpace: 'nowrap',
                  '@media (max-width: 340px)': {
                    width: '100%',
                  },
                }}
                size="small"
                variant="outlined"
                onClick={handleAddMultipleClasses}
              >
                <MdAdd size="20px" /> Add Multiple Classes
              </Button>
            </Box>
          </Stack>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate onReset={handleReset}>
                <Grid pb={2} pt={1} container spacing={3} alignItems="end">
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 300 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class Name
                      </Typography>
                      <TextField
                        name="className"
                        value={classNameFilter}
                        onChange={(e) => {
                          setClassNameFilter(e.target.value);
                          loadClassList({
                            ...currentClassListRequest,
                            filters: { ...currentClassListRequest.filters, className: e.target.value },
                          });
                        }}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 300 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class Status
                      </Typography>
                      <Select
                        labelId="classStatusFilter"
                        id="classStatusFilterSelect"
                        value={classStatusFilter?.toString() || '-1'}
                        onChange={handleStatusChange}
                      >
                        <MenuItem value={-1}>All</MenuItem>
                        {STATUS_OPTIONS.map((opt) => (
                          <MenuItem key={opt.id} value={opt.id}>
                            {opt.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12} sm={6}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      {/* <Button type="submit" variant="contained" color="primary" fullWidth>
                        Search
                      </Button> */}
                      <Button type="reset" variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>
            <Paper className="card-table-container" sx={{ marginTop: '12px' }}>
              <DataTable
                tableStyles={{ minWidth: { xs: '700px', lg: '1200px', xl: '100% ' } }}
                columns={classListColumns}
                data={classListData}
                getRowKey={getRowKey}
                fetchStatus={classListStatus}
                allowPagination
                allowSorting
                PaginationProps={pageProps}
                deletingRecords={deletingRecords}
                sortColumn={sortColumn}
                sortDirection={sortDirection}
                onSort={handleSort}
              />
            </Paper>
          </div>
        </Card>
      </ManageClassRoot>

      <TemporaryDrawer
        onClose={toggleDrawerClose}
        Title={selectedClassDetail === DefaultClassInfo ? 'Add New Class' : 'Edit Class Details'}
        state={drawerOpen}
        DrawerContent={
          <CreateEditClassForm
            classDetail={selectedClassDetail}
            onSave={handleSaveorEdit}
            onCancel={toggleDrawerClose}
            onClose={toggleDrawerClose}
            isSubmitting={isSubmitting}
          />
        }
      />
    </Page>
  ) : (
    <CreateClassMultiple onBackClick={handlebackFromClassMultiple} />
  );
}

export default ManageClass;
