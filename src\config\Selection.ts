import { ClassSelect, YearSelect } from '@/types/AttendanceMarking';
import { ExamSelect } from '@/types/ExamCenter';
import { FeeTypeOptions } from '@/types/ManageFee';
import { MessageTypeOptions } from '@/types/MessageBox';

export const GENDER_SELECT = [
  { id: '-1', gender: 'Select All' },
  { id: '0', gender: 'Male' },
  { id: '1', gender: 'Female' },
  { id: '2', gender: 'Other' },
];
export const STATUS_OPTIONS = [
  { id: '-1', name: 'Select Status' },
  { id: '1', name: 'Published' },
  { id: '0', name: 'Unpublished' },
];
export const ROLL_OPTIONS = [
  { id: 1, name: 'Class Teacher' },
  { id: 0, name: 'Teacher' },
];
export const STATUS_OPTION2 = [
  { id: -1, name: 'Select Status' },
  { id: 1, name: 'Published' },
  { id: 0, name: 'Unpublished' },
];
export const ALBUM_TYPE_OPTIONS = [
  { id: 1, name: 'Image' },
  { id: 0, name: 'Video' },
];
export const STAFF_CATEGORY_SELECT = [
  // { id: -1, category: 'Select category' },
  { id: 1, category: 'Hight Scool' },
  { id: 2, category: 'Primary' },
  { id: 3, category: 'Nursery' },
  { id: 4, category: 'CBSC' },
  { id: 5, category: 'PU' },
];
export const MONTH_SELECT_OPTION = [
  { id: 1, name: 'Jan' },
  { id: 2, name: 'Feb' },
  { id: 3, name: 'Mar' },
  { id: 4, name: 'Jun' },
  { id: 5, name: 'Jul' },
  { id: 6, name: 'Aug' },
  { id: 7, name: 'Sep' },
  { id: 8, name: 'Oct' },
  { id: 9, name: 'Nov' },
  { id: 10, name: 'Dec' },
];
export const PTA_ROLE_SELECT = [
  { id: '-1', role: 'Select Role' },
  { id: '1', role: 'President' },
  { id: '2', role: 'Vice President' },
  { id: '3', role: 'Secretary' },
  { id: '4', role: 'Joint Secretary' },
  { id: '5', role: 'Cashier' },
  { id: '6', role: 'Member' },
  { id: '7', role: 'Other' },
];

export const YEAR_SELECT = ['2023-2024', '2022-2023', '2021-2022', '2020-2021'];
export const CLASS_SELECT = ['VIII A', 'VIII B', 'VIII C', 'IX A'];
export const SESSION_SELECT = ['VII-A', 'X-B', 'VII-C', 'VII-D'];
export const TYPE_SELECT = ['Through Link', 'Approved Template', '30 Character'];
export const DAY_SELECT = ['Select Day','Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
export const STATUS_SELECT = ['Published', 'Unpublished'];
// export const GENDER_SELECT = ['Male', 'Female', 'Other'];
export const CATEGORY_SELECT = ['Category 1', 'Category 2', 'Category 3'];
export const options = [{ option: 'Edit' }, { option: 'Delete' }];
export const SUBJECT_SELECT = ['Hindi', 'English', 'Maths', 'Science', 'Social', 'Arabic', 'Music', 'P.E.T'];

export const MESSAGE_TYPE_OPTIONS: MessageTypeOptions[] = [
  { id: 1, name: '30 Character' },
  { id: 2, name: 'Approved Tepmlate' },
  { id: 3, name: 'Through Link' },
];

export const YEAR_SELECT_OPTIONS: YearSelect[] = [
  { id: 1, year: '2023-2024' },
  { id: 2, year: '2022-2023' },
  { id: 3, year: '2021-2022' },
  { id: 4, year: '2020-2021' },
];
export const CLASS_SELECT_OPTIONS: ClassSelect[] = [
  { id: 1, class: 'VIII A' },
  { id: 2, class: 'VIII B' },
  { id: 3, class: 'VIII C' },
  { id: 4, class: 'IX A' },
];
export const EXAM_SELECT_OPTIONS: ExamSelect[] = [
  { id: 1, exam: 'Annual Examination' },
  { id: 2, exam: 'Monthly Feb' },
  { id: 3, exam: 'Monthly Mar' },
  { id: 4, exam: 'Monthly Apr' },
  { id: 4, exam: 'Monthly May' },
];

export const FEE_TYPE_OPTIONS: FeeTypeOptions[] = [
  { id: 1, name: 'Standard' },
  { id: 2, name: 'School' },
  { id: 3, name: 'Optional' },
  { id: 4, name: 'Individual' },
];

export const FEE_TYPE_ID_OPTIONS: FeeTypeOptions[] = [
  { id: 1, name: 'Accademic Fee' },
  { id: 2, name: 'Bus Fee' },
];

export const MONTH_SELECT = [
  { id: -1, month: 'Select' },
  { id: 1, month: 'Jan' },
  { id: 2, month: 'Feb' },
  { id: 3, month: 'Mar' },
  { id: 4, month: 'Apr' },
  { id: 5, month: 'May' },
  { id: 6, month: 'Jun' },
  { id: 7, month: 'Jul' },
  { id: 8, month: 'Aug' },
  { id: 9, month: 'Sep' },
  { id: 10, month: 'Oct' },
  { id: 11, month: 'Nov' },
  { id: 12, month: 'Dec' },
];
