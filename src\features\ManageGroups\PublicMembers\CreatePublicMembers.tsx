import { useState } from 'react';
import { Button, TextField, Select, MenuItem, Typography, Box, Stack, FormControl, useTheme } from '@mui/material';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import LoadingButton from '@mui/lab/LoadingButton';
import { STATUS_OPTIONS, YEAR_SELECT_OPTIONS } from '@/config/Selection';
import { PublicMembersInfo } from '@/types/GeneralSettings';

export type CreatePublicMembersProps = {
  onSave?: (values: PublicMembersInfo) => void;
  onCancel?: () => void;
  onClose?: () => void;
  isSubmitting?: boolean;
};

const CreateAlbumValidationSchema = Yup.object({
  academicYear: Yup.string().required('Please enter Academic Year'),
  groupName: Yup.string().required('Please enter Group Name'),
  memberName: Yup.string().required('Please enter Member Name'),
  memberNumber: Yup.string().required('Please enter Member Number'),
});

function CreateMembers({ onSave, onCancel, isSubmitting }: CreatePublicMembersProps) {
  const theme = useTheme();
  const {
    values: { academicYear, groupName, memberName, memberNumber },
    handleChange,
    handleSubmit,
    touched,
    errors,
    setFieldValue,
  } = useFormik<PublicMembersInfo>({
    initialValues: {
      academicYear: '',
      groupName: '',
      memberName: '',
      memberNumber: '',
    },
    validationSchema: CreateAlbumValidationSchema,
    onSubmit: (values) => {
      onSave(values);
    },
  });

  return (
    <Box mt={3}>
      <form noValidate onSubmit={handleSubmit}>
        <Stack
          sx={{
            height: 'calc(100vh - 150px)',
            overflow: 'auto',
            '&::-webkit-scrollbar': {
              width: 0,
            },
          }}
          direction="column"
        >
          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Academic Year
            </Typography>
            <Select
              name="academicYear"
              value={academicYear}
              onChange={handleChange}
              error={touched.academicYear && !!errors.academicYear}
              disabled={isSubmitting}
            >
              {YEAR_SELECT_OPTIONS.map((opt) => (
                <MenuItem key={opt.id} value={opt.id}>
                  {opt.year}
                </MenuItem>
              ))}
            </Select>
            {touched.academicYear && !!errors.academicYear && (
              <Typography color="red" fontSize="0.750rem" ml={2} mt={1} variant="subtitle1">
                {errors.academicYear}
              </Typography>
            )}
          </FormControl>
          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Group Name
            </Typography>
            <Select
              name="groupName"
              value={groupName}
              onChange={handleChange}
              error={touched.groupName && !!errors.groupName}
              disabled={isSubmitting}
            >
              {STATUS_OPTIONS.map((opt) => (
                <MenuItem key={opt.id} value={opt.id}>
                  {opt.name}
                </MenuItem>
              ))}
            </Select>
            {touched.groupName && !!errors.groupName && (
              <Typography color="red" fontSize="0.750rem" ml={2} mt={1} variant="subtitle1">
                {errors.groupName}
              </Typography>
            )}
          </FormControl>
          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Member Name
            </Typography>
            <TextField
              placeholder="Enter name"
              name="memberName"
              value={memberName}
              onChange={handleChange}
              error={touched.memberName && !!errors.memberName}
              helperText={errors.memberName}
              disabled={isSubmitting}
            />
          </FormControl>
          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Member Number
            </Typography>
            <TextField
              placeholder="Enter number"
              name="memberNumber"
              value={memberNumber}
              onChange={handleChange}
              error={touched.memberNumber && !!errors.memberNumber}
              helperText={errors.memberNumber}
              disabled={isSubmitting}
            />
          </FormControl>
        </Stack>
        <Box sx={{ justifyContent: 'center', pt: 3, position: 'relative' }}>
          <Stack spacing={2} direction="row" sx={{}}>
            <Button onClick={onCancel} fullWidth variant="contained" color="secondary" type="button">
              Cancel
            </Button>
            <LoadingButton
              loading={isSubmitting}
              fullWidth
              variant="contained"
              color="primary"
              type="submit"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Saving...' : 'Save'}
            </LoadingButton>
          </Stack>
        </Box>
      </form>
    </Box>
  );
}

export default CreateMembers;
