import React from 'react';
import { Link, useLocation, useOutletContext } from 'react-router-dom';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';
import styled from 'styled-components';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import AlbumList from '@/features/Album/List/List';
import PhotoList from '@/features/Album/AlbumPhoto/Photo';
import VideoList from '@/features/Album/AlbumVideo/Video';
import Page from '@/components/shared/Page';
import TcList from '@/features/Tc&Cc/TcList/TcList';
import CcList from '@/features/Tc&Cc/CcList/CcList';
import SessionPlanList from '@/features/SchoolPlan/SessionPlanList/SessionPlanList';
import LessonPlanList from '@/features/SchoolPlan/LessonPlanList/LessonPlanList';
import AnnualPlanList from '@/features/SchoolPlan/AnnualPlanList/AnnualPlanList';
import { useDispatch } from 'react-redux';
import { flushStore } from '@/store/flush.slice';
import { OutletContextType } from '@/types/Common';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const SchoolPlanRoot = styled.div`
  min-height: calc(100vh - ${TOP_BAR_HEIGHT});

  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};

  .tab {
    background-color: ${(props) => (props.theme.themeMode === 'light' ? '#fff' : props.theme.palette.grey[800])};
  }
  .Mui-selected {
    color: ${(props) => props.theme.palette.primary.main};
  }
`;

function TabPanel({ children, value, index }: TabPanelProps) {
  return (
    <Box
      sx={{ paddingTop: '3rem' }}
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
    >
      {value === index && <Box>{children}</Box>}
    </Box>
  );
}
function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`,
  };
}

function SchoolPlan() {
  const { topBarHeight } = useOutletContext<OutletContextType>();
  const location = useLocation();
  const [value, setValue] = React.useState(0);

  const dispatch = useDispatch();

  React.useEffect(() => {
    dispatch(flushStore());
    console.log('Flush Store');
  }, [dispatch, value]);

  React.useEffect(() => {
    const currentPath = location.pathname;
    const routePaths = [
      '/school-plan/session-plan-list',
      '/school-plan/lesson-plan-list',
      '/school-plan/annual-plan-list',
    ];
    const index = routePaths.indexOf(currentPath);
    if (index !== -1) {
      setValue(index);
    }
  }, [location]);

  return (
    <Page title="">
      <SchoolPlanRoot>
        <Tabs
          className="tab"
          variant="scrollable"
          scrollButtons
          allowScrollButtonsMobile
          value={value}
          aria-label="basic tabs"
          sx={{
            ml: '0px',
            borderBottom: 1,
            borderColor: 'divider',
            position: 'fixed',
             top: `${topBarHeight}px`,
            zIndex: 1,
            width: '100%',
          }}
        >
          <Tab {...a11yProps(0)} label="Session Plan List" component={Link} to="/school-plan/session-plan-list" />
          <Tab {...a11yProps(1)} label="Lesson Plan List" component={Link} to="/school-plan/lesson-plan-list" />
          <Tab {...a11yProps(2)} label="Annual Plan List" component={Link} to="/school-plan/annual-plan-list" />
        </Tabs>

        <TabPanel value={value} index={0}>
          <SessionPlanList />
        </TabPanel>
        <TabPanel value={value} index={1}>
          <LessonPlanList />
        </TabPanel>
        <TabPanel value={value} index={2}>
          <AnnualPlanList />
        </TabPanel>
      </SchoolPlanRoot>
    </Page>
  );
}

export default SchoolPlan;
