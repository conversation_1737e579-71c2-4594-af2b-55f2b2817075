import { STATUS_OPTIONS } from '@/config/Selection';
import { SubjectListInfo } from '@/types/AcademicManagement';
import LoadingButton from '@mui/lab/LoadingButton/LoadingButton';
import { Button, TextField, Typography, Box, Stack, MenuItem, Select, FormControl } from '@mui/material';
import { useFormik } from 'formik';
import * as Yup from 'yup';

export type CreateEditSubjectFormProps = {
  onSave: (values: SubjectListInfo, mode: 'create' | 'edit') => void;
  onCancel: (mode: 'create' | 'edit') => void;
  onClose: (mode: 'create' | 'edit') => void;
  subjectDetail: SubjectListInfo;
  isSubmitting: boolean;
};

const CreateEditSubjectValidationSchema = Yup.object({
  subjectName: Yup.string().required('Please enter Subject Name'),
  subjectDescription: Yup.string().required('Please enter Subject description'),
  subjectStatus: Yup.number().oneOf([0, 1], 'Please select Subject Status'),
});

function CreateEditSubjectForm({ subjectDetail, onSave, onCancel, isSubmitting }: CreateEditSubjectFormProps) {
  const mode = subjectDetail.subjectId === 0 ? 'create' : 'edit';
  const {
    values: { subjectName, subjectDescription, subjectStatus },
    handleChange,
    handleSubmit,
    touched,
    errors,
  } = useFormik<SubjectListInfo>({
    initialValues: {
      subjectId: subjectDetail.subjectId,
      subjectName: subjectDetail.subjectName,
      subjectDescription: subjectDetail.subjectDescription,
      subjectStatus: subjectDetail.subjectStatus,
    },
    validationSchema: CreateEditSubjectValidationSchema,
    onSubmit: (values) => {
      onSave(values, mode);
    },
  });

  return (
    <Box mt={3}>
      <form noValidate onSubmit={handleSubmit}>
        <Stack sx={{ height: 'calc(100vh - 150px)' }} direction="column">
          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Subject Name
            </Typography>
            <TextField
              placeholder="Subject Name"
              name="subjectName"
              value={subjectName}
              onChange={handleChange}
              error={touched.subjectName && !!errors.subjectName}
              helperText={errors.subjectName}
              disabled={isSubmitting}
            />
          </FormControl>
          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Description
            </Typography>
            <TextField
              multiline
              fullWidth
              minRows={2}
              InputProps={{ inputProps: { style: { resize: 'vertical' } } }}
              placeholder="Enter description..."
              name="subjectDescription"
              value={subjectDescription}
              onChange={handleChange}
              error={touched.subjectDescription && !!errors.subjectDescription}
              helperText={errors.subjectDescription}
              disabled={isSubmitting}
            />
          </FormControl>
          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Status
            </Typography>
            <Select
              name="subjectStatus"
              value={subjectStatus}
              onChange={handleChange}
              error={touched.subjectStatus && !!errors.subjectStatus}
              disabled={isSubmitting}
            >
              {STATUS_OPTIONS.map((opt) => (
                <MenuItem key={opt.id} value={opt.id}>
                  {opt.name}
                </MenuItem>
              ))}
            </Select>
            {touched.subjectStatus && !!errors.subjectStatus && (
              <Typography color="red" fontSize="0.625rem" variant="subtitle1">
                {errors.subjectStatus}
              </Typography>
            )}
          </FormControl>
        </Stack>
        <Box sx={{ justifyContent: 'center', pt: 3, position: 'relative' }}>
          <Stack spacing={2} direction="row" sx={{}}>
            <Button onClick={() => onCancel(mode)} fullWidth variant="contained" color="secondary" type="button">
              Cancel
            </Button>
            <LoadingButton
              loading={isSubmitting}
              fullWidth
              variant="contained"
              color="primary"
              type="submit"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Saving...' : 'Save'}
            </LoadingButton>
          </Stack>
        </Box>
      </form>
    </Box>
  );
}

export default CreateEditSubjectForm;
