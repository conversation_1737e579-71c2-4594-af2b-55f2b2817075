import { VoiceDataType, VoiceState } from '@/types/VoiceMessage';
import { PayloadAction, createSlice } from '@reduxjs/toolkit';
import {
  DeleteVoiceMessageList,
  createVoice,
  fetchVoiceMessageList,
  fileUpload,
  voiceMessageSendToAllConveyors,
  voiceMessageSendToAllParents,
  voiceMessageSendToAllPta,
  voiceMessageSendToAllStaff,
  voiceMessageSendToClassDivision,
  voiceMessageSendToClassSection,
  voiceMessageSendToConveyorList,
  voiceMessageSendToGroupMembersList,
  voiceMessageSendToGroupWise,
  voiceMessageSendToParents,
  voiceMessageSendToPtaList,
  voiceMessageSendToPublicGroupMembersList,
  voiceMessageSendToPublicGroupWise,
  voiceMessageSendToStaffList,
} from './voiceMessage.thunk';
import { flushStore } from '../flush.slice';

const initialState: VoiceState = {
  voiceList: {
    data: [],
    status: 'idle',
    error: null,
  },
  filesUploadList: {
    data: [],
    status: 'idle',
    error: null,
  },
  submitting: false,
  deletingRecords: {},
  error: null,
};

export const voiceMessageSlice = createSlice({
  name: 'voiceList',
  initialState,
  reducers: {
    setVoiceList: (state, action: PayloadAction<VoiceDataType[]>) => {
      state.voiceList.data = action.payload;
    },
    setSubmitting: (state, action: PayloadAction<boolean>) => {
      state.submitting = action.payload;
    },
    // setProgress: (state, action: PayloadAction<number>) => {
    //   state.filesUploadList.progress = action.payload;
    // },
  },
  extraReducers(builder) {
    builder
      // extraReducers for fetching Notification List
      // ------Get------- //
      .addCase(fetchVoiceMessageList.pending, (state) => {
        state.voiceList.status = 'loading';
        state.voiceList.error = null;
      })
      .addCase(fetchVoiceMessageList.fulfilled, (state, action) => {
        state.voiceList.status = 'success';
        state.voiceList.data = action.payload;
        console.log('action.payload::::', action.payload);
        state.voiceList.error = null;
      })
      .addCase(fetchVoiceMessageList.rejected, (state, action) => {
        state.voiceList.status = 'error';
        state.voiceList.error = action.payload || 'Unknown error in fetching Notification Template';
      })
      // ------Create------- //
      .addCase(createVoice.pending, (state) => {
        state.submitting = true;
        state.voiceList.error = null;
      })
      .addCase(createVoice.fulfilled, (state) => {
        state.submitting = false;
        state.voiceList.error = null;
      })
      .addCase(createVoice.rejected, (state, action) => {
        state.submitting = false;
        state.voiceList.error = action.error.message || 'Unknown error in fetching voice message';
      })

      // ------File Upload------- //
      .addCase(fileUpload.pending, (state) => {
        state.submitting = true;
        state.filesUploadList.error = null;
      })
      .addCase(fileUpload.fulfilled, (state, action) => {
        state.submitting = false;
        state.filesUploadList.data = action.payload;
        state.filesUploadList.error = null;
      })
      .addCase(fileUpload.rejected, (state, action) => {
        state.submitting = false;
        state.filesUploadList.error = action.error.message || 'Unknown error in fetching Notification Template';
      })
      .addCase(DeleteVoiceMessageList.pending, (state, action) => {
        const { voiceId } = action.meta.arg;
        state.deletingRecords[voiceId] = true;
        state.error = null;
      })
      .addCase(DeleteVoiceMessageList.fulfilled, (state, action) => {
        const { voiceId } = action.meta.arg;
        if (action.payload.deleted) {
          const { [voiceId]: deletedRecord, ...restDeletingRecords } = state.deletingRecords;
          state.deletingRecords = restDeletingRecords;
        }
        state.error = null;
      })
      .addCase(DeleteVoiceMessageList.rejected, (state, action) => {
        state.submitting = false;
        state.error = action.error.message || 'Unknown error in deleting Notification Template';
      })
      // -----Send To Parents List----- //
      .addCase(voiceMessageSendToParents.pending, (state) => {
        state.submitting = true;
        state.voiceList.error = null;
      })
      .addCase(voiceMessageSendToParents.fulfilled, (state) => {
        state.submitting = false;
        state.voiceList.error = null;
      })
      .addCase(voiceMessageSendToParents.rejected, (state, action) => {
        state.submitting = false;
        state.voiceList.error = action.error.message || 'Unknown error in sending voice message to Parents List';
      })
      // -----Send To Staff List----- //
      .addCase(voiceMessageSendToStaffList.pending, (state) => {
        state.submitting = true;
        state.voiceList.error = null;
      })
      .addCase(voiceMessageSendToStaffList.fulfilled, (state) => {
        state.submitting = false;
        state.voiceList.error = null;
      })
      .addCase(voiceMessageSendToStaffList.rejected, (state, action) => {
        state.submitting = false;
        state.voiceList.error = action.error.message || 'Unknown error in sending voice message to Staff List';
      })
      // -----Send To ClassDivision List----- //
      .addCase(voiceMessageSendToPtaList.pending, (state) => {
        state.submitting = true;
        state.voiceList.error = null;
      })
      .addCase(voiceMessageSendToPtaList.fulfilled, (state) => {
        state.submitting = false;
        state.voiceList.error = null;
      })
      .addCase(voiceMessageSendToPtaList.rejected, (state, action) => {
        state.submitting = false;
        state.voiceList.error = action.error.message || 'Unknown error in sending Notification to Class Division';
      })
      // -----Send To Conveyor List----- //
      .addCase(voiceMessageSendToConveyorList.pending, (state) => {
        state.submitting = true;
        state.voiceList.error = null;
      })
      .addCase(voiceMessageSendToConveyorList.fulfilled, (state) => {
        state.submitting = false;
        state.voiceList.error = null;
      })
      .addCase(voiceMessageSendToConveyorList.rejected, (state, action) => {
        state.submitting = false;
        state.voiceList.error = action.error.message || 'Unknown error in sending Message to Conveyor List';
      })
      // ------Group List------- //
      .addCase(voiceMessageSendToGroupMembersList.pending, (state) => {
        state.submitting = true;
        state.voiceList.error = null;
      })
      .addCase(voiceMessageSendToGroupMembersList.fulfilled, (state) => {
        state.submitting = false;
        state.voiceList.error = null;
      })
      .addCase(voiceMessageSendToGroupMembersList.rejected, (state, action) => {
        state.submitting = false;
        state.voiceList.error = action.error.message || 'Unknown error in sending Message to Group Members List';
      })
      // / -----Send To Public Group Members List----- //
      .addCase(voiceMessageSendToPublicGroupMembersList.pending, (state) => {
        state.submitting = true;
        state.voiceList.error = null;
      })
      .addCase(voiceMessageSendToPublicGroupMembersList.fulfilled, (state) => {
        state.submitting = false;
        state.voiceList.error = null;
      })
      .addCase(voiceMessageSendToPublicGroupMembersList.rejected, (state, action) => {
        state.submitting = false;
        state.voiceList.error = action.error.message || 'Unknown error in sending Message to Public Group Members List';
      })
      // // -----Send To ClassDivision List----- //
      .addCase(voiceMessageSendToClassDivision.pending, (state) => {
        state.submitting = true;
        state.voiceList.error = null;
      })
      .addCase(voiceMessageSendToClassDivision.fulfilled, (state) => {
        state.submitting = false;
        state.voiceList.error = null;
      })
      .addCase(voiceMessageSendToClassDivision.rejected, (state, action) => {
        state.submitting = false;
        state.voiceList.error = action.error.message || 'Unknown error in sending Message to Class Division';
      })
      //   // -----Send To Class Section List----- //
      .addCase(voiceMessageSendToClassSection.pending, (state) => {
        state.submitting = true;
        state.voiceList.error = null;
      })
      .addCase(voiceMessageSendToClassSection.fulfilled, (state) => {
        state.submitting = false;
        state.voiceList.error = null;
      })
      .addCase(voiceMessageSendToClassSection.rejected, (state, action) => {
        state.submitting = false;
        state.voiceList.error = action.error.message || 'Unknown error in sending Notification to Class Section';
      })
      //   // -----Send To Group Wise List----- //
      .addCase(voiceMessageSendToGroupWise.pending, (state) => {
        state.submitting = true;
        state.voiceList.error = null;
      })
      .addCase(voiceMessageSendToGroupWise.fulfilled, (state) => {
        state.submitting = false;
        state.voiceList.error = null;
      })
      .addCase(voiceMessageSendToGroupWise.rejected, (state, action) => {
        state.submitting = false;
        state.voiceList.error = action.error.message || 'Unknown error in sending Notification to Class Section';
      })
      //   // -----Send To Public Group Wise List----- //
      .addCase(voiceMessageSendToPublicGroupWise.pending, (state) => {
        state.submitting = true;
        state.voiceList.error = null;
      })
      .addCase(voiceMessageSendToPublicGroupWise.fulfilled, (state) => {
        state.submitting = false;
        state.voiceList.error = null;
      })
      .addCase(voiceMessageSendToPublicGroupWise.rejected, (state, action) => {
        state.submitting = false;
        state.voiceList.error = action.error.message || 'Unknown error in sending Notification to Class Section';
      })
      // -----Send To All Parent----- //
      .addCase(voiceMessageSendToAllParents.pending, (state) => {
        state.submitting = true;
        state.voiceList.error = null;
      })
      .addCase(voiceMessageSendToAllParents.fulfilled, (state) => {
        state.submitting = false;
        state.voiceList.error = null;
      })
      .addCase(voiceMessageSendToAllParents.rejected, (state, action) => {
        state.submitting = false;
        state.voiceList.error = action.error.message || 'Unknown error in sending Message to Group Members List';
      })
      // -----Send To All Staff----- //
      .addCase(voiceMessageSendToAllStaff.pending, (state) => {
        state.submitting = true;
        state.voiceList.error = null;
      })
      .addCase(voiceMessageSendToAllStaff.fulfilled, (state) => {
        state.submitting = false;
        state.voiceList.error = null;
      })
      .addCase(voiceMessageSendToAllStaff.rejected, (state, action) => {
        state.submitting = false;
        state.voiceList.error = action.error.message || 'Unknown error in sending Message to Group Members List';
      })
      // -----Send To All Pta----- //
      .addCase(voiceMessageSendToAllPta.pending, (state) => {
        state.submitting = true;
        state.voiceList.error = null;
      })
      .addCase(voiceMessageSendToAllPta.fulfilled, (state) => {
        state.submitting = false;
        state.voiceList.error = null;
      })
      .addCase(voiceMessageSendToAllPta.rejected, (state, action) => {
        state.submitting = false;
        state.voiceList.error = action.error.message || 'Unknown error in sending Message to Group Members List';
      })
      // -----Send To All Conveyors----- //
      .addCase(voiceMessageSendToAllConveyors.pending, (state) => {
        state.submitting = true;
        state.voiceList.error = null;
      })
      .addCase(voiceMessageSendToAllConveyors.fulfilled, (state) => {
        state.submitting = false;
        state.voiceList.error = null;
      })
      .addCase(voiceMessageSendToAllConveyors.rejected, (state, action) => {
        state.submitting = false;
        state.voiceList.error = action.error.message || 'Unknown error in sending Message to Group Members List';
      })
      .addCase(flushStore, () => initialState);
  },
});

export const { setVoiceList } = voiceMessageSlice.actions;

export default voiceMessageSlice.reducer;
