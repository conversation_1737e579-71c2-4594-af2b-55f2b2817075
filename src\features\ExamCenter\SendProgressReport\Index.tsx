import React, { useState } from 'react';
import StudentWise from './StudentWise';
import SendProgressReport from './SendToParents';

function SendProgressReportIndex() {
  const [view, setView] = useState('SendProgressReport');

  const handleViewChange = (newView: string) => {
    setView(newView);
  };

  const renderView = () => {
    switch (view) {
      case 'SendProgressReport':
        return <SendProgressReport onClickStudentWise={() => handleViewChange('StudentWise')} />;
      case 'StudentWise':
        return <StudentWise onClickSendProgressReport={() => handleViewChange('SendProgressReport')} />;
      default:
        return null;
    }
  };

  return renderView();
}

export default SendProgressReportIndex;
