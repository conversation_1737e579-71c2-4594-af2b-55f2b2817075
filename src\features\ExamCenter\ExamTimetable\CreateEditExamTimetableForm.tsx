import { STATUS_OPTIONS } from '@/config/Selection';
import { ExamTimetableInfo } from '@/types/ExamCenter';
import LoadingButton from '@mui/lab/LoadingButton/LoadingButton';
import { Button, TextField, Typography, Box, Stack, MenuItem, Select, FormControl } from '@mui/material';
import { useFormik } from 'formik';
import * as Yup from 'yup';

export type CreateEditExamTimetableFormProps = {
  onSave: (values: ExamTimetableInfo, mode: 'create' | 'edit') => void;
  onCancel: (mode: 'create' | 'edit') => void;
  onClose: (mode: 'create' | 'edit') => void;
  examTimetableDetail: ExamTimetableInfo;
  isSubmitting: boolean;
};

const CreateEditExamTimetableValidationSchema = Yup.object({
  accademicYear: Yup.string().required('Please enter Year'),
  className: Yup.string().required('Please enter Class'),
  examName: Yup.string().required('Please enter Exam Name'),
  examSubject: Yup.string().required('Please enter Subject'),
  examDate: Yup.string().required('Please select Exam'),
  examTime: Yup.string().required('Please enter Date'),
  status: Yup.number().oneOf([0, 1], 'Please select Status'),
});

function CreateEditExamTimetableForm({
  examTimetableDetail,
  onSave,
  onCancel,
  isSubmitting,
}: CreateEditExamTimetableFormProps) {
  const mode = examTimetableDetail.examId === 0 ? 'create' : 'edit';
  const {
    values: { accademicYear, className, examSubject, examName, examDate, examTime, status },
    handleChange,
    handleSubmit,
    touched,
    errors,
  } = useFormik<ExamTimetableInfo>({
    initialValues: {
      examId: examTimetableDetail.examId,
      accademicYear: examTimetableDetail.accademicYear,
      examName: examTimetableDetail.examName,
      className: examTimetableDetail.className,
      examSubject: examTimetableDetail.examSubject,
      examDate: examTimetableDetail.examDate,
      examTime: examTimetableDetail.examTime,
      status: examTimetableDetail.status,
    },
    validationSchema: CreateEditExamTimetableValidationSchema,
    onSubmit: (values) => {
      onSave(values, mode);
    },
  });

  return (
    <Box mt={3}>
      <form noValidate onSubmit={handleSubmit}>
        <Stack
          sx={{
            height: 'calc(100vh - 150px)',
            overflow: 'scroll',
            '&::-webkit-scrollbar': {
              width: 0,
            },
          }}
          direction="column"
        >
          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Accademic Year
            </Typography>
            <TextField
              placeholder="Select year"
              name="accademicYear"
              value={accademicYear}
              onChange={handleChange}
              error={touched.accademicYear && !!errors.accademicYear}
              helperText={errors.accademicYear}
              disabled={isSubmitting}
            />
          </FormControl>
          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Class
            </Typography>
            <TextField
              placeholder="Select class"
              name="className"
              value={className}
              onChange={handleChange}
              error={touched.className && !!errors.className}
              helperText={errors.className}
              disabled={isSubmitting}
            />
          </FormControl>
          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Subject
            </Typography>
            <TextField
              placeholder="Select subject"
              name="examSubject"
              value={examSubject}
              onChange={handleChange}
              error={touched.examSubject && !!errors.examSubject}
              helperText={errors.examSubject}
              disabled={isSubmitting}
            />
          </FormControl>
          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Exam Name
            </Typography>
            <TextField
              placeholder="Select exam"
              name="examName"
              value={examName}
              onChange={handleChange}
              error={touched.examName && !!errors.examName}
              helperText={errors.examName}
              disabled={isSubmitting}
            />
          </FormControl>
          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Exam Date
            </Typography>
            <TextField
              placeholder="Select date"
              name="examDate"
              value={examDate}
              onChange={handleChange}
              error={touched.examDate && !!errors.examDate}
              helperText={errors.examDate}
              disabled={isSubmitting}
            />
          </FormControl>
          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Exam Time
            </Typography>
            <TextField
              placeholder="Enter time"
              name="examTime"
              value={examTime}
              onChange={handleChange}
              error={touched.examTime && !!errors.examTime}
              helperText={errors.examTime}
              disabled={isSubmitting}
            />
          </FormControl>
          <FormControl>
            <Typography mt={2} variant="subtitle1" fontSize={12}>
              Status
            </Typography>
            <Select
              name="status"
              value={status}
              onChange={handleChange}
              error={touched.status && !!errors.status}
              disabled={isSubmitting}
            >
              {STATUS_OPTIONS.map((opt) => (
                <MenuItem key={opt.id} value={opt.id}>
                  {opt.name}
                </MenuItem>
              ))}
            </Select>
            {touched.status && !!errors.status && (
              <Typography color="red" fontSize="0.625rem" variant="subtitle1">
                {errors.status}
              </Typography>
            )}
          </FormControl>
        </Stack>
        <Box sx={{ justifyContent: 'center', pt: 3, position: 'relative' }}>
          <Stack spacing={2} direction="row" sx={{}}>
            <Button onClick={() => onCancel(mode)} fullWidth variant="contained" color="secondary" type="button">
              Cancel
            </Button>
            <LoadingButton
              loading={isSubmitting}
              fullWidth
              variant="contained"
              color="primary"
              type="submit"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Saving...' : 'Save'}
            </LoadingButton>
          </Stack>
        </Box>
      </form>
    </Box>
  );
}

export default CreateEditExamTimetableForm;
