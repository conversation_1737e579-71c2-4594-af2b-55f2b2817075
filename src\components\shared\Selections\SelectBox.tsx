import * as React from 'react';
import MenuItem from '@mui/material/MenuItem';
import FormControl from '@mui/material/FormControl';
import Select from '@mui/material/Select';
import { useTheme } from 'styled-components';
import { getClassList } from '@/config/storeSelectors';
import { useAppSelector } from '@/hooks/useAppSelector';

export default function SelectBox({ Selection_Options, placeholder, handleChange, options }: any) {
  const theme = useTheme();

  // const handleChange = (event: SelectChangeEvent) => {
  //   setOptions(event.target.value);
  // };
  return (
    <FormControl sx={{ m: 1 }} size="small">
      <Select
        sx={{ backgroundColor: theme.palette.grey[100], color: theme.palette.primary.main }}
        value={options}
        onChange={handleChange}
        displayEmpty
        labelId="demo-dialog-select-label"
        id="demo-dialog-select"
        inputProps={{ 'aria-label': 'Without label' }}
      >
        <MenuItem value="" className="d-none">
          {placeholder}
        </MenuItem>
        {Selection_Options?.map((item: any) => (
          <MenuItem key={item} value={item.id}>
            {item}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
}
