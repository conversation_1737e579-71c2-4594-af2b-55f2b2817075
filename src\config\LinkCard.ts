/* eslint-disable prettier/prettier */
import { LinkCardItem } from '@/types/Dashboard';
import attendance from '@/assets/linkcard/attendance.svg';
import messagebox from '@/assets/linkcard/message.svg';
import voice from '@/assets/linkcard/voice.svg';
import notification from '@/assets/linkcard/notification.svg';
import manage from '@/assets/linkcard/manage.svg';
import track from '@/assets/linkcard/track.svg';
import calendar from '@/Parent-Side/assets/linkcard/Calendar.svg';
import gallery from '@/Parent-Side/assets/linkcard/Gallery.svg';
import library from '@/Parent-Side/assets/linkcard/Library.svg';
import track2 from '@/Parent-Side/assets/linkcard/track.svg';
import onlineVideo from '@/Parent-Side/assets/linkcard/Onlinevideo.svg';
import material from '@/Parent-Side/assets/linkcard/Material.svg';
import objectiveExam from '@/Parent-Side/assets/linkcard/ObjectiveExam.svg';
import descriptiveExam from '@/Parent-Side/assets/linkcard/DescriptiveExam.svg';

const Attendancemarking = ' linear-gradient(90deg, hsla(271, 91%, 65%, 1) 0%, hsla(243, 75%, 59%, 1) 100%);';
const VoiceMessage = 'linear-gradient(90deg, hsla(330, 81%, 60%, 1) 0%, hsla(350, 89%, 60%, 1) 100%)';
const Track = 'linear-gradient(90deg, hsla(199, 89%, 48%, 1) 0%, hsla(200, 98%, 39%, 1) 100%)';
const Notification = ' linear-gradient(90deg, hsla(142, 71%, 45%, 1) 0%, hsla(142, 76%, 36%, 1) 100%)';
const ManageFee = 'linear-gradient(90deg, hsla(330, 80%, 50%, 1) 0%, hsla(350, 88%, 50%, 1) 100%)';
const MessageBox = ' linear-gradient(90deg, hsla(27, 96%, 61%, 1) 36%, hsla(25, 95%, 53%, 1) 82%);';

const Calendar = 'linear-gradient(180deg, hsla(271, 91%, 90%, 1) 0%, hsla(271, 91%, 90%, 1) 100%);';
const Library = 'linear-gradient(180deg, hsla(350, 89%, 90%, 1) 0%, hsla(350, 89%, 90%, 1) 100%)';
const Gallery = 'linear-gradient(180deg, hsla(243, 89%, 90%, 1) 0%, hsla(243, 89%, 90%, 1) 100%)';
const Track2 = ' linear-gradient(180deg, hsla(142, 81%, 80%, 1) 0%, hsla(142, 81%, 80%, 1) 100%)';
// const Attendancemarking = ' linear-gradient(180deg, hsla(271, 91%, 80%, 1) 0%, hsla(271, 91%, 70%, 1) 100%);';
// const VoiceMessage = 'linear-gradient(180deg, hsla(350, 89%, 70%, 1) 0%, hsla(350, 89%, 60%, 1) 100%)';
// const Track = 'linear-gradient(180deg, hsla(243, 89%, 78%, 1) 0%, hsla(243, 89%, 68%, 1) 100%)';
// const Notification = ' linear-gradient(180deg, hsla(142, 81%, 45%, 1) 0%, hsla(142, 71%, 45%, 1) 100%)';
// const ManageFee = 'linear-gradient(180deg, hsla(330, 80%, 70%, 1) 0%, hsla(330, 80%, 60%, 1) 100%)';
// const MessageBox = ' linear-gradient(180deg, hsla(27, 96%, 71%, 1) 36%, hsla(27, 96%, 61%, 1) 82%);';

export const LinkCardData: LinkCardItem[] = [
  {
    id: 'attendance',
    label: 'Attendance',
    icon: attendance,
    link: '/attendance-marking/class-marking',
    color: Attendancemarking,
  },
  { id: 'messagebox', label: 'Message', icon: messagebox, link: '/message-box/quick-send', color: MessageBox },
  { id: 'voiceMessage', label: 'Voice', icon: voice, link: '/voice-message/voice-template', color: VoiceMessage },
  {
    id: 'notification',
    label: 'Notification',
    icon: notification,
    link: '/app-notification/list',
    color: Notification,
  },
  { id: 'manageFee', label: 'Manage Fee', icon: manage, link: '/manage-fee/overview', color: ManageFee },
  { id: 'track', label: 'Track', icon: track, link: '/vehicle-tracking/list', color: Track },
];

export const ParentLinkCardData: LinkCardItem[] = [
  { id: 'calendar', label: 'Calendar', icon: calendar, link: '/parent', color: Calendar },
  { id: 'gallery', label: 'Gallery', icon: gallery, link: '/parent', color: Gallery },
  { id: 'library', label: 'Library', icon: library, link: '/parent', color: Library },
  { id: 'track', label: 'Track', icon: track2, link: '/parent', color: Track2 },
];

export const ParentLinkCard2Data: LinkCardItem[] = [
  { id: 'onlineVideo', label: 'Online Video', icon: onlineVideo, link: '/parent', count: '12' },
  { id: 'material', label: 'Material', icon: material, link: '/parent', count: '22' },
  { id: 'objectiveExam', label: 'Objective Exam', icon: objectiveExam, link: '/parent', count: '8' },
  { id: 'descriptiveExam', label: 'DescriptiveExam', icon: descriptiveExam, link: '/parent', count: '5' },
];