import { PayloadAction, createSlice } from '@reduxjs/toolkit';
import { MessageBoxState, MessageTempDataType } from '@/types/MessageBox';
import {
  DeleteMessageTemplate,
  EditMessageTemplate,
  createMessageTempList,
  fetchStaffList,
  fetchMessageTempList,
  fetchParentsList,
  messageSendToParents,
  messageSendToParentsList,
  messageSendToStaffList,
  fetchPtaList,
  messageSendToPtaList,
  fetchConveyorList,
  messageSendToConveyorList,
  fetchGroupsList,
  fetchGroupMembersList,
  messageSendToGroupMembersList,
  fetchClassDivision,
  messageSendToClassDivision,
  messageSendToClassSection,
  fetchClassSection,
  fetchGroupWise,
  messageSendToGroupWise,
  fetchPublicGroupsList,
  fetchPublicGroupMembersList,
  messageSendToPublicGroupMembersList,
  fetchPublicGroupWiseList,
  messageSendToPublicGroupWiseList,
  messageSendToAllParents,
  messageSendToAllStaff,
  messageSendToAllPta,
  messageSendToAllConveyors,
  fetchMessageTemplate,
  fileUpload,
} from './messageBox.thunks';
import { flushStore } from '../flush.slice';

const initialState: MessageBoxState = {
  messageTempList: {
    data: [],
    status: 'idle',
    error: null,
  },
  messageTemplate: {
    data: {},
    status: 'idle',
    error: null,
  },
  parentsList: {
    data: [],
    status: 'idle',
    error: null,
  },
  staffList: {
    data: [],
    status: 'idle',
    error: null,
  },
  ptaList: {
    data: [],
    status: 'idle',
    error: null,
  },
  conveyorList: {
    data: [],
    status: 'idle',
    error: null,
  },
  groupList: {
    data: [],
    status: 'idle',
    error: null,
  },
  groupMembersList: {
    data: [],
    status: 'idle',
    error: null,
  },
  classDivisionList: {
    data: [],
    status: 'idle',
    error: null,
  },
  classSectionList: {
    data: [],
    status: 'idle',
    error: null,
  },
  groupWiseList: {
    data: [],
    status: 'idle',
    error: null,
  },
  publicGroupList: {
    data: [],
    status: 'idle',
    error: null,
  },
  publicGroupMembersList: {
    data: [],
    status: 'idle',
    error: null,
  },
  publicGroupWiseList: {
    data: [],
    status: 'idle',
    error: null,
  },
  filesUploadList: {
    data: [],
    status: 'idle',
    error: null,
    progress: 0,
  },
  submitting: false,
  deletingRecords: {},
  error: null,
};

export const messageBoxSlice = createSlice({
  name: 'messageBox',
  initialState,
  reducers: {
    setmessageTempList: (state, action: PayloadAction<MessageTempDataType[]>) => {
      state.messageTempList.data = action.payload;
    },
    setSubmitting: (state, action: PayloadAction<boolean>) => {
      state.submitting = action.payload;
    },
    setProgress: (state, action: PayloadAction<number>) => {
      state.filesUploadList.progress = action.payload;
    },
  },
  extraReducers(builder) {
    builder
      // extraReducers for fetching messageTempList
      // ------Get------- //
      .addCase(fetchMessageTempList.pending, (state) => {
        state.messageTempList.status = 'loading';
        state.messageTempList.error = null;
      })
      .addCase(fetchMessageTempList.fulfilled, (state, action) => {
        state.messageTempList.status = 'success';
        state.messageTempList.data = action.payload;
        state.messageTempList.error = null;
      })
      .addCase(fetchMessageTempList.rejected, (state, action) => {
        state.messageTempList.status = 'error';
        state.messageTempList.error = action.payload || 'Unknown error in fetching Message Template';
      })
      // ------Create------- //
      .addCase(createMessageTempList.pending, (state) => {
        state.submitting = true;
        state.messageTempList.error = null;
      })
      .addCase(createMessageTempList.fulfilled, (state) => {
        state.submitting = false;
        state.messageTempList.error = null;
      })
      .addCase(createMessageTempList.rejected, (state, action) => {
        state.submitting = false;
        state.messageTempList.error = action.error.message || 'Unknown error in fetching Message Template';
      })
      // ------Update------- //
      .addCase(EditMessageTemplate.pending, (state) => {
        state.submitting = true;
        state.error = null;
      })
      .addCase(EditMessageTemplate.fulfilled, (state, action) => {
        state.submitting = false;
        state.error = null;
        const dataIndex = state.messageTempList.data.findIndex((x) => x.messageId === action.meta.arg.messageId);
        if (dataIndex > -1) {
          state.messageTempList.data[dataIndex] = action.meta.arg;
        }
      })
      .addCase(EditMessageTemplate.rejected, (state, action) => {
        state.submitting = false;
        state.error = action.error.message || 'Unknown error in fetching Message Template';
      })
      // ------Delete------- //
      .addCase(DeleteMessageTemplate.pending, (state, action) => {
        state.deletingRecords[action.meta.arg] = true;
        state.error = null;
      })
      .addCase(DeleteMessageTemplate.fulfilled, (state, action) => {
        if (action.payload.deleted) {
          const { [action.meta.arg]: deletedRecord, ...restDeletingRecords } = state.deletingRecords;
          state.deletingRecords = restDeletingRecords;
        }
        state.error = null;
      })
      .addCase(DeleteMessageTemplate.rejected, (state, action) => {
        state.submitting = false;
        state.error = action.error.message || 'Unknown error in deleting Message Template';
      })
      // // ------Delete Multiple------- //
      // .addCase(DeleteMultipleMessageTemplate.pending, (state, action) => {
      //   state.deletingRecords[action.meta.arg] = true;
      //   state.error = null;
      // })
      // .addCase(DeleteMultipleMessageTemplate.fulfilled, (state, action) => {
      //   if (action.payload.deleted) {
      //     const { [action.meta.arg]: deletedRecord, ...restDeletingRecords } = state.deletingRecords;
      //     state.deletingRecords = restDeletingRecords;
      //   }
      //   state.error = null;
      // })
      // .addCase(DeleteMultipleMessageTemplate.rejected, (state, action) => {
      //   state.submitting = false;
      //   state.error = action.error.message || 'Unknown error in Multiple deleting Message Template';
      // })

      .addCase(fetchMessageTemplate.pending, (state) => {
        state.messageTemplate.status = 'loading';
        state.messageTemplate.error = null;
      })
      .addCase(fetchMessageTemplate.fulfilled, (state, action) => {
        state.messageTemplate.status = 'success';
        state.messageTemplate.data = action.payload;
        state.messageTemplate.error = null;
      })
      .addCase(fetchMessageTemplate.rejected, (state, action) => {
        state.messageTemplate.status = 'error';
        state.messageTemplate.error = action.payload || 'Unknown error in fetching Message';
      })
      // ------Parents List------- //
      .addCase(fetchParentsList.pending, (state) => {
        state.parentsList.status = 'loading';
        state.parentsList.error = null;
      })
      .addCase(fetchParentsList.fulfilled, (state, action) => {
        state.parentsList.status = 'success';
        state.parentsList.data = action.payload;
        state.parentsList.error = null;
      })
      .addCase(fetchParentsList.rejected, (state, action) => {
        state.parentsList.status = 'error';
        state.parentsList.error = action.payload || 'Unknown error in fetching Parents List';
      })
      // -----Send To Parents----- //
      .addCase(messageSendToParents.pending, (state) => {
        state.submitting = true;
        state.messageTempList.error = null;
      })
      .addCase(messageSendToParents.fulfilled, (state) => {
        state.submitting = false;
        state.messageTempList.error = null;
      })
      .addCase(messageSendToParents.rejected, (state, action) => {
        state.submitting = false;
        state.messageTempList.error = action.error.message || 'Unknown error in sending Message to Parents';
      })
      // -----Send To Parents List----- //
      .addCase(messageSendToParentsList.pending, (state) => {
        state.submitting = true;
        state.messageTempList.error = null;
      })
      .addCase(messageSendToParentsList.fulfilled, (state) => {
        state.submitting = false;
        state.messageTempList.error = null;
      })
      .addCase(messageSendToParentsList.rejected, (state, action) => {
        state.submitting = false;
        state.messageTempList.error = action.error.message || 'Unknown error in sending Message to Parents List';
      })

      // ------Staff List------- //
      .addCase(fetchStaffList.pending, (state) => {
        state.staffList.status = 'loading';
        state.staffList.error = null;
      })
      .addCase(fetchStaffList.fulfilled, (state, action) => {
        state.staffList.status = 'success';
        state.staffList.data = action.payload;
        state.staffList.error = null;
      })
      .addCase(fetchStaffList.rejected, (state, action) => {
        state.staffList.status = 'error';
        state.staffList.error = action.payload || 'Unknown error in fetching staff List';
      })
      // -----Send To Staff List----- //
      .addCase(messageSendToStaffList.pending, (state) => {
        state.submitting = true;
        state.messageTempList.error = null;
      })
      .addCase(messageSendToStaffList.fulfilled, (state) => {
        state.submitting = false;
        state.messageTempList.error = null;
      })
      .addCase(messageSendToStaffList.rejected, (state, action) => {
        state.submitting = false;
        state.messageTempList.error = action.error.message || 'Unknown error in sending Message to Staff List';
      })

      // ------Pta List------- //
      .addCase(fetchPtaList.pending, (state) => {
        state.ptaList.status = 'loading';
        state.ptaList.error = null;
      })
      .addCase(fetchPtaList.fulfilled, (state, action) => {
        state.ptaList.status = 'success';
        state.ptaList.data = action.payload;
        state.ptaList.error = null;
      })
      .addCase(fetchPtaList.rejected, (state, action) => {
        state.ptaList.status = 'error';
        state.ptaList.error = action.payload || 'Unknown error in fetching Pta List';
      })
      // -----Send To Pta List----- //
      .addCase(messageSendToPtaList.pending, (state) => {
        state.submitting = true;
        state.messageTempList.error = null;
      })
      .addCase(messageSendToPtaList.fulfilled, (state) => {
        state.submitting = false;
        state.messageTempList.error = null;
      })
      .addCase(messageSendToPtaList.rejected, (state, action) => {
        state.submitting = false;
        state.messageTempList.error = action.error.message || 'Unknown error in sending Message to Pta List';
      })

      // ------Conveyor List------- //
      .addCase(fetchConveyorList.pending, (state) => {
        state.conveyorList.status = 'loading';
        state.conveyorList.error = null;
      })
      .addCase(fetchConveyorList.fulfilled, (state, action) => {
        state.conveyorList.status = 'success';
        state.conveyorList.data = action.payload;
        state.conveyorList.error = null;
      })
      .addCase(fetchConveyorList.rejected, (state, action) => {
        state.conveyorList.status = 'error';
        state.conveyorList.error = action.payload || 'Unknown error in fetching Conveyor List';
      })
      // -----Send To Conveyor List----- //
      .addCase(messageSendToConveyorList.pending, (state) => {
        state.submitting = true;
        state.messageTempList.error = null;
      })
      .addCase(messageSendToConveyorList.fulfilled, (state) => {
        state.submitting = false;
        state.messageTempList.error = null;
      })
      .addCase(messageSendToConveyorList.rejected, (state, action) => {
        state.submitting = false;
        state.messageTempList.error = action.error.message || 'Unknown error in sending Message to Conveyor List';
      })

      // ------Group List------- //
      .addCase(fetchGroupsList.pending, (state) => {
        state.groupList.status = 'loading';
        state.groupList.error = null;
      })
      .addCase(fetchGroupsList.fulfilled, (state, action) => {
        state.groupList.status = 'success';
        state.groupList.data = action.payload;
        state.groupList.error = null;
      })
      .addCase(fetchGroupsList.rejected, (state, action) => {
        state.groupList.status = 'error';
        state.groupList.error = action.payload || 'Unknown error in fetching Group List';
      })
      // ------Group Members List------- //
      .addCase(fetchGroupMembersList.pending, (state) => {
        state.groupMembersList.status = 'loading';
        state.groupMembersList.error = null;
      })
      .addCase(fetchGroupMembersList.fulfilled, (state, action) => {
        state.groupMembersList.status = 'success';
        state.groupMembersList.data = action.payload;
        state.groupMembersList.error = null;
      })
      .addCase(fetchGroupMembersList.rejected, (state, action) => {
        state.groupMembersList.status = 'error';
        state.groupMembersList.error = action.payload || 'Unknown error in fetching Group Members List';
      })
      // -----Send To Group Members List----- //
      .addCase(messageSendToGroupMembersList.pending, (state) => {
        state.submitting = true;
        state.messageTempList.error = null;
      })
      .addCase(messageSendToGroupMembersList.fulfilled, (state) => {
        state.submitting = false;
        state.messageTempList.error = null;
      })
      .addCase(messageSendToGroupMembersList.rejected, (state, action) => {
        state.submitting = false;
        state.messageTempList.error = action.error.message || 'Unknown error in sending Message to Group Members List';
      })

      // ------ClassDivision List------- //
      .addCase(fetchClassDivision.pending, (state) => {
        state.classDivisionList.status = 'loading';
        state.classDivisionList.error = null;
      })
      .addCase(fetchClassDivision.fulfilled, (state, action) => {
        state.classDivisionList.status = 'success';
        state.classDivisionList.data = action.payload;
        state.classDivisionList.error = null;
      })
      .addCase(fetchClassDivision.rejected, (state, action) => {
        state.classDivisionList.status = 'error';
        state.classDivisionList.error = action.payload || 'Unknown error in fetching ClassDivision List';
      })
      // -----Send To ClassDivision List----- //
      .addCase(messageSendToClassDivision.pending, (state) => {
        state.submitting = true;
        state.messageTempList.error = null;
      })
      .addCase(messageSendToClassDivision.fulfilled, (state) => {
        state.submitting = false;
        state.messageTempList.error = null;
      })
      .addCase(messageSendToClassDivision.rejected, (state, action) => {
        state.submitting = false;
        state.messageTempList.error = action.error.message || 'Unknown error in sending Message to Class Division';
      })

      // ------ClassSection List------- //
      .addCase(fetchClassSection.pending, (state) => {
        state.classSectionList.status = 'loading';
        state.classSectionList.error = null;
      })
      .addCase(fetchClassSection.fulfilled, (state, action) => {
        state.classSectionList.status = 'success';
        state.classSectionList.data = action.payload;
        state.classSectionList.error = null;
      })
      .addCase(fetchClassSection.rejected, (state, action) => {
        state.classSectionList.status = 'error';
        state.classSectionList.error = action.payload || 'Unknown error in fetching ClassSection List';
      })
      // -----Send To ClassSection List----- //
      .addCase(messageSendToClassSection.pending, (state) => {
        state.submitting = true;
        state.messageTempList.error = null;
      })
      .addCase(messageSendToClassSection.fulfilled, (state) => {
        state.submitting = false;
        state.messageTempList.error = null;
      })
      .addCase(messageSendToClassSection.rejected, (state, action) => {
        state.submitting = false;
        state.messageTempList.error = action.error.message || 'Unknown error in sending Message to Class Section';
      })

      // ------ GroupWise List------- //
      .addCase(fetchGroupWise.pending, (state) => {
        state.groupWiseList.status = 'loading';
        state.groupWiseList.error = null;
      })
      .addCase(fetchGroupWise.fulfilled, (state, action) => {
        state.groupWiseList.status = 'success';
        state.groupWiseList.data = action.payload;
        state.groupWiseList.error = null;
      })
      .addCase(fetchGroupWise.rejected, (state, action) => {
        state.groupWiseList.status = 'error';
        state.groupWiseList.error = action.payload || 'Unknown error in fetching ClassSection List';
      })

      // -----Send To Group Wise List ----- //
      .addCase(messageSendToGroupWise.pending, (state) => {
        state.submitting = true;
        state.messageTempList.error = null;
      })
      .addCase(messageSendToGroupWise.fulfilled, (state) => {
        state.submitting = false;
        state.messageTempList.error = null;
      })
      .addCase(messageSendToGroupWise.rejected, (state, action) => {
        state.submitting = false;
        state.messageTempList.error = action.error.message || 'Unknown error in sending Message to Class Section';
      })
      // ------ Public Group List ------- //
      .addCase(fetchPublicGroupsList.pending, (state) => {
        state.publicGroupList.status = 'loading';
        state.publicGroupList.error = null;
      })
      .addCase(fetchPublicGroupsList.fulfilled, (state, action) => {
        state.publicGroupList.status = 'success';
        state.publicGroupList.data = action.payload;
        state.publicGroupList.error = null;
      })
      .addCase(fetchPublicGroupsList.rejected, (state, action) => {
        state.publicGroupList.status = 'error';
        state.publicGroupList.error = action.payload || 'Unknown error in fetching Group List';
      })
      // ------ Public Group Members List------- //
      .addCase(fetchPublicGroupMembersList.pending, (state) => {
        state.publicGroupMembersList.status = 'loading';
        state.publicGroupMembersList.error = null;
      })
      .addCase(fetchPublicGroupMembersList.fulfilled, (state, action) => {
        state.publicGroupMembersList.status = 'success';
        state.publicGroupMembersList.data = action.payload;
        state.publicGroupMembersList.error = null;
      })
      .addCase(fetchPublicGroupMembersList.rejected, (state, action) => {
        state.publicGroupMembersList.status = 'error';
        state.publicGroupMembersList.error = action.payload || 'Unknown error in fetching Group Members List';
      })
      // -----Send To Public Group Members List----- //
      .addCase(messageSendToPublicGroupMembersList.pending, (state) => {
        state.submitting = true;
        state.messageTempList.error = null;
      })
      .addCase(messageSendToPublicGroupMembersList.fulfilled, (state) => {
        state.submitting = false;
        state.messageTempList.error = null;
      })
      .addCase(messageSendToPublicGroupMembersList.rejected, (state, action) => {
        state.submitting = false;
        state.messageTempList.error = action.error.message || 'Unknown error in sending Message to Group Members List';
      })
      // ------ Public Group Wise List------- //
      .addCase(fetchPublicGroupWiseList.pending, (state) => {
        state.publicGroupWiseList.status = 'loading';
        state.publicGroupWiseList.error = null;
      })
      .addCase(fetchPublicGroupWiseList.fulfilled, (state, action) => {
        state.publicGroupWiseList.status = 'success';
        state.publicGroupWiseList.data = action.payload;
        state.publicGroupWiseList.error = null;
      })
      .addCase(fetchPublicGroupWiseList.rejected, (state, action) => {
        state.publicGroupWiseList.status = 'error';
        state.publicGroupWiseList.error = action.payload || 'Unknown error in fetching Group Members List';
      })
      // -----Send To Public Group Wise List----- //
      .addCase(messageSendToPublicGroupWiseList.pending, (state) => {
        state.submitting = true;
        state.messageTempList.error = null;
      })
      .addCase(messageSendToPublicGroupWiseList.fulfilled, (state) => {
        state.submitting = false;
        state.messageTempList.error = null;
      })
      .addCase(messageSendToPublicGroupWiseList.rejected, (state, action) => {
        state.submitting = false;
        state.messageTempList.error = action.error.message || 'Unknown error in sending Message to Group Members List';
      })
      // -----Send To All Parent----- //
      .addCase(messageSendToAllParents.pending, (state) => {
        state.submitting = true;
        state.messageTempList.error = null;
      })
      .addCase(messageSendToAllParents.fulfilled, (state) => {
        state.submitting = false;
        state.messageTempList.error = null;
      })
      .addCase(messageSendToAllParents.rejected, (state, action) => {
        state.submitting = false;
        state.messageTempList.error = action.error.message || 'Unknown error in sending Message to Group Members List';
      })
      // -----Send To All Staff----- //
      .addCase(messageSendToAllStaff.pending, (state) => {
        state.submitting = true;
        state.messageTempList.error = null;
      })
      .addCase(messageSendToAllStaff.fulfilled, (state) => {
        state.submitting = false;
        state.messageTempList.error = null;
      })
      .addCase(messageSendToAllStaff.rejected, (state, action) => {
        state.submitting = false;
        state.messageTempList.error = action.error.message || 'Unknown error in sending Message to Group Members List';
      })
      // -----Send To All Pta----- //
      .addCase(messageSendToAllPta.pending, (state) => {
        state.submitting = true;
        state.messageTempList.error = null;
      })
      .addCase(messageSendToAllPta.fulfilled, (state) => {
        state.submitting = false;
        state.messageTempList.error = null;
      })
      .addCase(messageSendToAllPta.rejected, (state, action) => {
        state.submitting = false;
        state.messageTempList.error = action.error.message || 'Unknown error in sending Message to Group Members List';
      })
      // -----Send To All Conveyors----- //
      .addCase(messageSendToAllConveyors.pending, (state) => {
        state.submitting = true;
        state.messageTempList.error = null;
      })
      .addCase(messageSendToAllConveyors.fulfilled, (state) => {
        state.submitting = false;
        state.messageTempList.error = null;
      })
      .addCase(messageSendToAllConveyors.rejected, (state, action) => {
        state.submitting = false;
        state.messageTempList.error = action.error.message || 'Unknown error in sending Message to Group Members List';
      })
      // ------File Upload------- //
      .addCase(fileUpload.pending, (state) => {
        state.submitting = true;
        state.filesUploadList.error = null;
      })
      .addCase(fileUpload.fulfilled, (state, action) => {
        state.submitting = false;
        state.filesUploadList.data = action.payload;
        state.filesUploadList.error = null;
      })
      .addCase(fileUpload.rejected, (state, action) => {
        state.submitting = false;
        state.filesUploadList.error = action.error.message || 'Unknown error in fetching Message Template';
      })
      .addCase(flushStore, () => initialState);
  },
});

export const { setmessageTempList, setProgress } = messageBoxSlice.actions;

export default messageBoxSlice.reducer;
