import * as React from 'react';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';
import Page from '@/components/shared/Page';
import styled from 'styled-components';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import { Link, useLocation, useOutletContext } from 'react-router-dom';
import ZoomLiveClass from '../features/LiveClass/ZoomLiveClass';
import ZoomScheduleList from '../features/LiveClass/ZoomScheduleList';
import ParentLiveClass from '../features/LiveClass/ParentLiveClass';
import ParentOnlineVideo from '../features/OnlineVideo/ParentOnlineVideo';
import ParentOfflineVideo from '../features/OnlineVideo/ParentOfflineVideo';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const OnlineVideoIndexRoot = styled.div`
  min-height: calc(100vh - ${TOP_BAR_HEIGHT});
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  .tab {
    background-color: ${(props) => (props.theme.themeMode === 'light' ? '#fff' : props.theme.palette.grey[800])};
    @media screen and (min-width: 1414px) {
      width: 100%;
    }
    @media screen and (max-width: 720px) {
      width: 100%;
    }
  }
  .Mui-selected {
    color: ${(props) => props.theme.palette.success.main};
  }
  .MuiTabs-indicator {
    background-color: ${(props) => props.theme.palette.success.main};
  }
`;
function TabPanel({ children, value, index }: TabPanelProps) {
  return (
    <Box
      sx={{ paddingTop: '3rem' }}
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
    >
      {value === index && <Box>{children}</Box>}
    </Box>
  );
}

function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`,
  };
}

export default function OnlineVideoIndex() {
  const { topBarHeight } = useOutletContext<OutletContextType>();
  const location = useLocation();
  const [value, setValue] = React.useState(0);

  React.useEffect(() => {
    const currentPath = location.pathname;
    const routePaths = ['/parent-online-video/online-video', '/parent-online-video/offline-video'];
    const index = routePaths.indexOf(currentPath);
    if (index !== -1) {
      setValue(index);
    }
  }, [location]);

  return (
    <Page title="Manage Fee">
      <OnlineVideoIndexRoot>
        <Tabs
          className="tab"
          variant="scrollable"
          scrollButtons
          allowScrollButtonsMobile
          value={value}
          aria-label="basic tabs"
          sx={{
            ml: '0px',
            borderBottom: 1,
            borderColor: 'divider',
            position: 'fixed',
             top: `${topBarHeight}px`,
            zIndex: 1,
            // width: '100%',
          }}
        >
          <Tab {...a11yProps(0)} label="Online Video" component={Link} to="/parent-online-video/online-video" />
          <Tab {...a11yProps(1)} label="Offline Video" component={Link} to="/parent-online-video/offline-video" />
        </Tabs>
        <TabPanel value={value} index={0}>
          <ParentOnlineVideo />
        </TabPanel>
        <TabPanel value={value} index={1}>
          <ParentOfflineVideo />
        </TabPanel>
      </OnlineVideoIndexRoot>
    </Page>
  );
}
