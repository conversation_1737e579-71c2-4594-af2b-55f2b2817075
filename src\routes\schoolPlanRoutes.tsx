/* eslint-disable prettier/prettier */
import Loadable from '@/components/shared/Loadable';
import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';

const SchoolPlan = Loadable(lazy(() => import('@/pages/SchoolPlan')));
const SessionPlanList = Loadable(lazy(() => import('@/features/SchoolPlan/SessionPlanList/SessionPlanList')));
const LessonPlanList = Loadable(lazy(() => import('@/features/SchoolPlan/LessonPlanList/LessonPlanList')));
const CcList = Loadable(lazy(() => import('@/features/Tc&Cc/CcList/CcList')));

export const schoolPlanRoutes: RouteObject[] = [
  {
    path: '/school-plan',
    element: <SchoolPlan />,
    children: [
      {
        path: 'session-plan-list',
        element: <SessionPlanList />,
      },
      {
        path: 'lesson-plan-list',
        element: <LessonPlanList />,
      },
      {
        path: 'annual-plan-list',
        element: <CcList />,
      },
    ],
  },
];
