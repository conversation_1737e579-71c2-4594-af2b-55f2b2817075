import Loadable from '@/components/shared/Loadable';
import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';

const AttendanceMarking = Loadable(lazy(() => import('@/pages/AttendanceMarking')));
const ClassMarking = Loadable(lazy(() => import('@/features/Attendance-Marking/Classmarking/ClassMarking')));
const DetailedClassMarking = Loadable(lazy(() => import('@/features/Attendance-Marking/DetailedClassMarking')));
const SessionClassMarking = Loadable(lazy(() => import('@/features/Attendance-Marking/SessionDetailedClassMarking')));
const QuickMarking = Loadable(lazy(() => import('@/features/Attendance-Marking/QuickMarking')));
const LeaveNoteList = Loadable(lazy(() => import('@/features/Attendance-Marking/LeaveNote/LeaveNoteList')));
const StudentsAttendance = Loadable(lazy(() => import('@/features/Attendance-Marking/StudentsAttendance')));
const AttendanceSummary = Loadable(lazy(() => import('@/features/Attendance-Marking/AttendanceSummary')));
const AbsenteesList = Loadable(lazy(() => import('@/features/Attendance-Marking/AbsenteesList')));

export const attendanceRoutes: RouteObject[] = [
  {
    path: '/attendance-marking',
    element: <AttendanceMarking />,
    children: [
      {
        path: 'class-marking',
        element: <ClassMarking />,
      },
      {
        path: 'detailed-marking',
        element: <DetailedClassMarking />,
      },
      {
        path: 'session-wise-marking',
        element: <SessionClassMarking />,
      },
      {
        path: 'quick-marking',
        element: <QuickMarking />,
      },
      {
        path: 'leave-note',
        element: <LeaveNoteList />,
      },
      {
        path: 'student-attendance-calendar',
        element: <StudentsAttendance />,
      },
      {
        path: 'attendance-summary',
        element: <AttendanceSummary />,
      },
      {
        path: 'absentees-list',
        element: <AbsenteesList />,
      },
    ],
  },
];
