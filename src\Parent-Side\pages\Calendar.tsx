import Page from '@/components/shared/Page';
import { Card, Divider, Paper, Stack, Typography } from '@mui/material';
import styled from 'styled-components';
import CalendarComponent from '@/components/Calendar/Calendar';

const CalendarRoot = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      flex-grow: 1;

      .card-top {
        background-color: ${(props) =>
          props.theme.themeMode === 'light' ? props.theme.palette.primary.lighter : props.theme.palette.grey[900]};
      }

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

export const data = [];

function Calendar() {
  return (
    <Page title="List">
      <CalendarRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: 2 }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={19}>
              Academic Calendar
            </Typography>
          </Stack>
          <Divider />

          <Paper className="card-main-body" sx={{ my: '5px' }}>
            <CalendarComponent />
          </Paper>
          {/* <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: 2 }, pt: 2 }}>
            <Stack spacing={2} direction="row">
              <Button onClick={toggleDrawerOpen} variant="contained" color="info">
                Print
              </Button>
            </Stack>
          </Box> */}
        </Card>
      </CalendarRoot>
    </Page>
  );
}

export default Calendar;
