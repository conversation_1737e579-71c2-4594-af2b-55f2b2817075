import { MessagesTypeProps } from '@/types/Common';
import { Box, Stack, Typography } from '@mui/material';

export const LoadingMessage = ({ message, icon }: MessagesTypeProps) => {
  return (
    <Stack sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
      <Box
        sx={{
          // backgroundColor: '#F4F6F8',
          // p: 2,
          height: 80,
          width: 80,
          display: 'flex',
          justifyContent: 'center',
        }}
      >
        <img style={{ borderRadius: '10%' }} src={icon} alt={icon} />
      </Box>
      <Typography variant="subtitle1" textAlign="center" color="secondary" px={5}>
        {message}
      </Typography>
    </Stack>
  );
};
