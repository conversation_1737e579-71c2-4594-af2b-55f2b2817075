/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import { Autocomplete, Divider, Grid, Stack, TextField, Button, Typography, Box } from '@mui/material';
import styled from 'styled-components';
import { CLASS_SELECT, YEAR_SELECT } from '@/config/Selection';

const MapMaterialsRoot = styled.div`
  width: 100%;
  padding: 0.5rem 1.5rem;
`;
type Props = {
  onClick: () => void;
};
function MapMaterials({ onClick }: Props) {
  return (
    <MapMaterialsRoot>
      <Box>
        <Typography variant="h6" fontSize={17}>
          Materials Map To Subject
          <Divider />
        </Typography>
        <Typography pt={2}>From</Typography>
        <Grid pb={1} pt={2} container spacing={3}>
          <Grid item lg={6} xs={12}>
            <Typography variant="h6" fontSize={14}>
              Academic Year
            </Typography>
            <Autocomplete
              options={YEAR_SELECT}
              renderInput={(params) => <TextField {...params} placeholder="Select" />}
            />
          </Grid>
          <Grid item lg={6} xs={12}>
            <Typography variant="h6" fontSize={14}>
              Class
            </Typography>
            <Autocomplete
              options={CLASS_SELECT}
              renderInput={(params) => <TextField {...params} placeholder="Select" />}
            />
          </Grid>
          <Grid item lg={6} xs={12}>
            <Typography variant="h6" fontSize={14}>
              Subject
            </Typography>
            <Autocomplete
              options={YEAR_SELECT}
              renderInput={(params) => <TextField {...params} placeholder="Select" />}
            />
          </Grid>
          <Grid item lg={6} xs={12}>
            <Typography variant="h6" fontSize={14}>
              Study Material
            </Typography>
            <Autocomplete
              options={YEAR_SELECT}
              renderInput={(params) => <TextField {...params} placeholder="Select" />}
            />
          </Grid>
        </Grid>
        <Typography>To</Typography>
        <Grid pb={4} pt={2} container spacing={3}>
          <Grid item lg={6} xs={12}>
            <Typography variant="h6" fontSize={14}>
              Academic Year
            </Typography>
            <Autocomplete
              options={YEAR_SELECT}
              renderInput={(params) => <TextField {...params} placeholder="Select" />}
            />
          </Grid>
          <Grid item lg={6} xs={12}>
            <Typography variant="h6" fontSize={14}>
              Class
            </Typography>
            <Autocomplete
              options={CLASS_SELECT}
              renderInput={(params) => <TextField {...params} placeholder="Select" />}
            />
          </Grid>
          <Grid item xs={12}>
            <Typography variant="h6" fontSize={14}>
              Subject
            </Typography>
            <Autocomplete
              options={YEAR_SELECT}
              renderInput={(params) => <TextField {...params} placeholder="Select" />}
            />
          </Grid>
          <Stack
            spacing={2}
            direction="row"
            sx={{ pt: { xs: 3.5, md: 3.79 }, ml: 'auto', width: { xs: '95%', md: '55%' } }}
          >
            <Button variant="contained" color="secondary" fullWidth>
              Cancel
            </Button>
            <Button onClick={onClick} variant="contained" color="primary" fullWidth>
              Map Material
            </Button>
          </Stack>
        </Grid>
      </Box>
    </MapMaterialsRoot>
  );
}

export default MapMaterials;
