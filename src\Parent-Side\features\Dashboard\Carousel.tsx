/* eslint-disable react/no-unescaped-entities */
import { Box, Card, IconButton, Stack, Typography } from '@mui/material';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Autoplay, Navigation, Pagination } from 'swiper';
import typography from '@/theme/typography';
// import { delay } from '@reduxjs/toolkit/dist/utils';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useAppSelector } from '@/hooks/useAppSelector';
import KeyboardArrowLeftIcon from '@mui/icons-material/KeyboardArrowLeft';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import { getClassData, getdashboardVideosData, getdashboardVideosStatus } from '@/config/storeSelectors';
import { fetchClassList, fetchDashboardVideos } from '@/store/Dashboard/dashboard.thunks';
import React, { useEffect, useState } from 'react';
import useAuth from '@/hooks/useAuth';
import MenuItem from '@mui/material/MenuItem';
import { BsFillPlayCircleFill } from 'react-icons/bs';
import Select, { SelectChangeEvent } from '@mui/material/Select';
import styled, { useTheme } from 'styled-components';
import { ClassListInfo } from '@/types/AcademicManagement';
import downloadillustraion from '@/assets/downloadlink/downloadillustraion.svg';

import 'swiper/css';
import 'swiper/css/effect-coverflow';
import 'swiper/css/pagination';
import Popup from '@/components/shared/Popup/Popup';
import PopupVideoPlayer from '@/components/shared/PopupVideoPlayer';
import { grey } from '@mui/material/colors';
// Install Swiper modules
// SwiperCore.use([Autoplay, Pagination]);

const CarouselRoot = styled.div`
  max-height: 100%;
  max-width: 100%;
  /* margin-top: 1rem; */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.success.lighter : props.theme.palette.success.lighter};
  /* margin-right: 0.5rem; */

  @media screen and (max-width: 1199.5px) {
    margin: 0rem 0.5rem 1rem 0.5rem;
  }
  .swiper-pagination-carousel {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 15px;
    /* --swiper-pagination-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[100]};
    --swiper-pagination-bullet-inactive-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[100]}; */
  }
  .swiper_container_Carousel {
    position: relative;
  }
  .swiper-slide {
    position: relative;
  }
  .swiper-pagination-bullet {
    width: 12px;
    height: 12px;
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.success.main : props.theme.palette.success.main};
  }
  .swiper-button-prev {
    height: 30px;
    width: 30px;
    color: ${(props) => props.theme.palette.success.main};
    transform: translateX(-8px);
  }

  .swiper-button-next {
    height: 30px;
    width: 30px;
    color: ${(props) => props.theme.palette.success.main};
    transform: translateX(8px);
  }
  .swiper-button-next,
  .swiper-button-prev {
    position: absolute;
    top: var(--swiper-navigation-top-offset, 50%);
    margin-top: calc(5px - (var(--swiper-navigation-size) / 2));
    z-index: 10;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .darkmode-color {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  }
  .MuiSelect-root {
    /* border: 1px solid red; */
    /* border-radius: 50px; */
    padding: 0px 10px 0px 10px;
    /* height: 5px; */
  }
`;

function Carousel() {
  const { user } = useAuth();
  const theme = useTheme();
  const adminId: number | undefined = user?.accountId;
  const dispatch = useAppDispatch();
  const [videoPopup, setVideoPopup] = React.useState(false);
  const [videoFile, setVideoFile] = React.useState(false);

  const handlePlayVideo = (eventLinkFile) => {
    setVideoPopup(true);
    setVideoFile(eventLinkFile);
  };

  const [isVideoPlaying, setIsVideoPlaying] = useState(false);

  const handleVideoPlay = () => {
    setIsVideoPlaying(true);
  };

  const handleVideoEnd = () => {
    setIsVideoPlaying(false);
  };
  const dashboardVideosStatus = useAppSelector(getdashboardVideosStatus);
  const dashboardVideosData = useAppSelector(getdashboardVideosData);
  const ClassData = useAppSelector(getClassData);

  const AllClassOption: ClassListInfo = {
    classId: -1,
    className: 'All Class',
    classDescription: 'string',
    classStatus: 1,
  };

  const classDataWithAllClass = [AllClassOption, ...ClassData];
  const [classOptions, setClassOptions] = useState<ClassListInfo>(classDataWithAllClass[0]);

  const { classId, className } = classOptions || {};

  const handleChange = (event: SelectChangeEvent) => {
    const selectedClass = classDataWithAllClass.find((item) => item.className === event.target.value);
    if (selectedClass) {
      setClassOptions(selectedClass);
      setIsVideoPlaying(false);
    }
  };

  useEffect(() => {
    dispatch(fetchClassList(adminId));
    dispatch(fetchDashboardVideos({ adminId, classId }));
    setIsVideoPlaying(false);
  }, [dispatch, adminId, classId]);

  return (
    <CarouselRoot>
      <Box>
        <Swiper
          spaceBetween={17}
          effect="coverflow"
          grabCursor
          coverflowEffect={{
            rotate: 0,
            depth: 0,
            stretch: 0,
          }}
          pagination={{ el: '.swiper-pagination-carousel', clickable: true }}
          navigation={{
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
          }}
          autoplay={{ delay: isVideoPlaying ? 100000 : 5000, disableOnInteraction: isVideoPlaying }}
          // loop
          modules={[Autoplay, Pagination, Navigation]}
          className="swiper_container_Carousel mt-4 "
        >
          {/* {dashboardVideosStatus === 'loading' || dashboardVideosData.length === 0 ? (
              <SwiperSlide className="swiper-slide " onClick={handleVideoPlay}>
                <Stack spacing={2} m="10px" justifyContent="center" alignItems="center">
                  <img src={noVideo} alt="video" width="170px" />
                  <Typography variant="subtitle2" color="GrayText">
                    No Videos in this Class
                  </Typography>
                </Stack>
              </SwiperSlide>
            ) : ( */}
          {[1, 2, 3]?.map((item) => (
            <SwiperSlide className="swiper-slide">
              <Stack direction="row" spacing={2} px={{ xs: 3, sm: 6 }}>
                <Stack direction="column" spacing={2} pt={5}>
                  <Typography variant="h4" fontWeight="bold" fontSize={{ xs: '18px', sm: '24px' }}>
                    Lorem ipsum <span style={{ color: theme.palette.success.main }}>dolor amet</span>
                  </Typography>
                  <Typography
                    variant="h5"
                    fontSize={{ xs: '13px', sm: '20px' }}
                    sx={{ fontFamily: typography.fontFamily, color: 'GrayText' }}
                  >
                    Lorem ipsum dolor sit amet, consectetur and elit,eiusmod nostrud exercitation laboris aliquip
                    commodo consequat.
                  </Typography>
                </Stack>
                <Box height={200}>
                  <img src={downloadillustraion} height="100%" width="100%" alt="" />
                </Box>
              </Stack>
            </SwiperSlide>
          ))}
          <div className="slider-controler ">
            <div className="swiper-pagination-carousel"> </div>
            <div className="d-none d-sm-flex">
              <KeyboardArrowLeftIcon className="swiper-button-prev slider-arrow border-0 card shadow rounded-circle" />
            </div>
            <div className="d-none d-sm-flex">
              <KeyboardArrowRightIcon className="swiper-button-next slider-arrow border-0 shadow card rounded-circle" />
            </div>
          </div>
        </Swiper>
      </Box>
    </CarouselRoot>
  );
}

export default Carousel;
