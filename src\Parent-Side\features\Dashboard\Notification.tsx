/* eslint-disable @typescript-eslint/no-use-before-define */
import styled, { ThemeConsumer } from 'styled-components';
import {
  Card,
  Typography,
  Stack,
  Box,
  useTheme,
  SelectChangeEvent,
  Select,
  MenuItem,
  Tooltip,
  Skeleton,
  Chip,
} from '@mui/material';
import LinearProgress, { linearProgressClasses } from '@mui/material/LinearProgress';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Pagination, Navigation, Grid } from 'swiper';
import KeyboardArrowLeftIcon from '@mui/icons-material/KeyboardArrowLeft';
import AccessAlarmsOutlinedIcon from '@mui/icons-material/AccessAlarmsOutlined';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';

import { breakPointsMaxwidth } from '@/config/breakpoints';
import typography from '@/theme/typography';
import useAuth from '@/hooks/useAuth';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { getClassData, getClassStatus, getNotificationData, getNotificationStatus } from '@/config/storeSelectors';
import { useAppSelector } from '@/hooks/useAppSelector';
import { ReactNode, useCallback, useEffect, useMemo, useState } from 'react';
import notification from '@/Parent-Side/assets/notification.svg';

import 'swiper/css';
import 'swiper/css/effect-coverflow';
import 'swiper/css/pagination';
import 'swiper/css/navigation';

const BorderLinearProgress = styled(LinearProgress)(({ theme }) => ({
  height: 10,
  borderRadius: 5,
  [`&.${linearProgressClasses.colorPrimary}`]: {
    backgroundColor: theme.palette.grey[theme.palette.mode === 'light' ? 200 : 700],
  },
  [`& .${linearProgressClasses.bar}`]: {
    borderRadius: 5,
    backgroundColor: theme.palette.mode === 'light' ? theme.palette.primary.main : '#fff',
  },
}));

const NotificationRoot = styled.div`
  /* padding: 1rem; */
  .heading {
    padding: 1rem;
  }
  .period {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.common.black : props.theme.palette.common.white};
    color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.common.black};
    border-radius: 20px;
    border: 1.5px solid
      ${(props) =>
        props.theme.themeMode === 'light' ? props.theme.palette.common.black : props.theme.palette.common.black};
  }
  .swiper_container {
    position: relative;
    padding: 1rem 2.2rem 1rem 2.2rem;
  }
  .swiper-slide {
    position: relative;
  }
  .swiper-button-prev {
    height: 30px;
    width: 30px;
    color: ${(props) => props.theme.palette.success.main};
    transform: translateX(-8px);
  }

  .swiper-button-next {
    height: 30px;
    width: 30px;
    color: ${(props) => props.theme.palette.success.main};
    transform: translateX(8px);
  }
  .swiper-button-next,
  .swiper-button-prev {
    position: absolute;
    top: var(--swiper-navigation-top-offset, 50%);
    margin-top: calc(5px - (var(--swiper-navigation-size) / 2));
    z-index: 10;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .Notification-card {
    /* background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[800]}; */
    border: 1px solid
      ${(props) => (props.theme.themeMode === 'light' ? props.theme.palette.grey[300] : props.theme.palette.grey[800])};
  }
  .swiper-pagination {
    position: relative;
    transform: translateY(10px);
  }
  @media ${breakPointsMaxwidth.sm} {
    .swiper_container {
      padding: 1rem;
    }
  }
  @keyframes scroll {
    0% {
      transform: translateX(0);
    }
    100% {
      transform: translateX(-100%);
    }
  }

  .scrolling-text {
    animation: scroll 10s linear infinite;
    white-space: nowrap;
    overflow: hidden;
  }
  .MuiSelect-outlined {
    /* border: 1px solid red; */
    /* border-radius: 50px; */
    padding: 0px 10px 0px 10px;
    /* height: 5px; */
  }
`;

export default function Notification() {
  const { user } = useAuth();
  const theme = useTheme();
  const adminId: number | undefined = user?.accountId;

  return (
    <NotificationRoot>
      <Box className="">
        <Stack direction="row" sx={{ padding: ' 1rem 1rem 0rem 1rem' }} display="flex" justifyContent="space-between">
          <Typography variant="h6" fontSize={18} sx={{ fontFamily: typography.fontFamily }}>
            Notification
          </Typography>
          <Typography
            variant="subtitle2"
            fontSize={10}
            color={theme.palette.success.main}
            sx={{ fontFamily: typography.fontFamily }}
          >
            See All
          </Typography>
          {/* <SelectBox Selection_Options={CLASS_SELECT} placeholder="Class" /> */}
        </Stack>
        <Swiper
          spaceBetween={17}
          effect="coverflow"
          grabCursor
          breakpoints={{
            299: {
              slidesPerView: 1.2,
            },
            499: {
              slidesPerView: 2,
            },
            699: {
              slidesPerView: 3,
            },
            766: {
              slidesPerView: 2,
            },
            999: {
              slidesPerView: 2,
            },
            1180: {
              slidesPerView: 3,
            },
            1499: {
              slidesPerView: 4,
            },
            1699: {
              slidesPerView: 5,
            },
          }}
          coverflowEffect={{
            rotate: 0,
            depth: 0,
            stretch: 0,
          }}
          pagination={{ el: '.swiper-pagination', clickable: true }}
          navigation={{
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
          }}
          modules={[Pagination, Navigation]}
          className="swiper_container"
        >
          <div>
            {[1, 2, 3, 4, 5, 6, 7, 8]?.map((data) => (
              <SwiperSlide className="swiper-slide">
                <Card className="Notification-card" sx={{ padding: '10px', boxShadow: '0' }}>
                  <Box display="flex" justifyContent="space-between" width="100%" gap={1}>
                    <Stack width="20%">
                      <img width={30} src={notification} alt="" />
                    </Stack>
                    <Box width="80%">
                      <Stack>
                        <Typography variant="subtitle2">Sports Day</Typography>
                      </Stack>
                      <Stack>
                        <Typography
                          variant="body2"
                          fontSize={10}
                          color="secondary"
                          sx={
                            {
                              // display: '-webkit-box',
                              // WebkitBoxOrient: 'vertical',
                              // WebkitLineClamp: 2, // Set the number of lines to display
                              // textOverflow: 'ellipsis',
                            }
                          }
                        >
                          Lorem ipsum dolor sit, amet consec consec amet cos...
                        </Typography>
                      </Stack>
                      <Box display="flex" alignItems="center">
                        <AccessAlarmsOutlinedIcon color="secondary" sx={{ fontSize: '11px', marginRight: '5px' }} />
                        <Typography
                          sx={{ fontFamily: 'Poppins medium', fontWeight: '500', fontSize: '10px', color: 'gray' }}
                        >
                          Today 01:00 PM
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                </Card>
              </SwiperSlide>
            ))}
          </div>
          <div className="slider-controler d-none d-sm-flex">
            <div>
              <KeyboardArrowLeftIcon className="swiper-button-prev slider-arrow border-0 card shadow rounded-circle" />
            </div>
            <div>
              <KeyboardArrowRightIcon className="swiper-button-next slider-arrow border-0 shadow card rounded-circle" />
            </div>
            {/* <div className="swiper-pagination d-none"> </div> */}
          </div>
        </Swiper>
      </Box>
    </NotificationRoot>
  );
}
