/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  Collapse,
  FormControl,
} from '@mui/material';
import styled from 'styled-components';
import SearchIcon from '@mui/icons-material/Search';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { CLASS_SELECT } from '@/config/Selection';

const CClistRoot = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      height: calc(100% - 45px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;
const dummyData = [
  {
    SlNo: 1,
    Name: 'Manohar',
    guardianName: 'Parent',
    Mobile: '+91-9546845701',
    Class: 'Test Class',
  },
  {
    SlNo: 2,
    Name: 'Hemal',
    guardianName: 'Parent',
    Mobile: '+91-8675670235',
    Class: 'Test Class',
  },
  {
    SlNo: 3,
    Name: 'Rishabh',
    guardianName: 'Parent',
    Mobile: '+91-9072564723',
    Class: 'Test Class',
  },
  {
    SlNo: 4,
    Name: 'Shresthi',
    guardianName: 'Parent',
    Mobile: '+91-6553524169',
    Class: 'Test Class',
  },
  {
    SlNo: 5,
    Name: 'Binod',
    guardianName: 'Parent',
    Mobile: '+91-9405230125',
    Class: 'Test Class',
  },
  {
    SlNo: 6,
    Name: 'Chanakya',
    guardianName: 'Parent',
    Mobile: '+91-9625212703',
    Class: 'Test Class',
  },
  {
    SlNo: 7,
    Name: 'Aarin',
    guardianName: 'Parent',
    Mobile: '+91-8417838415',
    Class: 'Test Class',
  },
  {
    SlNo: 8,
    Name: 'Jaivardhan',
    guardianName: 'Parent',
    Mobile: '+91-9214334843',
    Class: 'Test Class',
  },
];
function CClist() {
  const [showFilter, setShowFilter] = useState(false);

  const getRowKey = useCallback((row: any) => row.examId, []);
  const CClistColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'slNo',
        dataKey: 'SlNo',
        headerLabel: 'Sl.No',
        sortable: true,
      },
      {
        name: 'name',
        dataKey: 'Name',
        headerLabel: 'Student Name',
      },
      {
        name: 'class',
        dataKey: 'Class',
        headerLabel: 'Class',
      },
      {
        name: 'gaurdianName',
        dataKey: 'guardianName',
        headerLabel: 'Gaurdian Name',
      },
      {
        name: 'mobile',
        dataKey: 'Mobile',
        headerLabel: 'Mobile Number',
      },
      {
        name: 'actions',
        headerLabel: 'Actions',
        renderCell: () => {
          return (
            <Stack direction="row" gap={1}>
              <Button size="small" variant="contained">
                Create
              </Button>
            </Stack>
          );
        },
      },
    ],
    []
  );

  return (
    <Page title="Fee Alert Pending">
      <CClistRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17}>
              Conduct Certificates
            </Typography>
            <Box pb={1} sx={{ flexShrink: 0 }}>
              <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                <SearchIcon />
              </IconButton>
            </Box>
          </Stack>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={3} alignItems="end">
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 180 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Student Name
                      </Typography>
                      <TextField placeholder="Enter Name" />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 180 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Autocomplete
                        options={CLASS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select Class" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 180 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Guardian Name
                      </Typography>
                      <TextField placeholder="Enter Name" />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 180 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Mobile
                      </Typography>
                      <TextField placeholder="Enter No." />
                    </FormControl>
                  </Grid>

                  <Grid item lg="auto" xs={12} sm={6}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Paper className="card-table-container" sx={{ marginTop: '12px' }}>
              <DataTable columns={CClistColumns} data={dummyData} getRowKey={getRowKey} fetchStatus="success" />
            </Paper>
          </div>
        </Card>
      </CClistRoot>
    </Page>
  );
}

export default CClist;
