import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import Loadable from '@/components/shared/Loadable';

const Album = Loadable(lazy(() => import('@/pages/Certificates')));
const TClist = Loadable(lazy(() => import('@/features/Certifcates/TcList/TClist')));
const CClist = Loadable(lazy(() => import('@/features/Certifcates/CcList/CClist')));
const Bonafide = Loadable(lazy(() => import('@/features/Certifcates/Bonafide/Bonafide')));

export const certificateRoutes: RouteObject[] = [
  {
    path: '/certificates',
    element: <Album />,
    children: [
      {
        path: 'TC-list',
        element: <TClist />,
      },
      {
        path: 'CC-list',
        element: <CClist />,
      },
      {
        path: 'bonafide-list',
        element: <Bonafide />,
      },
    ],
  },
];
