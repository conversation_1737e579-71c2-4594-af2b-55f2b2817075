import Loadable from '@/components/shared/Loadable';
import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';

const PaymentDetails = Loadable(lazy(() => import('@/pages/PaymentDetails')));
const CategoryList = Loadable(lazy(() => import('@/features/PaymentDetails/CategoryList')));
const SubCategoryList = Loadable(lazy(() => import('@/features/PaymentDetails/SubCategoryList')));
const CreatePayment = Loadable(lazy(() => import('@/features/PaymentDetails/CreatePayment')));
const PaymentList = Loadable(lazy(() => import('@/features/PaymentDetails/PaymentList')));

export const paymentDetailsRoutes: RouteObject[] = [
  {
    path: '/payments',
    element: <PaymentDetails />,
    children: [
      {
        path: '/payments/category',
        element: <CategoryList />,
      },
      {
        path: '/payments/sub-category',
        element: <SubCategoryList />,
      },
      {
        path: '/payments/new',
        element: <CreatePayment />,
      },
      {
        path: '/payments/list',
        element: <PaymentList />,
      },
    ],
  },
];
