import {
  Table,
  TableCell,
  TableHead,
  TableRow,
  TableContainer,
  Box,
  TableBody,
  Typography,
  Button,
} from '@mui/material';

export const GetReceipt = () => {
  return (
    <Box>
      <Box sx={{ width: { xs: '320px', md: '650px' }, overflow: 'auto' }}>
        <Table>
          <TableBody>
            <TableRow>
              <TableCell>Student Name : Abiram <PERSON></TableCell>
              <TableCell align="right">Receipt No : 60487345</TableCell>
            </TableRow>
            <TableRow>
              <TableCell>Student Name : Class/Roll No : VII A / 08</TableCell>
              <TableCell align="right">Date : 20/05/2023</TableCell>
            </TableRow>
          </TableBody>
        </Table>

        <TableContainer>
          <Table>
            <TableHead sx={{ color: 'red' }}>
              <TableRow>
                <TableCell>Particular</TableCell>
                <TableCell align="right">Amount</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {[1].map((item) => (
                <TableRow key={item}>
                  <TableCell>Consolidated Fee </TableCell>
                  <TableCell align="right">300.00</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
        <Box sx={{ borderTop: '2px dashed gray' }} p={3} display="flex" justifyContent="space-between">
          <Typography variant="body2" fontWeight={600} color="secondary">
            Grand Total
          </Typography>
          <Typography variant="body2" fontWeight={600}>
            2000
          </Typography>
        </Box>
      </Box>
      <Box my={2} display="flex" sx={{ justifyContent: 'center' }}>
        <Button variant="contained" color="primary" size="medium" sx={{ width: '9.375rem' }}>
          Print Receipt
        </Button>
      </Box>
    </Box>
  );
};

export const DetailedReceipt = () => {
  return (
    <Box>
      <Box sx={{ width: { xs: '320px', md: '650px' }, overflow: 'auto' }}>
        <Table>
          <TableBody>
            <TableRow>
              <TableCell>Student Name : Abiram K</TableCell>
              <TableCell align="right">Receipt No : 60487345</TableCell>
            </TableRow>
            <TableRow>
              <TableCell>Student Name : Class/Roll No : VII A / 08</TableCell>
              <TableCell align="right">Date : 20/05/2023</TableCell>
            </TableRow>
          </TableBody>
        </Table>

        <TableContainer>
          <Table>
            <TableHead sx={{ color: 'red' }}>
              <TableRow>
                <TableCell>Particular</TableCell>
                <TableCell align="right">Amount</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {[1, 2, 3, 4].map((item) => (
                <TableRow key={item}>
                  <TableCell>Development Fee </TableCell>
                  <TableCell align="right">300.00</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
        <Box sx={{ borderTop: '2px dashed gray' }} p={3} display="flex" justifyContent="space-between">
          <Typography variant="body2" fontWeight={600} color="secondary">
            Grand Total
          </Typography>
          <Typography variant="body2" fontWeight={600}>
            2000
          </Typography>
        </Box>
      </Box>
      <Box my={2} display="flex" sx={{ justifyContent: 'center' }}>
        <Button variant="contained" color="primary" size="medium" sx={{ width: '9.375rem' }}>
          Print Receipt
        </Button>
      </Box>
    </Box>
  );
};
