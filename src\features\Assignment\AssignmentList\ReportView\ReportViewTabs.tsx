import * as React from 'react';
import SwipeableViews from 'react-swipeable-views';
import { useTheme } from '@mui/material/styles';
import AppBar from '@mui/material/AppBar';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import {
  FormLabel,
  Radio,
  RadioGroup,
  Stack,
  TextField,
  FormControl,
  FormControlLabel,
  IconButton,
  Collapse,
  Tooltip,
} from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import AddIcon from '@mui/icons-material/Add';
import useSettings from '@/hooks/useSettings';
import CloseIcon from '@mui/icons-material/Close';

interface TabPanelProps {
  children?: React.ReactNode;
  dir?: string;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`full-width-tabpanel-${index}`}
      aria-labelledby={`full-width-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ pt: 3 }}>{children}</Box>}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `full-width-tab-${index}`,
    'aria-controls': `full-width-tabpanel-${index}`,
  };
}

export default function ReportViewTabs() {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const [value, setValue] = React.useState(0);
  const [showComment, setShowComment] = React.useState(false);
  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  const handleChangeIndex = (index: number) => {
    setValue(index);
  };

  return (
    <Box sx={{ bgcolor: 'background.paper' }}>
      <Tabs
        value={value}
        onChange={handleChange}
        indicatorColor="secondary"
        textColor="inherit"
        aria-label="full width tabs example"
      >
        <Tab label="Assignment Details" {...a11yProps(0)} />
        <Tab label="Mark & Teacher Comment" {...a11yProps(1)} />
      </Tabs>
      <SwipeableViews
        axis={theme.direction === 'rtl' ? 'x-reverse' : 'x'}
        index={value}
        onChangeIndex={handleChangeIndex}
      >
        <TabPanel value={value} index={0} dir={theme.direction}>
          <p>
            Lorem ipsum dolor sit amet consectetur adipisicing elit. Eligendi reprehenderit expedita eos sunt nemo saepe
            doloribus dolore exercitationem, optio omnis perferendis vitae quos earum non ipsam harum praesentium
            temporibus recusandae.
          </p>
        </TabPanel>
        <TabPanel value={value} index={1} dir={theme.direction}>
          <Box display="flex" justifyContent="space-between">
            <Stack>
              <FormControl sx={{ width: 150 }}>
                <Typography variant="subtitle1" fontSize={14}>
                  Mark
                </Typography>
                <TextField fullWidth placeholder="Enter mark" />
              </FormControl>
            </Stack>
            <Stack>
              <FormControl>
                <Typography variant="subtitle1" fontSize={14}>
                  Teacher Comment
                </Typography>
                <RadioGroup
                  row
                  defaultValue="Excellent"
                  aria-labelledby="demo-row-radio-buttons-group-label"
                  name="row-radio-buttons-group"
                >
                  <FormControlLabel
                    value="Excellent"
                    control={<Radio checkedIcon={<CheckCircleIcon />} />}
                    label="Excellent"
                  />
                  <FormControlLabel
                    value="Very Good"
                    control={<Radio checkedIcon={<CheckCircleIcon />} />}
                    label="Very Good"
                  />
                  <FormControlLabel value="Good" control={<Radio checkedIcon={<CheckCircleIcon />} />} label="Good" />
                  <FormControlLabel
                    value="Need Improvement"
                    control={<Radio checkedIcon={<CheckCircleIcon />} />}
                    label="Need Improvement"
                  />
                </RadioGroup>
              </FormControl>
            </Stack>
            <Stack>
              <Tooltip title="Teacher Comment">
                <IconButton
                  onClick={() => setShowComment((x) => !x)}
                  size="small"
                  aria-label=""
                  sx={{ bgcolor: isLight ? theme.palette.grey[300] : theme.palette.grey[900] }}
                >
                  {showComment === false ? <AddIcon /> : <CloseIcon />}
                </IconButton>
              </Tooltip>
            </Stack>
          </Box>
          <Collapse in={showComment} sx={{ my: 2 }}>
            <FormControl fullWidth>
              <Typography variant="subtitle1" fontSize={14}>
                Teacher Comment
              </Typography>
              <TextField
                multiline
                fullWidth
                minRows={2}
                InputProps={{ inputProps: { style: { resize: 'vertical' } } }}
                placeholder="Enter comment..."
                name="classDescription"
                value=""
              />
            </FormControl>
          </Collapse>
        </TabPanel>
      </SwipeableViews>
    </Box>
  );
}
