import * as React from 'react';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';
import Page from '@/components/shared/Page';
import styled from 'styled-components';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import ManageYear from '@/features/AcademicManagement/ManageYear/ManageYear';
import ManageClass from '@/features/AcademicManagement/ManageClass/ManageClass';
import ClassSort from '@/features/AcademicManagement/ClassSort/ClassSort';
import SubjectCategory from '@/features/AcademicManagement/SubjectCategory/SubjectCategory';
import ManageSubject from '@/features/AcademicManagement/ManageSubject/ManageSubject';
import LanguageStudent from '@/features/AcademicManagement/LanguageStudent/LanguageStudent';
import MaterialsList from '@/features/AcademicManagement/MaterialsList/MaterialsList';
import EventDetailsList from '@/features/AcademicManagement/EventDetails/EventDetailsList';
import { Link, useLocation, useOutletContext } from 'react-router-dom';
import ManageSection from '@/features/AcademicManagement/ManageSection/ManageSection';
import { useDispatch } from 'react-redux';
import { flushStore } from '@/store/flush.slice';
import { OutletContextType } from '@/types/Common';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const AcademicManagementRoot = styled.div`
  min-height: calc(100vh - ${TOP_BAR_HEIGHT});
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  .tab {
    background-color: ${(props) => (props.theme.themeMode === 'light' ? '#fff' : props.theme.palette.grey[800])};
    @media screen and (min-width: 1171px) {
      width: 100%;
    }
    @media screen and (max-width: 720px) {
      width: 100%;
    }
  }
  .Mui-selected {
    color: ${(props) => props.theme.palette.primary.main};
  }
  /* .MuiTabs-scrollButtons.Mui-disabled {
    opacity: 0.3;
  } */
`;
function TabPanel({ children, value, index }: TabPanelProps) {
  return (
    <Box
      sx={{ paddingTop: '3rem' }}
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
    >
      {value === index && <Box>{children}</Box>}
    </Box>
  );
}
function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`,
  };
}

export default function AcademicManagement() {
  const { topBarHeight } = useOutletContext<OutletContextType>();
  const location = useLocation();
  const [value, setValue] = React.useState(0);

  const dispatch = useDispatch();

  React.useEffect(() => {
    dispatch(flushStore());
    console.log('Flush Store');
  }, [dispatch, value]);

  React.useEffect(() => {
    const currentPath = location.pathname;
    const routePaths = [
      '/academic-management/year',
      '/academic-management/class',
      '/academic-management/class-section',
      '/academic-management/sort-class',
      '/academic-management/subject',
      '/academic-management/category-subject',
      '/academic-management/language-student',
      '/academic-management/study-materials',
      '/academic-management/events',
    ];
    const index = routePaths.indexOf(currentPath);
    if (index !== -1) {
      setValue(index);
    }
  }, [location]);

  return (
    <Page title="Academic Management">
      <AcademicManagementRoot>
        <Tabs
          className="tab"
          variant="scrollable"
          scrollButtons
          allowScrollButtonsMobile
          value={value}
          aria-label="basic tabs"
          sx={{
            ml: '0px',
            borderBottom: 1,
            borderColor: 'divider',
            position: 'fixed',
            top: `${topBarHeight}px`,
            zIndex: 1,
            // width: '100%',
          }}
        >
          <Tab {...a11yProps(0)} label="Manage Year" component={Link} to="/academic-management/year" />
          <Tab {...a11yProps(1)} label="Manage Class" component={Link} to="/academic-management/class" />
          <Tab {...a11yProps(2)} label="Class Section" component={Link} to="/academic-management/class-section" />
          <Tab {...a11yProps(3)} label="Class Sort" component={Link} to="/academic-management/sort-class" />
          <Tab {...a11yProps(4)} label="Subjects" component={Link} to="/academic-management/subject" />
          <Tab {...a11yProps(5)} label="Subject Category" component={Link} to="/academic-management/category-subject" />
          <Tab {...a11yProps(6)} label="Language Student" component={Link} to="/academic-management/language-student" />
          <Tab {...a11yProps(7)} label="Materials List" component={Link} to="/academic-management/study-materials" />
          <Tab {...a11yProps(8)} label="Event Details List" component={Link} to="/academic-management/events" />
        </Tabs>
        <TabPanel value={value} index={0}>
          <ManageYear />
        </TabPanel>
        <TabPanel value={value} index={1}>
          <ManageClass />
        </TabPanel>
        <TabPanel value={value} index={2}>
          <ManageSection />
        </TabPanel>
        <TabPanel value={value} index={3}>
          <ClassSort />
        </TabPanel>
        <TabPanel value={value} index={4}>
          <ManageSubject />
        </TabPanel>
        <TabPanel value={value} index={5}>
          <SubjectCategory />
        </TabPanel>
        <TabPanel value={value} index={6}>
          <LanguageStudent />
        </TabPanel>
        <TabPanel value={value} index={7}>
          <MaterialsList />
        </TabPanel>
        <TabPanel value={value} index={8}>
          <EventDetailsList />
        </TabPanel>

        {/* <Box sx={{ paddingTop: '3rem' }}>
          <Outlet />
        </Box> */}
      </AcademicManagementRoot>
    </Page>
  );
}
