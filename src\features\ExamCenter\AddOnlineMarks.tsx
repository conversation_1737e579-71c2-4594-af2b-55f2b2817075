/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import { Autocomplete, Box, Grid, Stack, TextField, Button, Typography, Card, Radio, useTheme } from '@mui/material';
import styled from 'styled-components';
import { CLASS_SELECT, SUBJECT_SELECT, YEAR_SELECT } from '@/config/Selection';
import useSettings from '@/hooks/useSettings';
import typography from '@/theme/typography';
import Popup from '@/components/shared/Popup/Popup';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';

const AddOnlineMarksRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;
  .inner-card {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.grey[50] : props.theme.palette.grey[800]};
  }
  h6,
  h5 {
    font-weight: 600;
  }
  .MuiTextField-root:hover .MuiInputBase-input {
    cursor: text;
  }
`;

function  AddOnlineMarks() {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const [popup, setPopup] = React.useState(false);

  const handleClickOpen = () => setPopup(true);
  const handleClickClose = () => setPopup(false);

  const [selectedExamType, setSelectedExamType] = React.useState('MCQ');

  const handleExamTypeChange = (event) => {
    setSelectedExamType(event.target.value);
  };
  return (
    <Page title="Add Online Marks">
      <AddOnlineMarksRoot>
        <Stack sx={{ flexDirection: { xs: 'column', md: 'row' } }}>
          <Grid container>
            <Grid item xs={12}>
              <Card elevation={1} sx={{ p: 3, m: 2 }} className="inner-card">
                <Stack spacing={1}>
                  <Typography variant="h5" fontSize={18} pb={2}>
                    Online Exam
                  </Typography>
                  <Typography variant="h6" fontSize={14}>
                    Academic Year
                  </Typography>
                  <Autocomplete
                    options={YEAR_SELECT}
                    renderInput={(params) => <TextField {...params} placeholder="Select Year" />}
                  />
                  <Typography variant="h6" fontSize={14}>
                    Class
                  </Typography>
                  <Autocomplete
                    options={CLASS_SELECT}
                    renderInput={(params) => <TextField {...params} placeholder="Select Class" />}
                  />
                  <Typography variant="h6" fontSize={14}>
                    Subject
                  </Typography>
                  <Autocomplete
                    options={SUBJECT_SELECT}
                    renderInput={(params) => <TextField {...params} placeholder="Select Subject" />}
                  />
                  <Box onClick={() => setSelectedExamType('MCQ')}>
                    <Typography variant="h6" fontSize={14}>
                      <Radio
                        size="small"
                        value="MCQ"
                        checked={selectedExamType === 'MCQ'}
                        onChange={handleExamTypeChange}
                      />
                      Objective(MCQ) Exam
                    </Typography>
                    <Autocomplete
                      options={['Test 1', 'Test 2', 'Test 3', 'Test 4']}
                      disabled={selectedExamType !== 'MCQ'}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          placeholder="Select Objective Exam"
                          sx={{
                            backgroundColor:
                              selectedExamType !== 'MCQ' ? (isLight ? '#f0f0f0' : theme.palette.grey[900]) : 'inherit',
                          }}
                        />
                      )}
                    />
                  </Box>
                  <Box onClick={() => setSelectedExamType('Descriptive')}>
                    <Typography variant="h6" fontSize={14}>
                      <Radio
                        size="small"
                        value="Descriptive"
                        checked={selectedExamType === 'Descriptive'}
                        onChange={handleExamTypeChange}
                      />
                      Descriptive Exam
                    </Typography>
                    <Autocomplete
                      options={['Test 1', 'Test 2', 'Test 3', 'Test 4']}
                      disabled={selectedExamType !== 'Descriptive'}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          placeholder="Select Descriptive Exam"
                          sx={{
                            backgroundColor:
                              selectedExamType !== 'Descriptive'
                                ? isLight
                                  ? '#f0f0f0'
                                  : theme.palette.grey[900]
                                : 'inherit',
                          }}
                        />
                      )}
                    />
                  </Box>
                  <Typography variant="h6" fontSize={14}>
                    Grade Category
                  </Typography>
                  <Autocomplete
                    options={['KG-IV', 'V-VIII', 'IX-XII']}
                    renderInput={(params) => <TextField {...params} placeholder="Select Category" />}
                  />
                </Stack>
              </Card>
            </Grid>
          </Grid>
          <Grid container>
            <Grid item xs={12}>
              <Card elevation={1} sx={{ p: 3, m: 2 }} className="inner-card">
                <Stack spacing={1.1}>
                  <Typography variant="h5" fontSize={18} pb={2}>
                    Progress Card Details
                  </Typography>
                  <Typography variant="h6" fontSize={14}>
                    Academic Year
                  </Typography>
                  <Autocomplete
                    options={YEAR_SELECT}
                    renderInput={(params) => <TextField {...params} placeholder="Select Year" />}
                  />

                  <Typography variant="h6" fontSize={14}>
                    Class
                  </Typography>
                  <Autocomplete
                    options={CLASS_SELECT}
                    renderInput={(params) => <TextField {...params} placeholder="Select Class" />}
                  />
                  <Typography variant="h6" fontSize={14}>
                    Subject
                  </Typography>
                  <Autocomplete
                    options={SUBJECT_SELECT}
                    renderInput={(params) => <TextField {...params} placeholder="Select Subject" />}
                  />
                  <Typography variant="h6" fontSize={14}>
                    Exam
                  </Typography>
                  <Autocomplete
                    options={['Test 1', 'Test 2', 'Test 3', 'Test 4']}
                    renderInput={(params) => <TextField {...params} placeholder="Select Exam" />}
                  />
                  <Typography variant="h6" fontSize={14}>
                    Pass Mark
                  </Typography>
                  <TextField placeholder="Enter Mark" fullWidth />

                  <Typography variant="h6" fontSize={14}>
                    Conversion Mark, if Needed
                  </Typography>
                  <TextField placeholder="Enter Mark" fullWidth />
                </Stack>
              </Card>
            </Grid>
          </Grid>
        </Stack>
        <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: 2 }}>
          <Stack spacing={2} direction="row">
            <Button variant="contained" color="secondary" sx={{ fontFamily: typography.fontFamily }}>
              Cancel
            </Button>
            <Button onClick={handleClickOpen} variant="contained" color="primary">
              Map Details
            </Button>
          </Stack>
        </Box>
        <Popup
          size="xs"
          state={popup}
          onClose={handleClickClose}
          popupContent={<SuccessMessage message="Mapped Successfully" />}
        />
      </AddOnlineMarksRoot>
    </Page>
  );
}

export default AddOnlineMarks;
