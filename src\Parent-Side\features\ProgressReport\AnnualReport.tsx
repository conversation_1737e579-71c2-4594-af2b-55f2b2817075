/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  useTheme,
  IconButton,
  Collapse,
  Tooltip,
  FormControl,
  Chip,
  Snackbar,
  Alert,
  LinearProgress,
  linearProgressClasses,
} from '@mui/material';
import styled from 'styled-components';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import SearchIcon from '@mui/icons-material/Search';
import Popup from '@/components/shared/Popup/Popup';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import Physics from '@/Parent-Side/assets/liveclass/Icons/Physics.svg';
import EHS from '@/Parent-Side/assets/progressReport/EHS.png';
import { MdAdd, MdContentCopy } from 'react-icons/md';
import { CloseIcon } from '@/theme/overrides/CustomIcons';
import { Student, StudentInfoData } from '@/config/StudentDetails';
import useSettings from '@/hooks/useSettings';
import AutorenewOutlinedIcon from '@mui/icons-material/AutorenewOutlined';
import { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import DateSelect from '@/components/shared/Selections/DateSelect';
import MenuEditDelete from '@/components/shared/Selections/MenuEditDelete';
import CreateScheduleList from '@/features/LiveClass/ScheduleList/CreateScheduleList';
import InsertDriveFileIcon from '@mui/icons-material/InsertDriveFile';
import { Select } from '@mui/material';
import { MenuItem } from '@mui/material';
import { Avatar } from '@mui/material';

const BorderLinearProgress = styled(LinearProgress)(({ theme }) => ({
  height: 10,
  borderRadius: 5,
  [`&.${linearProgressClasses.colorPrimary}`]: {
    backgroundColor: theme.palette.grey[theme.palette.mode === 'light' ? 300 : 700],
  },
  [`& .${linearProgressClasses.bar}`]: {
    borderRadius: 5,
    backgroundColor: theme.palette.mode === 'light' ? theme.palette.common.black : '#fff',
  },
}));
const AnnualReportRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  .student_card {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  }

  .Card {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    height: 100%;
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      /* max-height: calc(100% - 40px); */
      flex-grow: 1;

      .card-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: auto;
        ::-webkit-scrollbar {
          width: 0px;
        }

        .MuiTableContainer-root {
          max-height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

function AnnualReport() {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const [changeView, setChangeView] = useState(false);
  const [showFilter, setShowFilter] = useState(false);
  const [Delete, setDelete] = useState(false);
  const [createPopup, setCreatePopup] = useState(false);

  return (
    <Page title="Schedule List">
      <AnnualReportRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 1, md: 2 } }}>
          <Box display="flex" alignItems="center" justifyContent="space-between" gap={5}>
            <Stack>
              <Avatar sx={{ width: '100px', height: '100px' }} src="" />
            </Stack>
            <Grid container>
              <Grid item lg={2} display="flex" flexDirection="column" gap={2}>
                <Typography variant="subtitle2" color="GrayText">
                  Name
                </Typography>
                <Typography variant="subtitle2" color="GrayText">
                  Admission No
                </Typography>
                <Typography variant="subtitle2" color="GrayText">
                  Class
                </Typography>
                <Typography variant="subtitle2" color="GrayText">
                  Gender
                </Typography>
              </Grid>
              <Grid item lg={2} display="flex" flexDirection="column" gap={2}>
                <Typography variant="subtitle2">Alex Peter</Typography>
                <Typography variant="subtitle2">89765</Typography>
                <Typography variant="subtitle2">X-A</Typography>
                <Typography variant="subtitle2">Male</Typography>
              </Grid>
              <Grid item lg={8} display="flex" flexDirection="column" gap={2}>
                <Stack direction="row">
                  <Chip color="success" icon={<img src={EHS} width={30} />} label="EHS" />
                </Stack>
                <Typography variant="subtitle2">Remarks</Typography>
                <Typography variant="subtitle2" color="GrayText">
                  Lorem ipsum dolor sit amet consectetur adipisicing elit. Incidunt minus libero necessitatibus animi
                  possimus iste aperiam soluta aliquam? Ut ex ipsum autem accusamus, magni iure earum veniam quasi
                  praesentium repudiandae?
                </Typography>
              </Grid>
            </Grid>
          </Box>
        </Card>
      </AnnualReportRoot>
      {/* <Popup
        size="xs"
        state={Delete}
        onClose={handleClickCloseDelete}
        popupContent={<DeleteMessage message="Are you sure want to delete?" />}
      /> */}
      {/* <Popup
        size="sm"
        title="Assignment Details"
        state={createPopup}
        onClose={() => setCreatePopup(false)}
        popupContent={<View />}
      /> */}
    </Page>
  );
}

export default AnnualReport;
