/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/alt-text */
import React, { useEffect, useState } from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  Paper,
  Table,
  TableBody,
  Checkbox,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  Avatar,
  IconButton,
  Collapse,
  Select,
  Tooltip,
  SelectChangeEvent,
  FormControl,
  MenuItem,
  Snackbar,
} from '@mui/material';
import styled from 'styled-components';
import { CLASS_SELECT, YEAR_SELECT } from '@/config/Selection';
import typography from '@/theme/typography';
import Popup from '@/components/shared/Popup/Popup';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import { PromoteStudentArray } from '@/config/TableData';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import NotSuccess from '@/assets/attendance/notApprove.svg';
import { PromoteStudent } from '@/types/ManageStudent';
import RemoveCircleOutlineIcon from '@mui/icons-material/RemoveCircleOutline';
import successIcon from '@/assets/MessageIcons/success.json';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { getClassData, getYearData, getYearStatus } from '@/config/storeSelectors';
import { useAppSelector } from '@/hooks/useAppSelector';
import { fetchClassList, fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import { MdAdd } from 'react-icons/md';
import SearchIcon from '@mui/icons-material/Search';
import { IoIosArrowUp } from 'react-icons/io';
import useAuth from '@/hooks/useAuth';
import { ClassListInfo } from '@/types/AcademicManagement';
import * as XLSX from 'xlsx';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import { SuccessIcon } from '@/theme/overrides/CustomIcons';
import MuiAlert from '@mui/material/Alert';

const PromoteStudentsRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  h6 {
    font-weight: 600;
  }
  .MuiTextField-root:hover .MuiInputBase-input {
    cursor: text;
  }
  /* .Card {
    min-height: calc(100vh - 500px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }
  } */
`;
const AllClassOption = {
  classId: -1,
  className: 'All Class',
  classDescription: 'string',
  classStatus: 1,
};
function PromoteStudents() {
  const { user } = useAuth();
  const dispatch = useAppDispatch();
  const adminId: number = user ? user.accountId : 0;
  const [Delete, setDelete] = React.useState(false);
  const [showFilter, setShowFilter] = useState(true);
  const YearStatus = useAppSelector(getYearStatus);
  const YearData = useAppSelector(getYearData);
  const ClassData = useAppSelector(getClassData);
  const classDataWithAllClass = [AllClassOption, ...ClassData];
  const defualtYear = YearData[0]?.accademicId || 0;
  const [academicYearFilter, setAcademicYearFilter] = useState(defualtYear);
  const [classFilter, setClassFilter] = useState(classDataWithAllClass[0]);
  const { classId, className } = classFilter || {};

  const [popup, setPopup] = React.useState(false);
  const [selected, setSelected] = useState<PromoteStudent[]>([]);
  const [open, setOpen] = React.useState(false);
  const [excelStudents, setExcelStudents] = useState<{ admission_no: string; name: string }[]>([]);
  const [checkedGroups, setCheckedGroups] = useState<{ [key: string]: boolean }>({});
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMsg, setSnackbarMsg] = useState('');

  useEffect(() => {
    if (excelStudents.length === 0) return;
    const matched = PromoteStudentArray.filter((student) =>
      excelStudents.some(
        (ex) =>
          student.admission_no.toString().trim().toLowerCase() === ex.admission_no.toLowerCase() &&
          student.name.trim().toLowerCase() === ex.name.toLowerCase()
      )
    );
    setSelected(matched);
  }, [excelStudents]);

  // Add this state:

  // Add this handler:

  // const handleExcelUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
  //   const file = e.target.files?.[0];
  //   if (!file) return;
  //   const reader = new FileReader();
  //   reader.onload = (evt) => {
  //     const bstr = evt.target?.result;
  //     if (!bstr) return;
  //     const wb = XLSX.read(bstr, { type: 'binary' });
  //     const wsname = wb.SheetNames[0];
  //     const ws = wb.Sheets[wsname];
  //     const data = XLSX.utils.sheet_to_json(ws, { header: 1 }) as string[][];
  //     const header = data[0].map((h) => h.toString().toLowerCase().trim());
  //     const nameIdx = header.findIndex((h) => h.includes('name'));
  //     const admissionIdx = header.findIndex((h) => h.includes('admission'));
  //     if (nameIdx === -1 || admissionIdx === -1) {
  //       alert('Excel must have "admission_no" and "name" columns.');
  //       return;
  //     }
  //     const students = data
  //       .slice(1)
  //       .map((row) => ({
  //         admission_no: (row[admissionIdx] || '').toString().trim(),
  //         name: (row[nameIdx] || '').toString().trim(),
  //       }))
  //       .filter((s) => s.admission_no && s.name);
  //     setExcelStudents(students);
  //   };
  //   reader.readAsBinaryString(file);
  // };

  const [excelClassDivGroups, setExcelClassDivGroups] = useState<{ [key: string]: any[] }>({});
  const [expandedRows, setExpandedRows] = useState<{ [key: string]: boolean }>({});
  const [excelHeaders, setExcelHeaders] = useState<string[]>([]);

  const handleExcelUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    const reader = new FileReader();
    reader.onload = (evt) => {
      const bstr = evt.target?.result;
      if (!bstr) return;
      const wb = XLSX.read(bstr, { type: 'binary' });
      const wsname = wb.SheetNames[0];
      const ws = wb.Sheets[wsname];
      const data = XLSX.utils.sheet_to_json(ws, { header: 1 }) as string[][];
      const header = data[0].map((h) => h.toString().trim());
      setExcelHeaders(header);

      const classIdx = header.findIndex((h) => h.toLowerCase() === 'class');
      const divisionIdx = header.findIndex((h) => h.toLowerCase().includes('division'));
      if (classIdx === -1 || divisionIdx === -1) {
        alert('Excel must have "class" and "division" columns.');
        return;
      }

      const students = data
        .slice(1)
        .map((row) => {
          const obj: any = {};
          header.forEach((h, idx) => {
            obj[h] = row[idx] || '';
          });
          obj.class = row[classIdx] || '';
          obj.division = row[divisionIdx] || '';
          return obj;
        })
        .filter((s) => s.class && s.division);

      // Group by class-division
      const groups: { [key: string]: any[] } = {};
      students.forEach((s) => {
        const key = `${s.class}-${s.division}`;
        if (!groups[key]) groups[key] = [];
        groups[key].push(s);
      });
      setExcelClassDivGroups(groups);
      setExpandedRows({});
    };
    reader.readAsBinaryString(file);
  };

  const handleClickOpen = () => {
    setPopup(true);
    setOpen(false);
  };
  // const handleClickClose = () => setPopup(false);

  const handleDrawerOpen = () => setOpen(true);
  const handleDrawerClose = () => {
    setOpen(false);
    setPopup(false);
  };

  const handleSelectAllClick = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      setSelected(PromoteStudentArray);
    } else {
      setSelected([]);
    }
  };

  const handleRowClick = (row: PromoteStudent) => {
    const selectedIndex = selected.indexOf(row);
    let newSelected: PromoteStudent[] = [];

    if (selectedIndex === -1) {
      newSelected = newSelected.concat(selected, row);
    } else if (selectedIndex === 0) {
      newSelected = newSelected.concat(selected.slice(1));
    } else if (selectedIndex === selected.length - 1) {
      newSelected = newSelected.concat(selected.slice(0, -1));
    } else if (selectedIndex > 0) {
      newSelected = newSelected.concat(selected.slice(0, selectedIndex), selected.slice(selectedIndex + 1));
    }

    setSelected(newSelected);
  };

  const isSelected = (row: PromoteStudent) => selected.indexOf(row) !== -1;
  const handleYearChange = (e: SelectChangeEvent) => {
    setAcademicYearFilter(parseInt(YearData.filter((item) => item.accademicId === e.target.value)[0].accademicId, 10));
    // loadTermFeeList({ ...currentTermFeeListRequest, accademicId: parseInt(e.target.value, 10) });
  };
  const handleClassChange = (e: SelectChangeEvent) => {
    const selectedClass = classDataWithAllClass.find((item) => item.className === e.target.value);
    if (selectedClass) {
      setClassFilter(selectedClass);
    }
    // loadStudentList({
    //   ...currentStudentListRequest,
    //   pageNumber: 1,
    //   filters: { ...currentStudentListRequest.filters, classId: selectedClass ? selectedClass.classId : 0 },
    // });
  };

  useEffect(() => {
    if (YearStatus === 'idle') {
      setAcademicYearFilter(defualtYear);
      dispatch(fetchYearList(adminId));
      dispatch(fetchClassList(adminId));
    }
  }, [defualtYear, YearStatus, adminId, dispatch]);

  const isGroupSelected = (groupStudents: any[]) => {
    // Check if all students in this group are selected in PromoteStudentArray
    return groupStudents.every((gs) =>
      PromoteStudentArray.some(
        (ps) =>
          selected.includes(ps) &&
          (ps.admission_no.toString().trim().toLowerCase() ===
            (gs.admission_no || '').toString().trim().toLowerCase() ||
            ps.name.trim().toLowerCase() === (gs.name || '').trim().toLowerCase())
      )
    );
  };

  const handleGroupCheckbox = (event: React.ChangeEvent<HTMLInputElement>, groupStudents: any[], groupKey: string) => {
    const matched = PromoteStudentArray.filter((ps) =>
      groupStudents.some(
        (gs) =>
          ps.admission_no.toString().trim().toLowerCase() === (gs.admission_no || '').toString().trim().toLowerCase() ||
          ps.name.trim().toLowerCase() === (gs.name || '').trim().toLowerCase()
      )
    );

    // Find unmatched students in this group
    const unmatchedCount = groupStudents.filter(
      (gs) =>
        !PromoteStudentArray.some(
          (ps) =>
            ps.admission_no.toString().trim().toLowerCase() ===
              (gs.admission_no || '').toString().trim().toLowerCase() ||
            ps.name.trim().toLowerCase() === (gs.name || '').trim().toLowerCase()
        )
    ).length;

    if (event.target.checked) {
      setCheckedGroups((prev) => ({ ...prev, [groupKey]: true }));
      setSelected((prev) => [...prev, ...matched.filter((m) => !prev.includes(m))]);
      if (unmatchedCount > 0) {
        setSnackbarMsg(`There are ${unmatchedCount} unmatched students in this group.`);
        setSnackbarOpen(true);
      }
    } else {
      setCheckedGroups((prev) => {
        const next = { ...prev };
        delete next[groupKey];
        return next;
      });
      setSelected((prev) =>
        prev.filter(
          (s) =>
            !matched.some(
              (m) =>
                s.admission_no.toString().trim().toLowerCase() === m.admission_no.toString().trim().toLowerCase() ||
                s.name.trim().toLowerCase() === m.name.trim().toLowerCase()
            )
        )
      );
    }
  };

  return (
    <Page title="Promote Student">
      <PromoteStudentsRoot>
        {/* <Card elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Typography variant="h6" fontSize={17}>
            Promote Students
          </Typography>
          <Divider />
          <Grid pb={4} pt={2} container spacing={3}>
            <Grid item lg={6} xs={12}>
              <Typography variant="h6">From</Typography>
              <Grid pb={4} pt={2} container spacing={3}>
                <Grid item xs={12}>
                  <Typography variant="h6" fontSize={14}>
                    Academic Year
                  </Typography>
                  <Autocomplete
                    options={YEAR_SELECT}
                    renderInput={(params) => <TextField {...params} placeholder="Select" />}
                  />
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="h6" fontSize={14}>
                    Class
                  </Typography>
                  <Autocomplete
                    options={CLASS_SELECT}
                    renderInput={(params) => <TextField {...params} placeholder="Select" />}
                  />
                </Grid>
              </Grid>
            </Grid>
            <Grid item lg={6} xs={12}>
              <Typography variant="h6">To</Typography>
              <Grid pb={4} pt={2} container spacing={3}>
                <Grid item xs={12}>
                  <Typography variant="h6" fontSize={14}>
                    Academic Year
                  </Typography>
                  <Autocomplete
                    options={YEAR_SELECT}
                    renderInput={(params) => <TextField {...params} placeholder="Select" />}
                  />
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="h6" fontSize={14}>
                    Class
                  </Typography>
                  <Autocomplete
                    options={CLASS_SELECT}
                    renderInput={(params) => <TextField {...params} placeholder="Select" />}
                  />
                </Grid>
              </Grid>
            </Grid>
          </Grid>
          <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' } }}>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="secondary" sx={{ fontFamily: typography.fontFamily }}>
                Cancel
              </Button>
              <Button onClick={handleClickOpen} variant="contained" color="primary">
                Promote
              </Button>
            </Stack>
          </Box>
        </Card> */}

        {/* Promote Selected Students */}

        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Box display="flex" pb={0.5} justifyContent="space-between">
            <Typography variant="h6" fontSize={17}>
              Promote Selected Students
            </Typography>
            <Stack direction="row" alignItems="center">
              <Stack direction="row" spacing={2} alignItems="center" mb={2}>
                <Button variant="outlined" component="label">
                  Upload Excel
                  <input type="file" accept=".xlsx,.xls" hidden onChange={handleExcelUpload} />
                </Button>
                {/* <Typography variant="body2" color="text.secondary">
                  Upload an Excel file w  ith a "Student Name" column to auto-select students.
                </Typography> */}
              </Stack>

              {excelStudents.length > 0 && (
                <Box mt={2}>
                  <Typography variant="subtitle2" color="primary">
                    Excel Students:
                  </Typography>
                  <Stack
                    spacing={0.5}
                    sx={{ maxHeight: 120, overflow: 'auto', border: '1px solid #eee', borderRadius: 1, p: 1 }}
                  >
                    {excelStudents.map((s, idx) => (
                      <Typography key={idx} fontSize={14}>
                        {s.admission_no} - {s.name}
                      </Typography>
                    ))}
                  </Stack>
                </Box>
              )}
            </Stack>
          </Box>
          <Divider />

          {Object.keys(excelClassDivGroups).length > 0 && (
            <Paper
              sx={{
                mb: 5,
                border: `1px solid #e8e8e9`,
                overflow: 'auto',
                // '&::-webkit-scrollbar': {
                //   width: 0,
                // },
              }}
            >
              <TableContainer
                sx={{
                  height: 'calc(100vh - 300px)',
                }}
              >
                <Table stickyHeader>
                  <TableHead>
                    <TableRow>
                      <TableCell width={80} />
                      <TableCell width={50} />
                      <TableCell>Class - Division</TableCell>
                      <TableCell>Students Count</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {Object.entries(excelClassDivGroups).map(([key, students]) => (
                      <React.Fragment key={key}>
                        <TableRow>
                          <TableCell>
                            <IconButton
                              size="small"
                              onClick={() =>
                                setExpandedRows((prev) => ({
                                  ...prev,
                                  [key]: !prev[key],
                                }))
                              }
                            >
                              {expandedRows[key] ? <IoIosArrowUp /> : <MdAdd />}
                            </IconButton>
                          </TableCell>
                          <TableCell>
                            <Checkbox
                              checked={checkedGroups[key]}
                              indeterminate={
                                // Some but not all students in this group are selected
                                PromoteStudentArray.some((ps) =>
                                  students.some(
                                    (gs) =>
                                      (ps.admission_no.toString().trim().toLowerCase() ===
                                        (gs.admission_no || '').toString().trim().toLowerCase() ||
                                        ps.name.trim().toLowerCase() === (gs.name || '').trim().toLowerCase()) &&
                                      selected.includes(ps)
                                  )
                                ) && !isGroupSelected(students)
                              }
                              onChange={(e) => handleGroupCheckbox(e, students, key)}
                              sx={{ mr: 1 }}
                            />
                          </TableCell>
                          <TableCell>{key}</TableCell>
                          <TableCell>{students.length}</TableCell>
                        </TableRow>
                        {expandedRows[key] && (
                          <TableRow>
                            <TableCell colSpan={4} sx={{ background: '#fafafa', p: 0 }}>
                              <Collapse in={expandedRows[key]}>
                                <Table size="small">
                                  <TableHead>
                                    <TableRow>
                                      <TableCell width={50}> </TableCell>
                                      {excelHeaders.map((h, idx) => (
                                        <TableCell sx={{ fontSize: 12 }} key={idx}>
                                          {h}
                                        </TableCell>
                                      ))}
                                      <TableCell width={50}> </TableCell>
                                    </TableRow>
                                  </TableHead>
                                  <TableBody>
                                    {students.map((s, idx) => {
                                      const isMatched = PromoteStudentArray.some(
                                        (ps) =>
                                          ps.admission_no.toString().trim().toLowerCase() ===
                                            (s.admission_no || '').toString().trim().toLowerCase() ||
                                          ps.name.trim().toLowerCase() === (s.name || '').trim().toLowerCase()
                                      );
                                      const isGroupChecked = checkedGroups[key];
                                      return (
                                        <TableRow
                                          key={idx}
                                          sx={{
                                            backgroundColor: isGroupChecked
                                              ? isMatched
                                                ? (theme) => theme.palette.primary.lighter
                                                : (theme) => theme.palette.error.main
                                              : undefined,
                                          }}
                                        >
                                          {/* Icon cell */}
                                          <TableCell sx={{ width: 40 }}>
                                            {isGroupChecked ? (
                                              isMatched ? (
                                                <SuccessIcon color="success" sx={{ verticalAlign: 'middle' }} />
                                              ) : (
                                                <ErrorIcon sx={{ color: 'white', verticalAlign: 'middle' }} />
                                              )
                                            ) : null}
                                          </TableCell>
                                          {/* Data cells */}
                                          {excelHeaders.map((h, i) => (
                                            <TableCell
                                              key={i}
                                              sx={{
                                                fontSize: 12,
                                                color: isGroupChecked
                                                  ? isMatched
                                                    ? (theme) => theme.palette.common.black
                                                    : (theme) => theme.palette.common.white
                                                  : undefined,
                                              }}
                                            >
                                              {s[h]}
                                            </TableCell>
                                          ))}
                                          <TableCell sx={{ width: 120 }}>
                                            {isGroupChecked ? (
                                              isMatched ? (
                                                ''
                                              ) : (
                                                <Button
                                                  variant="outlined"
                                                  sx={{
                                                    backgroundColor: 'white',
                                                    '&:hover': { backgroundColor: 'white' },
                                                  }}
                                                  size="small"
                                                >
                                                  Add Student
                                                </Button>
                                              )
                                            ) : null}
                                          </TableCell>
                                        </TableRow>
                                      );
                                    })}
                                  </TableBody>
                                </Table>
                              </Collapse>
                            </TableCell>
                          </TableRow>
                        )}
                      </React.Fragment>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Paper>
          )}

          {/* {showFilter === false ? (
            <Tooltip title="Search">
              <IconButton aria-label="delete" color="primary" sx={{ ml: 1 }} onClick={() => setShowFilter((x) => !x)}>
                <SearchIcon />
              </IconButton>
            </Tooltip>
          ) : (
            <IconButton aria-label="delete" color="primary" sx={{ ml: 1 }} onClick={() => setShowFilter((x) => !x)}>
              <IoIosArrowUp />
            </IconButton>
          )} */}
          <Stack direction="row" gap={3}>
            <Stack sx={{ width: { xs: '100%', md: '60%' } }}>
              <Typography variant="subtitle1">From</Typography>
            </Stack>
            <Stack sx={{ width: { xs: '100%', md: '40%' } }}>
              <Typography variant="subtitle1">To</Typography>
            </Stack>
          </Stack>
          <Stack direction="row" gap={3}>
            <Collapse in={showFilter} sx={{ width: { xs: '100%', md: '60%' } }}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={3} alignItems="end">
                  <Grid item xxl={3} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Select
                        labelId="academicYearFilter"
                        id="academicYearFilterSelect"
                        value={academicYearFilter.toString()}
                        onChange={handleYearChange}
                        placeholder="Select Year"
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Year
                        </MenuItem>
                        {YearData.map((opt) => (
                          <MenuItem key={opt.accademicId} value={opt.accademicId}>
                            {opt.accademicTime}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={3} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Select
                        labelId="classFilter"
                        id="classFilterSelect"
                        value={className}
                        onChange={handleClassChange}
                        placeholder="Select"
                        MenuProps={{
                          PaperProps: {
                            style: {
                              maxHeight: '250px', // Adjust the value to your desired height
                            },
                          },
                        }}
                      >
                        {classDataWithAllClass?.map((item: ClassListInfo) => (
                          <MenuItem key={item.classId} value={item.className}>
                            {item.className}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item lg={1}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button type="reset" variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Collapse in={showFilter} sx={{ width: { xs: '100%', md: '40%' } }}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={3} alignItems="end">
                  <Grid item xxl={4} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Select
                        labelId="academicYearFilter"
                        id="academicYearFilterSelect"
                        value={academicYearFilter.toString()}
                        onChange={handleYearChange}
                        placeholder="Select Year"
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Year
                        </MenuItem>
                        {YearData.map((opt) => (
                          <MenuItem key={opt.accademicId} value={opt.accademicId}>
                            {opt.accademicTime}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={4} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Select
                        labelId="classFilter"
                        id="classFilterSelect"
                        value={className}
                        onChange={handleClassChange}
                        placeholder="Select"
                        MenuProps={{
                          PaperProps: {
                            style: {
                              maxHeight: '250px', // Adjust the value to your desired height
                            },
                          },
                        }}
                      >
                        {classDataWithAllClass?.map((item: ClassListInfo) => (
                          <MenuItem key={item.classId} value={item.className}>
                            {item.className}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item lg={1}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button type="reset" variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>
          </Stack>

          <Stack sx={{ width: '100%', flexDirection: { xs: 'column', md: 'row' } }} gap={3}>
            <Paper
              sx={{
                border: `1px solid #e8e8e9`,
                width: { xs: '100%', md: '60%' },
                overflow: 'auto',
                '&::-webkit-scrollbar': {
                  width: 0,
                },
              }}
            >
              <TableContainer
                sx={{
                  height: 'calc(100vh - 300px)',
                  width: { xs: '700px', md: '100%' },
                  '&::-webkit-scrollbar': {
                    width: 0,
                  },
                }}
              >
                <Table stickyHeader aria-label="simple table">
                  <TableHead>
                    <TableRow>
                      <TableCell>
                        <Checkbox
                          indeterminate={selected.length > 0 && selected.length < PromoteStudentArray.length}
                          checked={selected.length === PromoteStudentArray.length}
                          onChange={handleSelectAllClick}
                          inputProps={{ 'aria-label': 'select all rows' }}
                        />
                      </TableCell>
                      <TableCell>Sl.No</TableCell>
                      <TableCell>Admission No</TableCell>
                      <TableCell>Student Name</TableCell>
                      <TableCell>Class</TableCell>
                      <TableCell>Academic Year</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {PromoteStudentArray.map((stdnt) => {
                      const isRowSelected = isSelected(stdnt);
                      return (
                        <TableRow
                          role="checkbox"
                          aria-checked={isRowSelected}
                          tabIndex={-1}
                          selected={isRowSelected}
                          hover
                          key={stdnt.id}
                          onClick={() => handleRowClick(stdnt)}
                        >
                          <TableCell>
                            <Checkbox
                              checked={isRowSelected}
                              inputProps={{ 'aria-labelledby': `checkbox-${stdnt.slNo}` }}
                            />
                          </TableCell>
                          <TableCell>{stdnt.slNo}</TableCell>
                          <TableCell>{stdnt.admission_no}</TableCell>
                          <TableCell>
                            <Stack direction="row">
                              <Avatar src="" sx={{ mr: 2 }} />
                              <Typography pt={0.7} fontSize={15}>
                                {stdnt.name}
                              </Typography>
                            </Stack>
                          </TableCell>
                          <TableCell>{stdnt.class}</TableCell>
                          <TableCell>{stdnt.year}</TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </TableContainer>
            </Paper>

            {/* ---------------- */}
            <Paper
              sx={{
                border: '1px solid #e8e8e9',
                overflow: 'auto',
                height: '100%',
                width: { xs: '100%', md: '40%' },
                '&::-webkit-scrollbar': {
                  width: 0,
                  height: 0,
                },
              }}
            >
              <Typography variant="h6" p={2} fontSize={17}>
                Promote List
              </Typography>
              {selected && selected?.length > 0 && (
                <TableContainer
                  sx={{
                    height: 'calc(100vh - 300px)',
                    width: { xs: '450px', md: '100%' },
                  }}
                >
                  <Table stickyHeader>
                    <TableHead>
                      <TableRow>
                        <TableCell>Admission No</TableCell>
                        <TableCell>Student Name</TableCell>
                        <TableCell>Class</TableCell>
                        <TableCell> </TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {selected?.map((row) => (
                        <TableRow>
                          <TableCell sx={{ fontWeight: 600 }}>{row.admission_no}</TableCell>
                          <TableCell>
                            <Typography className="student_name2"> {row.name}</Typography>
                          </TableCell>
                          <TableCell>{row.class}</TableCell>
                          <TableCell align="center">
                            <IconButton onClick={() => handleRowClick(row)}>
                              <RemoveCircleOutlineIcon sx={{ color: 'red' }} />
                            </IconButton>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              )}
              {selected && selected?.length === 0 && (
                <Stack
                  direction="column"
                  display="flex"
                  justifyContent="center"
                  alignItems="center"
                  width="100%"
                  sx={{ height: 'calc(100vh - 300px)' }}
                >
                  <Box pb={2}>
                    <img src={NotSuccess} alt="" />
                  </Box>

                  <Typography textAlign="center" variant="body2">
                    Not selected students.
                  </Typography>
                </Stack>
              )}
            </Paper>
          </Stack>
          <Box
            display="flex"
            sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: { xs: 5, md: 3 } }}
          >
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="secondary">
                Cancel
              </Button>
              <Button onClick={handleDrawerOpen} variant="contained" color="primary">
                Promote
              </Button>
            </Stack>
          </Box>
        </Card>
        <Popup
          size="xs"
          state={popup}
          onClose={handleDrawerClose}
          popupContent={<SuccessMessage jsonIcon={successIcon} loop={false} message="Students Promoted Successfully" />}
        />
        <TemporaryDrawer
          onClose={handleDrawerClose}
          Title="Promote To:"
          state={open}
          DrawerContent={
            <Box>
              <Typography variant="h6" fontSize={12} mt={5}>
                Academic Year
              </Typography>
              <Autocomplete
                options={YEAR_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select Year" />}
              />
              <Typography variant="h6" fontSize={12} mt={3}>
                Class
              </Typography>
              <Autocomplete
                options={CLASS_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select Class" />}
              />
              <Stack spacing={2} direction="row" mt={5}>
                <Button onClick={handleDrawerClose} fullWidth variant="contained" color="secondary">
                  Cancel
                </Button>
                <Button onClick={handleClickOpen} fullWidth variant="contained" color="primary">
                  Confirm
                </Button>
              </Stack>
            </Box>
          }
        />
      </PromoteStudentsRoot>
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={4000}
        onClose={() => setSnackbarOpen(false)}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <MuiAlert onClose={() => setSnackbarOpen(false)} severity="warning" sx={{ width: '100%' }}>
          {snackbarMsg}
        </MuiAlert>
      </Snackbar>
    </Page>
  );
}

export default PromoteStudents;
