/* eslint-disable jsx-a11y/alt-text */
import React, { useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  useTheme,
  IconButton,
  Collapse,
  Tooltip,
  FormControl,
  Chip,
  Radio,
} from '@mui/material';
import styled from 'styled-components';
import { CLASS_SELECT, STATUS_SELECT, YEAR_SELECT } from '@/config/Selection';
import SearchIcon from '@mui/icons-material/Search';
import Popup from '@/components/shared/Popup/Popup';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';

import { MdAdd } from 'react-icons/md';
import { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import DateSelect from '@/components/shared/Selections/DateSelect';
import MenuEditDelete from '@/components/shared/Selections/MenuEditDelete';
import { <PERSON>i<PERSON><PERSON> } from 'react-icons/bi';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';

export const data = [
  {
    createdBy: 'Passdaily',
    class: 'VII-B',
    meetingTitle: 'Daily Class',
    meetingID: 'IDGHDGB',
    academicYear: '2023-2024',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Active',
  },
  {
    createdBy: 'Passdaily',
    class: 'VI-C',
    academicYear: '2023-2024',
    meetingID: 'IDGHDGB',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Inactive',
  },
  {
    createdBy: 'Passdaily',
    class: 'XII-B',
    meetingTitle: 'Peter',
    academicYear: '2023-2024',
    meetingID: 'IDGHDGB',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Active',
  },
  {
    createdBy: 'Passdaily',
    class: 'V-A',
    meetingTitle: 'Daily Class Mic',
    academicYear: '2023-2024',
    meetingID: 'IDGHDGB',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Inactive',
  },
  {
    createdBy: 'Passdaily',
    class: 'II-B',
    meetingTitle: 'john',
    academicYear: '2023-2024',
    meetingID: 'IDGHDGB',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Active',
  },
  {
    createdBy: 'Passdaily',
    class: 'XII-C',
    meetingTitle: 'Micheal',
    academicYear: '2023-2024',
    meetingID: 'IDGHDGB',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Active',
  },
  {
    createdBy: 'Passdaily',
    class: 'I-B',
    meetingTitle: 'jack',
    academicYear: '2023-2024',
    meetingID: 'IDGHDGB',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Active',
  },
  {
    createdBy: 'Passdaily',
    class: 'VII-A',
    meetingTitle: 'Daily Class',
    academicYear: '2023-2024',
    meetingID: 'IDGHDGB',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Inactive',
  },
  {
    createdBy: 'Passdaily',
    class: 'VI-C',
    meetingTitle: 'Daily Class',
    academicYear: '2023-2024',
    meetingID: 'IDGHDGB',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Active',
  },
  {
    createdBy: 'Passdaily',
    class: 'X-B',
    meetingTitle: 'Daily Class',
    academicYear: '2023-2024',
    meetingID: 'IDGHDGB',
    meetingPassword: '8652154',
    meetingLink: 'meet.google.com/eum-jqes-eib-eum-jqes-eib',
    meetingStartDate: '12-11-2023 01:30PM',
    status: 'Inactive',
  },
];

const MaptoProgressCardRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  .student_card {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  }

  .Card {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 40px);
      flex-grow: 1;

      .card-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: auto;
        ::-webkit-scrollbar {
          width: 0px;
        }

        .MuiTableContainer-root {
          max-height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

function MaptoProgressCard() {
  const theme = useTheme();
  // const { themeMode } = useSettings();
  // const isLight = themeMode === 'light';
  const [showFilter, setShowFilter] = useState(false);
  const [Delete, setDelete] = useState(false);
  const [createPopup, setCreatePopup] = useState(false);

  // const [changeView, setChangeView] = useState(false);
  // const [students, setStudents] = useState(StudentInfoData);
  // const [open, setOpen] = useState(false);
  // const [openNew, setOpenNew] = useState(false);
  // const [showNewMultiple, setShowNewMultiple] = useState(false);
  // const [selectedStudent, setSelectedStudent] = useState(null);

  // const [updatedData, setData] = useState(data);

  // const handleStatusChange = (index) => {
  //   const copyData = [...updatedData];
  //   copyData[index].status = copyData[index].status === 'Active' ? 'Inactive' : 'Active';
  //   setData(copyData);
  // };

  // const handleToggle = () => {
  //   setChangeView((prevChangeView) => !prevChangeView);
  // };

  const handleClickDelete = () => setDelete(true);
  const handleClickCloseDelete = () => setDelete(false);
  // const handlePageChange = useCallback((event: unknown, newPage: number) => {
  //   // loadClassList({ ...currentClassListRequest, pageNumber: newPage + 1 });
  // }, []);

  // const handleChangeRowsPerPage = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
  //   const newPageSize = +event.target.value;
  //   // loadClassList({ ...currentClassListRequest, pageNumber: 1, pageSize: newPageSize });
  // }, []);

  // const pageProps = useMemo(
  //   () => ({
  //     rowsPerPageOptions: [10, 20, 30],
  //     pageNumber: 1,
  //     pageSize: 10,
  //     totalRecords: 100,
  //     onPageChange: handlePageChange,
  //     onRowsPerPageChange: handleChangeRowsPerPage,
  //   }),
  //   [handleChangeRowsPerPage, handlePageChange]
  // );

  // const handleClose = () => {
  //   setOpen(false);
  // };
  // const handleOpenNew = () => {
  //   setOpenNew(true);
  // };

  // const handleCloseNew = () => {
  //   setOpenNew(false);
  // };
  // const handleToggleNewMultiple = () => {
  //   setShowNewMultiple((prevState) => !prevState);
  // };

  // const handleUpdate = (id: number, updatedData: Student) => {
  //   const updatedStudents = students.map((student) => (student.id === id ? { ...student, ...updatedData } : student));
  //   setStudents(updatedStudents);
  //   setOpen(false);
  // };

  // const getRowKey = useCallback((row: any) => row.examId, []);

  const MaptoProgressCardColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'keyTitle',
        headerLabel: 'Key Title',
        renderCell: (row) => {
          return (
            <Chip
              size="small"
              label={
                <>
                  <BiKey />
                  {` ${row.meetingTitle}`}
                </>
              }
              sx={{
                border: 0,
                mt: 0.5,
                color: `${row.status === 'Inactive' ? theme.palette.info.main : theme.palette.primary.main}`,
              }}
            />
          );
        },
      },
      {
        name: 'createdBy',
        dataKey: 'createdBy',
        headerLabel: 'Username',
      },
      {
        name: 'password',
        dataKey: 'meetingPassword',
        headerLabel: ' Password',
      },
      {
        name: 'createdBy',
        dataKey: 'createdBy',
        headerLabel: 'Created By',
      },
    ],
    [theme]
  );

  //   const renderContent = () => {
  //     return students.map((student, rowIndex) => (
  //       <Card key={student.id}>
  //         {MaptoProgressCardColumns.map((column) => (
  //           <Typography key={column.name}>{student[column.dataKey]}</Typography>
  //         ))}
  //       </Card>
  //     ));
  //   };

  // const content: ReactNode = data.map((item, rowIndex: number) => {
  //   let cellData: ReactNode = null;

  //   return (
  //     <Typography key={column.name} variant="subtitle1" mb={1} fontSize={13}>
  //       <b>{cellData}</b>
  //     </Typography>
  //   );
  // });

  // Content = content;

  return (
    <Page title="Schedule List">
      <MaptoProgressCardRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={20} width="100%">
              Map to Progress Card
            </Typography>
            <Box pb={1} sx={{ flexShrink: 0 }}>
              <Tooltip title="Search">
                <IconButton aria-label="" color="primary" sx={{ mr: 1 }} onClick={() => setShowFilter((x) => !x)}>
                  <SearchIcon />
                </IconButton>
              </Tooltip>
              <Button
                sx={{ borderRadius: '20px' }}
                size="small"
                variant="outlined"
                onClick={() => setCreatePopup(true)}
              >
                <MdAdd size="20px" /> Create
              </Button>
            </Box>
          </Stack>
          <Stack>
            <Divider />
          </Stack>
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={2}>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Autocomplete
                        options={YEAR_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select year" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Autocomplete
                        options={CLASS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select class" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Meeting Title
                      </Typography>
                      <TextField fullWidth placeholder="Enter title" />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Select Date
                      </Typography>
                      <DateSelect />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" md={6} xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', lg: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Status
                      </Typography>
                      <Autocomplete
                        options={STATUS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select" />}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item lg={1} xs={12}>
                    <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                      <Button variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                      {/* <Button variant="contained" color="primary" fullWidth>
                          Search
                        </Button> */}
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Box className="card-container" my={2}>
              <Grid container spacing={2}>
                {data.map((student, rowIndex) => (
                  <Grid item xl={4} lg={6} md={12} sm={6} xs={12}>
                    <Card
                      className="student_card"
                      sx={{ p: 2, boxShadow: 0, border: `1px solid ${theme.palette.grey[300]}` }}
                    >
                      <Box display="flex" flexDirection="column">
                        <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                          <Radio />
                          <Stack direction="row" alignItems="center" gap={1} ml={1}>
                            <Chip
                              size="small"
                              label={student.status}
                              variant="outlined"
                              color={student.status === 'Active' ? 'success' : 'error'}
                              sx={{ border: '0px' }}
                            />
                            <MenuEditDelete
                              Edit={() => {
                                return 0;
                              }}
                              Delete={handleClickDelete}
                            />
                          </Stack>
                        </Box>

                        {MaptoProgressCardColumns.map((item) => (
                          <Stack direction="row" ml={1}>
                            <Grid container>
                              <Grid item lg={5} xs={6}>
                                <Typography key={item.name} variant="subtitle1" fontSize={13} mr={2}>
                                  {item.headerLabel}
                                </Typography>
                              </Grid>
                              <Grid item lg={7} xs={6} mb={0} mt="auto">
                                <Typography
                                  variant="h6"
                                  fontSize={13}
                                  color={item.dataKey === 'meetingLink' ? theme.palette.info.main : ''}
                                >
                                  {item.dataKey
                                    ? `:${' '}${(student as { [key: string]: any })[item.dataKey] ?? ''}`
                                    : item && item.renderCell && item.renderCell(student, rowIndex)}
                                </Typography>
                              </Grid>
                            </Grid>
                          </Stack>
                        ))}
                      </Box>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Box>
          </div>
        </Card>
      </MaptoProgressCardRoot>
      <Popup
        size="xs"
        state={Delete}
        onClose={handleClickCloseDelete}
        popupContent={<DeleteMessage message="Are you sure want to delete?" />}
      />
    </Page>
  );
}

export default MaptoProgressCard;
