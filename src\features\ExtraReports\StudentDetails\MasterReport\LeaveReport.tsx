/* eslint-disable jsx-a11y/alt-text */
import { useCallback, useMemo } from 'react';
import { Box, Grid, Card, useTheme, Chip, Paper } from '@mui/material';
import styled from 'styled-components';
import useSettings from '@/hooks/useSettings';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { LeaveInformationType, LeaveNoteReportType } from '@/types/Reports';

const LeaveInformationData = [
  {
    id: 1,
    absentDate: '14-Dec-2023',
    absentMonth: 'December',
  },
  {
    id: 2,
    absentMonth: 'December',
    absentDate: '12-Dec-2023',
  },
  {
    id: 3,
    absentMonth: 'July',
    absentDate: '10-Jul-2023',
  },
  {
    id: 4,
    absentMonth: 'December',
    absentDate: '09-Dec-2023',
  },
  {
    id: 5,
    absentMonth: 'October',
    absentDate: '08-Oct-2023',
  },
  {
    id: 6,
    absentMonth: 'December',
    absentDate: '02-Dec-2023',
  },
  {
    id: 7,
    absentMonth: 'December',
    absentDate: '01-Dec-2023',
  },
];
const LeaveNoteData = [
  {
    id: 1,
    subject: 'Vaction',
    description: 'Going To trip',
    leaveDate: 'Dec 26 to Jan 01,2023',
    status: 'Approved',
  },
  {
    id: 2,
    subject: 'Vaction',
    description: 'Going To trip',
    leaveDate: 'Dec 26 to Jan 01,2023',
    status: 'Requested',
  },
  {
    id: 3,
    subject: 'Vaction',
    description: 'Going To trip',
    leaveDate: 'Dec 26 to Jan 01,2023',
    status: 'Approved',
  },

  {
    id: 4,
    subject: 'Vaction',
    description: 'Going To trip',
    leaveDate: 'Dec 26 to Jan 01,2023',
    status: 'Approved',
  },
  {
    id: 5,
    subject: 'Vaction',
    description: 'Going To trip',
    leaveDate: 'Dec 26 to Jan 01,2023',
    status: 'Approved',
  },
  {
    id: 6,
    subject: 'Vaction',
    description: 'Going To trip',
    leaveDate: 'Dec 26 to Jan 01,2023',
    status: 'Requested',
  },
  {
    id: 7,
    subject: 'Vaction',
    description: 'Going To trip',
    leaveDate: 'Dec 26 to Jan 01,2023',
    status: 'Approved',
  },
];

const LeaveReportRoot = styled.div`
  .LeaveinfoCard {
    display: flex;
    flex-direction: column;
    height: 350px;
    background-color: ${(props) => (props.theme.themeMode === 'light' ? '#f7f3fb' : props.theme.palette.grey[900])};
    /* border: 1px solid ${(props) => props.theme.palette.grey[300]}; */
  }
  .LeaveNoteCard {
    display: flex;
    flex-direction: column;
    height: 350px;
    background-color: ${(props) => (props.theme.themeMode === 'light' ? '#f5f7f9' : props.theme.palette.grey[900])};
    /* border: 1px solid ${(props) => props.theme.palette.grey[300]}; */
  }
  .card-main-body {
    display: flex;
    flex-direction: column;
    min-height: calc(100% - 5px);
    flex-grow: 1;

    .card-table-container {
      flex-grow: 1;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      background-color: ${(props) => (props.theme.themeMode === 'light' ? '#f7f3fb' : props.theme.palette.grey[900])};
    }
    .card-table-container2 {
      flex-grow: 1;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      background-color: ${(props) => (props.theme.themeMode === 'light' ? '#f5f7f9' : props.theme.palette.grey[900])};
    }
  }
`;

type LeaveReportsProps = {
  idLeaveInfo: string;
  idLeaveNote: string;
};

function LeaveReport({ idLeaveInfo, idLeaveNote }: LeaveReportsProps) {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const tableStyles1 = { backgroundColor: isLight ? '#f7f3fb' : theme.palette.grey[900] };
  const tableStyles2 = {
    backgroundColor: isLight ? '#f5f7f9' : theme.palette.grey[900],
  };

  const getRowKeyLeaveInformation = useCallback((row: LeaveInformationType) => row.id, []);
  const getRowKeyLeaveNoteReport = useCallback((row: LeaveNoteReportType) => row.id, []);

  const LeaveInformationColumns: DataTableColumn<LeaveInformationType>[] = useMemo(
    () => [
      {
        name: 'absentMonth',
        dataKey: 'absentMonth',
        headerLabel: 'Absent Date',
      },
      {
        name: 'absentDate',
        dataKey: 'absentDate',
      },
    ],
    []
  );
  const LeaveNoteColumns: DataTableColumn<LeaveNoteReportType>[] = useMemo(
    () => [
      {
        name: 'subject',
        dataKey: 'subject',
        headerLabel: 'Subject',
      },
      {
        name: 'description',
        dataKey: 'description',
        headerLabel: 'Description',
      },
      {
        name: 'leaveDate',
        dataKey: 'leaveDate',
        headerLabel: 'Leave Date',
      },
      {
        name: 'status',
        headerLabel: 'Status',
        renderCell: (row) => {
          return (
            <Chip
              variant="outlined"
              sx={{ height: 25 }}
              label={row.status}
              color={row.status === 'Approved' ? 'success' : 'warning'}
            />
          );
        },
      },
    ],
    []
  );

  return (
    <LeaveReportRoot>
      <Grid container spacing={{ xs: 0, lg: 3 }}>
        <Grid id={idLeaveInfo} item xl={3.5} lg={4.5} xs={12}>
          <Card className="LeaveinfoCard" sx={{ mt: 3, boxShadow: 0, px: { xs: 3, md: 2 }, py: { xs: 2, md: 2 } }}>
            <div className="card-main-body">
              <Box>
                <Chip
                  label="Leave Information"
                  variant="filled"
                  sx={{
                    height: 25,
                    background: isLight ? theme.palette.common.black : theme.palette.common.white,
                    color: isLight ? theme.palette.common.white : theme.palette.common.black,
                  }}
                />
              </Box>
              <Paper className="card-table-container">
                <DataTable
                  columns={LeaveInformationColumns}
                  data={LeaveInformationData}
                  getRowKey={getRowKeyLeaveInformation}
                  fetchStatus="success"
                  tableStyles={tableStyles1}
                />
              </Paper>
            </div>
          </Card>
        </Grid>
        <Grid id={idLeaveNote} item xl={8.5} lg={7.5} xs={12}>
          <Card className="LeaveNoteCard" sx={{ mt: 3, boxShadow: 0, px: { xs: 3, md: 2 }, py: { xs: 2, md: 2 } }}>
            <div className="card-main-body">
              <Box>
                <Chip
                  label="Leave Note"
                  variant="filled"
                  sx={{
                    height: 25,
                    background: isLight ? theme.palette.common.black : theme.palette.common.white,
                    color: isLight ? theme.palette.common.white : theme.palette.common.black,
                  }}
                />
              </Box>
              <Paper className="card-table-container2">
                <DataTable
                  columns={LeaveNoteColumns}
                  data={LeaveNoteData}
                  getRowKey={getRowKeyLeaveNoteReport}
                  fetchStatus="success"
                  tableStyles={tableStyles2}
                />
              </Paper>
            </div>
          </Card>
        </Grid>
      </Grid>
    </LeaveReportRoot>
  );
}

export default LeaveReport;
