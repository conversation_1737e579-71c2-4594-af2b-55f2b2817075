/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  FormControl,
  FormControlLabel,
  Radio,
  RadioGroup,
} from '@mui/material';
import styled from 'styled-components';
import { YEAR_SELECT } from '@/config/Selection';
import typography from '@/theme/typography';
import Popup from '@/components/shared/Popup/Popup';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import DateSelect from '@/components/shared/Selections/DateSelect';
import { CategorydummyData } from '@/features/PaymentDetails/CategoryList';
import { SubCategorydummyData } from '@/features/PaymentDetails/SubCategoryList';

const CreatePaymentRoot = styled.div`
  min-height: calc(100vh - ${TOP_BAR_HEIGHT});
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  h6 {
    font-weight: 600;
  }
  .MuiTextField-root:hover .MuiInputBase-input {
    cursor: text;
  }
`;

function CreatePayment() {
  const [popup, setPopup] = React.useState(false);

  const handleClickOpen = () => setPopup(true);
  const handleClickClose = () => setPopup(false);

  return (
    <Page title="Create Payment">
      <CreatePaymentRoot>
        <Card elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Typography variant="h6" fontSize={17}>
            Add New Payment
          </Typography>
          <Divider />
          <form noValidate>
            <Grid pb={4} pt={2} container spacing={5}>
              <Grid item lg={6} xs={12}>
                <FormControl sx={{ minWidth: { xs: '100%', xl: 430, md: 300 } }}>
                  <Typography variant="h6" fontSize={14}>
                    Academic Year
                  </Typography>
                  <Autocomplete
                    options={YEAR_SELECT}
                    renderInput={(params) => <TextField {...params} placeholder="Select Year" />}
                  />
                </FormControl>
              </Grid>
              <Grid item lg={6} xs={12}>
                <FormControl sx={{ minWidth: { xs: '100%', xl: 430, md: 300 } }}>
                  <Typography variant="h6" fontSize={14}>
                    Category
                  </Typography>
                  <Autocomplete
                    options={CategorydummyData.map((item) => item.name)}
                    renderInput={(params) => <TextField {...params} placeholder="Select Category" />}
                  />
                </FormControl>
              </Grid>
              <Grid item lg={6} xs={12}>
                <FormControl sx={{ minWidth: { xs: '100%', xl: 430, md: 300 } }}>
                  <Typography variant="h6" fontSize={14}>
                    Sub Category
                  </Typography>
                  <Autocomplete
                    options={SubCategorydummyData.map((item) => item.name)}
                    renderInput={(params) => <TextField {...params} placeholder="Select Sub Category" />}
                  />
                </FormControl>
              </Grid>
              <Grid item lg={6} xs={12}>
                <FormControl sx={{ minWidth: { xs: '100%', xl: 430, md: 300 } }}>
                  <Typography variant="h6" fontSize={14}>
                    Staff
                  </Typography>
                  <Autocomplete
                    options={['Staff1', 'Staff2', 'Staff3', 'Admin']}
                    renderInput={(params) => <TextField {...params} placeholder="Select Staff" />}
                  />
                </FormControl>
              </Grid>
              <Grid item lg={6} xs={12}>
                <FormControl sx={{ minWidth: { xs: '100%', xl: 430, md: 300 } }}>
                  <Typography variant="h6" fontSize={14}>
                    Narration
                  </Typography>
                  <TextField placeholder="Enter Narration" />
                </FormControl>
              </Grid>
              <Grid item lg={6} xs={12}>
                <FormControl sx={{ minWidth: { xs: '100%', xl: 430, md: 300 } }}>
                  <Typography variant="h6" fontSize={14}>
                    Date
                  </Typography>
                  <DateSelect />
                </FormControl>
              </Grid>
              <Grid item lg={6} xs={12}>
                <FormControl sx={{ minWidth: { xs: '100%', xl: 430, md: 300 } }}>
                  <Typography variant="h6" fontSize={14}>
                    Amount
                  </Typography>
                  <TextField placeholder="Enter Amount" />
                </FormControl>
              </Grid>
              <Grid item lg={6} xs={12}>
                <FormControl sx={{ minWidth: { xs: '100%', xl: 430, md: 300 } }}>
                  <Typography variant="h6" fontSize={14} mb={1}>
                    Select Payment Type
                  </Typography>
                  <RadioGroup row aria-labelledby="demo-row-radio-buttons-group-label" name="row-radio-buttons-group">
                    <FormControlLabel value="cash" control={<Radio />} label="Cash" />
                    <FormControlLabel value="bank" control={<Radio />} label="Bank" />
                  </RadioGroup>
                </FormControl>
              </Grid>
            </Grid>
          </form>
          <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '100px' } }}>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="secondary" sx={{ fontFamily: typography.fontFamily }}>
                Cancel
              </Button>
              <Button onClick={handleClickOpen} variant="contained" color="primary">
                Save
              </Button>
            </Stack>
          </Box>
        </Card>
        <Popup
          size="xs"
          state={popup}
          onClose={handleClickClose}
          popupContent={<SuccessMessage message="TimeTable Updated Successfully" />}
        />
      </CreatePaymentRoot>
    </Page>
  );
}

export default CreatePayment;
