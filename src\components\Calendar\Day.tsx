/* eslint-disable react/no-array-index-key */
import { Box, Stack, Typography, Tooltip } from '@mui/material';
import React, { useContext, useEffect, useState } from 'react';
import dayjs from 'dayjs';
import CalendarContext, { EventType } from '@/contexts/CalendarContext';
import { useTheme } from 'styled-components';

type DayProps = {
  day: dayjs.Dayjs;
  idx: number;
};
function Day({ day, idx }: DayProps) {
  const theme = useTheme();
  const [dayEvents, setDayEvents] = useState<EventType[]>([]);
  const { setDaySelected, setShowEventModal, filteredEvents, setSelectedEvent, monthIndex } =
    useContext(CalendarContext);

  useEffect(() => {
    const events = filteredEvents.filter((evt) => dayjs(evt.day).format('DD-MM-YY') === day.format('DD-MM-YY'));
    setDayEvents(events);
  }, [filteredEvents, day]);

  const currentDay = dayjs();

  const handleDayClick = (dy: dayjs.Dayjs) => {
    setDaySelected(dy);
    setShowEventModal(true);
  };

  const handleEventClick = (evt: EventType) => {
    setSelectedEvent(evt);
    setShowEventModal(true);
  };
  return (
    <Box height="100%" onClick={() => handleDayClick(day)}>
      <Typography
        variant={day.format('M') === dayjs(new Date(dayjs().year(), monthIndex)).format('M') ? 'subtitle2' : 'body2'}
        color={day.format('M') === dayjs(new Date(dayjs().year(), monthIndex)).format('M') ? 'inherit' : 'GrayText'}
        onClick={() => handleDayClick(day)}
        sx={{ display: 'flex', justifyContent: 'center', cursor: 'pointer', py: 1 }}
      >
        {day.format('DD-MM-YY') === currentDay.format('DD-MM-YY') ? (
          <Box
            sx={{
              width: 25,
              height: 25,
              borderRadius: '50%',
              backgroundColor: theme.palette.info.main,
              color: theme.palette.common.white,
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            {day.format('DD')}
          </Box>
        ) : (
          day.format('DD')
        )}
      </Typography>
      <Stack spacing={0.3} sx={{ flex: 1, cursor: 'pointer', minHeight: 30 }}>
        {dayEvents.map((evt) => (
          <Tooltip title={`${evt.title}`}>
            <Box
              sx={{
                backgroundColor: theme.palette[evt.label].lighter,
                color: theme.palette[evt.label].darker,
                fontSize: '0.8rem',
                borderLeft: '3px solid',
                borderColor: theme.palette[evt.label].main,
                px: { xs: 1, md: 3 },
                py: 0.1,
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
              }}
              key={idx}
              onClick={() => handleEventClick(evt)}
            >
              {evt.title}
            </Box>
          </Tooltip>
        ))}
      </Stack>
    </Box>
  );
}

export default Day;
