import api from '@/api';
import { CreateResponse, DeleteResponse, SendResponse } from '@/types/Common';
import {
  DeleteMultipleVoiceMessageType,
  SendVoiceMessageToGroupWiseRequest,
  SendVoiceMessageToPublicGroupWiseRequest,
  SendVoiceToClassDivisionRequest,
  SendVoiceToClassSectionRequest,
  SendVoiceToConveyorRequest,
  SendVoiceToGroupRequest,
  SendVoiceToParentsRequest,
  SendVoiceToPtaRequest,
  SendVoiceToPublicGroupRequest,
  SendVoiceToStaffRequest,
  VoiceCreateRequest,
  VoiceDataType,
  VoiceMessageFilter,
} from '@/types/VoiceMessage';
import { createAsyncThunk } from '@reduxjs/toolkit';

export const createVoice = createAsyncThunk<CreateResponse, VoiceCreateRequest, { rejectValue: string }>(
  'voiceMessage/create',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.voiceMessage.CreateNewVoice(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in creating voice message');
    }
  }
);

export const fileUpload = createAsyncThunk<any, { files: File }, { rejectValue: string }>(
  'voiceMessage/FileUpload',
  async ({ files }, { rejectWithValue }) => {
    try {
      // Assuming your API method expects FormData for file upload
      const response = await api.voiceMessage.FileUpload(files);

      return response;
    } catch {
      return rejectWithValue('Something went wrong in Upload files');
    }
  }
);

export const fetchVoiceMessageList = createAsyncThunk<VoiceDataType[], VoiceMessageFilter, { rejectValue: string }>(
  'voiceMessage/voiceMessageList',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.voiceMessage.GetVoiceMessageList(request);
      console.log('request', request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in fetching Notification List');
    }
  }
);

export const DeleteVoiceMessageList = createAsyncThunk<
  DeleteResponse,
  { adminId: number | undefined; voiceId: number },
  { rejectValue: string }
>('voiceMessage/delete', async ({ adminId, voiceId }, { rejectWithValue }) => {
  try {
    const response = await api.voiceMessage.DeleteVoiceList(adminId, voiceId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in deleting voice message List');
  }
});

export const DeleteMultipleVoiceMessageList = createAsyncThunk<
  DeleteResponse,
  DeleteMultipleVoiceMessageType[],
  { rejectValue: string }
>('voiceMessage/deleteMultiple', async (request, { rejectWithValue }) => {
  try {
    const response = await api.voiceMessage.DeleteMultipleVoiceList(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in deleting voice message List');
  }
});

// Send to parent
export const voiceMessageSendToParents = createAsyncThunk<
  SendVoiceToParentsRequest,
  SendVoiceToParentsRequest,
  { rejectValue: string }
>('voiceMessage/SendToParent', async (request, { rejectWithValue }) => {
  try {
    const response = await api.voiceMessage.SendVoiceMessageToParents(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in sending voice message to parents');
  }
});
// Send to staff
export const voiceMessageSendToStaffList = createAsyncThunk<
  SendVoiceToStaffRequest,
  SendVoiceToStaffRequest[],
  { rejectValue: string }
>('voiceMessage/SendToStaffList', async (request, { rejectWithValue }) => {
  try {
    const response = await api.voiceMessage.SendVoiceMessageToStaff(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in sending  voice message to Staff List');
  }
});

// Send to Pta
export const voiceMessageSendToPtaList = createAsyncThunk<
  SendVoiceToPtaRequest,
  SendVoiceToPtaRequest[],
  { rejectValue: string }
>('voiceMessage/SendToPtaList', async (request, { rejectWithValue }) => {
  try {
    const response = await api.voiceMessage.SendVoiceMessageToPTA(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in sending voice message To Pta List');
  }
});

// Send to Conveyor
export const voiceMessageSendToConveyorList = createAsyncThunk<
  SendVoiceToConveyorRequest,
  SendVoiceToConveyorRequest[],
  { rejectValue: string }
>('voiceMessage/SendToConveyorList', async (request, { rejectWithValue }) => {
  try {
    const response = await api.voiceMessage.SendVoiceMessageToConveyor(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in sending voice message To Conveyor List');
  }
});
// Send to Group
export const voiceMessageSendToGroupMembersList = createAsyncThunk<
  SendVoiceToGroupRequest,
  SendVoiceToGroupRequest[],
  { rejectValue: string }
>('voiceMessage/SendToGroupMembersList', async (request, { rejectWithValue }) => {
  try {
    const response = await api.voiceMessage.SendVoiceMessageToGroup(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in sending voice message To Group Members List');
  }
});

// Send to public Group
export const voiceMessageSendToPublicGroupMembersList = createAsyncThunk<
  SendVoiceToPublicGroupRequest,
  SendVoiceToPublicGroupRequest[],
  { rejectValue: string }
>('voiceMessage/SendToPublicGroupMembersList', async (request, { rejectWithValue }) => {
  try {
    const response = await api.voiceMessage.SendVoiceMessageToPublicGroupMembers(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in sending voice message To Public Group Members List');
  }
});

// Send to Class Division
export const voiceMessageSendToClassDivision = createAsyncThunk<
  SendVoiceToClassDivisionRequest,
  SendVoiceToClassDivisionRequest[],
  { rejectValue: string }
>('voiceMessage/ClassDivisionList', async (request, { rejectWithValue }) => {
  try {
    const response = await api.voiceMessage.SendVoiceMessageToClassDivision(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in sending voice message To Class Division');
  }
});

// Send to Class Section
export const voiceMessageSendToClassSection = createAsyncThunk<
  SendVoiceToClassSectionRequest,
  SendVoiceToClassSectionRequest[],
  { rejectValue: string }
>('voiceMessage/ClassSectionList', async (request, { rejectWithValue }) => {
  try {
    const response = await api.voiceMessage.SendVoiceMessageToClassSection(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in sending voice message To Class Section');
  }
});

// Send to Group Wise
export const voiceMessageSendToGroupWise = createAsyncThunk<
  SendVoiceMessageToGroupWiseRequest,
  SendVoiceMessageToGroupWiseRequest[],
  { rejectValue: string }
>('voiceMessage/GroupWiseList', async (request, { rejectWithValue }) => {
  try {
    const response = await api.voiceMessage.SendVoiceMessageToGroupWise(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in sending voice message To Group Wise');
  }
});

// Send to Public Group Wise
export const voiceMessageSendToPublicGroupWise = createAsyncThunk<
  SendVoiceMessageToPublicGroupWiseRequest,
  SendVoiceMessageToPublicGroupWiseRequest[],
  { rejectValue: string }
>('voiceMessage/PublicGroupWiseList', async (request, { rejectWithValue }) => {
  try {
    const response = await api.voiceMessage.SendVoiceMessageToPublicGroupWise(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in sending voice message To Public Group Wise');
  }
});

//  Send To All

export const voiceMessageSendToAllParents = createAsyncThunk<
  SendResponse,
  { adminId: number | undefined; academicId: number | undefined; voiceId: number | undefined },
  { rejectValue: string }
>('voiceMessage/SendToAllParents', async ({ adminId, academicId, voiceId }, { rejectWithValue }) => {
  try {
    const response = await api.voiceMessage.SendVoiceMessageToAllParents(adminId, academicId, voiceId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in sending Messages To All Parents');
  }
});
export const voiceMessageSendToAllStaff = createAsyncThunk<
  SendResponse,
  { adminId: number | undefined; academicId: number | undefined; voiceId: number | undefined },
  { rejectValue: string }
>('voiceMessage/SendToAllStaff', async ({ adminId, academicId, voiceId }, { rejectWithValue }) => {
  try {
    const response = await api.voiceMessage.SendVoiceMessageToAllStaff(adminId, academicId, voiceId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in sending Messages To All Staff');
  }
});
export const voiceMessageSendToAllPta = createAsyncThunk<
  SendResponse,
  { adminId: number | undefined; academicId: number | undefined; voiceId: number | undefined },
  { rejectValue: string }
>('voiceMessage/SendToAllPta', async ({ adminId, academicId, voiceId }, { rejectWithValue }) => {
  try {
    const response = await api.voiceMessage.SendVoiceMessageToAllPta(adminId, academicId, voiceId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in sending Messages To All Pta');
  }
});
export const voiceMessageSendToAllConveyors = createAsyncThunk<
  SendResponse,
  { adminId: number | undefined; academicId: number | undefined; voiceId: number | undefined },
  { rejectValue: string }
>('voiceMessage/SendToAllConveyors', async ({ adminId, academicId, voiceId }, { rejectWithValue }) => {
  try {
    const response = await api.voiceMessage.SendVoiceMessageToAllConveyors(adminId, academicId, voiceId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in sending Messages To All Conveyors');
  }
});
