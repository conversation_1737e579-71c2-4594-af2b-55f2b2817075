import React, { useState } from 'react';
import StudentPromotionList from '@/features/ExamCenter/ProgressReport/StudentPromotionList';
import StudentWise from '@/features/ExamCenter/ProgressReport/StudentWise';
import ClassWise from './ClassWise';
import Topper from './Topper';
import GradeWise from './GradeWise';

function ProgressReportIndex() {
  const [view, setView] = useState('StudentWise');

  const handleViewChange = (newView: string) => {
    setView(newView);
  };

  const renderView = () => {
    switch (view) {
      case 'StudentWise':
        return (
          <StudentWise
            onClickPromotionList={() => handleViewChange('StudentPromotionList')}
            onClickClassWise={() => handleViewChange('ClassWise')}
            onClickTopper={() => handleViewChange('Topper')}
            onClickGradeWise={() => handleViewChange('GradeWise')}
          />
        );
      case 'StudentPromotionList':
        return (
          <StudentPromotionList
            onClickStudentWise={() => handleViewChange('StudentWise')}
            onClickClassWise={() => handleViewChange('ClassWise')}
            onClickTopper={() => handleViewChange('Topper')}
            onClickGradeWise={() => handleViewChange('GradeWise')}
          />
        );
      case 'ClassWise':
        return (
          <ClassWise
            onClickStudentWise={() => handleViewChange('StudentWise')}
            onClickPromotionList={() => handleViewChange('StudentPromotionList')}
            onClickTopper={() => handleViewChange('Topper')}
            onClickGradeWise={() => handleViewChange('GradeWise')}
          />
        );
      case 'Topper':
        return (
          <Topper
            onClickClassWise={() => handleViewChange('ClassWise')}
            onClickStudentWise={() => handleViewChange('StudentWise')}
            onClickPromotionList={() => handleViewChange('StudentPromotionList')}
            onClickGradeWise={() => handleViewChange('GradeWise')}
          />
        );
      case 'GradeWise':
        return (
          <GradeWise
            onClickClassWise={() => handleViewChange('ClassWise')}
            onClickStudentWise={() => handleViewChange('StudentWise')}
            onClickPromotionList={() => handleViewChange('StudentPromotionList')}
            onClickTopper={() => handleViewChange('Topper')}
          />
        );

      default:
        return null;
    }
  };

  return renderView();
}

export default ProgressReportIndex;
