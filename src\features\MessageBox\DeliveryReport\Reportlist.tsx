import { Box, Table, TableContainer, TableCell, TableHead, TableRow, TableBody, Paper } from '@mui/material';
import React from 'react';
import { useTheme } from 'styled-components';

export const Reportlist = () => {
  const theme = useTheme();
  return (
    <Box
      width="100%"
      sx={{
        padding: ' 0.5rem 1.5rem',
      }}
    >
      <Paper
        sx={{
          border: `1px solid #e8e8e9`,
          width: '100%',
          overflow: 'auto',
          '&::-webkit-scrollbar': {
            width: 0,
          },
        }}
      >
        <TableContainer className="tableContainer">
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                <TableCell>Sl.No</TableCell>
                <TableCell>Name</TableCell>
                <TableCell>Class</TableCell>
                <TableCell>Mobile</TableCell>
                <TableCell>Status</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {[1, 2, 3, 4, 5, 6, 7].map((row) => (
                <TableRow key={row} sx={{ borderBottom: '1px solid  rgb(200, 200, 200)' }}>
                  <TableCell>01</TableCell>
                  <TableCell>Fionna Grand</TableCell>
                  <TableCell>VIII-A</TableCell>
                  <TableCell>+91-9564554016</TableCell>
                  <TableCell>
                    <Paper
                      sx={{
                        border: '1px solid',
                        display: 'flex',
                        justifyContent: 'center',
                        p: 0.5,
                        maxWidth: 85,
                        borderRadius: '20px',
                        backgroundColor: theme.palette.success.lighter,
                        color: theme.palette.success.main,
                        fontSize: 12,
                      }}
                    >
                      Delivered
                    </Paper>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>
    </Box>
  );
};
