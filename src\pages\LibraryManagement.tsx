import React from 'react';
import { Link, useLocation, useOutletContext } from 'react-router-dom';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';
import styled from 'styled-components';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import BookFineSetting from '@/features/LibraryManagement/BookFineSetting';
import EnrollBooks from '@/features/LibraryManagement/EnrollBooks';
import BookList from '@/features/LibraryManagement/BookList/BookList';
import IssueList from '@/features/LibraryManagement/IssueList/IssueList';
import CategoryManager from '@/features/LibraryManagement/CategoryManager';
import LocationManager from '@/features/LibraryManagement/LocationManager';
import Page from '@/components/shared/Page';
import { useDispatch } from 'react-redux';
import { flushStore } from '@/store/flush.slice';
import { OutletContextType } from '@/types/Common';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const LibraryManagementRoot = styled.div`
  min-height: calc(100vh - ${TOP_BAR_HEIGHT});

  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};

  .tab {
    background-color: ${(props) => (props.theme.themeMode === 'light' ? '#fff' : props.theme.palette.grey[800])};
    /* @media screen and (min-width: 1414px) {
      width: 100%;
    }
    @media screen and (max-width: 720px) {
    } */
    width: 100%;
  }
  .Mui-selected {
    color: ${(props) => props.theme.palette.primary.main};
  }
`;

function TabPanel({ children, value, index }: TabPanelProps) {
  return (
    <Box
      sx={{ paddingTop: '3rem' }}
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
    >
      {value === index && <Box>{children}</Box>}
    </Box>
  );
}
function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`,
  };
}

function LibraryManagement() {
  const { topBarHeight } = useOutletContext<OutletContextType>();
  const location = useLocation();
  const [value, setValue] = React.useState(0);

  const dispatch = useDispatch();

  React.useEffect(() => {
    dispatch(flushStore());
    console.log('Flush Store');
  }, [dispatch, value]);

  React.useEffect(() => {
    const currentPath = location.pathname;
    const routePaths = [
      '/library-management/new-book',
      '/library-management/book-list',
      '/library-management/issue-list',
      '/library-management/category',
      '/library-management/location',
      '/library-management/fine-setting',
    ];
    const index = routePaths.indexOf(currentPath);
    if (index !== -1) {
      setValue(index);
    }
  }, [location]);

  return (
    <Page title="">
      <LibraryManagementRoot>
        <Tabs
          className="tab"
          variant="scrollable"
          scrollButtons
          allowScrollButtonsMobile
          value={value}
          aria-label="basic tabs"
          sx={{
            ml: '0px',
            borderBottom: 1,
            borderColor: 'divider',
            position: 'fixed',
            top: `${topBarHeight}px`,
            zIndex: 1,
          }}
        >
          <Tab {...a11yProps(0)} label="Add New" component={Link} to="/library-management/new-book" />
          <Tab {...a11yProps(1)} label="Books List" component={Link} to="/library-management/book-list" />
          <Tab {...a11yProps(2)} label="Issued List" component={Link} to="/library-management/issue-list" />
          <Tab {...a11yProps(3)} label="Category Manager" component={Link} to="/library-management/category" />
          <Tab {...a11yProps(4)} label="Location Manager" component={Link} to="/library-management/location" />
          <Tab {...a11yProps(4)} label="Fine Setting" component={Link} to="/library-management/fine-setting" />
        </Tabs>

        <TabPanel value={value} index={0}>
          <EnrollBooks />
        </TabPanel>
        <TabPanel value={value} index={1}>
          <BookList />
        </TabPanel>
        <TabPanel value={value} index={2}>
          <IssueList />
        </TabPanel>
        <TabPanel value={value} index={3}>
          <CategoryManager />
        </TabPanel>
        <TabPanel value={value} index={4}>
          <LocationManager />
        </TabPanel>
        <TabPanel value={value} index={5}>
          <BookFineSetting />
        </TabPanel>
      </LibraryManagementRoot>
    </Page>
  );
}

export default LibraryManagement;
