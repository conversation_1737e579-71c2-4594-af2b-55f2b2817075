import { Dispatch, SetStateAction } from 'react';

export interface Account {
  accountId: number;
  staffId: number;
  userName: string;
  firstName: string;
  lastName: string;
  roleId: number;
  roleName: string;
  photo: string | null;
  schoolId: number;
  schoolName: string;
}

export interface JwtToken {
  sub: string;
  name: string;
  roles: string;
  sid: string;
  iss: string;
  aud: string;
  nbf: number;
  exp: number;
  iat: number;
}

export type AuthContextState = {
  method: string;
  isAuthenticated: boolean;
  isInitialized: boolean;
  user: Account | null;
  loading: boolean;
  error: string | null;
  login: (request: LoginRequest) => Promise<void>;
  loginNew: (request: LoginRequestNew) => Promise<void>;
  logout: () => void;
  parentMode: boolean;
  setParentMode: Dispatch<SetStateAction<boolean>>;
  setLoginMode: 0 | 1;
  loginMode: boolean;
};

export type AuthActionPayload = Omit<Partial<AuthContextState>, 'method' | 'login' | 'logout'>;
export type AuthAction = {
  type: string;
  payload?: AuthActionPayload;
};

export type LoginRequest = {
  username: string;
  password: string;
};

export type LoginRequestNew = {
  username: string;
  password: string;
  schoolCode: string;
};

export type LoginStatus = 'Success' | 'Failure' | 'AccountInactive';

export type LoginResponse = {
  status: LoginStatus;
  account: Account | null;
  accessToken: string | null;
};

export interface TokenVerificationResponse {
  valid: boolean;
  account: Account | null;
}
