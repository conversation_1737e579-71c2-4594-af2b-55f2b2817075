export type LeaveInformationType = {
  id: number;
  absentDate: string;
};
export type LeaveNoteReportType = {
  id: number;
  subject: string;
  description: string;
  leaveDate: string;
  status: string;
};
export type EnquiryReportType = {
  id: number;
  subject: string;
  query: string;
  reply: string;
  status: string;
};
export type AssignmentReportType = {
  id: number;
  assignment: string;
  mark: string;
  comment: string;
  status: string;
};
export type ObjectiveExamReportType = {
  id: number;
  subject: string;
  exam: string;
  totalQuestion: number;
  answeredQuestion: number;
  correctAnswer: number;
};
export type DescriptiveExamReportType = {
  id: number;
  subject: string;
  exam: string;
  totalQuestion: number;
  answeredQuestion: number;
  correctAnswer: number;
};

export type ExamCenterStateReportType = {
  id: number;
  exam: string;
  outOffMark: number;
  getMark: number;
  totalAbsent: number;
  totalFailed: number;
  totalPassed: number;
};

export type StudentFeeDetailsReportType = {
  id: number;
  fee: string;
  amount: number;
  paid: number;
};
export type TermFeeDetailsReportType = {
  id: number;
  term: string;
  amount: number;
  busFee: number;
  paid: number;
};
export type StoreDetailsReportType = {
  id: number;
  product: string;
  productPrice: number;
  quantity: number;
  paid: number;
};
export type LibraryDetailsReportType = {
  id: number;
  book: string;
  issueDate: string;
  expectedDate: string;
  return: string;
};
