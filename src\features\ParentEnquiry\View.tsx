/* eslint-disable prettier/prettier */
import React from 'react';
import { Grid, Typography, Button, Stack } from '@mui/material';
import CustomTextField from '@/components/shared/Selections/CustomTextField';

type EditeStudentsProps = {
  student?: any;
  onCancel: () => void;
};
const EditStudent = ({ student, onCancel }: EditeStudentsProps) => {
  return (
    <Grid container spacing={2} m={4} maxWidth="90%">
      <Grid item xs={12}>
        <Typography variant="h6" mb={2}>
          Enquiry Details
        </Typography>
      </Grid>
      <Grid item md={3} xs={6}>
        <CustomTextField
          onchange={null}
          name="rollNo"
          label="Roll No"
          variant="outlined"
          value={student.RollNo}
          disabled
        />
      </Grid>
      <Grid item md={9} xs={12}>
        <CustomTextField
          onchange={null}
          name="name"
          label="Student Name"
          variant="outlined"
          value={student.StudentName}
          disabled
        />
      </Grid>
      <Grid item md={4} xs={6}>
        <CustomTextField onchange={null} name="class" label="Class" variant="outlined" value={student.Class} disabled />
      </Grid>
      <Grid item md={4} xs={6}>
        <CustomTextField
          onchange={null}
          name="sdate"
          label="Submitted Date"
          variant="outlined"
          value={student.SubmittedDate}
          disabled
        />
      </Grid>
      <Grid item md={4} xs={6}>
        <CustomTextField
          onchange={null}
          name="Subject"
          label="Subject"
          variant="outlined"
          value={student.Subject}
          disabled
        />
      </Grid>
      <Grid item xs={12}>
        <CustomTextField onchange={null} name="Query" label="Query" variant="outlined" value={student.Query} disabled />
      </Grid>
      <Grid item xs={12}>
        <CustomTextField
          name="Reply"
          label={student.Reply === null ? 'Reply To the Query' : 'Reply'}
          variant="outlined"
          value={student.Reply}
        />
      </Grid>
      <Grid item md={6} xs={12}>
        <CustomTextField
          onchange={null}
          name="RepliedDate"
          label="Replied Date"
          variant="outlined"
          value={student.RepliedDate}
          disabled
        />
      </Grid>
      <Grid item md={6} xs={12}>
        <CustomTextField
          onchange={null}
          name="Status"
          label="Status"
          variant="outlined"
          value={student.Status}
          disabled
        />
      </Grid>
      <Grid item xs={12}>
        <Stack justifyContent="end" direction="row" spacing={2}>
          <Button variant="contained" color="secondary" onClick={onCancel}>
            Cancel
          </Button>
          <Button variant="contained" color="primary">
            Save
          </Button>
        </Stack>
      </Grid>
    </Grid>
  );
};

export default EditStudent;
