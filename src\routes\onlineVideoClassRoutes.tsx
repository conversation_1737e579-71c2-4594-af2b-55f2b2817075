import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import Loadable from '@/components/shared/Loadable';

const OnlineVideoClass = Loadable(lazy(() => import('@/pages/OnlineVideoClass')));
const StudentsDeviceDetails = Loadable(lazy(() => import('@/features/OnlineVideoClass/StudentsDeviceDetails')));
const StudentBlockUnblock = Loadable(lazy(() => import('@/features/OnlineVideoClass/StudentBlockUnblock')));
const StudentBlockList = Loadable(lazy(() => import('@/features/OnlineVideoClass/StudentBlockList')));
const StudentBlockReport = Loadable(lazy(() => import('@/features/OnlineVideoClass/StudentBlockReport')));
const SubjectChapterDetails = Loadable(lazy(() => import('@/features/OnlineVideoClass/SubjectChapterDetails')));
const VideoDetail = Loadable(lazy(() => import('@/features/OnlineVideoClass/VideoDetail/VideoDetail')));

export const onlineVideoClassRoutes: RouteObject[] = [
  {
    path: '/online-video-class',
    element: <OnlineVideoClass />,
    children: [
      {
        path: 'student-device-details',
        element: <StudentsDeviceDetails />,
      },
      {
        path: 'student-block-unblock',
        element: <StudentBlockUnblock />,
      },
      {
        path: 'student-block-list',
        element: <StudentBlockList />,
      },
      {
        path: 'student-block-report',
        element: <StudentBlockReport />,
      },
      {
        path: 'video-detail',
        element: <SubjectChapterDetails />,
      },
      {
        path: 'subject-chapter-details',
        element: <VideoDetail />,
      },
    ],
  },
];
