/* eslint-disable react/no-array-index-key */
import React, { useContext } from 'react';
import { Box, Checkbox, List, ListItem, Typography } from '@mui/material';
import CalendarContext, { LabelType } from '@/contexts/CalendarContext';
import SmallCalendar from './SmallCalendar';

function Sidebar() {
  const { updateLabel } = useContext(CalendarContext);

  const initLabels: LabelType[] = [
    { label: 'info', type: 'Event', checked: true },
    { label: 'success', type: 'Exam', checked: true },
    { label: 'error', type: 'Holiday', checked: true },
    { label: 'warning', type: 'Online Class', checked: true },
    { label: 'secondary', type: 'Other', checked: true },
  ];

  const [labelState, setLabelState] = React.useState<LabelType[]>(initLabels);

  const handleUpdate = (label: LabelType) => {
    setLabelState((prev) => prev.map((lbl) => (lbl.label === label.label ? label : lbl)));
    updateLabel({ label: label.label, checked: label.checked, type: label.type });
  };

  return (
    <Box width="100%">
      <SmallCalendar />
      <Box sx={{ display: { xs: 'none', md: 'flex' } }}>
        <List sx={{ width: '100%', height: `calc(100vh - 520px)`, overflow: 'auto', mt: 2 }}>
          {labelState.map((lbl, idx) => (
            <ListItem key={idx} sx={{ display: 'flex', alignItems: 'center' }}>
              <Checkbox
                color={lbl.label} 
                checked={lbl.checked}
                onChange={() => handleUpdate({ label: lbl.label, checked: !lbl.checked, type: lbl.type })}
              />
              <Typography variant="body1">{lbl.type}</Typography>
            </ListItem>
          ))}
        </List>
      </Box>
    </Box>
  );
}

export default Sidebar;
