/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import {
  Autocomplete,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Box,
  Avatar,
  IconButton,
  Collapse,
  MenuItem,
} from '@mui/material';
import styled from 'styled-components';
import { GENDER_SELECT, YEAR_SELECT } from '@/config/Selection';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import SearchIcon from '@mui/icons-material/Search';
import DateSelect from '@/components/shared/Selections/DateSelect';
import { Select } from '@mui/material';
import DatePickers from '@/components/shared/Selections/DatePicker';
import InputFileUpload from '@/components/shared/Selections/FilesUploadTextField';

const CreateLeaveRoot = styled.div`
  width: 100%;
  .Card {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    height: calc(100vh - 200px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }
    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 10px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

function CreateLeave({ onCancel }: any) {
  return (
    <CreateLeaveRoot>
      <Divider />
      <Box p="1rem" px="1.5rem">
        <form noValidate>
          <Grid container spacing={3}>
            <Grid item md={6} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Staff Name
              </Typography>
              <TextField
                fullWidth
                placeholder="Enter name"
                // name="staffName"
                // value={staffName}
                // onChange={handleChange}
                // error={touched.staffName && !!errors.staffName}
                // helperText={errors.staffName}
                // disabled={isSubmitting}
              />
            </Grid>
            <Grid item md={6} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Class Name
              </Typography>
              <TextField fullWidth placeholder="Enter class" />
            </Grid>
            <Grid item md={6} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Leave Type
              </Typography>
              <Select fullWidth placeholder="Select Gender">
                <MenuItem value={0} sx={{ display: 'none' }}>
                  Select leave type
                </MenuItem>
                {GENDER_SELECT.map((opt) => (
                  <MenuItem key={opt.id} value=" Leave Type">
                    Leave Type
                  </MenuItem>
                ))}
              </Select>
            </Grid>
            <Grid item md={6} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Number of Days
              </Typography>
              <Select fullWidth placeholder="Select Gender">
                <MenuItem value={0} sx={{ display: 'none' }}>
                  Select leave type
                </MenuItem>
                {GENDER_SELECT.map((opt, index) => (
                  <MenuItem key={opt.id} value="1">
                    {index + 1}
                  </MenuItem>
                ))}
              </Select>
            </Grid>
            <Grid item md={12} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Reason
              </Typography>
              <TextField multiline rows={3} fullWidth placeholder="Type here" />
            </Grid>
            <Grid item md={6} xs={12}>
              <Typography variant="h6" fontSize={14}>
                From Date
              </Typography>
              <DatePickers />
            </Grid>
            <Grid item md={6} xs={12}>
              <Typography variant="h6" fontSize={14}>
                To Date
              </Typography>
              <DatePickers />
            </Grid>
            <Grid item md={12} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Upload Documents
              </Typography>
              <InputFileUpload />
            </Grid>
          </Grid>
          <Stack direction="row" justifyContent="center" gap={5} mt={5}>
            <Button variant="contained" color="secondary">
              Cancel
            </Button>
            <Button variant="contained" color="primary">
              Apply
            </Button>
          </Stack>
        </form>
      </Box>
    </CreateLeaveRoot>
  );
}

export default CreateLeave;
