// Get Parent Pay Fee

import api from '@/api';
import { GetReceiptForPrintType } from '@/types/ManageFee';
import { GetParentPayFeeDataType } from '@/types/Payment';
import { createAsyncThunk } from '@reduxjs/toolkit';

export const fetchParentPayFee = createAsyncThunk<
  GetParentPayFeeDataType[],
  {
    studentId: number | undefined;
    feeTypeId: number | undefined;
  },
  { rejectValue: string }
>('manageFee/getParentPayFee', async ({ studentId, feeTypeId }, { rejectWithValue }) => {
  try {
    const response = await api.Payment.getParentPayFee(studentId, feeTypeId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching Manage Fee Get Optional Fee Settings');
  }
});

export const fetchReceiptOnline = createAsyncThunk<GetReceiptForPrintType[], number, { rejectValue: string }>(
  'manageFee/getReceiptOnline ',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.Payment.GetReceiptOnline(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in fetching Manage Fee Get Receipt Online List');
    }
  }
);
