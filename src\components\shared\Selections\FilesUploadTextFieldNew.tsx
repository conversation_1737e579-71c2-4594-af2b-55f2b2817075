/* eslint-disable no-else-return */
/* eslint-disable no-nested-ternary */
import * as React from 'react';
import { styled } from '@mui/material/styles';
import Button from '@mui/material/Button';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import { IconButton, InputAdornment, TextField, Avatar, Box, Stack } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import pdf from '@/assets/NotificationIcons/pdf.png';
import { fileUpload } from '@/store/MessageBox/messageBox.thunks';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import { useTheme } from '@mui/material';

const VisuallyHiddenInput = styled('input')({
  clip: 'rect(0 0 0 0)',
  clipPath: 'inset(50%)',
  height: 1,
  overflow: 'hidden',
  position: 'absolute',
  bottom: 0,
  left: 0,
  whiteSpace: 'nowrap',
  width: 1,
});

type UploadPropsTypes = {
  onChange: (e: any) => void;
  accept: string;
  name: string;
  multiple?: boolean;
  disabled?: boolean;
  title?: string;
  height?: string | number;
  error?: boolean | undefined;
  helperText?: string | boolean | undefined;
  backgroundColorDisabled?: boolean;
  disabledTextfeild?: boolean;
  buttonLabel?: string;
  uploadedFile?: [];
  handleRemoveFile: () => void;
  fieldName?: string;
};

export default function InputFileUpload({
  onChange,
  accept,
  name,
  multiple,
  disabled,
  title,
  height,
  error,
  helperText,
  backgroundColorDisabled,
  uploadedFile,
  buttonLabel,
  disabledTextfeild,
  fieldName,
  handleRemoveFile,
}: UploadPropsTypes) {
  const theme = useTheme();
  const dispatch = useAppDispatch();
  const fileInputRef = React.useRef<HTMLInputElement | null>(null);
  const [files, setFiles] = React.useState<File[]>([]);

  const handleUpload = async () => {
    console.log('files', files);

    try {
      const response = await dispatch(fileUpload(files)).unwrap();
      console.log('response', response);
    } catch (error) {
      console.error(error);
    }
  };

  const handleFileChange = (event) => {
    onChange(event, fieldName);
    const selectedFiles = Array.from(event.target.files);
    if (multiple) {
      setFiles((prevFiles) => [...prevFiles, ...uploadedFile]);
    } else {
      setFiles(uploadedFile);
    }
  };

  // const handleRemoveFile = (index) => {
  //   setFiles((prevFiles) => {
  //     const newFiles = [...prevFiles];
  //     newFiles.splice(index, 1);
  //     return newFiles;
  //   });
  // };

  const handleTextFieldClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click(); // Open file picker when TextField is clicked
    }
  };

  return (
    <Box width="100%">
      <Stack direction="row" spacing={10} width="100%">
        <Stack
          direction="row"
          justifyContent="space-between"
          border={1}
          borderColor={theme.palette.grey[400]}
          width={1000}
          borderRadius={1}
          p={1}
          alignItems="center"
          onClick={handleTextFieldClick}
          sx={{ cursor: 'pointer' }}
        >
          <Stack>
            {Array.isArray(uploadedFile) &&
              uploadedFile.map((file, index) => {
                console.log('uploadedFile', uploadedFile);
                if (!file) return null; // Prevents undefined/null errors

                if (file.imageUrl && (file.type?.startsWith('image/png') || file.type?.startsWith('image/jpeg'))) {
                  return <Avatar key={index} alt="Uploaded File" src={file.imageUrl} sx={{ ml: 0 }} />;
                } else {
                  return <PictureAsPdfIcon />;
                }

                // if (file.type?.startsWith('application/pdf')) {

                // }

                return null; // Avoids returning an empty string
              })}
          </Stack>
          {uploadedFile?.some((file) => file.fieldName === fieldName) && (
            <IconButton
              size="small"
              onClick={() => handleRemoveFile('birthCertificate')}
              sx={{ position: 'absolute', right: 110, top: '50%', transform: 'translateY(-50%)' }}
            >
              <CloseIcon color="error" />
            </IconButton>
          )}
          <Button size="small" component="label" variant="contained" startIcon={<CloudUploadIcon />}>
            {buttonLabel ?? 'Upload File'}
            <VisuallyHiddenInput
              ref={fileInputRef}
              type="file"
              multiple={multiple ?? false}
              accept={accept ?? 'image/*'}
              onChange={handleFileChange}
            />
          </Button>
        </Stack>
        <TextField
          // onClick={handleTextFieldClick} // Clicking TextField opens file input
          fullWidth
          sx={{ cursor: 'pointer' }}
          // disabled={disabledTextfeild}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                {Array.isArray(uploadedFile) &&
                  uploadedFile.map((file, index) => {
                    console.log('uploadedFile', uploadedFile);
                    if (!file) return null; // Prevents undefined/null errors

                    if (file.imageUrl && (file.type?.startsWith('image/png') || file.type?.startsWith('image/jpeg'))) {
                      return <Avatar key={index} alt="Uploaded File" src={file.imageUrl} sx={{ ml: 0 }} />;
                    } else {
                      return <PictureAsPdfIcon />;
                    }

                    // if (file.type?.startsWith('application/pdf')) {

                    // }

                    return null; // Avoids returning an empty string
                  })}
              </InputAdornment>
            ),
            endAdornment: (
              <InputAdornment position="end">
                {uploadedFile?.some((file) => file.fieldName === fieldName) && (
                  <IconButton
                    size="small"
                    onClick={() => handleRemoveFile('birthCertificate')}
                    sx={{ position: 'absolute', right: 110, top: '50%', transform: 'translateY(-50%)' }}
                  >
                    <CloseIcon color="error" />
                  </IconButton>
                )}
                <Button size="small" component="label" variant="contained" startIcon={<CloudUploadIcon />}>
                  {buttonLabel ?? 'Upload File'}
                  <VisuallyHiddenInput
                    ref={fileInputRef}
                    type="file"
                    multiple={multiple ?? false}
                    accept={accept ?? 'image/*'}
                    onChange={handleFileChange}
                  />
                </Button>
              </InputAdornment>
            ),
          }}
          variant="outlined"
        />
        {/* <Button onClick={handleUpload} variant="contained" color="primary">
          Upload
        </Button> */}
      </Stack>
    </Box>
  );
}
