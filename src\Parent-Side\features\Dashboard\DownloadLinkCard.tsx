import styled from 'styled-components';
import { Card, Stack, Typography, Box } from '@mui/material';
import playstoreLink from '@/assets/downloadlink/playstore.svg';
import appStoreLink from '@/assets/downloadlink/app-store.svg';
import downloadillustraion from '@/assets/downloadlink/downloadillustraion.svg';
import { Link } from 'react-router-dom';

const DownloadCardRoot = styled(Card)`
  /* padding: 1rem; */
  border-top-right-radius: 0px;
  border-top-left-radius: 0px;
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.grey[400] : props.theme.palette.grey[900]};
  color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.common.black : props.theme.palette.common.white};
  /* margin: 1rem; */
  .image {
    img {
      width: 100px;
    }
  }
  .round-circle {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.primary.main : props.theme.palette.grey[900]};
    width: 100px;
    height: 100px;
    position: absolute;
    top: -35px;
    right: -35px;
    border: 15px solid white;
    border-radius: 100%;
  }
  .round-circle::before {
    content: '';
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.primary.main : props.theme.palette.grey[800]};
    height: 5rem;
    position: absolute;
    top: 7px;
    right: 7px;
    border-radius: 100%;
  }
  .img:hover {
    transform: scale(1.1);
  }
`;

function DownloadLinkCard() {
  return (
    <DownloadCardRoot className="">
      <Box>
        <Typography
          variant="h6"
          sx={{ fontFamily: 'Poppins Regular', fontSize: '1.5rem', paddingLeft: '16px', paddingTop: '16px' }}
        >
          Download <br /> PassDaily
        </Typography>
        <Stack direction="row" className="d-flex justify-content-between  ">
          <div className="d-flex px-3 py-3">
            <Link className="me-2" to="/">
              <img className="img" src={playstoreLink} alt="" />
            </Link>
            <Link to="/">
              <img className="img" src={appStoreLink} alt="" />
            </Link>
          </div>
          <div className="image ">
            <img src={downloadillustraion} alt="" />
          </div>
        </Stack>

        <Box className="round-circle"> </Box>
      </Box>
    </DownloadCardRoot>
  );
}

export default DownloadLinkCard;
