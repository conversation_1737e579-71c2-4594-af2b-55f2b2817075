import React from 'react';
import { Autocomplete, Box, Button, Stack, TextField, Typography } from '@mui/material';
import { STATUS_SELECT } from '@/config/Selection';

export const Create = ({ onClose, updates }: any) => {
  return (
    <Box mt={3}>
      <Stack sx={{ height: 'calc(100vh - 150px)' }}>
        <Typography variant="body2" fontSize={14} fontWeight={600}>
          Exam Name
        </Typography>
        <TextField
          fullWidth
          name={updates?.name}
          defaultValue={updates?.name}
          placeholder="Enter exam name"
          sx={{ mb: 2 }}
        />
        <Typography variant="h6" fontSize={14}>
          Status
        </Typography>
        <Autocomplete
          fullWidth
          sx={{ mb: 2 }}
          value={updates?.staffStatus}
          options={STATUS_SELECT}
          renderInput={(params) => <TextField name={updates?.staffStatus} {...params} placeholder="Select status" />}
        />
        <Typography variant="body2" fontSize={14} fontWeight={600}>
          Description
        </Typography>
        <TextField
          multiline
          fullWidth
          minRows={4}
          name={updates?.description}
          defaultValue={updates?.description}
          InputProps={{ inputProps: { style: { resize: 'both', padding: 0 } } }}
          placeholder="Enter here..."
        />
      </Stack>
      <Box sx={{ justifyContent: 'center', pt: 3, position: 'relative' }}>
        <Stack spacing={2} direction="row" sx={{}}>
          <Button onClick={onClose} fullWidth variant="contained" color="secondary">
            Cancel
          </Button>
          <Button
            // onKeyDown={toggleDrawer(false)}
            fullWidth
            variant="contained"
            color="primary"
          >
            Save
          </Button>
        </Stack>
      </Box>
    </Box>
  );
};
