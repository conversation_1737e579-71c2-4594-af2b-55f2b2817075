/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Box,
  Divider,
  Grid,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  useTheme,
  IconButton,
  Collapse,
  Tooltip,
  FormControl,
  Chip,
  Avatar,
  Popover,
  Select,
  MenuItem,
  SelectChangeEvent,
  Menu,
  Skeleton,
} from '@mui/material';
import styled from 'styled-components';
import SearchIcon from '@mui/icons-material/Search';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import NoData from '@/assets/no-datas.png';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import ReplyIcon from '@mui/icons-material/Reply';
import useSettings from '@/hooks/useSettings';
import { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import {
  getAttendanceListSubmitting,
  getClassData,
  getSortColumn,
  getSortDirection,
  getStudentEnquiryListData,
  getStudentEnquiryListPageInfo,
  getStudentEnquiryListSortColumn,
  getStudentEnquiryListSortDirection,
  getStudentEnquiryListStatus,
  getYearData,
  getYearStatus,
} from '@/config/storeSelectors';
import { useAppSelector } from '@/hooks/useAppSelector';
import { fetchClassList, fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import useAuth from '@/hooks/useAuth';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import DatePickers from '@/components/shared/Selections/DatePicker';
import { SuccessIcon } from '@/theme/overrides/CustomIcons';
import SortIcon from '@mui/icons-material/Sort';
import { PiSortAscendingBold } from 'react-icons/pi';
import { PiSortDescending } from 'react-icons/pi';
import SwapVertIcon from '@mui/icons-material/SwapVert';
import dayjs, { Dayjs } from 'dayjs';
import { fetchStudentEnquiryList, sendEnquiryReply } from '@/store/AttendanceMarking/attendanceMarking.thunks';
import { EnquiryReplyRequestData, StudentEnquiryListInfo, StudentEnquiryListRequest } from '@/types/AttendanceMarking';
import LoadingButton from '@mui/lab/LoadingButton';
import { TablePagination } from '@mui/material';
import { ListItemIcon } from '@mui/material';
import SpeakerNotesOffIcon from '@mui/icons-material/SpeakerNotesOff';
import {
  setFetchStatus,
  setSortColumn,
  setSortDirection,
  setStatus,
  setSubmitting,
} from '@/store/AttendanceMarking/attendanceMarking.slice';

const EnquiryListRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  .Card {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 50px);
      flex-grow: 1;

      .card-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: auto;
        ::-webkit-scrollbar {
          width: 0px;
        }
      }
      .student_card {
        /* border: 1px solid
          ${(props) =>
          props.theme.themeMode === 'light' ? props.theme.palette.grey[300] : props.theme.palette.grey[900]}; */
        background-color: ${(props) =>
          props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.grey[900]};
      }
      .student_card .date {
        font-weight: 500;
        color: ${(props) =>
          props.theme.themeMode === 'light' ? props.theme.palette.grey[600] : props.theme.palette.grey[600]};
      }
    }
  }
  .css-1woeorn-MuiButtonBase-root-MuiMenuItem-root .MuiListItemIcon-root {
    min-width: 0px;
  }
`;

function EnquiryList() {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const dispatch = useAppDispatch();
  const { user } = useAuth();
  const adminId: number = user ? user.accountId : 0;
  const YearData = useAppSelector(getYearData);
  const ClassData = useAppSelector(getClassData);
  const YearStatus = useAppSelector(getYearStatus);
  const StudentEnquiryListData = useAppSelector(getStudentEnquiryListData);
  const StudentEnquiryListStatus = useAppSelector(getStudentEnquiryListStatus);
  const sortColumn = useAppSelector(getStudentEnquiryListSortColumn);
  const sortDirection = useAppSelector(getStudentEnquiryListSortDirection);
  const paginationInfo = useAppSelector(getStudentEnquiryListPageInfo);
  const isSubmitting = useAppSelector(getAttendanceListSubmitting);

  const AllClassOption = {
    classId: -1,
    className: 'All Class',
    classDescription: 'string',
    classStatus: 1,
  };

  const defualtYear = YearData[0]?.accademicId || 0;
  const [academicYearFilter, setAcademicYearFilter] = useState(defualtYear);
  const [classFilter, setClassFilter] = useState(-1);
  const [fromDateFilter, setFromDateFilter] = useState<Dayjs | string>('');
  const [toDateFilter, setToDateFilter] = useState<Dayjs | string>('');
  const [studentEnquiryListData, setStudentEnquiryListData] = useState<StudentEnquiryListInfo[]>([]);
  const [showFilter, setShowFilter] = useState(false);
  const [createOpen, setCreateOpen] = React.useState(false);
  const [anchorEl, setAnchorEl] = useState<React.MouseEvent<Element, MouseEvent> | null>(null);
  const [sort, setSort] = useState<React.MouseEvent<Element, MouseEvent> | null>(null);
  const [replyText, setReplyText] = useState('');
  const [currentCardId, setCurrentCardId] = useState<number | null>(null);
  const currentDate = new Date().toLocaleDateString();
  const [expandedDescriptions, setExpandedDescriptions] = useState<Record<number, boolean>>({});
  const [selectedSortColumn, setSelectedSortColumn] = React.useState<string | undefined>('classId');
  const [selectedSortDirection, setSelectedSortDirection] = React.useState<'asc' | 'desc' | undefined>('asc');
  const [loadingStatus, setLoadingStatus] = React.useState<boolean>(true);

  // const [editingReply, setEditingReply] = useState<number | ''>('');
  // const { classId, className } = classFilter || {};

  const { pagenumber, pagesize, totalrecords } = paginationInfo;

  const currentStudentEnquiryListRequest: StudentEnquiryListRequest = React.useMemo(
    () => ({
      pageNumber: pagenumber,
      pageSize: pagesize,
      sortColumn,
      sortDirection,
      filters: {
        academicId: academicYearFilter,
        adminId,
        classId: classFilter,
        fromdate: fromDateFilter,
        todate: toDateFilter,
      },
    }),
    [
      pagenumber,
      pagesize,
      sortColumn,
      sortDirection,
      academicYearFilter,
      adminId,
      classFilter,
      fromDateFilter,
      toDateFilter,
    ]
  );
  // useEffect(() => {
  //   const data = JSON.parse(localStorage.getItem('absenteesData') || '[]');
  //   setSelectedABS(data);
  //   console.log('abssss', selectedABS);
  // }, [selectedABS]);

  const loadStudentEnquiryList = React.useCallback(
    async (request: StudentEnquiryListRequest) => {
      try {
        await dispatch(fetchStudentEnquiryList(request));
      } catch (error) {
        console.error('Error loading list:', error);
      }
    },
    [dispatch]
  );
  React.useEffect(() => {
    if (YearStatus === 'idle') {
      setAcademicYearFilter(defualtYear);
    }
    if (StudentEnquiryListStatus === 'idle') {
      dispatch(fetchYearList(adminId));
      dispatch(fetchClassList(adminId));
      loadStudentEnquiryList(currentStudentEnquiryListRequest);
    }
  }, [
    dispatch,
    adminId,
    currentStudentEnquiryListRequest,
    loadStudentEnquiryList,
    YearStatus,
    StudentEnquiryListStatus,
    defualtYear,
  ]);

  const toggleDescription = (id: number) => {
    setExpandedDescriptions((prev) => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  const handleSortClick = (event: React.MouseEvent<HTMLElement>) => {
    setSort(event.currentTarget);
  };

  const handleSortClose = () => {
    setSort(null);
  };

  const handleSortColumnChange = React.useCallback(
    (column?: string) => {
      console.log('column:', column);
      setSelectedSortColumn(column);

      loadStudentEnquiryList({
        ...currentStudentEnquiryListRequest,
        sortColumn: column ?? currentStudentEnquiryListRequest.sortColumn,
      });

      handleSortClose();
    },
    [loadStudentEnquiryList, currentStudentEnquiryListRequest]
  );

  const handleSortDirectionChange = React.useCallback(
    (direction?: 'asc' | 'desc') => {
      console.log('direction:', direction);
      setSelectedSortDirection(direction);

      loadStudentEnquiryList({
        ...currentStudentEnquiryListRequest,
        sortDirection: direction ?? currentStudentEnquiryListRequest.sortDirection,
      });

      handleSortClose();
    },
    [loadStudentEnquiryList, currentStudentEnquiryListRequest]
  );

  const handleSort = useCallback(
    (direction: 'asc' | 'desc') => {
      const newReq = { ...currentStudentEnquiryListRequest };
      console.log('Toggling direction', sortDirection + direction);
      setSelectedSortDirection(direction);
      if (direction === sortDirection) {
        // const sortDirectionNew = sortDirection === 'asc' ? 'desc' : 'asc';
        newReq.pageNumber = 1;
        newReq.sortDirection = direction;
        dispatch(setSortDirection(direction));
      } else {
        newReq.pageNumber = 1;
        newReq.sortDirection = direction;
        dispatch(setSortDirection(direction));
      }

      loadStudentEnquiryList(newReq);
      handleSortClose();
    },
    [currentStudentEnquiryListRequest, dispatch, loadStudentEnquiryList, sortDirection]
  );

  const handleReplyClick = (event: React.MouseEvent<Element, MouseEvent>, queryId: number) => {
    setAnchorEl(event.currentTarget);
    setCurrentCardId(queryId);
    // setEditingReply(reply ?? '');
    setReplyText(() => {
      if (queryId !== null) {
        const query = StudentEnquiryListData.find((f) => f.queryId === queryId);
        return query?.reply || '';
      }
      return '';
    });
  };

  const handlePopoverClose = () => {
    setAnchorEl(null);
    setReplyText('');
    setCurrentCardId(null);
  };

  const handleSaveReply = useCallback(async () => {
    setLoadingStatus(false);

    console.log('loadingStatus', loadingStatus);
    if (replyText.trim()) {
      setLoadingStatus(false);
      const Req: EnquiryReplyRequestData = { queryId: currentCardId, repliedBy: adminId, reply: replyText };
      const response = await dispatch(sendEnquiryReply(Req)).unwrap();
      console.log('response', Req);

      if (response) {
        // setFetchStatus('i');
        loadStudentEnquiryList(currentStudentEnquiryListRequest);
        setTimeout(() => {
          if (loadingStatus === false) {
            setLoadingStatus(true);
          }
        }, 1000);
      }
    }
    // setLoadingStatus(true);
    handlePopoverClose();
  }, [
    loadStudentEnquiryList,
    setLoadingStatus,
    loadingStatus,
    currentStudentEnquiryListRequest,
    adminId,
    currentCardId,
    replyText,
    dispatch,
  ]);

  const open = Boolean(anchorEl);

  const handleYearChange = (e: SelectChangeEvent) => {
    const selectedAcademicId = e.target.value;
    setAcademicYearFilter(parseInt(selectedAcademicId, 10));
    loadStudentEnquiryList({
      ...currentStudentEnquiryListRequest,
      filters: {
        ...currentStudentEnquiryListRequest.filters,
        academicId: parseInt(selectedAcademicId, 10),
      },
    });
  };

  const handleClassChange = (e: SelectChangeEvent) => {
    const selectedClass = parseInt(e.target.value, 10);
    if (selectedClass) {
      setClassFilter(selectedClass);
    }
    loadStudentEnquiryList({
      ...currentStudentEnquiryListRequest,
      pageNumber: 1,
      filters: { ...currentStudentEnquiryListRequest.filters, classId: selectedClass || 0 },
    });
  };

  useEffect(() => {
    dispatch(fetchYearList(adminId));
  }, [dispatch, adminId]);

  const handleReset = React.useCallback(
    (e: any) => {
      e.preventDefault();
      setClassFilter(-1);
      setAcademicYearFilter(defualtYear);
      setFromDateFilter('');
      setToDateFilter('');
      loadStudentEnquiryList({
        pageNumber: 1,
        pageSize: 20,
        sortColumn: 'classId',
        sortDirection: 'asc',
        filters: {
          academicId: -1,
          adminId,
          classId: -1,
          fromdate: '',
          todate: '',
        },
      });
    },
    [defualtYear, loadStudentEnquiryList, adminId]
  );

  const handlePageChange = useCallback(
    (event: unknown, newPage: number) => {
      loadStudentEnquiryList({ ...currentStudentEnquiryListRequest, pageNumber: newPage + 1 });
    },
    [currentStudentEnquiryListRequest, loadStudentEnquiryList]
  );

  const handleChangeRowsPerPage = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const newPageSize = +event.target.value;
      loadStudentEnquiryList({ ...currentStudentEnquiryListRequest, pageNumber: 1, pageSize: newPageSize });
    },
    [currentStudentEnquiryListRequest, loadStudentEnquiryList]
  );

  const pageProps = useMemo(
    () => ({
      rowsPerPageOptions: [10, 20, 30, 50, 100, 200, 300, 500],
      pageNumber: pagenumber - 1,
      pageSize: pagesize,
      totalRecords: totalrecords,
      onPageChange: handlePageChange,
      onRowsPerPageChange: handleChangeRowsPerPage,
    }),
    [pagenumber, pagesize, totalrecords, handlePageChange, handleChangeRowsPerPage]
  );

  const EnquiryListColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'studentName',
        headerLabel: 'Name',
        dataKey: 'studentName',
      },

      {
        name: 'class',
        dataKey: 'class',
        headerLabel: 'Class',
      },
      {
        name: 'admission',
        dataKey: 'admission',
        headerLabel: 'Admission',
      },
      {
        name: 'academicYear',
        dataKey: 'academicYear',
        headerLabel: 'Academic',
      },
    ],
    []
  );

  return (
    <Page title="Enquiry List">
      <EnquiryListRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, pt: { xs: 2, md: 3 } }}>
          <Stack pb={1} direction="row" justifyContent="space-between" alignItems="center" flexWrap="wrap">
            <Stack direction="row" alignItems="center" justifyContent="space-between" flex={1} whiteSpace="nowrap">
              <Typography variant="h6" fontSize={17} width="100%">
                Enquiry List
              </Typography>
              <Tooltip title="Sort">
                <IconButton color="primary" onClick={handleSortClick}>
                  <SwapVertIcon />
                </IconButton>
              </Tooltip>
              <Menu
                anchorEl={sort}
                open={Boolean(sort)}
                onClose={handleSortClose}
                anchorOrigin={{
                  vertical: 'bottom',
                  horizontal: 'right',
                }}
                transformOrigin={{
                  vertical: 'top',
                  horizontal: 'right',
                }}
              >
                {/* <MenuItem
                  selected={selectedSortColumn === 'classId'}
                  className="menuItem"
                  onClick={() => handleSort('classId')}
                >
                  <ListItemIcon className="customListItemIcon">
                    {sortDirection === 'asc' ? <PiSortAscendingBold /> : <PiSortDescending />}
                  </ListItemIcon>
                  Class
                </MenuItem> */}
                {/* <MenuItem
                  selected={selectedSortColumn === 'submittedDate'}
                  onClick={() => handleSortColumnChange('submittedDate', 'asc')}
                >
                  Submitted Date
                </MenuItem>
                <MenuItem
                  selected={selectedSortColumn === 'repliedDate'}
                  onClick={() => handleSortColumnChange('repliedDate')}
                >
                  Replied Date
                </MenuItem> */}
                <Divider sx={{ mt: 0, mb: 0 }} />
                <MenuItem selected={selectedSortDirection === 'asc'} onClick={() => handleSort('asc')}>
                  <ListItemIcon className="customListItemIcon">
                    <PiSortAscendingBold />
                  </ListItemIcon>
                  Ascending
                </MenuItem>
                <MenuItem selected={selectedSortDirection === 'desc'} onClick={() => handleSort('desc')}>
                  <ListItemIcon className="customListItemIcon">
                    <PiSortDescending />
                  </ListItemIcon>
                  Descending
                </MenuItem>
              </Menu>
              <Tooltip title="Search">
                <IconButton aria-label="" color="primary" sx={{ mr: 1 }} onClick={() => setShowFilter((x) => !x)}>
                  <SearchIcon />
                </IconButton>
              </Tooltip>
            </Stack>
          </Stack>
          <Stack>
            <Divider />
          </Stack>
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={5} pt={1} container columnSpacing={2} rowSpacing={1}>
                  <Grid item xxl={2} xl={2.5} lg={2.5} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Select
                        labelId="academicYearFilter"
                        id="academicYearFilterSelect"
                        value={academicYearFilter?.toString()}
                        // defaultValue={YearData[0]?.accademicId}
                        onChange={handleYearChange}
                        placeholder="Select Year"
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Year
                        </MenuItem>
                        {YearData.map((opt) => (
                          <MenuItem key={opt.accademicId} value={opt.accademicId}>
                            {opt.accademicTime}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={2.5} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Select Class
                      </Typography>
                      <Select
                        labelId="academicYearFilter"
                        id="academicYearFilterSelect"
                        value={classFilter?.toString()}
                        // defaultValue={YearData[0]?.accademicId}
                        onChange={handleClassChange}
                        placeholder="Select Class"
                      >
                        <MenuItem value={-1}>Select All</MenuItem>
                        {ClassData.map((opt) => (
                          <MenuItem key={opt.classId} value={opt.classId}>
                            {opt.className}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={2.5} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        From Date
                      </Typography>
                      <DatePickers
                        name="studentFromDate"
                        value={dayjs(fromDateFilter, 'DD/MM/YYYY')}
                        onChange={(e) => {
                          const formattedDate = e ? e.format('DD/MM/YYYY') : '';
                          setFromDateFilter(formattedDate);
                          console.log('fromdate::::', formattedDate);
                          loadStudentEnquiryList({
                            ...currentStudentEnquiryListRequest,
                            filters: {
                              ...currentStudentEnquiryListRequest.filters,
                              fromdate: formattedDate,
                            },
                          });
                        }}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={2.5} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        To Date
                      </Typography>
                      <DatePickers
                        name="studentToDate"
                        value={dayjs(toDateFilter, 'DD/MM/YYYY')}
                        onChange={(e) => {
                          const formattedDate = e ? e.format('DD/MM/YYYY') : '';
                          setToDateFilter(formattedDate);
                          console.log('todate::::', formattedDate);
                          loadStudentEnquiryList({
                            ...currentStudentEnquiryListRequest,
                            filters: {
                              ...currentStudentEnquiryListRequest.filters,
                              todate: formattedDate,
                            },
                          });
                        }}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item sm={1} xs={12}>
                    <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, sm: 3.79 } }}>
                      <Button variant="contained" color="secondary" fullWidth onClick={handleReset}>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Box className="card-container" pt={2}>
              <Grid container spacing={2} height="100%" px={0.5}>
                {StudentEnquiryListStatus === 'loading' && loadingStatus === true ? (
                  // Skeleton loader while loading
                  Array.from({ length: 4 }).map((_, index) => (
                    <Grid key={index} item xl={3} lg={6} md={12} sm={6} xs={12}>
                      <Card sx={{ p: 2, height: '100%' }}>
                        <Stack mb={2} direction="row" justifyContent="space-between" alignItems="center">
                          <Skeleton variant="text" width={100} height={20} />
                          <Skeleton variant="text" width={100} height={20} />
                        </Stack>
                        <Stack direction="row" alignItems="center" gap={2}>
                          <Skeleton variant="circular" width={50} height={50} />
                          <Stack alignItems="start">
                            <Skeleton variant="text" width={150} height={20} />
                            <Skeleton variant="text" width={100} height={15} />
                          </Stack>
                        </Stack>

                        <Stack gap={1.5} mt={1.5} alignItems="start">
                          <Stack
                            direction="row"
                            justifyContent="space-between"
                            flexWrap="wrap"
                            columnGap={1.5}
                            rowGap={1.5}
                          >
                            <Skeleton variant="text" width={150} height={15} />
                            <Skeleton variant="text" width={150} height={15} />
                          </Stack>
                          <Skeleton variant="text" width={200} height={20} />
                          <Skeleton variant="rectangular" sx={{ borderRadius: 1 }} width="100%" height={175} />
                        </Stack>
                      </Card>
                    </Grid>
                  ))
                ) : StudentEnquiryListData.length === 0 ? (
                  // Show "No Data Found" if no data is available
                  <Box
                    display="flex"
                    alignItems="center"
                    justifyContent="center"
                    width="100%"
                    height={{ xs: 'calc(100vh - 400px)', sm: 'calc(100vh - 400px)', lg: 'calc(100vh - 284px)' }}
                  >
                    <Stack direction="column" alignItems="center">
                      <img src={NoData} width="150px" alt="No Data" />
                      <Typography variant="subtitle2" mt={2} color="GrayText">
                        No data found!
                      </Typography>
                    </Stack>
                  </Box>
                ) : (
                  // Render actual content when data is loaded
                  StudentEnquiryListData.map((enquiry, cardIndex) => {
                    const isExpanded = expandedDescriptions[enquiry.queryId] || false;
                    const showViewMore = enquiry?.description?.length > 100;
                    const formattedEnquiredDate = dayjs(enquiry.enquiredDate).format('DD/MM/YYYY');
                    const formattedRepliedDateDate = dayjs(enquiry.repliedDate).format('DD/MM/YYYY');

                    return (
                      <Grid key={enquiry.queryId} item xl={3} lg={6} md={12} sm={6} xs={12} pb={2}>
                        <Card className="student_card" sx={{ p: 2, height: '100%' }}>
                          <Stack mb={2} direction="row" justifyContent="space-between" alignItems="center">
                            <Chip
                              size="small"
                              color="primary"
                              variant="outlined"
                              sx={{ fontSize: 12 }}
                              label={`Roll No : ${enquiry.rollNo}`}
                            />
                            <Stack direction="row" justifyContent="center" alignItems="center" gap={1}>
                              <Chip
                                size="small"
                                icon={<SuccessIcon />}
                                color={enquiry.status === 0 ? 'secondary' : 'success'}
                                sx={{ fontSize: 12 }}
                                label={`${enquiry.status === 0 ? 'Submitted' : 'Replied'}`}
                              />
                            </Stack>
                          </Stack>
                          <Stack direction="row" alignItems="center" gap={2}>
                            <Avatar sx={{ width: 50, height: 50 }} src={enquiry.image} />
                            <Stack alignItems="start">
                              <Typography variant="subtitle2">{enquiry.studentName}</Typography>
                              <Typography variant="subtitle2" fontSize={11}>
                                <span style={{ color: theme.palette.grey[600], fontWeight: '500' }}>Class : </span>
                                {enquiry.className}
                              </Typography>
                            </Stack>
                          </Stack>

                          <Stack gap={1.5} mt={1.5} alignItems="start">
                            <Stack
                              direction="row"
                              justifyContent="space-between"
                              flexWrap="wrap"
                              columnGap={1.5}
                              rowGap={1.5}
                            >
                              <Typography
                                variant="subtitle2"
                                fontSize={11}
                                display="flex"
                                gap={0.5}
                                alignItems="center"
                              >
                                <CalendarTodayIcon className="date" sx={{ fontSize: 12 }} />
                                <span className="date">Submitted Date</span>&nbsp;:&nbsp;{formattedEnquiredDate}
                              </Typography>
                              <Typography
                                variant="subtitle2"
                                fontSize={11}
                                display="flex"
                                gap={0.5}
                                alignItems="center"
                              >
                                <CalendarTodayIcon className="date" sx={{ fontSize: 12 }} />
                                <span className="date">Replied Date</span>&nbsp;:&nbsp;{formattedRepliedDateDate}
                              </Typography>
                            </Stack>
                            <Typography variant="subtitle2" fontSize={12}>
                              <span style={{ color: theme.palette.grey[600], fontWeight: '500' }}>
                                Subject&nbsp;:&nbsp;
                              </span>
                              {enquiry.subject}
                            </Typography>
                            <Tooltip title={showViewMore && enquiry.description}>
                              <Typography
                                variant="subtitle2"
                                fontSize={12}
                                sx={{
                                  display: '-webkit-box',
                                  WebkitLineClamp: isExpanded ? 'unset' : 3,
                                  WebkitBoxOrient: 'vertical',
                                  overflow: isExpanded ? 'visible' : 'hidden',
                                  textOverflow: 'ellipsis',
                                  minHeight: 55,
                                  mb: !showViewMore ? '10px' : '0px',
                                }}
                              >
                                <span style={{ color: theme.palette.grey[600], fontWeight: 500 }}>
                                  Query&nbsp;:&nbsp;
                                </span>{' '}
                                {isExpanded
                                  ? enquiry.description
                                  : enquiry.description?.split('\n').slice(0, 2).join('\n')}
                              </Typography>
                            </Tooltip>
                            {showViewMore && (
                              <Stack mt={-2} direction="row" justifyContent="end" width="100%">
                                <Button
                                  size="small"
                                  variant="text"
                                  sx={{ '&:hover': { backgroundColor: 'transparent' }, fontSize: 9, height: 15 }}
                                  onClick={() => toggleDescription(enquiry.queryId)}
                                >
                                  {isExpanded ? 'View Less' : 'View More'}
                                </Button>
                              </Stack>
                            )}

                            <div style={{ width: '100%' }}>
                              {enquiry.reply && (
                                <Stack mb={0.5} direction="row" justifyContent="space-between" alignItems="center">
                                  <Typography color={theme.palette.grey[600]} variant="body1" fontSize={11}>
                                    Reply
                                  </Typography>
                                  <IconButton size="small" onClick={(e) => handleReplyClick(e, enquiry.queryId)}>
                                    <ModeEditIcon sx={{ fontSize: '16px' }} />
                                  </IconButton>
                                </Stack>
                              )}
                              {!enquiry.reply && (
                                <Chip
                                  icon={<ReplyIcon />}
                                  size="small"
                                  color="success"
                                  sx={{ fontSize: 12, mt: isExpanded ? 2 : 0, mb: 1.5, height: 20 }}
                                  label="Reply"
                                  clickable
                                  onClick={(event) => handleReplyClick(event, enquiry.queryId)}
                                />
                              )}
                              <Card
                                sx={{
                                  width: '100%',
                                  height: 60,
                                  boxShadow: 0,
                                  border: 1,
                                  borderColor: isLight ? theme.palette.grey[300] : theme.palette.grey[800],
                                  p: 1,
                                  borderRadius: 1,
                                }}
                              >
                                {enquiry.reply !== null ? (
                                  <Typography variant="subtitle2" fontSize={11}>
                                    {enquiry.reply}
                                  </Typography>
                                ) : (
                                  <Stack alignItems="center" justifyContent="center" height="100%">
                                    <SpeakerNotesOffIcon fontSize="small" color="secondary" />
                                  </Stack>
                                )}
                              </Card>
                            </div>
                          </Stack>
                        </Card>
                      </Grid>
                    );
                  })
                )}
              </Grid>
            </Box>

            <TablePagination
              sx={{ height: 60 }}
              rowsPerPageOptions={pageProps.rowsPerPageOptions}
              component="div"
              count={pageProps.totalRecords}
              rowsPerPage={pageProps.pageSize}
              page={pageProps.pageNumber}
              onPageChange={pageProps.onPageChange}
              onRowsPerPageChange={pageProps.onRowsPerPageChange}
              showFirstButton
              showLastButton
            />
          </div>
        </Card>
      </EnquiryListRoot>

      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handlePopoverClose}
        anchorOrigin={{ vertical: 'top', horizontal: replyText === '' ? 'left' : 'right' }}
        transformOrigin={{ vertical: 'bottom', horizontal: replyText === '' ? 'left' : 'right' }}
      >
        <div style={{ padding: 20, width: '300px' }}>
          <TextField
            fullWidth
            disabled={isSubmitting}
            label={replyText !== '' ? 'Edit your reply' : 'Write your reply'}
            value={replyText}
            onChange={(e) => setReplyText(e.target.value)}
            multiline
            rows={5}
          />
          <Stack direction="row" justifyContent="end">
            <LoadingButton
              // startIcon={<}
              loadingPosition="start"
              disabled={isSubmitting || replyText === ''}
              loading={isSubmitting}
              size="small"
              variant="contained"
              color="primary"
              style={{ marginTop: '8px', width: isSubmitting ? '120px' : 'fit-content' }}
              onClick={handleSaveReply}
            >
              {isSubmitting ? 'Saving...' : 'Save'}
            </LoadingButton>
          </Stack>
        </div>
      </Popover>
    </Page>
  );
}

export default EnquiryList;
