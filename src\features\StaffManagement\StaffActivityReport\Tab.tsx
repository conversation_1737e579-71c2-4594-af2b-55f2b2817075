import * as React from 'react';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import { Checkbox, Stack, useTheme } from '@mui/material';
import LiveClass from './LiveClass';
import OnlineVideoClass from './OnlineVideoClass';
import Assignment from './Assignment';
import DescriptiveExam from './DescriptiveExam';
import ObjectExam from './ObjectExam';
import StudyMaterial from './StudyMaterial';
import StudyMaterialNew from './StudyMaterialNew';
import Enquiry from './Enquiry';
import LeaveApproved from './LeaveApproved';
import Attendance from './Attendance';

const tabButtons = [
  { id: 1, tabTitle: 'Live Class', status: '0/26' },
  { id: 2, tabTitle: 'Online Video Class', status: '200' },
  { id: 3, tabTitle: 'Assignment', status: '0/15' },
  { id: 4, tabTitle: 'Descriptive Exam', status: '0/3' },
  { id: 5, tabTitle: 'Object Exam', status: '0/5' },
  { id: 6, tabTitle: 'Study Materials Old', status: '0/26' },
  { id: 6, tabTitle: 'Study Materials New', status: '50' },
  { id: 6, tabTitle: 'Enquiry', status: '100' },
  { id: 6, tabTitle: 'Attendance', status: '60' },
  { id: 6, tabTitle: 'Leave Approved', status: '59' },
];
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box mt={3}>{children}</Box>}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`,
  };
}

export default function StaffActivtyTabPanel() {
  const [value, setValue] = React.useState(0);
  const theme = useTheme();

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  return (
    <Box sx={{ width: '100%' }}>
      <Box sx={{ borderBottom: 0, borderColor: 'divider' }}>
        <Tabs
          variant="scrollable"
          scrollButtons
          allowScrollButtonsMobile
          value={value}
          onChange={handleChange}
          aria-label="basic tabs example"
        >
          {tabButtons.map((tab) => (
            <Tab
              sx={{ borderRadius: 2 }}
              label={
                <Box
                  key={tab.id}
                  border={2}
                  borderColor={theme.palette.grey[200]}
                  borderRadius={2}
                  p={1}
                  bgcolor={theme.palette.primary.lighter}
                >
                  <Stack direction="row" justifyContent="space-between" gap={2} alignItems="center">
                    <Typography variant="h6" color="#000" fontSize={17}>
                      {tab.status}
                    </Typography>
                    <Checkbox />
                  </Stack>
                  <Stack textAlign="start">
                    <Typography variant="subtitle2" fontSize={10}>
                      {tab.tabTitle}
                    </Typography>
                  </Stack>
                </Box>
              }
            />
          ))}
        </Tabs>
      </Box>
      <TabPanel value={value} index={0}>
        <LiveClass />
      </TabPanel>
      <TabPanel value={value} index={1}>
        <OnlineVideoClass />
      </TabPanel>
      <TabPanel value={value} index={2}>
        <Assignment />
      </TabPanel>
      <TabPanel value={value} index={3}>
        <DescriptiveExam />
      </TabPanel>
      <TabPanel value={value} index={4}>
        <ObjectExam />
      </TabPanel>
      <TabPanel value={value} index={5}>
        <StudyMaterial />
      </TabPanel>
      <TabPanel value={value} index={6}>
        <StudyMaterialNew />
      </TabPanel>
      <TabPanel value={value} index={7}>
        <Enquiry />
      </TabPanel>
      <TabPanel value={value} index={8}>
        <Attendance />
      </TabPanel>
      <TabPanel value={value} index={9}>
        <LeaveApproved />
      </TabPanel>
    </Box>
  );
}
