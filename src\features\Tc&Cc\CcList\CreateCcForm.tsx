/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import {
  Autocomplete,
  Grid,
  Stack,
  TextField,
  Button,
  Typography,
  Box,
  FormControl,
  Select,
  MenuItem,
  RadioGroup,
  FormControlLabel,
  Radio,
} from '@mui/material';
import styled from 'styled-components';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import { CLASS_SELECT, STATUS_OPTIONS, SUBJECT_SELECT, YEAR_SELECT } from '@/config/Selection';
import SelectDateTimePicker from '@/components/shared/Selections/TimePicker';
import InputFileUpload from '@/components/shared/Selections/FilesUpload';
import DateSelect from '@/components/shared/Selections/DateSelect';

const CreateCcFormRoot = styled.div`
  width: 100%;
  padding: 1.5rem;
`;

function CreateCcForm({ onClose }: any) {
  return (
    <CreateCcFormRoot>
      <form noValidate>
        <Grid container spacing={2} direction="row" justifyContent="space-between">
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                SL.No
              </Typography>
              <TextField placeholder="Enter sl.number " name="" />
            </FormControl>
          </Grid>
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Admission No
              </Typography>
              <TextField placeholder="Enter admissinon no " name="" />
            </FormControl>
          </Grid>
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Name of the pupil
              </Typography>
              <TextField placeholder="Enter student name " name="" />
            </FormControl>
          </Grid>
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Father's / Guardian Name
              </Typography>
              <TextField placeholder="Enter name " name="" />
            </FormControl>
          </Grid>
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Date of Birth (in Figure)
              </Typography>
              <TextField placeholder="Enter date of birth " name="" />
            </FormControl>
          </Grid>
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Date of Birth (in Figure)
              </Typography>
              <TextField placeholder="Enter date of birth " name="" />
            </FormControl>
          </Grid>
          <Grid item lg={12} xs={12}>
            <FormControl fullWidth>
              <Typography variant="subtitle2" fontSize={14} color="GrayText">
                Student Conduct
              </Typography>
              <TextField placeholder="Enter student conduct " name="" />
            </FormControl>
          </Grid>
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="h6" fontSize={14} mb={1}>
                Passed Class
              </Typography>
              <TextField placeholder="Enterclass" name="" />
            </FormControl>
          </Grid>
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="h6" fontSize={14} mb={1}>
                Passed Year
              </Typography>
              <TextField placeholder="Enter year" name="" />
            </FormControl>
          </Grid>
          <Grid item lg={5.5} xs={12}>
            <FormControl fullWidth>
              <Typography variant="h6" fontSize={14} mb={1}>
                Gender
              </Typography>
              <RadioGroup
                defaultValue="yes"
                row
                aria-labelledby="demo-row-radio-buttons-group-label"
                name="row-radio-buttons-group"
              >
                <FormControlLabel value="Male" control={<Radio checkedIcon={<CheckCircleIcon />} />} label="Yes" />
                <FormControlLabel value="Female" control={<Radio checkedIcon={<CheckCircleIcon />} />} label="No" />
              </RadioGroup>
            </FormControl>
          </Grid>
        </Grid>
        <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 3 }}>
          <Stack spacing={2} direction="row">
            <Button variant="contained" color="secondary">
              Cancel
            </Button>
            <Button variant="contained" color="primary">
              Save
            </Button>
          </Stack>
        </Box>
      </form>
    </CreateCcFormRoot>
  );
}

export default CreateCcForm;
