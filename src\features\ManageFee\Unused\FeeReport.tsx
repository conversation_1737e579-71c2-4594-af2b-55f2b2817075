/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Typography,
  Card,
  Avatar,
  IconButton,
  Collapse,
  Select,
  MenuItem,
  FormControl,
  useTheme,
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import { IoIosArrowUp } from 'react-icons/io';
import styled from 'styled-components';
import { CLASS_SELECT, STATUS_OPTIONS, YEAR_SELECT } from '@/config/Selection';
import { StudentInfoData } from '@/config/StudentDetails';
import DateSelect from '@/components/shared/Selections/DateSelect';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import DatePickers from '@/components/shared/Selections/DatePicker';
import { Tooltip } from '@mui/material';

const FeeReportRoot = styled.div`
  padding: 1rem;
  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    /* @media screen and (max-width: 996px) {
      height: 100%;
    } */

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 50px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

const array = [
  {
    slNo: 1,
    feeTitle: 'Tution Fee',
    total: '10000',
    paid: '6800',
    pending: '5700',
    discount: '0.00',
    scholarship: '0.00',
    balance: '5700',
  },
  {
    slNo: 2,
    feeTitle: 'Tution Fee',
    total: '10000',
    paid: '6800',
    pending: '5700',
    discount: '0.00',
    scholarship: '0.00',
    balance: '5700',
  },
  {
    slNo: 3,
    feeTitle: 'Tution Fee',
    total: '10000',
    paid: '6800',
    pending: '5700',
    discount: '0.00',
    scholarship: '0.00',
    balance: '5700',
  },
  {
    slNo: 4,
    feeTitle: 'Tution Fee',
    total: '10000',
    paid: '6800',
    pending: '5700',
    discount: '0.00',
    scholarship: '0.00',
    balance: '5700',
  },
  {
    slNo: 5,
    feeTitle: 'Tution Fee',
    total: '10000',
    paid: '6800',
    pending: '5700',
    discount: '0.00',
    scholarship: '0.00',
    balance: '5700',
  },
];
export default function FeeReport() {
  const theme = useTheme();
  const [showFilter, setShowFilter] = useState(false);
  const FeeReportListColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'slNo',
        headerLabel: 'Sl.No',
        dataKey: 'slNo',
        sortable: true,
      },
      {
        name: 'feeTitle',
        headerLabel: 'Fees Title',
        dataKey: 'feeTitle',
      },
      {
        name: 'total',
        headerLabel: 'Total',
        dataKey: 'total',
      },
      {
        name: 'paid',
        headerLabel: 'Paid',
        dataKey: 'paid',
      },
      {
        name: 'pending',
        dataKey: 'pending',
        headerLabel: 'Pending',
      },
      {
        name: 'discount',
        dataKey: 'discount',
        headerLabel: 'Discount',
      },
      {
        name: 'scholarship',
        headerLabel: 'Scholarship',
        dataKey: 'scholarship',
      },
      {
        name: 'balance',
        headerLabel: 'Balance',
        dataKey: 'balance',
      },
    ],
    []
  );

  const getRowKey = useCallback((row: any) => row.classId, []);
  return (
    <Page title="Fees Collection">
      <FeeReportRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between" mb={2} alignItems="center">
            <Typography variant="h6" fontSize={17} width="80%">
              Fee Report
            </Typography>
          </Stack>
          <Divider />
          <div className="card-main-body">
            <Box display="flex" justifyContent="center" alignItems="ceneter" py={1} bgcolor={theme.palette.grey[100]}>
              <Stack direction="column" alignItems="center">
                <Typography variant="subtitle2" fontSize={18}>
                  Alex Peter
                </Typography>
                <Typography variant="subtitle1" fontSize={14} color="secondary">
                  Admission No. : 89555
                </Typography>
                <Typography variant="subtitle1" fontSize={14} color="secondary">
                  VII-A
                </Typography>
              </Stack>
            </Box>

            <Paper className="card-table-container" sx={{ marginTop: '12px' }}>
              <DataTable columns={FeeReportListColumns} data={array} getRowKey={getRowKey} fetchStatus="success" />
            </Paper>
          </div>
        </Card>
      </FeeReportRoot>
      {/* <TemporaryDrawer
        onClose={toggleDrawerClose}
        state={drawerOpen}
        DrawerContent={<EditList onClose={toggleDrawerClose} open={handleClickOpen} />}
      /> */}
      {/* <Popup size="md" state={popup} onClose={handleClickClose} popupContent={<EditedTable />} />
      <Popup
        size="xs"
        state={Delete}
        onClose={handleClickCloseDelete}
        popupContent={<DeleteMessage message="Are you sure want to delete?" />}
      /> */}
    </Page>
  );
}
