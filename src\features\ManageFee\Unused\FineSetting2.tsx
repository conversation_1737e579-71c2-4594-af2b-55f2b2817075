/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Paper,
  TextField,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Typography,
  Card,
  Checkbox,
  Stack,
} from '@mui/material';
import styled from 'styled-components';
import { StudentMappedList } from '@/config/TableData';
import { YEAR_SELECT } from '@/config/Selection';
// import { EditList } from './EditList';
// import { EditedTable } from './EditedTable';

const FineSetting2Root = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 16px;
`;

export default function FineSetting2() {
  return (
    <Page title="Fees Setting">
      <FineSetting2Root>
        <Card elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17}>
              Fine Setting
            </Typography>
            {/* <Box sx={{ display: 'flex', justifyContent: 'end', pb: 1 }}>
              <Button sx={{ borderRadius: '20px', mr: 1 }} variant="outlined">
                Fixed
              </Button>
              <Button sx={{ borderRadius: '20px' }} variant="outlined">
                Variable
              </Button>
            </Box> */}
          </Stack>
          <Divider />
          <Grid pb={4} pt={2} container spacing={3} justifyContent="space-between">
            <Grid item md={3} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Accademic Year
              </Typography>
              <Autocomplete
                options={YEAR_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select Year" />}
              />
            </Grid>
          </Grid>

          <Box>
            <Paper
              sx={{
                border: `.0625rem solid #e8e8e9`,
                width: '100%',
                overflow: 'auto',
                '&::-webkit-scrollbar': {
                  width: 0,
                },
              }}
            >
              <TableContainer
                sx={{
                  maxHeight: 410,
                  width: { xs: '43.75rem', md: '100%' },
                  '&::-webkit-scrollbar': {
                    width: 0,
                  },
                }}
              >
                <Table stickyHeader aria-label="simple table">
                  <TableHead>
                    <TableRow sx={{ height: '50px' }}>
                      <TableCell>
                        <Checkbox /> Select All Standards
                      </TableCell>
                      <TableCell align="center">Fixed Fine Date</TableCell>
                      <TableCell align="center">Fine Amount</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {StudentMappedList.map((row) => (
                      <TableRow sx={{ height: '50px' }}>
                        <TableCell sx={{ fontWeight: 600 }}>
                          <Checkbox />
                          {row.title}
                        </TableCell>
                        <TableCell align="center">
                          <TextField placeholder="00" size="small" sx={{ width: '50%' }} />
                        </TableCell>
                        <TableCell align="center">
                          <TextField placeholder="00" size="small" sx={{ width: '50%' }} />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Paper>
          </Box>
        </Card>
      </FineSetting2Root>
      {/* <TemporaryDrawer
        onClose={toggleDrawerClose}
        state={drawerOpen}
        DrawerContent={<EditList onClose={toggleDrawerClose} open={handleClickOpen} />}
      />
      <Popup size="md" state={popup} onClose={handleClickClose} popupContent={<EditedTable />} />
      <Popup
        size="xs"
        state={Delete}
        onClose={handleClickCloseDelete}
        popupContent={<DeleteMessage message="Are you sure want to delete?" />}
      /> */}
    </Page>
  );
}
