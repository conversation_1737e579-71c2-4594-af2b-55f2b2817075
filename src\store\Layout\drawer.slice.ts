import { createSlice } from '@reduxjs/toolkit';
import type { DrawerState } from '@/types/Layout';
import type { RootState } from '@/store';

const initialState: DrawerState = {
  drawerOpen: false,
};

export const drawerSlice = createSlice({
  name: 'drawer',
  initialState,
  reducers: {
    toggleDrawer: (state) => {
      state.drawerOpen = !state.drawerOpen;
    },
  },
});

export const { toggleDrawer } = drawerSlice.actions;

export const getDrawerState = (state: RootState) => state.drawer.drawerOpen;

export default drawerSlice.reducer;
