/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable no-nested-ternary */
/* eslint-disable react/no-array-index-key */
/* eslint-disable jsx-a11y/alt-text */
import React, { FormEvent, useCallback, useState } from 'react';
import {
  Divider,
  Grid,
  Paper,
  Stack,
  Button,
  Typography,
  Box,
  FormControl,
  Select,
  MenuItem,
  SelectChangeEvent,
  useTheme,
  Tooltip,
} from '@mui/material';
import styled from 'styled-components';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useAppSelector } from '@/hooks/useAppSelector';
import {
  getClassDivisionListStatus,
  getYearData,
  getMessageTempSubmitting,
  getNotificationSubmitting,
} from '@/config/storeSelectors';
import useAuth from '@/hooks/useAuth';
import { fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import { fetchClassDivision, messageSendToClassDivision } from '@/store/MessageBox/messageBox.thunks';
import LoadingButton from '@mui/lab/LoadingButton';
import { SendToClassDivisionType, ClassDivisionDataType } from '@/types/MessageBox';
import { SendToClassDivisionType as SendToClassDivisionType2 } from '@/types/Notification';
import SendIcon from '@mui/icons-material/Send';
import ErrorMsg from '@/assets/MessageIcons/error-message.gif';
import ErrorMsg1 from '@/assets/MessageIcons/error-message1.gif';
import ErrorMsg2 from '@/assets/MessageIcons/error-message2.gif';
import successIcon from '@/assets/MessageIcons/success.json';
import { SuccessIcon } from '@/theme/overrides/CustomIcons';
import errorIcon from '@/assets/MessageIcons/error-message.json';
import Lottie from 'lottie-react';
import SuccessMsg from '@/assets/MessageIcons/message-success.gif';
import { SendPopupProps } from '@/types/Common';
import { notificationSendToClassDivision } from '@/store/AppNotification/appNotification.thunks';
import { SuccessMessage } from '../Popup/SuccessMessage';
import { ErrorMessage } from '../Popup/ErrorMessage';
import { useConfirm } from '../Popup/Confirmation';
import { voiceMessageSendToClassDivision } from '@/store/VoiceMessage/voiceMessage.thunk';
import { SendVoiceToClassDivision } from '@/types/VoiceMessage';

const ClassDivisionRoot = styled.div`
  width: 100%;
  padding: 8px 24px;
`;

export type ClassDivisionListRequest = {
  adminId: number;
  academicId: number;
  groupId: number;
};
interface ClassDivisionWithStatus extends ClassDivisionDataType {
  sendStatus: 'Success' | 'Failed' | '' | undefined;
}

function ClassDivision({ messageId, notificationId, isSubmitting, voiceId, templateId }: SendPopupProps) {
  const { user } = useAuth();
  const theme = useTheme();
  const dispatch = useAppDispatch();
  const { confirm } = useConfirm();
  const adminId: number = user ? user.accountId : 0;
  const [sendResults, setSendResults] = useState<ClassDivisionWithStatus[]>([]);
  const [selectedRows, setSelectedRows] = useState<ClassDivisionWithStatus[]>([]);
  const YearData = useAppSelector(getYearData);
  const defaultYearId = 10;
  const [academicYearFilter, setAcademicYearFilter] = useState(defaultYearId);
  const ClassDivisionStatus = useAppSelector(getClassDivisionListStatus);
  const [selectedData, setSelectedData] = useState<ClassDivisionWithStatus[]>([]);
  const [individualSendLoadingMap, setIndividualSendLoadingMap] = useState<Record<string, boolean>>({});
  const [isLoading, setIsloading] = useState<boolean>(false);
  const [errMsgTooltip, setErrMsgTooltip] = useState('');
  const [showSuccessMap, setShowSuccessMap] = useState<{ [key: string]: boolean }>({});
  const [disabledCheckBoxes, setDisabledCheckBoxes] = useState<boolean[]>([]);

  const initialClassDivisionListRequest = React.useMemo(
    () => ({
      adminId,
      academicId: defaultYearId,
    }),
    [adminId, defaultYearId]
  );
  const currentClassDivisionListRequest = React.useMemo(
    () => ({
      adminId,
      academicId: academicYearFilter,
    }),
    [adminId, academicYearFilter]
  );

  const loadClassDivisionList = useCallback(
    async (request: { adminId: number; academicId: number }) => {
      try {
        const data = await dispatch(fetchClassDivision(request)).unwrap();
        const dataWithStatus = data.map((item) => ({
          ...item,
          sendStatus: sendResults.find((i) => i.classId === item.classId)?.sendStatus,
        }));
        setSelectedData(dataWithStatus);
        setSelectedRows([]);
      } catch (error) {
        console.error('Error loading Class Divisions list:', error);
      }
    },
    [dispatch]
  );

  React.useEffect(() => {
    dispatch(fetchYearList(adminId));
    loadClassDivisionList(initialClassDivisionListRequest);
  }, [dispatch, adminId, initialClassDivisionListRequest, loadClassDivisionList, academicYearFilter]);

  const handleYearChange = (e: SelectChangeEvent) => {
    setAcademicYearFilter(parseInt(YearData.filter((item) => item.accademicId === e.target.value)[0].accademicId, 10));
    loadClassDivisionList({ ...currentClassDivisionListRequest, academicId: parseInt(e.target.value, 10) });
    setSelectedRows([]);
  };

  const handleReset = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      setAcademicYearFilter(parseInt(YearData[0].accademicId, 10));
      setDisabledCheckBoxes([]);
      loadClassDivisionList(currentClassDivisionListRequest);
    },
    [YearData]
  );
  const handleCancel = () => {
    loadClassDivisionList(currentClassDivisionListRequest);
    setDisabledCheckBoxes([]);
  };
  const handleSubmit = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      loadClassDivisionList(currentClassDivisionListRequest);
      setSelectedRows([]);
    },
    [loadClassDivisionList, currentClassDivisionListRequest]
  );

  // Send Messages //

  const handleSendMessage = useCallback(async () => {
    setIsloading(true);
    try {
      if (!selectedRows || selectedRows.length === 0) {
        const errorMessage = (
          <ErrorMessage icon={ErrorMsg1} message="Please Select Atleast one Class Division to sent" />
        );
        await confirm(errorMessage, 'Class division not selected', { okLabel: 'Ok', showOnlyOk: true });
        return;
      }
      const sendRequests: SendToClassDivisionType[] = await Promise.all(
        selectedRows?.map(async (item) => {
          const { accademicTime, ...rest } = item;
          const sendReq = { messageId, adminId, ...rest };
          return sendReq;
        }) || []
      );
      const messageSendCount = sendRequests.length;

      sendRequests.map((row) => setIndividualSendLoadingMap((prevMap) => ({ ...prevMap, [row.classId]: true })));
      const actionResult = await dispatch(messageSendToClassDivision(sendRequests));

      sendRequests.map((row) => setIndividualSendLoadingMap((prevMap) => ({ ...prevMap, [row.classId]: false })));

      if (actionResult && Array.isArray(actionResult.payload)) {
        const results: ClassDivisionWithStatus[] = actionResult.payload;
        setSendResults([...sendResults, ...results]);

        const errorMessages = results.find((result) => result.sendStatus === 'Failed');
        const successMessages = results.find((result) => result.sendStatus === 'Success');
        if (!errorMessages) {
          const successMessage = (
            <SuccessMessage icon={SuccessMsg} message={`Message sent to ${messageSendCount} Recipient.`} />
          );
          await confirm(successMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        } else if (!successMessages) {
          const errorMessage = (
            <ErrorMessage
              icon={ErrorMsg2}
              message="No messages sent to any Class Divisions, Please check the numbers and Try again"
            />
          );
          await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        } else {
          const errorMessage = (
            <ErrorMessage
              icon={ErrorMsg}
              message="Error sending messages to some Class Divisions, Please recheck and Try again"
            />
          );
          await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        }
        const dataResult = selectedData.map((item) => ({
          ...item,
          sendStatus:
            item.sendStatus !== 'Success' && item.sendStatus !== 'Failed'
              ? results.find((result) => result.classId === item.classId)?.sendStatus
              : item.sendStatus,
        }));

        // setSelectedRows([]);
        setSelectedData(dataResult);
      } else {
        const errorMessage = <ErrorMessage message="Error while sending messages, Please Try again" />;
        await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
      }
      setSelectedRows([]);

      sendRequests.map((row) => setShowSuccessMap((prevMap) => ({ ...prevMap, [row.classId]: true })));
      // Update disabled state for each selected row
      const updatedDisabledCheckBoxes = selectedData.map((selectedItem) =>
        selectedRows.includes(selectedItem)
          ? !disabledCheckBoxes[selectedData.indexOf(selectedItem)]
          : disabledCheckBoxes[selectedData.indexOf(selectedItem)]
      );

      setDisabledCheckBoxes(updatedDisabledCheckBoxes);

      setTimeout(() => {
        sendRequests.map((row) => setShowSuccessMap((prevMap) => ({ ...prevMap, [row.classId]: false })));
      }, 5000);
    } catch (error) {
      console.error('Error sending messages:', error);
    }
  }, [dispatch, confirm, messageId, selectedRows, setSelectedRows, adminId, selectedData, sendResults]);

  // const handleSendIndivitual = useCallback(
  //   async (row: ClassDivisionWithStatus) => {
  //     try {
  //       const { accademicTime, ...rest } = row;
  //       const sendReq = [];
  //       sendReq[0] = { messageId, adminId, ...rest };
  //       const actionResult = await dispatch(messageSendToClassDivision([...sendReq]));
  //       if (actionResult && Array.isArray(actionResult.payload)) {
  //         const results: ClassDivisionWithStatus[] = actionResult.payload;
  //         console.log(results);
  //         setSendResults([...sendResults, ...results]);
  //         const dataResult = selectedData.map((item) => ({
  //           ...item,
  //           sendStatus:
  //             item.sendStatus !== 'Success' && item.sendStatus !== 'Failed'
  //               ? results.find((result) => result.classId === item.classId)?.sendStatus
  //               : item.sendStatus,
  //         }));
  //         setSelectedData(dataResult);
  //       } else {
  //         const errorMessage = <ErrorMessage message="Error while sending messages, Please Try again" />;
  //         await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
  //       }
  //     } catch (error) {
  //       console.error('Error sending messages:', error);
  //     }
  //   },
  //   [dispatch, confirm, messageId, adminId, selectedData, sendResults]
  // );

  const handleSendMessageIndivitual = useCallback(
    async (row: ClassDivisionWithStatus, rowIndex: number) => {
      setIsloading(false);
      try {
        const { accademicTime, ...rest } = row;

        // Check if the row is already loading, if yes, return
        if (individualSendLoadingMap[row.classSectionName]) {
          return;
        }

        // Set loading state for the specific row
        setIndividualSendLoadingMap((prevMap) => ({ ...prevMap, [row.classId]: true }));

        const updatedDisabledCheckBoxes = [...disabledCheckBoxes];
        updatedDisabledCheckBoxes[rowIndex] = !updatedDisabledCheckBoxes[rowIndex];
        setDisabledCheckBoxes(updatedDisabledCheckBoxes);

        const sendRequests: SendToClassDivisionType[] = [{ messageId, adminId, ...rest }];

        const actionResult = await dispatch(messageSendToClassDivision(sendRequests));

        // Reset loading state after the action is complete
        setIndividualSendLoadingMap((prevMap) => ({ ...prevMap, [row.classId]: false }));

        if (actionResult && Array.isArray(actionResult.payload)) {
          const results: ClassDivisionWithStatus[] = actionResult.payload;

          // Update the sendResults state based on the previous state
          setSendResults((prevResults) => [...prevResults, ...results]);

          // Update the selectedData state with the new status based on the previous state
          setSelectedData((prevResults) =>
            prevResults.map((item) => {
              const resultForItem = results.find((result) => result.classId === item.classId);

              if (resultForItem) {
                return {
                  ...item,
                  sendStatus:
                    item.sendStatus !== 'Success' && item.sendStatus !== 'Failed'
                      ? resultForItem.sendStatus
                      : item.sendStatus,
                };
              }

              return item;
            })
          );
          setSelectedRows([]);

          const errorMessages = results.find((result) => result.sendStatus === 'Failed');
          if (errorMessages) {
            setErrMsgTooltip('No messages sent to any Class Sections, Please check the numbers and Try again');
          } else {
            setErrMsgTooltip('Error sending messages to some Class Sections, Please recheck and Try again');
          }
          setSelectedRows([]);
        } else {
          setErrMsgTooltip('Error while sending messages, Please Try again');
        }
        setShowSuccessMap((prevMap) => ({ ...prevMap, [row.classId]: true }));

        setTimeout(() => {
          setShowSuccessMap((prevMap) => ({ ...prevMap, [row.classId]: false }));
          // setIsButtonDisabledMap((prevMap) => ({ ...prevMap, [row.classId]: true }));
        }, 5000);
      } catch (error) {
        console.error('Error sending messages:', error);
      }
    },
    [dispatch, confirm, messageId, adminId, selectedData, setSendResults, individualSendLoadingMap]
  );

  // send Notifications //

  const handleSendNotification = useCallback(async () => {
    setIsloading(true);
    try {
      if (!selectedRows || selectedRows.length === 0) {
        const errorMessage = (
          <ErrorMessage icon={ErrorMsg1} message="Please Select Atleast one Class Division to sent" />
        );
        await confirm(errorMessage, 'Class division not selected', { okLabel: 'Ok', showOnlyOk: true });
        return;
      }
      const sendRequests: SendToClassDivisionType2[] = await Promise.all(
        selectedRows?.map(async (item) => {
          const { accademicTime, ...rest } = item;
          const sendReq = { notificationId, adminId, ...rest };
          return sendReq;
        }) || []
      );
      const messageSendCount = sendRequests.length;

      sendRequests.map((row) => setIndividualSendLoadingMap((prevMap) => ({ ...prevMap, [row.classId]: true })));
      const actionResult = await dispatch(notificationSendToClassDivision(sendRequests));

      sendRequests.map((row) => setIndividualSendLoadingMap((prevMap) => ({ ...prevMap, [row.classId]: false })));

      if (actionResult && Array.isArray(actionResult.payload)) {
        const results: ClassDivisionWithStatus[] = actionResult.payload;
        setSendResults([...sendResults, ...results]);

        const errorMessages = results.find((result) => result.sendStatus === 'Failed');
        const successMessages = results.find((result) => result.sendStatus === 'Success');
        if (!errorMessages) {
          const successMessage = (
            <SuccessMessage icon={SuccessMsg} message={`Notification sent to ${messageSendCount} Recipient.`} />
          );
          await confirm(successMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        } else if (!successMessages) {
          const errorMessage = (
            <ErrorMessage
              icon={ErrorMsg2}
              message="No notifications sent to any Class Divisions, Please check the numbers and Try again"
            />
          );
          await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        } else {
          const errorMessage = (
            <ErrorMessage
              icon={ErrorMsg}
              message="Error sending notifications to some Class Divisions, Please recheck and Try again"
            />
          );
          await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        }
        const dataResult = selectedData.map((item) => ({
          ...item,
          sendStatus:
            item.sendStatus !== 'Success' && item.sendStatus !== 'Failed'
              ? results.find((result) => result.classId === item.classId)?.sendStatus
              : item.sendStatus,
        }));

        // setSelectedRows([]);
        setSelectedData(dataResult);
      } else {
        const errorMessage = <ErrorMessage message="Error while sending notifications, Please Try again" />;
        await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
      }
      setSelectedRows([]);

      sendRequests.map((row) => setShowSuccessMap((prevMap) => ({ ...prevMap, [row.classId]: true })));
      // Update disabled state for each selected row
      const updatedDisabledCheckBoxes = selectedData.map((selectedItem) =>
        selectedRows.includes(selectedItem)
          ? !disabledCheckBoxes[selectedData.indexOf(selectedItem)]
          : disabledCheckBoxes[selectedData.indexOf(selectedItem)]
      );

      setDisabledCheckBoxes(updatedDisabledCheckBoxes);

      setTimeout(() => {
        sendRequests.map((row) => setShowSuccessMap((prevMap) => ({ ...prevMap, [row.classId]: false })));
      }, 5000);
    } catch (error) {
      console.error('Error sending notifications:', error);
    }
  }, [dispatch, confirm, notificationId, selectedRows, setSelectedRows, adminId, selectedData, sendResults]);

  const handleSendNotificationIndivitual = useCallback(
    async (row: ClassDivisionWithStatus, rowIndex: number) => {
      setIsloading(false);
      try {
        const { accademicTime, ...rest } = row;

        // Check if the row is already loading, if yes, return
        if (individualSendLoadingMap[row.classSectionName]) {
          return;
        }

        // Set loading state for the specific row
        setIndividualSendLoadingMap((prevMap) => ({ ...prevMap, [row.classId]: true }));

        const updatedDisabledCheckBoxes = [...disabledCheckBoxes];
        updatedDisabledCheckBoxes[rowIndex] = !updatedDisabledCheckBoxes[rowIndex];
        setDisabledCheckBoxes(updatedDisabledCheckBoxes);

        const sendRequests: SendToClassDivisionType2[] = [{ notificationId, adminId, ...rest }];

        const actionResult = await dispatch(notificationSendToClassDivision(sendRequests));

        // Reset loading state after the action is complete
        setIndividualSendLoadingMap((prevMap) => ({ ...prevMap, [row.classId]: false }));

        if (actionResult && Array.isArray(actionResult.payload)) {
          const results: ClassDivisionWithStatus[] = actionResult.payload;

          // Update the sendResults state based on the previous state
          setSendResults((prevResults) => [...prevResults, ...results]);

          // Update the selectedData state with the new status based on the previous state
          setSelectedData((prevResults) =>
            prevResults.map((item) => {
              const resultForItem = results.find((result) => result.classId === item.classId);

              if (resultForItem) {
                return {
                  ...item,
                  sendStatus:
                    item.sendStatus !== 'Success' && item.sendStatus !== 'Failed'
                      ? resultForItem.sendStatus
                      : item.sendStatus,
                };
              }

              return item;
            })
          );
          setSelectedRows([]);

          const errorMessages = results.find((result) => result.sendStatus === 'Failed');
          if (errorMessages) {
            setErrMsgTooltip('No notifications sent to any Class Sections, Please check the numbers and Try again');
          } else {
            setErrMsgTooltip('Error sending notifications to some Class Sections, Please recheck and Try again');
          }
          setSelectedRows([]);
        } else {
          setErrMsgTooltip('Error while sending notifications, Please Try again');
        }
        setShowSuccessMap((prevMap) => ({ ...prevMap, [row.classId]: true }));

        setTimeout(() => {
          setShowSuccessMap((prevMap) => ({ ...prevMap, [row.classId]: false }));
          // setIsButtonDisabledMap((prevMap) => ({ ...prevMap, [row.classId]: true }));
        }, 5000);
      } catch (error) {
        console.error('Error sending notifications:', error);
      }
    },
    [dispatch, confirm, notificationId, adminId, selectedData, setSendResults, individualSendLoadingMap]
  );

  // send Voice Message //

  const handleSendVoiceMessage = useCallback(async () => {
    setIsloading(true);
    try {
      if (!selectedRows || selectedRows.length === 0) {
        const errorMessage = (
          <ErrorMessage icon={ErrorMsg1} message="Please Select Atleast one Class Division to sent" />
        );
        await confirm(errorMessage, 'Class division not selected', { okLabel: 'Ok', showOnlyOk: true });
        return;
      }
      const sendRequests: SendVoiceToClassDivision[] = await Promise.all(
        selectedRows?.map(async (item) => {
          const { accademicTime, ...rest } = item;
          const sendReq = { voiceId, templateId, adminId, ...rest };
          return sendReq;
        }) || []
      );
      const messageSendCount = sendRequests.length;

      sendRequests.map((row) => setIndividualSendLoadingMap((prevMap) => ({ ...prevMap, [row.classId]: true })));
      const actionResult = await dispatch(voiceMessageSendToClassDivision(sendRequests));

      sendRequests.map((row) => setIndividualSendLoadingMap((prevMap) => ({ ...prevMap, [row.classId]: false })));

      if (actionResult && Array.isArray(actionResult.payload)) {
        const results: ClassDivisionWithStatus[] = actionResult.payload;
        setSendResults([...sendResults, ...results]);

        const errorMessages = results.find((result) => result.sendStatus === 'Failed');
        const successMessages = results.find((result) => result.sendStatus === 'Success');
        if (!errorMessages) {
          const successMessage = (
            <SuccessMessage icon={SuccessMsg} message={`Notification sent to ${messageSendCount} Recipient.`} />
          );
          await confirm(successMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        } else if (!successMessages) {
          const errorMessage = (
            <ErrorMessage
              icon={ErrorMsg2}
              message="No notifications sent to any Class Divisions, Please check the numbers and Try again"
            />
          );
          await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        } else {
          const errorMessage = (
            <ErrorMessage
              icon={ErrorMsg}
              message="Error sending notifications to some Class Divisions, Please recheck and Try again"
            />
          );
          await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        }
        const dataResult = selectedData.map((item) => ({
          ...item,
          sendStatus:
            item.sendStatus !== 'Success' && item.sendStatus !== 'Failed'
              ? results.find((result) => result.classId === item.classId)?.sendStatus
              : item.sendStatus,
        }));

        // setSelectedRows([]);
        setSelectedData(dataResult);
      } else {
        const errorMessage = <ErrorMessage message="Error while sending notifications, Please Try again" />;
        await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
      }
      setSelectedRows([]);

      sendRequests.map((row) => setShowSuccessMap((prevMap) => ({ ...prevMap, [row.classId]: true })));
      // Update disabled state for each selected row
      const updatedDisabledCheckBoxes = selectedData.map((selectedItem) =>
        selectedRows.includes(selectedItem)
          ? !disabledCheckBoxes[selectedData.indexOf(selectedItem)]
          : disabledCheckBoxes[selectedData.indexOf(selectedItem)]
      );

      setDisabledCheckBoxes(updatedDisabledCheckBoxes);

      setTimeout(() => {
        sendRequests.map((row) => setShowSuccessMap((prevMap) => ({ ...prevMap, [row.classId]: false })));
      }, 5000);
    } catch (error) {
      console.error('Error sending notifications:', error);
    }
  }, [dispatch, confirm, notificationId, selectedRows, setSelectedRows, adminId, selectedData, sendResults]);

  const handleSendVoiceMessageIndivitual = useCallback(
    async (row: ClassDivisionWithStatus, rowIndex: number) => {
      setIsloading(false);
      try {
        const { accademicTime, ...rest } = row;

        // Check if the row is already loading, if yes, return
        if (individualSendLoadingMap[row.classSectionName]) {
          return;
        }

        // Set loading state for the specific row
        setIndividualSendLoadingMap((prevMap) => ({ ...prevMap, [row.classId]: true }));

        const updatedDisabledCheckBoxes = [...disabledCheckBoxes];
        updatedDisabledCheckBoxes[rowIndex] = !updatedDisabledCheckBoxes[rowIndex];
        setDisabledCheckBoxes(updatedDisabledCheckBoxes);

        const sendRequests: SendVoiceToClassDivision[] = [{ voiceId, templateId, adminId, ...rest }];

        const actionResult = await dispatch(voiceMessageSendToClassDivision(sendRequests));

        // Reset loading state after the action is complete
        setIndividualSendLoadingMap((prevMap) => ({ ...prevMap, [row.classId]: false }));

        if (actionResult && Array.isArray(actionResult.payload)) {
          const results: ClassDivisionWithStatus[] = actionResult.payload;

          // Update the sendResults state based on the previous state
          setSendResults((prevResults) => [...prevResults, ...results]);

          // Update the selectedData state with the new status based on the previous state
          setSelectedData((prevResults) =>
            prevResults.map((item) => {
              const resultForItem = results.find((result) => result.classId === item.classId);

              if (resultForItem) {
                return {
                  ...item,
                  sendStatus:
                    item.sendStatus !== 'Success' && item.sendStatus !== 'Failed'
                      ? resultForItem.sendStatus
                      : item.sendStatus,
                };
              }

              return item;
            })
          );
          setSelectedRows([]);

          const errorMessages = results.find((result) => result.sendStatus === 'Failed');
          if (errorMessages) {
            setErrMsgTooltip('No notifications sent to any Class Sections, Please check the numbers and Try again');
          } else {
            setErrMsgTooltip('Error sending notifications to some Class Sections, Please recheck and Try again');
          }
          setSelectedRows([]);
        } else {
          setErrMsgTooltip('Error while sending notifications, Please Try again');
        }
        setShowSuccessMap((prevMap) => ({ ...prevMap, [row.classId]: true }));

        setTimeout(() => {
          setShowSuccessMap((prevMap) => ({ ...prevMap, [row.classId]: false }));
          // setIsButtonDisabledMap((prevMap) => ({ ...prevMap, [row.classId]: true }));
        }, 5000);
      } catch (error) {
        console.error('Error sending notifications:', error);
      }
    },
    [dispatch, confirm, notificationId, adminId, selectedData, setSendResults, individualSendLoadingMap]
  );

  const getRowKey = useCallback((row: ClassDivisionWithStatus) => row.classId, []);

  const getStatusButton = (row: ClassDivisionWithStatus, rowIndex: number) => (
    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
      {row.sendStatus === 'Failed' ? (
        <Tooltip title={errMsgTooltip} placement="left">
          <Stack>
            <Lottie animationData={errorIcon} loop={false} style={{ width: '30px' }} />
          </Stack>
        </Tooltip>
      ) : row.sendStatus === 'Success' ? (
        showSuccessMap[row.classId] === true ? (
          <Stack>
            <Lottie animationData={successIcon} loop={false} style={{ width: '30px' }} />
          </Stack>
        ) : (
          <LoadingButton
            endIcon={<SuccessIcon color="success" />}
            variant="outlined"
            size="small"
            color="primary"
            disabled
            // disabled={isButtonDisabledMap[row.classId]}
          >
            Sent
          </LoadingButton>
        )
      ) : !selectedRows.includes(row) ? (
        <LoadingButton
          endIcon={<SendIcon />}
          onClick={() => {
            if (notificationId && notificationId !== 0) {
              handleSendNotificationIndivitual(row, rowIndex);
            } else if (messageId && messageId !== 0) {
              handleSendMessageIndivitual(row, rowIndex);
            } else {
              handleSendVoiceMessageIndivitual(row, rowIndex);
            }
          }}
          variant="outlined"
          size="small"
          color="primary"
          disabled={isSubmitting && isLoading}
          loading={individualSendLoadingMap[row.classId]}
        >
          Send
        </LoadingButton>
      ) : (
        <LoadingButton
          endIcon={<SendIcon />}
          variant="outlined"
          size="small"
          color="primary"
          disabled
          loading={individualSendLoadingMap[row.classId]}
        >
          Send
        </LoadingButton>
      )}
    </Box>
  );

  // const getStatusIcon = (row: ClassDivisionWithStatus) => (
  //   <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
  //     {row.sendStatus === 'Failed' ? (
  //       <FcCancel style={{ height: 20, width: 20 }} />
  //     ) : row.sendStatus === 'Success' ? (
  //       <FcOk style={{ height: 20, width: 20 }} />
  //     ) : (
  //       <LoadingButton
  //         onClick={() => {
  //           handleSendIndivitual(row);
  //         }}
  //         variant="outlined"
  //         size="small"
  //         color="primary"
  //         disabled={isSubmitting}
  //       >
  //         Send
  //       </LoadingButton>
  //     )}
  //   </Box>
  // );
  const ClassDivisionListColumns: DataTableColumn<ClassDivisionWithStatus>[] = [
    {
      name: 'slNo',
      headerLabel: 'Sl.No',
      dataKey: 'sortOrder',
      sortable: true,
    },
    {
      name: 'class',
      dataKey: 'className',
      headerLabel: 'Class',
    },
    {
      name: 'year',
      dataKey: 'accademicTime',
      headerLabel: 'Year',
    },
    {
      name: 'status',
      dataKey: 'TextField',
      renderCell: getStatusButton,
    },
  ];
  return (
    <ClassDivisionRoot>
      <Box>
        <Divider />
        <form noValidate onReset={handleReset} onSubmit={handleSubmit}>
          <Grid pb={2} pt={1} container spacing={3} alignItems="end">
            <Grid item xl={2} lg={3} md={3} sm={6} xs={12}>
              <FormControl fullWidth>
                <Typography variant="h6" fontSize={12} color="GrayText">
                  Academic Year
                </Typography>
                <Select
                  labelId="academicYearFilter"
                  id="academicYearFilterSelect"
                  value={academicYearFilter.toString()}
                  onChange={handleYearChange}
                  placeholder="Select Year"
                >
                  {YearData.map((opt) => (
                    <MenuItem key={opt.accademicId} value={opt.accademicId}>
                      {opt.accademicTime}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xl={2} lg={3} md={3} sm={6} xs={12}>
              <FormControl fullWidth>
                <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                  <Button variant="contained" color="secondary" type="reset" fullWidth>
                    Reset
                  </Button>
                  <Button variant="contained" color="primary" type="submit" fullWidth>
                    Search
                  </Button>
                </Stack>
              </FormControl>
            </Grid>
          </Grid>
        </form>

        <Paper
          sx={{
            border: selectedData?.length === 0 ? `0px` : `1px solid ${theme.palette.grey[300]}`,
            width: '100%',
            overflow: 'auto',
          }}
        >
          <Box
            sx={{
              height: 'calc(100vh - 23.7rem)',
              width: { xs: selectedData?.length !== 0 ? '43.75rem' : '100%', md: '100%' },
            }}
          >
            <DataTable
              ShowCheckBox
              isSubmitting={isSubmitting}
              disabledCheckBox={disabledCheckBoxes}
              RowSelected
              tableRowSelect
              setSelectedRows={setSelectedRows}
              selectedRows={selectedRows}
              columns={ClassDivisionListColumns}
              data={selectedData}
              getRowKey={getRowKey}
              fetchStatus={ClassDivisionStatus}
            />
          </Box>
        </Paper>
        <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 3 }}>
          {selectedData.length !== 0 && (
            <Stack spacing={2} direction="row">
              <Button disabled={isSubmitting} onClick={handleCancel} variant="contained" color="secondary">
                Cancel
              </Button>
              <LoadingButton
                endIcon={<SendIcon />}
                fullWidth
                loadingPosition="start"
                onClick={
                  notificationId && notificationId !== 0
                    ? handleSendNotification
                    : messageId && messageId !== 0
                    ? handleSendMessage
                    : handleSendVoiceMessage
                }
                loading={isSubmitting && isLoading}
                variant="contained"
                color="primary"
                disabled={isSubmitting}
              >
                {isSubmitting && isLoading ? 'Sending...' : 'Send'}
              </LoadingButton>
            </Stack>
          )}
        </Box>
      </Box>
    </ClassDivisionRoot>
  );
}

export default ClassDivision;
