/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  Collapse,
  FormControl,
} from '@mui/material';
import styled from 'styled-components';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import SearchIcon from '@mui/icons-material/Search';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { CLASS_SELECT, YEAR_SELECT } from '@/config/Selection';
import DeleteIcon from '@mui/icons-material/Delete';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import { MdAdd } from 'react-icons/md';

const TermFeesMapRoot = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      height: calc(100% - 40px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;
const dummyData = [
  {
    slNo: 1,
    year: '2023-2024',
    class: 1,
    term: 'Term - 1',
    stop: 'Palakkad',
    fees: 750,
  },
  {
    slNo: 2,
    year: '2023-2024',
    class: 2,
    term: 'Term - 2',
    stop: 'Thrissur',
    fees: 750,
  },
  {
    slNo: 3,
    year: '2023-2024',
    class: 3,
    term: 'Term - 3',
    stop: 'Kochi',
    fees: 750,
  },
  {
    slNo: 4,
    year: '2023-2024',
    class: 4,
    term: 'Term - 4',
    stop: 'Trivandrum',
    fees: 750,
  },
  {
    slNo: 5,
    year: '2023-2024',
    class: 5,
    term: 'Term - 5',
    stop: 'Kozhikkode',
    fees: 750,
  },
  {
    slNo: 1,
    year: '2023-2024',
    class: 6,
    term: 'Term - 6',
    stop: 'Palakkad',
    fees: 750,
  },
  {
    slNo: 2,
    year: '2023-2024',
    class: 7,
    term: 'Term - 7',
    stop: 'Thrissur',
    fees: 750,
  },
  {
    slNo: 3,
    year: '2023-2024',
    class: 8,
    term: 'Term - 8',
    stop: 'Kochi',
    fees: 750,
  },
  {
    slNo: 4,
    year: '2023-2024',
    class: 9,
    term: 'Term - 9',
    stop: 'Trivandrum',
    fees: 750,
  },
  {
    slNo: 5,
    year: '2023-2024',
    class: 10,
    term: 'Term - 10',
    stop: 'Kozhikkode',
    fees: 750,
  },
];
function TermFeesMap() {
  const [drawerOpen, setDrawerOpen] = React.useState<boolean>(false);
  const [showFilter, setShowFilter] = useState(false);

  const handleOpen = () => {
    setDrawerOpen(true);
  };

  const toggleDrawerClose = useCallback(() => setDrawerOpen(false), []);

  const getRowKey = useCallback((row: any) => row.examId, []);
  const termFeesMapColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'slNo',
        dataKey: 'slNo',
        headerLabel: 'Sl.No',
        sortable: true,
      },
      {
        name: 'class',
        dataKey: 'class',
        headerLabel: 'Class',
      },
      {
        name: 'year',
        dataKey: 'year',
        headerLabel: 'Academic Year',
      },
      {
        name: 'term',
        dataKey: 'term',
        headerLabel: 'Term',
      },
      {
        name: 'stop',
        dataKey: '',
        headerLabel: 'Bus Stop',
      },
      {
        name: 'fees',
        dataKey: 'fees',
        headerLabel: 'Fees',
      },
      {
        name: 'actions',
        headerLabel: 'Actions',
        renderCell: () => {
          return (
            <Stack direction="row" gap={1}>
              <IconButton size="small" onClick={handleOpen} sx={{ padding: 0.5 }}>
                <ModeEditIcon />
              </IconButton>
              <IconButton size="small" color="error" onClick={handleOpen} sx={{ padding: 0.5 }}>
                <DeleteIcon />
              </IconButton>
            </Stack>
          );
        },
      },
    ],
    []
  );

  return (
    <Page title="List">
      <TermFeesMapRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17}>
              Bus Term Fees Mapped List
            </Typography>
            <Box pb={1} sx={{ flexShrink: 0 }}>
              <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                <SearchIcon />
              </IconButton>
              <Button onClick={handleOpen} sx={{ borderRadius: '20px' }} variant="outlined" size="small">
                <MdAdd size="20px" /> Map New
              </Button>
            </Box>
          </Stack>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid
                  pb={2}
                  pt={1}
                  container
                  columnSpacing={2}
                  alignItems="end"
                  sx={{ mb: { xs: 'auto', lg: '100px', xl: '40px' } }}
                >
                  <Grid item lg="auto" md={6} xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', md: 200 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Autocomplete
                        options={YEAR_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select Year" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" md={6} xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', md: 200 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Autocomplete
                        options={CLASS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select Class" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" md={6} xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', md: 200 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Term
                      </Typography>
                      <Autocomplete
                        options={['Term - 1', 'Term - 2', 'Term - 3', 'Term - 4', 'Term - 5']}
                        renderInput={(params) => <TextField {...params} placeholder="Select Term" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" md={6} xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', md: 200 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Bus Name
                      </Typography>
                      <TextField placeholder="Enter Name" />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" md={6} xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', md: 200 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Bus Stop
                      </Typography>
                      <Autocomplete
                        options={[]}
                        renderInput={(params) => <TextField {...params} placeholder="Select Stop" />}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item lg="auto" md={6} xs={12}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Paper className="card-table-container" sx={{ mt: 2 }}>
              <DataTable columns={termFeesMapColumns} data={dummyData} getRowKey={getRowKey} fetchStatus="success" />
            </Paper>
          </div>
        </Card>
      </TermFeesMapRoot>

      <TemporaryDrawer
        onClose={toggleDrawerClose}
        Title="Upload Details"
        state={drawerOpen}
        DrawerContent={
          <Box mt={5}>
            <Stack sx={{ height: 'calc(100vh - 150px)' }}>
              <Typography mt={2} variant="h6" fontSize={14}>
                Academic Year
              </Typography>
              <Autocomplete
                fullWidth
                options={YEAR_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select Year" />}
              />
              <Typography mt={2} variant="h6" fontSize={14}>
                Class
              </Typography>
              <Autocomplete
                fullWidth
                options={CLASS_SELECT}
                renderInput={(params) => <TextField {...params} placeholder="Select Class" />}
              />
              <Typography mt={2} variant="h6" fontSize={14}>
                Term
              </Typography>
              <Autocomplete
                fullWidth
                options={['Term - 1', 'Term - 2', 'Term - 3', 'Term - 4', 'Term - 5']}
                renderInput={(params) => <TextField {...params} placeholder="Select Term" />}
              />
              <Typography mt={2} variant="h6" fontSize={14}>
                Stop
              </Typography>
              <Autocomplete
                fullWidth
                options={['Palakkad', 'Thrissur', 'Kochi', 'Trivandrum', 'Kozhikkode']}
                renderInput={(params) => <TextField {...params} placeholder="Select Stop" />}
              />
              <Typography mt={2} variant="h6" fontSize={14}>
                Bus Fees
              </Typography>
              <TextField placeholder="Enter Fees" />
            </Stack>
            <Box sx={{ justifyContent: 'center', pt: 3, position: 'relative' }}>
              <Stack spacing={2} direction="row">
                <Button onClick={toggleDrawerClose} fullWidth variant="contained" color="secondary">
                  Cancel
                </Button>
                <Button fullWidth variant="contained" color="primary">
                  Save
                </Button>
              </Stack>
            </Box>
          </Box>
        }
      />
    </Page>
  );
}

export default TermFeesMap;
