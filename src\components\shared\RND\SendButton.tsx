import { useState } from 'react';
import { Button } from '@mui/material';
import { motion } from 'framer-motion';
import { Send } from '@mui/icons-material';
import styled from '@emotion/styled';
import successIcon from '@/assets/MessageIcons/success.json';
import <PERSON><PERSON> from 'lottie-react';

const AnimatedButton = styled(motion(Button))`
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  /* min-width: 150px; */
  /* height: 45px; */
  /* border: 2px solid transparent; */
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.4s ease-in-out;
`;

const ButtonContainer = styled.div`
  height: 50vh;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
`;

const BorderLoader = styled(motion.div)`
  position: absolute;
  inset: 0;
  border-radius: 8px;
  border: 1px solid transparent;
  animation: borderAnimation 1.5s linear infinite;

  @keyframes borderAnimation {
    0% {
      border-color: transparent;
    }
    50% {
      border-color: #00b34c;
    }
    100% {
      border-color: transparent;
    }
  }
`;

export default function SendButton() {
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);

  const handleClick = () => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
      setSuccess(true);
    }, 2000);
    setTimeout(() => {
      setSuccess(false);
    }, 8000);
  };

  return (
    <ButtonContainer>
      <AnimatedButton
        variant={loading ? 'outlined' : 'outlined'}
        color={success ? 'success' : 'secondary'}
        onClick={success ? undefined : handleClick}
        disabled={loading}
        initial={{ width: 95 }}
        sx={{ border: loading ? '1px solid transparent' : '' }}
        animate={{
          //   width: loading ? 100 : success ? 150 : 150,
          justifyContent: loading ? 'center' : 'flex-start',
          //   paddingLeft: loading ? '0px' : success ? '15px' : '15px',
        }}
        transition={{ duration: 0.4, ease: 'easeInOut' }}
      >
        {loading && <BorderLoader />}

        <motion.span
          initial={{ opacity: 1, x: 0 }}
          animate={{ opacity: 1, x: loading ? 10 : 0 }}
          transition={{ duration: 0.3 }}
        >
          {/* Send */}
          {loading ? 'Sending...' : 'Send'}
        </motion.span>
        {!success && (
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: loading ? 0 : 1, x: loading ? 20 : 0 }}
            transition={{ duration: 0.3 }}
            style={{ marginLeft: 'auto', display: 'flex' }}
          >
            <Send fontSize="small" />
          </motion.div>
        )}
        {success && (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 2 }}
            transition={{ duration: 0.3 }}
            style={{ marginLeft: 'auto', display: 'flex' }}
          >
            <Lottie animationData={successIcon} loop={false} style={{ width: '25px' }} />
            {/* <CheckCircle fontSize="small" /> */}
          </motion.div>
        )}
      </AnimatedButton>
    </ButtonContainer>
  );
}
