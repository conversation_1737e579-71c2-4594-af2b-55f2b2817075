import ApiUrls from '@/config/ApiUrls';
import {
  SubjectCreateRequest,
  SubjectCreateRow,
  SubjectListInfo,
  SubjectListPagedData,
  SubjectListRequest,
} from '@/types/AcademicManagement';
import { CreateResponse, CreateResponseMulti, DeleteResponse, UpdateResponse } from '@/types/Common';
import { privateApi } from '@/api/base/api';
import { APIResponse } from '@/api/base/types';

async function GetSubjectList(request: SubjectListRequest): Promise<APIResponse<SubjectListPagedData>> {
  const response = await privateApi.post<SubjectListPagedData>(ApiUrls.GetSubjectList, request);
  return response;
}

async function SubjectList(subjectId: number | undefined): Promise<APIResponse<SubjectListPagedData[]>> {
  if (subjectId === undefined) {
    throw new Error('subjectId is required');
  }

  const url = ApiUrls.GetSubject.replace('{subjectId}', subjectId.toString());
  const response = await privateApi.get<any>(url);
  return response;
}

async function AddNewSubject(request: SubjectCreateRequest): Promise<APIResponse<CreateResponse>> {
  const response = await privateApi.post<CreateResponse>(ApiUrls.AddSubject, request);
  return response;
}

async function AddMultipleSubjects(request: SubjectCreateRow[]): Promise<APIResponse<CreateResponseMulti>> {
  const response = await privateApi.post<CreateResponseMulti>(ApiUrls.AddMultipleSubjects, request);
  return response;
}

async function UpdateSubject(request: SubjectListInfo): Promise<APIResponse<UpdateResponse>> {
  const response = await privateApi.post<UpdateResponse>(ApiUrls.UpdateSubject, request);
  return response;
}

async function DeleteSubject(subjectId: number): Promise<APIResponse<DeleteResponse>> {
  const url = ApiUrls.DeleteSubject.replace('{subjectId}', subjectId.toString());
  const response = await privateApi.get<DeleteResponse>(url);
  return response;
}

async function SubjectNameExists(subjectName: string): Promise<APIResponse<boolean>> {
  const url = `${ApiUrls.SubjectNameExists}?q=${subjectName}`;
  const response = await privateApi.get<boolean>(url);
  return response;
}

const methods = {
  GetSubjectList,
  AddNewSubject,
  AddMultipleSubjects,
  UpdateSubject,
  DeleteSubject,
  SubjectNameExists,
  SubjectList,
};

export default methods;
