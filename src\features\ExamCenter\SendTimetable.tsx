import React from 'react';
import Page from '@/components/shared/Page';
import { <PERSON><PERSON>, <PERSON><PERSON>ield, Box, <PERSON>po<PERSON>, Stack, Button, Card, Divider, Autocomplete } from '@mui/material';
import styled from 'styled-components';
import Popup from '@/components/shared/Popup/Popup';
import typography from '@/theme/card';
import { CLASS_SELECT, YEAR_SELECT } from '@/config/Selection';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';

const SendTimetableRoot = styled.div`
  padding: 1rem;
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.grey[100] : props.theme.palette.grey[900]};

  h6 {
    font-weight: 600;
  }
  .sub-heading {
    font-size: 0.75rem;
    color: ${(props) => props.theme.palette.grey[600]};
    font-weight: 500;
  }
  .MuiAutocomplete-input {
  }
  .MuiCard-root {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[800]};
  }
  .Card {
    min-height: calc(100vh - 165px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }
  }
  .CardContainer {
  }
`;

// eslint-disable-next-line @typescript-eslint/naming-convention
export type SendTimetableInfo = {
  Id: number;
  SendTimetableYear: string;
  SendTimetableClass: string;
  SendTimetableRollNo: number;
  SendTimetableDate: string;
};
// const DefaultSendTimetableInfo: SendTimetableInfo = {
//   Id: 0,
//   SendTimetableYear: '',
//   SendTimetableClass: '',
//   SendTimetableRollNo: 1,
//   SendTimetableDate: '',
// };

function SendTimetable() {
  const [drawerOpen, setDrawerOpen] = React.useState<boolean>(false);

  const handleClickOpen = () => {
    setDrawerOpen(true);
  };

  const handleClickClose = () => setDrawerOpen(false);
  return (
    <Page title="Send Timetable">
      <SendTimetableRoot>
        <Card className="Card" elevation={5} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Typography variant="h6" fontSize={17}>
            Send Timetable
          </Typography>
          <Divider />
          <form>
            <Grid className="CardContainer" container py={2} spacing={3}>
              <Grid item lg={6} xs={12}>
                <Typography variant="subtitle1" fontSize={12} color="GrayText">
                  Select Year
                </Typography>
                <Autocomplete
                  options={YEAR_SELECT}
                  renderInput={(params) => <TextField {...params} placeholder="Select" />}
                />
              </Grid>
              <Grid item lg={6} xs={12}>
                <Typography variant="subtitle1" fontSize={12} color="GrayText">
                  Select Class
                </Typography>
                <Autocomplete
                  options={CLASS_SELECT}
                  renderInput={(params) => <TextField {...params} placeholder="Select" />}
                />
              </Grid>
              <Grid item xs={12}>
                <Typography variant="subtitle1" fontSize={12} color="GrayText">
                  Select Exam
                </Typography>
                <Autocomplete
                  options={['Annual', 'Quaterly', 'Half-Yearly']}
                  renderInput={(params) => <TextField {...params} placeholder="Select" />}
                />
              </Grid>
            </Grid>
          </form>
          <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 3 }}>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="secondary" sx={{ fontFamily: typography.fontFamily }}>
                Cancel
              </Button>
              <Button onClick={handleClickOpen} variant="contained" color="primary">
                Send
              </Button>
            </Stack>
          </Box>
        </Card>
      </SendTimetableRoot>
      <Popup
        size="xs"
        state={drawerOpen}
        onClose={handleClickClose}
        popupContent={<SuccessMessage message="Timetable Sent Successfully" />}
      />
    </Page>
  );
}

export default SendTimetable;
