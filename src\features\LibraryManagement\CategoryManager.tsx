/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  Collapse,
  FormControl,
} from '@mui/material';
import styled from 'styled-components';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import SearchIcon from '@mui/icons-material/Search';
import deleteBin from '@/assets/ManageFee/deleteBin.json';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import DeleteIcon from '@mui/icons-material/Delete';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import DateSelect from '@/components/shared/Selections/DateSelect';
import { STATUS_SELECT } from '@/config/Selection';
import typography from '@/theme/typography';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import { deleteTermFeeList } from '@/store/ManageFee/manageFee.thunks';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';

const ManageUpdatesRoot = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 420px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 50px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

export const data = [
  {
    slNo: 1,
    date: '20/07/23',
    description: 'Welcome to PassDaily - eConnect for Schools and College',
    subject: 'Welcome to PassDaily',
  },
  { slNo: 2, date: '14/08/23', description: 'Keep your password safe', subject: 'Password' },
  {
    slNo: 3,
    date: '21/08/23',
    description: 'PassDaily Team Welcomes you to the eConnect tool for Schools... - eConnect for Schools and College',
    subject: 'Message',
  },
  {
    slNo: 4,
    date: '25/08/23',
    description: 'Welcome to PassDaily - eConnect for Schools and College',
    subject: 'PassDaily',
  },
  { slNo: 5, date: '27/08/23', description: 'Keep your password safe', subject: 'Password' },
  {
    slNo: 6,
    date: '30/08/23',
    description: 'PassDaily Team Welcomes you to the eConnect tool for Schools... - eConnect for Schools and College',
    subject: 'Message',
  },
  {
    slNo: 7,
    date: '02/09/23',
    description: 'Welcome to PassDaily - eConnect for Schools and College',
    subject: 'Welcome',
  },
];

function ManageUpdates() {
  const { confirm } = useConfirm();
  const dispatch = useAppDispatch();
  const [drawerOpen, setDrawerOpen] = React.useState<boolean>(false);
  const [showFilter, setShowFilter] = useState(false);

  const handleOpen = () => {
    setDrawerOpen(true);
  };

  const toggleDrawerClose = useCallback(() => setDrawerOpen(false), []);

  const getRowKey = useCallback((row: any) => row.examId, []);
  const handleDeleteTermFeeList = useCallback(
    async (termObj: any) => {
      const { termId } = termObj;
      const deleteConfirmMessage = (
        <DeleteMessage
          jsonIcon={deleteBin}
          message={
            <div>
              Are you sure you want to delete the Row <br />
              &quot;{termObj.termTitle}&quot; ?
            </div>
          }
        />
      );
      if (await confirm(deleteConfirmMessage, 'Delete Row?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
        const sendReq = [{ dbResult: 'string', deletedId: 0 }];

        console.log('sendReq', sendReq);

        const deleteResponse = await dispatch(deleteTermFeeList(sendReq));
        console.log('deleteResponse::::----', deleteResponse);
        if (deleteResponse && Array.isArray(deleteResponse.payload)) {
          const results: any[] = deleteResponse.payload;
          const errorMessages = results.find((result) => result.dbResult === 'Failed');
          const successMessages = results.find((result) => result.dbResult === 'Success');
          // Reload only the specific message template with the deleted messageId

          if (!errorMessages) {
            const deleteSuccessMessage = <SuccessMessage loop={false} message="Delete successfully" />;
            await confirm(deleteSuccessMessage, 'Success', { okLabel: 'Ok', showOnlyOk: true });
          } else if (!successMessages) {
            const deleteErrorMessage = <DeleteMessage message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          } else {
            const deleteErrorMessage = <DeleteMessage message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          }
          // loadTermFeeList({ ...currentTermFeeListRequest });

          // setSnackBar(true);
          // setTermFeeData((prevDetails) => prevDetails.filter((item) => item.termId !== termId));
        }
      }
    },
    [confirm]
  );

  const manageUpdatesColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'slNo',
        dataKey: 'slNo',
        headerLabel: 'Sl.No',
        sortable: true,
      },
      {
        name: 'date',
        dataKey: 'date',
        headerLabel: 'Date',
      },
      {
        name: 'subject',
        dataKey: 'subject',
        headerLabel: 'Subject',
      },
      {
        name: 'description',
        dataKey: 'description',
        headerLabel: 'Description',
      },
      {
        name: 'status',
        headerLabel: 'Status',
        renderCell: () => {
          return <Typography>Published</Typography>;
        },
      },
      {
        name: 'actions',
        headerLabel: 'Actions',
        renderCell: () => {
          return (
            <Stack direction="row" gap={1}>
              <IconButton size="small" onClick={handleOpen} sx={{ padding: 0.5 }}>
                <ModeEditIcon />
              </IconButton>
              <IconButton size="small" color="error" onClick={handleDeleteTermFeeList} sx={{ padding: 0.5 }}>
                <DeleteIcon />
              </IconButton>
            </Stack>
          );
        },
      },
    ],
    []
  );

  return (
    <Page title="List">
      <ManageUpdatesRoot>
        <Card elevation={1} sx={{ px: { xs: 3, md: 5 }, py: 2, mb: 3 }}>
          <Typography variant="h6" fontSize={17}>
            Send New Update
          </Typography>
          <Divider />
          <form noValidate>
            <Grid pb={4} pt={2} container rowSpacing={1} columnSpacing={3}>
              <Grid item lg={6} xs={12}>
                <FormControl sx={{ minWidth: { xs: '100%' } }}>
                  <Typography variant="h6" fontSize={14}>
                    Subject
                  </Typography>
                  <TextField placeholder="Enter Subject" />
                </FormControl>
              </Grid>
              <Grid item lg={6} xs={12}>
                <FormControl sx={{ minWidth: { xs: '100%' } }}>
                  <Typography variant="h6" fontSize={14}>
                    Date
                  </Typography>
                  <DateSelect />
                </FormControl>
              </Grid>
              <Grid item lg={8} xs={12}>
                <FormControl sx={{ minWidth: { xs: '100%' } }}>
                  <Typography variant="h6" fontSize={14}>
                    Description
                  </Typography>
                  <TextField placeholder="Enter Description" multiline />
                </FormControl>
              </Grid>
              <Grid item lg={4} xs={12} mt="auto" pb="1px">
                <FormControl sx={{ minWidth: { xs: '100%' } }}>
                  <Typography variant="h6" fontSize={14}>
                    Status
                  </Typography>
                  <Autocomplete
                    options={STATUS_SELECT}
                    renderInput={(params) => <TextField {...params} placeholder="Select Status" />}
                  />
                </FormControl>
              </Grid>
            </Grid>
          </form>
          <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' } }}>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="secondary" sx={{ fontFamily: typography.fontFamily }}>
                Cancel
              </Button>
              <Button variant="contained" color="primary">
                Announce
              </Button>
            </Stack>
          </Box>
        </Card>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: 2 }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={19}>
              Updates List
            </Typography>
            <Box sx={{ flexShrink: 0 }}>
              <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                <SearchIcon />
              </IconButton>
            </Box>
          </Stack>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={3} alignItems="end">
                  <Grid item lg="auto" md={6} xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', lg: 250 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Subject
                      </Typography>
                      <TextField placeholder="Enter Subject" />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" md={6} xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', lg: 250 } }}>
                      <Typography variant="h6" fontSize={14}>
                        Date
                      </Typography>
                      <DateSelect />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" md={6} xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', lg: 250 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Status
                      </Typography>
                      <Autocomplete
                        options={STATUS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select" />}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item lg="auto" xs={12}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Paper className="card-table-container" sx={{ marginTop: '5px' }}>
              <DataTable columns={manageUpdatesColumns} data={data} getRowKey={getRowKey} fetchStatus="success" />
            </Paper>
          </div>
          {/* <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 2 }}>
            <Stack spacing={2} direction="row">
              <Button variant="contained" color="secondary">
                Cancel
              </Button>
              <Button onClick={handleOpen} variant="contained" color="primary">
                Save
              </Button>
            </Stack>
          </Box> */}
        </Card>
      </ManageUpdatesRoot>

      <TemporaryDrawer onClose={toggleDrawerClose} Title="Upload Details" state={drawerOpen} DrawerContent="" />
    </Page>
  );
}

export default ManageUpdates;
