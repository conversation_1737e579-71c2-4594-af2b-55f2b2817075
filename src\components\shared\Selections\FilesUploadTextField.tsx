/* eslint-disable no-else-return */
/* eslint-disable no-nested-ternary */
import * as React from 'react';
import { styled } from '@mui/material/styles';
import Button from '@mui/material/Button';
import { IconButton, Avatar, Box, Stack, useTheme } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { fileUpload } from '@/store/MessageBox/messageBox.thunks';
import { BiSolidFilePdf } from 'react-icons/bi';
import { FaFolderOpen } from 'react-icons/fa';
import { FileObjTypes } from '@/pages/AdmissionFormNew';

const VisuallyHiddenInput = styled('input')({
  clip: 'rect(0 0 0 0)',
  clipPath: 'inset(50%)',
  height: 1,
  overflow: 'hidden',
  position: 'absolute', 
  bottom: 0,
  left: 0,
  whiteSpace: 'nowrap',
  width: 1,
});

type UploadPropsTypes = {
  onChange: (e: any, fieldName: string | undefined) => void;
  accept: string;
  name: string;
  multiple?: boolean;
  disabled?: boolean;
  title?: string;
  height?: string | number;
  error?: boolean | undefined;
  helperText?: string | boolean | undefined;
  backgroundColorDisabled?: boolean;
  disabledTextfeild?: string;
  buttonLabel?: string;
  uploadedFile?: FileObjTypes[] | undefined;
  handleRemoveFile: (fieldName: any) => void;
  fieldName?: string;
  disabledUploadButton?: any;
  inputRef?: any;
};

export default function InputFileUpload({
  onChange,
  accept,
  multiple,
  uploadedFile,
  buttonLabel,
  fieldName,
  handleRemoveFile,
  disabledUploadButton,
  inputRef,
}: UploadPropsTypes) {
  const theme = useTheme();
  const dispatch = useAppDispatch();
  const fileInputRef = React.useRef<HTMLInputElement | null>(null);
  const [files, setFiles] = React.useState<File[]>([]);

  const handleUpload = async () => {
    console.log('files', files);

    try {
      const response = await dispatch(fileUpload(files)).unwrap();
      console.log('response', response);
    } catch (error) {
      console.error(error);
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onChange(event, fieldName);
    const selectedFiles = Array.from(event.target.files);
    if (multiple) {
      setFiles((prevFiles) => [...prevFiles, ...uploadedFile]);
    } else {
      setFiles(uploadedFile);
    }
  };

  // const handleRemoveFile = (index) => {
  //   setFiles((prevFiles) => {
  //     const newFiles = [...prevFiles];
  //     newFiles.splice(index, 1);
  //     return newFiles;
  //   });
  // };

  const handleTextFieldClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click(); // Open file picker when TextField is clicked
    }
  };

  return (
    <Box width="100%">
      <Stack direction="row" spacing={0} width="100%">
        <Stack
          direction="row"
          justifyContent="space-between"
          border={1}
          borderColor={theme.palette.grey[400]}
          width={1000}
          borderRadius={1}
          // p={0.5}
          height={38}
          alignItems="center"
          onClick={
            uploadedFile?.some((file) => file.fieldName !== fieldName) || uploadedFile?.length === 0
              ? handleTextFieldClick
              : undefined
          }
          sx={{ cursor: 'pointer', borderTopRightRadius: 0, borderBottomRightRadius: 0 }}
          position="relative"
        >
          <Stack>
            {Array.isArray(uploadedFile) &&
              uploadedFile.map((file, index) => {
                console.log('uploadedFile', uploadedFile);
                if (!file) return null; // Prevents undefined/null errors

                if (file.imageUrl && (file.type?.startsWith('image/png') || file.type?.startsWith('image/jpeg'))) {
                  return <Avatar key={file.id} alt="Uploaded File" src={file.imageUrl} sx={{ ml: 1.5 }} />;
                } else {
                  return <BiSolidFilePdf fontSize={30} fill={theme.palette.error.main} style={{ marginLeft: 3 }} />;
                }

                // if (file.type?.startsWith('application/pdf')) {

                // }

                return null; // Avoids returning an empty string
              })}
          </Stack>
          {uploadedFile?.some((file) => file.fieldName === fieldName) && (
            <IconButton
              size="small"
              onClick={() => handleRemoveFile(fieldName)}
              sx={{ position: 'absolute', right: 3, top: '50%', transform: 'translateY(-50%)' }}
            >
              <CloseIcon fontSize="small" />
            </IconButton>
          )}
        </Stack>
        <Stack>
          <Button
            color={disabledUploadButton ? 'secondary' : 'info'}
            size="small"
            component="label"
            variant="contained"
            startIcon={<FaFolderOpen />}
            sx={{ borderTopLeftRadius: 0, borderBottomLeftRadius: 0, height: 38 }}
          >
            {buttonLabel ?? 'Upload File'}
            <VisuallyHiddenInput
              ref={fileInputRef || inputRef}
              type="file"
              disabled={disabledUploadButton}
              multiple={multiple ?? false}
              accept={accept ?? 'image/*'}
              onChange={handleFileChange}
            />
          </Button>
        </Stack>
        {/* <Button onClick={handleUpload} variant="contained" color="primary">
          Upload
        </Button> */}
      </Stack>
    </Box>
  );
}
