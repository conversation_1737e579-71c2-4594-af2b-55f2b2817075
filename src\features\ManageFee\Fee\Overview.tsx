/* eslint-disable import/no-cycle */
import styled from 'styled-components';
import Page from '@/components/shared/Page';
import {
  Card,
  FormControl,
  Grid,
  MenuItem,
  SelectChangeEvent,
  Typography,
  Select,
  Stack,
  Button,
  ToggleButton,
} from '@mui/material';
import React, { useState } from 'react';
import { FEE_TYPE_ID_OPTIONS } from '@/config/Selection';
import { getYearData } from '@/config/storeSelectors';
import { useAppSelector } from '@/hooks/useAppSelector';
import { Link } from 'react-router-dom';
import { fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import useAuth from '@/hooks/useAuth';
import { debounce } from 'lodash';
import { ToggleButtonGroup } from '@mui/material';
import { PendingFee } from './RecentPaidList';
import { ReceiveAmount } from './ReceiveAmount';
import Statistic from './Statistic';
import { Fee } from './Fee';

const OverviewRoot = styled.div`
  padding: 1rem;
  /* height: calc(100vh - 160px); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
`;

const Overview = ({ handleShowPayFee }: any) => {
  const YearData = useAppSelector(getYearData);
  const defualtYear = YearData[0]?.accademicId || 0;
  const defualtFeeType = FEE_TYPE_ID_OPTIONS[0]?.id || 0;
  const [academicYearFilter, setAcademicYearFilter] = useState(defualtYear);
  // YearData && YearData.length > 0 ? YearData[0].accademicId : 0
  const [feeTypeFilter, setFeeTypeFilter] = useState(defualtFeeType);
  const { user } = useAuth();
  const dispatch = useAppDispatch();
  const adminId: number = user ? user.accountId : 0;
  const [switchPaidAmountList, setSwitchPaidAmountList] = React.useState<'recentPaidList' | 'paymentModeList'>(
    'recentPaidList'
  );
  const handleChange = debounce(
    (event: React.MouseEvent<HTMLElement>, newAlignment: 'recentPaidList' | 'paymentModeList') => {
      setSwitchPaidAmountList(newAlignment);
    },
    0 // debounce delay in milliseconds
  );

  React.useEffect(() => {
    dispatch(fetchYearList(adminId));
    // console.log('YearData::::----', YearData);
  }, [dispatch, adminId]);

  const handleYearChange = (e: SelectChangeEvent) => {
    setAcademicYearFilter(parseInt(YearData.filter((item) => item.accademicId === e.target.value)[0].accademicId, 10));
    // loadFeePaidList({ ...currentFeePaidListRequest, academicId: parseInt(e.target.value, 10) });
  };

  const handleFeeTypeChange = (e: SelectChangeEvent) => {
    const feeTypeId = parseInt(e.target.value, 10);
    setFeeTypeFilter(feeTypeId);
    // loadFeePaidList({ ...currentFeePaidListRequest, feeTypeId: parseInt(e.target.value, 10) });
  };
  return (
    <Page title="Fee">
      <OverviewRoot>
        <Card sx={{ px: { xs: 3, md: 4 }, py: { xs: 2, md: 2 } }}>
          <Stack pb={1} direction="row" justifyContent="space-between" alignItems={{ xs: 'start', sm: 'center' }}>
            <Stack
              direction={{ xs: 'column', sm: 'row', md: 'column', lg: 'row' }}
              flex={1}
              alignItems={{ xs: 'start', lg: 'center' }}
              gap={1}
            >
              <Typography variant="h6" fontSize={17} mr={3}>
                Fee Overview
              </Typography>
              <Stack direction={{ xs: 'column', sm: 'row' }} gap={2}>
                <FormControl>
                  <Select
                    labelId="academicYearFilter"
                    id="academicYearFilterSelect"
                    value={academicYearFilter.toString()}
                    onChange={handleYearChange}
                    placeholder="Select Year"
                    sx={{ height: 30 }}
                  >
                    <MenuItem value={0} sx={{ display: 'none' }}>
                      Select Year
                    </MenuItem>
                    {YearData.map((opt) => (
                      <MenuItem key={opt.accademicId} value={opt.accademicId}>
                        {opt.accademicTime}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
                <FormControl>
                  <Select
                    labelId="feeTypeFilter"
                    id="feeTypeFilterSelect"
                    value={feeTypeFilter.toString()}
                    onChange={handleFeeTypeChange}
                    placeholder="Select Year"
                    sx={{ height: 30 }}
                  >
                    <MenuItem sx={{ display: 'none' }} value={0}>
                      Select Type
                    </MenuItem>
                    {FEE_TYPE_ID_OPTIONS.map((opt) => (
                      <MenuItem key={opt.id} value={opt.id}>
                        {opt.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Stack>
            </Stack>
            <Link to="/manage-fee/pay-fee-details">
              <Button
                size="small"
                color="success"
                sx={{ py: 0.4, px: 2.5 }}
                variant="contained"
                onClick={handleShowPayFee}
              >
                Pay Fee
              </Button>
            </Link>
          </Stack>
          <Grid container spacing={3} pb={2}>
            <Grid item xl={7} lg={12} xs={12}>
              <Fee academicId={academicYearFilter} feeTypeId={feeTypeFilter} />
            </Grid>
            <Grid item xl={5} lg={12} xs={12}>
              <Statistic academicId={academicYearFilter} feeTypeId={feeTypeFilter} />
            </Grid>
          </Grid>
          <ToggleButtonGroup
            color="primary"
            value={switchPaidAmountList}
            exclusive
            onChange={handleChange}
            aria-label="Platform"
            size="small"
            sx={{ mb: 2 }}
          >
            <ToggleButton sx={{ fontWeight: 'bold' }} value="recentPaidList">
              Recent Paid List
            </ToggleButton>
            <ToggleButton sx={{ fontWeight: 'bold' }} value="paymentModeList">
              Payment Mode List
            </ToggleButton>
          </ToggleButtonGroup>
          <Grid container spacing={3}>
            {switchPaidAmountList === 'recentPaidList' ? (
              <Grid item xl={12} lg={12} xs={12}>
                <PendingFee academicId={academicYearFilter} feeTypeId={feeTypeFilter} />
              </Grid>
            ) : (
              <Grid item xl={12} lg={12} xs={12}>
                <ReceiveAmount academicId={academicYearFilter} feeTypeId={feeTypeFilter} />
              </Grid>
            )}
            {/* <Grid item xl={7} lg={12} xs={12}>
              <PendingFee academicId={academicYearFilter} feeTypeId={feeTypeFilter} />
            </Grid>
            <Grid item xl={5} lg={12} xs={12}>
              <ReceiveAmount academicId={academicYearFilter} feeTypeId={feeTypeFilter} />
            </Grid> */}
          </Grid>
        </Card>
      </OverviewRoot>
    </Page>
  );
};

export default Overview;
