import { useState } from 'react';
import RichTextEditor from 'reactjs-tiptap-editor';

// Core & Common Extensions
import { BaseKit } from 'reactjs-tiptap-editor';
import { Bold } from 'reactjs-tiptap-editor/bold';
import { Italic } from 'reactjs-tiptap-editor/italic';
import { TextUnderline } from 'reactjs-tiptap-editor/textunderline';
import { Strike } from 'reactjs-tiptap-editor/strike';
import { Clear } from 'reactjs-tiptap-editor/clear';

// Headings & Lists
import { Heading } from 'reactjs-tiptap-editor/heading';
import { BulletList } from 'reactjs-tiptap-editor/bulletlist';
import { OrderedList } from 'reactjs-tiptap-editor/orderedlist';

// Quotes, Code & TextAlignment
import { Blockquote } from 'reactjs-tiptap-editor/blockquote';
import { CodeBlock } from 'reactjs-tiptap-editor/codeblock';
import { TextAlign } from 'reactjs-tiptap-editor/textalign';

// Fonts & Colors
import { FontFamily } from 'reactjs-tiptap-editor/fontfamily';
import { FontSize } from 'reactjs-tiptap-editor/fontsize';
import { Color } from 'reactjs-tiptap-editor/color';

// Media & Links
import { Image } from 'reactjs-tiptap-editor/image';
import { Link } from 'reactjs-tiptap-editor/link';

// Undo/Redo
import { History } from 'reactjs-tiptap-editor/history';

// Import Editor Styles
import 'reactjs-tiptap-editor/style.css';
import styled from 'styled-components';

const TextEditorRoot = styled.div`
  .text__editor {
  }
  .richtext-outline-1 {
    outline-width: 1px;
    outline-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.grey[400] : props.theme.palette.grey[900]};
  }
  .richtext-shadow {
    box-shadow: none !important;
  }
  .reactjs-tiptap-editor,
  .richtext-dialog-content {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.grey[800]};
  }
`;

// const extensions = [
//   BaseKit.configure({
//     placeholder: {
//       showOnlyCurrent: true,
//     },
//   }),
// ];

const DEFAULT = '';

export const TipTap = ({ limit, name, value, onChange }: any) => {
  const [content, setContent] = useState(DEFAULT);

  const onChangeContent = (val: string) => {
    setContent(val);
  };

  const getExtensions = (lim?: number) => [
    BaseKit.configure({
      placeholder: {
        showOnlyCurrent: true,
      },
      ...(lim && {
        characterCount: {
          limit,
        },
      }),
    }),
    History,
    Bold,
    Italic,
    TextUnderline,
    Strike,
    Clear,
    Heading,
    BulletList,
    OrderedList,
    Blockquote,
    CodeBlock,
    TextAlign,
    FontFamily,
    FontSize,
    Color,
    Image,
    Link,
  ];

  const handleEditorChange = (val: string) => {
    if (onChange && typeof onChange === 'function') {
      onChange({
        target: {
          name,
          value: val,
        },
      });
    }
  };

  return (
    <TextEditorRoot>
      <RichTextEditor
        // name={name}
        content={value}
        onChangeContent={handleEditorChange}
        extensions={getExtensions(limit)}
        contentClass="text__editor"
        output="text"
        dark={false}
        toolbar={{
          // Optional: Customize toolbar rendering if needed
          render: (props, toolbarItems, dom, containerDom) => {
            return containerDom(dom);
          },
        }}
      />
    </TextEditorRoot>
  );
};
