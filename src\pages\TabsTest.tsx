import React, { useState, useRef, useEffect } from "react";
import { Tabs, Tab, <PERSON>, Typography, TextField, Container } from "@mui/material";

const sections = [
  { id: "student", label: "Student Details", nameLabel: "Student Name" },
  { id: "father", label: "Father Details", nameLabel: "Father Name" },
  { id: "mother", label: "Mother Details", nameLabel: "Mother Name" },
];

const TabComponent = () => {
  const [activeTab, setActiveTab] = useState(0);
  const sectionRefs = useRef<HTMLElement[]>([]);
  const isScrollingRef = useRef(false);

  // Scroll to section when tab is clicked
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
    isScrollingRef.current = true;

    const section = sectionRefs.current[newValue];
    if (section) {
      window.scrollTo({ top: section.offsetTop - 80, behavior: "smooth" });

      setTimeout(() => {
        isScrollingRef.current = false;
      }, 800);
    }
  };

  // Detect active section on scroll
  useEffect(() => {
    const handleScroll = () => {
      if (isScrollingRef.current) return;

      let newActiveTab = 0;
      const scrollPosition = window.scrollY + 100;

      sectionRefs.current.forEach((section, index) => {
        if (section && scrollPosition >= section.offsetTop) {
          newActiveTab = index;
        }
      });

      setActiveTab(newActiveTab);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <Container>
      {/* Tabs (Sticky) */}
      <Box sx={{ position: "sticky", top: 0, background: "#fff", zIndex: 10, py: 1 }}>
        <Tabs value={activeTab} onChange={handleTabChange} centered>
          {sections.map((section, index) => (
            <Tab key={index} label={section.label} />
          ))}
        </Tabs>
      </Box>

      {/* Content Sections */}
      {sections.map((section, index) => (
        <Box
          key={section.id}
          ref={(el) => (sectionRefs.current[index] = el as HTMLElement)}
          sx={{ py: 4, minHeight: "100vh", scrollMarginTop: "80px" }}
        >
          <Typography variant="h5" gutterBottom>
            {section.label}
          </Typography>
          <Box sx={{ display: "flex", flexDirection: "column", gap: 2, maxWidth: 400 }}>
            <TextField label={section.nameLabel} variant="outlined" fullWidth />
            <TextField label="Email" variant="outlined" fullWidth />
            <TextField label="Gender" variant="outlined" fullWidth />
            <TextField label="Mobile Number" variant="outlined" fullWidth />
            <TextField label="Class Name" variant="outlined" fullWidth />
          </Box>
        </Box>
      ))}
    </Container>
  );
};

export default TabComponent;
