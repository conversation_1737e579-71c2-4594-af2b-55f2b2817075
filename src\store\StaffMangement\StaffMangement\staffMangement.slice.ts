import { PayloadAction, createSlice } from '@reduxjs/toolkit';
import { flushStore } from '@/store/flush.slice';
import { OutletContextType } from '@/types/Common';
import {
  CTSAllocationCreateUpdateInfo,
  CTSAllocationMappedInfo,
  CTSFilterInfo,
  StaffManagementState,
} from '@/types/StaffManagement';
import {
  AddCTSAllocationMap,
  addNewMultipleStaff,
  addNewStaff,
  createCTSAllocationSingle,
  deleteCTS,
  deleteStaff,
  fetchCTSFilter,
  fetchCTSList,
  fetchCTSMapping,
  fetchCTSTeacherWiseMapiing,
  fetchCTSUpdateDetail,
  fetchStaffDataList,
  updateCTSAllocationSingle,
  updateStaff,
} from './staffMangement.thunks';

const initialState: StaffManagementState = {
  staffList: {
    status: 'idle',
    data: [],
    error: null,
    pageInfo: {
      totalrecords: 0,
      pagesize: 20,
      pagenumber: 1,
      totalpages: 0,
      remainingpages: 0,
    },
    sortColumn: 'staffID',
    sortDirection: 'asc',
  },
  CTSFiltersList: {
    status: 'idle',
    data: {} as CTSFilterInfo,
    yearList: [],
    classList: [],
    subjectList: [],
    staffList: [],
    error: null,
  },
  CTSDetailsList: {
    status: 'idle',
    data: [],
    error: null,
    pageInfo: {
      totalrecords: 0,
      pagesize: 20,
      pagenumber: 1,
      totalpages: 0,
      remainingpages: 0,
    },
    sortColumn: 'classId',
    sortDirection: 'asc',
  },
  CTSUpdateDetailList: {
    status: 'idle',
    data: {} as CTSAllocationCreateUpdateInfo,
    error: null,
  },
  CTSAllocationMapedList: {
    status: 'idle',
    data: [],
    staffList: [],
    error: null,
  },
  CTSAllocationTeacherWiseMapedList: {
    status: 'idle',
    data: [],
    subjectList: [],
    classList: [],
    error: null,
  },
  submitting: false,
  deletingRecords: {},
  error: null,
};

export const staffManagementSlice = createSlice({
  name: 'staffManagement',
  initialState,
  reducers: {
    setStaffListPage: (state, action: PayloadAction<number>) => {
      state.staffList.pageInfo.pagenumber = action.payload;
    },
    setStaffListPageSize: (state, action: PayloadAction<number>) => {
      state.staffList.pageInfo.pagenumber = 1;
      state.staffList.pageInfo.pagesize = action.payload;
    },
    setSortColumn: (state, action: PayloadAction<string>) => {
      state.staffList.pageInfo.pagenumber = 1;
      state.staffList.sortColumn = action.payload;
      state.staffList.sortDirection = 'asc';
    },
    setSortDirection: (state, action: PayloadAction<'asc' | 'desc'>) => {
      state.staffList.pageInfo.pagenumber = 1;
      state.staffList.sortDirection = action.payload;
    },
    setSortColumnAndDirection: (state, action: PayloadAction<{ column: string; direction: 'asc' | 'desc' }>) => {
      state.staffList.pageInfo.pagenumber = 1;
      state.staffList.sortColumn = action.payload.column;
      state.staffList.sortDirection = action.payload.direction;
    },
    setSubmitting: (state, action: PayloadAction<boolean>) => {
      state.submitting = action.payload;
    },
    setDeletingRecords: (state, action: PayloadAction<{ recordId: number }>) => {
      state.deletingRecords[action.payload.recordId] = true;
    },
    clearDeletingRecords: (state, action: PayloadAction<{ recordId: number }>) => {
      delete state.deletingRecords[action.payload.recordId];
    },
    setCTSSortColumn: (state, action: PayloadAction<string>) => {
      state.CTSDetailsList.pageInfo.pagenumber = 1;
      state.CTSDetailsList.sortColumn = action.payload;
      state.CTSDetailsList.sortDirection = 'asc';
    },
    setCTSSortDirection: (state, action: PayloadAction<'asc' | 'desc'>) => {
      state.CTSDetailsList.pageInfo.pagenumber = 1;
      state.CTSDetailsList.sortDirection = action.payload;
    },
    setCTSSortColumnAndDirection: (state, action: PayloadAction<{ column: string; direction: 'asc' | 'desc' }>) => {
      state.CTSDetailsList.pageInfo.pagenumber = 1;
      state.CTSDetailsList.sortColumn = action.payload.column;
      state.CTSDetailsList.sortDirection = action.payload.direction;
    },
  },
  extraReducers(builder) {
    builder
      .addCase(fetchStaffDataList.pending, (state) => {
        state.staffList.status = 'loading';
        state.staffList.error = null;
      })
      .addCase(fetchStaffDataList.fulfilled, (state, action) => {
        const { data, ...pageInfo } = action.payload;
        state.staffList.status = 'success';
        state.staffList.data = data;
        state.staffList.error = null;
        state.staffList.pageInfo = pageInfo;
      })
      .addCase(fetchStaffDataList.rejected, (state, action) => {
        state.staffList.status = 'error';
        state.staffList.error = action.payload || 'Unknown error in fetching staff list';
      })

      .addCase(addNewStaff.pending, (state) => {
        state.submitting = true;
        state.error = null;
      })
      .addCase(addNewStaff.fulfilled, (state) => {
        state.submitting = false;
        state.error = null;
      })
      .addCase(addNewStaff.rejected, (state, action) => {
        state.submitting = false;
        state.error = action.error.message;
      })

      .addCase(addNewMultipleStaff.pending, (state) => {
        state.submitting = true;
        state.error = null;
      })
      .addCase(addNewMultipleStaff.fulfilled, (state) => {
        state.submitting = false;
        state.error = null;
      })
      .addCase(addNewMultipleStaff.rejected, (state, action) => {
        state.submitting = false;
        state.error = action.error.message;
      })

      .addCase(updateStaff.pending, (state) => {
        state.submitting = true;
        state.error = null;
      })
      .addCase(updateStaff.fulfilled, (state, action) => {
        state.submitting = false;
        state.error = null;
        const dataIndex = state.staffList.data.findIndex((x) => x.staffID === action.meta.arg.staffID);
        if (dataIndex > -1) {
          state.staffList.data[dataIndex] = action.meta.arg;
        }
      })
      .addCase(updateStaff.rejected, (state, action) => {
        state.submitting = false;
        state.error = action.error.message;
      })

      .addCase(deleteStaff.pending, (state, action) => {
        state.deletingRecords[action.meta.arg] = true;
        state.error = null;
      })
      .addCase(deleteStaff.fulfilled, (state, action) => {
        if (action.payload.deleted) {
          const { [action.meta.arg]: deletedRecord, ...restDeletingRecords } = state.deletingRecords;
          state.deletingRecords = restDeletingRecords;
        }
        state.error = null;
      })
      .addCase(deleteStaff.rejected, (state, action) => {
        state.deletingRecords = { ...initialState.deletingRecords };
        state.error = action.error.message;
      })
      .addCase(fetchCTSFilter.pending, (state) => {
        state.CTSFiltersList.status = 'loading';
        state.CTSFiltersList.error = null;
      })
      .addCase(fetchCTSFilter.fulfilled, (state, action) => {
        const { yearList, classList, subjectList, staffList } = action.payload;
        state.CTSFiltersList.status = 'success';
        state.CTSFiltersList.data = action.payload;
        state.CTSFiltersList.error = null;
        state.CTSFiltersList.yearList = yearList;
        state.CTSFiltersList.classList = classList;
        state.CTSFiltersList.subjectList = subjectList;
        state.CTSFiltersList.staffList = staffList;
      })
      .addCase(fetchCTSFilter.rejected, (state, action) => {
        state.CTSFiltersList.status = 'error';
        state.CTSFiltersList.error = action.payload || 'Unknown error in fetching CTS Filter list';
      })
      .addCase(fetchCTSList.pending, (state) => {
        state.CTSDetailsList.status = 'loading';
        state.CTSDetailsList.error = null;
      })
      .addCase(fetchCTSList.fulfilled, (state, action) => {
        const { data, ...pageInfo } = action.payload;
        state.CTSDetailsList.status = 'success';
        state.CTSDetailsList.data = data;
        state.CTSDetailsList.error = null;
        state.CTSDetailsList.pageInfo = pageInfo;
      })
      .addCase(fetchCTSList.rejected, (state, action) => {
        state.CTSDetailsList.status = 'error';
        state.CTSDetailsList.error = action.payload || 'Unknown error in fetching CTS list';
      })

      .addCase(createCTSAllocationSingle.pending, (state) => {
        state.submitting = true;
        state.error = null;
      })
      .addCase(createCTSAllocationSingle.fulfilled, (state) => {
        state.submitting = false;
        state.error = null;
      })
      .addCase(createCTSAllocationSingle.rejected, (state, action) => {
        state.submitting = false;
        state.error = action.error.message;
      })

      .addCase(fetchCTSUpdateDetail.pending, (state) => {
        state.CTSUpdateDetailList.status = 'loading';
        state.CTSUpdateDetailList.error = null;
      })
      .addCase(fetchCTSUpdateDetail.fulfilled, (state, action) => {
        state.CTSUpdateDetailList.status = 'success';
        state.CTSUpdateDetailList.data = action.payload;
        state.CTSUpdateDetailList.error = null;
      })
      .addCase(fetchCTSUpdateDetail.rejected, (state, action) => {
        state.CTSUpdateDetailList.status = 'error';
        state.CTSUpdateDetailList.error = action.payload || 'Unknown error in fetching CTS Filter list';
      })

      .addCase(updateCTSAllocationSingle.pending, (state) => {
        state.submitting = true;
        state.error = null;
      })
      .addCase(updateCTSAllocationSingle.fulfilled, (state, action) => {
        state.submitting = false;
        state.error = null;
        const dataIndex = state.CTSDetailsList.data.findIndex((x) => x.cteacherId === action.meta.arg.cteacherId);
        if (dataIndex > -1) {
          state.CTSDetailsList.data[dataIndex] = action.meta.arg;
        }
      })
      .addCase(updateCTSAllocationSingle.rejected, (state, action) => {
        state.submitting = false;
        state.error = action.error.message;
      })
      .addCase(fetchCTSMapping.pending, (state) => {
        state.CTSAllocationMapedList.status = 'loading';
        state.CTSAllocationMapedList.error = null;
      })
      .addCase(fetchCTSMapping.fulfilled, (state, action) => {
        const { staffList, mappedLists } = action.payload;
        state.CTSAllocationMapedList.status = 'success';
        // state.CTSAllocationMapedList.data = action.payload;
        state.CTSAllocationMapedList.data = mappedLists || [];
        state.CTSAllocationMapedList.staffList = staffList || [];
        state.CTSAllocationMapedList.error = null;
      })
      .addCase(fetchCTSMapping.rejected, (state, action) => {
        state.CTSAllocationMapedList.status = 'error';
        state.CTSAllocationMapedList.error = action.payload || 'Unknown error in fetching CTS Mapping list';
      })
      .addCase(AddCTSAllocationMap.pending, (state) => {
        state.submitting = true;
        state.error = null;
      })
      .addCase(AddCTSAllocationMap.fulfilled, (state) => {
        state.submitting = false;
        state.error = null;
      })
      .addCase(AddCTSAllocationMap.rejected, (state, action) => {
        state.submitting = false;
        state.error = action.error.message;
      })
      .addCase(fetchCTSTeacherWiseMapiing.pending, (state) => {
        state.CTSAllocationTeacherWiseMapedList.status = 'loading';
        state.CTSAllocationTeacherWiseMapedList.error = null;
      })
      .addCase(fetchCTSTeacherWiseMapiing.fulfilled, (state, action) => {
        const { subjectList, classList, mappedLists } = action.payload;
        state.CTSAllocationTeacherWiseMapedList.status = 'success';
        // state.CTSAllocationTeacherWiseMapedList.data = action.payload;
        state.CTSAllocationTeacherWiseMapedList.data = mappedLists || [];
        state.CTSAllocationTeacherWiseMapedList.subjectList = subjectList || [];
        state.CTSAllocationTeacherWiseMapedList.classList = classList || [];
        state.CTSAllocationTeacherWiseMapedList.error =
          'Something went wrong in fetching CTS Allocation Teacher Wise Mapping';
      })
      .addCase(fetchCTSTeacherWiseMapiing.rejected, (state, action) => {
        state.CTSAllocationTeacherWiseMapedList.status = 'error';
        state.CTSAllocationTeacherWiseMapedList.error =
          action.payload || 'Unknown error in fetching CTS Teacher Wise Mapping list';
      })
      // ------ Delete Basic Fee List  ------- //
      .addCase(deleteCTS.pending, (state, action) => {
        action.meta.arg.forEach((i) => {
          state.deletingRecords[i.cteacherId] = true;
        });
        state.error = null;
      })
      .addCase(deleteCTS.fulfilled, (state, action) => {
        if (action.payload.deleted) {
          action.meta.arg.forEach((i) => {
            const { [i.cteacherId]: deletedRecord, ...restDeletingRecords } = state.deletingRecords;
            state.deletingRecords = restDeletingRecords;
          });
        }
        state.error = null;
      })
      .addCase(deleteCTS.rejected, (state, action) => {
        state.submitting = false;
        state.error = action.error.message || 'Unknown error in deleting CTS List';
      })
      .addCase(flushStore, () => initialState);
  },
});

export const {
  setStaffListPage,
  setStaffListPageSize,
  setSortColumn,
  setSortDirection,
  setCTSSortColumn,
  setCTSSortDirection,
  setSortColumnAndDirection,
  setCTSSortColumnAndDirection,
  setSubmitting,
  setDeletingRecords,
  clearDeletingRecords,
} = staffManagementSlice.actions;

export default staffManagementSlice.reducer;
