/* eslint-disable prettier/prettier */
import React from 'react';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';
import ClassMarking from '@/features/Attendance-Marking/Classmarking/ClassMarking';
import DetailedClassMarking from '@/features/Attendance-Marking/DetailedClassMarking';
import SessionDetailedClassMarking from '@/features/Attendance-Marking/SessionDetailedClassMarking';
import QuickMarking from '@/features/Attendance-Marking/QuickMarking';
import StudentsAttendance from '@/features/Attendance-Marking/StudentsAttendance';
import AttendanceSummary from '@/features/Attendance-Marking/AttendanceSummary';
import AbsenteesList from '@/features/Attendance-Marking/AbsenteesList';
import styled from 'styled-components';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import { Link, useLocation, useOutletContext } from 'react-router-dom';
import Page from '@/components/shared/Page';
import LeaveNoteList from '@/features/Attendance-Marking/LeaveNote/LeaveNoteList';
import { useDispatch } from 'react-redux';
import { flushStore } from '@/store/flush.slice';
import { OutletContextType } from '@/types/Common';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const AttendanceMarkRoot = styled.div`
  min-height: calc(100vh - ${TOP_BAR_HEIGHT});

  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};

  .tab {
    background-color: ${(props) => (props.theme.themeMode === 'light' ? '#fff' : props.theme.palette.grey[800])};
    @media screen and (min-width: 1414px) {
      width: 100%;
    }
    @media screen and (max-width: 720px) {
      width: 100%;
    }
  }
  .Mui-selected {
    color: ${(props) => props.theme.palette.primary.main};
  }
  /* .MuiTabScrollButton-root {
    color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.common.black : props.theme.palette.common.white};
  } */
`;

// type CustomTabProps = {
//   label: string;
//   component: any;
//   to: string;
//   active?: boolean;
// };

// const StyledTab = styled(Tab)<CustomTabProps>`
//   && {
//     color: ${(props) => (props.active ? props.theme.palette.primary.main : props.theme.palette.text.primary)};
//     border-bottom: ${(props) => (props.active ? `2px solid ${props.theme.palette.primary.main}` : 'none')};
//   }
// `;
function TabPanel({ children, value, index }: TabPanelProps) {
  return (
    <Box
      sx={{ paddingTop: '3rem' }}
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
    >
      {value === index && <Box>{children}</Box>}
    </Box>
  );
}
function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`,
  };
}

function AttendanceMarking() {
  const { topBarHeight } = useOutletContext<OutletContextType>();
  const location = useLocation();
  const [value, setValue] = React.useState(0);

  const dispatch = useDispatch();

  React.useEffect(() => {
    dispatch(flushStore());
    console.log('Flush Store');
  }, [dispatch, value]);

  React.useEffect(() => {
    const currentPath = location.pathname;
    const routePaths = [
      '/attendance-marking/class-marking',
      '/attendance-marking/detailed-marking',
      '/attendance-marking/session-wise-marking',
      '/attendance-marking/quick-marking',
      '/attendance-marking/leave-note',
      '/attendance-marking/student-attendance-calendar',
      '/attendance-marking/attendance-summary',
      '/attendance-marking/absentees-list',
    ];
    const index = routePaths.indexOf(currentPath);
    if (index !== -1) {
      setValue(index);
    }
  }, [location]);
  return (
    <Page title="">
      <AttendanceMarkRoot>
        <Tabs
          className="tab"
          variant="scrollable"
          scrollButtons
          allowScrollButtonsMobile
          value={value}
          aria-label="basic tabs"
          sx={{
            ml: '0px',
            borderBottom: 1,
            borderColor: 'divider',
            position: 'fixed',
            top: `${topBarHeight}px`,
            zIndex: 1,
          }}
        >
          <Tab {...a11yProps(1)} label="Class Marking" component={Link} to="/attendance-marking/class-marking" />
          <Tab
            {...a11yProps(1)}
            label="Detailed Class Marking"
            component={Link}
            to="/attendance-marking/detailed-marking"
          />
          <Tab
            {...a11yProps(2)}
            label="Detailed Class Marking Session"
            component={Link}
            to="/attendance-marking/session-wise-marking"
          />
          <Tab {...a11yProps(3)} label="Quick Marking" component={Link} to="/attendance-marking/quick-marking" />
          <Tab {...a11yProps(4)} label="Leave Note" component={Link} to="/attendance-marking/leave-note" />
          <Tab
            {...a11yProps(5)}
            label="Student Attendance"
            component={Link}
            to="/attendance-marking/student-attendance-calendar"
          />
          <Tab
            {...a11yProps(6)}
            label="Attendance Summary"
            component={Link}
            to="/attendance-marking/attendance-summary"
          />
          <Tab {...a11yProps(7)} label="Absentees List" component={Link} to="/attendance-marking/absentees-list" />
        </Tabs>

        <TabPanel value={value} index={0}>
          <ClassMarking />
        </TabPanel>
        <TabPanel value={value} index={1}>
          <DetailedClassMarking />
        </TabPanel>
        <TabPanel value={value} index={2}>
          <SessionDetailedClassMarking />
        </TabPanel>
        <TabPanel value={value} index={3}>
          <QuickMarking />
        </TabPanel>
        <TabPanel value={value} index={4}>
          <LeaveNoteList />
        </TabPanel>
        <TabPanel value={value} index={5}>
          <StudentsAttendance />
        </TabPanel>
        <TabPanel value={value} index={6}>
          <AttendanceSummary />
        </TabPanel>
        <TabPanel value={value} index={7}>
          <AbsenteesList />
        </TabPanel>
        {/* 
      <Box sx={{ paddingTop: '3rem' }}>
        <Outlet />
      </Box> */}
      </AttendanceMarkRoot>
    </Page>
  );
}

export default AttendanceMarking;
